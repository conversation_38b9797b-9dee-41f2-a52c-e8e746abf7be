import com.github.jk1.license.render.CsvReportRenderer
import org.gradle.api.tasks.testing.logging.TestExceptionFormat
import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

val Project.libs: VersionCatalog
    get() = extensions.getByType(VersionCatalogsExtension::class.java).named("libs")

plugins {
    idea
    application
    kotlin("jvm")
    id("org.jlleitschuh.gradle.ktlint")
    id("com.github.jk1.dependency-license-report")
}

group = "io.coinmetrics.api"
version = "4.0-SNAPSHOT"

val ciJobToken: String? = System.getenv("CI_JOB_TOKEN")
val gitlabPersonalAccessToken: String? by project

repositories {
    mavenLocal()
    mavenCentral()
    listOf("12137844", "17855314", "31574851").map { version ->
        maven {
            url = uri("https://gitlab.com/api/v4/projects/$version/packages/maven")
            credentials(HttpHeaderCredentials::class) {
                val (tokenName, tokenValue) =
                    if (ciJobToken.isNullOrBlank()) {
                        "Private-Token" to gitlabPersonalAccessToken
                    } else {
                        "Job-Token" to ciJobToken
                    }
                name = tokenName
                value = tokenValue
            }
            authentication {
                create<HttpHeaderAuthentication>("header")
            }
        }
    }
}

tasks {
    withType<Test> {
        useJUnitPlatform()
        testLogging.showStandardStreams = true
        testLogging.exceptionFormat = TestExceptionFormat.FULL
        if (gitlabPersonalAccessToken != null) {
            systemProperties["gitlabPersonalAccessToken"] = gitlabPersonalAccessToken
        }
    }
    named<KotlinCompile>("compileKotlin") {
        compilerOptions {
            freeCompilerArgs.addAll(
                "-opt-in=kotlin.time.ExperimentalTime",
                "-opt-in=kotlinx.coroutines.ExperimentalCoroutinesApi",
            )
        }
    }
    named<Zip>("distZip") {
        enabled = false
    }
    named<Tar>("distTar") {
        enabled = false
    }
}

kotlin {
    jvmToolchain(21)
    compilerOptions {
        allWarningsAsErrors = true
    }
}

ktlint {
    version.set("1.3.1")
    filter {
        exclude { it.file.path.contains("/generated/") }
    }
}

// the report can be found here build/reports/dependency-license/licenses.csv
licenseReport {
    renderers = arrayOf(CsvReportRenderer())
}

dependencies {
    testImplementation(libs.findLibrary("coinmetrics-testing").get())

    // For JMX via JMXMP.
    runtimeOnly(libs.findLibrary("glassfish-jmx").get())
}
