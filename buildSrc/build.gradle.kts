plugins {
    `kotlin-dsl`
}

repositories {
    gradlePluginPortal()
}

kotlin {
    jvmToolchain(21)
    compilerOptions {
        allWarningsAsErrors = false
    }
}

val kotlinVersion = "2.1.0"

repositories {
    maven {
        url = uri("https://gitlab.com/api/v4/projects/17855314/packages/maven")
        credentials(HttpHeaderCredentials::class) {
            val token: String? = System.getenv("CI_JOB_TOKEN")
            val (tokenName, tokenValue) = if (token.isNullOrBlank()) {
                val gitlabPersonalAccessToken: String by project
                "Private-Token" to gitlabPersonalAccessToken
            } else {
                "Job-Token" to token
            }
            name = tokenName
            value = tokenValue
        }
        authentication {
            create<HttpHeaderAuthentication>("header")
        }
    }
}

dependencies {
    implementation("org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlinVersion")

    implementation(libs.jackson.databind)
    implementation(libs.jackson.module.kotlin)

    implementation("gradle.plugin.com.github.johnrengelman:shadow:7.1.2")
    implementation("org.jlleitschuh.gradle:ktlint-gradle:12.1.1")
    implementation("org.openapitools:openapi-generator-gradle-plugin:5.3.1")
    implementation("com.github.jk1:gradle-license-report:2.0")
    implementation("io.coinmetrics:api-server-codegen:0.3")
}
