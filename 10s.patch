Subject: [PATCH] 10s
---
Index: project/common/src/main/kotlin/io/coinmetrics/api/utils/DurationUnit.kt
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/project/common/src/main/kotlin/io/coinmetrics/api/utils/DurationUnit.kt b/project/common/src/main/kotlin/io/coinmetrics/api/utils/DurationUnit.kt
new file mode 100644
--- /dev/null	(date 1746470050220)
+++ b/project/common/src/main/kotlin/io/coinmetrics/api/utils/DurationUnit.kt	(date 1746470050220)
@@ -0,0 +1,36 @@
+package io.coinmetrics.api.utils
+
+import java.time.Duration
+import java.time.temporal.Temporal
+import java.time.temporal.TemporalUnit
+
+@Suppress("UNCHECKED_CAST")
+class DurationUnit(
+    private val duration: Duration,
+) : TemporalUnit {
+    override fun getDuration(): Duration = duration
+
+    override fun isDurationEstimated(): Boolean {
+        TODO("Not yet implemented")
+    }
+
+    override fun isDateBased(): Boolean {
+        TODO("Not yet implemented")
+    }
+
+    override fun isTimeBased(): Boolean {
+        TODO("Not yet implemented")
+    }
+
+    override fun <R : Temporal> addTo(
+        temporal: R,
+        amount: Long,
+    ): R = temporal.plus(duration.multipliedBy(amount)) as R
+
+    override fun between(
+        temporal1Inclusive: Temporal?,
+        temporal2Exclusive: Temporal?,
+    ): Long {
+        TODO("Not yet implemented")
+    }
+}
Index: src/test/kotlin/io/coinmetrics/api/utils/TimeUtilsTest.kt
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/test/kotlin/io/coinmetrics/api/utils/TimeUtilsTest.kt b/src/test/kotlin/io/coinmetrics/api/utils/TimeUtilsTest.kt
--- a/src/test/kotlin/io/coinmetrics/api/utils/TimeUtilsTest.kt	(revision 2c85177d08b0ed1ea0d861bf004cd0d299134f08)
+++ b/src/test/kotlin/io/coinmetrics/api/utils/TimeUtilsTest.kt	(date 1746470137384)
@@ -19,6 +19,7 @@
 import org.junit.jupiter.params.ParameterizedTest
 import org.junit.jupiter.params.provider.Arguments
 import org.junit.jupiter.params.provider.MethodSource
+import java.time.Duration
 import java.time.Instant
 import java.time.ZoneId
 import java.time.ZoneOffset
@@ -224,6 +225,218 @@
                 }
         }
 
+    @Test
+    fun `10s down sampling, paging_from=end with alignment`(): Unit =
+        runBlocking {
+            createStatefulDownSamplerWithAlignment(
+                "10s",
+                pagingFromStart = false,
+            ).map { downSampler ->
+                val result =
+                    listOf(
+                        Instant.parse("2025-01-03T01:46:00.321Z"),
+                        Instant.parse("2025-01-03T01:45:55Z"),
+                        Instant.parse("2025-01-03T01:45:40Z"),
+                        Instant.parse("2025-01-03T01:45:35.111Z"),
+                        Instant.parse("2025-01-03T01:45:30.111Z"),
+                        Instant.parse("2025-01-03T01:45:29.333Z"),
+                        Instant.parse("2025-01-03T01:45:22Z"),
+                        Instant.parse("2025-01-03T01:45:21.555Z"),
+                    ).filter {
+                        downSampler!!.invoke(it)
+                    }
+                println(result)
+                assertArrayEquals(
+                    arrayOf(
+                        Instant.parse("2025-01-03T01:46:00.321Z"),
+                        Instant.parse("2025-01-03T01:45:55Z"),
+                        Instant.parse("2025-01-03T01:45:40Z"),
+                        Instant.parse("2025-01-03T01:45:29.333Z"),
+                    ),
+                    result.toTypedArray(),
+                )
+            }.getOrElse {
+                fail(it.toResponseObject().toString())
+            }
+        }
+
+    @Test
+    fun `10s down sampling, paging_from=start with alignment`(): Unit =
+        runBlocking {
+            createStatefulDownSamplerWithAlignment(
+                granularity = "10s",
+                pagingFromStart = true,
+            ).map { downSampler ->
+                val result =
+                    listOf(
+                        Instant.parse("2025-01-03T01:45:21.555Z"),
+                        Instant.parse("2025-01-03T01:45:22Z"),
+                        Instant.parse("2025-01-03T01:45:29.333Z"),
+                        Instant.parse("2025-01-03T01:45:30.111Z"),
+                        Instant.parse("2025-01-03T01:45:35.111Z"),
+                        Instant.parse("2025-01-03T01:45:40Z"),
+                        Instant.parse("2025-01-03T01:45:55Z"),
+                        Instant.parse("2025-01-03T01:46:00.321Z"),
+                    ).filter {
+                        downSampler!!.invoke(it)
+                    }
+                println(result)
+                assertArrayEquals(
+                    arrayOf(
+                        Instant.parse("2025-01-03T01:45:21.555Z"),
+                        Instant.parse("2025-01-03T01:45:30.111Z"),
+                        Instant.parse("2025-01-03T01:45:40Z"),
+                        Instant.parse("2025-01-03T01:45:55Z"),
+                        Instant.parse("2025-01-03T01:46:00.321Z"),
+                    ),
+                    result.toTypedArray(),
+                )
+            }.getOrElse {
+                fail(it.toResponseObject().toString())
+            }
+        }
+
+    @Test
+    fun `10s down sampling, paging_from=end with alignment and timezone`(): Unit =
+        runBlocking {
+            createStatefulDownSamplerWithAlignment(
+                "10s",
+                pagingFromStart = false,
+                timezone = ZoneId.of("GMT+03:00"),
+            ).map { downSampler ->
+                val result =
+                    listOf(
+                        Instant.parse("2025-01-03T01:46:00.321Z"),
+                        Instant.parse("2025-01-03T01:45:55Z"),
+                        Instant.parse("2025-01-03T01:45:40Z"),
+                        Instant.parse("2025-01-03T01:45:35.111Z"),
+                        Instant.parse("2025-01-03T01:45:30.111Z"),
+                        Instant.parse("2025-01-03T01:45:29.333Z"),
+                        Instant.parse("2025-01-03T01:45:22Z"),
+                        Instant.parse("2025-01-03T01:45:21.555Z"),
+                    ).filter {
+                        downSampler!!.invoke(it)
+                    }
+                println(result)
+                assertArrayEquals(
+                    arrayOf(
+                        Instant.parse("2025-01-03T01:46:00.321Z"),
+                        Instant.parse("2025-01-03T01:45:55Z"),
+                        Instant.parse("2025-01-03T01:45:40Z"),
+                        Instant.parse("2025-01-03T01:45:29.333Z"),
+                    ),
+                    result.toTypedArray(),
+                )
+            }.getOrElse {
+                fail(it.toResponseObject().toString())
+            }
+        }
+
+    @Test
+    fun `10s down sampling, paging_from=start with alignment and timezone`(): Unit =
+        runBlocking {
+            createStatefulDownSamplerWithAlignment(
+                granularity = "10s",
+                pagingFromStart = true,
+                timezone = ZoneId.of("GMT+03:00"),
+            ).map { downSampler ->
+                val result =
+                    listOf(
+                        Instant.parse("2025-01-03T01:45:21.555Z"),
+                        Instant.parse("2025-01-03T01:45:22Z"),
+                        Instant.parse("2025-01-03T01:45:29.333Z"),
+                        Instant.parse("2025-01-03T01:45:30.111Z"),
+                        Instant.parse("2025-01-03T01:45:35.111Z"),
+                        Instant.parse("2025-01-03T01:45:40Z"),
+                        Instant.parse("2025-01-03T01:45:55Z"),
+                        Instant.parse("2025-01-03T01:46:00.321Z"),
+                    ).filter {
+                        downSampler!!.invoke(it)
+                    }
+                println(result)
+                assertArrayEquals(
+                    arrayOf(
+                        Instant.parse("2025-01-03T01:45:21.555Z"),
+                        Instant.parse("2025-01-03T01:45:30.111Z"),
+                        Instant.parse("2025-01-03T01:45:40Z"),
+                        Instant.parse("2025-01-03T01:45:55Z"),
+                        Instant.parse("2025-01-03T01:46:00.321Z"),
+                    ),
+                    result.toTypedArray(),
+                )
+            }.getOrElse {
+                fail(it.toResponseObject().toString())
+            }
+        }
+
+    @Test
+    fun `10s down sampling, paging_from=end`(): Unit =
+        runBlocking {
+            createStatefulDownSampler(
+                "10s",
+                pagingFromStart = false,
+            ).map { downSampler ->
+                val result =
+                    listOf(
+                        Instant.parse("2025-01-03T01:46:00.321Z"),
+                        Instant.parse("2025-01-03T01:45:55Z"),
+                        Instant.parse("2025-01-03T01:45:40Z"),
+                        Instant.parse("2025-01-03T01:45:35.111Z"),
+                        Instant.parse("2025-01-03T01:45:30.111Z"),
+                        Instant.parse("2025-01-03T01:45:29.333Z"),
+                        Instant.parse("2025-01-03T01:45:22Z"),
+                        Instant.parse("2025-01-03T01:45:21.555Z"),
+                    ).filter {
+                        downSampler!!.invoke(it)
+                    }
+                println(result)
+                assertArrayEquals(
+                    arrayOf(
+                        Instant.parse("2025-01-03T01:46:00.321Z"),
+                        Instant.parse("2025-01-03T01:45:40Z"),
+                        Instant.parse("2025-01-03T01:45:29.333Z"),
+                    ),
+                    result.toTypedArray(),
+                )
+            }.getOrElse {
+                fail(it.toResponseObject().toString())
+            }
+        }
+
+    @Test
+    fun `10s down sampling, paging_from=start`(): Unit =
+        runBlocking {
+            createStatefulDownSampler(
+                granularity = "10s",
+                pagingFromStart = true,
+            ).map { downSampler ->
+                val result =
+                    listOf(
+                        Instant.parse("2025-01-03T01:45:21.555Z"),
+                        Instant.parse("2025-01-03T01:45:22Z"),
+                        Instant.parse("2025-01-03T01:45:29.333Z"),
+                        Instant.parse("2025-01-03T01:45:30.111Z"),
+                        Instant.parse("2025-01-03T01:45:35.111Z"),
+                        Instant.parse("2025-01-03T01:45:40Z"),
+                        Instant.parse("2025-01-03T01:45:55Z"),
+                        Instant.parse("2025-01-03T01:46:00.321Z"),
+                    ).filter {
+                        downSampler!!.invoke(it)
+                    }
+                println(result)
+                assertArrayEquals(
+                    arrayOf(
+                        Instant.parse("2025-01-03T01:45:21.555Z"),
+                        Instant.parse("2025-01-03T01:45:35.111Z"),
+                        Instant.parse("2025-01-03T01:45:55Z"),
+                    ),
+                    result.toTypedArray(),
+                )
+            }.getOrElse {
+                fail(it.toResponseObject().toString())
+            }
+        }
+
     @Test
     fun `1m down sampling, paging_from=end with alignment`(): Unit =
         runBlocking {
@@ -907,7 +1120,7 @@
                         ErrorResponse(
                             ErrorObject(
                                 type = "bad_parameter",
-                                message = "Bad parameter 'granularity'. Value '5s' is not supported. Supported values are 'raw', '1d', '1h', '1m', '1s'.",
+                                message = "Bad parameter 'granularity'. Value '5s' is not supported. Supported values are 'raw', '1d', '1h', '1m', '10s', '1s'.",
                             ),
                         ),
                         it.toResponseObject(),
Index: src/main/kotlin/io/coinmetrics/api/endpoints/timeseries/market/S3OrderBooksEndpointImpl.kt
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/main/kotlin/io/coinmetrics/api/endpoints/timeseries/market/S3OrderBooksEndpointImpl.kt b/src/main/kotlin/io/coinmetrics/api/endpoints/timeseries/market/S3OrderBooksEndpointImpl.kt
--- a/src/main/kotlin/io/coinmetrics/api/endpoints/timeseries/market/S3OrderBooksEndpointImpl.kt	(revision 2c85177d08b0ed1ea0d861bf004cd0d299134f08)
+++ b/src/main/kotlin/io/coinmetrics/api/endpoints/timeseries/market/S3OrderBooksEndpointImpl.kt	(date 1746470445966)
@@ -150,7 +150,7 @@
         val downSamplingConfig =
             TimeUtils
                 .createStatefulDownSamplerConfig(
-                    granularity = request.granularity,
+                    granularity = if (request.granularity == "raw") "10s" else request.granularity,
                     pagingFromStart = request.pagingFrom == "start",
                     withAlignment = true,
                     timezone = ZoneId.of(request.timezone),
Index: project/common/src/main/kotlin/io/coinmetrics/api/utils/TimeUtils.kt
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/project/common/src/main/kotlin/io/coinmetrics/api/utils/TimeUtils.kt b/project/common/src/main/kotlin/io/coinmetrics/api/utils/TimeUtils.kt
--- a/project/common/src/main/kotlin/io/coinmetrics/api/utils/TimeUtils.kt	(revision 2c85177d08b0ed1ea0d861bf004cd0d299134f08)
+++ b/project/common/src/main/kotlin/io/coinmetrics/api/utils/TimeUtils.kt	(date 1746471328517)
@@ -8,6 +8,7 @@
 import org.slf4j.Logger
 import org.slf4j.LoggerFactory
 import java.sql.Timestamp
+import java.time.Duration
 import java.time.Instant
 import java.time.ZoneId
 import java.time.ZoneOffset
@@ -16,6 +17,7 @@
 import java.time.format.DateTimeParseException
 import java.time.temporal.ChronoField
 import java.time.temporal.ChronoUnit
+import java.time.temporal.TemporalUnit
 import java.util.concurrent.TimeUnit
 
 object TimeUtils {
@@ -247,6 +249,8 @@
         val nextTimeFun: (Instant) -> Instant,
     )
 
+    private val tenSecondsUnit = DurationUnit(Duration.ofSeconds(10))
+
     fun createStatefulDownSamplerConfig(
         granularity: String,
         pagingFromStart: Boolean,
@@ -259,12 +263,13 @@
                 "1d" -> ChronoUnit.DAYS
                 "1h" -> ChronoUnit.HOURS
                 "1m" -> ChronoUnit.MINUTES
+                "10s" -> tenSecondsUnit
                 "1s" -> ChronoUnit.SECONDS
                 else -> return FunctionResult.Failure(
                     ApiError.UnsupportedParameterValueWithSupportedInfo(
                         "granularity",
                         granularity,
-                        listOf("raw", "1d", "1h", "1m", "1s"),
+                        listOf("raw", "1d", "1h", "1m", "10s", "1s"),
                     ),
                 )
             }
@@ -300,7 +305,7 @@
 
     private fun truncate(
         time: Instant,
-        timeUnit: ChronoUnit,
+        timeUnit: TemporalUnit,
         timezone: ZoneId,
     ): Instant =
         if (timeUnit == ChronoUnit.DAYS && timezone != ZoneOffset.UTC) {
Index: src/main/kotlin/io/coinmetrics/api/endpoints/timeseries/market/OrderBooksDbService.kt
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/main/kotlin/io/coinmetrics/api/endpoints/timeseries/market/OrderBooksDbService.kt b/src/main/kotlin/io/coinmetrics/api/endpoints/timeseries/market/OrderBooksDbService.kt
--- a/src/main/kotlin/io/coinmetrics/api/endpoints/timeseries/market/OrderBooksDbService.kt	(revision 2c85177d08b0ed1ea0d861bf004cd0d299134f08)
+++ b/src/main/kotlin/io/coinmetrics/api/endpoints/timeseries/market/OrderBooksDbService.kt	(date 1746470445998)
@@ -144,7 +144,7 @@
         val downSamplingConfig =
             TimeUtils
                 .createStatefulDownSamplerConfig(
-                    granularity = request.granularity,
+                    granularity = if (request.granularity == "raw") "10s" else request.granularity,
                     pagingFromStart = request.pagingFrom == "start",
                     withAlignment = true,
                     timezone = ZoneId.of(request.timezone),
