[versions]
jackson = "2.17.2"
prometheus = "1.1.0"
kotlin-coroutines = "1.8.1"
junit = "5.9.0"
testcontainers = "1.19.3"
mockk = "1.14.3"

[libraries]
kotlin-coroutines-core = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-core", version.ref = "kotlin-coroutines" }
kotlin-coroutines-jdk8 = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-jdk8", version.ref = "kotlin-coroutines" }
kotlin-coroutines-slf4j = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-slf4j", version.ref = "kotlin-coroutines" }
kotlin-coroutines-debug = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-debug", version.ref = "kotlin-coroutines" }
kotlin-coroutines-test = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-test", version.ref = "kotlin-coroutines" }
kotlin-coroutines-guava = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-guava", version.ref = "kotlin-coroutines" }

jackson-databind = { module = "com.fasterxml.jackson.core:jackson-databind", version.ref = "jackson" }
jackson-module-kotlin = { module = "com.fasterxml.jackson.module:jackson-module-kotlin", version.ref = "jackson" }
jackson-datatype-jsr310 = { module = "com.fasterxml.jackson.datatype:jackson-datatype-jsr310", version.ref = "jackson" }
jackson-dataformat-xml = { module = "com.fasterxml.jackson.dataformat:jackson-dataformat-xml", version.ref = "jackson" }
jackson-dataformat-yaml = { module = "com.fasterxml.jackson.dataformat:jackson-dataformat-yaml", version.ref = "jackson" }

prometheus-metrics-core = { module = "io.prometheus:prometheus-metrics-core", version.ref = "prometheus" }
prometheus-metrics-model = { module = "io.prometheus:prometheus-metrics-model", version.ref = "prometheus" }
prometheus-metrics-instrumentation-jvm = { module = "io.prometheus:prometheus-metrics-instrumentation-jvm", version.ref = "prometheus" }
prometheus-metrics-exporter-httpserver = { module = "io.prometheus:prometheus-metrics-exporter-httpserver", version.ref = "prometheus" }
micrometer-registry-prometheus = { module = "io.micrometer:micrometer-registry-prometheus", version = "1.13.2" }

junit-api = { module = "org.junit.jupiter:junit-jupiter-api", version.ref = "junit" }
junit-engine = { module = "org.junit.jupiter:junit-jupiter-engine", version.ref = "junit" }
junit-params = { module = "org.junit.jupiter:junit-jupiter-params", version.ref = "junit" }

testcontainers = { module = "org.testcontainers:testcontainers", version.ref = "testcontainers" }
testcontainers-junit = { module = "org.testcontainers:junit-jupiter", version.ref = "testcontainers" }
testcontainers-kafka = { module = "org.testcontainers:kafka", version.ref = "testcontainers" }
testcontainers-postgresql = { module = "org.testcontainers:postgresql", version.ref = "testcontainers" }
testcontainers-minio = { module = "org.testcontainers:minio", version.ref = "testcontainers" }

kotlin-reflect = { module = "org.jetbrains.kotlin:kotlin-reflect", version = "1.9.10" }
google-protobuf = { module = "com.google.protobuf:protobuf-java", version = "3.21.7" }
google-guava = { module = "com.google.guava:guava", version = "31.0.1-jre" }
postgres = { module = "org.postgresql:postgresql", version = "42.5.0" }
hikaricp = { module = "com.zaxxer:HikariCP", version = "5.0.0" }
glassfish-jmx = { module = "org.glassfish.external:opendmk_jmxremote_optional_jar", version = "1.0-b01-ea" }

coinmetrics-databases = { module = "io.coinmetrics:databases", version = "1.2" }
coinmetrics-s3-databases = { module = "io.coinmetrics:s3-databases", version = "0.23" }
coinmetrics-shared-files = { module = "io.coinmetrics.sharedfiles:shared-files", version = "2.4" }
coinmetrics-defi-client = { module = "io.coinmetrics:defi-client", version = "0.2" }
coinmetrics-healthchecks = { module = "io.coinmetrics:healthchecks", version = "0.03" }
coinmetrics-atlas-codec = { module = "io.coinmetrics:atlas-v2-codec", version = "1.8.2" }
coinmetrics-queues = { module = "io.coinmetrics:queues", version = "0.42" }
coinmetrics-book-streams-client = { module = "io.coinmetrics.bookstreams:book-streams-client", version = "1.0" }
coinmetrics-http-server = { module = "io.coinmetrics:http-server", version = "2.0" }
coinmetrics-api-nd-jobs = { module = "io.coinmetrics.jobs:api-nd-jobs", version = "1.4" }
coinmetrics-testing = { module = "io.coinmetrics:testing", version = "1.1" }

slf4j = { module = "org.slf4j:slf4j-api", version = "1.7.36" }
logback-classic = { module = "ch.qos.logback:logback-classic", version = "1.5.3" }
logback-json-classic = { module = "ch.qos.logback.contrib:logback-json-classic", version = "0.1.5" }
logback-jackson = { module = "ch.qos.logback.contrib:logback-jackson", version = "0.1.5" }

apache-commons-csv = { module = "org.apache.commons:commons-csv", version = "1.9.0" }
assertj-core = { module = "org.assertj:assertj-core", version = "3.18.0" }
kafka-clients = { module = "org.apache.kafka:kafka-clients", version = "3.1.0" }
temporal-testing = { module = "io.temporal:temporal-testing", version = "1.22.2" }
mockk = { module = "io.mockk:mockk", version.ref = "mockk" }

[bundles]
kotlin-coroutines = ["kotlin-coroutines-core", "kotlin-coroutines-jdk8"]
jackson = ["jackson-databind", "jackson-module-kotlin", "jackson-datatype-jsr310"]
prometheus = [
    "prometheus-metrics-core",
    "prometheus-metrics-model",
    "prometheus-metrics-instrumentation-jvm",
    "prometheus-metrics-exporter-httpserver",
    "micrometer-registry-prometheus",
]
testcontainers = [
    "testcontainers",
    "testcontainers-junit",
    "testcontainers-kafka",
    "testcontainers-postgresql",
    "testcontainers-minio",
]

[plugins]
protobuf = { id = "com.google.protobuf", version = "0.8.18" }
