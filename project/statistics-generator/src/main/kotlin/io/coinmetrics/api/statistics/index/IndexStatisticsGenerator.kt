package io.coinmetrics.api.statistics.index

import io.coinmetrics.api.model.DataAvailabilityTimeRange
import io.coinmetrics.api.resources.Resources
import io.coinmetrics.api.statistics.StatisticsGenerator
import io.coinmetrics.api.utils.LazyInstant.Companion.toLazyInstant
import io.coinmetrics.api.utils.SqlUtils
import io.coinmetrics.api.utils.TimeUtils
import io.coinmetrics.databases.Database
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.coroutineScope
import org.slf4j.LoggerFactory
import java.sql.ResultSet

class IndexStatisticsGenerator(
    private val db: Database,
) : StatisticsGenerator<Map<String, Map<String, DataAvailabilityTimeRange>>> {
    companion object {
        private val log = LoggerFactory.getLogger(IndexStatisticsGenerator::class.java)
    }

    override suspend fun generate(): Map<String, Map<String, DataAvailabilityTimeRange>> {
        val list =
            coroutineScope {
                (
                    Resources.fidelityPrimaryIndexes.flatMap { indexInfo ->
                        indexInfo.allLevelFrequencies.map { frequency ->
                            async { calcFidStatistics(indexInfo.name, frequency) }
                        }
                    } +
                        Resources.nonFidelityIndexes.flatMap { indexInfo ->
                            indexInfo.allLevelFrequencies.map { frequency ->
                                async { calcCmbiStatistics(indexInfo.name, frequency) }
                            }
                        }
                ).awaitAll().filterNotNull()
            }.flatMap { triple ->
                // add total returns (aliases for statistics)
                val associatedTotalReturnIndexName = Resources.getAssociatedTotalReturnIndexName(triple.first)
                if (associatedTotalReturnIndexName == null) {
                    listOf(triple)
                } else {
                    listOf(triple, Triple(associatedTotalReturnIndexName, triple.second, triple.third))
                }
            }

        return HashMap<String, HashMap<String, DataAvailabilityTimeRange>>().also {
            list.forEach { (index, frequency, statistic) ->
                it.computeIfAbsent(index) { HashMap() }[frequency] = statistic
            }
        }
    }

    private suspend fun calcFidStatistics(
        index: String,
        userFrequency: String,
    ): Triple<String, String, DataAvailabilityTimeRange>? {
        val schema = db.config.schema
        val timeFieldName = "r.index_time"
        val indexInfo = Resources.getIndexInfo(index).onFailure { log.warn(it) }.getOrNull() ?: return null
        val indexId = indexInfo.id

        val (frequency, offset) = TimeUtils.parseFrequency(userFrequency)
        val normalizedFrequencyOffset = TimeUtils.normalizeFrequencyOffset(offset)
        checkNotNull(normalizedFrequencyOffset)

        val (timeFilteringQuery, minJoinQuery, maxJoinQuery, temporaryTimeZone) =
            if (frequency == "1d") {
                // optimization for the case when 1d frequency is requested, but hourly table is not available
                val minJoinQuery = SqlUtils.createTimeseriesJoinQuery(timeFieldName, normalizedFrequencyOffset, true)
                val maxJoinQuery = SqlUtils.createTimeseriesJoinQuery(timeFieldName, normalizedFrequencyOffset, false)
                val temporaryTimezone = normalizedFrequencyOffset.forcedTimeZone ?: "UTC"
                QueryParams(
                    timeFilteringQuery = "",
                    minJoinQuery = minJoinQuery,
                    maxJoinQuery = maxJoinQuery,
                    temporaryTimeZone = temporaryTimezone,
                )
            } else {
                val timeFilteringQuery =
                    SqlUtils.createTimeFilteringQuery(
                        timeFieldName = timeFieldName,
                        frequency = frequency,
                        hourlyTable = false,
                        dayOffsetHour = normalizedFrequencyOffset.hours,
                        dayOffsetMinute = normalizedFrequencyOffset.minutes,
                        timezone = normalizedFrequencyOffset.forcedTimeZone ?: "UTC",
                    )
                QueryParams(
                    timeFilteringQuery = timeFilteringQuery,
                    minJoinQuery = "",
                    maxJoinQuery = "",
                )
            }

        val (beforeQuery, afterQuery) =
            if (temporaryTimeZone == null) {
                null to null
            } else {
                val escapedTimeZone = SqlUtils.escapeSql(temporaryTimeZone)
                "SET timezone='$escapedTimeZone'" to "SET timezone='UTC'"
            }

        // "SELECT MIN(..), MAX(..) FROM fidelity_index" is slow, so we have to use two sub queries
        // MIN, MAX is slower than LIMIT 1 using index, so we switch to LIMIT query

        val minOrderBy = if (minJoinQuery.isEmpty()) "ORDER BY $timeFieldName" else ""
        val maxOrderBy = if (maxJoinQuery.isEmpty()) "ORDER BY $timeFieldName DESC" else ""

        return db
            .query(
                query =
                    """
                    SELECT
                        (
                            SELECT $timeFieldName FROM $schema.fidelity_index r
                            $minJoinQuery
                            WHERE r.index_id=$indexId $timeFilteringQuery $minOrderBy LIMIT 1
                        ) AS min_time,
                        (
                            SELECT $timeFieldName FROM $schema.fidelity_index r
                            $maxJoinQuery
                            WHERE r.index_id=$indexId $timeFilteringQuery $maxOrderBy LIMIT 1
                        ) AS max_time
                    """.trimIndent(),
                beforeQuery = beforeQuery,
                afterQuery = afterQuery,
            ) {
                it.map { rs -> rs.parseStatistics() }.filterNotNull().firstOrNull()
            }?.let {
                Triple(index, userFrequency, it)
            }
    }

    private suspend fun calcCmbiStatistics(
        index: String,
        userFrequency: String,
    ): Triple<String, String, DataAvailabilityTimeRange>? {
        val schema = db.config.schema
        val indexInfo = Resources.getIndexInfo(index).onFailure { log.warn(it) }.getOrNull() ?: return null
        val indexId = indexInfo.id

        val (frequency, offset) = TimeUtils.parseFrequency(userFrequency)
        val normalizedFrequencyOffset = TimeUtils.normalizeFrequencyOffset(offset)
        checkNotNull(normalizedFrequencyOffset)

        val tableName =
            if (frequency == "1s") {
                "cm_index_realtime"
            } else if ((frequency == "1h" || frequency == "1d") && indexInfo.hasHourlyValuesTable) {
                "cm_index_close"
            } else {
                "cm_index"
            }
        val timeFieldName = "r.cm_index_time"
        val hourlyTable = tableName == "cm_index_close"

        val (timeFilteringQuery, minJoinQuery, maxJoinQuery, temporaryTimeZone) =
            if (frequency == "1d" && !hourlyTable) {
                // optimization for the case when 1d frequency is requested, but hourly table is not available
                val minJoinQuery = SqlUtils.createTimeseriesJoinQuery(timeFieldName, normalizedFrequencyOffset, true)
                val maxJoinQuery = SqlUtils.createTimeseriesJoinQuery(timeFieldName, normalizedFrequencyOffset, false)
                val temporaryTimezone = normalizedFrequencyOffset.forcedTimeZone ?: "UTC"
                QueryParams(
                    timeFilteringQuery = "",
                    minJoinQuery = minJoinQuery,
                    maxJoinQuery = maxJoinQuery,
                    temporaryTimeZone = temporaryTimezone,
                )
            } else {
                val timeFilteringQuery =
                    SqlUtils.createTimeFilteringQuery(
                        timeFieldName = timeFieldName,
                        frequency = frequency,
                        hourlyTable = hourlyTable,
                        dayOffsetHour = normalizedFrequencyOffset.hours,
                        dayOffsetMinute = normalizedFrequencyOffset.minutes,
                        timezone = normalizedFrequencyOffset.forcedTimeZone ?: "UTC",
                    )
                QueryParams(
                    timeFilteringQuery = timeFilteringQuery,
                    minJoinQuery = "",
                    maxJoinQuery = "",
                )
            }

        val (beforeQuery, afterQuery) =
            if (temporaryTimeZone == null) {
                null to null
            } else {
                val escapedTimeZone = SqlUtils.escapeSql(temporaryTimeZone)
                "SET timezone='$escapedTimeZone'" to "SET timezone='UTC'"
            }

        // "SELECT MIN(..), MAX(..) FROM cm_index" is slow, so we have to use two sub queries
        return db
            .query(
                query =
                    """
                    SELECT
                        (
                            SELECT MIN($timeFieldName) FROM $schema.$tableName r
                            $minJoinQuery
                            WHERE r.cm_index_id=$indexId $timeFilteringQuery
                        ) AS min_time,
                        (
                            SELECT MAX($timeFieldName) FROM $schema.$tableName r
                            $maxJoinQuery
                            WHERE r.cm_index_id=$indexId $timeFilteringQuery
                        ) AS max_time
                    """.trimIndent(),
                beforeQuery = beforeQuery,
                afterQuery = afterQuery,
            ) {
                it.map { rs -> rs.parseStatistics() }.filterNotNull().firstOrNull()
            }?.let {
                Triple(index, userFrequency, it)
            }
    }

    private fun ResultSet.parseStatistics(): DataAvailabilityTimeRange? {
        val minTimeTimestamp = this.getTimestamp("min_time")
        val maxTimeTimestamp = this.getTimestamp("max_time")
        return if (this.wasNull()) {
            null
        } else {
            DataAvailabilityTimeRange(
                minTime = minTimeTimestamp.toInstant().toLazyInstant(),
                maxTime = maxTimeTimestamp.toInstant().toLazyInstant(),
            )
        }
    }

    private data class QueryParams(
        val timeFilteringQuery: String,
        val minJoinQuery: String,
        val maxJoinQuery: String,
        val temporaryTimeZone: String? = null,
    )
}
