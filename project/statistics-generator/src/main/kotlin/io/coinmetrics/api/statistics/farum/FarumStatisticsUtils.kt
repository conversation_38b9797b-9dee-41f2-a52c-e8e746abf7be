package io.coinmetrics.api.statistics.farum

import io.coinmetrics.api.model.DataAvailabilityTimeRange
import io.coinmetrics.api.utils.LazyInstant.Companion.toLazyInstant
import io.coinmetrics.api.utils.SqlUtils
import io.coinmetrics.databases.Database
import java.sql.ResultSet

class FarumStatisticsUtils(
    val db: Database,
    val tableSuffix: String,
    val timeFieldName: String,
) {
    suspend fun calculateStatistics(): Map<String, DataAvailabilityTimeRange> =
        findAllFarumTables()
            .mapNotNull { queryFarumStatistics(it) }
            .sortedBy { it.first }
            .toMap()

    private suspend fun queryFarumStatistics(tableInfo: FarumTableInfo): Pair<String, DataAvailabilityTimeRange>? {
        val tableFullName = "${db.config.schema}.${tableInfo.tableName}"
        val queryString =
            """
            SELECT (
                SELECT $timeFieldName
                FROM $tableFullName
                ORDER BY $timeFieldName ASC
                LIMIT 1
            ) AS min_time,
            (
                SELECT $timeFieldName
                FROM $tableFullName
                ORDER BY $timeFieldName DESC
                LIMIT 1
            ) AS max_time
            """.trimIndent()

        val result = db.query(queryString) { query -> query.map { toStatistics(it) }.firstOrNull() }
        return if (result != null) Pair(tableInfo.assetName, result) else null
    }

    private fun toStatistics(resultSet: ResultSet): DataAvailabilityTimeRange? {
        val minTime = resultSet.getTimestamp("min_time") ?: return null
        val maxTime = resultSet.getTimestamp("max_time") ?: return null
        return DataAvailabilityTimeRange(
            minTime = minTime.toInstant().toLazyInstant(),
            maxTime = maxTime.toInstant().toLazyInstant(),
        )
    }

    private suspend fun findAllFarumTables(): List<FarumTableInfo> {
        val select = SqlUtils.createSearchQueryForTablesBySuffix(db.config.schema, tableSuffix)
        return db.query(select) { query -> query.map { rs -> toFarumTableInfo(rs) }.toList() }
    }

    private fun toFarumTableInfo(resultSet: ResultSet): FarumTableInfo {
        val tableName = resultSet.getString("relname")
        // $asset_$tableSuffix format is expected
        val assetName = tableName.removeSuffix(tableSuffix)
        return FarumTableInfo(tableName, assetName)
    }

    data class FarumTableInfo(
        val tableName: String,
        val assetName: String,
    )
}
