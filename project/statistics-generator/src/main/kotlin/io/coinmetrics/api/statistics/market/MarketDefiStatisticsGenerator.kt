package io.coinmetrics.api.statistics.market

import io.coinmetrics.api.model.ParsedMarket
import io.coinmetrics.api.persistence.DefiDatabase
import io.coinmetrics.api.resources.Resources
import io.coinmetrics.api.statistics.StatisticsGenerator
import io.coinmetrics.api.statistics.utils.InternalUtils
import io.coinmetrics.api.utils.toHex
import io.coinmetrics.databases.getLongOrNull
import io.coinmetrics.defi.client.DeFiRawDataParser
import io.coinmetrics.defi.client.model.Bytes
import io.coinmetrics.defi.client.model.RawMarketData
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.coroutineScope
import org.slf4j.LoggerFactory
import java.math.BigDecimal
import java.sql.ResultSet
import kotlin.system.measureTimeMillis

class MarketDefiStatisticsGenerator(
    val defiDb: DefiDatabase,
    val deFiRawDataParser: DeFiRawDataParser,
) : StatisticsGenerator<MarketDefiStatisticsModel> {
    companion object {
        private val log = LoggerFactory.getLogger(MarketDefiStatisticsGenerator::class.java)
    }

    override suspend fun generate(): MarketDefiStatisticsModel {
        val marketToStatistics: Map<String, MarketStatistics.Statistics>
        val exchangeToStatistics: Map<String, MarketStatistics.ExchangeStatistics>
        val marketToDefiFee: Map<String, DefiFee>
        val marketToDefiContractAddress: Map<String, String>
        val marketToDefiPoolAddress: Map<String, String>

        val timeSpent =
            measureTimeMillis {
                val supportedExchanges = findSupportedExchanges()
                val supportedNetworks = supportedExchanges.mapNotNull { it.network }.distinct()

                val poolMapping = findPoolMapping(supportedNetworks)
                val defiStatistics = supportedExchanges.map { defiStatistics(it.name, poolMapping) }.asSequence().flatten()

                marketToStatistics = defiStatistics.toStatisticsMap()
                exchangeToStatistics = defiStatistics.toStatisticsPerExchangeMap()

                findDefiData(supportedNetworks).let { defiData ->
                    marketToDefiFee = defiData.mapValuesTo(HashMap()) { (_, data) -> data.fee }
                    marketToDefiContractAddress = defiData.mapValuesTo(HashMap()) { (_, data) -> data.contractAddress }
                    marketToDefiPoolAddress = defiData.mapValuesTo(HashMap()) { (_, data) -> data.poolAddress.data.toHex() }
                }
            }
        log.info("Provided market statistics for DeFi in ${timeSpent}ms.")
        return MarketDefiStatisticsModel(
            marketToStatistics,
            exchangeToStatistics,
            marketToDefiFee,
            marketToDefiContractAddress,
            marketToDefiPoolAddress,
        )
    }

    private suspend fun findPoolMapping(supportedNetworks: List<String>): Map<DefiPool, Int> =
        coroutineScope {
            supportedNetworks
                .map { network ->
                    async {
                        defiDb
                            .query(
                                """
                                SELECT 
                                    pool_config_id,
                                    contract_address,
                                    base_handle, 
                                    quote_handle 
                                FROM ${defiDb.config.schema}.dex_raw_markets drm
                                    INNER JOIN ${defiDb.config.schema}.${network}_main_blocks ${network}mb ON drm.creation_block_hash=${network}mb.block_hash
                                """.trimIndent(),
                            ) {
                                it
                                    .map { rs ->
                                        val poolConfigId = rs.getInt("pool_config_id")
                                        val poolAddress = rs.getBytes("contract_address")
                                        val baseHandle = rs.getBytes("base_handle")
                                        val quoteHandle = rs.getBytes("quote_handle")

                                        DefiPool(
                                            poolAddress = Bytes(poolAddress),
                                            baseHandle = Bytes(baseHandle),
                                            quoteHandle = Bytes(quoteHandle),
                                            network = network,
                                        ) to poolConfigId
                                    }.toList()
                            }
                    }
                }.awaitAll()
        }.flatten()
            .toMap()

    private suspend fun findDefiData(supportedNetworks: List<String>): Map<String, DefiMarket> {
        return coroutineScope {
            supportedNetworks
                .map { network ->
                    async {
                        defiDb
                            .query(
                                """
                                SELECT 
                                    fee,
                                    price_includes_fee,
                                    variable_fee,
                                    encode(contract_address, 'hex') AS address_hexed, 
                                    exchange_id,
                                    base_handle,
                                    quote_handle,
                                    pool_config_id,
                                    contract_address 
                                FROM ${defiDb.config.schema}.dex_raw_markets drm
                                    INNER JOIN ${defiDb.config.schema}.${network}_main_blocks ${network}mb ON drm.creation_block_hash=${network}mb.block_hash
                                """.trimIndent(),
                            ) {
                                it
                                    .map { rs ->
                                        val exchangeId = rs.getInt("exchange_id")
                                        val poolMarketId =
                                            resolvePoolMarketId(exchangeId, rs)
                                                ?: return@map null
                                        val address = rs.getString("address_hexed")
                                        val fee = rs.getLongOrNull("fee")
                                        val priceIncludesFee = rs.getBoolean("price_includes_fee")
                                        val variableFee = rs.getBoolean("variable_fee")
                                        val poolConfigId = rs.getInt("pool_config_id")
                                        val poolAddress = rs.getBytes("contract_address")

                                        poolMarketId to
                                            DefiMarket(
                                                fee =
                                                    DefiFee(
                                                        fee =
                                                            fee?.let { actualFee ->
                                                                BigDecimal.valueOf(actualFee).divide(BigDecimal(10000)).toString()
                                                            },
                                                        priceIncludesFee = priceIncludesFee,
                                                        variableFee = variableFee,
                                                    ),
                                                contractAddress = address,
                                                poolConfigId = poolConfigId,
                                                poolAddress = Bytes(poolAddress),
                                                exchangeId = exchangeId,
                                            )
                                    }.toList()
                            }.filterNotNull()
                    }
                }.awaitAll()
        }.flatten()
            .toMap()
    }

    private fun resolvePoolMarketId(
        exchangeId: Int,
        rs: ResultSet,
    ): String? {
        val baseHandle = rs.getBytes("base_handle")
        val quoteHandle = rs.getBytes("quote_handle")
        val poolConfigId = rs.getInt("pool_config_id")

        return deFiRawDataParser
            .convertRawMarketToMDMarket(
                rawMarket =
                    RawMarketData(
                        exchangeId = exchangeId,
                        baseHandle = baseHandle,
                        quoteHandle = quoteHandle,
                        poolConfigId = poolConfigId,
                    ),
            ) ?.poolMarketId
    }

    private suspend fun findSupportedExchanges(): List<Resources.Exchange> =
        defiDb
            .query(
                """
                SELECT relname
                FROM pg_catalog.pg_class c
                    join pg_catalog.pg_namespace n on c.relnamespace = n.oid
                WHERE n.nspname = '${defiDb.config.schema}' AND relkind IN ('r', 'v') AND relname LIKE 'dex_raw_swaps_%';
                """.trimIndent(),
            ) {
                it.map { rs -> rs.getString("relname") }.filterNotNull()
            }.map { tableName ->
                tableName.replace("dex_raw_swaps_", "")
            }.mapNotNull { exchange ->
                Resources.getExchangeByName(exchange).onFailure { log.warn(it) }.getOrNull()
            }

    private suspend fun defiStatistics(
        exchangeName: String,
        poolMapping: Map<DefiPool, Int>,
    ): List<Pair<ParsedMarket.ParsedDefiMarket, MarketStatistics.Statistics>> {
        val schema = defiDb.config.schema
        val tableName = "$schema.dex_raw_swaps_$exchangeName"

        // We use a magic query to get statistics in seconds.
        // "Loose indexscan" technique made it possible:
        // https://wiki.postgresql.org/wiki/Loose_indexscan

        return defiDb.query(
            """
            WITH RECURSIVE t AS (
                (SELECT base_handle, quote_handle, pool_id
                 FROM $tableName
                 ORDER BY base_handle, quote_handle, pool_id
                 LIMIT 1)
                UNION ALL
                SELECT
                       (SELECT base_handle
                        FROM $tableName
                        WHERE (base_handle, quote_handle, pool_id) >
                              (t.base_handle, t.quote_handle, t.pool_id)
                        ORDER BY base_handle, quote_handle, pool_id
                        LIMIT 1),
                       (SELECT quote_handle
                        FROM $tableName
                        WHERE (base_handle, quote_handle, pool_id) >
                              (t.base_handle, t.quote_handle, t.pool_id)
                        ORDER BY base_handle, quote_handle, pool_id
                        LIMIT 1),
                       (SELECT pool_id
                        FROM $tableName
                        WHERE (base_handle, quote_handle, pool_id) >
                              (t.base_handle, t.quote_handle, t.pool_id)
                        ORDER BY base_handle, quote_handle, pool_id
                        LIMIT 1)
                FROM t
                WHERE t.base_handle IS NOT NULL
            )
            SELECT t.*,
                   (SELECT block_time
                    FROM $tableName
                    WHERE base_handle = t.base_handle
                      AND quote_handle = t.quote_handle
                      AND pool_id = t.pool_id
                    ORDER BY block_time
                    LIMIT 1) AS min_time,
                   (SELECT block_time
                    FROM $tableName
                    WHERE base_handle = t.base_handle
                      AND quote_handle = t.quote_handle
                      AND pool_id = t.pool_id
                    ORDER BY block_time DESC
                    LIMIT 1) AS max_time
            FROM t
            WHERE t.base_handle IS NOT NULL;
            """.trimIndent(),
        ) {
            it
                .map { rs ->
                    InternalUtils.parseDefiMarketStatistics(
                        exchangeName = exchangeName,
                        resultSet = rs,
                        deFiRawDataParser = deFiRawDataParser,
                        poolMapping = poolMapping,
                    )
                }.filterNotNull()
        }
    }
}

private data class DefiMarket(
    val fee: DefiFee,
    val contractAddress: String,
    val poolConfigId: Int,
    val poolAddress: Bytes,
    val exchangeId: Int,
)

data class DefiPool(
    val poolAddress: Bytes,
    val baseHandle: Bytes,
    val quoteHandle: Bytes,
    val network: String,
)
