package io.coinmetrics.api.statistics.app

import ch.qos.logback.classic.ClassicConstants
import io.coinmetrics.api.model.S3BooksMarketType
import io.coinmetrics.api.persistence.Databases
import io.coinmetrics.api.statistics.InMemStatisticsRepository
import io.coinmetrics.api.statistics.StatisticsDescriptor
import io.coinmetrics.api.statistics.StatisticsGenerator
import io.coinmetrics.api.statistics.blockchain.UdmV2Statistics
import io.coinmetrics.api.statistics.blockchain.UdmV2StatisticsGenerator
import io.coinmetrics.api.statistics.defi.DefiStatistics
import io.coinmetrics.api.statistics.defi.DefiStatisticsGenerator
import io.coinmetrics.api.statistics.farum.AssetChainsStatistics
import io.coinmetrics.api.statistics.farum.AssetChainsStatisticsGenerator
import io.coinmetrics.api.statistics.farum.MempoolFeeratesStatistics
import io.coinmetrics.api.statistics.farum.MempoolFeeratesStatisticsGenerator
import io.coinmetrics.api.statistics.farum.MiningPoolTipsSummaryStatistics
import io.coinmetrics.api.statistics.farum.MiningPoolTipsSummaryStatisticsGenerator
import io.coinmetrics.api.statistics.farum.TransactionTrackerStatistics
import io.coinmetrics.api.statistics.farum.TransactionTrackerStatisticsGenerator
import io.coinmetrics.api.statistics.index.IndexCandlesStatistics
import io.coinmetrics.api.statistics.index.IndexCandlesStatisticsGenerator
import io.coinmetrics.api.statistics.index.IndexConstituentsStatistics
import io.coinmetrics.api.statistics.index.IndexConstituentsStatisticsGenerator
import io.coinmetrics.api.statistics.index.IndexStatistics
import io.coinmetrics.api.statistics.index.IndexStatisticsGenerator
import io.coinmetrics.api.statistics.market.FutureMetadataStatistics
import io.coinmetrics.api.statistics.market.FutureMetadataStatisticsGenerator
import io.coinmetrics.api.statistics.market.MarketCandlesStatistics
import io.coinmetrics.api.statistics.market.MarketCandlesStatisticsGenerator
import io.coinmetrics.api.statistics.market.MarketColdBooksStatistics
import io.coinmetrics.api.statistics.market.MarketColdBooksStatisticsGenerator
import io.coinmetrics.api.statistics.market.MarketDefiStatistics
import io.coinmetrics.api.statistics.market.MarketDefiStatisticsGenerator
import io.coinmetrics.api.statistics.market.MarketFundingRatesPredictedStatistics
import io.coinmetrics.api.statistics.market.MarketFundingRatesPredictedStatisticsGenerator
import io.coinmetrics.api.statistics.market.MarketFundingRatesStatistics
import io.coinmetrics.api.statistics.market.MarketFundingRatesStatisticsGenerator
import io.coinmetrics.api.statistics.market.MarketFutureTickerStatistics
import io.coinmetrics.api.statistics.market.MarketFutureTickerStatisticsGenerator
import io.coinmetrics.api.statistics.market.MarketHotBooksStatistics
import io.coinmetrics.api.statistics.market.MarketHotBooksStatisticsGenerator
import io.coinmetrics.api.statistics.market.MarketHotBooksStatisticsLegacyGenerator
import io.coinmetrics.api.statistics.market.MarketLiquidationsStatistics
import io.coinmetrics.api.statistics.market.MarketLiquidationsStatisticsGenerator
import io.coinmetrics.api.statistics.market.MarketMetricsStatistics
import io.coinmetrics.api.statistics.market.MarketMetricsStatisticsGenerator
import io.coinmetrics.api.statistics.market.MarketOpenInterestStatistics
import io.coinmetrics.api.statistics.market.MarketOpenInterestStatisticsGenerator
import io.coinmetrics.api.statistics.market.MarketOptionTickerStatistics
import io.coinmetrics.api.statistics.market.MarketOptionTickerStatisticsGenerator
import io.coinmetrics.api.statistics.market.MarketTradesStatistics
import io.coinmetrics.api.statistics.market.MarketTradesStatisticsGenerator
import io.coinmetrics.api.statistics.market.OptionMetadataStatistics
import io.coinmetrics.api.statistics.market.OptionMetadataStatisticsGenerator
import io.coinmetrics.api.statistics.market.SpotMetadataStatistics
import io.coinmetrics.api.statistics.market.SpotMetadataStatisticsGenerator
import io.coinmetrics.api.statistics.metrics.AssetMetricStatistics
import io.coinmetrics.api.statistics.metrics.AssetMetricStatisticsGenerator
import io.coinmetrics.api.statistics.metrics.ExchangeAssetMetricStatistics
import io.coinmetrics.api.statistics.metrics.ExchangeAssetMetricStatisticsGenerator
import io.coinmetrics.api.statistics.metrics.ExchangeMetricStatistics
import io.coinmetrics.api.statistics.metrics.ExchangeMetricStatisticsGenerator
import io.coinmetrics.api.statistics.metrics.InstitutionMetricStatistics
import io.coinmetrics.api.statistics.metrics.InstitutionMetricStatisticsGenerator
import io.coinmetrics.api.statistics.metrics.PairMetricStatistics
import io.coinmetrics.api.statistics.metrics.PairMetricStatisticsGenerator
import io.coinmetrics.api.statistics.pair.PairCandlesStatistics
import io.coinmetrics.api.statistics.pair.PairCandlesStatisticsGenerator
import io.coinmetrics.api.statistics.securitymaster.EndOfDayMarketCandleStatistics
import io.coinmetrics.api.statistics.securitymaster.EndOfDayMarketCandleStatisticsGenerator
import io.coinmetrics.api.statistics.tagging.AddressTaggingStatistics
import io.coinmetrics.api.statistics.tagging.AddressTaggingStatisticsGenerator
import io.coinmetrics.api.utils.S3Utils
import io.coinmetrics.defi.client.DeFiRawDataParser
import io.coinmetrics.defi.client.DeFiResources
import io.coinmetrics.healthchecks.BooleanHttpReadyState
import io.coinmetrics.healthchecks.HttpHealthCheck
import io.coinmetrics.sharedfiles.newS3SharedFiles
import io.prometheus.metrics.exporter.httpserver.HTTPServer
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.cancelAndJoin
import kotlinx.coroutines.job
import kotlinx.coroutines.runBlocking
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import java.lang.management.ManagementFactory
import java.time.Duration
import java.util.EnumMap
import java.util.TimeZone
import java.util.concurrent.atomic.AtomicBoolean
import javax.management.remote.JMXConnectorServerFactory
import javax.management.remote.JMXServiceURL
import kotlin.coroutines.suspendCoroutine

class StatisticsApp(
    private val deFiRawDataParser: DeFiRawDataParser,
    private val statisticsConfig: StatisticsAppConfig,
) {
    companion object {
        val log: Logger = LoggerFactory.getLogger(StatisticsApp::class.java)
    }

    private val monitoring = StatisticsAppMonitoring()
    private val dispatcher: CoroutineDispatcher
    private val dispatcherMonitoringScope: CoroutineScope

    init {
        monitoring.dispatcherWithMonitoring(Dispatchers.Default, "default").also { (d, s) ->
            dispatcher = d
            dispatcherMonitoringScope = s
        }
    }

    private val healthCheckState = BooleanHttpReadyState()
    private val healthCheck = HttpHealthCheck(healthCheckState, statisticsConfig.healthCheckPort)
    private val prometheusServer =
        HTTPServer
            .builder()
            .port(statisticsConfig.prometheusPort)
            .registry(monitoring.registry)
            .buildAndStart()
    private val databases = Databases(statisticsConfig.databases, monitoring.registry)

    // for now, we have only one storageClient per data type
    private val s3StorageClients =
        statisticsConfig.booksTiers
            .mapValuesTo(EnumMap(S3BooksMarketType::class.java)) { (_, tiers) ->
                S3Utils.createS3StorageClient(
                    tiers,
                    monitoring.s3Latency,
                )
            }
    private val s3Databases =
        s3StorageClients.mapValuesTo(EnumMap(S3BooksMarketType::class.java)) { (_, s3StorageClient) ->
            s3StorageClient?.let { S3Utils.createS3Databases(it, statisticsConfig.dellPowerScaleBugsWorkarounds) }
        }

    private val sharedFiles =
        newS3SharedFiles(
            statisticsConfig.statsS3Endpoint,
            statisticsConfig.statsS3Region,
            statisticsConfig.statsS3AccessKey,
            statisticsConfig.statsS3SecretKey,
        )

    private val repository = InMemStatisticsRepository()
    private val manager =
        StatisticsManager(
            repository,
            sharedFiles,
            statisticsConfig.statisticsFileGroupIdPrefix,
            monitoring,
            dispatcher,
        ).apply {
            addIfEnabled(
                UdmV2Statistics.descriptor,
                UdmV2StatisticsGenerator(databases.atlas.values.toList()),
                interval = Duration.ofSeconds(statisticsConfig.udmStatisticsRefreshIntervalSec.toLong()),
            )

            addIfEnabled(
                DefiStatistics.descriptor,
                DefiStatisticsGenerator(databases.defi),
                interval = Duration.ofMillis(statisticsConfig.defiStatisticsRefreshIntervalMs.toLong()),
            )

            addIfEnabled(
                AssetChainsStatistics.descriptor,
                AssetChainsStatisticsGenerator(databases.chainMonitor),
                interval = Duration.ofSeconds(statisticsConfig.institutionMetricStatisticsRefreshIntervalSec.toLong()),
            )
            addIfEnabled(
                MempoolFeeratesStatistics.descriptor,
                MempoolFeeratesStatisticsGenerator(databases.chainMonitor),
                interval = Duration.ofSeconds(statisticsConfig.institutionMetricStatisticsRefreshIntervalSec.toLong()),
            )
            addIfEnabled(
                MiningPoolTipsSummaryStatistics.descriptor,
                MiningPoolTipsSummaryStatisticsGenerator(databases.chainMonitor),
                interval = Duration.ofSeconds(statisticsConfig.institutionMetricStatisticsRefreshIntervalSec.toLong()),
            )
            addIfEnabled(
                TransactionTrackerStatistics.descriptor,
                TransactionTrackerStatisticsGenerator(databases.chainMonitor),
                interval = Duration.ofSeconds(statisticsConfig.institutionMetricStatisticsRefreshIntervalSec.toLong()),
            )

            addIfEnabled(
                IndexCandlesStatistics.descriptor,
                IndexCandlesStatisticsGenerator(databases.indexes),
                interval = Duration.ofSeconds(statisticsConfig.indexCandlesStatisticsRefreshIntervalSec.toLong()),
            )
            addIfEnabled(
                IndexStatistics.descriptor,
                IndexStatisticsGenerator(databases.indexes),
                interval = Duration.ofSeconds(statisticsConfig.indexStatisticsRefreshIntervalSec.toLong()),
            )
            addIfEnabled(
                IndexConstituentsStatistics.descriptor,
                IndexConstituentsStatisticsGenerator(databases.indexes),
                interval = Duration.ofSeconds(statisticsConfig.indexConstituentsStatisticsRefreshIntervalSec.toLong()),
            )

            addIfEnabled(
                FutureMetadataStatistics.descriptor,
                FutureMetadataStatisticsGenerator(databases.futures),
                interval = Duration.ofMillis(statisticsConfig.marketFutureTickerStatisticsRefreshIntervalMs.toLong()),
            )
            addIfEnabled(
                OptionMetadataStatistics.descriptor,
                OptionMetadataStatisticsGenerator(databases.futures),
                interval = Duration.ofMillis(statisticsConfig.marketOptionTickerStatisticsRefreshIntervalMs.toLong()),
            )
            addIfEnabled(
                SpotMetadataStatistics.descriptor,
                SpotMetadataStatisticsGenerator(databases.tradesSpot),
                interval = Duration.ofMillis(statisticsConfig.marketTradesStatisticsRefreshIntervalMs.toLong()),
            )
            addIfEnabled(
                MarketHotBooksStatistics.descriptor,
                if (statisticsConfig.useNewBooksTables) {
                    MarketHotBooksStatisticsGenerator(
                        databases.books,
                        databases.tradesSpot,
                    )
                } else {
                    MarketHotBooksStatisticsLegacyGenerator(
                        databases.books,
                    )
                },
                interval = Duration.ofMillis(statisticsConfig.marketHotBooksStatisticsRefreshIntervalMs.toLong()),
            )
            addIfEnabled(
                MarketColdBooksStatistics.descriptor,
                MarketColdBooksStatisticsGenerator(repository, s3Databases),
                interval = Duration.ofMillis(statisticsConfig.marketColdBooksStatisticsRefreshIntervalMs.toLong()),
            )
            addIfEnabled(
                MarketCandlesStatistics.descriptor,
                MarketCandlesStatisticsGenerator(databases.candles),
                interval = Duration.ofMillis(statisticsConfig.marketCandlesStatisticsRefreshIntervalMs.toLong()),
            )
            databases.defi?.let {
                addIfEnabled(
                    MarketDefiStatistics.descriptor,
                    MarketDefiStatisticsGenerator(databases.defi ?: error("Unexpected null defi database"), deFiRawDataParser),
                    interval = Duration.ofMillis(statisticsConfig.marketDefiStatisticsRefreshIntervalMs.toLong()),
                )
            }
            addIfEnabled(
                MarketFutureTickerStatistics.descriptor,
                MarketFutureTickerStatisticsGenerator(databases.tradesDeriv),
                interval = Duration.ofMillis(statisticsConfig.marketFutureTickerStatisticsRefreshIntervalMs.toLong()),
            )
            addIfEnabled(
                MarketFundingRatesStatistics.descriptor,
                MarketFundingRatesStatisticsGenerator(databases.futures),
                interval = Duration.ofMillis(statisticsConfig.marketFundingRatesStatisticsRefreshIntervalMs.toLong()),
            )
            addIfEnabled(
                MarketFundingRatesPredictedStatistics.descriptor,
                MarketFundingRatesPredictedStatisticsGenerator(databases.tradesDeriv),
                interval = Duration.ofMillis(statisticsConfig.marketFundingRatesPredictedStatisticsRefreshIntervalMs.toLong()),
            )
            addIfEnabled(
                MarketLiquidationsStatistics.descriptor,
                MarketLiquidationsStatisticsGenerator(databases.futures),
                interval = Duration.ofMillis(statisticsConfig.marketLiquidationsStatisticsRefreshIntervalMs.toLong()),
            )
            addIfEnabled(
                MarketMetricsStatistics.descriptor,
                MarketMetricsStatisticsGenerator(databases.metrics),
                interval = Duration.ofMillis(statisticsConfig.marketMetricsStatisticsRefreshIntervalMs.toLong()),
            )
            addIfEnabled(
                MarketOptionTickerStatistics.descriptor,
                MarketOptionTickerStatisticsGenerator(databases.futures),
                interval = Duration.ofMillis(statisticsConfig.marketOptionTickerStatisticsRefreshIntervalMs.toLong()),
            )
            addIfEnabled(
                MarketTradesStatistics.descriptor,
                MarketTradesStatisticsGenerator(databases.tradesSpot, databases.tradesDeriv),
                interval = Duration.ofMillis(statisticsConfig.marketTradesStatisticsRefreshIntervalMs.toLong()),
            )
            addIfEnabled(
                MarketOpenInterestStatistics.descriptor,
                MarketOpenInterestStatisticsGenerator(databases.futures),
                interval = Duration.ofMillis(statisticsConfig.marketOpenInterestStatisticsRefreshIntervalMs.toLong()),
            )

            addIfEnabled(
                AssetMetricStatistics.descriptor,
                AssetMetricStatisticsGenerator(
                    databases.network,
                    databases.hourlyNetwork,
                    databases.minutelyNetwork,
                    databases.rates,
                    databases.metrics,
                    databases.chainMonitor,
                    databases.factory,
                    databases.principalPrice,
                ),
                interval = Duration.ofSeconds(statisticsConfig.assetMetricStatisticsRefreshIntervalSec.toLong()),
            )
            addIfEnabled(
                ExchangeAssetMetricStatistics.descriptor,
                ExchangeAssetMetricStatisticsGenerator(databases.metrics),
                interval = Duration.ofSeconds(statisticsConfig.exchangeAssetMetricStatisticsRefreshIntervalSec.toLong()),
            )
            addIfEnabled(
                ExchangeMetricStatistics.descriptor,
                ExchangeMetricStatisticsGenerator(databases.metrics),
                interval = Duration.ofSeconds(statisticsConfig.exchangeMetricStatisticsRefreshIntervalSec.toLong()),
            )
            addIfEnabled(
                InstitutionMetricStatistics.descriptor,
                InstitutionMetricStatisticsGenerator(databases.futures),
                interval = Duration.ofSeconds(statisticsConfig.institutionMetricStatisticsRefreshIntervalSec.toLong()),
            )
            addIfEnabled(
                PairMetricStatistics.descriptor,
                PairMetricStatisticsGenerator(databases.metrics),
                interval = Duration.ofSeconds(statisticsConfig.pairMetricStatisticsRefreshIntervalSec.toLong()),
            )

            addIfEnabled(
                PairCandlesStatistics.descriptor,
                PairCandlesStatisticsGenerator(databases.rates),
                interval = Duration.ofSeconds(statisticsConfig.pairCandlesStatisticsRefreshIntervalSec.toLong()),
            )

            addIfEnabled(
                EndOfDayMarketCandleStatistics.descriptor,
                EndOfDayMarketCandleStatisticsGenerator(databases.candles),
                interval = Duration.ofMillis(statisticsConfig.marketCandlesStatisticsRefreshIntervalMs.toLong()),
            )

            addIfEnabled(
                AddressTaggingStatistics.descriptor,
                AddressTaggingStatisticsGenerator(databases.addressTagging),
                interval = Duration.ofSeconds(statisticsConfig.addressTaggingStatisticsRefreshIntervalSec.toLong()),
            )
        }

    suspend fun start() {
        repository.start()
        manager.start()
        healthCheckState.setState(true)
    }

    suspend fun close() {
        dispatcherMonitoringScope.coroutineContext.job.cancelAndJoin()
        manager.close()
        repository.close()
        sharedFiles.close()
        s3StorageClients.values.forEach { it?.close() }
        databases.close()
        healthCheckState.setState(false)
        healthCheck.close()
        prometheusServer.close()
    }

    private fun <T> StatisticsManager.addIfEnabled(
        descriptor: StatisticsDescriptor<T>,
        generator: StatisticsGenerator<T>,
        interval: Duration,
    ) {
        if (!statisticsConfig.disabledStatistics.contains(descriptor)) {
            add(descriptor, generator, interval)
        }
    }
}

suspend fun main() {
    TimeZone.setDefault(TimeZone.getTimeZone("UTC"))
    if (System.getenv("API_ENV") == "production") {
        System.setProperty(ClassicConstants.CONFIG_FILE_PROPERTY, "production/logback.xml")
    }

    val log = StatisticsApp.log
    val appName = "statistics"

    // Ensure process is killed on any critical error.
    val haltedOnCriticalError = AtomicBoolean()
    Thread.setDefaultUncaughtExceptionHandler { t, e ->
        val shouldHalt = e is VirtualMachineError && haltedOnCriticalError.compareAndSet(false, true)
        try {
            log.error("Uncaught exception in thread \"${t.name}\"", e)
            if (shouldHalt) {
                log.error("Critical error, JVM will halt", e)
            }
        } finally {
            if (shouldHalt) {
                Runtime.getRuntime().halt(1)
            }
        }
    }

    // JMX
    System.getProperty("io.coinmetrics.jmx.port")?.toIntOrNull()?.also { port ->
        try {
            JMXConnectorServerFactory
                .newJMXConnectorServer(
                    JMXServiceURL("jmxmp", null, port),
                    null,
                    ManagementFactory.getPlatformMBeanServer(),
                ).start()
        } catch (e: Exception) {
            log.error("Error starting JMXMP connector", e)
        }
    }

    // Ensure assertion conditions is not evaluated without -ea.
    if (!StatisticsApp::class.java.desiredAssertionStatus()) {
        assert(false) {
            "-Xassertions=jvm must be enabled in Kotlin compiler to avoid performance hit " +
                "from evaluating assert conditions."
        }
    }

    log.info("Starting $appName")

    val statisticsApp =
        StatisticsApp(
            deFiRawDataParser =
                DeFiRawDataParser(
                    DeFiResources(
                        currencyJsonPath = "currency.json",
                        exchangeJsonPath = "exchange.json",
                    ),
                ),
            statisticsConfig = StatisticsAppConfig(),
        )
    statisticsApp.start()

    suspendCoroutine<Nothing> {
        Runtime.getRuntime().addShutdownHook(
            Thread {
                runBlocking {
                    log.info("Stopping $appName")
                    statisticsApp.close()
                    log.info("Stopped $appName")
                }
            },
        )
    }
}
