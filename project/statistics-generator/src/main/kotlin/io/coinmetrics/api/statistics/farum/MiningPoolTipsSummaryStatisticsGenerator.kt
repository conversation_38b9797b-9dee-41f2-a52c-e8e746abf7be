package io.coinmetrics.api.statistics.farum

import io.coinmetrics.api.model.DataAvailabilityTimeRange
import io.coinmetrics.api.statistics.StatisticsGenerator
import io.coinmetrics.databases.Database

class MiningPoolTipsSummaryStatisticsGenerator(
    private val db: Database,
) : StatisticsGenerator<Map<String, DataAvailabilityTimeRange>> {
    override suspend fun generate(): Map<String, DataAvailabilityTimeRange> =
        FarumStatisticsUtils(db, "_mining_pool_tips_summary", "time").calculateStatistics()
}
