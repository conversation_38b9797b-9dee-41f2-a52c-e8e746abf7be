package io.coinmetrics.api.statistics.metrics

import io.coinmetrics.api.model.DataAvailabilityTimeRange
import io.coinmetrics.api.resources.Resources
import io.coinmetrics.api.statistics.StatisticsGenerator
import io.coinmetrics.api.utils.LazyInstant.Companion.toLazyInstant
import io.coinmetrics.api.utils.Quadruple
import io.coinmetrics.databases.Database
import org.slf4j.LoggerFactory
import java.sql.ResultSet

class InstitutionMetricStatisticsGenerator(
    private val db: Database,
) : StatisticsGenerator<InstitutionMetricStatisticsModel> {
    companion object {
        private val log = LoggerFactory.getLogger(InstitutionMetricStatisticsGenerator::class.java)
    }

    override suspend fun generate(): InstitutionMetricStatisticsModel {
        val schema = db.config.schema
        val table = "$schema.institution_metrics"

        val (supportedInstitutions, statisticsPerInstitution, statisticsPerMetric) =
            db.query(
                """
                WITH RECURSIVE t AS (
                   (SELECT institution_id, metric FROM $table ORDER BY institution_id, metric LIMIT 1)
                   UNION ALL
                   SELECT (SELECT institution_id FROM $table WHERE (institution_id, metric) > (t.institution_id, t.metric) ORDER BY institution_id, metric LIMIT 1), (SELECT metric FROM $table WHERE (institution_id, metric) > (t.institution_id, t.metric) ORDER BY institution_id, metric LIMIT 1)
                   FROM t
                   WHERE t.institution_id IS NOT NULL
                   )
                SELECT t.*, (select time from $table where institution_id=t.institution_id AND metric=t.metric order by time limit 1) as min_time, (select time from $table where institution_id=t.institution_id AND metric=t.metric order by time desc limit 1) as max_time FROM t WHERE t.institution_id IS NOT NULL
                """.trimIndent(),
            ) {
                it
                    .map { rs -> rs.toInstitutionMetricEntry() }
                    .asSequence()
                    .filterNotNull()
                    .collectStatisticsItems()
            }
        return InstitutionMetricStatisticsModel(supportedInstitutions, statisticsPerMetric, statisticsPerInstitution)
    }

    private fun ResultSet.toInstitutionMetricEntry(): Quadruple<String, String, String, DataAvailabilityTimeRange>? =
        Resources
            .getInstitutionNameById(getInt("institution_id"))
            .map { institution ->
                val metric = getString("metric")
                val timeRange =
                    DataAvailabilityTimeRange(
                        minTime = getTimestamp("min_time").toInstant().toLazyInstant(),
                        maxTime = getTimestamp("max_time").toInstant().toLazyInstant(),
                    )
                Quadruple(institution, metric, "1d", timeRange)
            }.onFailure { errorMessage ->
                log.warn(errorMessage)
            }.getOrNull()
}
