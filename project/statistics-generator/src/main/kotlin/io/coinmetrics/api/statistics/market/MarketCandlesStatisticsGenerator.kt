package io.coinmetrics.api.statistics.market

import io.coinmetrics.api.CommonConstants
import io.coinmetrics.api.model.DerivativesMarketType
import io.coinmetrics.api.model.ParsedMarket
import io.coinmetrics.api.resources.Resources
import io.coinmetrics.api.statistics.StatisticsGenerator
import io.coinmetrics.api.statistics.utils.InternalUtils
import io.coinmetrics.api.utils.SqlUtils
import io.coinmetrics.databases.Database
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import org.slf4j.LoggerFactory
import java.sql.ResultSet
import kotlin.system.measureTimeMillis

class MarketCandlesStatisticsGenerator(
    val candlesDb: Database,
) : StatisticsGenerator<MarketCandlesStatisticsModel> {
    companion object {
        private val log = LoggerFactory.getLogger(MarketCandlesStatisticsGenerator::class.java)
    }

    override suspend fun generate(): MarketCandlesStatisticsModel {
        val candlesStatisticsPerMarketMap: Map<String, Map<String, MarketStatistics.Statistics>>
        val timeSpent =
            measureTimeMillis {
                val candlesSpotTableInfo =
                    getCandleTables(
                        candlesDb,
                        candlesDb.config.schema,
                        "candles_market_spot_",
                    ).groupBy { it.frequency }
                val candlesFuturesTableInfo =
                    getCandleTables(
                        candlesDb,
                        candlesDb.config.schema,
                        "candles_market_futures_",
                    ).groupBy { it.frequency }
                val candlesOptionsTableInfo =
                    getCandleTables(
                        candlesDb,
                        candlesDb.config.schema,
                        "candles_market_options_",
                    ).groupBy { it.frequency }
                val candlesDefiTableInfo =
                    getCandleTables(
                        candlesDb,
                        candlesDb.config.schema,
                        "candles_market_defi_",
                    ).groupBy { it.frequency }

                candlesStatisticsPerMarketMap =
                    coroutineScope {
                        CommonConstants.candleFrequenciesMap.keys
                            .map { frequencyKey ->
                                val spotStatistics =
                                    async { candlesSpotTableInfo[frequencyKey]?.flatMap { getSpotCandleStatistics(it) } ?: emptyList() }
                                val defiStatistics =
                                    async { candlesDefiTableInfo[frequencyKey]?.flatMap { getDefiCandleStatistics(it) } ?: emptyList() }
                                val futuresStatistics =
                                    async {
                                        candlesFuturesTableInfo[frequencyKey]?.flatMap {
                                            getDerivativesCandleStatistics(DerivativesMarketType.FUTURE, it)
                                        } ?: emptyList()
                                    }
                                val optionsStatistics =
                                    async {
                                        candlesOptionsTableInfo[frequencyKey]?.flatMap {
                                            getDerivativesCandleStatistics(DerivativesMarketType.OPTION, it)
                                        } ?: emptyList()
                                    }
                                val statisticsMap =
                                    (
                                        spotStatistics.await() + defiStatistics.await() +
                                            futuresStatistics.await() + optionsStatistics.await()
                                    ).toStatisticsMap()
                                frequencyKey to statisticsMap
                            }.associateTo(HashMap()) { it }
                    }
            }
        log.info("Generated market statistics for candles in ${timeSpent}ms.")
        return MarketCandlesStatisticsModel(candlesStatisticsPerMarketMap)
    }

    private suspend fun getSpotCandleStatistics(tableInfo: CandleTableInfo): List<Pair<ParsedMarket, MarketStatistics.Statistics>> =
        getSpotCandleStatistics(tableInfo) { rs -> InternalUtils.parseSpotMarketStatistics(rs, "candle") }

    private suspend fun getDefiCandleStatistics(tableInfo: CandleTableInfo): List<Pair<ParsedMarket, MarketStatistics.Statistics>> =
        getMultiPoolDeFiCandleStatistics(tableInfo) { rs: ResultSet ->
            InternalUtils.parseDefiMarketStatistics(rs, "candle", poolIdSupported = true)
        }

    private suspend fun getSpotCandleStatistics(
        tableInfo: CandleTableInfo,
        resultMapper: (ResultSet) -> Pair<ParsedMarket, MarketStatistics.Statistics>?,
    ): List<Pair<ParsedMarket, MarketStatistics.Statistics>> {
        val schema = candlesDb.config.schema

        // We use a magic query to get statistics in seconds.
        // "Loose indexscan" technique made it possible:
        // https://wiki.postgresql.org/wiki/Loose_indexscan

        return candlesDb.query(
            """
            WITH RECURSIVE t AS (
                (SELECT candle_base_id, candle_quote_id
                 FROM $schema.${tableInfo.tableName}
                 ORDER BY candle_base_id, candle_quote_id
                 LIMIT 1)
                UNION ALL
                SELECT
                       (SELECT candle_base_id
                        FROM $schema.${tableInfo.tableName}
                        WHERE (candle_base_id, candle_quote_id) >
                              (t.candle_base_id, t.candle_quote_id)
                        ORDER BY candle_base_id, candle_quote_id
                        LIMIT 1),
                       (SELECT candle_quote_id
                        FROM $schema.${tableInfo.tableName}
                        WHERE (candle_base_id, candle_quote_id) >
                              (t.candle_base_id, t.candle_quote_id)
                        ORDER BY candle_base_id, candle_quote_id
                        LIMIT 1)
                FROM t
                WHERE t.candle_base_id IS NOT NULL AND t.candle_quote_id IS NOT NULL
            )
            SELECT ${tableInfo.exchangeId} as candle_exchange_id, t.*,
                   (SELECT candle_start_time
                    FROM $schema.${tableInfo.tableName}
                    WHERE candle_base_id = t.candle_base_id AND candle_quote_id = t.candle_quote_id
                    ORDER BY candle_start_time
                    LIMIT 1) as min_time,
                   (SELECT candle_start_time
                    FROM $schema.${tableInfo.tableName}
                    WHERE candle_base_id = t.candle_base_id AND candle_quote_id = t.candle_quote_id
                    ORDER BY candle_start_time desc
                    LIMIT 1) as max_time
            FROM t
            WHERE t.candle_base_id IS NOT NULL AND t.candle_quote_id IS NOT NULL
            """.trimIndent(),
        ) {
            it.map(resultMapper).filterNotNull()
        }
    }

    private suspend fun getMultiPoolDeFiCandleStatistics(
        tableInfo: CandleTableInfo,
        resultMapper: (ResultSet) -> Pair<ParsedMarket, MarketStatistics.Statistics>?,
    ): List<Pair<ParsedMarket, MarketStatistics.Statistics>> {
        val schema = candlesDb.config.schema

        // We use a magic query to get statistics in seconds.
        // "Loose indexscan" technique made it possible:
        // https://wiki.postgresql.org/wiki/Loose_indexscan

        return candlesDb.query(
            """
            WITH RECURSIVE t AS (
                (SELECT candle_pool_id, candle_base_id, candle_quote_id
                 FROM $schema.${tableInfo.tableName}
                 ORDER BY candle_pool_id, candle_base_id, candle_quote_id
                 LIMIT 1)
                UNION ALL
                SELECT
                    (SELECT candle_pool_id
                     FROM $schema.${tableInfo.tableName}
                     WHERE (candle_pool_id, candle_base_id, candle_quote_id) >
                           (t.candle_pool_id, t.candle_base_id, t.candle_quote_id)
                     ORDER BY candle_pool_id, candle_base_id, candle_quote_id
                     LIMIT 1),
                    (SELECT candle_base_id
                     FROM $schema.${tableInfo.tableName}
                     WHERE (candle_pool_id, candle_base_id, candle_quote_id) >
                           (t.candle_pool_id, t.candle_base_id, t.candle_quote_id)
                     ORDER BY candle_pool_id, candle_base_id, candle_quote_id
                     LIMIT 1),
                    (SELECT candle_quote_id
                     FROM $schema.${tableInfo.tableName}
                     WHERE (candle_pool_id, candle_base_id, candle_quote_id) >
                           (t.candle_pool_id, t.candle_base_id, t.candle_quote_id)
                     ORDER BY candle_pool_id, candle_base_id, candle_quote_id
                     LIMIT 1)
                FROM t
                WHERE t.candle_pool_id IS NOT NULL AND t.candle_base_id IS NOT NULL AND t.candle_quote_id IS NOT NULL
            )
            SELECT ${tableInfo.exchangeId} as candle_exchange_id, t.*,
                   (SELECT candle_start_time
                    FROM $schema.${tableInfo.tableName}
                    WHERE candle_pool_id = t.candle_pool_id AND candle_base_id = t.candle_base_id AND candle_quote_id = t.candle_quote_id
                    ORDER BY candle_start_time
                    LIMIT 1) as min_time,
                   (SELECT candle_start_time
                    FROM $schema.${tableInfo.tableName}
                    WHERE candle_pool_id = t.candle_pool_id AND candle_base_id = t.candle_base_id AND candle_quote_id = t.candle_quote_id
                    ORDER BY candle_start_time desc
                    LIMIT 1) as max_time
            FROM t
            WHERE t.candle_pool_id IS NOT NULL AND t.candle_base_id IS NOT NULL AND t.candle_quote_id IS NOT NULL;
            """.trimIndent(),
        ) {
            it.map(resultMapper).filterNotNull()
        }
    }

    private suspend fun getDerivativesCandleStatistics(
        marketType: DerivativesMarketType,
        tableInfo: CandleTableInfo,
    ): List<Pair<ParsedMarket.ParsedDerivativesMarket, MarketStatistics.Statistics>> {
        val schema = candlesDb.config.schema

        return candlesDb.query(
            """
            WITH RECURSIVE t AS (
                (SELECT candle_symbol FROM $schema.${tableInfo.tableName} ORDER BY candle_symbol LIMIT 1)
                UNION ALL
                SELECT (SELECT candle_symbol FROM $schema.${tableInfo.tableName} WHERE candle_symbol > t.candle_symbol ORDER BY candle_symbol LIMIT 1)
                FROM t
                WHERE t.candle_symbol IS NOT NULL
            )
            SELECT ${tableInfo.exchangeId} as candle_exchange_id, t.*
                , (SELECT candle_start_time FROM $schema.${tableInfo.tableName} WHERE candle_symbol = t.candle_symbol ORDER BY candle_start_time ASC  LIMIT 1) as min_time
                , (SELECT candle_start_time FROM $schema.${tableInfo.tableName} WHERE candle_symbol = t.candle_symbol ORDER BY candle_start_time DESC LIMIT 1) as max_time
            FROM t
            WHERE t.candle_symbol IS NOT NULL;   
            """.trimIndent(),
        ) {
            it
                .map { rs ->
                    InternalUtils.parseDerivativeMarketStatistics(rs, marketType, "candle")
                }.filterNotNull()
        }
    }

    private suspend fun getCandleTables(
        targetDb: Database,
        schema: String,
        targetTablePrefix: String,
    ): List<CandleTableInfo> {
        val sqlQuery = SqlUtils.createSearchQueryForTablesByPrefix(schema, targetTablePrefix)
        val tableInfo =
            targetDb.query(sqlQuery) {
                it.map { rs -> toCandleTableInfo(targetTablePrefix, rs) }.toList().filterNotNull()
            }
        return tableInfo
    }

    private fun toCandleTableInfo(
        targetTablePrefix: String,
        resultSet: ResultSet,
    ): CandleTableInfo? {
        val tableName = resultSet.getString("relname")
        // candles_market_{marketType}_{exchangeId}_{frequency} (e.g. candles_market_spot_4_1h) is expected.
        val exchangeIdAndFrequency = tableName.removePrefix(targetTablePrefix).split("_")
        if (exchangeIdAndFrequency.size != 2) {
            log.warn("Found {} with unknown format for {}.", tableName, targetTablePrefix)
            return null
        }
        val (exchangeIdName, frequency) = exchangeIdAndFrequency
        val exchangeId = exchangeIdName.toIntOrNull()?.takeIf { Resources.getExchangeById(it).getOrNull() != null }
        if (exchangeId == null) {
            log.warn("Found {} with unknown exchangeId for {}.", tableName, targetTablePrefix)
            return null
        }
        return CandleTableInfo(tableName, frequency, exchangeId)
    }

    private data class CandleTableInfo(
        val tableName: String,
        val frequency: String,
        val exchangeId: Int,
    )
}
