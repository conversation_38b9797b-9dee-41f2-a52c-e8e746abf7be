package io.coinmetrics.api.statistics.utils

import io.coinmetrics.api.model.DerivativesMarketType
import io.coinmetrics.api.model.ParsedMarket
import io.coinmetrics.api.resources.Resources
import io.coinmetrics.api.resources.Resources.normalizeExchangeName
import io.coinmetrics.api.statistics.market.DefiPool
import io.coinmetrics.api.statistics.market.MarketStatistics
import io.coinmetrics.api.utils.SqlUtils
import io.coinmetrics.api.utils.TimeUtils
import io.coinmetrics.databases.getIntOrNull
import io.coinmetrics.defi.client.DeFiRawDataParser
import io.coinmetrics.defi.client.model.Bytes
import io.coinmetrics.defi.client.model.RawMarketData
import org.slf4j.LoggerFactory
import java.sql.ResultSet

internal object InternalUtils {
    val log = LoggerFactory.getLogger(InternalUtils::class.java)

    fun getMarketBaseName(rs: ResultSet): String? = SqlUtils.getAssetName(rs, "market_base_id", "market_base_name")

    fun getMarketQuoteName(rs: ResultSet): String? = SqlUtils.getAssetName(rs, "market_quote_id", "market_quote_name")

    fun parseSpotTradeMarketStatistics(
        resultSet: ResultSet,
        prefix: String,
    ): Pair<ParsedMarket.ParsedSpotMarket, MarketStatistics.Statistics>? {
        val exchangeId = resultSet.getInt("${prefix}_exchange_id")
        val base = SqlUtils.getAssetName(resultSet, "${prefix}_base_id", "${prefix}_base_name") ?: return null
        val quote = SqlUtils.getAssetName(resultSet, "${prefix}_quote_id", "${prefix}_quote_name") ?: return null

        val exchange = Resources.getExchangeNameById(exchangeId).getOrElse { return null }

        val market =
            ParsedMarket.ParsedSpotMarket(
                exchange = exchange,
                base = base,
                quote = quote,
            )

        val statistics = resultSet.retrieveStatistics()
        return statistics?.let { market to it }
    }

    fun parseSpotMarketStatistics(
        resultSet: ResultSet,
        prefix: String,
    ): Pair<ParsedMarket, MarketStatistics.Statistics>? {
        val exchangeId = resultSet.getInt("${prefix}_exchange_id")
        val baseId = resultSet.getInt("${prefix}_base_id")
        val quoteId = resultSet.getInt("${prefix}_quote_id")

        val exchange = Resources.getExchangeById(exchangeId).getOrElse { return null }

        val base = Resources.getCurrencyTickerById(baseId) ?: return null
        val quote = Resources.getCurrencyTickerById(quoteId) ?: return null

        val market =
            if (!exchange.defi) {
                ParsedMarket.ParsedSpotMarket(
                    exchange = normalizeExchangeName(exchange.name),
                    base = base,
                    quote = quote,
                )
            } else {
                return null
            }

        val statistics = resultSet.retrieveStatistics()
        return statistics?.let { market to it }
    }

    fun parseDefiMarketStatistics(
        resultSet: ResultSet,
        prefix: String,
        poolIdSupported: Boolean = false,
    ): Pair<ParsedMarket, MarketStatistics.Statistics>? {
        val baseId = resultSet.getInt("${prefix}_base_id")
        val quoteId = resultSet.getInt("${prefix}_quote_id")

        val exchangeId = resultSet.getInt("${prefix}_exchange_id")
        val exchange = Resources.getExchangeById(exchangeId).getOrElse { return null }

        val base = Resources.getCurrencyTickerById(baseId) ?: return null
        val quote = Resources.getCurrencyTickerById(quoteId) ?: return null

        val market =
            if (exchange.defi) {
                val poolIdFromDB = resultSet.getIntOrNull("${prefix}_pool_id")
                val poolId =
                    if (exchange.defiPoolsSupported && poolIdSupported) {
                        poolIdFromDB!!.let { "$it" }
                    } else {
                        null
                    }

                ParsedMarket.ParsedDefiMarket.parse(
                    exchange.name,
                    poolId = poolId,
                    base = base,
                    quote = quote,
                ) ?: run {
                    return null
                }
            } else {
                error("DeFi exchange must be specified.")
            }
        val statistics = resultSet.retrieveStatistics()
        return statistics?.let { market to it }
    }

    fun parseDefiMarketStatistics(
        exchangeName: String,
        resultSet: ResultSet,
        deFiRawDataParser: DeFiRawDataParser,
        poolMapping: Map<DefiPool, Int>,
    ): Pair<ParsedMarket.ParsedDefiMarket, MarketStatistics.Statistics>? {
        val exchange = Resources.getExchangeByName(exchangeName).getOrNull() ?: return null

        val baseHandle = Bytes(resultSet.getBytes("base_handle"))
        val quoteHandle = Bytes(resultSet.getBytes("quote_handle"))
        val poolId = Bytes(resultSet.getBytes("pool_id"))

        val poolConfigId =
            poolMapping[
                DefiPool(
                    poolAddress = poolId,
                    baseHandle = baseHandle,
                    quoteHandle = quoteHandle,
                    network =
                        exchange.network ?: error(
                            "Exchange ${exchange.name} doesn't have a network field defined in exchanges.json.",
                        ),
                ),
            ]
                ?: return null
        val mdMarket =
            deFiRawDataParser.convertRawMarketToMDMarket(
                rawMarket =
                    RawMarketData(
                        exchangeId = exchange.id,
                        baseHandle = baseHandle.data,
                        quoteHandle = quoteHandle.data,
                        poolConfigId = poolConfigId,
                    ),
            ) ?: return null

        val base = Resources.getCurrencyTickerById(mdMarket.baseId) ?: return null
        val quote = Resources.getCurrencyTickerById(mdMarket.quoteId) ?: return null

        val market =
            ParsedMarket.ParsedDefiMarket.parseOrNull(
                exchangeName,
                poolId = poolConfigId.toString(),
                base = base,
                quote = quote,
            ) ?: return null

        val statistics = resultSet.retrieveStatistics()
        return statistics?.let { market to it }
    }

    fun parseDerivativeMarketStatistics(
        resultSet: ResultSet,
        marketType: DerivativesMarketType,
        prefix: String,
    ): Pair<ParsedMarket.ParsedDerivativesMarket, MarketStatistics.Statistics>? {
        val exchangeId = resultSet.getInt("${prefix}_exchange_id")
        val symbol = resultSet.getString("${prefix}_symbol")

        val exchange = Resources.getExchangeNameById(exchangeId).getOrElse { return null }
        val market =
            ParsedMarket.ParsedDerivativesMarket(
                exchange = exchange,
                symbol = symbol,
                type = marketType,
            )

        val statistics = resultSet.retrieveStatistics()
        return statistics?.let { market to it }
    }

    fun ResultSet.retrieveStatistics(): MarketStatistics.Statistics? {
        val minTime = this.getTimestamp("min_time") ?: return null
        val maxTime = this.getTimestamp("max_time") ?: return null
        return MarketStatistics.Statistics(
            minTime = TimeUtils.dateTimeFormatter.format(minTime.toInstant()),
            maxTime = TimeUtils.dateTimeFormatter.format(maxTime.toInstant()),
        )
    }
}
