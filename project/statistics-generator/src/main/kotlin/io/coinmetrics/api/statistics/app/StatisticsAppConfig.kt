package io.coinmetrics.api.statistics.app

import io.coinmetrics.api.CommonConstants
import io.coinmetrics.api.config.StaticTierConfig
import io.coinmetrics.api.config.parseTiers
import io.coinmetrics.api.model.S3BooksMarketType
import io.coinmetrics.api.persistence.DatabasesConfig
import io.coinmetrics.api.statistics.StatisticsDescriptor
import java.util.EnumMap

data class StatisticsAppConfig(
    val envVariablesResolver: (String) -> String? = CommonConstants.defaultEnvVariablesResolver,
    val env: String = envVariablesResolver("API_ENV") ?: "dev",
    val healthCheckPort: Int = envVariablesResolver("API_HEALTH_CHECK_PORT")?.toInt() ?: 0,
    val prometheusPort: Int = envVariablesResolver("API_PROMETHEUS_PORT")?.toInt() ?: 0,
    val databases: DatabasesConfig =
        DatabasesConfig(
            envVariablesResolver = envVariablesResolver,
            env = env,
        ),
    val booksTiers: Map<S3BooksMarketType, List<StaticTierConfig>> =
        listOf(S3BooksMarketType.SPOT_BOOKS, S3BooksMarketType.FUTURES_BOOKS, S3BooksMarketType.OPTIONS_BOOKS).associateWithTo(
            EnumMap(S3BooksMarketType::class.java),
        ) { dataType ->
            (envVariablesResolver("API_${dataType}_TIERS") ?: "").parseTiers(envVariablesResolver, dataType.toString())
        },
    val useNewBooksTables: Boolean = (envVariablesResolver("API_USE_NEW_BOOKS_TABLES") ?: "false").toBoolean(),
    val statsS3Endpoint: String = envVariablesResolver("API_STATISTICS_S3_ENDPOINT") ?: "",
    val statsS3Region: String = envVariablesResolver("API_STATISTICS_S3_REGION") ?: "region",
    val statsS3AccessKey: String = envVariablesResolver("API_STATISTICS_S3_ACCESS_KEY") ?: "minioadmin",
    val statsS3SecretKey: String = envVariablesResolver("API_STATISTICS_S3_SECRET_KEY") ?: "minioadmin",
    val statisticsFileGroupIdPrefix: String = envVariablesResolver("API_STATISTICS_FILE_GROUP_ID_PREFIX") ?: "test/api-statistics",
    val defiStatisticsRefreshIntervalMs: Int = (envVariablesResolver("API_DEFI_STAT_REFRESH_SEC") ?: "60").toInt() * 1000,
    val marketCandlesStatisticsRefreshIntervalMs: Int =
        (envVariablesResolver("API_MARKET_CANDLES_STAT_REFRESH_SEC") ?: "60").toInt() * 1000,
    val marketColdBooksStatisticsRefreshIntervalMs: Int =
        (envVariablesResolver("API_MARKET_COLD_BOOKS_STAT_REFRESH_SEC") ?: "60").toInt() * 1000,
    val marketDefiStatisticsRefreshIntervalMs: Int = (envVariablesResolver("API_MARKET_DEFI_STAT_REFRESH_SEC") ?: "60").toInt() * 1000,
    val marketFundingRatesPredictedStatisticsRefreshIntervalMs: Int =
        (envVariablesResolver("API_MARKET_FUNDING_RATES_PREDICTED_STAT_REFRESH_SEC") ?: "60").toInt() * 1000,
    val marketFundingRatesStatisticsRefreshIntervalMs: Int =
        (envVariablesResolver("API_MARKET_FUNDING_RATES_STAT_REFRESH_SEC") ?: "60").toInt() * 1000,
    val marketFutureTickerStatisticsRefreshIntervalMs: Int =
        (envVariablesResolver("API_MARKET_FUTURE_TICKER_STAT_REFRESH_SEC") ?: "60").toInt() * 1000,
    val marketHotBooksStatisticsRefreshIntervalMs: Int =
        (envVariablesResolver("API_MARKET_HOT_BOOKS_STAT_REFRESH_SEC") ?: "60").toInt() * 1000,
    val marketLiquidationsStatisticsRefreshIntervalMs: Int =
        (envVariablesResolver("API_MARKET_LIQUIDATIONS_STAT_REFRESH_SEC") ?: "60").toInt() * 1000,
    val marketMetricsStatisticsRefreshIntervalMs: Int =
        (envVariablesResolver("API_MARKET_METRICS_STAT_REFRESH_SEC") ?: "60").toInt() * 1000,
    val marketOpenInterestStatisticsRefreshIntervalMs: Int =
        (envVariablesResolver("API_MARKET_OPEN_INTEREST_STAT_REFRESH_SEC") ?: "60").toInt() * 1000,
    val marketOptionTickerStatisticsRefreshIntervalMs: Int =
        (envVariablesResolver("API_MARKET_OPTION_TICKER_STAT_REFRESH_SEC") ?: "60").toInt() * 1000,
    val marketTradesStatisticsRefreshIntervalMs: Int = (envVariablesResolver("API_MARKET_TRADES_STAT_REFRESH_SEC") ?: "60").toInt() * 1000,
    val assetMetricStatisticsRefreshIntervalSec: Int = (envVariablesResolver("API_ASSET_METRICS_STAT_REFRESH_SEC") ?: "60").toInt(),
    val pairMetricStatisticsRefreshIntervalSec: Int = (envVariablesResolver("API_PAIR_METRICS_STAT_REFRESH_SEC") ?: "60").toInt(),
    val pairCandlesStatisticsRefreshIntervalSec: Int = (envVariablesResolver("API_PAIR_CANDLES_STAT_REFRESH_SEC") ?: "60").toInt(),
    val indexStatisticsRefreshIntervalSec: Int = (envVariablesResolver("API_INDEX_STAT_REFRESH_SEC") ?: "60").toInt(),
    val indexCandlesStatisticsRefreshIntervalSec: Int = (envVariablesResolver("API_INDEX_CANDLES_STAT_REFRESH_SEC") ?: "60").toInt(),
    val indexConstituentsStatisticsRefreshIntervalSec: Int =
        (envVariablesResolver("API_INDEX_CONSTITUENTS_STAT_REFRESH_SEC") ?: "3600").toInt(),
    val institutionMetricStatisticsRefreshIntervalSec: Int =
        (envVariablesResolver("API_INSTITUTION_METRICS_STAT_REFRESH_SEC") ?: "60").toInt(),
    val exchangeAssetMetricStatisticsRefreshIntervalSec: Int =
        (envVariablesResolver("API_EXCHANGE_ASSET_METRICS_STAT_REFRESH_SEC") ?: "60").toInt(),
    val exchangeMetricStatisticsRefreshIntervalSec: Int = (envVariablesResolver("API_EXCHANGE_METRICS_STAT_REFRESH_SEC") ?: "60").toInt(),
    val udmStatisticsRefreshIntervalSec: Int = (envVariablesResolver("API_UDM_STAT_REFRESH_SEC") ?: "60").toInt(),
    val addressTaggingStatisticsRefreshIntervalSec: Int = (envVariablesResolver("API_ADDRESS_TAGGING_STAT_REFRESH_SEC") ?: "60").toInt(),
    val dellPowerScaleBugsWorkarounds: Boolean = (envVariablesResolver("API_POWERSCALE_WORKAROUNDS") ?: "false").toBoolean(),
    val disabledStatistics: Set<StatisticsDescriptor<*>> = setOf(),
)
