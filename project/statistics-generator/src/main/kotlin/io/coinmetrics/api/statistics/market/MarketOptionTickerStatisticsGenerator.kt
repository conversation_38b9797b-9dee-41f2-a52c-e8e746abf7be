package io.coinmetrics.api.statistics.market

import io.coinmetrics.api.model.DerivativesMarketType
import io.coinmetrics.api.model.ParsedMarket
import io.coinmetrics.api.statistics.StatisticsGenerator
import io.coinmetrics.api.statistics.utils.InternalUtils
import io.coinmetrics.databases.Database
import org.slf4j.LoggerFactory
import kotlin.system.measureTimeMillis

class MarketOptionTickerStatisticsGenerator(
    val futuresDb: Database,
) : StatisticsGenerator<MarketOptionTickerStatisticsModel> {
    companion object {
        private val log = LoggerFactory.getLogger(MarketOptionTickerStatisticsGenerator::class.java)
    }

    override suspend fun generate(): MarketOptionTickerStatisticsModel {
        val optionTickerStatisticsPerMarketMap: Map<String, MarketStatistics.Statistics>
        val timeSpent =
            measureTimeMillis {
                val optionStatistics = derivativesStatistics(DerivativesMarketType.OPTION)
                optionTickerStatisticsPerMarketMap = optionStatistics.toStatisticsMap()
            }
        log.info("Generated market statistics for option ticker in ${timeSpent}ms.")
        return MarketOptionTickerStatisticsModel(optionTickerStatisticsPerMarketMap)
    }

    private suspend fun derivativesStatistics(
        marketType: DerivativesMarketType,
    ): List<Pair<ParsedMarket.ParsedDerivativesMarket, MarketStatistics.Statistics>> {
        val targetTable = "option_ticker"
        val schema = futuresDb.config.schema
        return futuresDb.query(
            """
            WITH RECURSIVE t AS (
                (SELECT ticker_exchange_id, ticker_symbol
                 FROM $schema.$targetTable
                 ORDER BY ticker_exchange_id, ticker_symbol
                 LIMIT 1)
                UNION ALL
                SELECT (SELECT ticker_exchange_id
                        FROM $schema.$targetTable
                        WHERE (ticker_exchange_id, ticker_symbol) >
                              (t.ticker_exchange_id, t.ticker_symbol)
                        ORDER BY ticker_exchange_id, ticker_symbol
                        LIMIT 1),
                       (SELECT ticker_symbol
                        FROM $schema.$targetTable
                        WHERE (ticker_exchange_id, ticker_symbol) >
                              (t.ticker_exchange_id, t.ticker_symbol)
                        ORDER BY ticker_exchange_id, ticker_symbol
                        LIMIT 1)
                FROM t
                WHERE t.ticker_exchange_id IS NOT NULL
            )
            SELECT t.*,
                   (select ticker_time
                    from $schema.$targetTable
                    where ticker_exchange_id = t.ticker_exchange_id
                      AND ticker_symbol = t.ticker_symbol
                    order by ticker_time
                    limit 1) as min_time,
                   (select ticker_time
                    from $schema.$targetTable
                    where ticker_exchange_id = t.ticker_exchange_id
                      AND ticker_symbol = t.ticker_symbol
                    order by ticker_time desc
                    limit 1) as max_time
            FROM t
            WHERE t.ticker_exchange_id IS NOT NULL;    
            """.trimIndent(),
        ) {
            it.map { rs -> InternalUtils.parseDerivativeMarketStatistics(rs, marketType, "ticker") }.filterNotNull()
        }
    }
}
