package io.coinmetrics.api.statistics.market

import io.coinmetrics.api.resources.Resources
import io.coinmetrics.api.statistics.StatisticsGenerator
import io.coinmetrics.api.utils.SqlUtils
import io.coinmetrics.databases.Database
import io.coinmetrics.databases.getIntOrNull
import kotlinx.coroutines.CancellationException
import org.slf4j.LoggerFactory

class OptionMetadataStatisticsGenerator(
    private val db: Database,
) : StatisticsGenerator<Map<String, OptionMetadata>> {
    companion object {
        private val log = LoggerFactory.getLogger(OptionMetadataStatistics::class.java)
    }

    override suspend fun generate(): Map<String, OptionMetadata> {
        val queryText =
            """
            SELECT 
                contract_exchange_id,
                contract_symbol,
                contract_underlying_base_id,
                contract_underlying_quote_id,
                contract_size_asset_id,
                contract_underlying_base_name,
                contract_underlying_quote_name,
                contract_size_asset_name,
                contract_listing_date,
                contract_expiry_date,
                contract_size,
                contract_strike,
                contract_is_european,
                contract_type,
                contract_settlement_price,
                contract_status,
                contract_amount_increment,
                contract_amount_size_min,
                contract_amount_size_max,
                contract_price_increment,
                contract_price_size_min,
                contract_price_size_max,
                contract_order_size_min,
                contract_taker_fee,
                contract_maker_fee,
                contract_margin_trading_enabled,
                contract_min_catalog_version,
                contract_underlying_native_base_name base_native,
                contract_underlying_native_quote_name quote_native,
                contract_margin_asset_id,
                contract_margin_asset_name
            FROM ${db.config.schema}.option_metadata
            """.trimIndent()

        try {
            return db
                .query(queryText) { query ->
                    query
                        .map { rs ->
                            Resources
                                .getExchangeNameById(rs.getInt("contract_exchange_id"))
                                .onFailure { log.warn(it) }
                                .getOrNull()
                                ?.let { exchangeStr ->
                                    OptionMetadata(
                                        exchange = exchangeStr,
                                        symbol = rs.getString("contract_symbol"),
                                        base = SqlUtils.getAssetName(rs, "contract_underlying_base_id", "contract_underlying_base_name"),
                                        quote = SqlUtils.getAssetName(rs, "contract_underlying_quote_id", "contract_underlying_quote_name"),
                                        status = rs.getString("contract_status"),
                                        orderAmountIncrement = rs.getString("contract_amount_increment"),
                                        orderAmountMin = rs.getString("contract_amount_size_min"),
                                        orderAmountMax = rs.getString("contract_amount_size_max"),
                                        orderPriceIncrement = rs.getString("contract_price_increment"),
                                        orderPriceMin = rs.getString("contract_price_size_min"),
                                        orderPriceMax = rs.getString("contract_price_size_max"),
                                        orderSizeMin = rs.getString("contract_order_size_min"),
                                        orderTakerFee = rs.getString("contract_taker_fee"),
                                        orderMakerFee = rs.getString("contract_maker_fee"),
                                        marginTradingEnabled = rs.getObject("contract_margin_trading_enabled") as Boolean?,
                                        sizeAsset = SqlUtils.getAssetName(rs, "contract_size_asset_id", "contract_size_asset_name"),
                                        marginAsset = SqlUtils.getAssetName(rs, "contract_margin_asset_id", "contract_margin_asset_name"),
                                        size = rs.getBigDecimal("contract_size"),
                                        european = rs.getBoolean("contract_is_european"),
                                        strike = rs.getBigDecimal("contract_strike"),
                                        optionContractType = rs.getString("contract_type"),
                                        listing = rs.getTimestamp("contract_listing_date")?.toInstant(),
                                        expiration = rs.getTimestamp("contract_expiry_date").toInstant(),
                                        settlementPrice = rs.getBigDecimal("contract_settlement_price"),
                                        minCatalogVersion = rs.getIntOrNull("contract_min_catalog_version"),
                                        baseNative = rs.getString("base_native"),
                                        quoteNative = rs.getString("quote_native"),
                                    )
                                }
                        }.filterNotNull()
                }.associateByTo(HashMap()) {
                    "${it.exchange}-${it.symbol}-option"
                }
        } catch (e: CancellationException) {
            throw e
        } catch (e: Exception) {
            throw IllegalStateException("Failed to refresh option metadata.", e)
        }
    }
}
