package io.coinmetrics.api.statistics.blockchain

import io.coinmetrics.api.model.UDMStatistics
import io.coinmetrics.api.statistics.StatisticsGenerator
import io.coinmetrics.api.utils.LazyInstant.Companion.toLazyInstant
import io.coinmetrics.databases.Database

class UdmV2StatisticsGenerator(
    private val dbs: List<Database>,
) : StatisticsGenerator<Map<String, List<UDMStatistics>>> {
    private companion object {
        /**
         * @return Pair<TimeColumnName, OrderByColumnName>
         */
        fun getDBColumns(tableNamePart: String): Pair<String, String>? =
            when (tableNamePart) {
                "accounts" -> Pair("creation_consensus_time", "creation_chain_sequence_number")
                "balance_updates" -> Pair("consensus_time", "chain_sequence_number")
                "blocks" -> Pair("consensus_time", "height")
                "transactions" -> Pair("consensus_time", "block_height")
                else -> null
            }
    }

    /**
     * @return Map<Asset, UDMStatistics>
     */
    override suspend fun generate(): Map<String, List<UDMStatistics>> =
        dbs.flatMap { getAtlasStatistics(it) }.groupBy({ (asset, _) -> asset }, { (_, stats) -> stats })

    /**
     * @return List<Pair<Asset, UDMStatistics>>
     */
    private suspend fun getAtlasStatistics(db: Database): List<Pair<String, UDMStatistics>> =
        findSupportedAssets(db).map { (asset, tableNamePart) ->
            Pair(asset, getStatisticsFor(db, asset, tableNamePart))
        }

    /**
     * @return List<Pair<Asset, TableNamePart>>
     */
    private suspend fun findSupportedAssets(db: Database): List<Pair<String, String>> =
        db
            .query(
                """
                SELECT relname
                FROM pg_catalog.pg_class c
                    join pg_catalog.pg_namespace n on c.relnamespace = n.oid
                WHERE n.nspname = '${db.config.schema}' AND relkind IN ('r', 'v') AND relname LIKE '%_udm_v2_%s';
                """.trimIndent(),
            ) {
                it.map { rs -> rs.getString("relname") }.filterNotNull()
            }.map { tableName ->
                tableName.split("_udm_v2_").let { it[0] to it[1] }
            }

    /**
     * @return UDMStatistics
     */
    private suspend fun getStatisticsFor(
        db: Database,
        asset: String,
        tableNamePart: String,
    ): UDMStatistics =
        getDBColumns(tableNamePart)?.let { (timeColumn, orderByColumn) ->
            val tableName = """${db.config.schema}."${asset}_udm_v2_$tableNamePart""""
            db.query(
                """
                SELECT 
                    (SELECT $timeColumn FROM $tableName ORDER BY $orderByColumn LIMIT 1),
                    (SELECT $timeColumn FROM $tableName ORDER BY $orderByColumn DESC LIMIT 1)
                """.trimIndent(),
            ) { query ->
                query
                    .map { rs ->
                        UDMStatistics(
                            tableNamePart = tableNamePart,
                            minTime = rs.getTimestamp(1)?.toInstant()?.toLazyInstant(),
                            maxTime = rs.getTimestamp(2)?.toInstant()?.toLazyInstant(),
                        )
                    }.firstOrNull()
            }
        } ?: UDMStatistics(tableNamePart, null, null)
}
