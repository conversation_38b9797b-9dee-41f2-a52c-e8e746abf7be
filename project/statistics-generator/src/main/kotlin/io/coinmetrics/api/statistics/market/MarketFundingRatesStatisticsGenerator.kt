package io.coinmetrics.api.statistics.market

import io.coinmetrics.api.model.DerivativesMarketType
import io.coinmetrics.api.model.ParsedMarket
import io.coinmetrics.api.statistics.StatisticsGenerator
import io.coinmetrics.api.statistics.utils.InternalUtils
import io.coinmetrics.databases.Database
import org.slf4j.LoggerFactory
import kotlin.system.measureTimeMillis

class MarketFundingRatesStatisticsGenerator(
    val futuresDb: Database,
) : StatisticsGenerator<MarketFundingRatesStatisticsModel> {
    companion object {
        private val log = LoggerFactory.getLogger(MarketFundingRatesStatisticsGenerator::class.java)
    }

    override suspend fun generate(): MarketFundingRatesStatisticsModel {
        val fundingRatesStatisticsPerMarketMap: Map<String, MarketStatistics.Statistics>
        val timeSpent =
            measureTimeMillis {
                val derivativesStatistics = derivativesStatistics()
                fundingRatesStatisticsPerMarketMap = derivativesStatistics.toStatisticsMap()
            }
        log.info("Generated market statistics for funding rates in ${timeSpent}ms.")
        return MarketFundingRatesStatisticsModel(fundingRatesStatisticsPerMarketMap)
    }

    private suspend fun derivativesStatistics(): List<Pair<ParsedMarket.ParsedDerivativesMarket, MarketStatistics.Statistics>> {
        val targetTable = "funding_rates"
        val schema = futuresDb.config.schema
        return futuresDb.query(
            """
            WITH RECURSIVE t AS (
                (SELECT funding_exchange_id, funding_symbol
                 FROM $schema.$targetTable
                 ORDER BY funding_exchange_id, funding_symbol
                 LIMIT 1)
                UNION ALL
                SELECT (SELECT funding_exchange_id
                        FROM $schema.$targetTable
                        WHERE (funding_exchange_id, funding_symbol) >
                              (t.funding_exchange_id, t.funding_symbol)
                        ORDER BY funding_exchange_id, funding_symbol
                        LIMIT 1),
                       (SELECT funding_symbol
                        FROM $schema.$targetTable
                        WHERE (funding_exchange_id, funding_symbol) >
                              (t.funding_exchange_id, t.funding_symbol)
                        ORDER BY funding_exchange_id, funding_symbol
                        LIMIT 1)
                FROM t
                WHERE t.funding_exchange_id IS NOT NULL
            )
            SELECT t.*,
                   (select funding_time
                    from $schema.$targetTable
                    where funding_exchange_id = t.funding_exchange_id
                      AND funding_symbol = t.funding_symbol
                    order by funding_time
                    limit 1) as min_time,
                   (select funding_time
                    from $schema.$targetTable
                    where funding_exchange_id = t.funding_exchange_id
                      AND funding_symbol = t.funding_symbol
                    order by funding_time desc
                    limit 1) as max_time
            FROM t
            WHERE t.funding_exchange_id IS NOT NULL;    
            """.trimIndent(),
        ) {
            it
                .map { rs ->
                    InternalUtils.parseDerivativeMarketStatistics(rs, DerivativesMarketType.FUTURE, "funding")
                }.filterNotNull()
        }
    }
}
