package io.coinmetrics.api.statistics.market

import io.coinmetrics.api.model.ParsedMarket

fun <T : ParsedMarket> List<Pair<T, MarketStatistics.Statistics>>.mergeWithStatistics(
    vararg others: List<Pair<T, MarketStatistics.Statistics>>,
): List<Pair<T, MarketStatistics.Statistics>> {
    val resultMap: MutableMap<String, Pair<T, MarketStatistics.Statistics>> = this.associateBy { it.first.toString() }.toMap(HashMap())
    others.forEach { other ->
        other.forEach { (key, value) ->
            resultMap.compute(key.toString()) { _, currentValue ->
                if (currentValue == null) {
                    key to value
                } else {
                    key to
                        MarketStatistics.Statistics(
                            minTime = minOf(value.minTime, currentValue.second.minTime),
                            maxTime = maxOf(value.maxTime, currentValue.second.maxTime),
                        )
                }
            }
        }
    }
    return resultMap.values.toList()
}

fun Sequence<Pair<ParsedMarket, MarketStatistics.Statistics>>.toStatisticsMap(): HashMap<String, MarketStatistics.Statistics> =
    this.associateTo(HashMap()) { (market, statistic) -> market.toString() to statistic }

fun Sequence<Pair<ParsedMarket, MarketStatistics.Statistics>>.toStatisticsPerExchangeMap():
    Map<String, MarketStatistics.ExchangeStatistics> =
    this
        .groupByTo(HashMap()) { (market, _) -> market.exchange }
        .mapValues { (_, pairs) ->
            val marketsArray = pairs.map { (market, _) -> market.toString() }.toTypedArray().also { it.sort() }

            // it is expected that we can compare string representations of timestamps here
            val minTime = pairs.asSequence().map { (_, statistics) -> statistics.minTime }.minOrNull()!!
            val maxTime = pairs.asSequence().map { (_, statistics) -> statistics.maxTime }.maxOrNull()!!
            MarketStatistics.ExchangeStatistics(minTime, maxTime, marketsArray.toList())
        }

fun List<Pair<ParsedMarket, *>>.onlyMarkets(): List<ParsedMarket> = this.map { it.first }

fun Sequence<Pair<ParsedMarket, *>>.onlyMarkets(): List<ParsedMarket> = this.map { it.first }.toList()
