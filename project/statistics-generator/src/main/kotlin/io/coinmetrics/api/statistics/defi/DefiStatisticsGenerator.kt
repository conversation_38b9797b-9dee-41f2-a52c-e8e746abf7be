package io.coinmetrics.api.statistics.defi

import io.coinmetrics.api.persistence.DefiDatabase
import io.coinmetrics.api.statistics.StatisticsGenerator

class DefiStatisticsGenerator(
    private val db: DefiDatabase?,
) : StatisticsGenerator<DefiStatisticsModel> {
    override suspend fun generate(): DefiStatisticsModel {
        return if (db == null || !db.protocolsBalanceSheetTableExists()) {
            DefiStatisticsModel(emptySet())
        } else {
            return db
                .query("SELECT DISTINCT protocol, version, chain FROM ${db.protocolsBalanceSheetTableName()};") { query ->
                    query
                        .map { rs ->
                            DefiProtocol(
                                rs.getString("protocol"),
                                rs.getString("version"),
                                rs.getString("chain"),
                            )
                        }.toSet()
                }.let { result -> DefiStatisticsModel(result.map { it.toString() }.toSet()) }
        }
    }
}
