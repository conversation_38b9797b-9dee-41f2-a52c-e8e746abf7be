package io.coinmetrics.api.statistics.market

import io.coinmetrics.api.model.DerivativesMarketType
import io.coinmetrics.api.model.ParsedMarket
import io.coinmetrics.api.statistics.StatisticsGenerator
import io.coinmetrics.api.statistics.utils.InternalUtils
import io.coinmetrics.databases.Database
import org.slf4j.LoggerFactory
import kotlin.system.measureTimeMillis

class MarketOpenInterestStatisticsGenerator(
    val futuresDb: Database,
) : StatisticsGenerator<MarketOpenInterestStatisticsModel> {
    companion object {
        private val log = LoggerFactory.getLogger(MarketOpenInterestStatisticsGenerator::class.java)
    }

    override suspend fun generate(): MarketOpenInterestStatisticsModel {
        val openInterestStatisticsPerMarketMap: Map<String, MarketStatistics.Statistics>
        val timeSpent =
            measureTimeMillis {
                val statistics = derivativesStatistics(DerivativesMarketType.FUTURE) + derivativesStatistics(DerivativesMarketType.OPTION)
                openInterestStatisticsPerMarketMap = statistics.toStatisticsMap()
            }
        log.info("Generated market statistics for open interest in ${timeSpent}ms.")
        return MarketOpenInterestStatisticsModel(openInterestStatisticsPerMarketMap)
    }

    private suspend fun derivativesStatistics(
        marketType: DerivativesMarketType,
    ): List<Pair<ParsedMarket.ParsedDerivativesMarket, MarketStatistics.Statistics>> {
        val targetTable =
            when (marketType) {
                DerivativesMarketType.OPTION -> "option_open_interest"
                DerivativesMarketType.FUTURE -> "futures_open_interest"
            }
        val schema = futuresDb.config.schema
        return futuresDb.query(
            """
            WITH RECURSIVE t AS (
                (SELECT open_interest_exchange_id, open_interest_symbol
                 FROM $schema.$targetTable
                 ORDER BY open_interest_exchange_id, open_interest_symbol
                 LIMIT 1)
                UNION ALL
                SELECT (SELECT open_interest_exchange_id
                        FROM $schema.$targetTable
                        WHERE (open_interest_exchange_id, open_interest_symbol) >
                              (t.open_interest_exchange_id, t.open_interest_symbol)
                        ORDER BY open_interest_exchange_id, open_interest_symbol
                        LIMIT 1),
                       (SELECT open_interest_symbol
                        FROM $schema.$targetTable
                        WHERE (open_interest_exchange_id, open_interest_symbol) >
                              (t.open_interest_exchange_id, t.open_interest_symbol)
                        ORDER BY open_interest_exchange_id, open_interest_symbol
                        LIMIT 1)
                FROM t
                WHERE t.open_interest_exchange_id IS NOT NULL
            )
            SELECT t.*,
                   (select open_interest_time
                    from $schema.$targetTable
                    where open_interest_exchange_id = t.open_interest_exchange_id
                      AND open_interest_symbol = t.open_interest_symbol
                    order by open_interest_time
                    limit 1) as min_time,
                   (select open_interest_time
                    from $schema.$targetTable
                    where open_interest_exchange_id = t.open_interest_exchange_id
                      AND open_interest_symbol = t.open_interest_symbol
                    order by open_interest_time desc
                    limit 1) as max_time
            FROM t
            WHERE t.open_interest_exchange_id IS NOT NULL;    
            """.trimIndent(),
        ) {
            it.map { rs -> InternalUtils.parseDerivativeMarketStatistics(rs, marketType, "open_interest") }.filterNotNull()
        }
    }
}
