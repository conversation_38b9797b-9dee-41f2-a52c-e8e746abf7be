package io.coinmetrics.api.statistics.index

import io.coinmetrics.api.CommonConstants
import io.coinmetrics.api.model.CandleStatistics
import io.coinmetrics.api.resources.Resources
import io.coinmetrics.api.statistics.StatisticsGenerator
import io.coinmetrics.api.utils.SqlUtils
import io.coinmetrics.api.utils.TimeUtils
import io.coinmetrics.databases.Database
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.coroutineScope
import org.slf4j.LoggerFactory
import java.sql.ResultSet

class IndexCandlesStatisticsGenerator(
    private val db: Database,
) : StatisticsGenerator<Map<String, List<CandleStatistics>>> {
    companion object {
        private val log = LoggerFactory.getLogger(IndexCandlesStatisticsGenerator::class.java)
    }

    private val schema = db.config.schema

    private val candlesFrequencyOrder =
        CommonConstants.candleFrequenciesMap.keys
            .withIndex()
            .associate { it.value to it.index }

    override suspend fun generate(): Map<String, List<CandleStatistics>> =
        coroutineScope {
            findAllIndexCandleTables()
                .sortedWith(comparingFrequency())
                .map { async { expandForEachAlias(queryCandlesStatistics(it), it.indexAliases) } }
                .awaitAll()
                .flatten()
                .groupByTo(HashMap(), { it.first }, { it.second })
        }

    private fun expandForEachAlias(
        statistics: CandleStatistics?,
        aliases: List<String>,
    ): List<Pair<String, CandleStatistics>> = statistics?.let { aliases.map { it to statistics } } ?: emptyList()

    private suspend fun queryCandlesStatistics(tableInfo: IndexCandleTableInfo): CandleStatistics? {
        val tableFullName = "$schema.${tableInfo.tableName}"
        val queryString =
            """
             SELECT (
                SELECT start_time
                FROM $tableFullName
                ORDER BY start_time
                LIMIT 1
            ) AS min_time,
            (
                SELECT start_time
                FROM $tableFullName
                ORDER BY start_time DESC
                LIMIT 1
            ) AS max_time
            """.trimIndent()

        return db.query(queryString) { query -> query.map { toCandlesStatistics(it, tableInfo.frequency) }.firstOrNull() }
    }

    private fun toCandlesStatistics(
        resultSet: ResultSet,
        frequency: String,
    ): CandleStatistics? {
        val minTime = resultSet.getTimestamp("min_time") ?: return null
        val maxTime = resultSet.getTimestamp("max_time") ?: return null
        return CandleStatistics(
            frequency = frequency,
            minTime = TimeUtils.dateTimeFormatter.format(minTime.toInstant()),
            maxTime = TimeUtils.dateTimeFormatter.format(maxTime.toInstant()),
        )
    }

    private suspend fun findAllIndexCandleTables(): List<IndexCandleTableInfo> {
        val select = SqlUtils.createSearchQueryForTablesByPrefix(schema, CommonConstants.INDEX_CANDLES_TABLE_PREFIX)
        return db.query(select) { query -> query.map { rs -> toIndexCandleTableInfo(rs) }.filterNotNull() }
    }

    private fun toIndexCandleTableInfo(resultSet: ResultSet): IndexCandleTableInfo? {
        val tableName = resultSet.getString("relname")
        // index_candles_$index_$frequency format is expected
        val indexIdAndFrequency = tableName.removePrefix(CommonConstants.INDEX_CANDLES_TABLE_PREFIX).split("_")
        if (indexIdAndFrequency.size != 2) {
            log.warn("Found table {} with unknown pattern", tableName)
            return null
        }
        val (indexId, frequency) = indexIdAndFrequency
        if (!candlesFrequencyOrder.containsKey(frequency)) {
            log.warn("Found table {} with unsupported frequency", tableName)
            return null
        }

        val indexNames =
            indexId.toIntOrNull()?.let {
                Resources.getIndexNamesById(it).getOrElse { emptyList() }
            }
        if (indexNames.isNullOrEmpty()) {
            log.warn("Found table {} with unsupported indexId", tableName)
            return null
        }
        return IndexCandleTableInfo(tableName, indexNames, frequency)
    }

    private fun comparingFrequency(): Comparator<IndexCandleTableInfo> =
        Comparator.comparingInt { candlesFrequencyOrder[it.frequency] ?: -1 }

    private data class IndexCandleTableInfo(
        val tableName: String,
        val indexAliases: List<String>,
        val frequency: String,
    )
}
