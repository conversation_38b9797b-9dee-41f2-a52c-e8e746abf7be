package io.coinmetrics.api.statistics.market

import com.fasterxml.jackson.annotation.JsonFormat
import com.fasterxml.jackson.annotation.JsonProperty
import io.coinmetrics.api.model.ParsedMarket
import io.coinmetrics.api.statistics.StatisticsDescriptor
import io.coinmetrics.api.statistics.StatisticsRepository
import java.math.BigDecimal
import java.time.Instant

class SpotMetadataStatistics(
    private val repoItem: StatisticsRepository.Item<SpotMetadataStatisticsModel>,
) {
    companion object {
        val descriptor = StatisticsDescriptor.create<SpotMetadataStatisticsModel>("spot-metadata")
    }

    fun get(market: String) = repoItem.value.spotMetadataPerMarket[market]

    fun getMarket(
        exchangeId: Int,
        symbol: String,
    ) = repoItem.value.marketPerExchangeIdAndSymbol[exchangeId]?.get(symbol)
}

data class SpotMetadataStatisticsModel(
    @JsonProperty("market_per_exchange_id_and_symbol")
    val marketPerExchangeIdAndSymbol: Map<Int, Map<String, ParsedMarket.ParsedSpotMarket>>,
    @JsonProperty("spot_metadata_per_market")
    val spotMetadataPerMarket: Map<String, SpotMetadata>,
)

data class SpotMetadata(
    override val exchange: String,
    override val symbol: String,
    override val base: String,
    override val quote: String,
    override val status: String?,
    @JsonProperty("order_amount_increment")
    override val orderAmountIncrement: String?,
    @JsonProperty("order_amount_min")
    override val orderAmountMin: String?,
    @JsonProperty("order_amount_max")
    override val orderAmountMax: String?,
    @JsonProperty("order_price_increment")
    override val orderPriceIncrement: String?,
    @JsonProperty("order_price_min")
    override val orderPriceMin: String?,
    @JsonProperty("order_price_max")
    override val orderPriceMax: String?,
    @JsonProperty("order_size_min")
    override val orderSizeMin: String?,
    @JsonProperty("order_taker_fee")
    override val orderTakerFee: String?,
    @JsonProperty("order_maker_fee")
    override val orderMakerFee: String?,
    @JsonProperty("margin_trading_enabled")
    override val marginTradingEnabled: Boolean?,
    @JsonProperty("exchange_id")
    val exchangeId: Int,
    @JsonProperty("base_id")
    val baseId: Int?,
    @JsonProperty("quote_id")
    val quoteId: Int?,
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSSSSSSS'Z'", timezone = "UTC")
    val listing: Instant?,
    override val size: BigDecimal?,
    @JsonProperty("base_native")
    override val baseNative: String?,
    @JsonProperty("quote_native")
    override val quoteNative: String?,
) : MarketStatistics.MarketMetadata {
    override val type = "spot"
}
