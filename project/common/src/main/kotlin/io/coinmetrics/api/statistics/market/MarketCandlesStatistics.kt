package io.coinmetrics.api.statistics.market

import io.coinmetrics.api.statistics.StatisticsDescriptor
import io.coinmetrics.api.statistics.StatisticsRepository

data class MarketCandlesStatisticsModel(
    val frequencyToMarketToStatistics: Map<String, Map<String, MarketStatistics.Statistics>>,
) {
    val supportedEntities =
        SupportedMarkets.build {
            for (marketToStatistics in frequencyToMarketToStatistics.values) {
                addUnparsed(marketToStatistics.keys)
            }
        }
}

class MarketCandlesStatistics(
    repository: StatisticsRepository,
) : SupportedMarketStatistics {
    companion object {
        val descriptor = StatisticsDescriptor.create<MarketCandlesStatisticsModel>("market-candles")
    }

    private val repoItem = repository.getItem(descriptor)

    override val supportedMarketsHash: Int
        get() = repoItem.valueAndHash.second.hashCode()

    val frequencyToMarketToStatistics: Map<String, Map<String, MarketStatistics.Statistics>>
        get() = repoItem.value.frequencyToMarketToStatistics

    override fun isMarketSupported(market: String) =
        repoItem.value.supportedEntities.markets
            .contains(market)

    override fun isExchangeSupported(exchange: String) =
        repoItem.value.supportedEntities.exchanges
            .contains(exchange)

    override fun isSymbolSupported(symbol: String) =
        repoItem.value.supportedEntities.symbols
            .contains(symbol)

    override fun isBaseSupported(base: String) =
        repoItem.value.supportedEntities.bases
            .contains(base)

    override fun isQuoteSupported(quote: String) =
        repoItem.value.supportedEntities.quotes
            .contains(quote)

    override fun getSupportedMarkets(reverseOrder: Boolean) =
        repoItem.value.supportedEntities
            .getMarkets(reverseOrder)
            .asSequence()
}
