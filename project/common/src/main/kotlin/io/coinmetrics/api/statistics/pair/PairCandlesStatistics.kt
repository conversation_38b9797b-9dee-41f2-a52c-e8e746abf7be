package io.coinmetrics.api.statistics.pair

import io.coinmetrics.api.model.CandleStatistics
import io.coinmetrics.api.statistics.StatisticsDescriptor
import io.coinmetrics.api.statistics.StatisticsRepository

class PairCandlesStatistics(
    repository: StatisticsRepository,
) {
    companion object {
        val descriptor = StatisticsDescriptor.create<Map<String, List<CandleStatistics>>>("pair-candles")
    }

    private val repoItem = repository.getItem(descriptor)

    fun getPairCandlesStatistic() = repoItem.value
}
