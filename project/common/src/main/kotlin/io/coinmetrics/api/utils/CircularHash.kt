/*
 * Migrated from kotlin-shared
 */
package io.coinmetrics.api.utils

/**
 * Circular hash with a [HashMap] buffer using hash key type `K` and hash value type `V`.
 *
 * @property capacity The initial capacity of the [HashMap]
 */
class CircularHash<K, V>(
    val capacity: Int,
) {
    /**
     * Pointer to track the oldest key-value mapping in the circular hash
     */
    private var n = 0

    /**
     * Array of keys at the given capacity to track the oldest key-value mapping in the circular hash
     */
    @Suppress("UNCHECKED_CAST")
    private val arr: Array<K?> = arrayOfNulls<Any?>(capacity) as Array<K?>

    /**
     * Hash backing storage for key-value mappings
     */
    private val map = HashMap<K, V>(capacity)

    /**
     * Associates the specified value `v` with the specified key `k` in this circular hash. If the circular hash
     * previously contained a mapping for the key, the old value is replaced. The oldest key-value mapping in the
     * circular hash is always removed.
     *
     * Note: Null is returned rather than the previous value if the value being put into the circular hash will be a
     * replacement for the oldest key-value mapping.
     *
     * @param k key with which the specified value is to be associated
     * @param v value to be associated with the specified key
     *
     * @return the previous value associated with the key if it already exists and will remain in the circular hash,
     * null otherwise. (A null return can also indicate that the circular hash previously associated null with
     * the key.)
     */
    fun put(
        k: K,
        v: V,
    ): V? {
        val prev = arr[n]
        prev.let { map.remove(it) }
        arr[n] = k
        n = (n + 1) % capacity
        return map.put(k, v)
    }

    /**
     * Associate the specified value `v` with the specified key `k` if the key does not already have a mapping in the
     * circular hash.
     *
     * @param k key with which the specified value is to be associated
     * @param v value to be associated with the specified key
     *
     * @return true if the key was added; false otherwise
     */
    fun elsePut(
        k: K,
        v: V,
    ): Boolean {
        val notFound = !this.contains(k)
        if (notFound) this.put(k, v)
        return notFound
    }

    /**
     * Returns the value to which the specified key `k` is mapped, or null if this circular hash contains no mapping for
     * the key.
     *
     * More formally, if this circular hash contains a mapping from a key `k` to a value `v` such that
     * `(key==null ? k==null : key.equals(k))`, then this method returns `v`; otherwise it returns null. (There can
     * be at most one such mapping.)
     *
     * A return value of null does not necessarily indicate that the circular hash contains no mapping for the key; it's
     * also possible that the circular hash explicitly maps the key to null. The contains operation may be used to
     * distinguish these two cases.
     *
     * @param k the key whose associated value is to be returned
     *
     * @return the value to which the specified key is mapped, or null if this circular hash contains no mapping for the key
     */
    fun get(k: K): V? = map.get(k)

    /**
     * @return true if the circular hash contains the given key `k`, false otherwise.
     */
    fun contains(k: K): Boolean = map.contains(k)

    /**
     * @return the number of key-value mappings in this circular hash.
     */
    fun size(): Int = map.size
}
