package io.coinmetrics.api.model

import com.fasterxml.jackson.annotation.JsonProperty
import io.coinmetrics.api.utils.LazyInstant

data class DataAvailabilityTimeRange(
    @JsonProperty("min_time")
    val minTime: LazyInstant,
    @JsonProperty("max_time")
    val maxTime: LazyInstant,
)

class CandleStatistics(
    val frequency: String,
    @JsonProperty("min_time")
    val minTime: String,
    @JsonProperty("max_time")
    val maxTime: String,
)

data class UDMStatistics(
    @JsonProperty("table_name_part")
    val tableNamePart: String,
    @JsonProperty("min_time")
    val minTime: LazyInstant?,
    @JsonProperty("max_time")
    val maxTime: LazyInstant?,
)
