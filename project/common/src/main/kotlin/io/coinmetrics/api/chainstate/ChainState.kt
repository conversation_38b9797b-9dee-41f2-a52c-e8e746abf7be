/*
 * Migrated from kotlin-shared
 */
package io.coinmetrics.api.chainstate

import io.coinmetrics.api.utils.fullZip
import java.time.Instant

interface BlockHeaderProvider {
    fun getHeader(hash: String): BlockHeader?
}

data class BlockHeader(
    val height: Int,
    val time: Instant,
    val hash: String,
    val parent: String,
)

interface ChainStateListener {
    fun connect(block: BlockHeader)

    fun disconnect(block: BlockHeader)
}

class ChainState(
    private val provider: BlockHeaderProvider,
    private var tip: BlockHeader,
    private vararg val listeners: ChainStateListener,
) {
    init {
        listeners.forEach { it.connect(tip) }
    }

    fun getTip(): BlockHeader = tip

    fun setTip(header: BlockHeader) {
        // we need to rewind until we hit the common ancestor
        val commonAncestor = lastCommonAncestor(tip, header)
        while (tip.hash != commonAncestor.hash) {
            rewind()
        }

        // then we need to go forward until we reach the new proposed block
        ancestors(header, downTo = commonAncestor.height + 1)
            .sortedBy {
                it.height
            }.forEach {
                connect(it)
            }

        connect(header)
    }

    private fun connect(header: BlockHeader) {
        // ignore when we want to connect the same tip
        if (tip.hash == header.hash) {
            return
        }

        require(header.parent == tip.hash) { "Tried to connect $header when tip was $tip" }
        tip = header
        listeners.forEach { it.connect(tip) }
    }

    private fun rewind() {
        val parent = provider.getHeader(tip.parent) ?: throw IllegalStateException("${tip.hash} has no parent")
        listeners.forEach { it.disconnect(tip) }
        tip = parent
    }

    internal fun lastCommonAncestor(
        a: BlockHeader,
        b: BlockHeader,
    ): BlockHeader {
        if (a.height > b.height) {
            // swap arguments to simplify following logic
            return lastCommonAncestor(b, a)
        }

        assert(a.height <= b.height)

        // we need to get both branches on a common height first
        val rootA = a

        // B's height is greater than A, so we iterate over B's ancestors
        // until we reach A's height
        // example fork:
        //   C <- B
        //     \- D <- A
        // we rewind A's branch to get to D
        val rootB = ancestors(b, downTo = a.height).first { it.height == a.height }

        if (rootA == rootB) {
            return rootA
        }

        // we retrace each branches (A's and B's) one block at a time until we find a common ancestor
        ancestors(rootA).fullZip(ancestors(rootB)).forEach { (ancestorA, ancestorB) ->
            require(ancestorA?.height == ancestorB?.height)

            if (ancestorA == ancestorB && ancestorA != null) {
                return ancestorA
            }
        }

        throw IllegalStateException("No common ancestor between ${a.hash} and ${b.hash}")
    }

    /**
     * Returns an iterator of the ancestors (parents) of a given block down to a given height
     * (defaults to 0, genesis)
     */
    private fun ancestors(
        header: BlockHeader,
        downTo: Int = 0,
    ): Sequence<BlockHeader> {
        var currentBlock = provider.getHeader(header.parent)

        return object : Sequence<BlockHeader> {
            override fun iterator(): Iterator<BlockHeader> {
                return object : Iterator<BlockHeader> {
                    override fun hasNext(): Boolean {
                        val cb = currentBlock
                        // we can continue if we have a next block and if it is above the downTo limit
                        // at the next iteration we'll return the current block's parent
                        // which will then be at height == downTo
                        return cb != null && cb.height + 1 >= downTo
                    }

                    override fun next(): BlockHeader {
                        val copy = currentBlock
                        require(copy != null)
                        currentBlock = provider.getHeader(copy.parent)
                        return copy
                    }
                }
            }
        }
    }
}
