package io.coinmetrics.api.utils

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.model.TimeRestriction
import io.coinmetrics.api.toFailure
import io.coinmetrics.api.toSuccess
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import java.sql.Timestamp
import java.time.Duration
import java.time.Instant
import java.time.ZoneId
import java.time.ZoneOffset
import java.time.format.DateTimeFormatter
import java.time.format.DateTimeFormatterBuilder
import java.time.format.DateTimeParseException
import java.time.temporal.ChronoField
import java.time.temporal.ChronoUnit
import java.time.temporal.TemporalUnit
import java.util.concurrent.TimeUnit

object TimeUtils {
    val log: Logger = LoggerFactory.getLogger(this::class.java)

    val dateTimeFormatter: DateTimeFormatter =
        DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSSSSSSS'Z'").withZone(
            ZoneOffset.UTC,
        )

    val dateWithLenientTimeFormatter: DateTimeFormatter =
        DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm[:ss][.S][SS][SSS][SSS]['Z']").withZone(
            ZoneOffset.UTC,
        )

    val dateOnlyPatterns = listOf("yyyy-MM-dd", "yyyyMMdd")

    val dateTimeWithoutSubSecTimePatterns = listOf("yyyy-MM-dd'T'HH:mm:ss", "yyyy-MM-dd'T'HHmmss")

    val dateTimeWithMillisTimePatterns =
        listOf(
            "yyyy-MM-dd'T'HH:mm:ss.SSS",
            "yyyy-MM-dd'T'HHmmss.SSS",
        )

    val dateTimeWithMicrosTimePatterns =
        listOf(
            "yyyy-MM-dd'T'HH:mm:ss.SSSSSS",
            "yyyy-MM-dd'T'HHmmss.SSSSSS",
        )

    val dateTimeWithNanosTimePatterns =
        listOf(
            "yyyy-MM-dd'T'HH:mm:ss.SSSSSSSSS",
            "yyyy-MM-dd'T'HHmmss.SSSSSSSSS",
        )

    private val dateOnlyTimeFormatters =
        dateOnlyPatterns.map {
            DateTimeFormatterBuilder().appendPattern(it).parseDefaulting(ChronoField.NANO_OF_DAY, 0).toFormatter() to expectedLength(it)
        }
    private val dateTimeWithoutSubSecTimeFormatters =
        dateTimeWithoutSubSecTimePatterns.map {
            DateTimeFormatter.ofPattern(it) to expectedLength(it)
        }
    private val dateTimeWithMillisTimeFormatters =
        dateTimeWithMillisTimePatterns.map { DateTimeFormatter.ofPattern(it) to expectedLength(it) }
    private val dateTimeWithMicrosTimeFormatters =
        dateTimeWithMicrosTimePatterns.map { DateTimeFormatter.ofPattern(it) to expectedLength(it) }
    private val dateTimeWithNanosTimeFormatters =
        dateTimeWithNanosTimePatterns.map { DateTimeFormatter.ofPattern(it) to expectedLength(it) }

    private fun expectedLength(formatStr: String): Int = formatStr.replace("'T'", "T").length

    fun parseTimeAndRound(
        dateTimeString: String,
        timezone: ZoneId,
        roundToLatest: Boolean,
    ): FunctionResult<String, Instant> {
        val normalizedTime =
            if (dateTimeString.endsWith('Z', true)) {
                dateTimeString.substring(0, dateTimeString.length - 1)
            } else {
                dateTimeString
            }

        val dateOnly =
            dateOnlyTimeFormatters.mapNotNull {
                parseDate(normalizedTime, timezone, it)
            }
        if (dateOnly.isNotEmpty()) {
            return FunctionResult.Success(
                if (roundToLatest) {
                    dateOnly[0].plus(1, ChronoUnit.DAYS).minusNanos(1)
                } else {
                    dateOnly[0]
                },
            )
        }

        val dateTimeWithoutSubSec =
            dateTimeWithoutSubSecTimeFormatters.mapNotNull {
                parseDateTime(normalizedTime, timezone, it)
            }
        if (dateTimeWithoutSubSec.isNotEmpty()) {
            return FunctionResult.Success(
                if (roundToLatest) {
                    dateTimeWithoutSubSec[0].plusSeconds(1).minusNanos(1)
                } else {
                    dateTimeWithoutSubSec[0]
                },
            )
        }

        val dateTimeWithMillis =
            dateTimeWithMillisTimeFormatters.mapNotNull {
                parseDateTime(normalizedTime, timezone, it)
            }
        if (dateTimeWithMillis.isNotEmpty()) {
            return FunctionResult.Success(
                if (roundToLatest) {
                    dateTimeWithMillis[0].plus(1, ChronoUnit.MILLIS).minusNanos(1)
                } else {
                    dateTimeWithMillis[0]
                },
            )
        }

        val dateTimeWithMicros =
            dateTimeWithMicrosTimeFormatters.mapNotNull {
                parseDateTime(normalizedTime, timezone, it)
            }
        if (dateTimeWithMicros.isNotEmpty()) {
            return FunctionResult.Success(
                if (roundToLatest) {
                    dateTimeWithMicros[0].plus(1, ChronoUnit.MICROS).minusNanos(1)
                } else {
                    dateTimeWithMicros[0]
                },
            )
        }

        val dateTimeWithNanos =
            dateTimeWithNanosTimeFormatters.mapNotNull {
                parseDateTime(normalizedTime, timezone, it)
            }
        if (dateTimeWithNanos.isNotEmpty()) {
            return FunctionResult.Success(
                if (roundToLatest) {
                    dateTimeWithNanos[0].plus(1, ChronoUnit.NANOS).minusNanos(1)
                } else {
                    dateTimeWithNanos[0]
                },
            )
        }

        return FunctionResult.Failure("datetime string '$dateTimeString' has incorrect format.")
    }

    private fun parseDateTime(
        dateString: String,
        timezone: ZoneId,
        formatterWithExpectedLength: Pair<DateTimeFormatter, Int>,
    ): Instant? {
        if (dateString.length != formatterWithExpectedLength.second) return null
        return try {
            formatterWithExpectedLength.first
                .withZone(timezone)
                .parse(dateString) { Instant.from(it) }
        } catch (e: DateTimeParseException) {
            null
        }
    }

    private fun parseDate(
        dateString: String,
        timezone: ZoneId,
        formatterWithExpectedLength: Pair<DateTimeFormatter, Int>,
    ): Instant? {
        if (dateString.length != formatterWithExpectedLength.second) return null
        return try {
            formatterWithExpectedLength.first
                .withZone(timezone)
                .parse(dateString) { Instant.from(it) }
        } catch (e: DateTimeParseException) {
            null
        }
    }

    /**
     * Postgres TIMESTAMP doesn't support nanos, it only supports micros.
     * So we need to query larger range if nanoseconds are present.
     */
    fun toSqlCompareExpressionPair(
        comparison: String,
        time: Instant,
    ): Pair<String, String> {
        val hasNanos = time.nano % 1000 != 0
        val truncatedTime = time.truncatedTo(ChronoUnit.MICROS)
        return if (comparison == "=") {
            comparison to instantToSqlFormattedTimestamp(truncatedTime)
        } else if (comparison == "<" || comparison == "<=") {
            if (hasNanos) {
                // round to the next microsecond if nanoseconds are present
                "<" to instantToSqlFormattedTimestamp(truncatedTime.plusNanos(1000))
            } else {
                comparison to instantToSqlFormattedTimestamp(truncatedTime)
            }
        } else {
            if (hasNanos) {
                // round to the beginning of the current microsecond if nanoseconds are present
                ">" to instantToSqlFormattedTimestamp(truncatedTime)
            } else {
                comparison to instantToSqlFormattedTimestamp(truncatedTime)
            }
        }
    }

    fun toSqlCompareExpression(
        comparison: String,
        time: Instant,
    ): String {
        val (operator, timestamp) = toSqlCompareExpressionPair(comparison, time)
        return "$operator $timestamp"
    }

    private fun instantToSqlFormattedTimestamp(time: Instant): String = "'" + Timestamp.from(time).toString() + "'::timestamp"

    /**
     * The algorithm doesn't try to align the timestamps to the start of corresponding time intervals defined by the passed "granularity".
     * The "timezone" query parameter doesn't affect results.
     * The mentioned differences distinguish this algorithm from createTimeFilteringQuery logic.
     */
    fun createStatefulDownSampler(config: StatefulDownSamplerConfig): (Instant) -> Boolean {
        var nextTime: Instant? = null
        return { time ->
            if (nextTime == null || config.comparisonFun.compare(time, nextTime) >= 0) {
                nextTime = config.nextTimeFun.invoke(time)
                true
            } else {
                false
            }
        }
    }

    /**
     * Creates a stateful downsampler that selects the record closest to each boundary.
     * This version buffers records and compares distances to boundaries to ensure
     * the closest record to each time boundary is selected.
     */
    fun createBoundaryAwareStatefulDownSampler(config: StatefulDownSamplerConfig): StatefulBoundaryAwareDownSampler {
        return StatefulBoundaryAwareDownSampler(config)
    }

    class StatefulDownSamplerConfig(
        val pagingFromStart: Boolean,
        val comparisonFun: Comparator<Instant>,
        val nextTimeFun: (Instant) -> Instant,
    )

    /**
     * A stateful downsampler that selects the record closest to each time boundary.
     * It buffers records and compares their distances to boundaries to ensure optimal selection.
     */
    class StatefulBoundaryAwareDownSampler(private val config: StatefulDownSamplerConfig) {
        private var currentBoundary: Instant? = null
        private var candidateRecord: Instant? = null
        private var candidateDistance: Long = Long.MAX_VALUE

        /**
         * Processes a record and returns the record that should be emitted, or null if none.
         * This method looks ahead to find the record closest to each boundary.
         */
        fun processRecord(time: Instant): ProcessResult {
            // Initialize boundary on first record
            if (currentBoundary == null) {
                currentBoundary = config.nextTimeFun.invoke(time)
                candidateRecord = time
                candidateDistance = calculateDistanceToBoundary(time, currentBoundary!!)
                return ProcessResult.Buffer
            }

            val boundary = currentBoundary!!

            // Check if we've crossed the boundary
            if (config.comparisonFun.compare(time, boundary) >= 0) {
                // We've crossed the boundary, emit the best candidate from previous boundary
                val recordToEmit = candidateRecord

                // Update to next boundary and start fresh
                currentBoundary = config.nextTimeFun.invoke(time)
                candidateRecord = time
                candidateDistance = calculateDistanceToBoundary(time, currentBoundary!!)

                return if (recordToEmit != null) {
                    ProcessResult.EmitAndBuffer(recordToEmit)
                } else {
                    ProcessResult.Buffer
                }
            } else {
                // Still within current boundary, check if this record is closer
                val currentDistance = calculateDistanceToBoundary(time, boundary)

                if (currentDistance < candidateDistance) {
                    // This record is closer to the boundary
                    candidateRecord = time
                    candidateDistance = currentDistance
                }
                return ProcessResult.Buffer
            }
        }

        /**
         * Call this when the stream ends to get the last buffered record if any.
         */
        fun finalize(): Instant? = candidateRecord

        private fun calculateDistanceToBoundary(time: Instant, boundary: Instant): Long {
            return kotlin.math.abs(time.toEpochMilli() - boundary.toEpochMilli())
        }

        sealed class ProcessResult {
            object Buffer : ProcessResult()
            data class EmitAndBuffer(val recordToEmit: Instant) : ProcessResult()
        }
    }

    private val tenSecondsUnit = DurationUnit(Duration.ofSeconds(10))

    fun createStatefulDownSamplerConfig(
        granularity: String,
        pagingFromStart: Boolean,
        withAlignment: Boolean = false,
        timezone: ZoneId = ZoneOffset.UTC,
    ): FunctionResult<ApiError, StatefulDownSamplerConfig?> {
        val timeUnit =
            when (granularity) {
                "raw" -> return FunctionResult.Success(null)
                "1d" -> ChronoUnit.DAYS
                "1h" -> ChronoUnit.HOURS
                "1m" -> ChronoUnit.MINUTES
                "10s" -> tenSecondsUnit
                "1s" -> ChronoUnit.SECONDS
                else -> return FunctionResult.Failure(
                    ApiError.UnsupportedParameterValueWithSupportedInfo(
                        "granularity",
                        granularity,
                        listOf("raw", "1d", "1h", "1m", "1s"),
                    ),
                )
            }
        return StatefulDownSamplerConfig(
            pagingFromStart = pagingFromStart,
            comparisonFun = if (pagingFromStart) Comparator.naturalOrder() else Comparator.reverseOrder(),
            nextTimeFun =
                if (withAlignment) {
                    if (pagingFromStart) {
                        { time ->
                            val truncatedTime = truncate(time = time, timeUnit = timeUnit, timezone = timezone)
                            truncatedTime.plus(1, timeUnit)
                        }
                    } else {
                        { time ->
                            val truncatedTime = truncate(time = time, timeUnit = timeUnit, timezone = timezone)
                            if (truncatedTime == time) {
                                truncatedTime.minus(1, timeUnit)
                            } else {
                                truncatedTime
                            }
                        }
                    }
                } else {
                    if (pagingFromStart) {
                        { time -> time.plus(1, timeUnit) }
                    } else {
                        { time -> time.minus(1, timeUnit) }
                    }
                },
        ).toSuccess()
    }

    private fun truncate(
        time: Instant,
        timeUnit: TemporalUnit,
        timezone: ZoneId,
    ): Instant =
        if (timeUnit == ChronoUnit.DAYS && timezone != ZoneOffset.UTC) {
            time.atZone(timezone).truncatedTo(timeUnit).toInstant()
        } else {
            time.truncatedTo(timeUnit)
        }

    fun fromMicros(mu: Long): Instant = Instant.ofEpochSecond(mu / 1_000_000, (mu % 1_000_000) * 1_000)

    fun toMicros(instant: Instant): Long = with(instant) { epochSecond * 1_000_000 + nano / 1_000 }

    fun fromEpochNanoSecond(epochNanoSecond: Long): Instant {
        val seconds = TimeUnit.NANOSECONDS.toSeconds(epochNanoSecond)
        return Instant.ofEpochSecond(seconds, epochNanoSecond - TimeUnit.SECONDS.toNanos(seconds))
    }

    val Instant.epochNanosecond: Long
        get() = TimeUnit.SECONDS.toNanos(epochSecond) + nano

    // for temporary workaround
    fun haveTheSameSeconds(
        startTime: String,
        endTime: String,
        timezone: String,
    ): Boolean {
        val zoneId = ZoneId.of(timezone)
        val startSec =
            when (val t = parseTimeAndRound(startTime, zoneId, false)) {
                is FunctionResult.Failure -> return false
                is FunctionResult.Success -> t.value.epochSecond
            }
        val endSec =
            when (val t = parseTimeAndRound(endTime, zoneId, true)) {
                is FunctionResult.Failure -> return false
                is FunctionResult.Success -> t.value.with(ChronoField.NANO_OF_SECOND, 0).epochSecond
            }
        return startSec == endSec
    }

    /**
     * Parses "1d-ny-close" to "1d" and "ny-close".
     * @return frequency and time offset
     */
    fun parseFrequency(frequency: String): Pair<String, String?> {
        val pos = frequency.indexOf('-')
        return if (pos < 0) {
            frequency to null
        } else {
            frequency.substring(0, pos) to frequency.substring(pos + 1)
        }
    }

    /**
     * Converts "ny-close" to (16, 0, "America/New_York") and "10:20" to (10, 20, null).
     * @return hourOffset (0-23), minuteOffset (0-59), forcedTimeZone
     */
    fun normalizeFrequencyOffset(offset: String?): NormalizedFrequencyOffset? {
        return when (offset) {
            null -> NormalizedFrequencyOffset.DEFAULT
            "ny-close" -> NormalizedFrequencyOffset(16, 0, "America/New_York")
            "ny-midday" -> NormalizedFrequencyOffset(12, 0, "America/New_York")
            "sg-close" -> NormalizedFrequencyOffset(16, 0, "Asia/Singapore")
            else -> {
                val parts = offset.split(':')
                if (parts.size != 2) return null
                val hour = parts[0].toIntOrNull()
                val minute = parts[1].toIntOrNull()
                if (hour == null || minute == null) return null
                if (hour < 0 || hour > 23) return null
                if (minute < 0 || minute > 59) return null
                NormalizedFrequencyOffset(hour, minute, null)
            }
        }
    }

    data class NormalizedFrequencyOffset(
        val hours: Int,
        val minutes: Int,
        val forcedTimeZone: String?,
    ) {
        companion object {
            val DEFAULT = NormalizedFrequencyOffset(0, 0, null)
        }

        val default: Boolean
            get() = this === DEFAULT
    }

    fun getEnforcedStartTime(
        timeRestrictions: List<String>,
        timezone: String? = null,
        maxAvailableTime: String,
        requestReceivedAt: Instant,
    ): FunctionResult<ApiError, Instant?> {
        val zoneId = timezone?.let { parseTimezoneRequestParam(it).getOrElse { apiError -> return apiError.toFailure() } } ?: ZoneOffset.UTC
        return getEnforcedStartTime(timeRestrictions, zoneId, maxAvailableTime, requestReceivedAt).toSuccess()
    }

    /**
     * Time restriction is a list that can contain values like this: ["null", "latest-1d", "latest-1h"].
     * If multiple restrictions are specified, choose the max value.
     * If the list contains "null" this means no restrictions will be applied.
     */
    fun getEnforcedStartTime(
        timeRestrictions: List<String>,
        zoneId: ZoneId,
        maxAvailableTime: String,
        requestReceivedAt: Instant,
    ): Instant? {
        val maxTime = parseTimeAndRound(maxAvailableTime, zoneId, roundToLatest = false).getOrElse { error(it) }
        val context = TimeRestriction.EvaluateContext(now = requestReceivedAt, maxAvailableTime = maxTime)
        return timeRestrictions.takeIf { "null" !in it }?.maxOfOrNull {
            val bound = TimeRestriction.Bound.parse(it)
            require(bound is TimeRestriction.Bound.Relative) {
                "Unsupported time_restriction: $it"
            }
            bound.evaluate(context)
        }
    }

    fun parseTimezoneRequestParam(timezone: String): FunctionResult<ApiError, ZoneId> =
        try {
            FunctionResult.Success(ZoneId.of(timezone))
        } catch (e: Exception) {
            log.error("Can't parse timezone '{}'", timezone, e)
            FunctionResult.Failure(ApiError.BadParameter("timezone"))
        }

    fun format(time: Instant): String = dateTimeFormatter.format(time)
}

interface WithTime {
    val time: Instant
}

interface WithHeight {
    fun height(): String
}
