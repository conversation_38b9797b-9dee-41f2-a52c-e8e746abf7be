package io.coinmetrics.api.persistence

import io.coinmetrics.databases.Database

class DefiDatabase(
    database: Database,
) : Database by database {
    companion object {
        private const val PROTOCOLS_BALANCE_SHEET_TABLE_NAME = "protocols_balance_sheet"
    }

    fun protocolsBalanceSheetTableName(): String = "${config.schema}.$PROTOCOLS_BALANCE_SHEET_TABLE_NAME"

    suspend fun protocolsBalanceSheetTableExists(): <PERSON><PERSON><PERSON> =
        query(
            """
            SELECT relname
            FROM pg_catalog.pg_class c
                join pg_catalog.pg_namespace n on c.relnamespace = n.oid
            WHERE n.nspname = '${config.schema}' AND relkind IN ('r', 'v') AND relname = '$PROTOCOLS_BALANCE_SHEET_TABLE_NAME';
            """.trimIndent(),
        ) {
            it.map { rs -> rs.getString("relname") }.filterNotNull()
        }.isNotEmpty()
}
