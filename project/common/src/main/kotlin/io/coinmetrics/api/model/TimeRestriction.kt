package io.coinmetrics.api.model

import java.time.Duration
import java.time.Instant

data class TimeRestriction(
    val start: Bound?,
    val end: Bound?,
) {
    data class EvaluateContext(
        val now: Instant,
        val maxAvailableTime: Instant?,
    )

    sealed class Bound {
        data class Relative(
            val delay: Duration,
            val origin: Origin,
        ) : Bound() {
            enum class Origin {
                Now,
                MaxAvailable,
            }

            override fun evaluate(context: EvaluateContext): Instant {
                val originTime =
                    when (origin) {
                        Origin.Now -> context.now
                        Origin.MaxAvailable -> {
                            require(context.maxAvailableTime != null) {
                                "maxAvailableTime wasn't set"
                            }
                            context.maxAvailableTime
                        }
                    }
                return originTime - delay
            }

            override fun toString(): String = "TimeRange.Bound.Relative(delay=$delay, origin=$origin)"
        }

        abstract fun evaluate(context: EvaluateContext): Instant

        companion object {
            fun parse(s: String): Bound {
                val parts = s.split('-', limit = 2)
                val origin =
                    when (parts[0]) {
                        "latest" -> Relative.Origin.MaxAvailable
                        "now" -> Relative.Origin.Now
                        else -> parseError(s)
                    }

                val numericPart = parts[1].dropLast(1).toLongOrNull() ?: parseError(s)
                val unit = parts[1].last()
                val delay =
                    when (unit) {
                        'd' -> Duration.ofDays(numericPart)
                        'h' -> Duration.ofHours(numericPart)
                        'm' -> Duration.ofMinutes(numericPart)
                        's' -> Duration.ofSeconds(numericPart)
                        else -> parseError(s)
                    }

                return Relative(delay, origin)
            }

            private fun parseError(s: String): Nothing = throw IllegalArgumentException("Invalid TimeRange.Bound: $s")
        }
    }

    companion object {
        val unbounded = TimeRestriction(start = null, end = null)

        val strictnessComparator =
            Comparator<TimeRestriction> { a, b ->
                when {
                    a.isUnbounded() && b.isUnbounded() -> 0
                    a.isUnbounded() -> -1 // a is less strict
                    b.isUnbounded() -> 1 // b is less strict
                    else -> {
                        // More bounds = more strict
                        val aBounds = listOfNotNull(a.start, a.end).size
                        val bBounds = listOfNotNull(b.start, b.end).size
                        val boundsComp = aBounds.compareTo(bBounds)

                        if (boundsComp != 0) {
                            boundsComp
                        } else {
                            // Same number of bounds, compare by strictness score
                            val aScore = a.getStrictnessScore()
                            val bScore = b.getStrictnessScore()
                            aScore.compareTo(bScore) // Lower score = less strict
                        }
                    }
                }
            }

        fun parse(s: String): TimeRestriction {
            if (s == "null") {
                return unbounded
            }
            val parts = s.split(":", limit = 2)
            val start = parts[0].takeIf { it.isNotEmpty() }?.let { Bound.parse(it) }
            val end = parts.getOrNull(1)?.takeIf { it.isNotEmpty() }?.let { Bound.parse(it) }
            return TimeRestriction(start, end)
        }
    }

    fun isUnbounded() = start == null && end == null

    fun evaluate(context: EvaluateContext) = start?.evaluate(context) to end?.evaluate(context)

    private fun getStrictnessScore(): Long {
        var score = 0L

        // For start bounds: subtract delay (smaller delay = higher score = more strict)
        start?.let { bound ->
            if (bound is Bound.Relative) {
                score -= bound.delay.toSeconds()
            }
        }

        // For end bounds: add delay (smaller delay = lower score = less strict)
        end?.let { bound ->
            if (bound is Bound.Relative) {
                score += bound.delay.toSeconds()
            }
        }

        return score
    }
}

fun Pair<Instant?, Instant?>.isUnbounded() = first == null && second == null
