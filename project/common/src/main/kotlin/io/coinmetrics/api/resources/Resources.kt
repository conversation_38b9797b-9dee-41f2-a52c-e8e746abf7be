package io.coinmetrics.api.resources

import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.toSuccess
import org.slf4j.LoggerFactory
import java.io.InputStream

object Resources {
    // TODO: reload resources dynamically
    private val mapper =
        ObjectMapper()
            .registerKotlinModule()
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)

    private val log = LoggerFactory.getLogger(this.javaClass)

    val currencies: List<CurrencyInfo>
    private val currencyTickerToInfoMap: Map<String, CurrencyInfo>
    private val currencyIdToTickerMap: Map<Int, String>

    private val metricToInfoMap: Map<String, MetricInfo>

    val exchangeIdToExchange: Map<Int, Exchange>
    val exchangeNameToIdMap: Map<String, Int>

    private val institutionIdToNameMap: Map<Int, String>
    private val institutionNameToIdMap: Map<String, Int>

    val indexNameToInfoMap: Map<String, IndexInfo>
    private val indexIdToNamesMap: Map<Int, List<String>>
    private val priceReturnToTotalReturnIndexMap: Map<String, String>
    val nonFidelityIndexes: List<IndexInfo>
    val cmbiIndexesMap: Map<String, IndexInfo>
    private val fidelityIndexes: List<IndexInfo>
    val fidelityPrimaryIndexes: List<IndexInfo>
    val fidelityMultiAssetIndexNames: List<String>

    init {
        currencies =
            try {
                mapper
                    .readValue<List<CurrencyInfo>>(
                        loadResource(
                            "currency.json",
                        ),
                    ).sortedBy { it.cmTicker }
            } catch (e: Exception) {
                throw IllegalStateException("Can't parse currency.json.", e)
            }

        currencyTickerToInfoMap =
            currencies
                .asSequence()
                .flatMap { currencyInfo ->
                    // accounting for the deprecated assets
                    (sequenceOf(currencyInfo.cmTicker) + (currencyInfo.previousCmTickers?.asSequence() ?: emptySequence()))
                        .map { ticker -> ticker to currencyInfo }
                }.toMap(HashMap())

        currencyIdToTickerMap = currencies.associateTo(HashMap()) { it.id to it.cmTicker }

        val metrics: List<MetricInfo> =
            try {
                mapper.readValue(
                    loadResource(
                        "metrics.json",
                    ),
                )
            } catch (e: Exception) {
                throw IllegalStateException("Can't parse metrics.json.", e)
            }
        metricToInfoMap =
            metrics.associateByTo(HashMap()) { it.shortForm }.also { map ->
                map["ReferenceRate"] = map.getValue("ReferenceRateUSD")
            }

        val exchanges: List<Exchange> =
            try {
                mapper.readValue<List<Exchange>>(loadResource("exchange.json"))
            } catch (e: Exception) {
                throw IllegalStateException("Can't parse exchange.json.", e)
            }

        exchangeNameToIdMap =
            exchanges.associateTo(HashMap()) {
                normalizeExchangeName(
                    it.name,
                ) to it.id
            }
        exchangeIdToExchange = exchanges.associateByTo(HashMap()) { it.id }

        val institutions: List<Exchange> =
            try {
                mapper.readValue(
                    loadResource(
                        "institutions.json",
                    ),
                )
            } catch (e: Exception) {
                throw IllegalStateException("Can't parse institutions.json.", e)
            }
        institutionIdToNameMap =
            institutions.associateTo(HashMap()) {
                it.id to
                    normalizeExchangeName(
                        it.name,
                    )
            }
        institutionNameToIdMap =
            institutions.associateTo(HashMap()) {
                normalizeExchangeName(
                    it.name,
                ) to it.id
            }

        val indexesInfo: List<IndexInfo> =
            try {
                mapper.readValue(
                    loadResource(
                        "indexes.json",
                    ),
                )
            } catch (e: Exception) {
                throw IllegalStateException("Can't parse indexes.json.", e)
            }
        indexNameToInfoMap = indexesInfo.associateByTo(HashMap()) { it.name }
        indexIdToNamesMap = indexesInfo.groupByTo(HashMap(), { it.id }, { it.name })
        priceReturnToTotalReturnIndexMap =
            indexesInfo
                .groupByTo(HashMap(), { it.id }, { it })
                .filter { it.value.size == 2 }
                .map { (_, value) ->
                    val priceReturn = value.find { it.returnType == "price_return" }
                    val totalReturn = value.find { it.returnType == "total_return" }
                    if (priceReturn == null || totalReturn == null) null else priceReturn.name to totalReturn.name
                }.filterNotNull()
                .toMap()
        nonFidelityIndexes = indexesInfo.filter { !it.base.equals("fid", ignoreCase = true) }
        cmbiIndexesMap = indexesInfo.filter { it.base.equals("cmbi", ignoreCase = true) }.associateByTo(HashMap()) { it.name }
        fidelityIndexes = indexesInfo.filter { it.base.equals("fid", ignoreCase = true) }
        fidelityPrimaryIndexes = fidelityIndexes.filter { it.returnType.equals("price_return", ignoreCase = true) }
        fidelityMultiAssetIndexNames =
            fidelityIndexes
                .filter {
                    it.type != null && it.type.equals("multi_asset", ignoreCase = true)
                }.map { it.name }
                .distinct()
    }

    private fun loadResource(name: String): InputStream {
        log.info("Reading in file: {}", name)
        return this::class.java.classLoader.getResourceAsStream(name)!!
    }

    fun normalizeExchangeName(name: String) = name.lowercase().replace('-', '_')

    fun isFlowMetric(metric: String): Boolean {
        val info = metricToInfoMap[metric]
        return if (info == null) {
            log.warn("Metric '{}' is not found in resources.", metric)
            false
        } else {
            info.flow
        }
    }

    fun retainReviewableMetrics(metrics: Array<String>): Set<String> =
        metrics.asSequence().filter { metric -> isMetricReviewable(metric) }.toHashSet()

    fun isMetricReviewable(metric: String): Boolean =
        if (metric.startsWith("ReferenceRate")) {
            false
        } else {
            metricToInfoMap[metric]?.reviewable ?: false
        }

    fun getMetricInfo(metric: String): MetricInfo? =
        metricToInfoMap[metric].also {
            if (it == null) log.warn("Metric '{}' is not found in resources.", metric)
        }

    fun getIndexInfo(index: String): FunctionResult<String, IndexInfo> =
        indexNameToInfoMap[index]?.toSuccess()
            ?: FunctionResult.Failure("Index '$index' is not found in resources.")

    fun getIndexNamesById(indexId: Int): FunctionResult<String, List<String>> =
        indexIdToNamesMap[indexId]?.toSuccess()
            ?: FunctionResult.Failure("Index '$indexId' is not found in resources.")

    fun getAssociatedTotalReturnIndexName(priceReturnIndexName: String): String? = priceReturnToTotalReturnIndexMap[priceReturnIndexName]

    fun getExchangeById(id: Int): FunctionResult<String, Exchange> =
        exchangeIdToExchange[id]?.toSuccess()
            ?: FunctionResult.Failure("Exchange '$id' is not found in resources.")

    fun getExchangeIdByName(name: String): FunctionResult<String, Int> {
        val exchangeId =
            exchangeNameToIdMap[name]
                ?: return FunctionResult.Failure("Exchange '$name' is not found in resources.")
        return exchangeId.toSuccess()
    }

    fun getExchangeByName(name: String): FunctionResult<String, Exchange> = getExchangeIdByName(name).flatMap { getExchangeById(it) }

    fun getExchangeNameById(id: Int): FunctionResult<String, String> = getExchangeById(id).map { normalizeExchangeName(it.name) }

    fun getInstitutionIdByName(name: String): FunctionResult<String, Int> =
        institutionNameToIdMap[name]?.toSuccess()
            ?: FunctionResult.Failure("Institution '$name' is not found in resources.")

    fun getInstitutionNameById(id: Int): FunctionResult<String, String> =
        institutionIdToNameMap[id]?.toSuccess()
            ?: FunctionResult.Failure("Institution id '$id' is not found in resources.")

    fun getCurrencyTickerById(id: Int): String? = currencyIdToTickerMap[id]

    fun getCurrencyInfo(ticker: String): CurrencyInfo? = currencyTickerToInfoMap[ticker]

    data class Exchange(
        val id: Int,
        val name: String,
        @JsonProperty("rest_api_rate_limit")
        val restApiRateLimit: Float,
        val defi: Boolean = false,
        @JsonProperty("defi_pools_supported")
        val defiPoolsSupported: Boolean = false,
        val network: String? = null,
    ) {
        fun getNormalizedName() = normalizeExchangeName(name)
    }
}
