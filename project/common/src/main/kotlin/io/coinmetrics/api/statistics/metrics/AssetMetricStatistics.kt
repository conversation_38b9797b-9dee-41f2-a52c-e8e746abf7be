package io.coinmetrics.api.statistics.metrics

import com.fasterxml.jackson.annotation.JsonProperty
import io.coinmetrics.api.statistics.StatisticsDescriptor
import io.coinmetrics.api.statistics.StatisticsRepository
import io.coinmetrics.api.utils.StringTriple

class AssetMetricStatistics(
    repository: StatisticsRepository,
) {
    companion object {
        val descriptor = StatisticsDescriptor.create<AssetMetricStatisticsModel>("asset-metrics")
    }

    private val repoItem = repository.getItem(descriptor)

    fun getAssetMetricStatistics(
        asset: String,
        metric: String,
        frequency: String,
    ): AssetMetricStatisticsModel.Statistics? = repoItem.value.statisticsPerAsset[StringTriple(asset, metric, frequency)]
}

data class AssetMetricStatisticsModel(
    /**
     * Map<Triple<Asset, Metric, Frequency>, Statistic>
     */
    @JsonProperty("statistics_per_asset")
    val statisticsPerAsset: Map<StringTriple, Statistics>,
) {
    data class Statistics(
        @JsonProperty("min_time")
        val minTime: String,
        @JsonProperty("max_time")
        val maxTime: String,
        @JsonProperty("min_height")
        val minHeight: String? = null,
        @JsonProperty("max_height")
        val maxHeight: String? = null,
        @JsonProperty("min_hash")
        val minHash: String? = null,
        @JsonProperty("max_hash")
        val maxHash: String? = null,
    )
}
