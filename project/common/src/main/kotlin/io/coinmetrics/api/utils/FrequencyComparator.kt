package io.coinmetrics.api.utils

object FrequencyComparator : Comparator<String> {
    private val unitOrder = listOf("b", "s", "m", "h", "d", "d-ny-close", "d-sg-close")

    override fun compare(
        one: String,
        two: String,
    ): Int {
        if (one == two) {
            return 0
        }

        val (oneAmount, oneUnit) =
            if (one == "1d-ny-close") {
                1 to "d-ny-close"
            } else {
                one.dropLast(1).toIntOrNull() to one.last().toString()
            }

        val (twoAmount, twoUnit) =
            if (two == "1d-ny-close") {
                1 to "d-ny-close"
            } else {
                two.dropLast(1).toIntOrNull() to two.last().toString()
            }

        val oneOrder = unitOrder.indexOf(oneUnit).takeIf { oneAmount != null && it >= 0 } ?: Integer.MAX_VALUE
        val twoOrder = unitOrder.indexOf(twoUnit).takeIf { twoAmount != null && it >= 0 } ?: Integer.MAX_VALUE

        if (oneOrder == twoOrder) {
            val oneAmountNotNull = oneAmount ?: Integer.MAX_VALUE
            val twoAmountNotNull = twoAmount ?: Integer.MAX_VALUE
            return oneAmountNotNull - twoAmountNotNull
        }
        return oneOrder - twoOrder
    }
}
