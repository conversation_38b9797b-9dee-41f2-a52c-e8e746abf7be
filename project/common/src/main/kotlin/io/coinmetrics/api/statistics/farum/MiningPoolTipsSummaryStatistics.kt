package io.coinmetrics.api.statistics.farum

import io.coinmetrics.api.model.DataAvailabilityTimeRange
import io.coinmetrics.api.statistics.StatisticsDescriptor
import io.coinmetrics.api.statistics.StatisticsRepository

class MiningPoolTipsSummaryStatistics(
    repository: StatisticsRepository,
) {
    companion object {
        val descriptor =
            StatisticsDescriptor.create<Map<String, DataAvailabilityTimeRange>>("mining-pool-tips-summary")
    }

    private val repoItem = repository.getItem(descriptor)

    fun getMiningPoolTipsSummaryStatistics() = repoItem.value
}
