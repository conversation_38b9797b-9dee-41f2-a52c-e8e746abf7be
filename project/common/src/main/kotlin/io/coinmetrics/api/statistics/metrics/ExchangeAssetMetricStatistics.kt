package io.coinmetrics.api.statistics.metrics

import com.fasterxml.jackson.annotation.JsonProperty
import io.coinmetrics.api.model.DataAvailabilityTimeRange
import io.coinmetrics.api.statistics.StatisticsDescriptor
import io.coinmetrics.api.statistics.StatisticsRepository

class ExchangeAssetMetricStatistics(
    repository: StatisticsRepository,
) : MetricsStatistics {
    companion object {
        val descriptor = StatisticsDescriptor.create<ExchangeAssetMetricStatisticsModel>("exchange-asset-metrics")
    }

    private val repoItem = repository.getItem(descriptor)

    override fun isSupported(entity: String): Boolean = repoItem.value.supportedExchangeAssets.contains(entity)

    override fun isSupported(
        entity: String,
        metric: String,
        frequency: String,
    ): Boolean =
        repoItem.value.statisticsPerExchangeAsset[entity]
            ?.get(metric)
            ?.get(frequency) != null

    override fun groupedByMetric(): Map<String, Map<String, Set<String>>> = repoItem.value.statisticsPerMetric

    override fun groupedByEntity(): Map<String, Map<String, Map<String, DataAvailabilityTimeRange>>> =
        repoItem.value.statisticsPerExchangeAsset
}

data class ExchangeAssetMetricStatisticsModel(
    @JsonProperty("supported_exchange_assets")
    val supportedExchangeAssets: Set<String>,
    /**
     * Map<Metric, Map<Frequency, Set<ExchangeAsset>>>
     */
    @JsonProperty("statistics_per_metric")
    val statisticsPerMetric: Map<String, Map<String, Set<String>>>,
    /**
     * Map<Triple<ExchangeAsset, Metric, Frequency>, Statistic>
     */
    @JsonProperty("statistics_per_exchange_asset")
    val statisticsPerExchangeAsset: Map<String, Map<String, Map<String, DataAvailabilityTimeRange>>>,
)
