package io.coinmetrics.api.persistence

import io.coinmetrics.api.utils.SqlUtils
import io.coinmetrics.databases.Database
import java.sql.ResultSet

class AddressTaggingDatabase(
    database: Database,
) : Database by database {
    companion object {
        private const val TAGS_TABLE_NAME = "tags"
        private const val TAG_LOCATIONS_TABLE_NAME = "tag_locations"
    }

    suspend fun tagsTableName(): String? = if (tagsTableExists()) "${config.schema}.$TAGS_TABLE_NAME" else null

    suspend fun tagLocationsTableName(): String? = if (tagLocationsTableExists()) "${config.schema}.$TAG_LOCATIONS_TABLE_NAME" else null

    private suspend fun tagsTableExists(): Boolean = isTableExists(config.schema, TAGS_TABLE_NAME)

    private suspend fun tagLocationsTableExists(): Boolean = isTableExists(config.schema, TAG_LOCATIONS_TABLE_NAME)

    private suspend fun isTableExists(
        schema: String,
        name: String,
    ): Boolean =
        query(
            SqlUtils.createSearchQueryForTablesAndViewsByName(schema, name),
        ) { it.map(relnameMapper()).toList() }.isNotEmpty()

    private fun relnameMapper(): (ResultSet) -> String = { rs -> rs.getString("relname") }
}
