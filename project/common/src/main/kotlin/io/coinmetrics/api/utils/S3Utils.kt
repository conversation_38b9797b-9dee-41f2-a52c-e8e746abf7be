package io.coinmetrics.api.utils

import io.coinmetrics.api.config.StaticTierConfig
import io.coinmetrics.s3databases.S3Databases
import io.coinmetrics.s3databases.storageclients.S3StorageClient
import io.prometheus.metrics.core.metrics.Histogram
import java.time.Duration
import java.time.temporal.ChronoUnit

object S3Utils {
    /**
     * Assumes that we have only one S3 tier for now.
     */
    fun createS3StorageClient(
        tiers: List<StaticTierConfig>,
        s3LatencyHistogram: Histogram,
    ): S3StorageClient? {
        val s3Tier = tiers.find { it.type == StaticTierConfig.TierType.S3 }
        return if (s3Tier != null) {
            S3StorageClient(
                S3StorageClient.S3ClientConfig(
                    s3Tier.s3Endpoint,
                    s3Tier.s3Region,
                    s3Tier.s3AccessKey,
                    s3Tier.s3SecretKey,
                    connectionTimeout = Duration.ofSeconds(10),
                    tcpKeepAliveTimeout = Duration.ofSeconds(20),
                    onResponseLatencyNotification = { notification ->
                        s3LatencyHistogram
                            .labelValues(notification.s3Method, notification.bucketName)
                            .observe(notification.latencyMs.toDouble() / 1000)
                    },
                    trustAllCertificates = true,
                ),
            )
        } else {
            null
        }
    }

    fun createS3Databases(
        it: S3StorageClient,
        dellPowerScaleBugsWorkarounds: Boolean,
    ) = S3Databases(
        S3Databases.Config(
            tiers = listOf(S3Databases.Config.TierConfig(storageClient = it)),
            customPartitionKeyNames = listOf("exchange", "symbol"),
            timestampPrecision = ChronoUnit.MICROS,
            dellPowerScaleBugsWorkarounds = dellPowerScaleBugsWorkarounds,
        ),
    )
}
