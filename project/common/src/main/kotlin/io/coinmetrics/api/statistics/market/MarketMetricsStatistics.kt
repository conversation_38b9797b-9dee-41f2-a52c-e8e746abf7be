package io.coinmetrics.api.statistics.market

import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonProperty
import io.coinmetrics.api.statistics.StatisticsDescriptor
import io.coinmetrics.api.statistics.StatisticsRepository
import java.util.SortedSet

data class MarketMetricsStatisticsModel(
    val marketToMetricToMetricStatistics: Map<String, Map<String, List<MetricStatistics>>>,
) {
    val supportedEntities =
        SupportedMarkets.build {
            addUnparsed(marketToMetricToMetricStatistics.keys)
        }

    @JsonIgnore
    val marketMetrics: SortedSet<String> =
        marketToMetricToMetricStatistics.values
            .flatMap { it.keys }
            .toSortedSet()
}

data class MetricStatistics(
    @JsonProperty("min_time")
    val minTime: String,
    @JsonProperty("max_time")
    val maxTime: String,
    val frequency: String,
)

class MarketMetricsStatistics(
    repository: StatisticsRepository,
) : SupportedMarketStatistics {
    companion object {
        val descriptor = StatisticsDescriptor.create<MarketMetricsStatisticsModel>("market-metrics")
    }

    private val repoItem = repository.getItem(descriptor)

    override val supportedMarketsHash: Int
        get() = repoItem.valueAndHash.second.hashCode()

    val marketToMetricToMetricStatistics: Map<String, Map<String, List<MetricStatistics>>>
        get() = repoItem.value.marketToMetricToMetricStatistics

    val marketMetrics: Set<String>
        get() = repoItem.value.marketMetrics

    override fun isMarketSupported(market: String) =
        repoItem.value.supportedEntities.markets
            .contains(market)

    override fun isExchangeSupported(exchange: String) =
        repoItem.value.supportedEntities.exchanges
            .contains(exchange)

    override fun isSymbolSupported(symbol: String) =
        repoItem.value.supportedEntities.symbols
            .contains(symbol)

    override fun isBaseSupported(base: String) =
        repoItem.value.supportedEntities.bases
            .contains(base)

    override fun isQuoteSupported(quote: String) =
        repoItem.value.supportedEntities.quotes
            .contains(quote)

    override fun getSupportedMarkets(reverseOrder: Boolean) =
        repoItem.value.supportedEntities
            .getMarkets(reverseOrder)
            .asSequence()
}
