package io.coinmetrics.api.statistics.defi

import io.coinmetrics.api.statistics.StatisticsDescriptor
import io.coinmetrics.api.statistics.StatisticsRepository

class DefiStatistics(
    repository: StatisticsRepository,
) {
    companion object {
        val descriptor = StatisticsDescriptor.create<DefiStatisticsModel>("defi")
    }

    private val repoItem = repository.getItem(descriptor)

    fun getSupportedDefiProtocols() = repoItem.value.supportedDefiProtocols
}

data class DefiProtocol(
    val protocol: String,
    val version: String,
    val chain: String,
) {
    companion object {
        fun fromString(protocol: String): DefiProtocol? {
            val parts = protocol.lowercase().split("_").takeIf { it.size == 3 } ?: return null
            return DefiProtocol(parts[0], parts[1].removePrefix("v"), parts[2])
        }
    }

    override fun toString(): String = "${protocol}_v${version}_$chain"
}

class DefiStatisticsModel(
    val supportedDefiProtocols: Set<String>,
)
