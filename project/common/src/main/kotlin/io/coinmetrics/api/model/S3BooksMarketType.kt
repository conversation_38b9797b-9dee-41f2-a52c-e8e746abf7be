package io.coinmetrics.api.model

enum class S3BooksMarketType {
    SPOT_BOOKS,
    FUTURES_BOOKS,
    OPTIONS_BOOKS,
    ;

    companion object {
        fun fromMarketType(marketType: String): S3BooksMarketType? =
            when (marketType) {
                "spot" -> SPOT_BOOKS
                "future" -> FUTURES_BOOKS
                "option" -> OPTIONS_BOOKS
                else -> null
            }
    }
}
