package io.coinmetrics.api.statistics.market

import io.coinmetrics.api.model.S3BooksMarketType
import io.coinmetrics.api.utils.CommonUtils
import java.util.EnumMap

interface MarketBooksStatistics {
    fun MarketStatistics.Statistics.merge(other: MarketStatistics.Statistics): MarketStatistics.Statistics =
        MarketStatistics.Statistics(
            minTime = minOf(other.minTime, minTime),
            maxTime = maxOf(other.maxTime, maxTime),
        )

    fun aggregateByBooksDataType(
        map: Map<Int, Map<String, MarketStatistics.Statistics>>,
    ): Map<S3BooksMarketType, MarketStatistics.Statistics> {
        return (map[0] ?: return emptyMap())
            .entries
            .asSequence()
            .mapNotNull { (market, stat) ->
                // filter out non-book markets
                val marketType = CommonUtils.parseMarket(market)?.getType() ?: return@mapNotNull null
                val bookDataType = S3BooksMarketType.fromMarketType(marketType) ?: return@mapNotNull null
                bookDataType to stat
            }.groupingBy { (bookDataType, _) -> bookDataType }
            .aggregateTo(EnumMap(S3BooksMarketType::class.java)) { _, accumulator: MarketStatistics.Statistics?, element, _ ->
                val (_, stat) = element
                accumulator?.merge(stat) ?: stat
            }
    }

    fun getActualBooksStatisticsPerMarketMap(
        booksStatisticsPerMarketMap: Map<Int, Map<String, MarketStatistics.Statistics>>,
        optionTickerStatisticsPerMarketMap: Map<String, MarketStatistics.Statistics>,
    ): Map<Int, Map<String, MarketStatistics.Statistics>> {
        // adding option books with depth=0 (all) and depth=1
        return HashMap<Int, HashMap<String, MarketStatistics.Statistics>>().also {
            booksStatisticsPerMarketMap.entries.forEach { (depth, statisticsMap) ->
                val mutableStatisticsMap = it.computeIfAbsent(depth) { HashMap(statisticsMap) }
                if (depth == 0) {
                    mutableStatisticsMap.putAll(optionTickerStatisticsPerMarketMap)
                }
            }
            it[1] = HashMap(optionTickerStatisticsPerMarketMap)
        }
    }
}
