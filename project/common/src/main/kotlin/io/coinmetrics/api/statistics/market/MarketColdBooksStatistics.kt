package io.coinmetrics.api.statistics.market

import io.coinmetrics.api.model.S3BooksMarketType
import io.coinmetrics.api.statistics.StatisticsDescriptor
import io.coinmetrics.api.statistics.StatisticsRepository

data class MarketColdBooksStatisticsModel(
    /**
     * To read, please use [MarketColdBooksStatistics.tieredS3BooksStatisticsPerMarketMapForCatalogV1] instead.
     */
    val s3BooksStatisticsPerMarketMapForCatalogV1: Map<Int, Map<String, MarketStatistics.Statistics>>,
    val supportedMarkets: List<String>,
) {
    val supportedEntities =
        SupportedMarkets.build {
            addUnparsed(supportedMarkets)
        }
}

class MarketColdBooksStatistics(
    repository: StatisticsRepository,
) : MarketBooksStatistics,
    SupportedMarketStatistics {
    companion object {
        val descriptor = StatisticsDescriptor.create<MarketColdBooksStatisticsModel>("market-cold-books")
    }

    private data class TieredS3BooksStatisticsPerMarketMap(
        val tieredS3BooksStatisticsPerMarketMapForCatalogV1: Map<Int, Map<String, MarketStatistics.Statistics>>,
        val tieredS3BooksStatisticsPerMarketMapForCatalogV2: Map<Int, Map<String, MarketStatistics.Statistics>>,
    )

    // merge Postgres and S3 book statistics
    // TODO: later, replace merging with concatenation keeping in mind each tier time ranges to improve the quality of catalog data (min_time & max_time)
    private val tieredS3BooksStatisticsPerMarketMapAggregation =
        repository.createAggregation {
            val hotBooksStatsItem = getUpstreamItem(MarketHotBooksStatistics.descriptor)
            val coldBooksStatsItem = getUpstreamItem(descriptor)
            return@createAggregation {
                val tieredS3BooksStatisticsPerMarketMapForCatalog =
                    (
                        hotBooksStatsItem.value.booksStatisticsPerMarketMapForCatalogV1.keys +
                            coldBooksStatsItem.value.s3BooksStatisticsPerMarketMapForCatalogV1.keys
                    ).associateWithTo(HashMap()) { depth ->
                        val map1 = hotBooksStatsItem.value.booksStatisticsPerMarketMapForCatalogV1[depth] ?: emptyMap()
                        val map2 = coldBooksStatsItem.value.s3BooksStatisticsPerMarketMapForCatalogV1[depth] ?: emptyMap()
                        map1.mergeWithStatistics(map2)
                    }
                TieredS3BooksStatisticsPerMarketMap(
                    tieredS3BooksStatisticsPerMarketMapForCatalogV1 = tieredS3BooksStatisticsPerMarketMapForCatalog,
                    tieredS3BooksStatisticsPerMarketMapForCatalogV2 = tieredS3BooksStatisticsPerMarketMapForCatalog,
                )
            }
        }

    private val s3BooksStatisticsPerDataTypeAggregation =
        repository.createAggregation {
            val repoItem = getUpstreamItem(descriptor)
            return@createAggregation {
                aggregateByBooksDataType(repoItem.value.s3BooksStatisticsPerMarketMapForCatalogV1)
            }
        }

    private val repoItem = repository.getItem(descriptor)

    override val supportedMarketsHash: Int
        get() = repoItem.valueAndHash.second.hashCode()

    val tieredS3BooksStatisticsPerMarketMapForCatalogV1: Map<Int, Map<String, MarketStatistics.Statistics>>
        get() = tieredS3BooksStatisticsPerMarketMapAggregation().tieredS3BooksStatisticsPerMarketMapForCatalogV1

    val tieredS3BooksStatisticsPerMarketMap: Map<Int, Map<String, MarketStatistics.Statistics>>
        get() = tieredS3BooksStatisticsPerMarketMapAggregation().tieredS3BooksStatisticsPerMarketMapForCatalogV2

    val s3BooksStatisticsPerDataType: Map<S3BooksMarketType, MarketStatistics.Statistics>
        get() = s3BooksStatisticsPerDataTypeAggregation()

    override fun isMarketSupported(market: String) =
        repoItem.value.supportedEntities.markets
            .contains(market)

    override fun isExchangeSupported(exchange: String) =
        repoItem.value.supportedEntities.exchanges
            .contains(exchange)

    override fun isSymbolSupported(symbol: String) =
        repoItem.value.supportedEntities.symbols
            .contains(symbol)

    override fun isBaseSupported(base: String) =
        repoItem.value.supportedEntities.bases
            .contains(base)

    override fun isQuoteSupported(quote: String) =
        repoItem.value.supportedEntities.quotes
            .contains(quote)

    override fun getSupportedMarkets(reverseOrder: Boolean) =
        repoItem.value.supportedEntities
            .getMarkets(reverseOrder)
            .asSequence()

    private fun Map<String, MarketStatistics.Statistics>.mergeWithStatistics(
        vararg others: Map<String, MarketStatistics.Statistics>,
    ): Map<String, MarketStatistics.Statistics> {
        val resultMap = this.toMutableMap()
        others.forEach { other ->
            other.forEach { (key, value) ->
                resultMap.compute(key) { _, currentValue ->
                    currentValue?.merge(value) ?: value
                }
            }
        }
        return resultMap
    }
}
