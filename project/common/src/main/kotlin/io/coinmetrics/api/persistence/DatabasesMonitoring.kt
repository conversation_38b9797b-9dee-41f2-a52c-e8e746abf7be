package io.coinmetrics.api.persistence

import io.prometheus.metrics.core.metrics.GaugeWithCallback
import io.prometheus.metrics.core.metrics.Histogram
import io.prometheus.metrics.model.registry.PrometheusRegistry

class DatabasesMonitoring(
    registry: PrometheusRegistry,
    private val dbQueueLengthCallback: GaugeWithCallback.Callback.() -> Unit,
) {
    val sqlQueryDuration: Histogram =
        Histogram
            .builder()
            .name("sql_query_latency_seconds")
            .help("SQL query latency in seconds.")
            .classicUpperBounds(
                .0002,
                .0005,
                .0007,
                .001,
                .002,
                .003,
                .004,
                .005,
                .01,
                .015,
                0.05,
                0.1,
                0.2,
                0.3,
                0.4,
                0.6,
                0.8,
                1.0,
                2.0,
                4.0,
                6.0,
                8.0,
                10.0,
                15.0,
            ).labelNames("db", "table")
            .register(registry)

    val dbQueueTime: Histogram =
        Histogram
            .builder()
            .name("db_queue_time_seconds")
            .help("Time that query waits for execution. Indicates a problem with API_DB_THREADS.")
            .classicUpperBounds(
                .0002,
                .0005,
                .0007,
                .001,
                .002,
                .003,
                .004,
                .005,
                .01,
                .015,
                0.05,
                0.1,
                0.2,
                0.3,
                0.4,
                0.6,
                0.8,
                1.0,
                2.0,
                4.0,
                6.0,
                8.0,
                10.0,
                15.0,
            ).labelNames("db")
            .register(registry)

    init {
        GaugeWithCallback
            .builder()
            .name("db_queue_length")
            .help("Number of pending sql queries. Indicates a problem with API_DB_THREADS.")
            .labelNames("db", "pool")
            .callback {
                dbQueueLengthCallback(it)
            }.register(registry)
    }
}
