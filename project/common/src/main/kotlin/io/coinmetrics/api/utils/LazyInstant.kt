package io.coinmetrics.api.utils

import com.fasterxml.jackson.annotation.JsonCreator
import com.fasterxml.jackson.annotation.JsonValue
import java.time.Instant

class LazyInstant private constructor(
    private var str: String?,
    private var data: Instant?,
) : Comparable<LazyInstant> {
    companion object {
        private val formatter = TimeUtils.dateTimeFormatter

        fun Instant.toLazyInstant() = LazyInstant(this)

        fun String.toLazyInstant() = LazyInstant(this)
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    private constructor(str: String) : this(str, data = null) {
        require(
            str[0].isDigit() &&
                str[1].isDigit() &&
                str[2].isDigit() &&
                str[3].isDigit() &&
                str[4] == '-' &&
                str[5].isDigit() &&
                str[6].isDigit() &&
                str[7] == '-' &&
                str[8].isDigit() &&
                str[9].isDigit() &&
                str[10] == 'T' &&
                str[11].isDigit() &&
                str[12].isDigit() &&
                str[13] == ':' &&
                str[14].isDigit() &&
                str[15].isDigit() &&
                str[16] == ':' &&
                str[17].isDigit() &&
                str[18].isDigit() &&
                str.last() == 'Z',
        ) {
            "Invalid timestamp: $str"
        }
    }

    private constructor(value: Instant) : this(str = null, value)

    val value: Instant
        get(): Instant {
            return data ?: formatter.parse(str ?: error("INVARIANT"), Instant::from).also {
                data = it
            }
        }

    @JsonValue
    override fun toString(): String =
        str ?: formatter.format(data ?: error("INVARIANT")).also {
            str = it
        }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as LazyInstant

        return value == other.value
    }

    override fun hashCode(): Int = value.hashCode()

    override fun compareTo(other: LazyInstant): Int = value.compareTo(other.value)
}
