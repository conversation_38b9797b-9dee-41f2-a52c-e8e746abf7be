package io.coinmetrics.api.statistics.market

import io.coinmetrics.api.model.S3BooksMarketType
import io.coinmetrics.api.statistics.StatisticsDescriptor
import io.coinmetrics.api.statistics.StatisticsRepository

data class MarketHotBooksStatisticsModel(
    val booksStatisticsPerMarketMapForCatalogV1: Map<Int, Map<String, MarketStatistics.Statistics>>,
    val supportedMarkets: List<String>,
) {
    val supportedEntities =
        SupportedMarkets.build {
            addUnparsed(supportedMarkets)
        }
}

class MarketHotBooksStatistics(
    repository: StatisticsRepository,
) : MarketBooksStatistics,
    SupportedMarketStatistics {
    companion object {
        val descriptor = StatisticsDescriptor.create<MarketHotBooksStatisticsModel>("market-hot-books")
    }

    private val booksStatisticsPerDataTypeAggregation =
        repository.createAggregation {
            val repoItem = getUpstreamItem(descriptor)
            return@createAggregation {
                aggregateByBooksDataType(repoItem.value.booksStatisticsPerMarketMapForCatalogV1)
            }
        }

    private val repoItem = repository.getItem(descriptor)

    override val supportedMarketsHash: Int
        get() = repoItem.valueAndHash.second.hashCode()

    val booksStatisticsPerMarketMapForCatalogV1: Map<Int, Map<String, MarketStatistics.Statistics>>
        get() = repoItem.value.booksStatisticsPerMarketMapForCatalogV1

    val booksStatisticsPerDataType: Map<S3BooksMarketType, MarketStatistics.Statistics>
        get() = booksStatisticsPerDataTypeAggregation()

    override fun isMarketSupported(market: String) =
        repoItem.value.supportedEntities.markets
            .contains(market)

    override fun isExchangeSupported(exchange: String) =
        repoItem.value.supportedEntities.exchanges
            .contains(exchange)

    override fun isSymbolSupported(symbol: String) =
        repoItem.value.supportedEntities.symbols
            .contains(symbol)

    override fun isBaseSupported(base: String) =
        repoItem.value.supportedEntities.bases
            .contains(base)

    override fun isQuoteSupported(quote: String) =
        repoItem.value.supportedEntities.quotes
            .contains(quote)

    override fun getSupportedMarkets(reverseOrder: Boolean) =
        repoItem.value.supportedEntities
            .getMarkets(reverseOrder)
            .asSequence()
}
