package io.coinmetrics.api.statistics.index

import io.coinmetrics.api.model.DataAvailabilityTimeRange
import io.coinmetrics.api.statistics.StatisticsDescriptor
import io.coinmetrics.api.statistics.StatisticsRepository

class IndexStatistics(
    repository: StatisticsRepository,
) {
    companion object {
        val descriptor =
            StatisticsDescriptor.create<Map<String, Map<String, DataAvailabilityTimeRange>>>("index")
    }

    private val repoItem = repository.getItem(descriptor)

    fun getIndexFrequencyStatistics(
        index: String,
        frequency: String,
    ): DataAvailabilityTimeRange? = repoItem.value[index]?.get(frequency)

    fun getIndexes() = repoItem.value.keys
}
