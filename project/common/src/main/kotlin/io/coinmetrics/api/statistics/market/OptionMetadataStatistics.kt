package io.coinmetrics.api.statistics.market

import com.fasterxml.jackson.annotation.JsonFormat
import com.fasterxml.jackson.annotation.JsonProperty
import io.coinmetrics.api.statistics.StatisticsDescriptor
import io.coinmetrics.api.statistics.StatisticsRepository
import java.math.BigDecimal
import java.time.Instant

class OptionMetadataStatistics(
    repository: StatisticsRepository,
) {
    companion object {
        val descriptor =
            StatisticsDescriptor.create<Map<String, OptionMetadata>>("option-metadata")
    }

    private val repoItem = repository.getItem(descriptor)

    fun get(market: String) = repoItem.value[market]
}

data class OptionMetadata(
    override val exchange: String,
    override val symbol: String,
    override val base: String?,
    override val quote: String?,
    override val status: String?,
    @JsonProperty("order_amount_increment")
    override val orderAmountIncrement: String?,
    @JsonProperty("order_amount_min")
    override val orderAmountMin: String?,
    @JsonProperty("order_amount_max")
    override val orderAmountMax: String?,
    @JsonProperty("order_price_increment")
    override val orderPriceIncrement: String?,
    @JsonProperty("order_price_min")
    override val orderPriceMin: String?,
    @JsonProperty("order_price_max")
    override val orderPriceMax: String?,
    @JsonProperty("order_size_min")
    override val orderSizeMin: String?,
    @JsonProperty("order_taker_fee")
    override val orderTakerFee: String?,
    @JsonProperty("order_maker_fee")
    override val orderMakerFee: String?,
    @JsonProperty("margin_trading_enabled")
    override val marginTradingEnabled: Boolean?,
    @JsonProperty("size_asset")
    override val sizeAsset: String?,
    @JsonProperty("margin_asset")
    override val marginAsset: String?,
    override val size: BigDecimal,
    val european: Boolean,
    val strike: BigDecimal,
    @JsonProperty("option_contract_type")
    val optionContractType: String,
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSSSSSSS'Z'", timezone = "UTC")
    override val listing: Instant?,
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSSSSSSS'Z'", timezone = "UTC")
    override val expiration: Instant,
    val settlementPrice: BigDecimal?,
    val minCatalogVersion: Int?,
    @JsonProperty("base_native")
    override val baseNative: String?,
    @JsonProperty("quote_native")
    override val quoteNative: String?,
) : MarketStatistics.DerivativeMarketMetadata {
    override val type = "option"
}
