package io.coinmetrics.api

import io.coinmetrics.api.utils.TimeUtils

val supportedDateFormats =
    (
        TimeUtils.dateOnlyPatterns +
            TimeUtils.dateTimeWithoutSubSecTimePatterns +
            TimeUtils.dateTimeWithMillisTimePatterns +
            TimeUtils.dateTimeWithMicrosTimePatterns +
            TimeUtils.dateTimeWithNanosTimePatterns
    ).joinToString(separator = ", ") {
        "'${it.replace("'T'", "T")}'"
    }

fun badTimeFormatMessage(value: String) = "Incorrect time format '$value'. Supported formats are $supportedDateFormats."

fun startTimeLessThanEndMessage(
    startTime: String,
    endTime: String,
) = "Start time '$startTime' is later than the end time '$endTime'."

fun badMarketIdMessage(marketId: String) = "Incorrect market '$marketId'."

fun badNextPageToken(): ApiError.BadParameter = ApiError.BadParameter("next_page_token")

sealed class ApiError(
    val status: Int,
    val type: String,
    val message: String,
) {
    object NotFound : ApiError(404, "not_found", "Not found.")

    class NotFoundWithMessage(
        message: String,
    ) : ApiError(404, "not_found", message)

    object OperationFailed : ApiError(500, "operation_failed", "Operation failed.")

    class MissingParameter(
        name: String,
    ) : ApiError(400, "missing_parameter", "Missing parameter '$name'.")

    class BadParameter(
        val name: String,
        message: String? = null,
    ) : ApiError(
            400,
            "bad_parameter",
            "Bad parameter '$name'." + if (message != null) " $message" else "",
        )

    class BadParameters(
        message: String? = null,
    ) : ApiError(400, "bad_parameters", message ?: "")

    class UnsupportedParameterValue(
        val name: String,
        value: Any,
    ) : ApiError(
            400,
            "bad_parameter",
            "Bad parameter '$name'. Value '$value' is not supported.",
        )

    class UnsupportedParameterValueWithSupportedInfo(
        val name: String,
        value: Any,
        supportedValues: Collection<Any>,
    ) : ApiError(
            400,
            "bad_parameter",
            "Bad parameter '$name'. Value '$value' is not supported. Supported values are ${supportedValues.joinToString(
                ", ",
            ) { "'$it'" }}.",
        )

    class UnsupportedParameter(
        name: String,
    ) : ApiError(400, "unsupported_parameter", "Unsupported parameter '$name'.")

    object MissingApiKey : ApiError(401, "unauthorized", "Requested resource requires authorization.")

    object WrongApiKey : ApiError(401, "wrong_credentials", "Supplied credentials are not valid.")

    object Forbidden : ApiError(403, "forbidden", "Requested resource is not available with supplied credentials.")

    class ForbiddenWithMessage(
        message: String,
    ) : ApiError(403, "forbidden", message)

    object TooManyRequests : ApiError(
        429,
        "too_many_requests",
        "There have been too many calls to the API or endpoint. Wait a bit and try again. " +
            "For more info, please refer to 'X-RateLimit-*' response headers and API documentation: https://docs.coinmetrics.io/api.",
    )

    object TooManyStreamConnections : ApiError(
        429,
        "too_many_requests",
        "There are too many websocket connections open for this API Key. " +
            "For more information on API rate limiting please see: https://docs.coinmetrics.io/api/v4/#tag/Rate-limits.",
    )

    object RequestTooLarge : ApiError(413, "request_too_large", "Request is too large.")
}
