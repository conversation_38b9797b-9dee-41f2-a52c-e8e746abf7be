---
# Source: api4-ingress/templates/secret-origin-certificate.yaml
apiVersion: v1
kind: Secret
type: kubernetes.io/tls
metadata:
  name: release-name-api4-ingress-cloudflare
  labels:
    helm.sh/chart: api4-ingress-0.1.0
    app.kubernetes.io/name: api4-ingress
    app.kubernetes.io/instance: release-name
    app.kubernetes.io/version: "0.1.0"
    app.kubernetes.io/managed-by: Helm
data:
  tls.key: RU5DW0FFUzI1Nl9HQ00sZGF0YTpRRW5HYml2a1AxTk91YUZ3YTFXWWo0RTZodzF6Ri9xMmVMVHRJYkg4ZldaRnNVTDhTV2t6L3dxcTdnb2VMUGY3ZG1WSEg0NjJmbC85Tkd3blFqUks4VGcxTlN3a280OHZWNGJVZE1MS05aWkoxNktmWkNxZ3g0RXR6MldmckJrN3Mwa1YvYmd2NUxUcHhRR2hpZ0pOcnhBYVBnR1BKU2lXckVOY0lGcjYzcHFHVUpQZXFadGhBL3liUkhwY1Z3MXM0c0IxdDVBcXBzQXh2SC9VTjdkdEV2Q0ZaZEYyS2hNcHRYQmNPdVlxNXBqKzJyUkVsaittZ2Y5Si8xSmQzeDlkNVJDRTM5NWNiSWp3SkVJbFJLeGNmOERaZStyTXpPc2hIQXpVMFFGTkUycmpUZXdBZGp1SUwyUUc3aHdzbEpyelZnNTdoYWJUaStDVlFiLzZGNWVodE45aGc1b1FFbk9GUXVsWUkrS044aVR0Tm1OMkFFUGVCYjB6VXd4VDRCUWtNYlJXc1BKQ3Q1bmoyUzRQdW1XYTlnbUlhRldsL0NqVGNIblQ4djJyQlEwN1dhOUJFN2N0SG02QkIzUml5VXAzVHIwOVJ3MTMrMGVRYlk4MmtXQ0pNaVg4eno0QlkzWlgwUjJMNy9GcDdkR2lvWjhnYmpha0pncS8vWW9aZy9kM28rRmdlWWhObXJBWkIvNTZQS0hZNzlaMUZkRTVtZXJBQ1p0eHdVMWxwS3lpNkJDUytVMDFOUFFJM1Q2NXA4aEVUSnZ5d1hoSlBpbnlOVi92Sy8zd2QwZzBQd1laK3FKam8zcUJjWkJFS3piSnVQS1BFL0dSVmtuRm9YdjVHY2RpK2hTMHJZNkZlNW5OUmdTditHREl3UVFtYzVjWktSMmc0RWNkNFcxdjgrYU1TcWJZdjRWblJNajlTa1JiUWdKaXBxTG12ZVFOeDlmb2F4Mjk2c29pbGJpVG04TXZibFZNZ0tKZ1lsc1FHNkZ3Q0dLTk1uTFN2UmtNZ1ltZWVFUFZSWm4wS3hleG5zM01FT09UY1lUVDE1LzFPQkxzcDVoT21yLzNQaWt6d0JibEczd2tPS1BjNWZNY3huVTBPNVZTbWt6U0RzQ2UvbUVPOTdrQktUK0xjRlR6UHZTelVUVzJkcDJuYmNVK1hWY2ZnTHJBOElMcDRLUUw4ZlUxN3dPeEsyNGhaSjB6a0JYMWdKM3lsT0VLMWNEYmhoN1pMdjhGalVmUWlVbkRjN3R6QzNiNmFNUTkyMEJWOU5FSUovL1czWHJQNFVxUGVIODc1NjA5eXlSQm1VclJLRlM4TkhpSGk1SksrK0NnSmNUR3FQRjl6Qi9yd1RKcm5lZHR1SHJ5VlluWVJUc3puaUJHVFNkYXpXaitQbXBZaEJnaHFsckFMNlpTRDdxNzlIVFRDK2xkbTliQ1R2SE9HMlRnT3pFbEt1aGtabjNWU2tTQTRzVUExVUhCWTZaRExubXRsbEdsNXpaZnQ2cEtST2hGZHQ4UG1TY0ZVTHRMRGNydWpEUmtzQ2I4R1dIbkN3Y2Nxcnd2VXo0c0VEcmJmbk51amF4aEMxN0ZnMTNKZ3NMYUI1YmV4STJmRkRvaHVtN0xOd00zMVdNV2ppdUtwRTBCYzM3OEFzcXZ3ZDRlazZiMWNvSzRJWXF5a2JhQkVhTFh4Sy9DUGhBNCtnOGZWQmhXdjNSOWxnMmJLc0NZMGllZzh0S3JmVVN3R0VvVGVmdUVsMGtmR01nR1YrLyt3SUtVZFdNcjBFSm5UMll5VENkcFprM0h2M1ZQVlZzTVNaYjBiK3p1MHFwRnN0R1RyWFRqNjV6QkFybnZNMDRod2FaaGliOEhNWGdNVkZHTkpBMXNSUFgxY3JNNTVRNzcxcWhma2RWd2paZm5vMEw3dlBFUXNtZ0RGU3hjT2pGSFhOK1Uwc20zZjFqOTJOVDBjdmhmcUlPOHVwdFVhM2dlTHJZRS90ODViSGFnK3gzSkNoQmhxZUVydUxaaityVWFBY3ZDU1Nab2xVclJzVnZZYkdoRjNjMG84R1hpRG1kaWJXVFBNZEp6RGdSQlByMlFyUDFJaER3WXJXY05zdGF1MHlpcWF3UzNYRlBUeFJuN0hBZEx3R1daNk1qd2pIY1BucG5sUVlWejFHMzlFZGVDcllyY0tRTEUwNDFCai9qR0NvOWJUOC9VSEJkZEIzYTBES2JKZCs3TVczd1VRU3hxaS9xd1VLQzhUNFJCazR2Rkx0MUlOVHVMVUdMYTd2VENVVG9GSXVkbW9GaXJad3h1aHVNVHlOM2dSOHlWRllBV1NFellkMC9DSTY2MEplNGxvRGtoOXNUNDBOamoxMlA5TlZOd2pJL0k4aTExVkt6V3JpcmJvVjJUSE1EbzhRei93b1Q5UUExYkNkVlpnSU0rVy9XY0dwTmFwMTN0U3NSNzhzaWN3MmVqMGpwelp2dTVGWERSaGJuZk1aMW91NzFVbHhkQTJWRjFHcG1Qc1VkK0diODdQdkljY01CbkVyUnhPT1BQS1plUW9DNGRETVJqbktZRFRUUmduNlY3clJxSEVCL2Nld3pQWkNueHRXdkJRdEVpUVFFeDlaMDlXZE5ISnVHR0NXU3hiTTk0blZWOTN5K0pUeXpTcW0yK2pKNmNldGVhNzZra1dhM2I5bHlmUWJWTzYwZkUvSnpVckFxQXdGeEdWUys1TVZDU1J0THVHU3hGL0hNMUxuc3JsR2RQYmJTYjV5V2NHU3lEQVQ2SU8yTjM5M054SjZUOTV3eUJNU1BiTWh6NUhzMExqMjdNTDVqNUhPaFRaVmRsZkNWMEVON2ZpcldFY2UwZldNblJENEdGUTI1dWVIWkFlMVpSZVp1UlQxRUpoK292enZ5aC8wRGREVTB2bEpuaXI1eDhoMTdlMms0TW5XaXpIQmxkK082eW5mcGVwZEJkWjl5RWp4czZxdzNZQjBVY0dKYUl1OE9NcWJYc2YzL0RSYjJtdmhFOThzTTBZRi9wdkhFdW81UndTaW5LLGl2OmFnWHpvTWs4Qk5Qa3ZtUm5KdytwOC9IbVNuRFJjTTJOMjBHak5mM0NDNHc9LHRhZzpNdk9VdVdIbTh2U3lDVnFmL21PUVdnPT0sdHlwZTpzdHJd
  tls.crt: RU5DW0FFUzI1Nl9HQ00sZGF0YTppLzdSdlJQUTFZSFVYQ1M5eXJxY200UEtCTmtBTjg4V2x3aUR5VjlxYjUrVUhEYXp0ZDhoRG8vMnZETDJOaGh3akJEeTNaYWRVUXZrRGxoTU1PbUlCU0lhVHFoMmQycHBFTDdQYzVPKzJOOWdFVXZOMG56M21YRVgyU1NTM2JhanBDM1dzNWJ5dWdURnBJM1B6UzZsU2xOT001OGcxT2Y3SmxtK0lEWEJkbkxQemJxZzRkeEl5b3IzUjE0RDgzYWhjUGRRcUJsOXdKdjNkTkpTYjVlRHhJUTdkUmtLMjRIeXZ6NFZuQ2hrbVhFaDVmODQrMTBEc2FvWEszQ0lLc2dyMmNiOE5mQWVQSVErTDhBcmJRdlh0cmFIdXliTDFhVU5PQU54NlRzS0xYVVZEdjJsdkJnTXA2d0FWNkxLZHdIVEliZEt4cWhoQ0g5cHlXam05ai9USzJmOW9DR05rUnl6T3VHV2RBUW5BTy9QbmJzZmkxaXR0dUNXcU9pV1ZXRTFVRkRQdnVLODNDU2h3emdNdUVEdTJHdDdBUTZsYmpONUpGRUZGZzdlZ2NiUnRSQTJmdXlwbGZTcmMvSzZBbVNMUWFsWi9EaU1ieVFkM0ttSWJoZDRRL2NIMkpGcVlBeEJRM3V6L2ZGMFp2MWJrRHFWUG9qeURPbkhqT2FPZ0JsWFlxUnA2ZEl5d1BsN3AzZGZ6SzBMZ2tZWWd5eFdRbFJwby9VVkV5d2FXVnJFZlZ6dVBUL1haYlY1K0Y5Z3AzMkEyUGxLNUdNMTUrVU5WeTlSVm54N29FcEZ2M3Njd1JGSnF4NkEyV1hWUDBCWm43WXpPN0VpVzhvMGVDaEcxYTF4RkdabnBuOHp1YW1VLzlvQ1VQNEZ3NEdSYzBCL25jT0lFSHhVT05FQ3J4eUpjNm1lU1NMekJPZHBuYjBFYStsR2p3ekpOVlppMkZqd0pGSTdNQjVVcTlnM3poR0VHSWRjMitRNHlvc2VVNVp6UGdReDE4RFhEcEpvZWMvSXlSYzFoNFgycjNERmtSWEFLSy9pQ1RTODQ1dWVTcllIcDBTRFBYQk5HYUN6ajZMbjB2ZGZ6ZHhzVzV5RzFCMXZyM0IzVDZ0TmRIcFNWcGNqL2E1bmNYa2VYWHk0Mm9HOXJ6a1EreUhnT292SlNKVEJ0YlFRT2gvY0h5SXVqK3VKeGFNNXV5WHNYUzNPQlI5UzlRUVNid2dmNHVLT0s5eDFoQnEybXZRajRkQVJmOHpYK3JJbERGUnAvTEhVUXdFZGJHTm9VcWJSd0lqN1VvL2Z5cVE3SE8vVUUxeTF1MTAzUlRZM3VJK2Q1WTV3dHI2T2Q3ak95Tk9PeE4rd0t6Kys0dVVmNy9Mby9CbkJDRC80RGVIdEtMMWIxWE05bDU4RGZGaEN3aUFhcnBQeC9QdnYzSUk0UXVOdjBLUjFERzhTNTJnZVJ4ZW96Q3cxNG1hYkhQYjNUSTJaM2hvd2VvcmhTK3ZycHBGbHhqOWpYZ0tWYUhXeEZ4TU9IZE5oQ3AzaUZ0VGVpQXhQaFRFZEdraTZxK0FKYWc4YnVhWkdaQkhibE43dE1hK1ZTVWdWV3ptenllSWZBZEVKZldWaGduczRlVkJEeVlEVVBvK0xvOGxFZmhOTEdWOUI0a0dQc0s5Mnl5aDg4Y2NHb2lRS3hWaHBrMjZwNHBuRUtUUWE2a3FFLzA0OE41aTNwVmVWM1ppa2lBSzhnQ0krS2M2OXlwbGU5MmFLOG1rQTdTNmNUSDJ0WmovdnpMVkhXU2N5ZDRZU2xFd3pOQ0hVODg4Q29Bc1Mxd1k1dldDWWRUM3lmZ1YzZThXdytIa2htNkp2Rk1lazNpdXk2Y2I1U0wxTnNqNzYzclZPR0FreURNUU9rZlUwMnJEaUN6Ulg3cTVZbFVsd1pKa3VUNE5Lcjg0NjZJTTFJZFp5WTBleEsweFhSaTYxc08rUnY2cDlGWnYyZnJ1QkM5OEZEejB6b3FxcktLY2hYNUhTYzIySFg4WGVEMlNtVHdGRHY0clN1SHZGd3grdVNLU2Q4L0pESFhnTkQwQ0lyaFFnRnBvZlJWRlUwMnp0aDhxMk9XRWQ2MjNudm5iN3hKNUFxZGVpT1M1SUVHNHJsdmJoRDA0dWFyNU5ZWFR3d0lDR05Kbk1OcDUzWWYrN3lMb2JyZmZpMDdKdVJxbGFUVVplTjlZYjBwSC9yZEw5WXFXYW0wVTI5a0xKN0QrcnIrYTRJSWlUdThaU2tSYVJha0JOblQvdkRld2dQSUcreVVZMkNXREQ3dmFGY1c1bHRqTTRKSE5GZ1k2ZjBkMTYwQjZaUjlibVRJUnAwRlBzUjV6anZLTVlPUnN4TVdiZDZ2VnlGNExJQ1JYbTQ5THFjWG4rZU9Vd3dHQTJvcEZ4VGZEUDg0Zk9OZnZwRjNQRFpwY0V5Zjh4dGtqRzEybnNNMTBheGk0blFzM3djTkplVmlPRkR0YWtPVWZJOC9JSGpYVERNdzhlSjhjbzZYWHcralhabzJielB5Zm4vZHpJTW5Zd2JIdC82TmhpVU5ZdXJsa2dnNFBITlE2TFk0YmwvZVNScFFpN0hTUWtZWUViZFUwVXNHa2g5OXdFblBpazh3RWVhMnV3TXhab2crWHZncitxWXhLTW9UR2tGcHFkbmJkYkY4SjB6U0Jzbit0dTlGaVhQNVpuMEJlR2UzOHV1QmRTVGY3eHFvMjRVNVBEU3l4OHd2M1orRU1Qb0NQV0hYS2N4MDhQNUtDTk43eFU5TXBFOER3UWRiQkJSSVBLVmhqV096dVYrUnpWNU0rL01ZQ3c3WWxhTVE2NzdHRW4yVng3TkJxWm1HVDdZcEtWaWc3Q3FQM1RQcUpPNVY3TGZtN3hYMFlXNU54d0J1QTB5RC9vRGVhMCtYemIwL1RYWnhJcU5MTW9qQjVieFJkNnZXc3NhbGFGOFpzQXRNdG96bDhQRGNEcHBCR1ArS3hQTzA1MzFQeXd6S09qcnZ6cjVjUUZjZlVkMkZiM0ZzYnVsVGhtLGl2OkVEOHdodGt0VC9ZNVNBMSt0Vy9HaFM2OVc5WFZvSHBsVXRlcy9IZHgybTA9LHRhZzo0QytXVFVpTk0wdEpSaGdFck96QVJBPT0sdHlwZTpzdHJd
---
# Source: api4-ingress/templates/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: release-name-api4-ingress
  labels:
    helm.sh/chart: api4-ingress-0.1.0
    app.kubernetes.io/name: api4-ingress
    app.kubernetes.io/instance: release-name
    app.kubernetes.io/version: "0.1.0"
    app.kubernetes.io/managed-by: Helm
spec:
  ipFamilyPolicy: SingleStack
  ipFamilies:
    - IPv6
  ports:
    - name: http
      port: 8080
      protocol: TCP
      targetPort: 8080
  selector:
    api4.coinmetrics.io/service-level: pro
    app.kubernetes.io/name: api4
---
# Source: api4-ingress/templates/virtual-server-api.yaml
apiVersion: k8s.nginx.org/v1
kind: VirtualServer
metadata:
  name: release-name-api4-ingress-api-${LOCATION}-coinmetrics-io
  labels:
    helm.sh/chart: api4-ingress-0.1.0
    app.kubernetes.io/name: api4-ingress
    app.kubernetes.io/instance: release-name
    app.kubernetes.io/version: "0.1.0"
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: api4
spec:
  host: "api-${LOCATION}.coinmetrics.io"
  ingressClassName: "nginx-production"
  server-snippets: |
    client_header_buffer_size 64k;
    large_client_header_buffers 4 64k;
  routes:
    - path: /fidelity
      route: ${FIDELITY_NAMESPACE}/fidelity-api1-ingress-api-${LOCATION}-coinmetrics-io
    - path: /v4/timeseries-stream/market-orderbooks
      route: streaming-books-api-ingress-api-${LOCATION}-coinmetrics-io
    - path: /v4/timeseries-stream/market-trades
      route: streaming-trades-api-ingress-api-${LOCATION}-coinmetrics-io
    - path: /v4/status
      action:
        pass: api4-status
      location-snippets: |
        expires -1;
    - action:
        proxy:
          requestHeaders:
            set:
              - name: Host
                value: ${http_host}
          upstream: api4
      location-snippets: |
        gzip on;
        gzip_min_length 1100;
        gzip_comp_level 6;
        gzip_types text/plain application/json text/csv application/x-ndjson;
        gzip_disable "msie6";
        gzip_vary on;
        expires -1;
      path: /v4/
    - action:
        proxy:
          requestHeaders:
            set:
              - name: Host
                value: ${http_host}
          upstream: api4-stream
      location-snippets: |
        expires -1;
      path: /v4/timeseries-stream/
    - action:
        return:
          body: The legacy API is deprecated. Please, migrate to the API v4 (https://docs.coinmetrics.io/api/v4).
          code: 200
          type: text/plain
      path: /v1/
    - action:
        return:
          body: The legacy API is deprecated. Please, migrate to the API v4 (https://docs.coinmetrics.io/api/v4).
          code: 200
          type: text/plain
      path: /v2/
    - action:
        return:
          body: The legacy API is deprecated. Please, migrate to the API v4 (https://docs.coinmetrics.io/api/v4).
          code: 200
          type: text/plain
      path: /v3/
    - action:
        return:
          body: Not found
          code: 404
          type: text/plain
      path: /v4/internal/
    - action:
        return:
          body: Not found
          code: 404
          type: text/plain
      path: /v4/metrics
    - action:
        return:
          body: " "
          code: 204
          type: text/plain
      path: /feed-handlers-proxy-check
    - action:
        return:
          body: Coin Metrics API.
          code: 200
          type: text/plain
      path: /
  tls:
    redirect:
      enable: true
    secret: release-name-api4-ingress-cloudflare
  upstreams:
    - name: api4
      keepalive: 16
      max-fails: 0
      fail-timeout: "0"
      next-upstream: error timeout invalid_header http_500 http_502 http_503 http_504
      next-upstream-tries: 3
      port: 8080
      service: release-name-api4-ingress
    - name: api4-status
      keepalive: 16
      max-fails: 0
      fail-timeout: "0"
      next-upstream: error timeout invalid_header http_500 http_502 http_503 http_504
      next-upstream-tries: 3
      port: 8080
      service: release-name-api4-ingress
      # Cloudflare health check's timeout is 5 seconds, but health checks don't fail if our backend hangs (CF's bug).
      # So we return a 504 error in this case and force Cloudflare health checks to fail.
      read-timeout: 4s
    - name: api4-stream
      keepalive: 16
      fail-timeout: "0"
      max-fails: 0
      buffering: false
      next-upstream: error timeout invalid_header http_500 http_502 http_503 http_504
      next-upstream-tries: 3
      port: 8080
      read-timeout: 15m
      service: release-name-api4-ingress
---
# Source: api4-ingress/templates/virtual-server-api.yaml
apiVersion: k8s.nginx.org/v1
kind: VirtualServer
metadata:
  name: release-name-api4-ingress-api-coinmetrics-io
  labels:
    helm.sh/chart: api4-ingress-0.1.0
    app.kubernetes.io/name: api4-ingress
    app.kubernetes.io/instance: release-name
    app.kubernetes.io/version: "0.1.0"
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: api4
spec:
  host: "api.coinmetrics.io"
  ingressClassName: "nginx-production"
  server-snippets: |
    client_header_buffer_size 64k;
    large_client_header_buffers 4 64k;
  routes:
    - path: /fidelity
      route: ${FIDELITY_NAMESPACE}/fidelity-api1-ingress-api-coinmetrics-io
    - path: /v4/timeseries-stream/market-orderbooks
      route: streaming-books-api-ingress-api-coinmetrics-io
    - path: /v4/timeseries-stream/market-trades
      route: streaming-trades-api-ingress-api-coinmetrics-io
    - path: /v4/status
      action:
        pass: api4-status
      location-snippets: |
        expires -1;
    - action:
        proxy:
          requestHeaders:
            set:
              - name: Host
                value: ${http_host}
          upstream: api4
      location-snippets: |
        gzip on;
        gzip_min_length 1100;
        gzip_comp_level 6;
        gzip_types text/plain application/json text/csv application/x-ndjson;
        gzip_disable "msie6";
        gzip_vary on;
        expires -1;
      path: /v4/
    - action:
        proxy:
          requestHeaders:
            set:
              - name: Host
                value: ${http_host}
          upstream: api4-stream
      location-snippets: |
        expires -1;
      path: /v4/timeseries-stream/
    - action:
        return:
          body: The legacy API is deprecated. Please, migrate to the API v4 (https://docs.coinmetrics.io/api/v4).
          code: 200
          type: text/plain
      path: /v1/
    - action:
        return:
          body: The legacy API is deprecated. Please, migrate to the API v4 (https://docs.coinmetrics.io/api/v4).
          code: 200
          type: text/plain
      path: /v2/
    - action:
        return:
          body: The legacy API is deprecated. Please, migrate to the API v4 (https://docs.coinmetrics.io/api/v4).
          code: 200
          type: text/plain
      path: /v3/
    - action:
        return:
          body: Not found
          code: 404
          type: text/plain
      path: /v4/internal/
    - action:
        return:
          body: Not found
          code: 404
          type: text/plain
      path: /v4/metrics
    - action:
        return:
          body: " "
          code: 204
          type: text/plain
      path: /feed-handlers-proxy-check
    - action:
        return:
          body: Coin Metrics API.
          code: 200
          type: text/plain
      path: /
  tls:
    redirect:
      enable: true
    secret: release-name-api4-ingress-cloudflare
  upstreams:
    - name: api4
      keepalive: 16
      max-fails: 0
      fail-timeout: "0"
      next-upstream: error timeout invalid_header http_500 http_502 http_503 http_504
      next-upstream-tries: 3
      port: 8080
      service: release-name-api4-ingress
    - name: api4-status
      keepalive: 16
      max-fails: 0
      fail-timeout: "0"
      next-upstream: error timeout invalid_header http_500 http_502 http_503 http_504
      next-upstream-tries: 3
      port: 8080
      service: release-name-api4-ingress
      # Cloudflare health check's timeout is 5 seconds, but health checks don't fail if our backend hangs (CF's bug).
      # So we return a 504 error in this case and force Cloudflare health checks to fail.
      read-timeout: 4s
    - name: api4-stream
      keepalive: 16
      fail-timeout: "0"
      max-fails: 0
      buffering: false
      next-upstream: error timeout invalid_header http_500 http_502 http_503 http_504
      next-upstream-tries: 3
      port: 8080
      read-timeout: 15m
      service: release-name-api4-ingress
---
# Source: api4-ingress/templates/virtual-server-api.yaml
apiVersion: k8s.nginx.org/v1
kind: VirtualServer
metadata:
  name: release-name-api4-ingress-api-${LOCATION}-coinmetrics2-io
  labels:
    helm.sh/chart: api4-ingress-0.1.0
    app.kubernetes.io/name: api4-ingress
    app.kubernetes.io/instance: release-name
    app.kubernetes.io/version: "0.1.0"
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: api4
spec:
  host: "api-${LOCATION}.coinmetrics2.io"
  ingressClassName: "nginx-production"
  server-snippets: |
    client_header_buffer_size 64k;
    large_client_header_buffers 4 64k;
  routes:
    - path: /fidelity
      route: ${FIDELITY_NAMESPACE}/fidelity-api1-ingress-api-${LOCATION}-coinmetrics2-io
    - path: /v4/timeseries-stream/market-orderbooks
      route: streaming-books-api-ingress-api-${LOCATION}-coinmetrics2-io
    - path: /v4/timeseries-stream/market-trades
      route: streaming-trades-api-ingress-api-${LOCATION}-coinmetrics2-io
    - path: /v4/status
      action:
        pass: api4-status
      location-snippets: |
        expires -1;
    - action:
        proxy:
          requestHeaders:
            set:
              - name: Host
                value: ${http_host}
          upstream: api4
      location-snippets: |
        gzip on;
        gzip_min_length 1100;
        gzip_comp_level 6;
        gzip_types text/plain application/json text/csv application/x-ndjson;
        gzip_disable "msie6";
        gzip_vary on;
        expires -1;
      path: /v4/
    - action:
        proxy:
          requestHeaders:
            set:
              - name: Host
                value: ${http_host}
          upstream: api4-stream
      location-snippets: |
        expires -1;
      path: /v4/timeseries-stream/
    - action:
        return:
          body: The legacy API is deprecated. Please, migrate to the API v4 (https://docs.coinmetrics.io/api/v4).
          code: 200
          type: text/plain
      path: /v1/
    - action:
        return:
          body: The legacy API is deprecated. Please, migrate to the API v4 (https://docs.coinmetrics.io/api/v4).
          code: 200
          type: text/plain
      path: /v2/
    - action:
        return:
          body: The legacy API is deprecated. Please, migrate to the API v4 (https://docs.coinmetrics.io/api/v4).
          code: 200
          type: text/plain
      path: /v3/
    - action:
        return:
          body: Not found
          code: 404
          type: text/plain
      path: /v4/internal/
    - action:
        return:
          body: Not found
          code: 404
          type: text/plain
      path: /v4/metrics
    - action:
        return:
          body: " "
          code: 204
          type: text/plain
      path: /feed-handlers-proxy-check
    - action:
        return:
          body: Coin Metrics API.
          code: 200
          type: text/plain
      path: /
  tls:
    redirect:
      enable: true
    secret: release-name-api4-ingress-cloudflare
  upstreams:
    - name: api4
      keepalive: 16
      max-fails: 0
      fail-timeout: "0"
      next-upstream: error timeout invalid_header http_500 http_502 http_503 http_504
      next-upstream-tries: 3
      port: 8080
      service: release-name-api4-ingress
    - name: api4-status
      keepalive: 16
      max-fails: 0
      fail-timeout: "0"
      next-upstream: error timeout invalid_header http_500 http_502 http_503 http_504
      next-upstream-tries: 3
      port: 8080
      service: release-name-api4-ingress
      # Cloudflare health check's timeout is 5 seconds, but health checks don't fail if our backend hangs (CF's bug).
      # So we return a 504 error in this case and force Cloudflare health checks to fail.
      read-timeout: 4s
    - name: api4-stream
      keepalive: 16
      fail-timeout: "0"
      max-fails: 0
      buffering: false
      next-upstream: error timeout invalid_header http_500 http_502 http_503 http_504
      next-upstream-tries: 3
      port: 8080
      read-timeout: 15m
      service: release-name-api4-ingress
---
# Source: api4-ingress/templates/virtual-server-api.yaml
apiVersion: k8s.nginx.org/v1
kind: VirtualServer
metadata:
  name: release-name-api4-ingress-api-coinmetrics2-io
  labels:
    helm.sh/chart: api4-ingress-0.1.0
    app.kubernetes.io/name: api4-ingress
    app.kubernetes.io/instance: release-name
    app.kubernetes.io/version: "0.1.0"
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: api4
spec:
  host: "api.coinmetrics2.io"
  ingressClassName: "nginx-production"
  server-snippets: |
    client_header_buffer_size 64k;
    large_client_header_buffers 4 64k;
  routes:
    - path: /fidelity
      route: ${FIDELITY_NAMESPACE}/fidelity-api1-ingress-api-coinmetrics2-io
    - path: /v4/timeseries-stream/market-orderbooks
      route: streaming-books-api-ingress-api-coinmetrics2-io
    - path: /v4/timeseries-stream/market-trades
      route: streaming-trades-api-ingress-api-coinmetrics2-io
    - path: /v4/status
      action:
        pass: api4-status
      location-snippets: |
        expires -1;
    - action:
        proxy:
          requestHeaders:
            set:
              - name: Host
                value: ${http_host}
          upstream: api4
      location-snippets: |
        gzip on;
        gzip_min_length 1100;
        gzip_comp_level 6;
        gzip_types text/plain application/json text/csv application/x-ndjson;
        gzip_disable "msie6";
        gzip_vary on;
        expires -1;
      path: /v4/
    - action:
        proxy:
          requestHeaders:
            set:
              - name: Host
                value: ${http_host}
          upstream: api4-stream
      location-snippets: |
        expires -1;
      path: /v4/timeseries-stream/
    - action:
        return:
          body: The legacy API is deprecated. Please, migrate to the API v4 (https://docs.coinmetrics.io/api/v4).
          code: 200
          type: text/plain
      path: /v1/
    - action:
        return:
          body: The legacy API is deprecated. Please, migrate to the API v4 (https://docs.coinmetrics.io/api/v4).
          code: 200
          type: text/plain
      path: /v2/
    - action:
        return:
          body: The legacy API is deprecated. Please, migrate to the API v4 (https://docs.coinmetrics.io/api/v4).
          code: 200
          type: text/plain
      path: /v3/
    - action:
        return:
          body: Not found
          code: 404
          type: text/plain
      path: /v4/internal/
    - action:
        return:
          body: Not found
          code: 404
          type: text/plain
      path: /v4/metrics
    - action:
        return:
          body: " "
          code: 204
          type: text/plain
      path: /feed-handlers-proxy-check
    - action:
        return:
          body: Coin Metrics API.
          code: 200
          type: text/plain
      path: /
  tls:
    redirect:
      enable: true
    secret: release-name-api4-ingress-cloudflare
  upstreams:
    - name: api4
      keepalive: 16
      max-fails: 0
      fail-timeout: "0"
      next-upstream: error timeout invalid_header http_500 http_502 http_503 http_504
      next-upstream-tries: 3
      port: 8080
      service: release-name-api4-ingress
    - name: api4-status
      keepalive: 16
      max-fails: 0
      fail-timeout: "0"
      next-upstream: error timeout invalid_header http_500 http_502 http_503 http_504
      next-upstream-tries: 3
      port: 8080
      service: release-name-api4-ingress
      # Cloudflare health check's timeout is 5 seconds, but health checks don't fail if our backend hangs (CF's bug).
      # So we return a 504 error in this case and force Cloudflare health checks to fail.
      read-timeout: 4s
    - name: api4-stream
      keepalive: 16
      fail-timeout: "0"
      max-fails: 0
      buffering: false
      next-upstream: error timeout invalid_header http_500 http_502 http_503 http_504
      next-upstream-tries: 3
      port: 8080
      read-timeout: 15m
      service: release-name-api4-ingress
---
# Source: api4-ingress/templates/virtual-server-api.yaml
apiVersion: k8s.nginx.org/v1
kind: VirtualServer
metadata:
  name: release-name-api4-ingress-api-uat-${LOCATION}-coinmetrics2-io
  labels:
    helm.sh/chart: api4-ingress-0.1.0
    app.kubernetes.io/name: api4-ingress
    app.kubernetes.io/instance: release-name
    app.kubernetes.io/version: "0.1.0"
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: api4
spec:
  host: "api-uat-${LOCATION}.coinmetrics2.io"
  ingressClassName: "nginx-production"
  server-snippets: |
    client_header_buffer_size 64k;
    large_client_header_buffers 4 64k;
  routes:
    - path: /fidelity
      route: fidelity-api1-uat/fidelity-api1-ingress-api-uat-${LOCATION}-coinmetrics2-io
    - path: /v4/timeseries-stream/market-orderbooks
      route: streaming-books-api-ingress-api-uat-${LOCATION}-coinmetrics2-io
    - path: /v4/timeseries-stream/market-trades
      route: streaming-trades-api-ingress-api-uat-${LOCATION}-coinmetrics2-io
    - path: /v4/status
      action:
        pass: api4-status
      location-snippets: |
        expires -1;
    - action:
        proxy:
          requestHeaders:
            set:
              - name: Host
                value: ${http_host}
          upstream: api4
      location-snippets: |
        gzip on;
        gzip_min_length 1100;
        gzip_comp_level 6;
        gzip_types text/plain application/json text/csv application/x-ndjson;
        gzip_disable "msie6";
        gzip_vary on;
        expires -1;
      path: /v4/
    - action:
        proxy:
          requestHeaders:
            set:
              - name: Host
                value: ${http_host}
          upstream: api4-stream
      location-snippets: |
        expires -1;
      path: /v4/timeseries-stream/
    - action:
        return:
          body: The legacy API is deprecated. Please, migrate to the API v4 (https://docs.coinmetrics.io/api/v4).
          code: 200
          type: text/plain
      path: /v1/
    - action:
        return:
          body: The legacy API is deprecated. Please, migrate to the API v4 (https://docs.coinmetrics.io/api/v4).
          code: 200
          type: text/plain
      path: /v2/
    - action:
        return:
          body: The legacy API is deprecated. Please, migrate to the API v4 (https://docs.coinmetrics.io/api/v4).
          code: 200
          type: text/plain
      path: /v3/
    - action:
        return:
          body: Not found
          code: 404
          type: text/plain
      path: /v4/internal/
    - action:
        return:
          body: Not found
          code: 404
          type: text/plain
      path: /v4/metrics
    - action:
        return:
          body: " "
          code: 204
          type: text/plain
      path: /feed-handlers-proxy-check
    - action:
        return:
          body: Coin Metrics API.
          code: 200
          type: text/plain
      path: /
  tls:
    redirect:
      enable: true
    secret: release-name-api4-ingress-cloudflare
  upstreams:
    - name: api4
      keepalive: 16
      max-fails: 0
      fail-timeout: "0"
      next-upstream: error timeout invalid_header http_500 http_502 http_503 http_504
      next-upstream-tries: 3
      port: 8080
      service: release-name-api4-ingress
    - name: api4-status
      keepalive: 16
      max-fails: 0
      fail-timeout: "0"
      next-upstream: error timeout invalid_header http_500 http_502 http_503 http_504
      next-upstream-tries: 3
      port: 8080
      service: release-name-api4-ingress
      # Cloudflare health check's timeout is 5 seconds, but health checks don't fail if our backend hangs (CF's bug).
      # So we return a 504 error in this case and force Cloudflare health checks to fail.
      read-timeout: 4s
    - name: api4-stream
      keepalive: 16
      fail-timeout: "0"
      max-fails: 0
      buffering: false
      next-upstream: error timeout invalid_header http_500 http_502 http_503 http_504
      next-upstream-tries: 3
      port: 8080
      read-timeout: 15m
      service: release-name-api4-ingress
---
# Source: api4-ingress/templates/virtual-server-api.yaml
apiVersion: k8s.nginx.org/v1
kind: VirtualServer
metadata:
  name: release-name-api4-ingress-api-uat-coinmetrics2-io
  labels:
    helm.sh/chart: api4-ingress-0.1.0
    app.kubernetes.io/name: api4-ingress
    app.kubernetes.io/instance: release-name
    app.kubernetes.io/version: "0.1.0"
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: api4
spec:
  host: "api-uat.coinmetrics2.io"
  ingressClassName: "nginx-production"
  server-snippets: |
    client_header_buffer_size 64k;
    large_client_header_buffers 4 64k;
  routes:
    - path: /fidelity
      route: fidelity-api1-uat/fidelity-api1-ingress-api-uat-coinmetrics2-io
    - path: /v4/timeseries-stream/market-orderbooks
      route: streaming-books-api-ingress-api-uat-coinmetrics2-io
    - path: /v4/timeseries-stream/market-trades
      route: streaming-trades-api-ingress-api-uat-coinmetrics2-io
    - path: /v4/status
      action:
        pass: api4-status
      location-snippets: |
        expires -1;
    - action:
        proxy:
          requestHeaders:
            set:
              - name: Host
                value: ${http_host}
          upstream: api4
      location-snippets: |
        gzip on;
        gzip_min_length 1100;
        gzip_comp_level 6;
        gzip_types text/plain application/json text/csv application/x-ndjson;
        gzip_disable "msie6";
        gzip_vary on;
        expires -1;
      path: /v4/
    - action:
        proxy:
          requestHeaders:
            set:
              - name: Host
                value: ${http_host}
          upstream: api4-stream
      location-snippets: |
        expires -1;
      path: /v4/timeseries-stream/
    - action:
        return:
          body: The legacy API is deprecated. Please, migrate to the API v4 (https://docs.coinmetrics.io/api/v4).
          code: 200
          type: text/plain
      path: /v1/
    - action:
        return:
          body: The legacy API is deprecated. Please, migrate to the API v4 (https://docs.coinmetrics.io/api/v4).
          code: 200
          type: text/plain
      path: /v2/
    - action:
        return:
          body: The legacy API is deprecated. Please, migrate to the API v4 (https://docs.coinmetrics.io/api/v4).
          code: 200
          type: text/plain
      path: /v3/
    - action:
        return:
          body: Not found
          code: 404
          type: text/plain
      path: /v4/internal/
    - action:
        return:
          body: Not found
          code: 404
          type: text/plain
      path: /v4/metrics
    - action:
        return:
          body: " "
          code: 204
          type: text/plain
      path: /feed-handlers-proxy-check
    - action:
        return:
          body: Coin Metrics API.
          code: 200
          type: text/plain
      path: /
  tls:
    redirect:
      enable: true
    secret: release-name-api4-ingress-cloudflare
  upstreams:
    - name: api4
      keepalive: 16
      max-fails: 0
      fail-timeout: "0"
      next-upstream: error timeout invalid_header http_500 http_502 http_503 http_504
      next-upstream-tries: 3
      port: 8080
      service: release-name-api4-ingress
    - name: api4-status
      keepalive: 16
      max-fails: 0
      fail-timeout: "0"
      next-upstream: error timeout invalid_header http_500 http_502 http_503 http_504
      next-upstream-tries: 3
      port: 8080
      service: release-name-api4-ingress
      # Cloudflare health check's timeout is 5 seconds, but health checks don't fail if our backend hangs (CF's bug).
      # So we return a 504 error in this case and force Cloudflare health checks to fail.
      read-timeout: 4s
    - name: api4-stream
      keepalive: 16
      fail-timeout: "0"
      max-fails: 0
      buffering: false
      next-upstream: error timeout invalid_header http_500 http_502 http_503 http_504
      next-upstream-tries: 3
      port: 8080
      read-timeout: 15m
      service: release-name-api4-ingress
---
# Source: api4-ingress/templates/virtual-server-docs.yaml
apiVersion: k8s.nginx.org/v1
kind: VirtualServer
metadata:
  name: release-name-api4-ingress-docs-docs-${LOCATION}-coinmetrics-io
  labels:
    helm.sh/chart: api4-ingress-0.1.0
    app.kubernetes.io/name: api4-ingress
    app.kubernetes.io/instance: release-name
    app.kubernetes.io/version: "0.1.0"
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: docs
spec:
  host: "docs-${LOCATION}.coinmetrics.io"
  ingressClassName: "nginx-production"
  routes:
    - path: /api/v4/
      action:
        pass: api4-docs
    - path: /api/static/
      action:
        pass: api4-docs
    - path: /info/
      action:
        redirect:
          url: https://coverage.coinmetrics.io
          code: 301
    - path: /fidelity-api/
      route: ${FIDELITY_NAMESPACE}/fidelity-api1-ingress-docs-${LOCATION}-coinmetrics-io

  server-snippets: |
    resolver [2620:a6:2001:2::a] valid=10m;

    location / {
      set $gitbook gitbook-docs.coinmetrics.io;
      proxy_ssl_name          $gitbook;
      proxy_ssl_server_name   on;
      proxy_pass              https://$gitbook;
      proxy_set_header        Host $gitbook;
      # it's required to proxy big cloudflare headers (gitbook uses cloudflare)
      proxy_buffering         off;
      proxy_buffer_size       128k;
      proxy_buffers           4 256k;
      proxy_busy_buffers_size 256k;
    }
  tls:
    redirect:
      enable: true
    secret: release-name-api4-ingress-cloudflare
  upstreams:
    - name: api4-docs
      next-upstream: error timeout invalid_header http_500 http_502 http_503 http_504
      next-upstream-tries: 2
      port: 8080
      service: api4-docs
---
# Source: api4-ingress/templates/virtual-server-docs.yaml
apiVersion: k8s.nginx.org/v1
kind: VirtualServer
metadata:
  name: release-name-api4-ingress-docs-docs-coinmetrics-io
  labels:
    helm.sh/chart: api4-ingress-0.1.0
    app.kubernetes.io/name: api4-ingress
    app.kubernetes.io/instance: release-name
    app.kubernetes.io/version: "0.1.0"
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: docs
spec:
  host: "docs.coinmetrics.io"
  ingressClassName: "nginx-production"
  routes:
    - path: /api/v4/
      action:
        pass: api4-docs
    - path: /api/static/
      action:
        pass: api4-docs
    - path: /info/
      action:
        redirect:
          url: https://coverage.coinmetrics.io
          code: 301
    - path: /fidelity-api/
      route: ${FIDELITY_NAMESPACE}/fidelity-api1-ingress-docs-coinmetrics-io

  server-snippets: |
    resolver [2620:a6:2001:2::a] valid=10m;

    location / {
      set $gitbook gitbook-docs.coinmetrics.io;
      proxy_ssl_name          $gitbook;
      proxy_ssl_server_name   on;
      proxy_pass              https://$gitbook;
      proxy_set_header        Host $gitbook;
      # it's required to proxy big cloudflare headers (gitbook uses cloudflare)
      proxy_buffering         off;
      proxy_buffer_size       128k;
      proxy_buffers           4 256k;
      proxy_busy_buffers_size 256k;
    }
  tls:
    redirect:
      enable: true
    secret: release-name-api4-ingress-cloudflare
  upstreams:
    - name: api4-docs
      next-upstream: error timeout invalid_header http_500 http_502 http_503 http_504
      next-upstream-tries: 2
      port: 8080
      service: api4-docs
---
# Source: api4-ingress/templates/virtual-server-docs.yaml
apiVersion: k8s.nginx.org/v1
kind: VirtualServer
metadata:
  name: release-name-api4-ingress-docs-docs-coinmetrics2-io
  labels:
    helm.sh/chart: api4-ingress-0.1.0
    app.kubernetes.io/name: api4-ingress
    app.kubernetes.io/instance: release-name
    app.kubernetes.io/version: "0.1.0"
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: docs
spec:
  host: "docs.coinmetrics2.io"
  ingressClassName: "nginx-production"
  routes:
    - path: /api/v4/
      action:
        pass: api4-docs
    - path: /api/static/
      action:
        pass: api4-docs
    - path: /info/
      action:
        redirect:
          url: https://coverage.coinmetrics.io
          code: 301
    - path: /fidelity-api/
      route: ${FIDELITY_NAMESPACE}/fidelity-api1-ingress-docs-coinmetrics2-io

  server-snippets: |
    resolver [2620:a6:2001:2::a] valid=10m;

    location / {
      set $gitbook gitbook-docs.coinmetrics.io;
      proxy_ssl_name          $gitbook;
      proxy_ssl_server_name   on;
      proxy_pass              https://$gitbook;
      proxy_set_header        Host $gitbook;
      # it's required to proxy big cloudflare headers (gitbook uses cloudflare)
      proxy_buffering         off;
      proxy_buffer_size       128k;
      proxy_buffers           4 256k;
      proxy_busy_buffers_size 256k;
    }
  tls:
    redirect:
      enable: true
    secret: release-name-api4-ingress-cloudflare
  upstreams:
    - name: api4-docs
      next-upstream: error timeout invalid_header http_500 http_502 http_503 http_504
      next-upstream-tries: 2
      port: 8080
      service: api4-docs
