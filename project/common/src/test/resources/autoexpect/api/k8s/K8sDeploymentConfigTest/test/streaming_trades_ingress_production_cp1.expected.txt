---
# Source: streaming-trades-api-ingress/templates/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: release-name-streaming-trades-api-ingress
  labels:
    helm.sh/chart: streaming-trades-api-ingress-0.1.0
    app.kubernetes.io/name: streaming-trades-api-ingress
    app.kubernetes.io/instance: release-name
    app.kubernetes.io/version: "0.1.0"
    app.kubernetes.io/managed-by: Helm
spec:
  ipFamilyPolicy: SingleStack
  ipFamilies:
    - IPv6
  ports:
    - name: http
      port: 8080
      protocol: TCP
      targetPort: 8080
  selector:
    api4.coinmetrics.io/service-level: pro
    app.kubernetes.io/name: streaming-trades-api
---
# Source: streaming-trades-api-ingress/templates/virtual-server-route.yaml
apiVersion: k8s.nginx.org/v1
kind: VirtualServerRoute
metadata:
  name: release-name-streaming-trades-api-ingress-api-${LOCATION}-coinmetrics-io
spec:
  host: "api-${LOCATION}.coinmetrics.io"
  upstreams:
    - name: streaming-trades-api
      service: release-name-streaming-trades-api-ingress
      port: 8080
      next-upstream: error timeout invalid_header http_500 http_502 http_503 http_504
      next-upstream-tries: 3
      fail-timeout: "0"
      max-fails: 0
      buffering: false
      keepalive: 16
      read-timeout: 15m
  subroutes:
    - path: /v4/timeseries-stream/market-trades
      action:
        proxy:
          requestHeaders:
            set:
              - name: Host
                value: ${http_host}
          upstream: streaming-trades-api
      location-snippets: |
        expires -1;
---
# Source: streaming-trades-api-ingress/templates/virtual-server-route.yaml
apiVersion: k8s.nginx.org/v1
kind: VirtualServerRoute
metadata:
  name: release-name-streaming-trades-api-ingress-api-coinmetrics-io
spec:
  host: "api.coinmetrics.io"
  upstreams:
    - name: streaming-trades-api
      service: release-name-streaming-trades-api-ingress
      port: 8080
      next-upstream: error timeout invalid_header http_500 http_502 http_503 http_504
      next-upstream-tries: 3
      fail-timeout: "0"
      max-fails: 0
      buffering: false
      keepalive: 16
      read-timeout: 15m
  subroutes:
    - path: /v4/timeseries-stream/market-trades
      action:
        proxy:
          requestHeaders:
            set:
              - name: Host
                value: ${http_host}
          upstream: streaming-trades-api
      location-snippets: |
        expires -1;
