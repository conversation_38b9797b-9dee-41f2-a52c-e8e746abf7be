---
# Source: api4-fallback-proxy/templates/config.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: api4-fallback-proxy-1-nginx-config
  labels:
    app.kubernetes.io/name: api4
    api4.coinmetrics.io/service-level: api4-fallback-proxy
    app.kubernetes.io/instance: api4-fallback-proxy-1
data:
  nginx.conf: |
    events {}
    http {
      log_format main escape=default '"$remote_addr" [$time_iso8601] "$request_method $host$request_uri $server_protocol" $status $body_bytes_sent $request_time "$http_referer" "$http_user_agent" $http_cf_ray';
      keepalive_timeout 60s;
      client_header_timeout 60s;
      client_body_timeout 60s;
      send_timeout 60s;

      # these directives allow client to specify requests with long query parameters and big headers,
      # we set the values we currently have in APISIX
      client_header_buffer_size 64k;
      large_client_header_buffers 4 64k;

      server {
        listen 0.0.0.0:8080 reuseport;
        listen [::]:8080 reuseport;

        location / {
          if ($host ~* ^(community-)?api-((k8s|cdev1)-stg|cp\d)\.coinmetrics\.io$) {
            add_header Content-Type text/plain;
            return 502 'Fallback is disabled.';
          }

          proxy_pass https://************;
          proxy_ssl_server_name on;

          expires -1;

          proxy_connect_timeout 60s;
          proxy_read_timeout 60s;
          proxy_send_timeout 60s;
          client_max_body_size 1m;

          proxy_pass_request_headers on;
          proxy_set_header Host "${http_host}";
          proxy_set_header Upgrade $http_upgrade;
          proxy_set_header Connection $http_connection;
        }
      }
    }
---
# Source: api4-fallback-proxy/templates/config.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: api4-fallback-proxy-2-nginx-config
  labels:
    app.kubernetes.io/name: api4
    api4.coinmetrics.io/service-level: api4-fallback-proxy
    app.kubernetes.io/instance: api4-fallback-proxy-2
data:
  nginx.conf: |
    events {}
    http {
      log_format main escape=default '"$remote_addr" [$time_iso8601] "$request_method $host$request_uri $server_protocol" $status $body_bytes_sent $request_time "$http_referer" "$http_user_agent" $http_cf_ray';
      keepalive_timeout 60s;
      client_header_timeout 60s;
      client_body_timeout 60s;
      send_timeout 60s;

      # these directives allow client to specify requests with long query parameters and big headers,
      # we set the values we currently have in APISIX
      client_header_buffer_size 64k;
      large_client_header_buffers 4 64k;

      server {
        listen 0.0.0.0:8080 reuseport;
        listen [::]:8080 reuseport;

        location / {
          if ($host ~* ^(community-)?api-((k8s|cdev1)-stg|cp\d)\.coinmetrics\.io$) {
            add_header Content-Type text/plain;
            return 502 'Fallback is disabled.';
          }

          proxy_pass https://**************;
          proxy_ssl_server_name on;

          expires -1;

          proxy_connect_timeout 60s;
          proxy_read_timeout 60s;
          proxy_send_timeout 60s;
          client_max_body_size 1m;

          proxy_pass_request_headers on;
          proxy_set_header Host "${http_host}";
          proxy_set_header Upgrade $http_upgrade;
          proxy_set_header Connection $http_connection;
        }
      }
    }
---
# Source: api4-fallback-proxy/templates/config.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: api4-fallback-proxy-3-nginx-config
  labels:
    app.kubernetes.io/name: api4
    api4.coinmetrics.io/service-level: api4-fallback-proxy
    app.kubernetes.io/instance: api4-fallback-proxy-3
data:
  nginx.conf: |
    events {}
    http {
      log_format main escape=default '"$remote_addr" [$time_iso8601] "$request_method $host$request_uri $server_protocol" $status $body_bytes_sent $request_time "$http_referer" "$http_user_agent" $http_cf_ray';
      keepalive_timeout 60s;
      client_header_timeout 60s;
      client_body_timeout 60s;
      send_timeout 60s;

      # these directives allow client to specify requests with long query parameters and big headers,
      # we set the values we currently have in APISIX
      client_header_buffer_size 64k;
      large_client_header_buffers 4 64k;

      server {
        listen 0.0.0.0:8080 reuseport;
        listen [::]:8080 reuseport;

        location / {
          if ($host ~* ^(community-)?api-((k8s|cdev1)-stg|cp\d)\.coinmetrics\.io$) {
            add_header Content-Type text/plain;
            return 502 'Fallback is disabled.';
          }

          proxy_pass https://************;
          proxy_ssl_server_name on;

          expires -1;

          proxy_connect_timeout 60s;
          proxy_read_timeout 60s;
          proxy_send_timeout 60s;
          client_max_body_size 1m;

          proxy_pass_request_headers on;
          proxy_set_header Host "${http_host}";
          proxy_set_header Upgrade $http_upgrade;
          proxy_set_header Connection $http_connection;
        }
      }
    }
---
# Source: api4-fallback-proxy/templates/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: api4-fallback-proxy
  labels:
    app.kubernetes.io/name: api4
    api4.coinmetrics.io/service-level: api4-fallback-proxy
spec:
  selector:
    app.kubernetes.io/name: api4
    api4.coinmetrics.io/service-level: api4-fallback-proxy
  ports:
    - name: http
      protocol: TCP
      port: 8080
      targetPort: 8080
---
# Source: api4-fallback-proxy/templates/app.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: api4-fallback-proxy-1
  labels:
    app.kubernetes.io/name: api4
    api4.coinmetrics.io/service-level: api4-fallback-proxy
    app.kubernetes.io/instance: api4-fallback-proxy-1
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: api4
      api4.coinmetrics.io/service-level: api4-fallback-proxy
      app.kubernetes.io/instance: api4-fallback-proxy-1
  template:
    metadata:
      labels:
        app.kubernetes.io/name: api4
        api4.coinmetrics.io/service-level: api4-fallback-proxy
        app.kubernetes.io/instance: api4-fallback-proxy-1
      annotations:
        checksum/config: 8959430929837f5f0b91a4f817bcc5c3a9ea3bc219d34ed04d718746ad2e5463
    spec:
      containers:
        - name: nginx
          image: nginx:1.27.0
          ports:
            - containerPort: 8080
              name: http
              protocol: TCP
          volumeMounts:
            - name: api4-fallback-proxy-1-nginx-config
              mountPath: /etc/nginx/nginx.conf
              subPath: nginx.conf
          resources:
            limits:
              memory: 384Mi
            requests:
              cpu: 100m
              memory: 384Mi
      volumes:
        - name: api4-fallback-proxy-1-nginx-config
          configMap:
            name: api4-fallback-proxy-1-nginx-config
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              matchExpressions:
              - key: api4.coinmetrics.io/service-level
                operator: In
                values:
                - api4-fallback-proxy
            topologyKey: "kubernetes.io/hostname"
---
# Source: api4-fallback-proxy/templates/app.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: api4-fallback-proxy-2
  labels:
    app.kubernetes.io/name: api4
    api4.coinmetrics.io/service-level: api4-fallback-proxy
    app.kubernetes.io/instance: api4-fallback-proxy-2
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: api4
      api4.coinmetrics.io/service-level: api4-fallback-proxy
      app.kubernetes.io/instance: api4-fallback-proxy-2
  template:
    metadata:
      labels:
        app.kubernetes.io/name: api4
        api4.coinmetrics.io/service-level: api4-fallback-proxy
        app.kubernetes.io/instance: api4-fallback-proxy-2
      annotations:
        checksum/config: 8959430929837f5f0b91a4f817bcc5c3a9ea3bc219d34ed04d718746ad2e5463
    spec:
      containers:
        - name: nginx
          image: nginx:1.27.0
          ports:
            - containerPort: 8080
              name: http
              protocol: TCP
          volumeMounts:
            - name: api4-fallback-proxy-2-nginx-config
              mountPath: /etc/nginx/nginx.conf
              subPath: nginx.conf
          resources:
            limits:
              memory: 384Mi
            requests:
              cpu: 100m
              memory: 384Mi
      volumes:
        - name: api4-fallback-proxy-2-nginx-config
          configMap:
            name: api4-fallback-proxy-2-nginx-config
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              matchExpressions:
              - key: api4.coinmetrics.io/service-level
                operator: In
                values:
                - api4-fallback-proxy
            topologyKey: "kubernetes.io/hostname"
---
# Source: api4-fallback-proxy/templates/app.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: api4-fallback-proxy-3
  labels:
    app.kubernetes.io/name: api4
    api4.coinmetrics.io/service-level: api4-fallback-proxy
    app.kubernetes.io/instance: api4-fallback-proxy-3
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: api4
      api4.coinmetrics.io/service-level: api4-fallback-proxy
      app.kubernetes.io/instance: api4-fallback-proxy-3
  template:
    metadata:
      labels:
        app.kubernetes.io/name: api4
        api4.coinmetrics.io/service-level: api4-fallback-proxy
        app.kubernetes.io/instance: api4-fallback-proxy-3
      annotations:
        checksum/config: 8959430929837f5f0b91a4f817bcc5c3a9ea3bc219d34ed04d718746ad2e5463
    spec:
      containers:
        - name: nginx
          image: nginx:1.27.0
          ports:
            - containerPort: 8080
              name: http
              protocol: TCP
          volumeMounts:
            - name: api4-fallback-proxy-3-nginx-config
              mountPath: /etc/nginx/nginx.conf
              subPath: nginx.conf
          resources:
            limits:
              memory: 384Mi
            requests:
              cpu: 100m
              memory: 384Mi
      volumes:
        - name: api4-fallback-proxy-3-nginx-config
          configMap:
            name: api4-fallback-proxy-3-nginx-config
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              matchExpressions:
              - key: api4.coinmetrics.io/service-level
                operator: In
                values:
                - api4-fallback-proxy
            topologyKey: "kubernetes.io/hostname"
