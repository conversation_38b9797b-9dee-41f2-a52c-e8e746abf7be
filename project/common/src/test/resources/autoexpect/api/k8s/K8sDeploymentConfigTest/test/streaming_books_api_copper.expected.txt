---
# Source: k8s/templates/secret-env.yaml
apiVersion: v1
kind: Secret
metadata:
  name: release-name-streaming-books-api-env
  labels:
    helm.sh/chart: k8s-2.0.0-stable
    app.kubernetes.io/name: streaming-books-api
    app.kubernetes.io/instance: release-name
    
    
    environment: staging
    app.kubernetes.io/version: "2.0.9"
    app.kubernetes.io/managed-by: Helm
data:
  API_BOOKS_TIER_COLD_S3_SECRET_KEY: RU5DW0FFUzI1Nl9HQ00sZGF0YTovWnN0SEZuUk9MT2RvZlBTT21DWmRXMUcyanZIb1IyNEx0WWhWQ3dTcGk3ZmxQclVTMGh6L3c9PSxpdjpIdXF2ZzRNSDNlZ2x2SWpiT240WDVhOUVqNHNXSmxGNUsyMkFTS1JYeC9JPSx0YWc6Tk1rcERtWGkwOW5wMG5YZmNLbjhqQT09LHR5cGU6c3RyXQ==
  API_STATISTICS_S3_SECRET_KEY: RU5DW0FFUzI1Nl9HQ00sZGF0YTpja2JWWjh3b2lnMkZNUWxVK0N3eXZId3hhNU94SmU3SXRXK0lRU25zOThzPSxpdjpJcGpOWjJYNzA5RktmNWlUbEZnTlN6U0dGdlhlcUU2ejB5TDE4MWlBSjJZPSx0YWc6dURrc0NneHoyQ3duSVNVRGVidXp0UT09LHR5cGU6c3RyXQ==
  API_TEMPORAL_CLIENT_CERTIFICATE_CONTENT: 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
  API_TEMPORAL_CLIENT_KEY_CONTENT: 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
  PGPASSWORD: RU5DW0FFUzI1Nl9HQ00sZGF0YTpxaXpSbTVNVm9nPT0saXY6ZkZOMS9PUjZDSHBJTG92RjQ5dE5iaGNQdVpIYW16bkJSNVhvelo4Nk9qVT0sdGFnOlJ5Z2VnK2U2bW82S0R1ajhlczVCM2c9PSx0eXBlOnN0cl0=
---
# Source: k8s/templates/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: release-name-streaming-books-api
  labels:
    helm.sh/chart: k8s-2.0.0-stable
    app.kubernetes.io/name: streaming-books-api
    app.kubernetes.io/instance: release-name
    
    
    environment: staging
    app.kubernetes.io/version: "2.0.9"
    app.kubernetes.io/managed-by: Helm
  annotations:
spec:
  type: ClusterIP
  ipFamilyPolicy: SingleStack
  ports:
    - name: http
      port: 8080
      targetPort: http
  selector:
    app.kubernetes.io/name: streaming-books-api
    app.kubernetes.io/instance: release-name
    
    
    environment: staging
---
# Source: k8s/templates/app.yaml
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: release-name-streaming-books-api
  labels:
    helm.sh/chart: k8s-2.0.0-stable
    app.kubernetes.io/name: streaming-books-api
    app.kubernetes.io/instance: release-name
    
    
    environment: staging
    app.kubernetes.io/version: "2.0.9"
    app.kubernetes.io/managed-by: Helm
spec:
  replicas: 1
  serviceName: streaming-books-api
  selector:
    matchLabels:
      app.kubernetes.io/name: streaming-books-api
      app.kubernetes.io/instance: release-name
      
      
      environment: staging
  template:
    metadata:
      annotations:
        checksum/secret: c2c24d7a37ac7519f09b7366f8f14b3bc378ddc0117419e6c02321841bbbf1c1
        checksum/config: 01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b
      labels:
        app.kubernetes.io/name: streaming-books-api
        app.kubernetes.io/instance: release-name
        
        
        environment: staging
        api4.coinmetrics.io/service-level: "pro"
        coinmetrics.io/allow-minio: ""
        deployment: "staging"
    spec:
      serviceAccountName: default
      securityContext:
        fsGroup: 1000
        runAsUser: 1000
      initContainers:
      containers:
        - name: main-release-name
          image: "$CI_REGISTRY_IMAGE:$CI_COMMIT_SHA"
          imagePullPolicy: IfNotPresent
          env:
            - name: HOSTNAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.name
            - name: NODENAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
            - name: POD_IP
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: status.podIP
            - name: envName
              value: "staging"
            - name: "API_ADDRESS_TAGGING_ENABLED"
              value: "true"
            - name: "API_AMS_HOST"
              value: "ams-api.ams-api"
            - name: "API_AMS_THREADS"
              value: "4"
            - name: "API_ASSET_ALERTS_SUPPORTED_ASSETS"
              value: "btc,usdc,usdt_eth,pax,busd,aave,sushi,usdk,husd,wbtc,renbtc,xaut,paxg"
            - name: "API_ATLAS_SHARD_1_ASSETS"
              value: "BCH,BTC,BTG,ETH_CL"
            - name: "API_ATLAS_SHARD_2_ASSETS"
              value: "ETH"
            - name: "API_ATLAS_SHARD_3_ASSETS"
              value: "1INCH,AAVE,ADA,AE_ETH,AION_ETH,ALCX,ALGO,ALPHA,ALUSD,ANT,APE,API3,AUDIO,AUDIO_ETH,AVAXC,AVAXP,AVAXX,AXS_ETH,BADGER,BAL,BAND_ETH,BAT,BNB_ETH,BNT,BTM_ETH,BUIDL_ETH,BUSD,CBAT,CBBTC_BASE.ETH,CBBTC_ETH,CBETH,CCOMP,CDAI,CEL,CENNZ,CETH,CHZ_ETH,COMP,CRO,CRV,CRVUSD_ETH,CTXC,CUNI,CUSDC,CUSDT,CVC,CWBTC,CZRX,DAI,DAI.E_BASE.ETH,DASH,DGB,DOGE,DOLA.E_BASE.ETH,DPI,DRGN,ELF,ENJ,ENS,EOS_ETH,ESD,ETC,ETHOS,EURCV_ETH,EURC_ETH,EURS_ETH,FDUSD_ETH,FEI_ETH,FLOW_EVM,FRAX_ETH,FTM_ETH,FTT,FUN,FXC_ETH,GAS,GBPT_ETH,GHO_ETH,GLM,GNO,GNT,GRT,GRT_ETH,GUSD,GYEN_ETH,HBTC,HEDG,HT,HUSD,ICN,ICP,ICX_ETH,IDRT_ETH,INST,KCS,KNC,LDO,LEND,LEO_ETH,LINK,LOOM,LPT,LRC_ETH,LTC,LUSD_ETH,MAID,MANA,MATIC_ETH,MCO,MKR,MTL_METAL,NAS_ETH,NEO,NFTX,NXM,OGN,OKB,OMG,OP_OP.ETH,PAID,PAX,PAXG,PAY,PERP,POLY,POL_ETH,POWR,PPT,PYUSD_ETH,QASH,QNT,QTUM_ETH,RAD,RADAR,RAD_ETH,RAI_FINANCE_OLD_ETH,REN,RENBTC,REP,REV_ETH,RHOC,ROOK,RSR,SAI,SALT,SAND,SHIB,SHIB_ETH,SLP_ETH,SNT,SNX,SPELL,SRM,SRN,STMX,STORJ,SUSHI,SWRV,TOKE,TOKE_ETH,TRX_ETH,TUSD_TRX,UBT,UMA,UNI,USDC,USDC_AVAXC,USDC_BASE.ETH,USDC_ETH,USDC_OP.ETH,USDC.E_OP.ETH,USDC_TRX,USDD_ETH,USDE_ETH,USDK,USDT_AVAXC,USDT.E_OP.ETH,USDT_ETH,USDT_OMNI,USDT_TRX,VERI,VET_ETH,VTC,WBTC,WETH,WNXM,WSTETH,WSTETH.E_BASE.ETH,WSTETH.E_OP.ETH,WTC,XAUT,XIDR_ETH,XRP,XSGD_ETH,XSUSHI,XVG,YFI,ZEC,ZIL_ETH,ZRX"
            - name: "API_ATLAS_SHARD_4_ASSETS"
              value: "BNB_BSC,ETH_SEPOLIA,ETH_CL_SEPOLIA,FLOW,MATIC_POLYGON"
            - name: "API_DATA_PATH"
              value: "/mnt/api4"
            - name: "API_DB_ADDRESS_TAGGING"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-tagging-pipeline-stg-hel1-p-2?user=postgres&password=$(PGPASSWORD), postgresql://pgbouncer.pgbouncer.svc:5432/pg-tagging-pipeline-stg-hel1-p-1?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_ATLAS_SHARD_1"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-atlasv2-btc-1-r-1?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_ATLAS_SHARD_1_REQUIRE_REDUNDANCY"
              value: "false"
            - name: "API_DB_ATLAS_SHARD_2"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-atlasv2-eth-1-r-1?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_ATLAS_SHARD_2_REQUIRE_REDUNDANCY"
              value: "false"
            - name: "API_DB_ATLAS_SHARD_3"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-atlasv2-other-1-r-1?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_ATLAS_SHARD_3_REQUIRE_REDUNDANCY"
              value: "false"
            - name: "API_DB_ATLAS_SHARD_4"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-atlasv2-experimental-1-r-1?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_ATLAS_SHARD_4_REQUIRE_REDUNDANCY"
              value: "false"
            - name: "API_DB_BOOKS"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-books-stg-hel1-p?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_BOOKS_REQUIRE_REDUNDANCY"
              value: "false"
            - name: "API_DB_CANDLES_MARKET"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-sharded-candles-1?user=postgres&password=$(PGPASSWORD), postgresql://pgbouncer.pgbouncer.svc:5432/pg-sharded-candles-2?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_CANDLES_MARKET_SCHEMA"
              value: "staging"
            - name: "API_DB_CANDLES_MARKET_THREADS"
              value: "20"
            - name: "API_DB_CHAIN_MONITOR"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-cmf-stg-1?user=postgres&password=$(PGPASSWORD),postgresql://pgbouncer.pgbouncer.svc:5432/pg-cmf-stg-2?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_CONNECTION_TIMEOUT_MS"
              value: "30000"
            - name: "API_DB_DEFI"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-defi-1?user=postgres&password=$(PGPASSWORD), postgresql://pgbouncer.pgbouncer.svc:5432/pg-defi-2?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_ETH_SMART_CONTRACTS"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-scmf-stg-1?user=postgres&password=$(PGPASSWORD), postgresql://pgbouncer.pgbouncer.svc:5432/pg-scmf-stg-2?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_FUTURES"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-derivatives-stg-hel1-p?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_FUTURES_REQUIRE_REDUNDANCY"
              value: "false"
            - name: "API_DB_HOURLY_NETWORK"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-network-data-factory-metrics-1h-stg-1?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_HOURLY_NETWORK_REQUIRE_REDUNDANCY"
              value: "false"
            - name: "API_DB_METRICS"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-market-metrics-1?user=postgres&password=$(PGPASSWORD), postgresql://pgbouncer.pgbouncer.svc:5432/pg-market-metrics-2?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_METRICS_SCHEMA"
              value: "staging"
            - name: "API_DB_MINUTELY_NETWORK"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-network-data-factory-metrics-1d-1b-1m-stg-1?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_MINUTELY_NETWORK_REQUIRE_REDUNDANCY"
              value: "false"
            - name: "API_DB_NETWORK"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-network-data-factory-metrics-1d-1b-1m-stg-1?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_NETWORK_REQUIRE_REDUNDANCY"
              value: "false"
            - name: "API_DB_PRINCIPAL_PRICE"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-rates-2?user=postgres&password=$(PGPASSWORD), postgresql://pgbouncer.pgbouncer.svc:5432/pg-rates-1?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_PRINCIPAL_PRICE_SCHEMA"
              value: "production"
            - name: "API_DB_QUERY_TIMEOUT_SEC"
              value: "40"
            - name: "API_DB_REQUIRE_REDUNDANCY"
              value: "true"
            - name: "API_DB_RR"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-indices-1?user=postgres&password=$(PGPASSWORD), postgresql://pgbouncer.pgbouncer.svc:5432/pg-indices-2?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_RR_NEW"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-rates-1?user=postgres&password=$(PGPASSWORD), postgresql://pgbouncer.pgbouncer.svc:5432/pg-rates-2?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_RR_NEW_SCHEMA"
              value: "production"
            - name: "API_DB_RR_NEW_THREADS"
              value: "20"
            - name: "API_DB_RR_SCHEMA"
              value: "staging"
            - name: "API_DB_SCHEMA"
              value: "public"
            - name: "API_DB_SLOW_QUERY_MS"
              value: "3000"
            - name: "API_DB_THREADS"
              value: "10"
            - name: "API_DB_TOTAL_QUERY_TIMEOUT_SEC"
              value: "45"
            - name: "API_DB_TRADES_DERIV"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-trades-derivatives-stg-hel1-p?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_TRADES_DERIV_REQUIRE_REDUNDANCY"
              value: "false"
            - name: "API_DB_TRADES_SPOT"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-trades-spot-stg-hel1-p?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_TRADES_SPOT_REQUIRE_REDUNDANCY"
              value: "false"
            - name: "API_DEFI_ENABLED"
              value: "true"
            - name: "API_ENV"
              value: "staging"
            - name: "API_FUTURES_BOOKS_TIERS"
              value: "COLD:[..-1d],HOT:[..]"
            - name: "API_FUTURES_BOOKS_TIER_COLD_S3_ACCESS_KEY"
              value: "Hyg4PvYWQ48Q3lMns8eD"
            - name: "API_FUTURES_BOOKS_TIER_COLD_S3_ENDPOINT"
              value: "https://minio.hel1.cnmtrcs.io"
            - name: "API_FUTURES_BOOKS_TIER_COLD_S3_REGION"
              value: "eu-hel1-k8s"
            - name: "API_FUTURES_BOOKS_TIER_COLD_S3_SECRET_KEY"
              value: "$(API_BOOKS_TIER_COLD_S3_SECRET_KEY)"
            - name: "API_FUTURES_BOOKS_TIER_COLD_TYPE"
              value: "S3"
            - name: "API_HEAP_SIZE"
              value: "5g"
            - name: "API_INSTANCE_ID"
              value: "${HELM_RELEASE_SUFFIX}"
            - name: "API_JOB_EXPIRATION_PERIOD"
              value: "12h"
            - name: "API_JSON_LOG_LEVEL"
              value: "trace"
            - name: "API_JVM_ARGS"
              value: "-XX:+UnlockDiagnosticVMOptions -XX:GCLockerRetryAllocationCount=100 -XX:-OmitStackTraceInFastThrow -XX:+CrashOnOutOfMemoryError -XX:ErrorFile=/opt/coinmetrics/api/dumps/hs_err_pid_%p.log -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/opt/coinmetrics/api/dumps -Dcom.sun.management.jmxremote.port=8001 -Dcom.sun.management.jmxremote.authenticate=false -Dcom.sun.management.jmxremote.ssl=false -Djava.rmi.server.hostname=app -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:8000 -Xlog:gc*=debug,safepoint,age*=trace:file=/opt/coinmetrics/api/dumps/gc.log:time::filecount=10,filesize=10M"
            - name: "API_MODULES"
              value: "streaming_books"
            - name: "API_NET_THREADS"
              value: "8"
            - name: "API_OPTIONS_BOOKS_TIERS"
              value: "COLD:[..-1d],HOT:[..]"
            - name: "API_OPTIONS_BOOKS_TIER_COLD_S3_ACCESS_KEY"
              value: "Hyg4PvYWQ48Q3lMns8eD"
            - name: "API_OPTIONS_BOOKS_TIER_COLD_S3_ENDPOINT"
              value: "https://minio.hel1.cnmtrcs.io"
            - name: "API_OPTIONS_BOOKS_TIER_COLD_S3_REGION"
              value: "eu-hel1-k8s"
            - name: "API_OPTIONS_BOOKS_TIER_COLD_S3_SECRET_KEY"
              value: "$(API_BOOKS_TIER_COLD_S3_SECRET_KEY)"
            - name: "API_OPTIONS_BOOKS_TIER_COLD_TYPE"
              value: "S3"
            - name: "API_PARALLEL_HTTP_REQUEST_LIMIT_OVERRIDES"
              value: "ZrgEy7xjg94Dj8c7qF6Z:10000,ac4zGhVRR2r5mdUr2MeM:10000,ppDpCp3ugKc11HdofYaK:10000,tf6xsFH4f9Tk6EoIUrzl:10000,BdWsturPvrvOA3EuC2wK:10000"
            - name: "API_PLAIN_LOG_LEVEL"
              value: "off"
            - name: "API_PORT"
              value: "8080"
            - name: "API_PROCESSING_THREADS"
              value: "8"
            - name: "API_PROCESSORS"
              value: "8"
            - name: "API_REALTIME_METRICS_UPDATE_FREQUENCY_MS"
              value: "10000"
            - name: "API_SPOT_BOOKS_TIERS"
              value: "COLD:[..-1d],HOT:[..]"
            - name: "API_SPOT_BOOKS_TIER_COLD_S3_ACCESS_KEY"
              value: "Hyg4PvYWQ48Q3lMns8eD"
            - name: "API_SPOT_BOOKS_TIER_COLD_S3_ENDPOINT"
              value: "https://minio.hel1.cnmtrcs.io"
            - name: "API_SPOT_BOOKS_TIER_COLD_S3_REGION"
              value: "eu-hel1-k8s"
            - name: "API_SPOT_BOOKS_TIER_COLD_S3_SECRET_KEY"
              value: "$(API_BOOKS_TIER_COLD_S3_SECRET_KEY)"
            - name: "API_SPOT_BOOKS_TIER_COLD_TYPE"
              value: "S3"
            - name: "API_STATISTICS_FILE_GROUP_ID_PREFIX"
              value: "api-stats-stg/hel1"
            - name: "API_STATISTICS_S3_ACCESS_KEY"
              value: "api-stats-stg"
            - name: "API_STATISTICS_S3_ENDPOINT"
              value: "https://minio.cnmtrcs.io:9002"
            - name: "API_STATISTICS_S3_REGION"
              value: "eu-hel1-hetz"
            - name: "API_STATISTICS_S3_SECRET_KEY"
              value: "$(API_STATISTICS_S3_SECRET_KEY)"
            - name: "API_STREAM_CONNECTION_LIMIT_COUNT_DEFAULT"
              value: "200"
            - name: "API_STREAM_CONNECTION_LIMIT_COUNT_OVERRIDES"
              value: "qJjd3A36Gfq976Fo0MFo:2000,81BEEfuul26Ib2ozMBgG:250,49z8QDlQR9gjNvMw7YRw:250"
            - name: "API_TEMPORAL_NAMESPACE"
              value: "api-k8s-stg.rztn5"
            - name: "API_TEMPORAL_SERVICE_TARGET"
              value: "api-k8s-stg.rztn5.tmprl.cloud:7233"
            - name: "API_TOTAL_BANDWIDTH_BYTES_PER_SEC"
              value: "524288000"
            - name: "API_USE_NEW_BOOKS_TABLES"
              value: "true"
            - name: "KAFKA_ASSET_QUOTES"
              value: "kafka-mdf-metrics-1.kafka.svc:9092,kafka-mdf-metrics-2.kafka.svc:9092"
            - name: "KAFKA_BOOKS_0"
              value: "kafka-book-streams-1.kafka.svc:9092,kafka-book-streams-2.kafka.svc:9092"
            - name: "KAFKA_BOOKS_0_EXCHANGES"
              value: "0, 1, 2, 4, 5, 6, 7, 9, 10, 20, 28, 31, 32, 33, 34, 35, 37, 38, 39, 40, 41, 42, 46, 48, 49, 50, 53, 54, 56, 57, 59"
            - name: "KAFKA_CANDLES"
              value: "kafka-mdf-candles-1.kafka.svc:9092,kafka-mdf-candles-2.kafka.svc:9092"
            - name: "KAFKA_INDEX_LEVELS"
              value: "kafka-mdf-indexes-1.kafka.svc:9092,kafka-mdf-indexes-2.kafka.svc:9092"
            - name: "KAFKA_LIQUIDATIONS_0"
              value: "kafka-trades-1.kafka.svc:9092,kafka-trades-2.kafka.svc:9092"
            - name: "KAFKA_LIQUIDATIONS_0_EXCHANGES"
              value: "2:26020, 4:10154413, 6:53917, 9:4684773, 10:108100, 34:26321, 37:46090, 42:4890239, 59:10123"
            - name: "KAFKA_MARKET_QUOTES"
              value: "kafka-trades-1.kafka.svc:9092,kafka-trades-2.kafka.svc:9092"
            - name: "KAFKA_OPEN_INTERESTS_0"
              value: "kafka-trades-1.kafka.svc:9092,kafka-trades-2.kafka.svc:9092"
            - name: "KAFKA_OPEN_INTERESTS_0_EXCHANGES"
              value: "2:26012, 4:10153005, 6:53899, 9:4685151, 10:108065, 34:26312, 37:48762, 42:4888669, 59:10120"
            - name: "KAFKA_PAIR_QUOTES"
              value: "kafka-mdf-metrics-1.kafka.svc:9092,kafka-mdf-metrics-2.kafka.svc:9092"
            - name: "KAFKA_PRINCIPAL_PRICE"
              value: "kafka-mdf-pprice-1.kafka.svc:9092,kafka-mdf-pprice-2.kafka.svc:9092"
            - name: "KAFKA_PRINCIPAL_PRICE_TOPICS"
              value: "principal_price_realtime_all"
            - name: "KAFKA_RATES"
              value: "kafka-mdf-rates-1.kafka.svc:9092,kafka-mdf-rates-2.kafka.svc:9092"
            - name: "KAFKA_RATES_TOPICS"
              value: "realtime_all:1s,rates_all_200ms:200ms"
            - name: "KAFKA_TRADES_0"
              value: "kafka-trades-1.kafka.svc:9092,kafka-trades-2.kafka.svc:9092"
            - name: "KAFKA_TRADES_0_EXCHANGES"
              value: "0:6339, 1:189831, 2:13134, 4:7741385, 5:10480, 6:71307, 7:8356, 8:5112, 9:2279970, 10:1527377, 11:9613, 12:847286, 16:788407, 20:323028, 21:100131, 22:0, 24:815968, 26:2958, 28:4320, 31:62, 32:4, 33:0, 34:9299, 35:1661, 37:16293, 38:177269, 39:322595, 40:1732, 41:3575, 42:2762688, 46:7956, 48:140999, 49:1418009, 50:110971, 53:22, 54:133660, 55:135530, 56:1314, 57:1, 59:4054"
            - name: "KAFKA_TRADES_1"
              value: "kafka-defi-1.kafka.svc:9092,kafka-defi-2.kafka.svc:9092"
            - name: "KAFKA_TRADES_1_EXCHANGES"
              value: "44:1442, 45:5409, 47:433"
          envFrom:
            - secretRef:
                name: release-name-streaming-books-api-env
                optional: false
          ports:
            - containerPort: 8080
              name: http
              protocol: TCP
          readinessProbe:
            httpGet:
              path: /v4/status
              port: http
            initialDelaySeconds: 15
            periodSeconds: 5
          resources:
            limits:
              memory: 7Gi
            requests:
              cpu: 4
              memory: 7Gi
          volumeMounts:
            - mountPath: /opt/coinmetrics/api/dumps
              name: dumps
      volumes:
        - emptyDir:
            sizeLimit: 100Gi
          name: dumps
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            - labelSelector:
                matchExpressions:
                  - key: "api4.coinmetrics.io/service-level"
                    operator: In
                    values:
                      - "pro"
                  - key: "app.kubernetes.io/name"
                    operator: In
                    values:
                      - "streaming-books-api"
                  - key: "coinmetrics.io/allow-minio"
                    operator: In
                    values:
                      - ""
                  - key: "deployment"
                    operator: In
                    values:
                      - "staging"
              topologyKey: "kubernetes.io/hostname"
        podAffinity:
            {}
