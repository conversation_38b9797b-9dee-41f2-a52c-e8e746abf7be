---
# Source: k8s/templates/secret-env.yaml
apiVersion: v1
kind: Secret
metadata:
  name: release-name-api4-env
  labels:
    helm.sh/chart: k8s-2.0.0-stable
    app.kubernetes.io/name: api4
    app.kubernetes.io/instance: release-name
    
    
    environment: production
    app.kubernetes.io/version: "2.0.9"
    app.kubernetes.io/managed-by: Helm
data:
  API_BOOKS_TIER_COLD_S3_SECRET_KEY: RU5DW0FFUzI1Nl9HQ00sZGF0YTpZVFJ0SDhiaTNWckE2eG9zV1lrajVXSmEsaXY6TmkvQ1JudHFqT0hOdnJmUHJWWWFyeEQ0L2lmbXU2WStad0hqR0ZNNlFDZz0sdGFnOldIeHYvZW1qdHN3R3ZiMjN1NVdBY2c9PSx0eXBlOnN0cl0=
  API_STATISTICS_S3_SECRET_KEY: RU5DW0FFUzI1Nl9HQ00sZGF0YTpBUzJQT2R6RnY3STh2ZVpGTjFtd3RHTm5LQVhRcmRPWXd1QVRxbTdkaEk0PSxpdjpwTkN4eGZXdEcwVmdESm1VYk5mRGNUY0RxZDN5bW1XOUJkYXFSOEVZRW1nPSx0YWc6QlIyU3Fpb0lsSmkrSmJkMHgzNXhxQT09LHR5cGU6c3RyXQ==
  API_TEMPORAL_CLIENT_CERTIFICATE_CONTENT: 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
  API_TEMPORAL_CLIENT_KEY_CONTENT: 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
  PGPASSWORD: RU5DW0FFUzI1Nl9HQ00sZGF0YTpkUXdyMDgrak1GQU5waTA9LGl2OjRCUUF4NlA5dzd5MzBoQm9GamdZVVlZTHd5VmdOTTZOckFqbmJ4bXhWZnc9LHRhZzpQMGVYRFJnZlpZNE5aNFRoMHlmbXNRPT0sdHlwZTpzdHJd
---
# Source: k8s/templates/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: release-name-api4
  labels:
    helm.sh/chart: k8s-2.0.0-stable
    app.kubernetes.io/name: api4
    app.kubernetes.io/instance: release-name
    
    
    environment: production
    app.kubernetes.io/version: "2.0.9"
    app.kubernetes.io/managed-by: Helm
  annotations:
spec:
  type: ClusterIP
  ipFamilyPolicy: SingleStack
  ports:
    - name: http
      port: 8080
      targetPort: http
  selector:
    app.kubernetes.io/name: api4
    app.kubernetes.io/instance: release-name
    
    
    environment: production
---
# Source: k8s/templates/app.yaml
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: release-name-api4
  labels:
    helm.sh/chart: k8s-2.0.0-stable
    app.kubernetes.io/name: api4
    app.kubernetes.io/instance: release-name
    
    
    environment: production
    app.kubernetes.io/version: "2.0.9"
    app.kubernetes.io/managed-by: Helm
spec:
  replicas: 1
  serviceName: api4
  selector:
    matchLabels:
      app.kubernetes.io/name: api4
      app.kubernetes.io/instance: release-name
      
      
      environment: production
  volumeClaimTemplates:
    - metadata:
        name: local-state
      spec:
        accessModes:
        - ReadWriteOnce
        resources:
          requests:
            storage: 1Gi
        storageClassName: local-storage
  template:
    metadata:
      annotations:
        checksum/secret: 2b37b9ed2f6cb9649b243f47376e23751301301768fdfa260056980d5e20b98c
        checksum/config: 01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b
      labels:
        app.kubernetes.io/name: api4
        app.kubernetes.io/instance: release-name
        
        
        environment: production
        api4.coinmetrics.io/service-level: "community"
        coinmetrics.io/allow-minio: ""
        deployment: "production"
    spec:
      serviceAccountName: default
      securityContext:
        fsGroup: 1000
        runAsUser: 1000
      initContainers:
      containers:
        - name: main-release-name
          image: "$CI_REGISTRY_IMAGE:$CI_COMMIT_SHA"
          imagePullPolicy: IfNotPresent
          env:
            - name: HOSTNAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.name
            - name: NODENAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
            - name: POD_IP
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: status.podIP
            - name: envName
              value: "production"
            - name: "API_ADDRESS_TAGGING_ENABLED"
              value: "true"
            - name: "API_AMS_HOST"
              value: "community-ams-api.ams-api"
            - name: "API_AMS_THREADS"
              value: "4"
            - name: "API_ASSET_ALERTS_SUPPORTED_ASSETS"
              value: "btc,usdc,usdt_eth,pax,busd,aave,sushi,usdk,husd,wbtc,renbtc,xaut,paxg"
            - name: "API_ATLAS_SHARD_1_ASSETS"
              value: "BTC"
            - name: "API_ATLAS_SHARD_2_ASSETS"
              value: "ETH"
            - name: "API_ATLAS_SHARD_3_ASSETS"
              value: "ALGO"
            - name: "API_ATLAS_SHARD_4_ASSETS"
              value: "XRP"
            - name: "API_ATLAS_SHARD_5_ASSETS"
              value: "AVAXC,AVAXP,AVAXX,BCH"
            - name: "API_ATLAS_SHARD_6_ASSETS"
              value: "1INCH,AAVE,ADA,AE_ETH,AION_ETH,ALCX,ALPHA,ALUSD,ANT,APE,API3,AUDIO,AUDIO_ETH,AXS_ETH,BADGER,BAL,BAND_ETH,BAT,BNB_ETH,BNT,BTM_ETH,BUIDL_ETH,BUSD,CBAT,CBBTC_BASE.ETH,CBBTC_ETH,CBETH,CCOMP,CDAI,CEL,CENNZ,CETH,CHZ_ETH,COMP,CRO,CRV,CRVUSD_ETH,CTXC,CUNI,CUSDC,CUSDT,CVC,CWBTC,CZRX,DAI,DAI.E_BASE.ETH,DASH,DGB,DOGE,DOLA.E_BASE.ETH,DPI,DRGN,ELF,ENJ,ENS,EOS_ETH,ESD,ETC"
            - name: "API_ATLAS_SHARD_7_ASSETS"
              value: "ETHOS,EURCV_ETH,EURC_ETH,EURS_ETH,FDUSD_ETH,FEI_ETH,FRAX_ETH,FTM_ETH,FTT,FUN,FXC_ETH,GAS,GBPT_ETH,GHO_ETH,GLM,GNO,GNT,GRT,GRT_ETH,GUSD,GYEN_ETH,HBTC,HEDG,HT,HUSD,ICN,ICP,ICX_ETH,IDRT_ETH,INST,KCS,KNC,LDO,LEND,LEO_ETH,LINK,LOOM,LPT,LRC_ETH,LTC,LUSD_ETH,MAID,MANA,MATIC_ETH,MCO,MKR,MTL_METAL,NAS_ETH,NEO,NFTX,NXM,OGN,OKB,OMG,OP_OP.ETH,PAID,PAX,PAXG,PAY,PERP,POLY,POL_ETH,POWR,PPT,PYUSD_ETH,QASH,QNT,QTUM_ETH,RAD,RADAR,RAD_ETH,RAI_FINANCE_OLD_ETH,REN,RENBTC,REP,REV_ETH,RHOC,ROOK,RSR,SAI,SALT,SAND,SHIB,SHIB_ETH,SLP_ETH,SNT,SNX,SPELL,SRM,SRN,STMX,STORJ,SUSHI,SWRV,TOKE,TOKE_ETH,TRX_ETH,TUSD_TRX,UBT,UMA,UNI,USDC,USDC.E_OP.ETH,USDC_AVAXC,USDC_BASE.ETH,USDC_ETH,USDC_OP.ETH,USDC_TRX,USDD_ETH,USDE_ETH,USDK,USDT.E_OP.ETH"
            - name: "API_ATLAS_SHARD_8_ASSETS"
              value: "USDT_AVAXC,USDT_ETH,USDT_OMNI,USDT_TRX"
            - name: "API_ATLAS_SHARD_9_ASSETS"
              value: "BTG,CC,VERI,VET_ETH,VTC,WBTC,WETH,WNXM,WSTETH,WSTETH.E_BASE.ETH,WSTETH.E_OP.ETH,WTC,XAUT,XIDR_ETH,XSGD_ETH,XSUSHI,XVG,YFI,ZEC,ZIL_ETH,ZRX"
            - name: "API_DATA_PATH"
              value: "/mnt/api4"
            - name: "API_DB_ADDRESS_TAGGING"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-tagging-pipeline-prd-${LOCATION}-p-1?user=postgres&password=$(PGPASSWORD), postgresql://pgbouncer.pgbouncer.svc:5432/pg-tagging-pipeline-prd-${LOCATION}-p-2?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_ATLAS_SHARD_1"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-atlas-1-prd-cp1-r-1?user=postgres&password=$(PGPASSWORD), postgresql://pgbouncer.pgbouncer.svc:5432/pg-atlas-1-prd-cp1?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_ATLAS_SHARD_2"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-atlas-2-prd-cp1-r-1?user=postgres&password=$(PGPASSWORD), postgresql://pgbouncer.pgbouncer.svc:5432/pg-atlas-2-prd-cp1?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_ATLAS_SHARD_3"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-atlas-3-prd-cp1-r-1?user=postgres&password=$(PGPASSWORD), postgresql://pgbouncer.pgbouncer.svc:5432/pg-atlas-3-prd-cp1?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_ATLAS_SHARD_4"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-atlas-4-prd-cp1-r-1?user=postgres&password=$(PGPASSWORD), postgresql://pgbouncer.pgbouncer.svc:5432/pg-atlas-4-prd-cp1?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_ATLAS_SHARD_5"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-atlas-5-prd-cp1-r-1?user=postgres&password=$(PGPASSWORD), postgresql://pgbouncer.pgbouncer.svc:5432/pg-atlas-5-prd-cp1?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_ATLAS_SHARD_6"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-atlas-6-prd-cp1-r-1?user=postgres&password=$(PGPASSWORD), postgresql://pgbouncer.pgbouncer.svc:5432/pg-atlas-6-prd-cp1?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_ATLAS_SHARD_7"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-atlas-7-prd-cp1-r-1?user=postgres&password=$(PGPASSWORD), postgresql://pgbouncer.pgbouncer.svc:5432/pg-atlas-7-prd-cp1?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_ATLAS_SHARD_8"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-atlas-8-prd-cp1-r-1?user=postgres&password=$(PGPASSWORD), postgresql://pgbouncer.pgbouncer.svc:5432/pg-atlas-8-prd-cp1?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_ATLAS_SHARD_9"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-atlas-9-prd-cp1-r-1?user=postgres&password=$(PGPASSWORD), postgresql://pgbouncer.pgbouncer.svc:5432/pg-atlas-9-prd-cp1?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_BOOKS"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-books-partitioned-1-r?user=postgres&password=$(PGPASSWORD), postgresql://pgbouncer.pgbouncer.svc:5432/pg-books-partitioned-1-p?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_BOOKS_REQUIRE_REDUNDANCY"
              value: "false"
            - name: "API_DB_CANDLES_MARKET"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-sharded-candles-2?user=postgres&password=$(PGPASSWORD), postgresql://pgbouncer.pgbouncer.svc:5432/pg-sharded-candles-1?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_CANDLES_MARKET_SCHEMA"
              value: "production"
            - name: "API_DB_CANDLES_MARKET_THREADS"
              value: "20"
            - name: "API_DB_CHAIN_MONITOR"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-cmf-prd-1?user=postgres&password=$(PGPASSWORD),postgresql://pgbouncer.pgbouncer.svc:5432/pg-cmf-prd-2?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_DEFI"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-defi-1?user=postgres&password=$(PGPASSWORD), postgresql://pgbouncer.pgbouncer.svc:5432/pg-defi-2?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_ETH_SMART_CONTRACTS"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-scmf-prd-1?user=postgres&password=$(PGPASSWORD), postgresql://pgbouncer.pgbouncer.svc:5432/pg-scmf-prd-2?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_FUTURES"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-derivatives-1-r-1?user=postgres&password=$(PGPASSWORD), postgresql://pgbouncer.pgbouncer.svc:5432/pg-derivatives-1-p?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_HOURLY_NETWORK"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-network-data-factory-metrics-1h-prd-1?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_HOURLY_NETWORK_REQUIRE_REDUNDANCY"
              value: "false"
            - name: "API_DB_METRICS"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-market-metrics-1?user=postgres&password=$(PGPASSWORD), postgresql://pgbouncer.pgbouncer.svc:5432/pg-market-metrics-2?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_METRICS_SCHEMA"
              value: "production"
            - name: "API_DB_MINUTELY_NETWORK"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-network-data-factory-metrics-1d-1b-1m-prd-1?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_MINUTELY_NETWORK_REQUIRE_REDUNDANCY"
              value: "false"
            - name: "API_DB_NETWORK"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-network-data-factory-metrics-1d-1b-1m-prd-1?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_NETWORK_REQUIRE_REDUNDANCY"
              value: "false"
            - name: "API_DB_PRINCIPAL_PRICE"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-pprice-2?user=postgres&password=$(PGPASSWORD), postgresql://pgbouncer.pgbouncer.svc:5432/pg-pprice-1?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_PRINCIPAL_PRICE_SCHEMA"
              value: "production"
            - name: "API_DB_QUERY_TIMEOUT_SEC"
              value: "40"
            - name: "API_DB_REQUIRE_REDUNDANCY"
              value: "true"
            - name: "API_DB_RR"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-indices-1?user=postgres&password=$(PGPASSWORD), postgresql://pgbouncer.pgbouncer.svc:5432/pg-indices-2?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_RR_NEW"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-rates-1?user=postgres&password=$(PGPASSWORD), postgresql://pgbouncer.pgbouncer.svc:5432/pg-rates-2?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_RR_NEW_SCHEMA"
              value: "production"
            - name: "API_DB_RR_NEW_THREADS"
              value: "20"
            - name: "API_DB_RR_SCHEMA"
              value: "production"
            - name: "API_DB_SCHEMA"
              value: "public"
            - name: "API_DB_SLOW_QUERY_MS"
              value: "3000"
            - name: "API_DB_THREADS"
              value: "10"
            - name: "API_DB_TOTAL_QUERY_TIMEOUT_SEC"
              value: "45"
            - name: "API_DB_TRADES_DERIV"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-trades-derivatives-1-r-1?user=postgres&password=$(PGPASSWORD), postgresql://pgbouncer.pgbouncer.svc:5432/pg-trades-derivatives-1-p?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_TRADES_SPOT"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-trades-spot-1-r-1?user=postgres&password=$(PGPASSWORD), postgresql://pgbouncer.pgbouncer.svc:5432/pg-trades-spot-1-p?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DEFI_ENABLED"
              value: "true"
            - name: "API_ENV"
              value: "production"
            - name: "API_FUTURES_BOOKS_TIERS"
              value: "COLD:[..-1d],HOT:[..]"
            - name: "API_FUTURES_BOOKS_TIER_COLD_S3_ACCESS_KEY"
              value: "JKkn232ob23knnKba3"
            - name: "API_FUTURES_BOOKS_TIER_COLD_S3_ENDPOINT"
              value: "https://s3-us-east-1.coinmetrics.io"
            - name: "API_FUTURES_BOOKS_TIER_COLD_S3_REGION"
              value: "us-east-1"
            - name: "API_FUTURES_BOOKS_TIER_COLD_S3_SECRET_KEY"
              value: "$(API_BOOKS_TIER_COLD_S3_SECRET_KEY)"
            - name: "API_FUTURES_BOOKS_TIER_COLD_TYPE"
              value: "S3"
            - name: "API_HEAP_SIZE"
              value: "15g"
            - name: "API_INSTANCE_ID"
              value: "${HELM_RELEASE_SUFFIX}"
            - name: "API_JOB_EXPIRATION_PERIOD"
              value: "12h"
            - name: "API_JSON_LOG_LEVEL"
              value: "trace"
            - name: "API_JVM_ARGS"
              value: "-XX:+UnlockDiagnosticVMOptions -XX:GCLockerRetryAllocationCount=100 -XX:-OmitStackTraceInFastThrow -XX:+CrashOnOutOfMemoryError -XX:ErrorFile=/opt/coinmetrics/api/dumps/hs_err_pid_%p.log -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/opt/coinmetrics/api/dumps -Dcom.sun.management.jmxremote.port=8001 -Dcom.sun.management.jmxremote.authenticate=false -Dcom.sun.management.jmxremote.ssl=false -Djava.rmi.server.hostname=app -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:8000 -Xlog:gc*=debug,safepoint,age*=trace:file=/opt/coinmetrics/api/dumps/gc.log:time::filecount=10,filesize=10M"
            - name: "API_MODULES"
              value: "main"
            - name: "API_NET_THREADS"
              value: "8"
            - name: "API_OPTIONS_BOOKS_TIERS"
              value: "COLD:[..-1d],HOT:[..]"
            - name: "API_OPTIONS_BOOKS_TIER_COLD_S3_ACCESS_KEY"
              value: "JKkn232ob23knnKba3"
            - name: "API_OPTIONS_BOOKS_TIER_COLD_S3_ENDPOINT"
              value: "https://s3-us-east-1.coinmetrics.io"
            - name: "API_OPTIONS_BOOKS_TIER_COLD_S3_REGION"
              value: "us-east-1"
            - name: "API_OPTIONS_BOOKS_TIER_COLD_S3_SECRET_KEY"
              value: "$(API_BOOKS_TIER_COLD_S3_SECRET_KEY)"
            - name: "API_OPTIONS_BOOKS_TIER_COLD_TYPE"
              value: "S3"
            - name: "API_PARALLEL_HTTP_REQUEST_LIMIT_OVERRIDES"
              value: "ZrgEy7xjg94Dj8c7qF6Z:10000,ac4zGhVRR2r5mdUr2MeM:10000,ppDpCp3ugKc11HdofYaK:10000,tf6xsFH4f9Tk6EoIUrzl:10000,BdWsturPvrvOA3EuC2wK:10000"
            - name: "API_PLAIN_LOG_LEVEL"
              value: "off"
            - name: "API_PORT"
              value: "8080"
            - name: "API_PROCESSING_THREADS"
              value: "8"
            - name: "API_PROCESSORS"
              value: "6"
            - name: "API_REALTIME_METRICS_UPDATE_FREQUENCY_MS"
              value: "10000"
            - name: "API_SPOT_BOOKS_TIERS"
              value: "COLD:[..-1d],HOT:[..]"
            - name: "API_SPOT_BOOKS_TIER_COLD_S3_ACCESS_KEY"
              value: "JKkn232ob23knnKba3"
            - name: "API_SPOT_BOOKS_TIER_COLD_S3_ENDPOINT"
              value: "https://s3-us-east-1.coinmetrics.io"
            - name: "API_SPOT_BOOKS_TIER_COLD_S3_REGION"
              value: "us-east-1"
            - name: "API_SPOT_BOOKS_TIER_COLD_S3_SECRET_KEY"
              value: "$(API_BOOKS_TIER_COLD_S3_SECRET_KEY)"
            - name: "API_SPOT_BOOKS_TIER_COLD_TYPE"
              value: "S3"
            - name: "API_STATISTICS_FILE_GROUP_ID_PREFIX"
              value: "api-stats-cp1"
            - name: "API_STATISTICS_S3_ACCESS_KEY"
              value: "api-stats-ro"
            - name: "API_STATISTICS_S3_ENDPOINT"
              value: "https://s3-jfk1.coinmetrics.io"
            - name: "API_STATISTICS_S3_REGION"
              value: "prd-jfk1"
            - name: "API_STATISTICS_S3_SECRET_KEY"
              value: "$(API_STATISTICS_S3_SECRET_KEY)"
            - name: "API_STREAM_CONNECTION_LIMIT_COUNT_DEFAULT"
              value: "200"
            - name: "API_STREAM_CONNECTION_LIMIT_COUNT_OVERRIDES"
              value: "qJjd3A36Gfq976Fo0MFo:2000,81BEEfuul26Ib2ozMBgG:250,49z8QDlQR9gjNvMw7YRw:250"
            - name: "API_TOTAL_BANDWIDTH_BYTES_PER_SEC"
              value: "524288000"
            - name: "API_USE_NEW_BOOKS_TABLES"
              value: "true"
            - name: "KAFKA_ASSET_QUOTES"
              value: "kafka-mdf-metrics-1.kafka.svc:9092,kafka-mdf-metrics-2.kafka.svc:9092"
            - name: "KAFKA_CANDLES"
              value: "kafka-mdf-candles-1.kafka.svc:9092,kafka-mdf-candles-2.kafka.svc:9092"
            - name: "KAFKA_INDEX_LEVELS"
              value: "kafka-mdf-indexes-1.kafka.svc:9092,kafka-mdf-indexes-2.kafka.svc:9092"
            - name: "KAFKA_MARKET_QUOTES"
              value: "kafka-trades-1.kafka.svc:9092,kafka-trades-2.kafka.svc:9092"
            - name: "KAFKA_PAIR_QUOTES"
              value: "kafka-mdf-metrics-1.kafka.svc:9092,kafka-mdf-metrics-2.kafka.svc:9092"
          envFrom:
            - secretRef:
                name: release-name-api4-env
                optional: false
          ports:
            - containerPort: 8080
              name: http
              protocol: TCP
          readinessProbe:
            httpGet:
              path: /v4/status
              port: http
            initialDelaySeconds: 15
            periodSeconds: 5
          resources:
            limits:
              memory: 19Gi
            requests:
              cpu: 6
              memory: 19Gi
          volumeMounts:
            - mountPath: /mnt/api4
              name: local-state
            - mountPath: /opt/coinmetrics/api/dumps
              name: dumps
      volumes:
        - emptyDir:
            sizeLimit: 100Gi
          name: dumps
      affinity:
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - preference:
              matchExpressions:
              - key: node-role.kubernetes.io/api4
                operator: In
                values:
                - "true"
            weight: 50
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            - labelSelector:
                matchExpressions:
                  - key: "api4.coinmetrics.io/service-level"
                    operator: In
                    values:
                      - "community"
                  - key: "app.kubernetes.io/name"
                    operator: In
                    values:
                      - "api4"
                  - key: "coinmetrics.io/allow-minio"
                    operator: In
                    values:
                      - ""
                  - key: "deployment"
                    operator: In
                    values:
                      - "production"
              topologyKey: "kubernetes.io/hostname"
        podAffinity:
            {}
      tolerations:
        - effect: NoSchedule
          key: coinmetrics.io/api4-only
          operator: Exists
