---
# Source: k8s/templates/secret-env.yaml
apiVersion: v1
kind: Secret
metadata:
  name: release-name-api4-env
  labels:
    helm.sh/chart: k8s-2.0.0-stable
    app.kubernetes.io/name: api4
    app.kubernetes.io/instance: release-name
    
    
    environment: staging
    app.kubernetes.io/version: "2.0.9"
    app.kubernetes.io/managed-by: Helm
data:
  API_BOOKS_TIER_COLD_S3_SECRET_KEY: RU5DW0FFUzI1Nl9HQ00sZGF0YTp6U0ZqNnIzVVlvMllHODNvZjJ3MXJRWEwsaXY6Skl1T2Zob2JRNHVoaVFkUStUR3hqNGhvaFVDQTA4eEFLYlFkMDJuVEw2az0sdGFnOkIrcXpaenNndEhpNEIzUkpFWDVDbEE9PSx0eXBlOnN0cl0=
  API_STATISTICS_S3_SECRET_KEY: RU5DW0FFUzI1Nl9HQ00sZGF0YTorcTQ1dTFpRXV1em9UeDl0VDJaNi9MNUJVcENub3hBdG96QS94Sit4NUIzdURkTHlUMmxScUE9PSxpdjp0b25ONGJMVUFFR3NXUXlhUnRpejJOVTYvN0pOR2Q0ZmVpclVqMlBxeE5zPSx0YWc6WGxmTDB0S2E4Y3lBb3g2di9wUGhQdz09LHR5cGU6c3RyXQ==
  API_TEMPORAL_CLIENT_CERTIFICATE_CONTENT: 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
  API_TEMPORAL_CLIENT_KEY_CONTENT: 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
  PGPASSWORD: RU5DW0FFUzI1Nl9HQ00sZGF0YTpSbWV4bTJFQ0dlWlRXeTA9LGl2OlBFYjZVeEQzU2FPUG9MdTZ6OWdOY3dTN1NYQVJNSXhsYnI0VFF0Sk9kWm89LHRhZzpreS9ueUdjYjhCd0xuemc0eDNRL2dRPT0sdHlwZTpzdHJd
---
# Source: k8s/templates/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: release-name-api4
  labels:
    helm.sh/chart: k8s-2.0.0-stable
    app.kubernetes.io/name: api4
    app.kubernetes.io/instance: release-name
    
    
    environment: staging
    app.kubernetes.io/version: "2.0.9"
    app.kubernetes.io/managed-by: Helm
  annotations:
spec:
  type: ClusterIP
  ipFamilyPolicy: SingleStack
  ports:
    - name: http
      port: 8080
      targetPort: http
  selector:
    app.kubernetes.io/name: api4
    app.kubernetes.io/instance: release-name
    
    
    environment: staging
---
# Source: k8s/templates/app.yaml
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: release-name-api4
  labels:
    helm.sh/chart: k8s-2.0.0-stable
    app.kubernetes.io/name: api4
    app.kubernetes.io/instance: release-name
    
    
    environment: staging
    app.kubernetes.io/version: "2.0.9"
    app.kubernetes.io/managed-by: Helm
spec:
  replicas: 1
  serviceName: api4
  selector:
    matchLabels:
      app.kubernetes.io/name: api4
      app.kubernetes.io/instance: release-name
      
      
      environment: staging
  volumeClaimTemplates:
    - metadata:
        name: local-state
      spec:
        accessModes:
        - ReadWriteOnce
        resources:
          requests:
            storage: 128Mi
        storageClassName: zfs
  template:
    metadata:
      annotations:
        checksum/secret: 19694efc082e89ff69a6c32b4d5bb6d049957e53083ab644724d8c460692fb3d
        checksum/config: 01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b
      labels:
        app.kubernetes.io/name: api4
        app.kubernetes.io/instance: release-name
        
        
        environment: staging
        api4.coinmetrics.io/service-level: "pro"
        coinmetrics.io/allow-minio: ""
        deployment: "staging"
    spec:
      serviceAccountName: default
      securityContext:
        fsGroup: 1000
        runAsUser: 1000
      initContainers:
      containers:
        - name: main-release-name
          image: "$CI_REGISTRY_IMAGE:$CI_COMMIT_SHA"
          imagePullPolicy: IfNotPresent
          env:
            - name: HOSTNAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.name
            - name: NODENAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
            - name: POD_IP
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: status.podIP
            - name: envName
              value: "staging"
            - name: "API_ADDRESS_TAGGING_ENABLED"
              value: "true"
            - name: "API_AMS_HOST"
              value: "ams-api.ams-api"
            - name: "API_AMS_THREADS"
              value: "4"
            - name: "API_ASSET_ALERTS_SUPPORTED_ASSETS"
              value: "btc,usdc,usdt_eth,pax,busd,aave,sushi,usdk,husd,wbtc,renbtc,xaut,paxg"
            - name: "API_ATLAS_SHARD_10_ASSETS"
              value: "CC"
            - name: "API_ATLAS_SHARD_1_ASSETS"
              value: "BTC"
            - name: "API_ATLAS_SHARD_2_ASSETS"
              value: "ETH"
            - name: "API_ATLAS_SHARD_3_ASSETS"
              value: "ALGO"
            - name: "API_ATLAS_SHARD_4_ASSETS"
              value: "XRP"
            - name: "API_ATLAS_SHARD_5_ASSETS"
              value: "AVAXC,AVAXP,AVAXX,BCH"
            - name: "API_ATLAS_SHARD_6_ASSETS"
              value: "1INCH,AAVE,ADA,AE_ETH,AION_ETH,ALCX,ALPHA,ALUSD,ANT,APE,API3,AUDIO,AUDIO_ETH,AXS_ETH,BADGER,BAL,BAND_ETH,BAT,BNB_ETH,BNT,BTM_ETH,BUIDL_ETH,BUSD,CBAT,CBBTC_BASE.ETH,CBBTC_ETH,CBETH,CCOMP,CDAI,CEL,CENNZ,CETH,CHZ_ETH,COMP,CRO,CRV,CRVUSD_ETH,CTXC,CUNI,CUSDC,CUSDT,CVC,CWBTC,CZRX,DAI,DAI.E_BASE.ETH,DASH,DGB,DOGE,DOLA.E_BASE.ETH,DPI,DRGN,ELF,ENJ,ENS,EOS_ETH,ESD,ETC"
            - name: "API_ATLAS_SHARD_7_ASSETS"
              value: "ETHOS,EURCV_ETH,EURC_ETH,EURS_ETH,FDUSD_ETH,FEI_ETH,FRAX_ETH,FTM_ETH,FTT,FUN,FXC_ETH,GAS,GBPT_ETH,GHO_ETH,GLM,GNO,GNT,GRT,GRT_ETH,GUSD,GYEN_ETH,HBTC,HEDG,HT,HUSD,ICN,ICP,ICX_ETH,IDRT_ETH,INST,KCS,KNC,LDO,LEND,LEO_ETH,LINK,LOOM,LPT,LRC_ETH,LTC,LUSD_ETH,MAID,MANA,MATIC_ETH,MCO,MKR,MTL_METAL,NAS_ETH,NEO,NFTX,NXM,OGN,OKB,OMG,OP_OP.ETH,PAID,PAX,PAXG,PAY,PERP,POLY,POL_ETH,POWR,PPT,PYUSD_ETH,QASH,QNT,QTUM_ETH,RAD,RADAR,RAD_ETH,RAI_FINANCE_OLD_ETH,REN,RENBTC,REP,REV_ETH,RHOC,ROOK,RSR,SAI,SALT,SAND,SHIB,SHIB_ETH,SLP_ETH,SNT,SNX,SPELL,SRM,SRN,STMX,STORJ,SUSHI,SWRV,TOKE,TOKE_ETH,TRX_ETH,TUSD_TRX,UBT,UMA,UNI,USDC,USDC.E_OP.ETH,USDC_AVAXC,USDC_BASE.ETH,USDC_ETH,USDC_OP.ETH,USDC_TRX,USDD_ETH,USDE_ETH,USDK,USDT.E_OP.ETH"
            - name: "API_ATLAS_SHARD_8_ASSETS"
              value: "USDT_AVAXC,USDT_ETH,USDT_OMNI,USDT_TRX"
            - name: "API_ATLAS_SHARD_9_ASSETS"
              value: "BTG,VERI,VET_ETH,VTC,WBTC,WETH,WNXM,WSTETH,WSTETH.E_BASE.ETH,WSTETH.E_OP.ETH,WTC,XAUT,XIDR_ETH,XSGD_ETH,XSUSHI,XVG,YFI,ZEC,ZIL_ETH,ZRX"
            - name: "API_DATA_PATH"
              value: "/mnt/api4"
            - name: "API_DB_ADDRESS_TAGGING"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-tagging-pipeline-stg-cdev1-p-2?user=postgres&password=$(PGPASSWORD), postgresql://pgbouncer.pgbouncer.svc:5432/pg-tagging-pipeline-stg-cdev1-p-1?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_ATLAS_SHARD_1"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-atlas-1-stg-1?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_ATLAS_SHARD_10"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-atlas-exp-stg-1?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_ATLAS_SHARD_10_REQUIRE_REDUNDANCY"
              value: "false"
            - name: "API_DB_ATLAS_SHARD_1_REQUIRE_REDUNDANCY"
              value: "false"
            - name: "API_DB_ATLAS_SHARD_2"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-atlas-2-stg-1?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_ATLAS_SHARD_2_REQUIRE_REDUNDANCY"
              value: "false"
            - name: "API_DB_ATLAS_SHARD_3"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-atlas-3-stg-1?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_ATLAS_SHARD_3_REQUIRE_REDUNDANCY"
              value: "false"
            - name: "API_DB_ATLAS_SHARD_4"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-atlas-4-stg-1?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_ATLAS_SHARD_4_REQUIRE_REDUNDANCY"
              value: "false"
            - name: "API_DB_ATLAS_SHARD_5"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-atlas-5-stg-1?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_ATLAS_SHARD_5_REQUIRE_REDUNDANCY"
              value: "false"
            - name: "API_DB_ATLAS_SHARD_6"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-atlas-6-stg-1?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_ATLAS_SHARD_6_REQUIRE_REDUNDANCY"
              value: "false"
            - name: "API_DB_ATLAS_SHARD_7"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-atlas-7-stg-1?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_ATLAS_SHARD_7_REQUIRE_REDUNDANCY"
              value: "false"
            - name: "API_DB_ATLAS_SHARD_8"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-atlas-8-stg-1?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_ATLAS_SHARD_8_REQUIRE_REDUNDANCY"
              value: "false"
            - name: "API_DB_ATLAS_SHARD_9"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-atlas-9-stg-1?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_ATLAS_SHARD_9_REQUIRE_REDUNDANCY"
              value: "false"
            - name: "API_DB_BOOKS"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-books-stg-cdev1-p?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_BOOKS_REQUIRE_REDUNDANCY"
              value: "false"
            - name: "API_DB_CANDLES_MARKET"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-sharded-candles-1?user=postgres&password=$(PGPASSWORD), postgresql://pgbouncer.pgbouncer.svc:5432/pg-sharded-candles-2?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_CANDLES_MARKET_SCHEMA"
              value: "staging"
            - name: "API_DB_CANDLES_MARKET_THREADS"
              value: "20"
            - name: "API_DB_CHAIN_MONITOR"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-cmf-stg-1?user=postgres&password=$(PGPASSWORD),postgresql://pgbouncer.pgbouncer.svc:5432/pg-cmf-stg-2?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_CONNECTION_TIMEOUT_MS"
              value: "30000"
            - name: "API_DB_DEFI"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-defi-1?user=postgres&password=$(PGPASSWORD), postgresql://pgbouncer.pgbouncer.svc:5432/pg-defi-2?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_ETH_SMART_CONTRACTS"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-scmf-stg-1?user=postgres&password=$(PGPASSWORD), postgresql://pgbouncer.pgbouncer.svc:5432/pg-scmf-stg-2?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_FUTURES"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-derivatives-stg-cdev1-p?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_FUTURES_REQUIRE_REDUNDANCY"
              value: "false"
            - name: "API_DB_HOURLY_NETWORK"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-network-data-factory-metrics-1h-stg-1?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_HOURLY_NETWORK_REQUIRE_REDUNDANCY"
              value: "false"
            - name: "API_DB_METRICS"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-market-metrics-1?user=postgres&password=$(PGPASSWORD), postgresql://pgbouncer.pgbouncer.svc:5432/pg-market-metrics-2?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_METRICS_SCHEMA"
              value: "staging"
            - name: "API_DB_MINUTELY_NETWORK"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-network-data-factory-metrics-1d-1b-1m-stg-1?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_MINUTELY_NETWORK_REQUIRE_REDUNDANCY"
              value: "false"
            - name: "API_DB_NETWORK"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-network-data-factory-metrics-1d-1b-1m-stg-1?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_NETWORK_REQUIRE_REDUNDANCY"
              value: "false"
            - name: "API_DB_PRINCIPAL_PRICE"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-rates-2?user=postgres&password=$(PGPASSWORD), postgresql://pgbouncer.pgbouncer.svc:5432/pg-rates-1?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_PRINCIPAL_PRICE_SCHEMA"
              value: "production"
            - name: "API_DB_QUERY_TIMEOUT_SEC"
              value: "40"
            - name: "API_DB_REQUIRE_REDUNDANCY"
              value: "true"
            - name: "API_DB_RR"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-indices-1?user=postgres&password=$(PGPASSWORD), postgresql://pgbouncer.pgbouncer.svc:5432/pg-indices-2?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_RR_NEW"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-rates-1?user=postgres&password=$(PGPASSWORD), postgresql://pgbouncer.pgbouncer.svc:5432/pg-rates-2?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_RR_NEW_SCHEMA"
              value: "production"
            - name: "API_DB_RR_NEW_THREADS"
              value: "20"
            - name: "API_DB_RR_SCHEMA"
              value: "staging"
            - name: "API_DB_SCHEMA"
              value: "public"
            - name: "API_DB_SLOW_QUERY_MS"
              value: "3000"
            - name: "API_DB_THREADS"
              value: "10"
            - name: "API_DB_TOTAL_QUERY_TIMEOUT_SEC"
              value: "45"
            - name: "API_DB_TRADES_DERIV"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-trades-derivatives-stg-cdev1-p?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_TRADES_DERIV_REQUIRE_REDUNDANCY"
              value: "false"
            - name: "API_DB_TRADES_SPOT"
              value: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-trades-spot-stg-cdev1-p?user=postgres&password=$(PGPASSWORD)"
            - name: "API_DB_TRADES_SPOT_REQUIRE_REDUNDANCY"
              value: "false"
            - name: "API_DEFI_ENABLED"
              value: "true"
            - name: "API_ENV"
              value: "staging"
            - name: "API_FUTURES_BOOKS_TIERS"
              value: "COLD:[..-1d],HOT:[..]"
            - name: "API_FUTURES_BOOKS_TIER_COLD_S3_ACCESS_KEY"
              value: "JKkn232ob23knnKba3"
            - name: "API_FUTURES_BOOKS_TIER_COLD_S3_ENDPOINT"
              value: "https://mini-minio-cdev1.cnmtrcs.io"
            - name: "API_FUTURES_BOOKS_TIER_COLD_S3_REGION"
              value: "us-east-1"
            - name: "API_FUTURES_BOOKS_TIER_COLD_S3_SECRET_KEY"
              value: "$(API_BOOKS_TIER_COLD_S3_SECRET_KEY)"
            - name: "API_FUTURES_BOOKS_TIER_COLD_TYPE"
              value: "S3"
            - name: "API_HEAP_SIZE"
              value: "10g"
            - name: "API_INSTANCE_ID"
              value: "${HELM_RELEASE_SUFFIX}"
            - name: "API_JOB_EXPIRATION_PERIOD"
              value: "12h"
            - name: "API_JSON_LOG_LEVEL"
              value: "trace"
            - name: "API_JVM_ARGS"
              value: "-XX:+UnlockDiagnosticVMOptions -XX:GCLockerRetryAllocationCount=100 -XX:-OmitStackTraceInFastThrow -XX:+CrashOnOutOfMemoryError -XX:ErrorFile=/opt/coinmetrics/api/dumps/hs_err_pid_%p.log -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/opt/coinmetrics/api/dumps -Dcom.sun.management.jmxremote.port=8001 -Dcom.sun.management.jmxremote.authenticate=false -Dcom.sun.management.jmxremote.ssl=false -Djava.rmi.server.hostname=app -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:8000 -Xlog:gc*=debug,safepoint,age*=trace:file=/opt/coinmetrics/api/dumps/gc.log:time::filecount=10,filesize=10M"
            - name: "API_MODULES"
              value: "main"
            - name: "API_NET_THREADS"
              value: "8"
            - name: "API_OPTIONS_BOOKS_TIERS"
              value: "COLD:[..-1d],HOT:[..]"
            - name: "API_OPTIONS_BOOKS_TIER_COLD_S3_ACCESS_KEY"
              value: "JKkn232ob23knnKba3"
            - name: "API_OPTIONS_BOOKS_TIER_COLD_S3_ENDPOINT"
              value: "https://mini-minio-cdev1.cnmtrcs.io"
            - name: "API_OPTIONS_BOOKS_TIER_COLD_S3_REGION"
              value: "us-east-1"
            - name: "API_OPTIONS_BOOKS_TIER_COLD_S3_SECRET_KEY"
              value: "$(API_BOOKS_TIER_COLD_S3_SECRET_KEY)"
            - name: "API_OPTIONS_BOOKS_TIER_COLD_TYPE"
              value: "S3"
            - name: "API_PARALLEL_HTTP_REQUEST_LIMIT_OVERRIDES"
              value: "ZrgEy7xjg94Dj8c7qF6Z:10000,ac4zGhVRR2r5mdUr2MeM:10000,ppDpCp3ugKc11HdofYaK:10000,tf6xsFH4f9Tk6EoIUrzl:10000,BdWsturPvrvOA3EuC2wK:10000"
            - name: "API_PLAIN_LOG_LEVEL"
              value: "off"
            - name: "API_PORT"
              value: "8080"
            - name: "API_PROCESSING_THREADS"
              value: "8"
            - name: "API_PROCESSORS"
              value: "8"
            - name: "API_REALTIME_METRICS_UPDATE_FREQUENCY_MS"
              value: "10000"
            - name: "API_SPOT_BOOKS_TIERS"
              value: "COLD:[..-1d],HOT:[..]"
            - name: "API_SPOT_BOOKS_TIER_COLD_S3_ACCESS_KEY"
              value: "JKkn232ob23knnKba3"
            - name: "API_SPOT_BOOKS_TIER_COLD_S3_ENDPOINT"
              value: "https://mini-minio-cdev1.cnmtrcs.io"
            - name: "API_SPOT_BOOKS_TIER_COLD_S3_REGION"
              value: "us-east-1"
            - name: "API_SPOT_BOOKS_TIER_COLD_S3_SECRET_KEY"
              value: "$(API_BOOKS_TIER_COLD_S3_SECRET_KEY)"
            - name: "API_SPOT_BOOKS_TIER_COLD_TYPE"
              value: "S3"
            - name: "API_STATISTICS_FILE_GROUP_ID_PREFIX"
              value: "api-stats-cdev1"
            - name: "API_STATISTICS_S3_ACCESS_KEY"
              value: "api-stats-rw"
            - name: "API_STATISTICS_S3_ENDPOINT"
              value: "https://mini-minio-cdev1.cnmtrcs.io"
            - name: "API_STATISTICS_S3_REGION"
              value: "us-east-1"
            - name: "API_STATISTICS_S3_SECRET_KEY"
              value: "$(API_STATISTICS_S3_SECRET_KEY)"
            - name: "API_STREAM_CONNECTION_LIMIT_COUNT_DEFAULT"
              value: "200"
            - name: "API_STREAM_CONNECTION_LIMIT_COUNT_OVERRIDES"
              value: "qJjd3A36Gfq976Fo0MFo:2000,81BEEfuul26Ib2ozMBgG:250,49z8QDlQR9gjNvMw7YRw:250"
            - name: "API_TEMPORAL_NAMESPACE"
              value: "api-k8s-stg.rztn5"
            - name: "API_TEMPORAL_SERVICE_TARGET"
              value: "api-k8s-stg.rztn5.tmprl.cloud:7233"
            - name: "API_TOTAL_BANDWIDTH_BYTES_PER_SEC"
              value: "524288000"
            - name: "API_USE_NEW_BOOKS_TABLES"
              value: "true"
            - name: "KAFKA_ASSET_QUOTES"
              value: "kafka-mdf-metrics-1.kafka.svc:9092,kafka-mdf-metrics-2.kafka.svc:9092"
            - name: "KAFKA_CANDLES"
              value: "kafka-mdf-candles-1.kafka.svc:9092,kafka-mdf-candles-2.kafka.svc:9092"
            - name: "KAFKA_INDEX_LEVELS"
              value: "kafka-mdf-indexes-1.kafka.svc:9092,kafka-mdf-indexes-2.kafka.svc:9092"
            - name: "KAFKA_LIQUIDATIONS_0"
              value: "kafka-trades-1.kafka.svc:9092,kafka-trades-2.kafka.svc:9092"
            - name: "KAFKA_LIQUIDATIONS_0_EXCHANGES"
              value: "2:26020, 4:10154413, 6:53917, 9:4684773, 10:108100, 34:26321, 37:46090, 42:4890239, 59:10123"
            - name: "KAFKA_MARKET_QUOTES"
              value: "kafka-trades-1.kafka.svc:9092,kafka-trades-2.kafka.svc:9092"
            - name: "KAFKA_OPEN_INTERESTS_0"
              value: "kafka-trades-1.kafka.svc:9092,kafka-trades-2.kafka.svc:9092"
            - name: "KAFKA_OPEN_INTERESTS_0_EXCHANGES"
              value: "2:26012, 4:10153005, 6:53899, 9:4685151, 10:108065, 34:26312, 37:48762, 42:4888669, 59:10120"
            - name: "KAFKA_PAIR_QUOTES"
              value: "kafka-mdf-metrics-1.kafka.svc:9092,kafka-mdf-metrics-2.kafka.svc:9092"
            - name: "KAFKA_PRINCIPAL_PRICE"
              value: "kafka-mdf-pprice-1.kafka.svc:9092,kafka-mdf-pprice-2.kafka.svc:9092"
            - name: "KAFKA_PRINCIPAL_PRICE_TOPICS"
              value: "principal_price_realtime_all"
            - name: "KAFKA_RATES"
              value: "kafka-mdf-rates-1.kafka.svc:9092,kafka-mdf-rates-2.kafka.svc:9092"
            - name: "KAFKA_RATES_TOPICS"
              value: "realtime_all:1s,rates_all_200ms:200ms"
            - name: "KAFKA_TRADES_0"
              value: "kafka-trades-1.kafka.svc:9092,kafka-trades-2.kafka.svc:9092"
            - name: "KAFKA_TRADES_0_EXCHANGES"
              value: "0:6339, 1:189831, 2:13134, 4:7741385, 5:10480, 6:71307, 7:8356, 8:5112, 9:2279970, 10:1527377, 11:9613, 12:847286, 16:788407, 20:323028, 21:100131, 22:0, 24:815968, 26:2958, 28:4320, 31:62, 32:4, 33:0, 34:9299, 35:1661, 37:16293, 38:177269, 39:322595, 40:1732, 41:3575, 42:2762688, 46:7956, 48:140999, 49:1418009, 50:110971, 53:22, 54:133660, 55:135530, 56:1314, 57:1, 59:4054"
            - name: "KAFKA_TRADES_1"
              value: "kafka-defi-1.kafka.svc:9092,kafka-defi-2.kafka.svc:9092"
            - name: "KAFKA_TRADES_1_EXCHANGES"
              value: "44:1442, 45:5409, 47:433"
          envFrom:
            - secretRef:
                name: release-name-api4-env
                optional: false
          ports:
            - containerPort: 8080
              name: http
              protocol: TCP
          readinessProbe:
            httpGet:
              path: /v4/status
              port: http
            initialDelaySeconds: 15
            periodSeconds: 5
          resources:
            limits:
              memory: 14Gi
            requests:
              cpu: 5
              memory: 14Gi
          volumeMounts:
            - mountPath: /mnt/api4
              name: local-state
            - mountPath: /opt/coinmetrics/api/dumps
              name: dumps
      volumes:
        - emptyDir:
            sizeLimit: 100Gi
          name: dumps
      affinity:
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - preference:
              matchExpressions:
              - key: node-role.kubernetes.io/api4
                operator: In
                values:
                - "true"
            weight: 50
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            - labelSelector:
                matchExpressions:
                  - key: "api4.coinmetrics.io/service-level"
                    operator: In
                    values:
                      - "pro"
                  - key: "app.kubernetes.io/name"
                    operator: In
                    values:
                      - "api4"
                  - key: "coinmetrics.io/allow-minio"
                    operator: In
                    values:
                      - ""
                  - key: "deployment"
                    operator: In
                    values:
                      - "staging"
              topologyKey: "kubernetes.io/hostname"
        podAffinity:
            {}
      tolerations:
        - effect: NoSchedule
          key: coinmetrics.io/api4-only
          operator: Exists
        - effect: NoExecute
          key: coinmetrics.io/zfs-only
          operator: Exists
