package io.coinmetrics.api.utils

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import io.coinmetrics.api.utils.LazyInstant.Companion.toLazyInstant
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import java.time.Instant

class LazyInstantTest {
    private val objectMapper = jacksonObjectMapper()

    @ParameterizedTest
    @ValueSource(
        strings = [
            "2024-02-03T04:05:06.123456789Z",
        ],
    )
    fun `json ser deser`(input: String) {
        for (inputLazyInstant in listOf(Instant.parse(input).toLazyInstant(), input.toLazyInstant())) {
            var actual = objectMapper.writeValueAsString(inputLazyInstant)
            val expected = "\"$input\""
            assertEquals(expected, actual)

            val input2 = objectMapper.readValue<LazyInstant>(expected)
            actual = objectMapper.writeValueAsString(input2)
            assertEquals(expected, actual)
        }
    }
}
