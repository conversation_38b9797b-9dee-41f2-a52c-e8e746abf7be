package io.coinmetrics.api.utils

import java.util.concurrent.TimeUnit

fun executeCommand(vararg cmd: String): String {
    val proc =
        ProcessBuilder(*cmd)
            .redirectOutput(ProcessBuilder.Redirect.PIPE)
            .redirectError(ProcessBuilder.Redirect.INHERIT)
            .start()

    var output: String? = null
    val inputReader =
        Thread {
            output = proc.inputStream.bufferedReader().readText()
        }.also {
            it.isDaemon = true
        }.also {
            it.start()
        }

    proc.waitFor(1, TimeUnit.MINUTES)
    val exitValue = proc.exitValue()
    check(exitValue == 0) {
        "Command `${cmd.joinToString(" ")}` failed with $exitValue:\n$output"
    }
    inputReader.join()

    return output!!
}
