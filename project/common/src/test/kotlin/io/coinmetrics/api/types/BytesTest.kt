package io.coinmetrics.api.types

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test

/**
 * Tests for the [Bytes] class
 */
class BytesTest {
    /**
     * Tests for equality:
     *  - Equal reference
     *  - Unequal types
     *  - Equal content (different or same wrapped ByteArray)
     *  - Unequal content (different size, order, or values)
     */
    @Test
    fun `equality tests`() {
        val bytes = Bytes(byteArrayOf(1, 2))

        // Test referential equality
        assertTrue(bytes.equals(bytes))

        // Test unequal types
        assertFalse(bytes.equals(null))
        assertFalse(bytes.equals(""))

        // Test equality of wrapping the same underlying ByteArray
        var testBytes = Bytes(bytes.data)
        assertTrue(bytes.equals(testBytes))

        // Test content equals (same number of the same elements in the same order)
        testBytes = Bytes(byteArrayOf(1, 2))
        assertTrue(bytes.equals(testBytes))

        // Test content not equal (different number of elements)
        testBytes = Bytes(byteArrayOf(1, 2, 3))
        assertFalse(bytes.equals(testBytes))
        testBytes = Bytes(byteArrayOf(1))
        assertFalse(bytes.equals(testBytes))

        // Test content not equal (different elements)
        testBytes = Bytes(byteArrayOf(1, 3))
        assertFalse(bytes.equals(testBytes))

        // Test content not equal (same number of the same elements in a different order)
        testBytes = Bytes(byteArrayOf(2, 1))
        assertFalse(bytes.equals(testBytes))
    }

    /**
     * Test that Bytes hashCode returns the underlying data contentHashCode
     */
    @Test
    fun `hashCode is contentHashCode`() {
        val bytes = Bytes(byteArrayOf(1, 2))

        assertEquals(994, bytes.hashCode())
    }

    /**
     * Test that string representations are correct
     */
    @Test
    fun `toString and toHex representations`() {
        var bytes = Bytes(byteArrayOf(0x01, 0x23, 0x45, 0x67, 0x89.toByte(), 0xab.toByte(), 0xcd.toByte(), 0xef.toByte()))

        assertEquals("0123456789abcdef", bytes.toHex())
        assertEquals("Bytes(0123456789abcdef)", bytes.toString())

        bytes = Bytes(byteArrayOf())

        assertEquals("", bytes.toHex())
        assertEquals("Bytes()", bytes.toString())
    }
}
