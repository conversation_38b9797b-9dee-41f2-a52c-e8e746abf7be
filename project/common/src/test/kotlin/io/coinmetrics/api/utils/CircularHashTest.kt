package io.coinmetrics.api.utils

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

/**
 * Tests for the [CircularHash] class
 */
class CircularHashTest {
    /**
     * Tests the CircularHash get, put, and size methods.
     */
    @Test
    fun `get, put, and size tests`() {
        val hash = CircularHash<Int, Int?>(5)

        // Test that new circular hash is empty
        assertEquals(0, hash.size())

        // Test initial put and associated get and size
        //  -> [ 1 => 1 ]
        assertEquals(null, hash.put(1, 1))
        assertEquals(1, hash.get(1))
        assertEquals(1, hash.size())

        // Fill to capacity testing put/get/size
        //  -> [ 1 => 1 ]
        //  -> [ 1 => 1, 2 => 2 ]
        //  -> [ 1 => 1, 2 => 2, 3 => 3 ]
        //  -> [ 1 => 1, 2 => 2, 3 => 3, 4 => 4 ]
        //  -> [ 1 => 1, 2 => 2, 3 => 3, 4 => 4, 5 => 5 ]
        repeat(4) {
            assertEquals(null, hash.put(it + 2, it + 2))
            assertEquals(it + 2, hash.get(it + 2))
            assertEquals(it + 2, hash.size())
        }

        // Add next element when at capacity, which should remove the oldest while keeping the same capacity
        //  -> [ 2 => 2, 3 => 3, 4 => 4, 5 => 5, 6 => 6 ]
        assertEquals(null, hash.put(6, 6))
        assertEquals(null, hash.get(1))
        assertEquals(6, hash.get(6))
        assertEquals(5, hash.size())

        // Test that previous value is returned when replaced and that oldest entry is still removed, reducing the size by 1
        //  -> [ 3 => 3, 4 => 4, 5 => 5, 6 => 0 ]
        assertEquals(6, hash.put(6, 0))
        assertEquals(null, hash.get(2))
        assertEquals(0, hash.get(6))
        assertEquals(4, hash.size())

        // Test ability to associate a null value with a key, size remains the same until the previous replacement expires
        //  -> [ 4 => 4, 5 => 5, 6 => 0, 2 => null ]
        assertEquals(null, hash.put(2, null))
        assertEquals(null, hash.get(2))
        assertEquals(4, hash.size())

        // Test size remains the same until the previous replacement expires
        //  -> [ 5 => 5, 6 => 0, 2 => null, 7 => 7 ]
        //  -> [ 6 => 0, 2 => null, 7 => 7, 8 => 8 ]
        //  -> [ 2 => null, 7 => 7, 8 => 8, 9 => 9 ]
        //  -> [ 2 => null, 7 => 7, 8 => 8, 9 => 9, 10 => 10 ]
        repeat(3) {
            assertEquals(null, hash.put(it + 7, it + 7))
            assertEquals(it + 7, hash.get(it + 7))
            assertEquals(4, hash.size())
        }
        assertEquals(null, hash.put(10, 10))
        assertEquals(10, hash.get(10))
        assertEquals(5, hash.size())

        // Test getting a key that isn't present
        assertEquals(null, hash.get(11))

        // Test filling circular hash with replacements
        //  -> [ 7 => 7, 8 => 8, 9 => 9, 10 => 10, 11 => 11 ]
        //  -> [ 8 => 8, 9 => 9, 10 => 10, 11 => 11 ]
        //  -> [ 9 => 9, 10 => 10, 11 => 11 ]
        //  -> [ 10 => 10, 11 => 11 ]
        //  -> [ 11 => 11 ]
        //  -> [ 11 => 11 ]
        assertEquals(null, hash.put(11, 11))
        assertEquals(11, hash.get(11))
        assertEquals(5, hash.size())
        repeat(4) {
            assertEquals(11, hash.put(11, 11))
            assertEquals(11, hash.get(11))
            assertEquals(4 - it, hash.size())
        }
        assertEquals(null, hash.put(11, 11))
        assertEquals(11, hash.get(11))
        assertEquals(1, hash.size())
    }

    /**
     * Tests the CircularHash contains and elsePut methods.
     */
    @Test
    fun `contains and elsePut tests`() {
        val hash = CircularHash<Int, Int?>(5)

        // Test false returned when key doesn't exist
        assertEquals(false, hash.contains(1))

        // Test true returned when key doesn't exist on elsePut
        //  -> [ 1 => 1 ]
        assertEquals(true, hash.elsePut(1, 1))

        // Test true returned when key does exist
        //  -> [ 1 => 1 ]
        assertEquals(true, hash.contains(1))

        // Test false returned when key does exist on elsePut
        //  -> [ 1 => 1 ]
        assertEquals(false, hash.elsePut(1, 2))
        assertEquals(1, hash.get(1))
    }
}
