{"type": "object", "additionalProperties": false, "required": ["base", "description", "extra_daily_frequencies", "full_name", "has_hourly_constituents", "has_hourly_values_table", "id", "level_frequencies", "name", "released", "return_type"], "properties": {"name": {"type": "string"}, "base": {"type": "string"}, "full_name": {"type": "string"}, "description": {"type": "string"}, "id": {"type": "integer"}, "return_type": {"type": "string", "enum": ["price_return", "total_return"]}, "has_hourly_values_table": {"type": "boolean"}, "has_hourly_constituents": {"type": "boolean"}, "released": {"type": "boolean"}, "type": {"type": "string"}, "level_frequencies": {"type": "array", "items": {"type": "string"}, "minItems": 1, "uniqueItems": true}, "extra_daily_frequencies": {"type": "array", "items": {"type": "string"}, "minItems": 0, "uniqueItems": true}, "return_multipliers": {"type": "array", "additionalProperties": false, "items": {"type": "object", "properties": {"time": {"type": "string"}, "value": {"type": "string"}}, "required": ["time", "value"]}}}}