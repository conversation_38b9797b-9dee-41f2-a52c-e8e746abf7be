stages:
  - build
  - post-build
  - docker
  - deploy hetzner
  - deploy cdev1
  - deploy cp1

before_script:
  - export GRADLE_USER_HOME=`pwd`/.gradle

variables:
  GIT_STRATEGY: clone
  GIT_SUBMODULE_STRATEGY: recursive
  # vars for postgres instances
  POSTGRES_HOST_AUTH_METHOD: trust # for passwordless access
  STATISTICS_IMAGE: ${CI_REGISTRY_IMAGE}/statistics

build:
  stage: build
  image: registry.gitlab.com/coinmetrics/libs/standard-images/jvm:21
  services:
    - docker:dind
  script:
    - ./gradlew build
    - echo https://gitlab.com/api/v4/projects/$CI_PROJECT_ID/jobs/$CI_JOB_ID/artifacts > url.txt
    - echo " " > job.txt
  artifacts:
    paths:
      - build/reports/tests/test
      - build/libs/*.jar
      - build/libs-java-agent/*.jar
      - project/statistics-generator/build/libs/*.jar
      - specs/generated
      - url.txt
      - job.txt
      - docs/conf.d
      - docs/data
    reports:
      junit:
      - build/test-results/test/**/TEST-*.xml
      - project/*/build/test-results/test/**/TEST-*.xml
    when: always
    expire_in: 1 week
  tags:
    - docker
    - coinmetrics-build-runner
  variables:
    AMS_API_DATA_REPOSITORY_PATH: env/ams-api/data
    AMS_API_DATA_COMMIT_HASH: $CI_COMMIT_SHA
    AMS_API_DATA_REPOSITORY: **************:coinmetrics/api4.git

validate-openapi-specs:
  image: node
  stage: post-build
  script:
    - npm install -g @apidevtools/swagger-cli
    - cp specs/generated/openapi-public.yaml specs/openapi-public.yaml
    - swagger-cli validate specs/openapi-public.yaml
    - cp specs/generated/openapi-internal.yaml specs/openapi-internal.yaml
    - swagger-cli validate specs/openapi-internal.yaml
    - cp specs/generated/openapi-public-redocly.yaml specs/openapi-public-redocly.yaml
    - swagger-cli validate specs/openapi-public-redocly.yaml
  tags:
    - coinmetrics-build-runner

docker-api:
  stage: docker
  image: docker:latest
  services:
    - docker:dind
  tags:
    - docker
    - coinmetrics-build-runner
  script:
    - mkdir -p ~/.docker && echo ${DOCKER_AUTH_CONFIG} > ~/.docker/config.json
    - docker build
      -t $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA
      -t $CI_REGISTRY_IMAGE:master
      --build-arg ssh_private_key="$(cat git-keys/id_rsa)"
      --build-arg ssh_public_key="$(cat git-keys/id_rsa.pub)"
      .
    - docker login $CI_REGISTRY -u ${CI_REGISTRY_USER} -p ${CI_REGISTRY_PASSWORD}
    - docker push $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA
    - |
      if [ "$CI_COMMIT_REF_NAME" = master ]; then
        echo "Tagging: $CI_REGISTRY_IMAGE as master"
        docker push $CI_REGISTRY_IMAGE:master
      fi


include:
  - local: /gitlab/k8s.yaml
  - local: /gitlab/statistics/.gitlab-ci.yaml
  - local: /gitlab/main-api/.gitlab-ci.yaml
  - local: /gitlab/fallback-proxy/.gitlab-ci.yaml
  - local: /gitlab/streaming-books-api/.gitlab-ci.yaml
  - local: /gitlab/streaming-trades-api/.gitlab-ci.yaml
  - local: /gitlab/docs/.gitlab-ci.yaml
