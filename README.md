# Coin Metrics Resources

## Merging rules

1) Committing to master directly is prohibited by repository settings. You must create an MR, Jira issue/message and ask your technical manager within your team to review it.
2) Every change **merged** to the master (except documentation changes) should be mentioned as a "production" change in the #cm-releases Slack channel with a link to the issue.

## Downstream applications

Some changes committed to the master will be automatically propagated to the [AMS-data repository](https://gitlab.com/coinmetrics/data-delivery/infrastructure-services/ams-data/) and can affect production without additional confirmations!

Also, this repository is used as a git submodule in many CM applications. Submodules are referenced by commit hashes and can be updated by maintainers of downstream applications.

It means that any changes made in the repo will not be automatically propagated to ALL downstream applications. You need to ask maintainers of the applications to update resources in their applications:

- API (#guild-data-delivery)
- Fidelity API (#project-fidelity-api)
- Market data factories (#team-market-data)
- ATLAS (#team-network-data)
- data-factory-availability (#team-network-data)
- Network data factories (#team-network-data)
- Flat files admin (#guild-data-delivery)
- Feed handlers (#product-exchanges)
- and others

## API Keys

### Request an API key

A new API key can be requested via the Jira portal: https://coinmetrics.atlassian.net/servicedesk/customer/portal/12

### Manage API keys

To manage API keys, we use a dedicated web UI: https://ams-ui.cnmtrcs.io. It's accessible only for selected employees.

## Holidays

At the start of each New Year, update the `holidays_.*.json` files with another year of calendars once they are published.

Holiday data can be found here:
- NYSE: https://www.nyse.com/markets/hours-calendars
- USD (FEDWIRE): https://www.frbservices.org/about/holiday-schedules. Pay close attention to `*` (skip) and `**` (following day) holiday designations.  The `**` following day holidays are noted in the footnote, not the holidays table.

When edits are complete, run `test.py` to validate the JSON files.

# currency.json rules

Please follow the conventions and principles in [Asset Ticker Naming Conventions and Guide to Populating Data in currency.json](https://docs.google.com/document/d/1z4p8mzV8DmuSYXJvdJra6ByyQz6dR7Sx5hsDSZg5KTM/edit). There is a section titled "Steps for Adding New Entries to currency.json" that walks you through how to add new entries.

