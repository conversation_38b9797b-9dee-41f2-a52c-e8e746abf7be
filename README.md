# API v4

Tech stack: Kotlin + AMS API + Postgres.

## Gitlab integration setup

Create a personal access token in gitlab: 
- Preferences (upper right account) → Access Tokens (left menu)
- Token name doesn't matter, 'IntelliJ' suggested to keep track of its use.
- Grant all permission levels (please update this if a subset works)

Edit `~/.gradle/gradle.properties`, add this line with your key replacing xyzzy.  Note: no quotes in the configuration file
```text
gitlabPersonalAccessToken=xyzzy
```

Login to registry.gitlab.com using "test" as a username and your gitlabPersonalAccessToken as a password. It's required to run tests locally.
```shell
docker login registry.gitlab.com
```

## Local testing environment

### Pre-requisites
Install Docker or Docker Desktop
- The latest versions of Docker Desktop don't create the unix socket file under /var/run/docker.sock, so it is necessary to create a soft link as follows:
````shell
sudo ln -s $HOME/.docker/run/docker.sock /var/run/docker.sock
````

If this is a fresh clone of the APIv4 repo you will need to run `git submodule update --init`. This clones the `resources` folder,
without this folder the repo will not function correctly. 

### Setup environment
AMS API & PostgreSQL:
```shell
# it will once ask your gitlab username and password to download images from private registry. If you have 2FA enabled you must use your access token instead of your password.
sh ./env/cm-env.sh
```


Kafka:
- Some integration tests use `confluentinc/cp-kafka` image to spin up a kafka container. Since Confluent does not officially support ARM images yet, 
you can use community prebuilt one (or build one yourself) and override the image name in `~/.testcontainers.properties`, e.g.
```properties
docker.client.strategy=org.testcontainers.dockerclient.UnixSocketClientProviderStrategy
kafka.container.image=mvsxm/cp-kafka:5.0.1
```
- If you choose to work with the image above, execute the following docker command:
```shell
docker pull mvsxm/cp-kafka:5.0.1
```
- If you are using Mac with Silicon M1 chip you will need to install Rosetta in order to run this image 

### Build process
The build process consists of a files generation step involving protobuf and the compilation steps.

```shell
./gradlew generateProto
./gradlew openApiMainSpecGenerate
./gradlew build
```
Integration and Unit tests run in the scope of build step. If you'd like to skip it you can do the following:
```shell
./gradlew build -x test
```
*Note*: Some integration tests are dealing with huge Json strings and, when there's a difference between the expected and actual results, it can be difficult to spot it when the json is contained in a single line. In order to make it easier to test, a new functionality has been implemented on BaseTest.kt to reformat the Json strings before comparing them. To activate this functionality just create a file with the name .cm/local-env.properties under your HOME directory with the following content:
```
show.multiline.diffs=true
```

### Run API Docs
In order to check API documentation in browser, please execute the following command:
```shell
./docs/cm-docs.sh
```
This will build Docker image and run Docker container with API documentation available at http://localhost:8082/api/v4/.

### Run API server

Run main class using IDE: `io.coinmetrics.api.Server`

Note: you need to set Environment variables: `API_DB_SCHEMA=test`, `API_DB=postgresql://127.0.0.1:5432/test?user=postgres`.



### Queries for testing local server

#### Catalog All

##### Exchanges
    http://localhost:8080/v4/catalog-all/exchanges?api_key=x1


##### Markets
    http://localhost:8080/v4/catalog-all/markets?exchange=binance&api_key=x1
    http://localhost:8080/v4/catalog-all/markets?exchange=binance&base=btc&api_key=x1
    http://localhost:8080/v4/catalog-all/markets?exchange=binance&quote=btc&api_key=x1
    http://localhost:8080/v4/catalog-all/markets?exchange=binance&asset=btc&api_key=x1
    http://localhost:8080/v4/catalog-all/markets?asset=btc&api_key=x1
    http://localhost:8080/v4/catalog-all/markets?type=derivatives&api_key=x1
    http://localhost:8080/v4/catalog-all/markets?api_key=x1

#### Catalog

##### Exchanges
    http://localhost:8080/v4/catalog/exchanges?id=binance&api_key=x1
    http://localhost:8080/v4/catalog/exchanges?id=binance,binance.us,bitmex&api_key=x1

##### Markets

    http://localhost:8080/v4/catalog/markets?id=binance-btc-usdt-spot&api_key=x1
    http://localhost:8080/v4/catalog/markets?exchange=binance&api_key=x1
    http://localhost:8080/v4/catalog/markets?exchange=binance&base=btc&api_key=x1
    http://localhost:8080/v4/catalog/markets?exchange=binance&quote=btc&api_key=x1
    http://localhost:8080/v4/catalog/markets?exchange=binance&asset=btc&api_key=x1
    http://localhost:8080/v4/catalog/markets?asset=btc&api_key=x1
    http://localhost:8080/v4/catalog/markets?type=derivative&api_key=x1
    http://localhost:8080/v4/catalog/markets?api_key=x1

#### Running local API server against staging data
If you'd like to run a local development server against staging database you can port forward to kubernetes postgres and do it 
that way. This should not be done against prod postgres. I will show how to do this for spot trades:
1. Find the `postgres` pod that is stores the table you are interested in. Running `kubectl get pods -n postgres | grep trades` I see it is: `pg-trades-spot-stg-hel1-p-1-0`
2. Port forward using a port of your choosing to that pods `5432` port: `kubectl -n postgres port-forward pg-trades-spot-stg-hel1-p-1-0 4040:5432`
3. Set the proper environment variables in order to override the default: `API_DB_SCHEMA=test;API_DB_TRADES_SPOT=postgresql://localhost:4040/postgres?user=postgres;API_DB_TRADES_SPOT_SCHEMA=public`
In this case I am overwriting the environment variables API_DB_TRADES_SPOT and API_TRADES_SPOT_SCHEMA. These env vars can be found in the [k8s/api/values.yaml](https://gitlab.com/coinmetrics/data-delivery/api4/-/blob/master/k8s/api/common-values.yaml),
[k8s/api/api4-hel1-values.yaml](https://gitlab.com/coinmetrics/data-delivery/api4/-/blob/master/k8s/api/api4-hel1-values.yaml) and are resolved in [DbConfig.kt](https://gitlab.com/coinmetrics/libs/databases/-/blob/master/src/main/kotlin/io/coinmetrics/databases/DbConfig.kt#L42)
4. Now run Server.kt and it's now possible to get data for `curl http://localhost:8080/v4/timeseries/market-trades\?markets\=zb.com-zb-usdt-spot\&api_key\=x1` for example.

Doing the same using pgbouncer:
1. Port forward to a specific pgbouncer worker `k -n pgbouncer port-forward pgbouncer-288wd 4040:5432`
2. Update the env vars to work with pgbouncer ` API_DB_TRADES_SPOT:"postgresql://localhost:4040/pg-trades-spot-stg-hel1-p-1?user=postgres&password=$(PGPASSWORD),API_DB_TRADES_SPOT_SCHEMA=public`
3. Run server now
The main difference being when you port forward to pgbouncer you are using the same url as in the hel1 values section, 
but changing `pgbouncer.pgbouncer.svc:5432` -> `localhost:4040`

There is more information on kubernetes and [port forwarding in the engineering wiki](https://gitlab.com/coinmetrics/wiki/-/wikis/kubernetes/Useful-Kubernetes-commands#how-to-access-postgres-inside-of-kubernetes).

## Release process
1. Create a release branch for your feature. Usually, it can be done, pressing ‘Create merge request’ in the GitLab task or by pushing your branch to GitLab.
2. Task requires release notes in description or comment. Example is [here](https://gitlab.com/coinmetrics/templates/-/blob/master/.gitlab/issue_templates/release_approval_ticket.md).
3. Deploy to staging using manual action in GitLab (your MR pipeline). Your MR pipeline runs a pipeline in [operations repo](https://gitlab.com/coinmetrics/ops/operations/-/pipelines), that actually deploys your feature on staging. Make sure this pipeline does not fail. Check everything is working on staging.
4. Ask for review of the task and changes in [#dd-api4](https://coinmetrics.slack.com/archives/CSBMENHMY). Make sure all latest changes from the master branch are in your MR.
5. Merge (use squash commits & delete branch features).
6. Deploy on staging again.
7. Add a note that you're going to deploy your feature in the [#cm-releases](https://coinmetrics.slack.com/archives/CV3EBSNTH) Slack channel.
8. Deploy to prod-community -> then prod (from master branch pipeline). Check everything is working on each step. Make sure pipelines in [operations repo](https://gitlab.com/coinmetrics/ops/operations/-/pipelines) do not fail.
9. React to your message posted at step 7 with DONE smiley.

### Notes
* Person that approves MR/task cannot deploy.
* Feature can be deployed on prod **ONLY** if the corresponding task and MR are approved (there should be task comment ‘approved’ from the person other than you).
* EBF release (emergency bugfix) can be done with an EBF ticket after the actual deployment. If there’s no one to approve that ticket’s MR, it’s okay to merge and deploy if it’s urgent. In this case, it should be approved/reworked afterwards.

### Release troubleshooting
- If k8s deployment job fails due to unhealthy API container subsequent attempts to re-run the job will fail with the following error:
    > previous release was unsuccessful.  'HELM_FAILURE_HANDLING' is set to 'fail', so helm upgrade will be aborted to prevent corrupting state.  Please investigate manually.
    
    It does this to prevent an application which has state from corrupting itself. We have two options to resolve it manually:
    1. Rollback the helm chart to the previous version:
        ```
         helm rollback -n api4 api4-1
        ```
       This will terminate the unhealthy pod and spin up a pod with previous version. Later, once the root cause is fixed, we can attempt to deploy once again using CI job.
    2. Uninstall the helm chart:
       ```
       helm uninstall -n api4 api4-1
       ```
       This will remove the pod completely and allow us to re-run CI job.
  
    Alternatively, we can set env variable `HELM_FAILURE_HANDLING` to either `rollback` or `uninstall` and CI job will do the corresponding action automatically before attempting to deploy a failed release.
    
    For staging environment `HELM_FAILURE_HANDLING` is set to `uninstall` by default. For production manual action is required to make sure application is not in broken state.

## Generate MINIO test files

Integration tests in API require MINIO `.json.gz` data files. They should be placed in resources.\
You can convert our existing [test Postgres data](https://gitlab.com/coinmetrics/data-delivery/api4/-/blob/master/src/test/resources/postgres/init_db.sql) into MINIO files with the help of [this S3 data loader script](https://gitlab.com/coinmetrics/data-delivery/s3-data-loaders/order-books/-/blob/master/src/test/kotlin/io/coinmetrics/s3dataloaders/orderbooks/batch/ApiTestFilesBuilder.kt).

## Statistics application

Statistics generation is decoupled from the main API application. S3 storage (via Shared Files library) is used as an
intermediate between statistics generator app and the API app to allow for improved availability.

### Adding new statistics item

Each statistics item consists of two components: statistics generator and statistics facade. Statistics generator
implements `StatisticsGenerator` interface to produce raw statistics data. Statistics facade provides access to the
raw data.

In order to add new statistics item:

1. Inside `io.coinmetrics.api.statistics.${package}` create `${name}Statistics` and `${name}StatisticsGenerator` classes
   where `${package}` is an appropriate sub-package for the new statistics item and `${name}` is the new statistics item
   name.
2. Implement `StatisticsGenerator` interface for `${name}StatisticsGenerator` class.
3. Implement `${name}Statistics`.
   1. Create `descriptor` field inside companion object that specifies statistics item name and raw data type. The name
      must be unique across other statistics items.
   2. Accept `StatisticsRepository` as parameter and access statistics item using 
      `StatisticsRepository.getItem(descriptor)`.
   3. Provide necessary facade accessors.
4. Inside `StatisticsApp` add the new `${name}StatisticsGenerator` to `StatisticsManager` configuration.
5. Inside `HttpRequestHandlerImpl` add new field that instantiates the new `${name}Statistics` class.
