{{>licenseInfo}}
package {{packageName}}

import io.coinmetrics.api.endpoints.Endpoint
{{#imports}}import {{import}}
{{/imports}}
{{#apiInfo}}
{{#apis}}
    {{#operations}}
        {{#operation}}
import io.coinmetrics.api.endpoints.{{#lambda.titlecase}}{{operationId}}{{/lambda.titlecase}}Endpoint
        {{/operation}}
    {{/operations}}
{{/apis}}

class Paths(
{{#apis}}
    {{#operations}}
        {{#operation}}
    {{operationId}}Endpoint: {{#lambda.titlecase}}{{operationId}}{{/lambda.titlecase}}Endpoint? = null,
        {{/operation}}
    {{/operations}}
{{/apis}}
) {
    val generatedPaths = LinkedHashMap<String, Endpoint<*>>()
    init {
{{#apis}}
    {{#operations}}
        {{#operation}}
        if ({{operationId}}Endpoint != null) generatedPaths["/v{api_version}{{path}}"] = {{operationId}}Endpoint
        {{/operation}}
    {{/operations}}
{{/apis}}
    }
}
{{/apiInfo}}
