{{>licenseInfo}}
package {{apiPackage}}

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.Response
import io.coinmetrics.api.Response.Companion.errorResponse
import io.coinmetrics.api.effectiveApiKey
import io.coinmetrics.httpserver.HttpRequest
import org.slf4j.LoggerFactory
{{#imports}}
import {{import}}
{{/imports}}

{{#operations}}
{{#operation}}
/**
* {{summary}}
* {{#unescapedNotes}}{{.}}{{/unescapedNotes}}
*/
abstract class {{#lambda.titlecase}}{{operationId}}{{/lambda.titlecase}}Endpoint : Endpoint<{{{returnType}}}> {
    protected val log = LoggerFactory.getLogger(this::class.java)
    protected val supportedParameters = setOf<String>({{#allParams}}"{{baseName}}", {{/allParams}}"api_version", "api_key")

    override suspend fun handle(httpRequest: HttpRequest): Response<{{{returnType}}}> {
        val allParameters = httpRequest.queryParameters.keys + httpRequest.pathParameters.keys
        val unsupportedParameters = allParameters - supportedParameters
        if (unsupportedParameters.isNotEmpty()) {
            return errorResponse(ApiError.UnsupportedParameter(unsupportedParameters.first()))
        }

        val apiKey = try {
            httpRequest.effectiveApiKey()
                ?.also {
                    if (it.isEmpty()) return errorResponse(ApiError.MissingApiKey)
                }
                ?: return errorResponse(ApiError.MissingApiKey)
        } catch (e: Exception) {
            log.error("Can't parse 'api_key' parameter", e)
            return errorResponse(ApiError.BadParameter("api_key"))
        }

        {{#allParams}}
        val {{paramName}} = try {
            httpRequest.{{#isQueryParam}}query{{/isQueryParam}}{{#isPathParam}}path{{/isPathParam}}{{#isFormParam}}form{{/isFormParam}}Parameters["{{baseName}}"]
                {{#isInteger}}?.toInt(){{/isInteger}}
                {{#isLong}}?.toLong(){{/isLong}}
                {{#isBoolean}}?.toBoolean(){{/isBoolean}}
                {{#isContainer}}?.let { value -> value.split(',').asSequence().map { it.trim() }.filter { it.isNotEmpty() }.distinct().toList().ifEmpty { null } }{{/isContainer}}
            {{!
                There's two different ways an enum can appear in OpenAPI:
                1. Inline enums. No enum class is generated and isEnum is true. If `format` field is not set the actual
                   data type will be String, otherwise the format is the name of enum class to use as the data type.
                   Custom format must be properly registered in the build.gradle.kts.
                2. Referenced enums (via `schema.$ref`). Enum class is generated and isEnum is false. The actual data type
                   will be the generated enum class.
            }}
            {{#allowableValues}}
                ?.let {
                    when (it) {
                        {{#allowableValues.enumVars}}
                            {{{value}}} ->
                                {{#isEnum}}
                                    {{#dataFormat}}{{{dataType}}}.{{#lambda.uppercase}}{{name}}{{/lambda.uppercase}}{{/dataFormat}}
                                    {{^dataFormat}}it{{/dataFormat}}
                                {{/isEnum}}
                                {{^isEnum}}{{{dataType}}}.{{#lambda.uppercase}}{{name}}{{/lambda.uppercase}}{{/isEnum}}
                        {{/allowableValues.enumVars}}
                        else -> return errorResponse(ApiError.UnsupportedParameterValueWithSupportedInfo("{{baseName}}", it, listOf({{#allowableValues.enumVars}}{{{value}}}, {{/allowableValues.enumVars}})))
                    }
                }
            {{/allowableValues}}
            {{^allowableValues}}
                {{#isString}}
                    ?.also {
                        if (it.isEmpty()) return errorResponse(ApiError.BadParameter("{{baseName}}"))
                    }
                {{/isString}}
            {{/allowableValues}}
            {{#minimum}}
                ?.also {
                    if (it < {{{.}}}) return errorResponse(ApiError.BadParameter("{{baseName}}", "Must be at least {{{.}}}."))
                }
            {{/minimum}}
            {{#maximum}}
                ?.also {
                    if (it > {{{.}}}) return errorResponse(ApiError.BadParameter("{{baseName}}", "Must be at most {{{.}}}."))
                }
            {{/maximum}}
            {{^required}}
                {{#defaultValue}}
                ?:
                    {{#allowableValues}}
                        {{#isEnum}}
                            {{#dataFormat}}{{{dataType}}}.valueOf({{{defaultValue}}}.uppercase()){{/dataFormat}}
                            {{^dataFormat}}{{{defaultValue}}}{{/dataFormat}}
                        {{/isEnum}}
                        {{^isEnum}}{{{dataType}}}.{{#lambda.uppercase}}{{{defaultValue}}}{{/lambda.uppercase}}{{/isEnum}}
                    {{/allowableValues}}
                    {{^allowableValues}}{{{defaultValue}}}{{/allowableValues}}
                {{/defaultValue}}
            {{/required}}
            {{#required}}
                ?: return errorResponse(ApiError.MissingParameter("{{baseName}}"))
            {{/required}}
        } catch (e: Exception) {
            log.error("Can't parse '{{baseName}}' parameter", e)
            return errorResponse(ApiError.BadParameter("{{baseName}}"))
        }

        {{/allParams}}
        val req = {{#lambda.titlecase}}{{operationId}}{{/lambda.titlecase}}Request(httpRequest, apiKey{{#allParams.0}}, {{/allParams.0}}{{#allParams}}{{paramName}}{{^-last}}, {{/-last}}{{/allParams}})
        return handle(req)
    }

    /**
    * Works in a network thread.
    */
    abstract suspend fun handle(request: {{#lambda.titlecase}}{{operationId}}{{/lambda.titlecase}}Request): Response<{{{returnType}}}>
}

data class {{#lambda.titlecase}}{{operationId}}{{/lambda.titlecase}}Request(
    val httpRequest: HttpRequest,
    val apiKey: String,
{{#allParams}}
    // {{description}} {{^required}}(optional{{#defaultValue}}, default to {{{.}}}{{/defaultValue}}){{/required}}
    val {{paramName}}: {{{dataType}}}{{^required}}{{^defaultValue}}?{{/defaultValue}}{{#defaultValue}} =
        {{#allowableValues}}
            {{#isEnum}}
                {{#dataFormat}}{{{dataType}}}.valueOf({{{defaultValue}}}.uppercase()){{/dataFormat}}
                {{^dataFormat}}{{{defaultValue}}}{{/dataFormat}}
            {{/isEnum}}
            {{^isEnum}}{{{dataType}}}.{{#lambda.uppercase}}{{{defaultValue}}}{{/lambda.uppercase}}{{/isEnum}}
        {{/allowableValues}}
        {{^allowableValues}}{{{defaultValue}}}{{/allowableValues}}
    {{/defaultValue}}{{/required}},
{{/allParams}}
)

{{/operation}}
{{/operations}}
