{{#description}}
    /* {{{description}}} */
{{/description}}
    @JsonProperty("{{{baseName}}}")
{{#items.mostInnerItems.isNullable}}
    @JsonInclude(JsonInclude.Include.USE_DEFAULTS)
{{/items.mostInnerItems.isNullable}}
{{^items.mostInnerItems.isNullable}}
{{#isNullable}}
    @JsonInclude(JsonInclude.Include.ALWAYS)
{{/isNullable}}
{{/items.mostInnerItems.isNullable}}
    {{>modelMutable}} {{{name}}}: {{#isEnum}}{{classname}}.{{nameInCamelCase}}{{/isEnum}}{{^isEnum}}{{#items.isListContainer}}{{{baseType}}}<{{{items.baseType}}}<{{{items.complexType}}}>>{{/items.isListContainer}}{{^items.isListContainer}}{{{dataType}}}{{/items.isListContainer}}{{/isEnum}}