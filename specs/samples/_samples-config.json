{"host": "api.coinmetrics.io/v4", "endpoints": [{"method": "PUT", "endpoint": "/blockchain-job/account-balances", "languages": [{"url": "/blockchain-job/account-balances?api_key=<your_key>", "lang": "curl,python"}], "formAttributes": {"assets": "sol", "accounts": "account1,account2", "at_time": "2024-07-04T00:00:00.000000000Z"}}, {"method": "PUT", "endpoint": "/blockchain-job/balance-updates", "languages": [{"url": "/blockchain-job/balance-updates?api_key=<your_key>", "lang": "curl,python"}], "formAttributes": {"assets": "sol", "accounts": "account1,account2", "start_time": "2023-07-04T00:00:00.000000000Z", "end_time": "2024-07-04T00:00:00.000000000Z"}}, {"method": "PUT", "endpoint": "/blockchain-job/transactions", "languages": [{"url": "/blockchain-job/transactions?api_key=<your_key>", "lang": "curl,python"}], "formAttributes": {"assets": "sol", "txids": "txid1,txid2"}}, {"endpoint": "/blockchain-metadata/tags", "languages": [{"url": "/blockchain-metadata/tags?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/blockchain-v2/{asset}/accounts", "languages": [{"url": "/blockchain-v2/usdc/accounts?pretty=true&api_key=<your_key>", "lang": "curl,python", "comment": "Gets list of accounts for USDC"}]}, {"endpoint": "/blockchain-v2/{asset}/accounts/{account}/balance-updates", "languages": [{"url": "/blockchain-v2/btc/accounts/**********************************/balance-updates?pretty=true&api_key=<your_key>", "lang": "curl,python", "comment": "Gets list of balance-updates for a BTC account"}]}, {"endpoint": "/blockchain-v2/{asset}/balance-updates", "languages": [{"url": "/blockchain-v2/usdc/balance-updates?pretty=true&api_key=<your_key>", "lang": "curl,python", "comment": "Gets list of balance-updates for USDC"}]}, {"endpoint": "/blockchain-v2/{asset}/blocks", "languages": [{"url": "/blockchain-v2/ltc/blocks?pretty=true&api_key=<your_key>", "lang": "curl,python", "comment": "Gets list of blocks for Litecoin"}]}, {"endpoint": "/blockchain-v2/{asset}/blocks/{block_hash}", "languages": [{"url": "/blockchain-v2/ltc/blocks/885f0ba526e9a683f64c8fdb83c5e8cbc0d6e74fb93f2351941ee409a924b7a8?pretty=true&api_key=<your_key>", "lang": "curl,python", "comment": "Gets block data for a specific Litecoin block, specified by the hash of the block"}]}, {"endpoint": "/blockchain-v2/{asset}/blocks/{block_hash}/transactions/{txid}", "languages": [{"url": "/blockchain-v2/ltc/blocks/885f0ba526e9a683f64c8fdb83c5e8cbc0d6e74fb93f2351941ee409a924b7a8/transactions/3345cbbcc53b8a0113951b69f98a72a84b5a094af84842b46ed931c242c09597?pretty=true&api_key=<your_key>", "lang": "curl,python", "comment": "Gets block data and balance updates for a specific Litecoin block, specified by the hash of the block"}]}, {"endpoint": "/blockchain-v2/{asset}/sub-accounts", "languages": [{"url": "/blockchain-v2/ltc/sub-accounts?pretty=true&api_key=<your_key>", "lang": "curl,python", "comment": "Gets list of sub accounts for Litecoin"}]}, {"endpoint": "/blockchain-v2/{asset}/transactions", "languages": [{"url": "/blockchain-v2/etc/transactions?pretty=true&api_key=<your_key>", "lang": "curl,python", "comment": "Gets list of transactions for Ethereum classic"}]}, {"endpoint": "/blockchain-v2/{asset}/transactions/{txid}", "languages": [{"url": "/blockchain-v2/ltc/transactions/3345cbbcc53b8a0113951b69f98a72a84b5a094af84842b46ed931c242c09597?pretty=true&api_key=<your_key>", "lang": "curl,python", "comment": "Gets block data for a specific Litecoin transaction, specified by the hash of the block"}]}, {"endpoint": "/blockchain/{asset}/accounts", "languages": [{"url": "/blockchain/eth/accounts?pretty=true&api_key=<your_key>", "lang": "curl,python", "comment": "Gets list of accounts for Ethereum"}]}, {"endpoint": "/blockchain/{asset}/balance-updates", "languages": [{"url": "/blockchain/btc/balance-updates?pretty=true&api_key=<your_key>", "lang": "curl,python", "comment": "Gets balance updates for Bitcoin accounts"}]}, {"endpoint": "/blockchain/{asset}/blocks", "languages": [{"url": "/blockchain/eth/blocks?pretty=true&api_key=<your_key>", "lang": "curl,python", "comment": "Gets list of blocks for Ethereum"}]}, {"endpoint": "/blockchain/{asset}/blocks/{block_hash}", "languages": [{"url": "/blockchain/eth/blocks/0x27a2bd0fd3b3298dd8004c18aaad83374bdc5dbd36eac46bfe00772d88dba7cf?pretty=true&api_key=<your_key>", "lang": "curl,python", "comment": "Gets block data for a specific Ethereum block, specified by the hash of the block"}]}, {"endpoint": "/blockchain/{asset}/blocks/{block_hash}/transactions/{txid}", "languages": [{"url": "/blockchain/btc/blocks/0000000000000000000334e8637314d72d86a533c71f48da23e85e70a82cd38a/transactions/29d401526b06d55749034c10c3ee7ffd9ecab942c9b6852c963fa61103552729?pretty=true&api_key=<your_key>", "lang": "curl,python", "comment": "Gets full transaction data for a specific Bitcoin transaction in a block"}]}, {"endpoint": "/blockchain/{asset}/transaction-tracker", "languages": [{"url": "/blockchain/btc/transaction-tracker?pretty=true&api_key=<your_key>", "lang": "curl,python", "comment": "Gets data on the status of the most recent bitcoin transactions"}]}, {"endpoint": "/blockchain/{asset}/transactions", "languages": [{"url": "/blockchain/ada/transactions?pretty=true&api_key=<your_key>", "lang": "curl,python", "comment": "Gets list of transactions on Cardano"}]}, {"endpoint": "/blockchain/{asset}/transactions/{transaction_hash}", "languages": [{"url": "/blockchain/btc/transactions/29d401526b06d55749034c10c3ee7ffd9ecab942c9b6852c963fa61103552729?pretty=true&api_key=<your_key>", "lang": "curl,python", "comment": "Gets transaction data for a specific Bitcoin transaction"}]}, {"endpoint": "/catalog-all-v2/asset-chains", "languages": [{"url": "/catalog-all-v2/asset-chains?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-all-v2/asset-metrics", "languages": [{"url": "/catalog-all-v2/asset-metrics?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-all-v2/exchange-asset-metrics", "languages": [{"url": "/catalog-all-v2/exchange-asset-metrics?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-all-v2/exchange-metrics", "languages": [{"url": "/catalog-all-v2/exchange-metrics?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-all-v2/index-candles", "languages": [{"url": "/catalog-all-v2/index-candles?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-all-v2/index-levels", "languages": [{"url": "/catalog-all-v2/index-levels?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-all-v2/institution-metrics", "languages": [{"url": "/catalog-all-v2/institution-metrics?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-all-v2/market-candles", "languages": [{"url": "/catalog-all-v2/market-candles?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-all-v2/market-contract-prices", "languages": [{"url": "/catalog-all-v2/market-contract-prices?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-all-v2/market-funding-rates", "languages": [{"url": "/catalog-all-v2/market-funding-rates?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-all-v2/market-funding-rates-predicted", "languages": [{"url": "/catalog-all-v2/market-funding-rates-predicted?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-all-v2/market-greeks", "languages": [{"url": "/catalog-all-v2/market-greeks?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-all-v2/market-implied-volatility", "languages": [{"url": "/catalog-all-v2/market-implied-volatility?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-all-v2/market-liquidations", "languages": [{"url": "/catalog-all-v2/market-liquidations?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-all-v2/market-metrics", "languages": [{"url": "/catalog-all-v2/market-metrics?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-all-v2/market-openinterest", "languages": [{"url": "/catalog-all-v2/market-openinterest?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-all-v2/market-orderbooks", "languages": [{"url": "/catalog-all-v2/market-orderbooks?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-all-v2/market-quotes", "languages": [{"url": "/catalog-all-v2/market-quotes?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-all-v2/market-trades", "languages": [{"url": "/catalog-all-v2/market-trades?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-all-v2/mempool-feerates", "languages": [{"url": "/catalog-all-v2/mempool-feerates?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-all-v2/mining-pool-tips-summary", "languages": [{"url": "/catalog-all-v2/mining-pool-tips-summary?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-all-v2/pair-candles", "languages": [{"url": "/catalog-all-v2/pair-candles?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-all-v2/pair-metrics", "languages": [{"url": "/catalog-all-v2/pair-metrics?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-all-v2/transaction-tracker", "languages": [{"url": "/catalog-all-v2/transaction-tracker?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/blockchain-metadata/tagged-entities", "languages": [{"url": "/blockchain-metadata/tagged-entities?tags=ADDRESS_IS_CONTRACT,ADDRESS_IS_SWAPPER&pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-all/asset-alerts", "languages": [{"url": "/catalog-all/asset-alerts?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-all/asset-chains", "languages": [{"url": "/catalog-all/asset-chains?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-all/asset-metrics", "languages": [{"url": "/catalog-all/asset-metrics?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-all/assets", "languages": [{"url": "/catalog-all/assets?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-all/exchange-asset-metrics", "languages": [{"url": "/catalog-all/exchange-asset-metrics?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-all/exchange-assets", "languages": [{"url": "/catalog-all/exchange-assets?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-all/exchange-metrics", "languages": [{"url": "/catalog-all/exchange-metrics?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-all/exchanges", "languages": [{"url": "/catalog-all/exchanges?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-all/index-candles", "languages": [{"url": "/catalog-all/index-candles?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-all/indexes", "languages": [{"url": "/catalog-all/indexes?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-all/institution-metrics", "languages": [{"url": "/catalog-all/institution-metrics?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-all/institutions", "languages": [{"url": "/catalog-all/institutions?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-all/market-candles", "languages": [{"url": "/catalog-all/market-candles?pretty=true&limit=100&api_key=<your_key>", "lang": "curl"}, {"url": "/catalog-all/market-candles?pretty=true&api_key=<your_key>", "lang": "python"}]}, {"endpoint": "/catalog-all/market-contract-prices", "languages": [{"url": "/catalog-all/market-contract-prices?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-all/market-funding-rates", "languages": [{"url": "/catalog-all/market-funding-rates?pretty=true&limit=100&api_key=<your_key>", "lang": "curl"}, {"url": "/catalog-all/market-funding-rates?pretty=true&api_key=<your_key>", "lang": "python"}]}, {"endpoint": "/catalog-all/market-greeks", "languages": [{"url": "/catalog-all/market-greeks?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-all/market-implied-volatility", "languages": [{"url": "/catalog-all/market-implied-volatility?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-all/market-liquidations", "languages": [{"url": "/catalog-all/market-liquidations?pretty=true&limit=100&api_key=<your_key>", "lang": "curl"}, {"url": "/catalog-all/market-liquidations?pretty=true&api_key=<your_key>", "lang": "python"}]}, {"endpoint": "/catalog-all/market-metrics", "languages": [{"url": "/catalog-all/market-metrics?pretty=true&limit=100&api_key=<your_key>", "lang": "curl"}, {"url": "/catalog-all/market-metrics?pretty=true&api_key=<your_key>", "lang": "python"}]}, {"endpoint": "/catalog-all/market-openinterest", "languages": [{"url": "/catalog-all/market-openinterest?pretty=true&limit=100&api_key=<your_key>", "lang": "curl"}, {"url": "/catalog-all/market-openinterest?pretty=true&api_key=<your_key>", "lang": "python"}]}, {"endpoint": "/catalog-all/market-orderbooks", "languages": [{"url": "/catalog-all/market-orderbooks?pretty=true&limit=100&api_key=<your_key>", "lang": "curl"}, {"url": "/catalog-all/market-orderbooks?pretty=true&api_key=<your_key>", "lang": "python"}]}, {"endpoint": "/catalog-all/market-quotes", "languages": [{"url": "/catalog-all/market-quotes?pretty=true&limit=100&api_key=<your_key>", "lang": "curl"}, {"url": "/catalog-all/market-quotes?pretty=true&api_key=<your_key>", "lang": "python"}]}, {"endpoint": "/catalog-all/market-trades", "languages": [{"url": "/catalog-all/market-trades?pretty=true&limit=100&api_key=<your_key>", "lang": "curl"}, {"url": "/catalog-all/market-trades?pretty=true&api_key=<your_key>", "lang": "python"}]}, {"endpoint": "/catalog-all/markets", "languages": [{"url": "/catalog-all/markets?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-all/mempool-feerates", "languages": [{"url": "/catalog-all/mempool-feerates?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-all/metrics", "languages": [{"url": "/catalog-all/metrics?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-all/mining-pool-tips-summary", "languages": [{"url": "/catalog-all/mining-pool-tips-summary?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-all/pair-candles", "languages": [{"url": "/catalog-all/pair-candles?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-all/pair-metrics", "languages": [{"url": "/catalog-all/pair-metrics?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-all/pairs", "languages": [{"url": "/catalog-all/pairs?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-all/transaction-tracker", "languages": [{"url": "/catalog-all/transaction-tracker?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-v2/asset-chains", "languages": [{"url": "/catalog-v2/asset-chains?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-v2/asset-metrics", "languages": [{"url": "/catalog-v2/asset-metrics?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-v2/exchange-asset-metrics", "languages": [{"url": "/catalog-v2/exchange-asset-metrics?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-v2/exchange-metrics", "languages": [{"url": "/catalog-v2/exchange-metrics?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-v2/index-candles", "languages": [{"url": "/catalog-v2/index-candles?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-v2/index-levels", "languages": [{"url": "/catalog-v2/index-levels?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-v2/institution-metrics", "languages": [{"url": "/catalog-v2/institution-metrics?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-v2/market-candles", "languages": [{"url": "/catalog-v2/market-candles?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-v2/market-contract-prices", "languages": [{"url": "/catalog-v2/market-contract-prices?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-v2/market-funding-rates", "languages": [{"url": "/catalog-v2/market-funding-rates?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-v2/market-funding-rates-predicted", "languages": [{"url": "/catalog-v2/market-funding-rates-predicted?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-v2/market-greeks", "languages": [{"url": "/catalog-v2/market-greeks?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-v2/market-implied-volatility", "languages": [{"url": "/catalog-v2/market-implied-volatility?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-v2/market-liquidations", "languages": [{"url": "/catalog-v2/market-liquidations?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-v2/market-metrics", "languages": [{"url": "/catalog-v2/market-metrics?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-v2/market-openinterest", "languages": [{"url": "/catalog-v2/market-openinterest?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-v2/market-orderbooks", "languages": [{"url": "/catalog-v2/market-orderbooks?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-v2/market-quotes", "languages": [{"url": "/catalog-v2/market-quotes?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-v2/market-trades", "languages": [{"url": "/catalog-v2/market-trades?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-v2/mempool-feerates", "languages": [{"url": "/catalog-v2/mempool-feerates?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-v2/mining-pool-tips-summary", "languages": [{"url": "/catalog-v2/mining-pool-tips-summary?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-v2/pair-candles", "languages": [{"url": "/catalog-v2/pair-candles?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-v2/pair-metrics", "languages": [{"url": "/catalog-v2/pair-metrics?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-v2/transaction-tracker", "languages": [{"url": "/catalog-v2/transaction-tracker?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-v2/blockchain-v2/accounts", "languages": [{"url": "/catalog-v2/blockchain-v2/accounts?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-v2/blockchain-v2/balance-updates", "languages": [{"url": "/catalog-v2/blockchain-v2/balance-updates?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-v2/blockchain-v2/blocks", "languages": [{"url": "/catalog-v2/blockchain-v2/blocks?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog-v2/blockchain-v2/transactions", "languages": [{"url": "/catalog-v2/blockchain-v2/transactions?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog/asset-alerts", "languages": [{"url": "/catalog/asset-alerts?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog/asset-chains", "languages": [{"url": "/catalog/asset-chains?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog/asset-metrics", "languages": [{"url": "/catalog/asset-metrics?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog/assets", "languages": [{"url": "/catalog/assets?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog/exchange-asset-metrics", "languages": [{"url": "/catalog/exchange-asset-metrics?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog/exchange-assets", "languages": [{"url": "/catalog/exchange-assets?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog/exchange-metrics", "languages": [{"url": "/catalog/exchange-metrics?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog/exchanges", "languages": [{"url": "/catalog/exchanges?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog/index-candles", "languages": [{"url": "/catalog/index-candles?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog/indexes", "languages": [{"url": "/catalog/indexes?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog/institution-metrics", "languages": [{"url": "/catalog/institution-metrics?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog/institutions", "languages": [{"url": "/catalog/institutions?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog/market-candles", "languages": [{"url": "/catalog/market-candles?pretty=true&limit=100&api_key=<your_key>", "lang": "curl"}, {"url": "/catalog/market-candles?pretty=true&api_key=<your_key>", "lang": "python"}]}, {"endpoint": "/catalog/market-contract-prices", "languages": [{"url": "/catalog/market-contract-prices?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog/market-funding-rates", "languages": [{"url": "/catalog/market-funding-rates?pretty=true&limit=100&api_key=<your_key>", "lang": "curl"}, {"url": "/catalog/market-funding-rates?pretty=true&api_key=<your_key>", "lang": "python"}]}, {"endpoint": "/catalog/market-greeks", "languages": [{"url": "/catalog/market-greeks?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog/market-implied-volatility", "languages": [{"url": "/catalog/market-implied-volatility?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog/market-liquidations", "languages": [{"url": "/catalog/market-liquidations?pretty=true&limit=100&api_key=<your_key>", "lang": "curl"}, {"url": "/catalog/market-liquidations?pretty=true&api_key=<your_key>", "lang": "python"}]}, {"endpoint": "/catalog/market-metrics", "languages": [{"url": "/catalog/market-metrics?pretty=true&limit=100&api_key=<your_key>", "lang": "curl"}, {"url": "/catalog/market-metrics?pretty=true&api_key=<your_key>", "lang": "python"}]}, {"endpoint": "/catalog/market-openinterest", "languages": [{"url": "/catalog/market-openinterest?pretty=true&limit=100&api_key=<your_key>", "lang": "curl"}, {"url": "/catalog/market-openinterest?pretty=true&api_key=<your_key>", "lang": "python"}]}, {"endpoint": "/catalog/market-orderbooks", "languages": [{"url": "/catalog/market-orderbooks?pretty=true&limit=100&api_key=<your_key>", "lang": "curl"}, {"url": "/catalog/market-orderbooks?pretty=true&api_key=<your_key>", "lang": "python"}]}, {"endpoint": "/catalog/market-quotes", "languages": [{"url": "/catalog/market-quotes?pretty=true&limit=100&api_key=<your_key>", "lang": "curl"}, {"url": "/catalog/market-quotes?pretty=true&api_key=<your_key>", "lang": "python"}]}, {"endpoint": "/catalog/market-trades", "languages": [{"url": "/catalog/market-trades?pretty=true&limit=100&api_key=<your_key>", "lang": "curl"}, {"url": "/catalog/market-trades?pretty=true&api_key=<your_key>", "lang": "python"}]}, {"endpoint": "/catalog/markets", "languages": [{"url": "/catalog/markets?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog/mempool-feerates", "languages": [{"url": "/catalog/mempool-feerates?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog/metrics", "languages": [{"url": "/catalog/metrics?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog/mining-pool-tips-summary", "languages": [{"url": "/catalog/mining-pool-tips-summary?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog/pair-candles", "languages": [{"url": "/catalog/pair-candles?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog/pair-metrics", "languages": [{"url": "/catalog/pair-metrics?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog/pairs", "languages": [{"url": "/catalog/pairs?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/catalog/transaction-tracker", "languages": [{"url": "/catalog/transaction-tracker?pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/constituent-snapshots/asset-metrics", "languages": [{"url": "/constituent-snapshots/asset-metrics?metric=volume_trusted_spot_usd_1d&api_key=<your_key>", "lang": "curl,python", "comment": "Gets snapshots of asset metric constituents"}]}, {"endpoint": "/constituent-timeframes/asset-metrics", "languages": [{"url": "/constituent-timeframes/asset-metrics?metric=volume_trusted_spot_usd_1d&api_key=<your_key>", "lang": "curl,python", "comment": "Gets timeframes of asset metric constituents"}]}, {"endpoint": "/jobs", "languages": [{"url": "/jobs?ids=ZjRjZDE2N2EtYzljYy00MjQ5LTk4ZWYtOTlkZDljZTE1ZDU3OjgzNTZkODQyLTA5ODMtNGM2NC1hMGE5LWY5MzBhZTMxODFiZg&api_key=<your_key>", "lang": "curl,python", "comment": "Gets job details by ID"}]}, {"endpoint": "/profile/assets", "languages": [{"url": "/profile/assets?api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/profile/networks", "languages": [{"url": "/profile/networks?api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/reference-data/assets", "languages": [{"url": "/reference-data/assets?api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/reference-data/asset-metrics", "languages": [{"url": "/reference-data/asset-metrics?api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/reference-data/exchanges", "languages": [{"url": "/reference-data/exchanges?api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/reference-data/exchange-asset-metrics", "languages": [{"url": "/reference-data/exchange-asset-metrics?api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/reference-data/exchange-metrics", "languages": [{"url": "/reference-data/exchange-metrics?api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/reference-data/indexes", "languages": [{"url": "/reference-data/indexes?api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/reference-data/institution-metrics", "languages": [{"url": "/reference-data/institution-metrics?api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/reference-data/markets", "languages": [{"url": "/reference-data/markets?api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/reference-data/market-metrics", "languages": [{"url": "/reference-data/market-metrics?api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/reference-data/pairs", "languages": [{"url": "/reference-data/pair-metrics?api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/reference-data/pair-metrics", "languages": [{"url": "/reference-data/pair-metrics?api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/security-master/assets", "languages": [{"url": "/security-master/assets?assets=btc,eth&pretty=true&api_key=<your_key>", "lang": "curl,python", "comment": "Gets a metadata for btc and eth assets"}]}, {"endpoint": "/security-master/markets", "languages": [{"url": "/security-master/markets?type=spot&pretty=true&api_key=<your_key>", "lang": "curl,python", "comment": "Gets a list of spot markets"}]}, {"endpoint": "/taxonomy-metadata/assets", "languages": [{"url": "/taxonomy-metadata/assets?api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/taxonomy/assets", "languages": [{"url": "/taxonomy/assets?api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/timeseries-stream/asset-metrics", "languages": [{"url": "/timeseries-stream/asset-metrics?assets=btc,eth&metrics=ReferenceRate&frequency=1s&pretty=true&api_key=<your_key>", "lang": "js,python_ws", "comment": "This is getting Reference Rates for Bitcoin and Ethereum at a frequency of 1 second"}]}, {"endpoint": "/timeseries-stream/asset-quotes", "languages": [{"url": "/timeseries-stream/asset-quotes?assets=btc&pretty=true&api_key=<your_key>", "lang": "js,python_ws"}]}, {"endpoint": "/timeseries-stream/index-levels", "languages": [{"url": "/timeseries-stream/index-levels?indexes=CMBIBTC&pretty=true&api_key=<your_key>", "lang": "js,python_ws", "comment": "Streams the levels for CMBIBTC index"}]}, {"endpoint": "/timeseries-stream/market-candles", "languages": [{"url": "/timeseries-stream/market-candles?markets=bitstamp-btc-usd-spot&pretty=true&api_key=<your_key>", "lang": "js,python_ws"}]}, {"endpoint": "/timeseries-stream/market-orderbooks", "languages": [{"url": "/timeseries-stream/market-orderbooks?markets=coinbase-btc-usd-spot&pretty=true&api_key=<your_key>", "lang": "js,python_ws"}]}, {"endpoint": "/timeseries-stream/market-quotes", "languages": [{"url": "/timeseries-stream/market-quotes?markets=coinbase-btc-usd-spot&pretty=true&api_key=<your_key>", "lang": "js,python_ws"}]}, {"endpoint": "/timeseries-stream/market-trades", "languages": [{"url": "/timeseries-stream/market-trades?markets=coinbase-btc-usd-spot&pretty=true&api_key=<your_key>", "lang": "js,python_ws"}]}, {"endpoint": "/timeseries-stream/market-liquidations", "languages": [{"url": "/timeseries-stream/market-liquidations?markets=coinbase-BTCUSDT-future&pretty=true&api_key=<your_key>", "lang": "js,python_ws"}]}, {"endpoint": "/timeseries-stream/market-openinterest", "languages": [{"url": "/timeseries-stream/market-openinterest?markets=binance-BTCUSDT-future&pretty=true&api_key=<your_key>", "lang": "js,python_ws"}]}, {"endpoint": "/timeseries-stream/pair-quotes", "languages": [{"url": "/timeseries-stream/pair-quotes?pairs=btc-usd&pretty=true&api_key=<your_key>", "lang": "js,python_ws"}]}, {"endpoint": "/timeseries/asset-metrics", "languages": [{"url": "/timeseries/asset-metrics?assets=btc&metrics=PriceUSD,FlowInGEMUSD&frequency=1d&pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/timeseries/defi-balance-sheets", "languages": [{"url": "/timeseries/defi-balance-sheets?defi_protocols=aave_v2_eth&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/timeseries/exchange-asset-metrics", "languages": [{"url": "/timeseries/exchange-asset-metrics?exchange_assets=binance-btc&metrics=volume_reported_spot_usd_1d&frequency=1d&pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/timeseries/exchange-metrics", "languages": [{"url": "/timeseries/exchange-metrics?exchanges=binance&metrics=volume_reported_spot_usd_1d&frequency=1d&pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/timeseries/index-candles", "languages": [{"url": "/timeseries/index-candles?start_time=2022-06-28&paging_from=start&indexes=CMBIBTC&frequency=1d&pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/timeseries/index-constituents", "languages": [{"url": "/timeseries/index-constituents?start_time=2020-01-01&paging_from=start&indexes=CMBIBTC&pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/timeseries/index-levels", "languages": [{"url": "/timeseries/index-levels?start_time=2020-01-01&paging_from=start&indexes=CMBIBTC&frequency=1d-ny-close&pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/timeseries/institution-metrics", "languages": [{"url": "/timeseries/institution-metrics?institutions=grayscale&metrics=btc_total_assets&frequency=1d&pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/timeseries/market-candles", "languages": [{"url": "/timeseries/market-candles?start_time=2020-01-01&paging_from=start&markets=coinbase-btc-usd-spot&pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/timeseries/market-contract-prices", "languages": [{"url": "/timeseries/market-contract-prices?start_time=2020-01-01&paging_from=start&markets=deribit-ETH-25MAR22-1200-P-option&pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/timeseries/market-funding-rates", "languages": [{"url": "/timeseries/market-funding-rates?start_time=2020-01-01&paging_from=start&markets=bitmex-XBTUSD-future&pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/timeseries/market-funding-rates-predicted", "languages": [{"url": "/timeseries/market-funding-rates-predicted?start_time=2023-01-01&paging_from=start&markets=deribit-XRP_USDC-PERPETUAL-future&pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/timeseries/market-greeks", "languages": [{"url": "/timeseries/market-greeks?start_time=2020-01-01&paging_from=start&markets=deribit-ETH-25MAR22-1200-P-option&pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/timeseries/market-implied-volatility", "languages": [{"url": "/timeseries/market-implied-volatility?start_time=2020-01-01&paging_from=start&markets=deribit-ETH-25MAR22-1200-P-option&pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/timeseries/market-liquidations", "languages": [{"url": "/timeseries/market-liquidations?start_time=2020-01-01&paging_from=start&markets=bitmex-XBTUSD-future&pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/timeseries/market-metrics", "languages": [{"url": "/timeseries/market-metrics?markets=binance-BTCUSDT-future&metrics=liquidations_reported_future_buy_usd_5m&frequency=5m&pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/timeseries/market-openinterest", "languages": [{"url": "/timeseries/market-openinterest?start_time=2020-01-01&paging_from=start&markets=bitmex-XBTUSD-future&pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/timeseries/market-orderbooks", "languages": [{"url": "/timeseries/market-orderbooks?start_time=2020-01-01&paging_from=start&markets=coinbase-btc-usd-spot&pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/timeseries/market-quotes", "languages": [{"url": "/timeseries/market-quotes?start_time=2020-01-01&paging_from=start&markets=coinbase-btc-usd-spot&pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/timeseries/market-trades", "languages": [{"url": "/timeseries/market-trades?start_time=2020-01-01&paging_from=start&markets=coinbase-btc-usd-spot&pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/timeseries/mining-pool-tips-summary", "languages": [{"url": "/timeseries/mining-pool-tips-summary?assets=btc&page_size=3&pretty=true&api_key=<your_key>", "lang": "curl,python"}]}, {"endpoint": "/timeseries/pair-candles", "languages": [{"url": "/timeseries/pair-candles?pairs=btc-usd&frequency=1d&pretty=true&api_key=<your_key>", "lang": "curl"}, {"url": "/timeseries/pair-candles?pairs=btc-usd&frequency=1d&pretty=true&api_key=<your_key>", "lang": "python"}]}, {"endpoint": "/timeseries/pair-metrics", "languages": [{"url": "/timeseries/pair-metrics?pairs=btc-usd&metrics=volume_trusted_spot_usd_1d&frequency=1d&pretty=true&api_key=<your_key>", "lang": "curl,python"}]}]}