- label: Shell
  source: |
    curl --compressed "https://api.coinmetrics.io/v4/timeseries/market-funding-rates-predicted?start_time=2023-01-01&paging_from=start&markets=deribit-XRP_USDC-PERPETUAL-future&pretty=true&api_key=<your_key>"
- label: Python
  source: |
    import requests
    response = requests.get('https://api.coinmetrics.io/v4/timeseries/market-funding-rates-predicted?start_time=2023-01-01&paging_from=start&markets=deribit-XRP_USDC-PERPETUAL-future&pretty=true&api_key=<your_key>').json()
    print(response)
