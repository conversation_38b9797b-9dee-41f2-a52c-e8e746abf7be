- label: Shell
  source: |
    curl -X PUT --compressed "https://api.coinmetrics.io/v4/blockchain-job/balance-updates?api_key=<your_key>" \
      --header "Content-Type: application/x-www-form-urlencoded" \
      --data-urlencode "assets=sol" \
      --data-urlencode "accounts=account1,account2" \
      --data-urlencode "start_time=2023-07-04T00:00:00.000000000Z" \
      --data-urlencode "end_time=2024-07-04T00:00:00.000000000Z"
- label: Python
  source: |
    import requests
    data = {
        "assets": "sol",
        "accounts": "account1,account2",
        "start_time": "2023-07-04T00:00:00.000000000Z",
        "end_time": "2024-07-04T00:00:00.000000000Z"
    }
    response = requests.put('https://api.coinmetrics.io/v4/blockchain-job/balance-updates?api_key=<your_key>',data=data).json()
    print(response)
