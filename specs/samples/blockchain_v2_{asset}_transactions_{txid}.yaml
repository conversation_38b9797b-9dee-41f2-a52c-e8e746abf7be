- label: Shell
  source: |
    # Gets block data for a specific Litecoin transaction, specified by the hash of the block
    curl --compressed "https://api.coinmetrics.io/v4/blockchain-v2/ltc/transactions/3345cbbcc53b8a0113951b69f98a72a84b5a094af84842b46ed931c242c09597?pretty=true&api_key=<your_key>"
- label: Python
  source: |
    # Gets block data for a specific Litecoin transaction, specified by the hash of the block
    import requests
    response = requests.get('https://api.coinmetrics.io/v4/blockchain-v2/ltc/transactions/3345cbbcc53b8a0113951b69f98a72a84b5a094af84842b46ed931c242c09597?pretty=true&api_key=<your_key>').json()
    print(response)
