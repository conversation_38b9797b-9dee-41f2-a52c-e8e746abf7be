- label: Shell
  source: |
    curl --compressed "https://api.coinmetrics.io/v4/timeseries/market-openinterest?start_time=2020-01-01&paging_from=start&markets=bitmex-XBTUSD-future&pretty=true&api_key=<your_key>"
- label: Python
  source: |
    import requests
    response = requests.get('https://api.coinmetrics.io/v4/timeseries/market-openinterest?start_time=2020-01-01&paging_from=start&markets=bitmex-XBTUSD-future&pretty=true&api_key=<your_key>').json()
    print(response)
