- label: Shell
  source: |
    # Gets transaction data for a specific Bitcoin transaction
    curl --compressed "https://api.coinmetrics.io/v4/blockchain/btc/transactions/29d401526b06d55749034c10c3ee7ffd9ecab942c9b6852c963fa61103552729?pretty=true&api_key=<your_key>"
- label: Python
  source: |
    # Gets transaction data for a specific Bitcoin transaction
    import requests
    response = requests.get('https://api.coinmetrics.io/v4/blockchain/btc/transactions/29d401526b06d55749034c10c3ee7ffd9ecab942c9b6852c963fa61103552729?pretty=true&api_key=<your_key>').json()
    print(response)
