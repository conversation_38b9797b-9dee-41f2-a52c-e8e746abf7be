import com.google.protobuf.gradle.generateProtoTasks
import com.google.protobuf.gradle.ofSourceSet
import com.google.protobuf.gradle.protoc
import io.coinmetrics.api.OpenApiProtectedFilesGenerateTask
import io.coinmetrics.api.OpenApiRedoclyFileGenerateTask
import io.coinmetrics.api.OpenApiSamplesGenerateTask
import org.openapitools.generator.gradle.plugin.tasks.GenerateTask
import org.openapitools.generator.gradle.plugin.tasks.ValidateTask

plugins {
    java
    id("io.coinmetrics.api.conventions-app")
    id("org.openapi.generator")
    alias(libs.plugins.protobuf)
}

application {
    mainClass.set("io.coinmetrics.api.ServerKt")
}

sourceSets {
    val main by getting {
        java {
            setSrcDirs(listOf("$rootDir/generated/src/main/java"))
        }
        kotlin {
            setSrcDirs(listOf("src/main/kotlin", "$rootDir/generated/src/main/kotlin"))
        }
        resources {
            setSrcDirs(listOf("src/main/resources"))
        }
        // Assuming "proto" is defined as an additional source directory
        proto {
            setSrcDirs(listOf("$rootDir/resources/market_data"))
        }
    }
    val test by getting {
        kotlin {
            setSrcDirs(listOf("src/test/kotlin"))
        }
        resources {
            setSrcDirs(listOf("src/test/resources"))
        }
        compileClasspath += sourceSets["main"].output
        runtimeClasspath += sourceSets["main"].output
    }
}

val javaAgent: Configuration by configurations.creating {
    isTransitive = false
}

dependencies {
    implementation(project(":common"))

    implementation(libs.bundles.jackson)
    implementation(libs.bundles.prometheus)
    implementation(libs.bundles.kotlin.coroutines)

    implementation(libs.kotlin.reflect)
    implementation(libs.google.protobuf)
    implementation(libs.google.guava)
    implementation(libs.kotlin.coroutines.slf4j)
    implementation(libs.kotlin.coroutines.guava)

    implementation(libs.coinmetrics.databases)
    implementation(libs.coinmetrics.s3.databases)
    implementation(libs.coinmetrics.shared.files)
    implementation(libs.coinmetrics.defi.client)
    implementation(libs.coinmetrics.healthchecks)
    implementation(libs.coinmetrics.atlas.codec)
    implementation(libs.coinmetrics.queues)
    implementation(libs.coinmetrics.book.streams.client)
    implementation(libs.coinmetrics.http.server)
    implementation(libs.coinmetrics.api.nd.jobs)

    implementation(libs.slf4j)
    implementation(libs.logback.classic)
    implementation(libs.logback.json.classic)
    runtimeOnly(libs.logback.jackson)

    javaAgent(libs.kotlin.coroutines.debug)

    // database access
    implementation(libs.postgres)
    implementation(libs.hikaricp)

    // for an S3 API client
    implementation(libs.jackson.dataformat.xml)

    testImplementation(libs.junit.api)
    testImplementation(libs.junit.params)
    testRuntimeOnly(libs.junit.engine)
    testImplementation(libs.bundles.testcontainers)

    testImplementation(project(":statistics-generator"))
    testImplementation(libs.kotlin.coroutines.test)

    testImplementation(libs.apache.commons.csv)
    testImplementation(libs.assertj.core)
    testImplementation(libs.kafka.clients)
    testImplementation(libs.mockk)

    testImplementation(libs.temporal.testing)
}

tasks {
    register<GenerateTask>("openApiMainSpecGenerate") {
        group = "coinmetrics"
        generatorName.set("io.coinmetrics.apiservercodegen.ApiServerCodegen")
        templateDir.set("$rootDir/specs/templates")
        inputSpec.set("$rootDir/specs/openapi.yaml")
        outputDir.set("$rootDir/generated")
        apiPackage.set("io.coinmetrics.api.endpoints")
        packageName.set("io.coinmetrics.api")
        configOptions.set(mapOf("dateLibrary" to "java8"))
        globalProperties.set(
            mapOf(
                "apis" to "",
                "models" to "",
                "supportingFiles" to "Paths.kt",
                // "debugOperations" to "true",
            ),
        )
        typeMappings.set(
            mapOf(
                "string+PagingFrom" to "PagingFrom",
            ),
        )
        importMappings.set(
            mapOf(
                "PagingFrom" to "io.coinmetrics.api.models.PagingFrom",
            ),
        )
    }
    register<OpenApiProtectedFilesGenerateTask>("openApiProtectedFilesGenerate") {
        group = "coinmetrics"
    }
    register<OpenApiSamplesGenerateTask>("openApiSamplesGenerate") {
        group = "coinmetrics"
    }
    register<OpenApiRedoclyFileGenerateTask>("openApiRedoclyFileGenerateTask") {
        group = "coinmetrics"
    }
    register<ValidateTask>("openApiMainSpecValidate") {
        group = "coinmetrics"
        inputSpec.set("$rootDir/specs/openapi.yaml")
        recommend.set(false)
    }
    register<ValidateTask>("openApiValidatePublicRedocly") {
        group = "coinmetrics"
        inputSpec.set("$rootDir/specs/generated/openapi-public-redocly.yaml")
        recommend.set(false)
    }
    register<ValidateTask>("openApiValidatePublic") {
        group = "coinmetrics"
        inputSpec.set("$rootDir/specs/generated/openapi-public.yaml")
        recommend.set(false)
    }
    register<ValidateTask>("openApiValidateInternal") {
        group = "coinmetrics"
        inputSpec.set("$rootDir/specs/generated/openapi-internal.yaml")
        recommend.set(false)
    }
}

tasks {
    withType<Test> {
        maxParallelForks = findProperty("test.maxParallelForks")
            ?.toString()
            ?.toIntOrNull()
            ?: (Runtime.getRuntime().availableProcessors() / 2).coerceAtLeast(1).coerceAtMost(4)

        if (maxParallelForks > 1) {
            logger.warn("Will run tests in $maxParallelForks parallel JVMs")
        }
    }
    register<Copy>("copyJavaAgent") {
        val libsDir =
            layout.buildDirectory
                .dir("libs-java-agent")
                .get()
                .asFile
        configurations.getByName("javaAgent").resolvedConfiguration.resolvedArtifacts.forEach { artifact ->
            from(artifact.file)
            into(libsDir)
            rename { "${artifact.name}.${artifact.extension}" }
        }
    }
}

protobuf {
    with(protobuf) {
        generatedFilesBaseDir = "$rootDir/generated/src"
        protoc {
            artifact = "com.google.protobuf:protoc:3.21.7"
        }
        generateProtoTasks {
            ofSourceSet("main")
        }
    }
}

tasks.named("compileKotlin") {
    dependsOn(tasks.named("openApiValidatePublicRedocly"))
}
tasks.named("openApiValidatePublicRedocly") {
    dependsOn(tasks.named("openApiRedoclyFileGenerateTask"))
}
tasks.named("openApiRedoclyFileGenerateTask") {
    dependsOn(tasks.named("openApiValidatePublic"))
}
tasks.named("openApiValidatePublic") {
    dependsOn(tasks.named("openApiValidateInternal"))
}
tasks.named("openApiValidateInternal") {
    dependsOn(tasks.named("openApiProtectedFilesGenerate"))
}
tasks.named("openApiProtectedFilesGenerate") {
    dependsOn(tasks.named("openApiMainSpecGenerate"))
}
tasks.named("openApiMainSpecGenerate") {
    dependsOn(tasks.named("openApiMainSpecValidate"))
}
tasks.named("openApiMainSpecValidate") {
    dependsOn(tasks.named("openApiSamplesGenerate"))
}
tasks.named("runKtlintCheckOverMainSourceSet") {
    dependsOn(tasks.named("generateProto"))
    dependsOn(tasks.named("openApiMainSpecGenerate"))
}
tasks.named("runKtlintFormatOverMainSourceSet") {
    dependsOn(tasks.named("generateProto"))
    dependsOn(tasks.named("openApiMainSpecGenerate"))
}
tasks.named("assemble") {
    dependsOn("copyJavaAgent")
}
