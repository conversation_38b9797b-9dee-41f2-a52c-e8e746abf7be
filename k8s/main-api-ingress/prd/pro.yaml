---
envName: production

originCertificate:
  create: true
  name: cloudflare

api:
  enable: true
  domains:
    - name: api-${LOCATION}.coinmetrics.io
      fidelityNamespace: ${FIDELITY_NAMESPACE}

    - name: api.coinmetrics.io
      fidelityNamespace: ${FIDELITY_NAMESPACE}

    - name: api-${LOCATION}.coinmetrics2.io
      fidelityNamespace: ${FIDELITY_NAMESPACE}

    - name: api.coinmetrics2.io
      fidelityNamespace: ${FIDELITY_NAMESPACE}

    - name: api-uat-${LOCATION}.coinmetrics2.io
      fidelityNamespace: fidelity-api1-uat

    - name: api-uat.coinmetrics2.io
      fidelityNamespace: fidelity-api1-uat

  serviceLevel: pro
  labelSelector:
    app.kubernetes.io/name: api4
    api4.coinmetrics.io/service-level: pro
  upstreamTries: 3

docs:
  enable: true
  domains:
    - name: docs-${LOCATION}.coinmetrics.io
      fidelityNamespace: ${FIDELITY_NAMESPACE}
    - name: docs.coinmetrics.io
      fidelityNamespace: ${FIDELITY_NAMESPACE}
    - name: docs.coinmetrics2.io
      fidelityNamespace: ${FIDELITY_NAMESPACE}
  service: api4-docs
  upstream: gitbook-docs.coinmetrics.io
  upstreamTries: 2
