{{- if .Values.api.enable }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "api4-ingress.fullname" . }}
  labels:
    {{- include "api4-ingress.labels" . | nindent 4 }}
spec:
  ipFamilyPolicy: SingleStack
  ipFamilies:
    - IPv6
  ports:
    - name: http
      port: 8080
      protocol: TCP
      targetPort: 8080
  selector:
    {{- toYaml .Values.api.labelSelector | nindent 4 }}
{{- end }}
