{{ range $domain := .Values.api.domains }}
---
apiVersion: k8s.nginx.org/v1
kind: VirtualServerRoute
metadata:
  name: {{ include "streaming-trades-api-ingress.fullname" $ }}-{{ $domain | replace "." "-" }}
spec:
  host: "{{ $domain }}"
  upstreams:
    - name: streaming-trades-api
      service: {{ include "streaming-trades-api-ingress.fullname" $ }}
      port: 8080
      next-upstream: error timeout invalid_header http_500 http_502 http_503 http_504
      next-upstream-tries: {{ $.Values.api.upstreamTries }}
      fail-timeout: "0"
      max-fails: 0
      buffering: false
      keepalive: 16
      read-timeout: 15m
  subroutes:
    - path: /v4/timeseries-stream/market-trades
      action:
        proxy:
          requestHeaders:
            set:
              - name: Host
                value: ${http_host}
          upstream: streaming-trades-api
      location-snippets: |
        expires -1;
{{ end }}
