#  helm secrets dec api4-hel1-secret-values.yaml
#  helm secrets enc api4-hel1-secret-values.yaml
secretEnv:
    PGPASSWORD: ENC[AES256_GCM,data:qizRm5MVog==,iv:fFN1/OR6CHpILovF49tNbhcPuZHamznBR5XozZ86OjU=,tag:Rygeg+e6mo6KDuj8es5B3g==,type:str]
    API_BOOKS_TIER_COLD_S3_SECRET_KEY: ENC[AES256_GCM,data:/ZstHFnROLOdofPSOmCZdW1G2jvHoR24LtYhVCwSpi7flPrUS0hz/w==,iv:Huqvg4MH3eglvIjbOn4X5a9Ej4sWJlF5K22ASKRXx/I=,tag:NMkpDmXi09np0nXfcKn8jA==,type:str]
    API_STATISTICS_S3_SECRET_KEY: ENC[AES256_GCM,data:ckbVZ8woig2FMQlU+CwyvHwxa5OxJe7ItW+IQSns98s=,iv:IpjNZ2X709FKf5iTlFgNSzSGFvXeqE6z0yL181iAJ2Y=,tag:uDksCgxz2CwnISUDebuztQ==,type:str]
    API_TEMPORAL_CLIENT_CERTIFICATE_CONTENT: ENC[AES256_GCM,data: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,iv:lhGTTb5QVgLKn9OlpgKYd+wKRVxBZc+7sjn7da0/VyQ=,tag:9Q9AOcHmnFuqZ+HJxlG6UQ==,type:str]
    API_TEMPORAL_CLIENT_KEY_CONTENT: ENC[AES256_GCM,data: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,iv:bWs6B9LXcQXcGz8/b/YW2GEgxQiQ4WhMfRRjIo2Mnv0=,tag:zaxQplRIbZHabYv5TkpVxQ==,type:str]
sops:
    kms: []
    gcp_kms: []
    azure_kv: []
    hc_vault: []
    age:
        - recipient: age1g7qfrkl0gwpt2agyz6qn0gltfdjzqy843hg03rz9n6yugm8fwsgqjmrf8v
          enc: |
            -----BEGIN AGE ENCRYPTED FILE-----
            YWdlLWVuY3J5cHRpb24ub3JnL3YxCi0+IFgyNTUxOSA0OEhML1ZnV3V5eHNxVmRV
            LzlzVC9zakE1UVgrNlVCbm1aRDdHRm5rQW1RCkEwV2FLaEwzOTJCVEpzYUNoTkFO
            M05idEE2dFM5aTlQVS8rRjdoYjd2TDgKLS0tIERvaGxaM2U1MEgvU2JNcENkS2hC
            UkdrZ0NUQmVzdTZ4SkM5VG01N1dVNmsK9lJ+phXMJOIPReqwcNo+pUKfPYeO6zj5
            orYItEj1yjLFGuTzNmxOwTTEBN6+Ye0en1VBmVNVemtHY6ZbNagj8w==
            -----END AGE ENCRYPTED FILE-----
    lastmodified: "2024-08-15T11:16:01Z"
    mac: ENC[AES256_GCM,data:wfp1dFliVTjtEVpkvWfAg8PrMtKrPpQTKWhx5qM6yzs4Kjm11hsLNxPtx9qGDOJdJlXpsvtTZ4VR+Sx5VFgID5KqODSFiezBNHZHEKdQlsUj95mR0crW2SmBlK+WIxZR7hYvjiTBwIpx4aahAkvQqHY0gGy+b8NBRIsCa4AA90M=,iv:eZMt8uhVtygU0oL+Dcfj5RMvCeTzsPqqz41OmuYCUGw=,tag:N5tpkrnIaEtNNiJ6R0NvVg==,type:str]
    pgp: []
    encrypted_regex: ^(data|stringData|secretEnv|originCertificate)$
    version: 3.8.0
