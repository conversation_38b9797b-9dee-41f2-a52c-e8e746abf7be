env:
  API_ENV: staging
  API_AMS_HOST: "ams-api.ams-api"
  API_DB_CONNECTION_TIMEOUT_MS: 30000
  API_DB_RR_SCHEMA: staging
  API_DB_RR_NEW_SCHEMA: production
  API_DB_CANDLES_MARKET_SCHEMA: staging
  API_DB_METRICS_SCHEMA: staging
  API_DB_PRINCIPAL_PRICE_SCHEMA: production
  API_DB_TRADES_SPOT: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-trades-spot-stg-cdev1-p?user=postgres&password=$(PGPASSWORD)"
  API_DB_TRADES_SPOT_REQUIRE_REDUNDANCY: "false"
  API_DB_TRADES_DERIV: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-trades-derivatives-stg-cdev1-p?user=postgres&password=$(PGPASSWORD)"
  API_DB_TRADES_DERIV_REQUIRE_REDUNDANCY: "false"
  API_DB_BOOKS: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-books-stg-cdev1-p?user=postgres&password=$(PGPASSWORD)"
  API_DB_BOOKS_REQUIRE_REDUNDANCY: "false"
  API_DB_FUTURES: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-derivatives-stg-cdev1-p?user=postgres&password=$(PGPASSWORD)"
  API_DB_FUTURES_REQUIRE_REDUNDANCY: "false"
  API_DB_RR: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-indices-1?user=postgres&password=$(PGPASSWORD), postgresql://pgbouncer.pgbouncer.svc:5432/pg-indices-2?user=postgres&password=$(PGPASSWORD)"
  API_DB_RR_NEW: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-rates-1?user=postgres&password=$(PGPASSWORD), postgresql://pgbouncer.pgbouncer.svc:5432/pg-rates-2?user=postgres&password=$(PGPASSWORD)"
  API_DB_NETWORK: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-network-data-factory-metrics-1d-1b-1m-stg-1?user=postgres&password=$(PGPASSWORD)"
  API_DB_NETWORK_REQUIRE_REDUNDANCY: "false"
  API_DB_HOURLY_NETWORK: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-network-data-factory-metrics-1h-stg-1?user=postgres&password=$(PGPASSWORD)"
  API_DB_HOURLY_NETWORK_REQUIRE_REDUNDANCY: "false"
  API_DB_MINUTELY_NETWORK: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-network-data-factory-metrics-1d-1b-1m-stg-1?user=postgres&password=$(PGPASSWORD)"
  API_DB_MINUTELY_NETWORK_REQUIRE_REDUNDANCY: "false"
  API_DB_CANDLES_MARKET: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-sharded-candles-1?user=postgres&password=$(PGPASSWORD), postgresql://pgbouncer.pgbouncer.svc:5432/pg-sharded-candles-2?user=postgres&password=$(PGPASSWORD)"
  API_DB_METRICS: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-market-metrics-1?user=postgres&password=$(PGPASSWORD), postgresql://pgbouncer.pgbouncer.svc:5432/pg-market-metrics-2?user=postgres&password=$(PGPASSWORD)"
  API_DB_ETH_SMART_CONTRACTS: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-scmf-stg-1?user=postgres&password=$(PGPASSWORD), postgresql://pgbouncer.pgbouncer.svc:5432/pg-scmf-stg-2?user=postgres&password=$(PGPASSWORD)"
  API_DB_PRINCIPAL_PRICE: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-rates-2?user=postgres&password=$(PGPASSWORD), postgresql://pgbouncer.pgbouncer.svc:5432/pg-rates-1?user=postgres&password=$(PGPASSWORD)"
  API_DB_ADDRESS_TAGGING: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-tagging-pipeline-stg-cdev1-p-2?user=postgres&password=$(PGPASSWORD), postgresql://pgbouncer.pgbouncer.svc:5432/pg-tagging-pipeline-stg-cdev1-p-1?user=postgres&password=$(PGPASSWORD)"
  API_DB_CHAIN_MONITOR: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-cmf-stg-1?user=postgres&password=$(PGPASSWORD),postgresql://pgbouncer.pgbouncer.svc:5432/pg-cmf-stg-2?user=postgres&password=$(PGPASSWORD)"
  KAFKA_TRADES_0: "kafka-trades-1.kafka.svc:9092,kafka-trades-2.kafka.svc:9092"
  KAFKA_TRADES_0_EXCHANGES: "0:6339, 1:189831, 2:13134, 4:7741385, 5:10480, 6:71307, 7:8356, 8:5112, 9:2279970, 10:1527377, 11:9613, 12:847286, 16:788407, 20:323028, 21:100131, 22:0, 24:815968, 26:2958, 28:4320, 31:62, 32:4, 33:0, 34:9299, 35:1661, 37:16293, 38:177269, 39:322595, 40:1732, 41:3575, 42:2762688, 46:7956, 48:140999, 49:1418009, 50:110971, 53:22, 54:133660, 55:135530, 56:1314, 57:1, 59:4054"
  KAFKA_TRADES_1: "kafka-defi-1.kafka.svc:9092,kafka-defi-2.kafka.svc:9092"
  KAFKA_TRADES_1_EXCHANGES: "44:1442, 45:5409, 47:433"
  KAFKA_LIQUIDATIONS_0: "kafka-trades-1.kafka.svc:9092,kafka-trades-2.kafka.svc:9092"
  KAFKA_LIQUIDATIONS_0_EXCHANGES: "2:26020, 4:10154413, 6:53917, 9:4684773, 10:108100, 34:26321, 37:46090, 42:4890239, 59:10123"
  KAFKA_OPEN_INTERESTS_0: "kafka-trades-1.kafka.svc:9092,kafka-trades-2.kafka.svc:9092"
  KAFKA_OPEN_INTERESTS_0_EXCHANGES: "2:26012, 4:10153005, 6:53899, 9:4685151, 10:108065, 34:26312, 37:48762, 42:4888669, 59:10120"
  KAFKA_RATES: "kafka-mdf-rates-1.kafka.svc:9092,kafka-mdf-rates-2.kafka.svc:9092"
  KAFKA_RATES_TOPICS: "realtime_all:1s,rates_all_200ms:200ms"
  KAFKA_PRINCIPAL_PRICE: "kafka-mdf-pprice-1.kafka.svc:9092,kafka-mdf-pprice-2.kafka.svc:9092"
  KAFKA_PRINCIPAL_PRICE_TOPICS: "principal_price_realtime_all"
  # TODO: PLAT-1588: Update COLD storage config when long-term S3 solution is ready (using mini minio settings for now)
  API_SPOT_BOOKS_TIERS: "COLD:[..-1d],HOT:[..]"
  API_SPOT_BOOKS_TIER_COLD_TYPE: "S3"
  API_SPOT_BOOKS_TIER_COLD_S3_ENDPOINT: "https://mini-minio-cdev1.cnmtrcs.io"
  API_SPOT_BOOKS_TIER_COLD_S3_REGION: "us-east-1"
  API_SPOT_BOOKS_TIER_COLD_S3_ACCESS_KEY: "JKkn232ob23knnKba3"
  API_SPOT_BOOKS_TIER_COLD_S3_SECRET_KEY: "$(API_BOOKS_TIER_COLD_S3_SECRET_KEY)"
  API_FUTURES_BOOKS_TIERS: "COLD:[..-1d],HOT:[..]"
  API_FUTURES_BOOKS_TIER_COLD_TYPE: "S3"
  API_FUTURES_BOOKS_TIER_COLD_S3_ENDPOINT: "https://mini-minio-cdev1.cnmtrcs.io"
  API_FUTURES_BOOKS_TIER_COLD_S3_REGION: "us-east-1"
  API_FUTURES_BOOKS_TIER_COLD_S3_ACCESS_KEY: "JKkn232ob23knnKba3"
  API_FUTURES_BOOKS_TIER_COLD_S3_SECRET_KEY: "$(API_BOOKS_TIER_COLD_S3_SECRET_KEY)"
  API_OPTIONS_BOOKS_TIERS: "COLD:[..-1d],HOT:[..]"
  API_OPTIONS_BOOKS_TIER_COLD_TYPE: "S3"
  API_OPTIONS_BOOKS_TIER_COLD_S3_ENDPOINT: "https://mini-minio-cdev1.cnmtrcs.io"
  API_OPTIONS_BOOKS_TIER_COLD_S3_REGION: "us-east-1"
  API_OPTIONS_BOOKS_TIER_COLD_S3_ACCESS_KEY: "JKkn232ob23knnKba3"
  API_OPTIONS_BOOKS_TIER_COLD_S3_SECRET_KEY: "$(API_BOOKS_TIER_COLD_S3_SECRET_KEY)"

  # TODO: PLAT-1587: Update stats S3 storage config when long-term S3 solution is ready (using mini minio settings for now)
  API_STATISTICS_S3_ENDPOINT: "https://mini-minio-cdev1.cnmtrcs.io"
  API_STATISTICS_S3_REGION: "us-east-1"
  API_STATISTICS_S3_ACCESS_KEY: "api-stats-rw"
  API_STATISTICS_FILE_GROUP_ID_PREFIX: "api-stats-cdev1"
  API_PROCESSORS: "8"
  API_TEMPORAL_NAMESPACE: "api-k8s-stg.rztn5"
  API_TEMPORAL_SERVICE_TARGET: "api-k8s-stg.rztn5.tmprl.cloud:7233"
  # Default value is 1 GB/s
  # API_TOTAL_BANDWIDTH_BYTES_PER_SEC: "1073741824"
  # The default value is 1 GB.
  # API_TOTAL_S3_QUERY_MEMORY_LIMIT_BYTES: "1073741824"

  API_DB_ATLAS_SHARD_1: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-atlas-1-stg-1?user=postgres&password=$(PGPASSWORD)"
  API_ATLAS_SHARD_1_ASSETS: "BTC"
  API_DB_ATLAS_SHARD_1_REQUIRE_REDUNDANCY: "false"
  API_DB_ATLAS_SHARD_2: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-atlas-2-stg-1?user=postgres&password=$(PGPASSWORD)"
  API_ATLAS_SHARD_2_ASSETS: "ETH"
  API_DB_ATLAS_SHARD_2_REQUIRE_REDUNDANCY: "false"
  API_DB_ATLAS_SHARD_3: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-atlas-3-stg-1?user=postgres&password=$(PGPASSWORD)"
  API_ATLAS_SHARD_3_ASSETS: "ALGO"
  API_DB_ATLAS_SHARD_3_REQUIRE_REDUNDANCY: "false"
  API_DB_ATLAS_SHARD_4: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-atlas-4-stg-1?user=postgres&password=$(PGPASSWORD)"
  API_ATLAS_SHARD_4_ASSETS: "XRP"
  API_DB_ATLAS_SHARD_4_REQUIRE_REDUNDANCY: "false"
  API_DB_ATLAS_SHARD_5: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-atlas-5-stg-1?user=postgres&password=$(PGPASSWORD)"
  API_ATLAS_SHARD_5_ASSETS: "AVAXC,AVAXP,AVAXX,BCH"
  API_DB_ATLAS_SHARD_5_REQUIRE_REDUNDANCY: "false"
  API_DB_ATLAS_SHARD_6: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-atlas-6-stg-1?user=postgres&password=$(PGPASSWORD)"
  API_ATLAS_SHARD_6_ASSETS: "1INCH,AAVE,ADA,AE_ETH,AION_ETH,ALCX,ALPHA,ALUSD,ANT,APE,API3,AUDIO,AUDIO_ETH,AXS_ETH,BADGER,BAL,BAND_ETH,BAT,BNB_ETH,BNT,BTM_ETH,BUIDL_ETH,BUSD,CBAT,CBBTC_BASE.ETH,CBBTC_ETH,CBETH,CCOMP,CDAI,CEL,CENNZ,CETH,CHZ_ETH,COMP,CRO,CRV,CRVUSD_ETH,CTXC,CUNI,CUSDC,CUSDT,CVC,CWBTC,CZRX,DAI,DAI.E_BASE.ETH,DASH,DGB,DOGE,DOLA.E_BASE.ETH,DPI,DRGN,ELF,ENJ,ENS,EOS_ETH,ESD,ETC"
  API_DB_ATLAS_SHARD_6_REQUIRE_REDUNDANCY: "false"
  API_DB_ATLAS_SHARD_7: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-atlas-7-stg-1?user=postgres&password=$(PGPASSWORD)"
  API_ATLAS_SHARD_7_ASSETS: "ETHOS,EURCV_ETH,EURC_ETH,EURS_ETH,FDUSD_ETH,FEI_ETH,FRAX_ETH,FTM_ETH,FTT,FUN,FXC_ETH,GAS,GBPT_ETH,GHO_ETH,GLM,GNO,GNT,GRT,GRT_ETH,GUSD,GYEN_ETH,HBTC,HEDG,HT,HUSD,ICN,ICP,ICX_ETH,IDRT_ETH,INST,KCS,KNC,LDO,LEND,LEO_ETH,LINK,LOOM,LPT,LRC_ETH,LTC,LUSD_ETH,MAID,MANA,MATIC_ETH,MCO,MKR,MTL_METAL,NAS_ETH,NEO,NFTX,NXM,OGN,OKB,OMG,OP_OP.ETH,PAID,PAX,PAXG,PAY,PERP,POLY,POL_ETH,POWR,PPT,PYUSD_ETH,QASH,QNT,QTUM_ETH,RAD,RADAR,RAD_ETH,RAI_FINANCE_OLD_ETH,REN,RENBTC,REP,REV_ETH,RHOC,ROOK,RSR,SAI,SALT,SAND,SHIB,SHIB_ETH,SLP_ETH,SNT,SNX,SPELL,SRM,SRN,STMX,STORJ,SUSHI,SWRV,TOKE,TOKE_ETH,TRX_ETH,TUSD_TRX,UBT,UMA,UNI,USDC,USDC.E_OP.ETH,USDC_AVAXC,USDC_BASE.ETH,USDC_ETH,USDC_OP.ETH,USDC_TRX,USDD_ETH,USDE_ETH,USDK,USDT.E_OP.ETH"
  API_DB_ATLAS_SHARD_7_REQUIRE_REDUNDANCY: "false"
  API_DB_ATLAS_SHARD_8: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-atlas-8-stg-1?user=postgres&password=$(PGPASSWORD)"
  API_ATLAS_SHARD_8_ASSETS: "USDT_AVAXC,USDT_ETH,USDT_OMNI,USDT_TRX"
  API_DB_ATLAS_SHARD_8_REQUIRE_REDUNDANCY: "false"
  API_DB_ATLAS_SHARD_9: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-atlas-9-stg-1?user=postgres&password=$(PGPASSWORD)"
  API_ATLAS_SHARD_9_ASSETS: "BTG,VERI,VET_ETH,VTC,WBTC,WETH,WNXM,WSTETH,WSTETH.E_BASE.ETH,WSTETH.E_OP.ETH,WTC,XAUT,XIDR_ETH,XSGD_ETH,XSUSHI,XVG,YFI,ZEC,ZIL_ETH,ZRX"
  API_DB_ATLAS_SHARD_9_REQUIRE_REDUNDANCY: "false"
  API_DB_ATLAS_SHARD_10: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-atlas-exp-stg-1?user=postgres&password=$(PGPASSWORD)"
  API_ATLAS_SHARD_10_ASSETS: "CC"
  API_DB_ATLAS_SHARD_10_REQUIRE_REDUNDANCY: "false"
