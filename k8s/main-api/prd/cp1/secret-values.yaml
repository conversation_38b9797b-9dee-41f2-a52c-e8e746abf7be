# helm secrets dec secret-values.yaml
# helm secrets enc secret-values.yaml
secretEnv:
    PGPASSWORD: ENC[AES256_GCM,data:dQwr08+jMFANpi0=,iv:4BQAx6P9w7y30hBoFjgYUYYLwyVgNM6NrAjnbxmxVfw=,tag:P0eXDRgfZY4NZ4Th0yfmsQ==,type:str]
    API_BOOKS_TIER_COLD_S3_SECRET_KEY: ENC[AES256_GCM,data:YTRtH8bi3VrA6xosWYkj5WJa,iv:Ni/CRntqjOHNvrfPrVYarxD4/ifmu6Y+ZwHjGFM6QCg=,tag:WHxv/emjtswGvb23u5WAcg==,type:str]
    API_STATISTICS_S3_SECRET_KEY: ENC[AES256_GCM,data:AS2POdzFv7I8veZFN1mwtGNnKAXQrdOYwuATqm7dhI4=,iv:pNCxxfWtG0VgDJmUbNfDcTcDqd3ymmW9BdaqR8EYEmg=,tag:BR2SqioIlJi+Jbd0x35xqA==,type:str]
    API_TEMPORAL_CLIENT_CERTIFICATE_CONTENT: ENC[AES256_GCM,data:p33ZLa+9BWP86gZCoOY7gq+byqqBwdu0CDquLpZOs1Ij7Q1GJXVAlkQQmRcvwvJhKxnAkm80+8ewHzp0t371mQRl9IzW4GOTBRqJy2UF1jXIrjqsFPUX2lyVUrqx2a44QqZ4wyDhU6ddSZlE2cdILPZDkp8CM6MH/ODSSmPexM3DIj3yZn2Ms8m7eklhhs86kBreNIzDAE0kU0U4JYgr8XTssRiU/5Wttfwc0GXO9QacqC1HMUwMJAWH31IlArhIKxpQoGVcOVxXi3qULdkE+mJcf9Q+VPoIke+1k+nd1GTmpl30tT2Pnc4I8r8sTz6qsHKvP8+4/AU/kPwXaDUfxckZV5Mzo7sNNVwrl0OZiit4Gsl5Snq8sJWSOIQnV7rO2c0k/jsP/CRB9xf8bb70aF5d/8WMjKz8HxAHOSfIH7wVpsd+hEGRcPAw8xBc5SxHangzrn0ITmfBobxZz9vj8wSnu8ZlC5TFhxDpN9VnE6Ln4Ffz7+HSEPdStxLjDD5CNFAIibNrAEFnNpJw6s7Uoj0cdpKTkX1wSPSnt0XtEktw+6IkxkKuAJbzrqYV3WF/krFHf4yL/HOTxSS43wwMbE22QS5m5KSUuC4X1c6yHFfTfd0deo7ntrnX13Ry8i0aEkKaHHEHKqrCBZJYPnrhGeBiQ3szc+Y4MlxCXee42IH+jB7omHrLRlGjQyM7s2fP86899fdoG968P2pW7Bx9S3VJ9vck0dChOGHAm4ydymcjfXV0czzdZxjZpVeY1n9wIEoGiH+yPOQyzRfTwsUuoWVJhoDipQpQbkSns4SJXh+TaLiHRj7uKm3o46LX3Co2KWlG/QF0xnQc1yuCbRvOYAWFRIUEaxHzyp1eO3jjfZe3tl1wm/xuBu1aWO986dGA6fNzvhXDE4v4DIOx01+E327jewwbg3C84TW0Y7SAAT4w15QqSp3Hp/7/SIJtLkgQz9OIt744u/RsuM1ehVAwAfjeXukNYdjshTXDPBUNnuFjmlj7djaFD+TkztjAES8xQ2ep/c19GNORJ0H8ceqU3G1LIX8oq+idu3gprWv5fULmEj9Xyx7KdFmK6z7DmmAKDBYjDVsz1btI3QaAz4izlHZhIionyxgUCkr8KrooqtBvvCH8j9mhuPd/UJ7S8uPy2KQfS8SJS8Ysk9y2ij6LvGfyiuSz6AweTww+75XRmIvEAve5iC4hCuekzn7lhjTs1S1qLC7mLlsgemC2WFc9zSmUkd4Qk0xSu5X+4e+GWnrQgtsldHP87lEnDUTS08mi2g8Un7Nn0+7A28roavBGFtIRfnxwaRkhzji+3rmMkNXgQxBEfTpda88IRWg0Rs2pqGUiLixmYNn0CzX5d1M35uxszj4P7xTYxRtCcczjdIIQJpXyUsXFg3hyDEoSxPBrnnLEl98MYOqg06P+G+8xZhv6D80Ydn8SRQrQOillRyTkele9B20kCxVhK9Kl8gbCw2vCNkmGquMa/aL+YzERRFIzjGGuyVguQ5c7lkQ2ucGuN4rVMqPh3CwTTiH5zi241297TB+7lWWcWwLeh9C6BWLOxPA15BetxsH2yEV1EX0MYUCzQQrF5g+iGzOHqN7BEVEQ2h8iDAu0qAq7dXsljgHegz0DKpkOgLNW2bFC0XW6idz7YzEf9iu9f5lvhHrorf75Rqus2vxPi4Sa3Inp85Uzjo7F5vNPwFsjijSVD32pCgmYYyoblIAg0wTu38ktFa9G9nLNMM8vHpA7OBTDHXgX0NwagYdKqmer7nrKyr7qEPKfAUU283HbZT17VEtlXeqmK54uoDrs+1GhFkw5wNnWTUlKVDwEr1uQ7gpUV1hmVd++6x6xHgBDjwqjCm2gBM+cJWTxuSw47nZo+xQ06ymn25D6ZcGhH6HkZ2edTWS9V6EbZX+G2c03O/r7GB+nboljL5hh7u2h5s1illKY5eLweml+YygN0rhLw1IpIRp8SEpM9iSR8DhnTiZF7wqCuoYscxvgLUtH,iv:3LoI+5CFrgObcdV8pKJ7/xBHu3o4G9YlIKsP/bpUwCc=,tag:FvoDHdvsNawg0i7ImQ6RoA==,type:str]
    API_TEMPORAL_CLIENT_KEY_CONTENT: ENC[AES256_GCM,data: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,iv:hGiE0RctsPVhsHJ1i0aqVm3isgGS7nlqsKOXKn6KBSQ=,tag:2QttQaiMFuBDjxF+lO+RnQ==,type:str]
sops:
    kms: []
    gcp_kms: []
    azure_kv: []
    hc_vault: []
    age:
        - recipient: age1g7qfrkl0gwpt2agyz6qn0gltfdjzqy843hg03rz9n6yugm8fwsgqjmrf8v
          enc: |
            -----BEGIN AGE ENCRYPTED FILE-----
            YWdlLWVuY3J5cHRpb24ub3JnL3YxCi0+IFgyNTUxOSBDMjNaUnd6VkxLUENIUjJj
            cmNwTGFFTEZEREMyVTNLMDN5aXp2eXhWZWpjClpqZitVM01BeFFoSDNmMjhIM2tq
            bmE4N0xkZW9nRkZia1cyUE5NZ3JBaGsKLS0tIGY5ZjMzOUxONEloVnZ1VmpTRTBU
            UUpnRHd6QWp2S1ZMTHN1MGxUVFdBUzgKZ/f8ZqnklnDvxhXewh/VlCfFvX2mrNRd
            L5zWaZWFZoEXoLcr7tr1NJhDbiz1hasnGDCwE2gLAdrshOrriOueRw==
            -----END AGE ENCRYPTED FILE-----
    lastmodified: "2024-08-15T11:20:20Z"
    mac: ENC[AES256_GCM,data:p6cTO44ob8Wvq75fgBOLU5bZXboHnBs1xSxZSDnMPhfbUQP8wg+0uEfn4QPWv0ejq0UQDAILw5ZhIwBMSWAIyPICnSnYr7aRTFAxthrYpSLAPVFcFjk8DL/D7d5PEzBGjC7DqO43vF5NUbUUeOIXrMGVJGwFALtbHBYXt6+ZiJI=,iv:gLZtel91o9GHxLGaxFVtuzw1LCsnZ7O0EOrGq1c5Zkc=,tag:NIlTxsY5FWuzsag0FRHBUw==,type:str]
    pgp: []
    encrypted_regex: ^(data|stringData|secretEnv|originCertificate)$
    version: 3.8.0
