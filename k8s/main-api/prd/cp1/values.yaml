volumeMounts:
  - name: local-state
    mountPath: /mnt/api4
  - name: dumps
    mountPath: /opt/coinmetrics/api/dumps

volumeClaimTemplates:
  - metadata:
      name: local-state
    spec:
      accessModes: [ "ReadWriteOnce" ]
      storageClassName: "local-storage"
      resources:
        requests:
          storage: 1Gi

tolerations:
  - effect: NoSchedule
    key: coinmetrics.io/api4-only
    operator: Exists

nodeAffinity:
  preferredDuringSchedulingIgnoredDuringExecution:
    - preference:
        matchExpressions:
          - key: node-role.kubernetes.io/api4
            operator: In
            values:
              - "true"
      weight: 50
