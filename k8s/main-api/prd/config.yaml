---
env:
  API_ENV: production
  API_AMS_HOST: "community-ams-api.ams-api"
  API_DB_RR_SCHEMA: production
  API_DB_RR_NEW_SCHEMA: production
  API_DB_CANDLES_MARKET_SCHEMA: production
  API_DB_METRICS_SCHEMA: production
  API_DB_PRINCIPAL_PRICE_SCHEMA: production
  API_DB_TRADES_SPOT: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-trades-spot-1-r-1?user=postgres&password=$(PGPASSWORD), postgresql://pgbouncer.pgbouncer.svc:5432/pg-trades-spot-1-p?user=postgres&password=$(PGPASSWORD)"
  API_DB_TRADES_DERIV: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-trades-derivatives-1-r-1?user=postgres&password=$(PGPASSWORD), postgresql://pgbouncer.pgbouncer.svc:5432/pg-trades-derivatives-1-p?user=postgres&password=$(PGPASSWORD)"
  API_DB_BOOKS: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-books-partitioned-1-r?user=postgres&password=$(PGPASSWORD), postgresql://pgbouncer.pgbouncer.svc:5432/pg-books-partitioned-1-p?user=postgres&password=$(PGPASSWORD)"
  API_DB_BOOKS_REQUIRE_REDUNDANCY: "false"
  API_DB_FUTURES: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-derivatives-1-r-1?user=postgres&password=$(PGPASSWORD), postgresql://pgbouncer.pgbouncer.svc:5432/pg-derivatives-1-p?user=postgres&password=$(PGPASSWORD)"
  API_DB_RR: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-indices-1?user=postgres&password=$(PGPASSWORD), postgresql://pgbouncer.pgbouncer.svc:5432/pg-indices-2?user=postgres&password=$(PGPASSWORD)"
  API_DB_RR_NEW: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-rates-1?user=postgres&password=$(PGPASSWORD), postgresql://pgbouncer.pgbouncer.svc:5432/pg-rates-2?user=postgres&password=$(PGPASSWORD)"
  API_DB_NETWORK: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-network-data-factory-metrics-1d-1b-1m-prd-1?user=postgres&password=$(PGPASSWORD)"
  API_DB_NETWORK_REQUIRE_REDUNDANCY: "false"
  API_DB_HOURLY_NETWORK: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-network-data-factory-metrics-1h-prd-1?user=postgres&password=$(PGPASSWORD)"
  API_DB_HOURLY_NETWORK_REQUIRE_REDUNDANCY: "false"
  API_DB_MINUTELY_NETWORK: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-network-data-factory-metrics-1d-1b-1m-prd-1?user=postgres&password=$(PGPASSWORD)"
  API_DB_MINUTELY_NETWORK_REQUIRE_REDUNDANCY: "false"
  # It is important to keep 2, 1 ordering here because the db 1 is used by Fidelity API as a primary to make the response latency more stable for them
  API_DB_CANDLES_MARKET: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-sharded-candles-2?user=postgres&password=$(PGPASSWORD), postgresql://pgbouncer.pgbouncer.svc:5432/pg-sharded-candles-1?user=postgres&password=$(PGPASSWORD)"
  API_DB_METRICS: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-market-metrics-1?user=postgres&password=$(PGPASSWORD), postgresql://pgbouncer.pgbouncer.svc:5432/pg-market-metrics-2?user=postgres&password=$(PGPASSWORD)"
  API_DB_ETH_SMART_CONTRACTS: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-scmf-prd-1?user=postgres&password=$(PGPASSWORD), postgresql://pgbouncer.pgbouncer.svc:5432/pg-scmf-prd-2?user=postgres&password=$(PGPASSWORD)"
  API_DB_PRINCIPAL_PRICE: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-pprice-2?user=postgres&password=$(PGPASSWORD), postgresql://pgbouncer.pgbouncer.svc:5432/pg-pprice-1?user=postgres&password=$(PGPASSWORD)"
  API_DB_ADDRESS_TAGGING: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-tagging-pipeline-prd-${LOCATION}-p-1?user=postgres&password=$(PGPASSWORD), postgresql://pgbouncer.pgbouncer.svc:5432/pg-tagging-pipeline-prd-${LOCATION}-p-2?user=postgres&password=$(PGPASSWORD)"
  API_DB_CHAIN_MONITOR: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-cmf-prd-1?user=postgres&password=$(PGPASSWORD),postgresql://pgbouncer.pgbouncer.svc:5432/pg-cmf-prd-2?user=postgres&password=$(PGPASSWORD)"
  API_PROCESSORS: "6"

  API_DB_ATLAS_SHARD_1: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-atlas-1-prd-cp1-r-1?user=postgres&password=$(PGPASSWORD), postgresql://pgbouncer.pgbouncer.svc:5432/pg-atlas-1-prd-cp1?user=postgres&password=$(PGPASSWORD)"
  API_ATLAS_SHARD_1_ASSETS: "BTC"
  API_DB_ATLAS_SHARD_2: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-atlas-2-prd-cp1-r-1?user=postgres&password=$(PGPASSWORD), postgresql://pgbouncer.pgbouncer.svc:5432/pg-atlas-2-prd-cp1?user=postgres&password=$(PGPASSWORD)"
  API_ATLAS_SHARD_2_ASSETS: "ETH"
  API_DB_ATLAS_SHARD_3: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-atlas-3-prd-cp1-r-1?user=postgres&password=$(PGPASSWORD), postgresql://pgbouncer.pgbouncer.svc:5432/pg-atlas-3-prd-cp1?user=postgres&password=$(PGPASSWORD)"
  API_ATLAS_SHARD_3_ASSETS: "ALGO"
  API_DB_ATLAS_SHARD_4: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-atlas-4-prd-cp1-r-1?user=postgres&password=$(PGPASSWORD), postgresql://pgbouncer.pgbouncer.svc:5432/pg-atlas-4-prd-cp1?user=postgres&password=$(PGPASSWORD)"
  API_ATLAS_SHARD_4_ASSETS: "XRP"
  API_DB_ATLAS_SHARD_5: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-atlas-5-prd-cp1-r-1?user=postgres&password=$(PGPASSWORD), postgresql://pgbouncer.pgbouncer.svc:5432/pg-atlas-5-prd-cp1?user=postgres&password=$(PGPASSWORD)"
  API_ATLAS_SHARD_5_ASSETS: "AVAXC,AVAXP,AVAXX,BCH"
  API_DB_ATLAS_SHARD_6: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-atlas-6-prd-cp1-r-1?user=postgres&password=$(PGPASSWORD), postgresql://pgbouncer.pgbouncer.svc:5432/pg-atlas-6-prd-cp1?user=postgres&password=$(PGPASSWORD)"
  API_ATLAS_SHARD_6_ASSETS: "1INCH,AAVE,ADA,AE_ETH,AION_ETH,ALCX,ALPHA,ALUSD,ANT,APE,API3,AUDIO,AUDIO_ETH,AXS_ETH,BADGER,BAL,BAND_ETH,BAT,BNB_ETH,BNT,BTM_ETH,BUIDL_ETH,BUSD,CBAT,CBBTC_BASE.ETH,CBBTC_ETH,CBETH,CCOMP,CDAI,CEL,CENNZ,CETH,CHZ_ETH,COMP,CRO,CRV,CRVUSD_ETH,CTXC,CUNI,CUSDC,CUSDT,CVC,CWBTC,CZRX,DAI,DAI.E_BASE.ETH,DASH,DGB,DOGE,DOLA.E_BASE.ETH,DPI,DRGN,ELF,ENJ,ENS,EOS_ETH,ESD,ETC"
  API_DB_ATLAS_SHARD_7: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-atlas-7-prd-cp1-r-1?user=postgres&password=$(PGPASSWORD), postgresql://pgbouncer.pgbouncer.svc:5432/pg-atlas-7-prd-cp1?user=postgres&password=$(PGPASSWORD)"
  API_ATLAS_SHARD_7_ASSETS: "ETHOS,EURCV_ETH,EURC_ETH,EURS_ETH,FDUSD_ETH,FEI_ETH,FRAX_ETH,FTM_ETH,FTT,FUN,FXC_ETH,GAS,GBPT_ETH,GHO_ETH,GLM,GNO,GNT,GRT,GRT_ETH,GUSD,GYEN_ETH,HBTC,HEDG,HT,HUSD,ICN,ICP,ICX_ETH,IDRT_ETH,INST,KCS,KNC,LDO,LEND,LEO_ETH,LINK,LOOM,LPT,LRC_ETH,LTC,LUSD_ETH,MAID,MANA,MATIC_ETH,MCO,MKR,MTL_METAL,NAS_ETH,NEO,NFTX,NXM,OGN,OKB,OMG,OP_OP.ETH,PAID,PAX,PAXG,PAY,PERP,POLY,POL_ETH,POWR,PPT,PYUSD_ETH,QASH,QNT,QTUM_ETH,RAD,RADAR,RAD_ETH,RAI_FINANCE_OLD_ETH,REN,RENBTC,REP,REV_ETH,RHOC,ROOK,RSR,SAI,SALT,SAND,SHIB,SHIB_ETH,SLP_ETH,SNT,SNX,SPELL,SRM,SRN,STMX,STORJ,SUSHI,SWRV,TOKE,TOKE_ETH,TRX_ETH,TUSD_TRX,UBT,UMA,UNI,USDC,USDC.E_OP.ETH,USDC_AVAXC,USDC_BASE.ETH,USDC_ETH,USDC_OP.ETH,USDC_TRX,USDD_ETH,USDE_ETH,USDK,USDT.E_OP.ETH"
  API_DB_ATLAS_SHARD_8: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-atlas-8-prd-cp1-r-1?user=postgres&password=$(PGPASSWORD), postgresql://pgbouncer.pgbouncer.svc:5432/pg-atlas-8-prd-cp1?user=postgres&password=$(PGPASSWORD)"
  API_ATLAS_SHARD_8_ASSETS: "USDT_AVAXC,USDT_ETH,USDT_OMNI,USDT_TRX"
  API_DB_ATLAS_SHARD_9: "postgresql://pgbouncer.pgbouncer.svc:5432/pg-atlas-9-prd-cp1-r-1?user=postgres&password=$(PGPASSWORD), postgresql://pgbouncer.pgbouncer.svc:5432/pg-atlas-9-prd-cp1?user=postgres&password=$(PGPASSWORD)"
  API_ATLAS_SHARD_9_ASSETS: "BTG,CC,VERI,VET_ETH,VTC,WBTC,WETH,WNXM,WSTETH,WSTETH.E_BASE.ETH,WSTETH.E_OP.ETH,WTC,XAUT,XIDR_ETH,XSGD_ETH,XSUSHI,XVG,YFI,ZEC,ZIL_ETH,ZRX"
