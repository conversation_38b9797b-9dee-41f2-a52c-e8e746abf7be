---
nameOverride: api4

kind: "StatefulSet"

image:
  repository: "$CI_REGISTRY_IMAGE"
  tag: "$CI_COMMIT_SHA"

volumeMounts:
  - name: dumps
    mountPath: /opt/coinmetrics/api/dumps

volumes:
  - name: dumps
    emptyDir:
      sizeLimit: 100Gi

livenessProbe: {}

readinessProbe:
  httpGet:
    path: /v4/status
    port: http
  initialDelaySeconds: 15
  periodSeconds: 5

ports:
  - name: http
    containerPort: 8080
    protocol: TCP

service:
  ports:
  - name: http
    port: 8080
    targetPort: http

podLabels:
  coinmetrics.io/allow-minio: ""

imagePullSecrets:
  type: none
