import json

with open("exchange.json", "r") as file:
    data = json.load(file)

# Sort the "tickers" field alphabetically for each exchange
for exchange in data:
    if "tickers" in exchange:
        exchange["tickers"] = {
            key: exchange["tickers"][key] 
            for key in sorted(exchange["tickers"].keys())
        }

with open("exchange.json", "w") as file:
    json.dump(data, file, indent=4)

print("Tickers sorted and saved to 'exchange.json'.")
