SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET client_min_messages = warning;
SET row_security = off;

SET default_tablespace = '';

SET default_with_oids = false;


CREATE SCHEMA IF NOT EXISTS test;
CREATE SCHEMA IF NOT EXISTS public;

CREATE TYPE test.BOOKENTRY AS
(
    amount NUMERIC,
    price NUMERIC,
    order_count INTEGER
);

CREATE TABLE IF NOT EXISTS test.statistics_realtime (
   block_hash TEXT NOT NULL,
   parent_block_hash TEXT NOT NULL,
   asset TEXT NOT NULL,
   metric TEXT NOT NULL,
   time TIMESTAMP WITHOUT TIME ZONE NOT NULL,
   height INT NOT NULL,
   value NUMERIC,
   computed_at TIMESTAMP WITHOUT TIME ZONE NOT NULL,
   computed_by TEXT NOT NULL,
   PRIMARY KEY (asset, metric, block_hash)
);

CREATE TABLE test.statistics_flows_realtime (
    block_hash bytea NOT NULL,
    parent_block_hash bytea NOT NULL,
    asset text NOT NULL,
    metric text NOT NULL,
    "time" timestamp without time zone NOT NULL,
    height integer NOT NULL,
    value numeric NOT NULL,
    status text NOT NULL,
    status_timestamp timestamp without time zone NOT NULL,
    computed_by text NOT NULL
);

CREATE TABLE test.hourly_btc_usd (
    rate_time timestamp without time zone NOT NULL,
    rate_price double precision NOT NULL,
    rate_num_trades integer DEFAULT 0 NOT NULL,
    rate_created timestamp without time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
    rate_corrected timestamp without time zone,
    rate_approved timestamp without time zone,
    rate_original_price double precision NOT NULL,
    rate_contingency boolean DEFAULT false NOT NULL
);

CREATE TABLE test.hourly_eth_usd (
    rate_time timestamp without time zone NOT NULL,
    rate_price double precision NOT NULL,
    rate_num_trades integer DEFAULT 0 NOT NULL,
    rate_created timestamp without time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
    rate_corrected timestamp without time zone,
    rate_approved timestamp without time zone,
    rate_original_price double precision NOT NULL,
    rate_contingency boolean DEFAULT false NOT NULL
);

CREATE TABLE test.hourly_btc_eur (
    rate_time timestamp without time zone NOT NULL,
    rate_price double precision NOT NULL,
    rate_num_trades integer DEFAULT 0 NOT NULL,
    rate_created timestamp without time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
    rate_corrected timestamp without time zone,
    rate_approved timestamp without time zone,
    rate_original_price double precision NOT NULL,
    rate_contingency boolean DEFAULT false NOT NULL
);

CREATE TABLE test.hourly_eth_eur (
    rate_time timestamp without time zone NOT NULL,
    rate_price double precision NOT NULL,
    rate_num_trades integer DEFAULT 0 NOT NULL,
    rate_created timestamp without time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
    rate_corrected timestamp without time zone,
    rate_approved timestamp without time zone,
    rate_original_price double precision NOT NULL,
    rate_contingency boolean DEFAULT false NOT NULL
);

CREATE TABLE test.realtime_btc_usd (
    rate_time timestamp without time zone NOT NULL,
    rate_price double precision NOT NULL,
    rate_num_trades integer DEFAULT 0 NOT NULL,
    rate_created timestamp without time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
    rate_corrected timestamp without time zone,
    rate_approved timestamp without time zone,
    rate_original_price double precision NOT NULL,
    rate_contingency boolean DEFAULT false NOT NULL
);

CREATE TABLE test.realtime_eth_usd (
    rate_time timestamp without time zone NOT NULL,
    rate_price double precision NOT NULL,
    rate_num_trades integer DEFAULT 0 NOT NULL,
    rate_created timestamp without time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
    rate_corrected timestamp without time zone,
    rate_approved timestamp without time zone,
    rate_original_price double precision NOT NULL,
    rate_contingency boolean DEFAULT false NOT NULL
);

CREATE TABLE test.realtime_btc_eur (
    rate_time timestamp without time zone NOT NULL,
    rate_price double precision NOT NULL,
    rate_num_trades integer DEFAULT 0 NOT NULL,
    rate_created timestamp without time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
    rate_corrected timestamp without time zone,
    rate_approved timestamp without time zone,
    rate_original_price double precision NOT NULL,
    rate_contingency boolean DEFAULT false NOT NULL
);

CREATE TABLE test.realtime_eth_eur (
    rate_time timestamp without time zone NOT NULL,
    rate_price double precision NOT NULL,
    rate_num_trades integer DEFAULT 0 NOT NULL,
    rate_created timestamp without time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
    rate_corrected timestamp without time zone,
    rate_approved timestamp without time zone,
    rate_original_price double precision NOT NULL,
    rate_contingency boolean DEFAULT false NOT NULL
);


CREATE TABLE test.trades_spot_4 (                                   -- Binance
                        trade_id            NUMERIC       NOT NULL,
                        trade_symbol        TEXT          NOT NULL,
                        trade_amount        NUMERIC       NOT NULL,
                        trade_price         NUMERIC       NOT NULL,
                        trade_buy           BOOLEAN           NULL,
                        trade_time          TIMESTAMPTZ   NOT NULL,
                        trade_database_time TIMESTAMPTZ   NOT NULL
);

CREATE TABLE test.trades_spot_10 (                                  -- Huobi
                        trade_id            NUMERIC       NOT NULL,
                        trade_symbol        TEXT          NOT NULL,
                        trade_amount        NUMERIC       NOT NULL,
                        trade_price         NUMERIC       NOT NULL,
                        trade_buy           BOOLEAN           NULL,
                        trade_time          TIMESTAMPTZ   NOT NULL,
                        trade_database_time TIMESTAMPTZ   NOT NULL
);

CREATE TABLE test.trades_spot_33 (                                  -- Bittrex
                        trade_id            NUMERIC       NOT NULL,
                        trade_symbol        TEXT          NOT NULL,
                        trade_amount        NUMERIC       NOT NULL,
                        trade_price         NUMERIC       NOT NULL,
                        trade_buy           BOOLEAN           NULL,
                        trade_time          TIMESTAMPTZ   NOT NULL,
                        trade_database_time TIMESTAMPTZ   NOT NULL
);

CREATE TABLE test.trades_spot_35 (                                  -- Binance.US
                        trade_id            NUMERIC       NOT NULL,
                        trade_symbol        TEXT          NOT NULL,
                        trade_amount        NUMERIC       NOT NULL,
                        trade_price         NUMERIC       NOT NULL,
                        trade_buy           BOOLEAN           NULL,
                        trade_time          TIMESTAMPTZ   NOT NULL,
                        trade_database_time TIMESTAMPTZ   NOT NULL
);

CREATE TABLE test.trades_futures_1 (
                    trade_id            NUMERIC       NOT NULL,
                    trade_symbol        TEXT          NOT NULL,
                    trade_amount        NUMERIC       NOT NULL,
                    trade_price         NUMERIC       NOT NULL,
                    trade_buy           BOOLEAN           NULL,
                    trade_time          TIMESTAMPTZ   NOT NULL,
                    trade_database_time TIMESTAMPTZ   NOT NULL
);

INSERT INTO test.trades_futures_1
  (trade_id, trade_symbol, trade_amount, trade_price, trade_buy, trade_time, trade_database_time)
VALUES
  (1,'ETHUSD',100,12.34,true,'2025-02-13 10:00:00'::timestamp,'2025-02-13 10:00:00'::timestamp);


CREATE TABLE test.trades_futures_4 (
                    trade_id            NUMERIC       NOT NULL,
                    trade_symbol        TEXT          NOT NULL,
                    trade_amount        NUMERIC       NOT NULL,
                    trade_price         NUMERIC       NOT NULL,
                    trade_buy           BOOLEAN           NULL,
                    trade_time          TIMESTAMPTZ   NOT NULL,
                    trade_database_time TIMESTAMPTZ   NOT NULL
);

CREATE TABLE test.trades_futures_34 (
                    trade_id            NUMERIC       NOT NULL,
                    trade_symbol        TEXT          NOT NULL,
                    trade_amount        NUMERIC       NOT NULL,
                    trade_price         NUMERIC       NOT NULL,
                    trade_buy           BOOLEAN           NULL,
                    trade_time          TIMESTAMPTZ   NOT NULL,
                    trade_database_time TIMESTAMPTZ   NOT NULL
);

CREATE TABLE test.trades_futures_37 (
                    trade_id            NUMERIC       NOT NULL,
                    trade_symbol        TEXT          NOT NULL,
                    trade_amount        NUMERIC       NOT NULL,
                    trade_price         NUMERIC       NOT NULL,
                    trade_buy           BOOLEAN           NULL,
                    trade_time          TIMESTAMPTZ   NOT NULL,
                    trade_database_time TIMESTAMPTZ   NOT NULL
);

CREATE TABLE test.trades_futures_40 (
                    trade_id            NUMERIC       NOT NULL,
                    trade_symbol        TEXT          NOT NULL,
                    trade_amount        NUMERIC       NOT NULL,
                    trade_price         NUMERIC       NOT NULL,
                    trade_buy           BOOLEAN           NULL,
                    trade_time          TIMESTAMPTZ   NOT NULL,
                    trade_database_time TIMESTAMPTZ   NOT NULL
);

CREATE TABLE test.trades_option_37 (
                    trade_id            NUMERIC       NOT NULL,
                    trade_symbol        TEXT          NOT NULL,
                    trade_amount        NUMERIC       NOT NULL,
                    trade_price         NUMERIC       NOT NULL,
                    trade_buy           BOOLEAN           NULL,
                    trade_time          TIMESTAMPTZ   NOT NULL,
                    trade_database_time TIMESTAMPTZ   NOT NULL
);


CREATE TABLE test.spot_metadata (
                      market_exchange_id                SMALLINT      NOT NULL,
                      market_symbol                     TEXT          NOT NULL,

                      market_base_id                    SMALLINT          NULL,
                      market_quote_id                   SMALLINT          NULL,
                      market_base_name                  TEXT              NULL,
                      market_quote_name                 TEXT              NULL,

                      market_listing_date               TIMESTAMPTZ   NOT NULL,
                      market_end_date                   TIMESTAMPTZ       NULL,
                      market_is_current                 BOOL          NOT NULL,
                      market_database_time              TIMESTAMPTZ   NOT NULL,
                      market_status                     TEXT              NULL,
                      market_amount_increment           NUMERIC           NULL,
                      market_amount_size_min            NUMERIC           NULL,
                      market_amount_size_max            NUMERIC           NULL,
                      market_price_increment            NUMERIC           NULL,
                      market_price_size_min             NUMERIC           NULL,
                      market_price_size_max             NUMERIC           NULL,
                      market_order_size_min             NUMERIC           NULL,
                      market_taker_fee                  NUMERIC           NULL,
                      market_maker_fee                  NUMERIC           NULL,
                      market_margin_trading_enabled     BOOLEAN           NULL,
                      market_native_base_name           TEXT              NULL,
                      market_native_quote_name          TEXT              NULL,
                      market_contract_size              NUMERIC           NULL
);

CREATE TABLE test.futures_metadata (
    contract_exchange_id smallint NOT NULL,
    contract_symbol text NOT NULL,
    contract_underlying_base_id smallint,
    contract_underlying_quote_id smallint,
    contract_size_asset_id smallint,
    contract_margin_asset_id smallint,
    contract_underlying_base_name text,
    contract_underlying_quote_name text,
    contract_size_asset_name text,
    contract_margin_asset_name text,
    contract_listing_date timestamp without time zone,
    contract_expiry_date timestamp without time zone,
    contract_size numeric,
    contract_tick_size numeric,
    contract_database_time timestamp without time zone NOT NULL,
    contract_multiplier_size            NUMERIC  NULL,
    contract_status                     TEXT      NULL,
    contract_amount_increment           NUMERIC   NULL,
    contract_amount_size_min            NUMERIC   NULL,
    contract_amount_size_max            NUMERIC   NULL,
    contract_price_increment            NUMERIC   NULL,
    contract_price_size_min             NUMERIC   NULL,
    contract_price_size_max             NUMERIC   NULL,
    contract_order_size_min             NUMERIC   NULL,
    contract_taker_fee                  NUMERIC   NULL,
    contract_maker_fee                  NUMERIC   NULL,
    contract_margin_trading_enabled     BOOLEAN   NULL,
    contract_underlying_native_base_name TEXT    NULL,
    contract_underlying_native_quote_name TEXT   NULL
);

INSERT INTO test.futures_metadata(contract_exchange_id, contract_symbol, contract_underlying_base_id, contract_underlying_quote_id, contract_size_asset_id, contract_margin_asset_id, contract_underlying_base_name, contract_underlying_quote_name, contract_size_asset_name, contract_margin_asset_name, contract_listing_date, contract_expiry_date, contract_size, contract_tick_size, contract_database_time)
VALUES
    (40,'BTCQ1',0,3,3,0,'btc','usd','btc','usd',null,to_timestamp(1618172643),1,0.0025,to_timestamp(1618172643));

CREATE TABLE test.option_metadata
(
    contract_exchange_id            SMALLINT    NOT NULL,
    contract_symbol                 TEXT        NOT NULL,

    contract_underlying_base_id     SMALLINT NULL,
    contract_underlying_quote_id    SMALLINT NULL,
    contract_size_asset_id          SMALLINT NULL,
    contract_underlying_base_name   TEXT        NOT NULL,
    contract_underlying_quote_name  TEXT        NOT NULL,
    contract_size_asset_name        TEXT        NOT NULL,

    contract_type                   TEXT        NOT NULL,
    contract_strike                 NUMERIC     NOT NULL,
    contract_expiry_date            TIMESTAMPTZ NOT NULL,
    contract_size                   NUMERIC     NOT NULL,
    contract_is_european            BOOLEAN     NOT NULL,
    contract_listing_date           TIMESTAMPTZ NOT NULL,
    contract_database_time          TIMESTAMPTZ NOT NULL,
    contract_tick_size              NUMERIC     NOT NULL,
    contract_settlement_state       TEXT        NOT NULL DEFAULT 'open',
    contract_settlement_price       NUMERIC NULL,
    contract_status                 TEXT NULL,
    contract_amount_increment       NUMERIC NULL,
    contract_amount_size_min        NUMERIC NULL,
    contract_amount_size_max        NUMERIC NULL,
    contract_price_increment        NUMERIC NULL,
    contract_price_size_min         NUMERIC NULL,
    contract_price_size_max         NUMERIC NULL,
    contract_order_size_min         NUMERIC NULL,
    contract_taker_fee              NUMERIC NULL,
    contract_maker_fee              NUMERIC NULL,
    contract_margin_trading_enabled BOOLEAN,
    contract_min_catalog_version    NUMERIC     NOT NULL DEFAULT 1,
    contract_underlying_native_base_name TEXT   NULL,
    contract_underlying_native_quote_name TEXT  NULL
);

CREATE TABLE test.option_ticker (
                              ticker_exchange_id        SMALLINT        NOT NULL,
                              ticker_symbol             TEXT            NOT NULL,
                              ticker_time               TIMESTAMPTZ     NOT NULL,
                              ticker_exchange_time      TIMESTAMPTZ         NULL,
                              ticker_price_last         NUMERIC             NULL,
                              ticker_price_bid          NUMERIC             NULL,
                              ticker_price_ask          NUMERIC             NULL,
                              ticker_price_mark         NUMERIC             NULL,
                              ticker_price_index        NUMERIC             NULL,
                              ticker_amount_bid         NUMERIC             NULL,
                              ticker_amount_ask         NUMERIC             NULL,
                              ticker_index_name         TEXT                NULL,
                              ticker_implied_vol_trade  NUMERIC             NULL,
                              ticker_implied_vol_bid    NUMERIC             NULL,
                              ticker_implied_vol_ask    NUMERIC             NULL,
                              ticker_implied_vol_mark   NUMERIC             NULL,
                              ticker_greek_delta        NUMERIC             NULL,
                              ticker_greek_gamma        NUMERIC             NULL,
                              ticker_greek_theta        NUMERIC             NULL,
                              ticker_greek_vega         NUMERIC             NULL,
                              ticker_greek_rho          NUMERIC             NULL,
                              ticker_database_time      TIMESTAMPTZ     NOT NULL,
                              ticker_estimated_settlement_price NUMERIC     NULL
);

CREATE TABLE test.funding_rates (
    funding_exchange_id SMALLINT,
    funding_symbol TEXT,
    funding_rate NUMERIC,
    funding_rate_period INTERVAL,
    funding_interval INTERVAL,
    funding_time TIMESTAMP,
    funding_database_time TIMESTAMP
);

CREATE TABLE test.liquidations (
    liquidation_id numeric NOT NULL,
    liquidation_exchange_id smallint NOT NULL,
    liquidation_symbol text NOT NULL,
    liquidation_amount numeric,
    liquidation_price numeric,
    liquidation_buy boolean,
    liquidation_order boolean,
    liquidation_time timestamp without time zone,
    liquidation_database_time timestamp without time zone
);

INSERT INTO test.liquidations
(liquidation_id,liquidation_exchange_id,liquidation_symbol,liquidation_amount,liquidation_price,liquidation_buy,liquidation_order,liquidation_time,liquidation_database_time)
VALUES (1,1,'ETHUSD',100,43.21,true,true,'2025-02-13 10:00:00'::timestamp,'2025-02-13 10:00:00'::timestamp);

CREATE TABLE test.futures_open_interest (
    open_interest_exchange_id smallint NOT NULL,
    open_interest_symbol text NOT NULL,
    open_interest_time timestamp NOT NULL,
    open_interest_contract_count numeric NOT NULL,
    open_interest_value_usd numeric NOT NULL,
    open_interest_database_time timestamp NOT NULL
);

INSERT INTO test.futures_open_interest
(open_interest_exchange_id,open_interest_symbol,open_interest_time,open_interest_contract_count,open_interest_value_usd,open_interest_database_time)
VALUES (1,'ETHUSD','2025-02-13 10:00:00'::timestamp,100,43.21,'2025-02-13 10:00:00'::timestamp);


CREATE TABLE test.option_open_interest (
    open_interest_exchange_id smallint NOT NULL,
    open_interest_symbol text NOT NULL,
    open_interest_time timestamp NOT NULL,
    open_interest_contract_count numeric NOT NULL,
    open_interest_value_usd numeric NOT NULL,
    open_interest_database_time timestamp NOT NULL
);

CREATE TABLE test.fidelity_index (
    index_id smallint NOT NULL,
    index_time timestamp NOT NULL,
    index_price double precision NOT NULL,
    index_num_trades integer NOT NULL default 0
);

CREATE TABLE test.cm_index (
    cm_index_id smallint NOT NULL,
    cm_index_time timestamp without time zone NOT NULL,
    cm_index_price double precision NOT NULL,
    cm_index_num_trades integer DEFAULT 0 NOT NULL
);


CREATE TABLE test.cm_index_close (
    cm_index_id smallint NOT NULL,
    cm_index_time timestamp without time zone NOT NULL,
    cm_index_price double precision NOT NULL,
    cm_index_num_trades integer DEFAULT 0 NOT NULL
);

CREATE TABLE test.cm_index_realtime
(
    cm_index_id         smallint          not null,
    cm_index_time       timestamp         not null,
    cm_index_price      double precision  not null,
    cm_index_num_trades integer default 0 not null,
    PRIMARY KEY (cm_index_id, cm_index_time)
);

CREATE TABLE test.cm_index_constituents (
    cm_index_id smallint NOT NULL,
    cm_index_time timestamp without time zone NOT NULL,
    cm_currency_ticker character varying(64) NOT NULL,
    cm_weight numeric NOT NULL
);


CREATE TABLE test.statistics (
    asset text NOT NULL,
    metric text NOT NULL,
    "time" timestamp with time zone NOT NULL,
    value numeric NOT NULL,
    computed_at timestamp with time zone,
    computed_by text
);

CREATE TABLE test.institution_metrics (
    institution_id smallint NOT NULL,
    "time" timestamp with time zone NOT NULL,
    metric text NOT NULL,
    value numeric NOT NULL,
    database_time timestamp with time zone NOT NULL
);


CREATE TYPE public.network_data_metric as enum ('AdrAccCnt', 'AdrAct30dCnt', 'AdrAct7dCnt', 'AdrActCnt', 'AdrActContCnt', 'AdrActRecCnt', 'AdrActSentCnt', 'AdrBal1in100KCnt', 'AdrBal1in100MCnt', 'AdrBal1in10BCnt', 'AdrBal1in10KCnt', 'AdrBal1in10MCnt', 'AdrBal1in1BCnt', 'AdrBal1in1KCnt', 'AdrBal1in1MCnt', 'AdrBalCnt', 'AdrBalForkCnt', 'AdrBalNtv0001Cnt', 'AdrBalNtv001Cnt', 'AdrBalNtv01Cnt', 'AdrBalNtv100Cnt', 'AdrBalNtv100KCnt', 'AdrBalNtv10Cnt', 'AdrBalNtv10KCnt', 'AdrBalNtv1Cnt', 'AdrBalNtv1KCnt', 'AdrBalNtv1MCnt', 'AdrBalUSD100Cnt', 'AdrBalUSD100KCnt', 'AdrBalUSD10Cnt', 'AdrBalUSD10KCnt', 'AdrBalUSD10MCnt', 'AdrBalUSD1Cnt', 'AdrBalUSD1KCnt', 'AdrBalUSD1MCnt', 'AdrCnt', 'AdrNewBalCnt', 'AdrNewCnt', 'AdrPresCnt', 'AdrRegCnt', 'AssetEODCompletionTime', 'BlkCnt', 'BlkHgt', 'BlkIntMean', 'BlkSizeByte', 'BlkSizeMeanByte', 'BlkUncCnt', 'BlkUncRevPct', 'BlkUncRwd', 'BlkUncRwdUSD', 'BlkWghtMean', 'BlkWghtTot', 'CapAct1yrUSD', 'CapFutExp10yrUSD', 'CapMVRVCur', 'CapMVRVFF', 'CapMrktCurUSD', 'CapMrktFFUSD', 'CapRealUSD', 'ContBalCnt', 'ContCnt', 'ContERC1155Cnt', 'ContERC20Cnt', 'ContERC721Cnt', 'DiffLast', 'DiffMean', 'FeeByteMeanNtv', 'FeeMeanNtv', 'FeeMeanUSD', 'FeeMedNtv', 'FeeMedUSD', 'FeePrioMeanNtv', 'FeePrioMeanUSD', 'FeePrioMedNtv', 'FeePrioMedUSD', 'FeePrioTotNtv', 'FeePrioTotUSD', 'FeeRevPct', 'FeeTotNtv', 'FeeTotUSD', 'FeeWghtMeanNtv', 'FlowInBFXNtv', 'FlowInBFXUSD', 'FlowInBMXNtv', 'FlowInBMXUSD', 'FlowInBNBNtv', 'FlowInBNBUSD', 'FlowInBSPNtv', 'FlowInBSPUSD', 'FlowInBTXNtv', 'FlowInBTXUSD', 'FlowInCBSNtv', 'FlowInCBSUSD', 'FlowInDERNtv', 'FlowInDERUSD', 'FlowInExInclNtv', 'FlowInExInclUSD', 'FlowInExNtv', 'FlowInExUSD', 'FlowInGEMNtv', 'FlowInGEMUSD', 'FlowInHUONtv', 'FlowInHUOUSD', 'FlowInKRKNtv', 'FlowInKRKUSD', 'FlowInOKXNtv', 'FlowInOKXUSD', 'FlowInPOLNtv', 'FlowInPOLUSD', 'FlowMinerIn0HopAllExNtv', 'FlowMinerIn0HopAllExUSD', 'FlowMinerIn0HopAllNtv', 'FlowMinerIn0HopAllUSD', 'FlowMinerIn1HopAllBFXNtv', 'FlowMinerIn1HopAllBFXUSD', 'FlowMinerIn1HopAllBMXNtv', 'FlowMinerIn1HopAllBMXUSD', 'FlowMinerIn1HopAllBNBNtv', 'FlowMinerIn1HopAllBNBUSD', 'FlowMinerIn1HopAllBSPNtv', 'FlowMinerIn1HopAllBSPUSD', 'FlowMinerIn1HopAllBTXNtv', 'FlowMinerIn1HopAllBTXUSD', 'FlowMinerIn1HopAllCBSNtv', 'FlowMinerIn1HopAllCBSUSD', 'FlowMinerIn1HopAllDERNtv', 'FlowMinerIn1HopAllDERUSD', 'FlowMinerIn1HopAllExNtv', 'FlowMinerIn1HopAllExUSD', 'FlowMinerIn1HopAllGEMNtv', 'FlowMinerIn1HopAllGEMUSD', 'FlowMinerIn1HopAllHUONtv', 'FlowMinerIn1HopAllHUOUSD', 'FlowMinerIn1HopAllKRKNtv', 'FlowMinerIn1HopAllKRKUSD', 'FlowMinerIn1HopAllNtv', 'FlowMinerIn1HopAllOKENtv', 'FlowMinerIn1HopAllOKEUSD', 'FlowMinerIn1HopAllPOLNtv', 'FlowMinerIn1HopAllPOLUSD', 'FlowMinerIn1HopAllUSD', 'FlowMinerNet0HopAllNtv', 'FlowMinerNet0HopAllUSD', 'FlowMinerNet1HopAllNtv', 'FlowMinerNet1HopAllUSD', 'FlowMinerOut0HopAllExNtv', 'FlowMinerOut0HopAllExUSD', 'FlowMinerOut0HopAllNtv', 'FlowMinerOut0HopAllUSD', 'FlowMinerOut1HopAllBFXNtv', 'FlowMinerOut1HopAllBFXUSD', 'FlowMinerOut1HopAllBMXNtv', 'FlowMinerOut1HopAllBMXUSD', 'FlowMinerOut1HopAllBNBNtv', 'FlowMinerOut1HopAllBNBUSD', 'FlowMinerOut1HopAllBSPNtv', 'FlowMinerOut1HopAllBSPUSD', 'FlowMinerOut1HopAllBTXNtv', 'FlowMinerOut1HopAllBTXUSD', 'FlowMinerOut1HopAllCBSNtv', 'FlowMinerOut1HopAllCBSUSD', 'FlowMinerOut1HopAllDERNtv', 'FlowMinerOut1HopAllDERUSD', 'FlowMinerOut1HopAllExNtv', 'FlowMinerOut1HopAllExUSD', 'FlowMinerOut1HopAllGEMNtv', 'FlowMinerOut1HopAllGEMUSD', 'FlowMinerOut1HopAllHUONtv', 'FlowMinerOut1HopAllHUOUSD', 'FlowMinerOut1HopAllKRKNtv', 'FlowMinerOut1HopAllKRKUSD', 'FlowMinerOut1HopAllNtv', 'FlowMinerOut1HopAllOKENtv', 'FlowMinerOut1HopAllOKEUSD', 'FlowMinerOut1HopAllPOLNtv', 'FlowMinerOut1HopAllPOLUSD', 'FlowMinerOut1HopAllUSD', 'FlowNetBFXNtv', 'FlowNetBFXUSD', 'FlowNetBMXNtv', 'FlowNetBMXUSD', 'FlowNetBNBNtv', 'FlowNetBNBUSD', 'FlowNetBSPNtv', 'FlowNetBSPUSD', 'FlowNetBTXNtv', 'FlowNetBTXUSD', 'FlowNetCBSNtv', 'FlowNetCBSUSD', 'FlowNetDERNtv', 'FlowNetDERUSD', 'FlowNetGEMNtv', 'FlowNetGEMUSD', 'FlowNetHUONtv', 'FlowNetHUOUSD', 'FlowNetKRKNtv', 'FlowNetKRKUSD', 'FlowNetOKXNtv', 'FlowNetOKXUSD', 'FlowNetPOLNtv', 'FlowNetPOLUSD', 'FlowOutBFXNtv', 'FlowOutBFXUSD', 'FlowOutBMXNtv', 'FlowOutBMXUSD', 'FlowOutBNBNtv', 'FlowOutBNBUSD', 'FlowOutBSPNtv', 'FlowOutBSPUSD', 'FlowOutBTXNtv', 'FlowOutBTXUSD', 'FlowOutCBSNtv', 'FlowOutCBSUSD', 'FlowOutDERNtv', 'FlowOutDERUSD', 'FlowOutExInclNtv', 'FlowOutExInclUSD', 'FlowOutExNtv', 'FlowOutExUSD', 'FlowOutGEMNtv', 'FlowOutGEMUSD', 'FlowOutHUONtv', 'FlowOutHUOUSD', 'FlowOutKRKNtv', 'FlowOutKRKUSD', 'FlowOutOKXNtv', 'FlowOutOKXUSD', 'FlowOutPOLNtv', 'FlowOutPOLUSD', 'FlowTfrFromExCnt', 'FlowTfrFromExInclCnt', 'FlowTfrInBFXCnt', 'FlowTfrInBMXCnt', 'FlowTfrInBNBCnt', 'FlowTfrInBSPCnt', 'FlowTfrInBTXCnt', 'FlowTfrInCBSCnt', 'FlowTfrInDERCnt', 'FlowTfrInGEMCnt', 'FlowTfrInHUOCnt', 'FlowTfrInKRKCnt', 'FlowTfrInOKXCnt', 'FlowTfrInPOLCnt', 'FlowTfrOutBFXCnt', 'FlowTfrOutBMXCnt', 'FlowTfrOutBNBCnt', 'FlowTfrOutBSPCnt', 'FlowTfrOutBTXCnt', 'FlowTfrOutCBSCnt', 'FlowTfrOutDERCnt', 'FlowTfrOutGEMCnt', 'FlowTfrOutHUOCnt', 'FlowTfrOutKRKCnt', 'FlowTfrOutOKXCnt', 'FlowTfrOutPOLCnt', 'FlowTfrToExCnt', 'FlowTfrToExInclCnt', 'GasBaseBlkMean', 'GasLmtBlk', 'GasLmtBlkMean', 'GasLmtTx', 'GasLmtTxMean', 'GasUsedTx', 'GasUsedTxMean', 'GiniAdrAct5yr', 'GiniAdrCur', 'GiniAdrFF', 'HashRate', 'HashRate30d', 'HashRate30dOtherHardware', 'HashRate30dOtherHardwarePct', 'HashRate30dS7', 'HashRate30dS7Pct', 'HashRate30dS9', 'HashRate30dS9Pct', 'HashRib30d60d', 'IssContNtv', 'IssContPctAnn', 'IssContPctDay', 'IssContUSD', 'IssTotNtv', 'IssTotUSD', 'MCRC', 'MCTC', 'MOMR', 'MRI0HopAll30d', 'MRI1HopAll30d', 'MinerEntity', 'NDF', 'NVTAdj', 'NVTAdj90', 'NVTAdjFF', 'NVTAdjFF90', 'PriceBTC', 'PriceUSD', 'PuellMulCont', 'PuellMulRev', 'PuellMulTot', 'RCTC', 'ROI1yr', 'ROI30d', 'RVTAdj', 'RVTAdj90', 'ReferenceRateBTC', 'ReferenceRateETH', 'ReferenceRateEUR', 'ReferenceRateUSD', 'RevAllTimeUSD', 'RevHash1yAvgNtv', 'RevHash1yAvgUSD', 'RevHashNtv', 'RevHashRateNtv', 'RevHashRateUSD', 'RevHashUSD', 'RevNtv', 'RevUSD', 'SER', 'SOPR', 'SOPROut', 'SplyAccNtv', 'SplyAccUSD', 'SplyAct10yr', 'SplyAct180d', 'SplyAct1d', 'SplyAct1yr', 'SplyAct2yr', 'SplyAct30d', 'SplyAct3yr', 'SplyAct4yr', 'SplyAct5yr', 'SplyAct7d', 'SplyAct90d', 'SplyActEver', 'SplyActPct1yr', 'SplyAdrBal1in100K', 'SplyAdrBal1in100M', 'SplyAdrBal1in10B', 'SplyAdrBal1in10K', 'SplyAdrBal1in10M', 'SplyAdrBal1in1B', 'SplyAdrBal1in1K', 'SplyAdrBal1in1M', 'SplyAdrBalNtv0001', 'SplyAdrBalNtv001', 'SplyAdrBalNtv01', 'SplyAdrBalNtv1', 'SplyAdrBalNtv10', 'SplyAdrBalNtv100', 'SplyAdrBalNtv100K', 'SplyAdrBalNtv10K', 'SplyAdrBalNtv1K', 'SplyAdrBalNtv1M', 'SplyAdrBalUSD1', 'SplyAdrBalUSD10', 'SplyAdrBalUSD100', 'SplyAdrBalUSD100K', 'SplyAdrBalUSD10K', 'SplyAdrBalUSD10M', 'SplyAdrBalUSD1K', 'SplyAdrBalUSD1M', 'SplyAdrPres', 'SplyAdrReg', 'SplyAdrTop100', 'SplyAdrTop10Pct', 'SplyAdrTop1Pct', 'SplyBFXNtv', 'SplyBFXUSD', 'SplyBMXNtv', 'SplyBMXUSD', 'SplyBNBNtv', 'SplyBNBUSD', 'SplyBSPNtv', 'SplyBSPUSD', 'SplyBTXNtv', 'SplyBTXUSD', 'SplyBot20PctNtv', 'SplyBot20PctUSD', 'SplyBurntNtv', 'SplyBurntUSD', 'SplyCBSNtv', 'SplyCBSUSD', 'SplyContNtv', 'SplyContUSD', 'SplyCur', 'SplyDERNtv', 'SplyDERUSD', 'SplyExNtv', 'SplyExUSD', 'SplyExpFut10yr', 'SplyExpFut10yrCMBI', 'SplyFF', 'SplyFork', 'SplyFound', 'SplyGEMNtv', 'SplyGEMUSD', 'SplyHUONtv', 'SplyHUOUSD', 'SplyKRKNtv', 'SplyKRKUSD', 'SplyLost', 'SplyMiner0HopAllNtv', 'SplyMiner0HopAllUSD', 'SplyMiner1HopAllNtv', 'SplyMiner1HopAllUSD', 'SplyOKXNtv', 'SplyOKXUSD', 'SplyPOLNtv', 'SplyPOLUSD', 'SplyRes', 'SplyRvv180d', 'SplyRvv1yr', 'SplyRvv2yr', 'SplyRvv30d', 'SplyRvv3yr', 'SplyRvv4yr', 'SplyRvv5yr', 'SplyRvv7d', 'SplyRvv90d', 'SplyShld', 'SplyTeam', 'SplyTop20PctNtv', 'SplyTop20PctUSD', 'SplyUTXOLoss', 'SplyUTXOProf', 'SplyWalBal1in100M', 'SplyWalBal1in100k', 'SplyWalBal1in10B', 'SplyWalBal1in10M', 'SplyWalBal1in10k', 'SplyWalBal1in1B', 'SplyWalBal1in1M', 'SplyWalBal1in1k', 'SplyWalBalNtv0001', 'SplyWalBalNtv001', 'SplyWalBalNtv01', 'SplyWalBalNtv1', 'SplyWalBalNtv10', 'SplyWalBalNtv100', 'SplyWalBalNtv100k', 'SplyWalBalNtv10k', 'SplyWalBalNtv1M', 'SplyWalBalNtv1k', 'SplyWalBalUSD1', 'SplyWalBalUSD10', 'SplyWalBalUSD100', 'SplyWalBalUSD100k', 'SplyWalBalUSD10M', 'SplyWalBalUSD10k', 'SplyWalBalUSD1M', 'SplyWalBalUSD1k', 'TxCnt', 'TxCntSec', 'TxContCallCnt', 'TxContCallSuccCnt', 'TxContCnt', 'TxContCreatCnt', 'TxContDestCnt', 'TxEIP1559Cnt', 'TxERC1155Cnt', 'TxERC20Cnt', 'TxERC721Cnt', 'TxExCnt', 'TxMeanByte', 'TxOpRetCnt', 'TxRecTop001PctCnt', 'TxRecTop01PctCnt', 'TxRecTop100Cnt', 'TxRecTop100KCnt', 'TxRecTop10Cnt', 'TxRecTop10KCnt', 'TxRecTop10PctCnt', 'TxRecTop1KCnt', 'TxRecTop1PctCnt', 'TxSentTop001PctCnt', 'TxSentTop01PctCnt', 'TxSentTop100Cnt', 'TxSentTop100KCnt', 'TxSentTop10Cnt', 'TxSentTop10KCnt', 'TxSentTop10PctCnt', 'TxSentTop1KCnt', 'TxSentTop1PctCnt', 'TxShldCnt', 'TxShldFullCnt', 'TxTfrCnt', 'TxTfrERC1155Cnt', 'TxTfrERC20Cnt', 'TxTfrERC721Cnt', 'TxTfrMeanByte', 'TxTfrTknCnt', 'TxTfrValAbUSD100MCnt', 'TxTfrValAbUSD100MNtv', 'TxTfrValAbUSD100MUSD', 'TxTfrValAbUSD100kCnt', 'TxTfrValAbUSD100kNtv', 'TxTfrValAbUSD100kUSD', 'TxTfrValAbUSD10MCnt', 'TxTfrValAbUSD10MNtv', 'TxTfrValAbUSD10MUSD', 'TxTfrValAbUSD1MCnt', 'TxTfrValAbUSD1MNtv', 'TxTfrValAbUSD1MUSD', 'TxTfrValAdjByte', 'TxTfrValAdjNtv', 'TxTfrValAdjUSD', 'TxTfrValBelUSD100Cnt', 'TxTfrValBelUSD100Ntv', 'TxTfrValBelUSD100USD', 'TxTfrValBelUSD10kCnt', 'TxTfrValBelUSD10kNtv', 'TxTfrValBelUSD10kUSD', 'TxTfrValBelUSD1kCnt', 'TxTfrValBelUSD1kNtv', 'TxTfrValBelUSD1kUSD', 'TxTfrValBelUSD500Cnt', 'TxTfrValBelUSD500Ntv', 'TxTfrValBelUSD500USD', 'TxTfrValContCallNtv', 'TxTfrValContCallUSD', 'TxTfrValDayDst', 'TxTfrValDayDstMean', 'TxTfrValMeanNtv', 'TxTfrValMeanUSD', 'TxTfrValMedNtv', 'TxTfrValMedUSD', 'TxTfrValNtv', 'TxTfrValRecNtv', 'TxTfrValRecTop001PctNtv', 'TxTfrValRecTop01PctNtv', 'TxTfrValRecTop100KNtv', 'TxTfrValRecTop100Ntv', 'TxTfrValRecTop10KNtv', 'TxTfrValRecTop10Ntv', 'TxTfrValRecTop10PctNtv', 'TxTfrValRecTop1KNtv', 'TxTfrValRecTop1PctNtv', 'TxTfrValSentNtv', 'TxTfrValSentTop001PctNtv', 'TxTfrValSentTop01PctNtv', 'TxTfrValSentTop100KNtv', 'TxTfrValSentTop100Ntv', 'TxTfrValSentTop10KNtv', 'TxTfrValSentTop10Ntv', 'TxTfrValSentTop10PctNtv', 'TxTfrValSentTop1KNtv', 'TxTfrValSentTop1PctNtv', 'TxTfrValUSD', 'TxTknCnt', 'TxValAdjBelUSD100Cnt', 'TxValAdjBelUSD100Ntv', 'TxValAdjBelUSD100USD', 'TxValAdjBelUSD10kCnt', 'TxValAdjBelUSD10kNtv', 'TxValAdjBelUSD10kUSD', 'TxValAdjBelUSD1kCnt', 'TxValAdjBelUSD1kNtv', 'TxValAdjBelUSD1kUSD', 'TxValAdjBelUSD500Cnt', 'TxValAdjBelUSD500Ntv', 'TxValAdjBelUSD500USD', 'UTXOAgeMean', 'UTXOAgeMed', 'UTXOAgeValMean', 'UTXOCnt', 'UTXODay', 'UTXOLossCnt', 'UTXOLossUnrealUSD', 'UTXOProfCnt', 'UTXOProfUnrealUSD', 'VelAct1yr', 'VelActAdj1yr', 'VelCur1yr', 'VelCurAdj1yr', 'VtyDayRet180d', 'VtyDayRet30d', 'VtyDayRet60d', 'WalActCnt', 'WalActRecCnt', 'WalActSentCnt', 'WalBalCnt', 'TxFailCnt', 'SplyWalBalNtv0.1', 'SplyWalBalNtv0.01', 'SplyWalBalNtv0.001', 'SplyAdrBalNtv0.001', 'SplyAdrBalNtv0.01', 'SplyAdrBalNtv0.1', 'AdrBalNtv0.1Cnt', 'AdrBalNtv0.01Cnt', 'AdrBalNtv0.001Cnt');

CREATE TYPE public.network_data_metric_status as enum ('FLASH', 'REVISED', 'REVIEWED');

CREATE TABLE public."1b_metrics"
(
    asset_id          integer                            not null,
    block_time        timestamp with time zone           not null,
    block_height      integer,
    block_hash        bytea,
    block_parent_hash bytea,
    metric            public.network_data_metric        not null,
    value             numeric,
    status            public.network_data_metric_status not null,
    computed_at       timestamp with time zone           not null,
    computed_by       text                               not null,
    latest_revision   boolean                            not null
);

CREATE TABLE test.fidelity_index_constituents
(
    index_id        smallint          not null,
    index_time      timestamp         not null,
    currency_ticker varchar(64)       not null,
    weight          numeric default 0 not null,
    PRIMARY KEY (index_id, index_time, currency_ticker)
);

CREATE TABLE test.instant_candles_market_spot (
	candle_exchange_id INTEGER NOT NULL,
	candle_interval INTEGER NOT NULL,
	candle_end_time TIMESTAMPTZ NOT NULL,
	candle_base_id SMALLINT NOT NULL,
	candle_quote_id SMALLINT NOT NULL,
	candle_start_time TIMESTAMPTZ NOT NULL,
	candle_open_price NUMERIC NOT NULL DEFAULT 0,
	candle_close_price NUMERIC NOT NULL DEFAULT 0,
	candle_low_price NUMERIC NOT NULL DEFAULT 0,
	candle_high_price NUMERIC NOT NULL DEFAULT 0,
	candle_vwap NUMERIC NOT NULL DEFAULT 0,
	candle_volume NUMERIC NOT NULL DEFAULT 0,
	candle_trades_count INTEGER NOT NULL DEFAULT 0,
	candle_usd_volume NUMERIC NOT NULL DEFAULT 0,
	candle_status VARCHAR(7) NOT NULL DEFAULT 'OK',
	candle_gap_length INTEGER NOT NULL DEFAULT 0,
	computed_by TEXT NULL DEFAULT NULL,
	created_at TIMESTAMPTZ NULL DEFAULT now(),
	PRIMARY KEY (candle_exchange_id, candle_interval, candle_end_time, candle_base_id, candle_quote_id)
);

CREATE TABLE test.candles_market_spot_33_1m
(
    candle_base_id      smallint                       NOT NULL,
    candle_quote_id     smallint                       NOT NULL,
    candle_start_time   timestamp without time zone NOT NULL,
    candle_open_price   numeric              DEFAULT 0 NOT NULL,
    candle_close_price  numeric              DEFAULT 0 NOT NULL,
    candle_low_price    numeric              DEFAULT 0 NOT NULL,
    candle_high_price   numeric              DEFAULT 0 NOT NULL,
    candle_vwap         numeric              DEFAULT 0 NOT NULL,
    candle_volume       numeric              DEFAULT 0 NOT NULL,
    candle_trades_count integer              DEFAULT 0 NOT NULL,
    candle_usd_volume   numeric              DEFAULT 0 NOT NULL,
    candle_status       character varying(7) DEFAULT 'OK':: character varying NOT NULL,
    candle_gap_length   integer              DEFAULT 0 NOT NULL
);

SELECT * INTO test.candles_market_spot_4_1m FROM test.candles_market_spot_33_1m;
INSERT INTO test.candles_market_spot_4_1m (candle_base_id, candle_quote_id, candle_start_time, candle_open_price, candle_close_price, candle_low_price, candle_high_price, candle_vwap, candle_volume, candle_trades_count, candle_usd_volume, candle_status, candle_gap_length)
VALUES (6, 3, '2025-02-14T01:21:00', 1.23, 1.24, 1.23, 1.24, 1.235, 10, 15, 12.35, 'OK', 0);

SELECT * INTO test.candles_market_spot_33_5m FROM test.candles_market_spot_33_1m;
SELECT * INTO test.candles_market_spot_33_10m FROM test.candles_market_spot_33_1m;
SELECT * INTO test.candles_market_spot_33_15m FROM test.candles_market_spot_33_1m;
SELECT * INTO test.candles_market_spot_33_30m FROM test.candles_market_spot_33_1m;
SELECT * INTO test.candles_market_spot_33_1h FROM test.candles_market_spot_33_1m;
SELECT * INTO test.candles_market_spot_33_1d FROM test.candles_market_spot_33_1m;

CREATE TABLE test.instant_candles_market_futures (
	candle_exchange_id INTEGER NOT NULL,
	candle_interval INTEGER NOT NULL,
	candle_end_time TIMESTAMPTZ NOT NULL,
	candle_symbol TEXT NOT NULL,
	candle_start_time TIMESTAMPTZ NOT NULL,
	candle_open_price NUMERIC NOT NULL DEFAULT 0,
	candle_close_price NUMERIC NOT NULL DEFAULT 0,
	candle_low_price NUMERIC NOT NULL DEFAULT 0,
	candle_high_price NUMERIC NOT NULL DEFAULT 0,
	candle_vwap NUMERIC NOT NULL DEFAULT 0,
	candle_volume NUMERIC NOT NULL DEFAULT 0,
	candle_trades_count INTEGER NOT NULL DEFAULT 0,
	candle_usd_volume NUMERIC NOT NULL DEFAULT 0,
	candle_status VARCHAR(7) NOT NULL DEFAULT 'OK',
	candle_gap_length INTEGER NOT NULL DEFAULT 0,
	computed_by TEXT NULL DEFAULT NULL,
	created_at TIMESTAMPTZ NULL DEFAULT now(),
	PRIMARY KEY (candle_exchange_id, candle_interval, candle_end_time, candle_symbol)
);

CREATE TABLE test.candles_market_futures_34_5m (
   candle_symbol text NOT NULL,
   candle_start_time timestamp without time zone NOT NULL,
   candle_open_price numeric DEFAULT 0 NOT NULL,
   candle_close_price numeric DEFAULT 0 NOT NULL,
   candle_low_price numeric DEFAULT 0 NOT NULL,
   candle_high_price numeric DEFAULT 0 NOT NULL,
   candle_vwap numeric DEFAULT 0 NOT NULL,
   candle_volume numeric DEFAULT 0 NOT NULL,
   candle_trades_count integer DEFAULT 0 NOT NULL,
   candle_usd_volume numeric DEFAULT 0 NOT NULL,
   candle_status character varying(7) DEFAULT 'OK'::character varying NOT NULL,
   candle_gap_length integer DEFAULT 0 NOT NULL
);

SELECT * INTO test.candles_market_futures_1_1m FROM test.candles_market_futures_34_5m;
INSERT INTO test.candles_market_futures_1_1m (candle_symbol, candle_start_time, candle_open_price, candle_close_price, candle_low_price, candle_high_price, candle_vwap, candle_volume, candle_trades_count, candle_usd_volume, candle_status, candle_gap_length)
VALUES ('ETHUSD', '2025-02-14T01:21:00', 1.23, 1.24, 1.23, 1.24, 1.235, 10, 15, 12.35, 'OK', 0);

SELECT * INTO test.candles_market_futures_4_1m FROM test.candles_market_futures_34_5m;
SELECT * INTO test.candles_market_futures_34_10m FROM test.candles_market_futures_34_5m;
SELECT * INTO test.candles_market_futures_34_15m FROM test.candles_market_futures_34_5m;
SELECT * INTO test.candles_market_futures_34_30m FROM test.candles_market_futures_34_5m;
SELECT * INTO test.candles_market_futures_34_1h FROM test.candles_market_futures_34_5m;
SELECT * INTO test.candles_market_futures_34_1d FROM test.candles_market_futures_34_5m;
SELECT * INTO test.candles_market_futures_40_1d FROM test.candles_market_futures_34_5m;

CREATE TABLE test.rate_candle_realtime_btc_usd
(
    candle_duration     INTERVAL          NOT NULL,
    candle_start_time   TIMESTAMP         NOT NULL,
    candle_open_price   NUMERIC DEFAULT 0 NOT NULL,
    candle_close_price  NUMERIC DEFAULT 0 NOT NULL,
    candle_low_price    NUMERIC DEFAULT 0 NOT NULL,
    candle_high_price   NUMERIC DEFAULT 0 NOT NULL,
    PRIMARY KEY (candle_duration, candle_start_time)
);

CREATE INDEX rate_candle_realtime_btc_usd_time_index
    ON test.rate_candle_realtime_btc_usd (candle_start_time, candle_duration);

CREATE TABLE test.rate_candle_realtime_eth_usd
(
    candle_duration     INTERVAL          NOT NULL,
    candle_start_time   TIMESTAMP         NOT NULL,
    candle_open_price   NUMERIC DEFAULT 0 NOT NULL,
    candle_close_price  NUMERIC DEFAULT 0 NOT NULL,
    candle_low_price    NUMERIC DEFAULT 0 NOT NULL,
    candle_high_price   NUMERIC DEFAULT 0 NOT NULL,
    PRIMARY KEY (candle_duration, candle_start_time)
);

CREATE INDEX rate_candle_realtime_eth_usd_time_index
    ON test.rate_candle_realtime_eth_usd (candle_start_time, candle_duration);

CREATE TABLE test.spot_books (
    book_exchange_id smallint NOT NULL,
    book_base_id smallint NOT NULL,
    book_quote_id smallint NOT NULL,
    book_depth_limit integer NOT NULL,
    book_time timestamp with time zone NOT NULL,
    book_database_time timestamp with time zone,
    book_exchange_sequence_id numeric,
    book_exchange_time timestamp with time zone,
    book_bids test.BOOKENTRY[],
    book_asks test.BOOKENTRY[]
);

INSERT INTO test.spot_books
(book_exchange_id,book_base_id,book_quote_id,book_depth_limit,book_time,book_database_time,book_exchange_sequence_id,book_exchange_time,book_bids,book_asks)
VALUES (4,0,100,100,'2025-02-13 10:00:00'::timestamp,'2025-02-13 10:00:00'::timestamp,2,NULL,ARRAY[ROW(0.1,1.041,NULL),ROW(0.11,1.035,NULL)]::test.BOOKENTRY[],ARRAY[ROW(0.4,1.077,NULL),ROW(0.3,1.51,NULL)]::test.BOOKENTRY[]);

CREATE TABLE test.futures_books (
    book_exchange_id smallint NOT NULL,
    book_symbol text NOT NULL,
    book_depth_limit integer NOT NULL,
    book_time timestamp with time zone NOT NULL,
    book_database_time timestamp with time zone,
    book_exchange_sequence_id numeric,
    book_exchange_time timestamp with time zone,
    book_bids test.BOOKENTRY[],
    book_asks test.BOOKENTRY[]
);

INSERT INTO test.futures_books
(book_exchange_id,book_symbol,book_depth_limit,book_time,book_database_time,book_exchange_sequence_id,book_exchange_time,book_bids,book_asks)
VALUES (4,'BTCUSDT',100,'2025-02-13 10:00:00'::timestamp,'2025-02-13 10:00:00'::timestamp,NULL,NULL,ARRAY[ROW(0.1,1.04,NULL),ROW(0.11,1.03,NULL)]::test.BOOKENTRY[],ARRAY[ROW(0.4,1.07,NULL),ROW(0.3,1.5,NULL)]::test.BOOKENTRY[]),
       (1,'ETHUSD',100,'2025-02-13 10:00:00'::timestamp,'2025-02-13 10:00:00'::timestamp,NULL,NULL,ARRAY[ROW(0.1,1.04,NULL),ROW(0.11,1.03,NULL)]::test.BOOKENTRY[],ARRAY[ROW(0.4,1.07,NULL),ROW(0.3,1.5,NULL)]::test.BOOKENTRY[])
;

CREATE TABLE test.option_books (
	book_exchange_id SMALLINT NOT NULL,
	book_symbol TEXT NOT NULL,
	book_depth_limit INTEGER NOT NULL,
	book_time TIMESTAMPTZ NOT NULL,
	book_database_time TIMESTAMPTZ NULL DEFAULT NULL,
	book_exchange_sequence_id NUMERIC NULL DEFAULT NULL,
	book_exchange_time TIMESTAMPTZ NULL DEFAULT NULL,
	book_bids test.BOOKENTRY[] NULL DEFAULT NULL,
	book_asks test.BOOKENTRY[] NULL DEFAULT NULL,
	PRIMARY KEY (book_exchange_id, book_symbol, book_depth_limit, book_time)
);

CREATE INDEX option_books_time_index ON test.option_books (book_time);
