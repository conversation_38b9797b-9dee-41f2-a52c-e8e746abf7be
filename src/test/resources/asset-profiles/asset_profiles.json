[{"asset": "audio", "full_name": "<PERSON><PERSON>", "description": "Audius is a decentralized content and streaming platform streaming protocol. The AUDIO token incentivizes activity on the Audius platform, which allows artists and fans to transact directly, host and share content, and make governance decisions for the Audius protocol.", "overview": "Audius is a decentralized content and streaming platform streaming protocol. The AUDIO token incentivizes activity on the Audius platform, which allows artists and fans to transact directly, host and share content, and make governance decisions for the Audius protocol. Audius aims to solve the inefficiencies present in the current music industry by creating an economy that allows for more value to be captured by artists, creates more transparency in distribution and intellectual property rights, and directs connections between artists and content consumers. AUDIO can be staked to ensure the security of the protocol, unlock features, and exert influence in governance decisions. The Audius platform also allows for content to be hosted using its decentralized storage on AudSP, the Audius-native extension 5 to IPFS. Run using “Content Nodes”, gives content creators the power of distributing and permissioning content at their discretion. ", "token_purpose": null, "project_team": null, "foundation": null, "website": "https://audius.org", "whitepaper_url": "https://whitepaper.audius.co/AudiusWhitepaper.pdf", "creation_date": "2020-10-21", "token_generation_event_supply": null, "supply_cap": "1115757599", "initial_supply_token_distribution": null, "vesting_schedule": null, "new_token_issuance_recipient": null, "issuing_networks": null, "bridged_networks": null, "issuance_schedule_changes": null, "significant_historical_changes": null, "upgrade_history": null, "blog_updates": null, "project_github_repository": null, "asset_regulation": null, "asset_regulated_products": null, "etp_custodians": null}, {"asset": "badger", "full_name": "Badger DAO", "description": "Badger is a DAO focused on building infrastructure to connect Bitcoin to DeFi applications across multiple blockchains.", "overview": "Badger is a DAO focused on building infrastructure to connect Bitcoin to DeFi applications across multiple blockchains. BadgerDAO is a community that builds products that integrate Bitcoin into DeFi. BadgerDAO incentivizes builders in the ecosystem using its native token, BADGER. The BADGER token also gives the user governance rights within the Badger ecosystem. BADGER can be staked across various protocols to receive bBADGER, a claim token on interest accrued from staking BADGER.", "token_purpose": null, "project_team": null, "foundation": null, "website": "https://badger.com/", "whitepaper_url": "https://docs.badger.com/badger-finance/", "creation_date": "2020-11-20", "token_generation_event_supply": null, "supply_cap": "21000000", "initial_supply_token_distribution": null, "vesting_schedule": null, "new_token_issuance_recipient": null, "issuing_networks": null, "bridged_networks": null, "issuance_schedule_changes": null, "significant_historical_changes": null, "upgrade_history": null, "blog_updates": null, "project_github_repository": null, "asset_regulation": null, "asset_regulated_products": null, "etp_custodians": null}, {"asset": "btc", "full_name": "Bitcoin", "description": "Bitcoin is a peer-to-peer network that facilitates transfers between network participants without any central authority.", "overview": "Bitcoin is a peer-to-peer network that facilitates transfers between network participants without any central authority. Bitcoin utilizes blockchain technology to create a trusted distributed ledger storing transaction and account data. The Bitcoin blockchain consists of blocks, added roughly every 10 minutes, that contain information on recent transactions, active addresses, and a link to previous blocks, ultimately providing a complete record of all transactions on the network. To add a block to the chain, miners must find a particular nonce that when hashed meets the requirements presented by the network. When a passable nonce is discovered the miner broadcasts it to all the nodes on the network, who in turn make sure each transaction on the block is valid and add it to their copy of the chain. To incentivize mining, the miner that finds the correct nonce is rewarded a fixed amount of bitcoin ( the reward amount halves every 210,000 blocks) and any transaction fees included. This cryptographic process is referred to as proof of work. The difficulty of each block is controlled by the network, which scales difficulty as the total hashing power on the network increases. To access the bitcoin network, users have wallets that allow them to see their available balance and transact with others. Each account has two keys associated with it: a public key and a private key. The public key gets converted into an address to which people can send you funds and the private key allows a user to prove ownership of bitcoin. When sending a transaction a user inputs the address of the intended recipient and the amount being transferred, signs the transaction with their private key, and specifies a transaction fee. Transactions wait in the mempool until they are added to blocks. Generally, a user can get a transaction included in a block earlier by paying a higher transaction fee. Since its inception, multiple second-layer solutions have been developed on the Bitcoin protocol. One of these solutions is the Lightning Network, which provides instant and low-cost micropayments between users. To use the network, two users open a payment channel that operates off-chain. Funds transferred through this channel are only settled on the blockchain when the users close the channel.", "token_purpose": ["Store of Value Token", "<PERSON><PERSON>"], "project_team": "Bitcoin Core Developers", "foundation": null, "website": "https://bitcoin.org/en/", "whitepaper_url": "https://bitcoin.org/bitcoin.pdf", "creation_date": "2009-01-01", "token_generation_event_supply": "N/A - Mark all greens as N/A in endpoint\nNote - Remove thousand separator in this column", "supply_cap": "21000000", "initial_supply_token_distribution": [{"initial_supply_allocation": "N/A", "initial_supply_distribution": "N/A"}], "vesting_schedule": [{"vesting_schedule_allocation": "N/A", "vesting_schedule_cliff": "N/A", "vesting_schedule_total_unlock_time": "N/A"}], "new_token_issuance_recipient": ["Miner"], "issuing_networks": ["Bitcoin"], "bridged_networks": ["Ethereum", "Lightning Network", "Stax", "<PERSON>", "Arbitrum", "OP Mainnet", "Base"], "issuance_schedule_changes": "Bitcoin issuance rewards halve every 210,000 blocks or about 4 years. BTC issuance began at 50 BTC per block and is currently at 3.125 BTC per block. The latest halving was at block 840,000 with the next halving at block 1,050,000.", "significant_historical_changes": [{"date": "2008-10-31", "details": "BTC Whitepaper published"}, {"date": "2009-01-03", "details": "First BTC Block:'Chancellor on brink of second bailout for banks'"}, {"date": "2010-05-22", "details": "BTC Pizza Transaction"}, {"date": "2010-07-18", "details": "MtGox is announced"}, {"date": "2010-07-25", "details": "Consensus change to follow chain with most work"}, {"date": "2010-08-15", "details": "Overflow block is reorged out after <PERSON><PERSON> releases fix and adivses to the re-org"}, {"date": "2010-12-12", "details": "Final post from <PERSON><PERSON> on bitcointalk.org"}, {"date": "2011-06-19", "details": "MtGox is hacked"}, {"date": "2011-08-19", "details": "First Bitcoin Improvement Proposal (BIP)"}, {"date": "2012-04-01", "details": "BIP-16 P2SH"}, {"date": "2012-11-29", "details": "First BTC halving"}, {"date": "2013-01-31", "details": "First ASICs are shipped"}, {"date": "2013-09-01", "details": "144k BTC from Silk Road are confiscated"}, {"date": "2014-02-07", "details": "Mt. Gox halts BTC withdrawals declares bancrupcy by end of month"}, {"date": "2014-03-19", "details": "OP_RETURN is introduced"}, {"date": "2015-02-01", "details": "Lightning Whitepaper is published"}, {"date": "2016-02-03", "details": "BIP2 which defines the BIP process"}, {"date": "2016-07-10", "details": "Second halving"}, {"date": "2017-07-20", "details": "BIP 91 (SegWit) activation is locked in"}, {"date": "2017-08-24", "details": "SegWit goes live"}, {"date": "2018-09-01", "details": "CVE-2018-17144 is discovered that would have allowed attackers to crash bitcoin nodes and exceed the 21 million coin limit"}, {"date": "2020-05-16", "details": "Thid bitcoin halving"}, {"date": "2024-01-10", "details": "First Bitcion Spot ETFs approved in the USA"}, {"date": "2024-04-20", "details": "Fourth bitcoin halving"}], "upgrade_history": [{"date": "2014-01-20", "details": "Bitcoin XT - Forked to increase transaction count per second"}, {"date": "2016-02-20", "details": "Bitcoin Classic - Forked to increase block size"}, {"date": "2016-03-20", "details": "Bitcoin Unlimited - Forked to create variable block sizes"}, {"date": "2017-10-20", "details": "Bitcoin Cash - Forked after Segregated Witness upgrade to speed up transaction times"}, {"date": "2017-12-20", "details": "Bitcoin Gold - Forked to adjust algorithm to return to GPU Proof-of-Work mining and disincentivize specialized technology investment"}, {"date": "2018-08-20", "details": "Bitcoin-<PERSON><PERSON>'s Vision - Forked from Bitcoin Cash to increase block size"}], "blog_updates": null, "project_github_repository": ["https://github.com/bitcoin"], "asset_regulation": "Indirectly", "asset_regulated_products": ["Bitcoin Spot ETFs"], "etp_custodians": ["Coinbase", "Fidelity", "Gemini"]}, {"asset": "coti", "full_name": "COTI", "description": "COTI, Currency of the Internet, is a base layer trust-based payments protocol focused on high transaction throughput and low fees.", "overview": "COTI, Currency of the Internet, is a base layer trust-based payments protocol focused on high transaction throughput and low fees. The COTI token is the native currency for network participants of the COTI protocol, a low-cost high transaction throughput trust-based payment system. Network participants can earn COTI by validating transactions, participating in Arbitration Service, and collecting network fees. The COTI protocol uses a directed acyclic graph (DAG) based ledger to allow for the transaction throughput to scale as network activity grows, contrasting blockchains that tend to have the opposite effect. The COTI protocol can achieve fast consensus using its “Trustchain Algorithm” by relying on validators with high “trust scores” based on historical behavior and known properties of validators. Transactions are validated once a cumulative “trust score” threshold is reached. ", "token_purpose": null, "project_team": null, "foundation": null, "website": "https://coti.io/", "whitepaper_url": "https://coti.io/files/COTI-technical-whitepaper.pdf", "creation_date": "2019-12-10", "token_generation_event_supply": null, "supply_cap": "2000000000", "initial_supply_token_distribution": null, "vesting_schedule": null, "new_token_issuance_recipient": null, "issuing_networks": null, "bridged_networks": null, "issuance_schedule_changes": null, "significant_historical_changes": null, "upgrade_history": null, "blog_updates": null, "project_github_repository": null, "asset_regulation": null, "asset_regulated_products": null, "etp_custodians": null}, {"asset": "crv", "full_name": "Curve DAO Token", "description": "Curve Finance is an automated market maker that focuses on efficient stablecoin trading.", "overview": "Curve Finance is an automated market maker that focuses on efficient stablecoin trading. Curve Finance aims to provide efficient stablecoin trading with low slippage fees using its StableSwap mechanism. The Curve AMM consists of liquidity pools of various kinds: Plain pools (stablecoin pairs), Lending pools (wrapped token pairs where the underlying is lent out to some other protocol), and Metapools (stablecoin is paired against the LP token). Its native token CRV is used for voting on governance proposals and value accrual from staking on liquidity pools. ", "token_purpose": ["Staking Token"], "project_team": "Swiss Stake AG", "foundation": null, "website": "https://curve.fi", "whitepaper_url": "https://curve.fi/whitepaper", "creation_date": "2020-08-12", "token_generation_event_supply": "N/A", "supply_cap": "**********", "initial_supply_token_distribution": null, "vesting_schedule": [{"vesting_schedule_allocation": "pre-CRV liquidity providers", "vesting_schedule_cliff": "", "vesting_schedule_total_unlock_time": "1"}, {"vesting_schedule_allocation": "Team and Investors", "vesting_schedule_cliff": "", "vesting_schedule_total_unlock_time": "2-4"}, {"vesting_schedule_allocation": "Employees", "vesting_schedule_cliff": "", "vesting_schedule_total_unlock_time": "2"}], "new_token_issuance_recipient": ["LiquidityProviders", "veCRVholders"], "issuing_networks": ["Ethereum", "xDai", "<PERSON><PERSON>", "Solana", "Arbitrum"], "bridged_networks": null, "issuance_schedule_changes": "New token issuance receivers vary dependent on gauge weights for each liquidity pool. Incentives to provide liquidity vary on a weekly basis due to governance voters or veCRV holders voting on which pools to incentivize.", "significant_historical_changes": [{"date": "2021-06-01", "details": "Curve Factory v2 launches"}, {"date": "2021-05-03", "details": "crvUSD stablecoin product launches"}, {"date": "2024-03-12", "details": "Curve Llamalend lending solution releases"}], "upgrade_history": null, "blog_updates": "https://news.curve.fi/", "project_github_repository": ["https://github.com/curvefi/curve-contract"], "asset_regulation": "N/A", "asset_regulated_products": null, "etp_custodians": ["N/A"]}, {"asset": "dydx", "full_name": "dYdX", "description": "dYdX is a decentralized margin trading and derivatives trading platform.", "overview": "dYdX is a decentralized margin trading and derivatives trading platform. The protocol issues the dYdX governance tokens which can be staked for yield and used to vote on governance protocols. dYdX uses off-chain order books with on-chain settlement to facilitate efficient markets. dYdX allows for the trading of digital assets and financial products of such assets without the need for centralized authority. The main features of the dYdX platform are its Margin Trading Protocol and Options Protocol. The Margin Trading Protocol allows traders to profit off of price movements, modify leverage, and provide low-risk loans in exchange for interest-bearing dYdX tokens. The dYdX Options Protocol can be used to manage risk and create options for and trade ERC-20 tokens.", "token_purpose": null, "project_team": null, "foundation": null, "website": "https://dydx.exchange/", "whitepaper_url": "https://whitepaper.dydx.exchange/", "creation_date": "2021-07-12", "token_generation_event_supply": null, "supply_cap": "1000000000", "initial_supply_token_distribution": null, "vesting_schedule": null, "new_token_issuance_recipient": null, "issuing_networks": null, "bridged_networks": null, "issuance_schedule_changes": null, "significant_historical_changes": null, "upgrade_history": null, "blog_updates": null, "project_github_repository": null, "asset_regulation": null, "asset_regulated_products": null, "etp_custodians": null}, {"asset": "eth", "full_name": "Ethereum", "description": "Ethereum is a decentralized computing platform that allows users to create, deploy and interact with programs known as smart contracts.", "overview": "Ethereum is a decentralized computing platform that allows users to create, deploy and interact with programs known as smart contracts. Ethereum is a transaction based state machine, meaning that at any point in time the state of the Ethereum network is represented by a mapping of accounts to their associated balance or state. The state of the Ethereum network is updated every time a new block is added to the chain, roughly every 12 seconds. Each block contains information regarding new transactions and is linked to the previous block. The ETH 1.0 chain used a proof-of-work consensus protocol similar to the one implemented by Bitcoin. However, in September 2022 Ethereum transitioned to a new proof-of-stake consensus mechanism that will help reduce fees and increase speed. In a proof-of-stake network, validators stake ETH capital (32 ETH) in a smart contract as a deterrent to act dishonestly. Validators in the network are responsible for validating blocks sent into the network, and creating new blocks when directed. Ethereum also has the capability for users to create smart contracts that are written in a high level language, compiled into bytecode and sent to a contract. The Ethereum Virtual Machine handles all of the bytecode and imposes the coded logic. Smart contracts allow developers to impose logic and rules upon interactions, ultimately creating a product that provides value on the chain. Examples of products built on Ethereum include Aave, a lending platform, and Livepeer, a video streaming infrastructure. Ethereum also provides the framework to create many different types of assets with rules. Examples of popular smart contracts include ERC-20, a framework to create tokens on Ethereum, or ERC-721, a framework for NFTs. Users can interact with smart contracts once they have been deployed allowing for the creation of decentralized applications and additional use cases.", "token_purpose": ["<PERSON><PERSON>", "Staking Token"], "project_team": null, "foundation": "Ethereum Foundation", "website": "https://ethereum.org/en/", "whitepaper_url": "https://ethereum.org/en/whitepaper/#ethereum-whitepaper", "creation_date": "2015-07-30", "token_generation_event_supply": "72009990.5", "supply_cap": null, "initial_supply_token_distribution": null, "vesting_schedule": [{"vesting_schedule_allocation": "N/A", "vesting_schedule_cliff": "N/A", "vesting_schedule_total_unlock_time": "N/A"}], "new_token_issuance_recipient": ["<PERSON><PERSON>"], "issuing_networks": ["Ethereum"], "bridged_networks": null, "issuance_schedule_changes": "Ethereum issuance when using Proof-of-Work began at 5 ETH per block, lowering to 3 ETH per block after the Byzantium fork in October 2017, and again to 2 ETH per block after the Constantinople fork in Feburary 2019. After the merge and Ethereum's migration to Proof-of-Stake, ETH is issued by calculating 166 times the square root of the sum of staked ETH. ETH issuance fluctuates consistently depending on the amount staked and amount of ETH participating in each epoch, overall decreasing when more ETH is staked.", "significant_historical_changes": [{"date": "2016-06-17", "details": "Ethereum DAO was hacked for $50 million"}, {"date": "2021-08-05", "details": "EIP-1559 was introduced restructuring the fee market and introducing a variable base fee that is burned"}, {"date": "2022-09-15", "details": "Ethereum migrated from Proof-of-Work to Proof-of-Stake completing the merge"}, {"date": "2024-03-13", "details": "EIP-4844 introduced to provide dedicated data space or blobspace in blocks for Layer-2 rollups"}], "upgrade_history": [{"date": "2015-03-13", "details": "Frontier Thawing (2015) - Forked to increase security and speed of future forks"}, {"date": "2016-02-13", "details": "Homestead (2016) - Forked for compatibility updates and further design features"}, {"date": "2016-04-13", "details": "DAO Fork (2016) - Roll back after DAO $50 million in DAO funds were stolen, creating Ethereum Classic and today's Ethereum chain"}, {"date": "2016-05-13", "details": "<PERSON><PERSON><PERSON> (2016) - Fork to prevent Denial-of-Service attack spamming empty transactions"}, {"date": "2016-06-13", "details": "Spurious Dragon (2016) - Second Fork to prevent DoS attacks, adjusting opcode fees, and \"enabling 'debloat'\" of blockchain"}, {"date": "2017-07-13", "details": "Byzantium (2017) - Reduced block rewards from 5 ETH to 3 ETH, delayed difficulty bomb by 1 year, increased transaction privacy and Layer-2 network scaling activities"}, {"date": "2019-08-13", "details": "Constantinople (2019) - Reduced block rewards from 3 ETH to 2 ETH and EVM gas optimizations"}, {"date": "2019-03-13", "details": "Istanbul (2019) - Improved DoS prevention, optimized EVM gas costs, increased efficiency of SNARK and STARK proofs on the network"}, {"date": "2020-11-13", "details": "Muir Glacier (2020) - Delayed difficulty bomb due to increased difficulty with Istanbul fork"}, {"date": "2020-10-13", "details": "Beacon Chain Genesis (2020) - Deployment of the Beacon Chain for validators to stake ETH tokens and begin process of transferring to Proof of Stake from Proof of Work"}, {"date": "2021-09-13", "details": "Berlin (2021) - Optimized EVM gas costs"}, {"date": "2021-08-13", "details": "London (2021) - Introduction of EIP-1559 with optimized fee mechanisms separating base fee and priority fee for transaction inclusion in current block"}, {"date": "2021-07-13", "details": "Altair (Beacon Chain Fork) (2021) - Enabled light client interaction and increase validator activity"}, {"date": "2021-06-13", "details": "Arrow Glacier (2021) - Delayed difficulty bomb"}, {"date": "2022-05-13", "details": "Gray Glacier (2022) - Delayed difficulty bomb"}, {"date": "2022-04-13", "details": "Bellatrix (Beacon Chain Fork) (2022) - Increased slashable balances for validators on Beacon Chain in preparation for The Merge"}, {"date": "2022-03-13", "details": "Paris (Consensus Layer Fork) (2022) - Turned off Proof-of-Work and tell execution clients to connect to consensus clients for Proof-of-Stake"}, {"date": "2023-02-13", "details": "Shanghai-Capella (2023) - Enabled staking withdrawals to both execution layer and consensus layer respectively"}, {"date": "2024-01-13", "details": "Cancun<PERSON><PERSON><PERSON> (2024) - EIP-4844 enabled proto-danksharding to improve Layer-2 transaction costs and data availability decreased validator churn rate on consensus layer"}], "blog_updates": "https://blog.ethereum.org/", "project_github_repository": ["https://github.com/ethereum"], "asset_regulation": "Indirectly", "asset_regulated_products": ["ETH Spot ETFs"], "etp_custodians": ["Coinbase Custody Trust Company, LLC"]}, {"asset": "kava", "full_name": "<PERSON><PERSON>", "description": "The KAVA Network is a blockchain", "overview": "The KAVA Network is a blockchain", "website": "https://www.kava.io/", "whitepaper_url": "https://docsend.com/view/gwbwpc3", "creation_date": "2019-10-23"}, {"asset": "knc", "full_name": "Kyber Network Crystal", "description": "Kyber Network is a decentralized finance automated market maker application", "overview": "Kyber Network is a decentralized finance automated market maker application", "website": "https://kyber.network/", "whitepaper_url": "https://docs.kyberswap.com/introduction", "creation_date": "2021-07-12"}, {"asset": "kp3r", "full_name": "Keep3rV1", "description": "Keep3r is a decentralized network to incentivize the performance of on-chain jobs.", "overview": "Keep3r is a decentralized network to incentivize the performance of on-chain jobs.", "website": "https://keep3r.network/", "whitepaper_url": "https://docs.keep3r.network/", "creation_date": "2020-10-28"}, {"asset": "ksm", "full_name": "<PERSON><PERSON><PERSON>", "description": "Kusama is a testing network for applications in the Polkadot network. ", "overview": "Kusama is a testing network for applications in the Polkadot network.", "website": "https://kusama.network/", "whitepaper_url": "https://polkadot.network/Polkadot-lightpaper.pdf", "creation_date": "2019-11-28", "supply_cap": "10000000"}]