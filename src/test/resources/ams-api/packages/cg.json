{"type": "composite", "script": ["asset_metrics {'asset': '1inch', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'aave', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'aca', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'ach', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'acs', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'ada', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'adx', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'ae', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'aergo', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'afc', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'ageur_eth', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'agix', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'agld', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'aion', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'akro', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'akt', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'alcx', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'aleph', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'algo', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'alice', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'allianceblock_nexera', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'alpaca', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'alpha', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'alpine', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'amp', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'ampl', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'anc', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'ankr', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'ant', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'ape', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'api3', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'apt', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'apx', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'ar', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'arb', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'ardr', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'arg', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'ark', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'arpa', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'arv', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'ast', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'astr', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'ata', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'atlas', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'atom', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'auction', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'audio', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'ava', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'avax', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'avg', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'avt', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'axl', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'axs', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'azero', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'azy', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'b2m', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'badger', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'bal', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'band', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'bat', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'bbf', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'bcd', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'bch', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'bel', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'bico', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'bifi', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'bit', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'bitci', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'bld', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'blur', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'blz', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'bnb', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'bnt', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'boba', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'bond', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'bone', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'bonk', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'boring', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'boson', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'brise', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'brwl', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'bsv', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'btc', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'btg', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'btm', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'bts', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'btt', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'bttc', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'busd', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'c98', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'cake', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'cards', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'cel', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'celo', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'celr', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'chess', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'chr', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'chz', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'city', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'ckb', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'clv', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'cmp', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'comp', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'conv', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'coti', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'cpool', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'cqt', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'cream', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'cro', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'crpt', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'crv', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'ctc', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'ctk', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'ctsi', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'cudos', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'cult', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'cvc', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'cvp', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'cvx', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'cwar', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'cweb', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'dai', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'dar', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'dash', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'dawn', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'dc', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'dcr', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'dego', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'dent', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'dgb', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'dia', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'dmtr', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'dnt', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'dodo', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'doge', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'dome', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'dot', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'dpx', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'dusk', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'dydx', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'ecox', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'egld', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'elf', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'elfi', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'elon', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'elt_edenloop', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'enj', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'ens', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'eos', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'ern', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'ertha', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'etc', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'eth', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'ethf', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'eurt', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'evmos', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'ewt', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'fame_famemma', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'fame_fantommaker', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'farm', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'fet', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'fida', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'fil', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'fio', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'firo', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'fis', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'flow', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'flr', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'flux', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'fly', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'fort', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'forth', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'fpft', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'free_freerossdao', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'front', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'ftm', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'ftt', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'fun', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'fx', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'fxs', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'gal', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'gala', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'galft', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'gari', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'gas', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'gear', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'gear_metagear', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'gf', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'gfi', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'gft', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'gft_galaxyfinance', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'gft_gamefantasy', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'ghst', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'glm', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'glmr', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'gmt', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'gmx', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'gno', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'gns', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'gnt', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'go', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'goal', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'gods', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'gpt', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'grail', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'grt', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'gt', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'gtc', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'gusd', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'hbar', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'hero_stephero', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'hft', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'hifi', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'hive', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'hnt', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'hook', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'ht', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'ice', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'ice_decentralgamesice', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'icp', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'icx', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'idex', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'igu', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'ilv', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'imx', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'indi', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'inj', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'inv', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'inx', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'iost', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'iotx', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'ip3', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'iq', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'iris', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'jasmy', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'jewel', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'joe', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'jst', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'juv', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'kai', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'kava', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'kcal', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'kda', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'kdoe', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'keep', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'key', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'kicks', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'kin', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'kint', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'klay', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'klv', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'kmd', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'knc', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'kon', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'kp3r', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'krl', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'ksm', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'kub', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'lazio', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'lbl', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'lcx', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'ldo', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'link', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'lith', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'lmr', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'loka', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'loom', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'lpt', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'lrc', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'lsk', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'ltc', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'lto', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'luna', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'luna2', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'magic', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'mana', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'mask', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'matic', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'mbl', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'mbox', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'mc', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'mcrt', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'mdt', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'med', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'mer', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'metis', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'mft', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'mim_mintmarble', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'mina', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'mine', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'miota', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'mir', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'mith', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'mkr', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'mln', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'mnde', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'mngo', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'mnw', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'mona_monavale', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'movez', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'movr', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'mpl', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'mplx', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'msol', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'mta', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'mtl_metal', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'mts_metastrike', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'multi', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'mvl', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'nano', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'nap', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'nas', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'near', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'neo', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'nexo', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'nft', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'nftx', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'ngl', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'nkn', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'nmr', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'nodl', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'nom', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'npt', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'nu', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'nuls', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'o3', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'oax', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'ocean', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'ogn', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'ogv', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'ohm', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'okb', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'ole', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'om', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'omg', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'onit', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'onston', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'ont', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'ooki', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'op', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'orb', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'orbs', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'orca', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'orn', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'oxt', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'oxy', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'path', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'paw_paw', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'paw_pawzone', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'paxg', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'people', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'perp', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'pha', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'pla', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'pokt', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'polc', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'polis', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'pols', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'poly', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'polyx', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'pond', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'popk', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'por', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'por_portuma', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'powr', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'primal', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'prime', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'prmx', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'prom', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'pros', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'prq', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'psg', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'psi', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'pumlx', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'pundix', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'push', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'pyr', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'qi', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'qkc', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'qlc', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'qnt', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'qrdo', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'qsp', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'qtum', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'quick', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'rad', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'ranker', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'rare', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'rari', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'ray', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'rbn', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'rdnt', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'reef', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'rei', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'ren', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'renbtc', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'rep', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'req', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'rev_rchain', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'revv', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'rfox', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'rgt', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'rlc', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'rly', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'rndr', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'rook', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'rose', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'rpl', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'rsr', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'rss3', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'rsv', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'rune', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'rvn', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'samo', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'sand', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'santos', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'sbr', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'sc', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'scrt', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'sd', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'sdl', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'senate', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'sgb', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'shib', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'sidus', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'skeb', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'skl', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'slp', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'snt', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'snx', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'sol', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'sos', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'spell', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'sps', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'srm', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'steem', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'stg', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'stmx', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'store', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'storj', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'stpt', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'strax', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'strm', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'stx', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'sudo', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'suku', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'sun', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'super', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'sushi', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'swftc', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'sxp', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'sylo', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'sys', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 't', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'tfuel', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'theta', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'time_wonderlandtime', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'tlm', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'toke', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'tomo', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'torn', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'trade', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'trade_unitrade', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'trb', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'trg', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'tribe', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'tribl', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'tru', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'trx', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'tryb', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'tsuka', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'tusd', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'tvk', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'twt', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'uma', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'unfi', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'uni', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'usdc', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'usdp', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'usdt', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'utk', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'vee', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'vee_veefinance', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'vet', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'vgx', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'vinu', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'vite', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'volt', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'voxel', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'vr', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'vra', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'vsys', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'vtho', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'wabi', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'waves', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'waxp', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'wbtc', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'well', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'wnxm', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'woo', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'woof', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'wrx', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'wwy', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'xaut', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'xcad', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'xdc', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'xdefi', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'xec', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'xem', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'xen', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'xeta', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'xlm', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'xmon', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'xmr', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'xpla', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'xrp', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'xtz', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'xvg', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'xvs', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'xym', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'xyo', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'yfi', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'yfii', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'yfx', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'ygg', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'zec', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'zen', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'zil', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'zks', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}", "+ asset_metrics {'asset': 'zrx', 'frequency': '1d', 'metric': 'CapMrktEstUSD'}"]}