#!/bin/sh
set -e

src_dir=$2

mc config host add srv http://$1 minioadmin minioadmin

mc rb --force --dangerous srv/

for dataType in "books-spot-100-10s-freq" "books-spot-10pct-mid-price-10s-freq" "books-spot-full-1h-freq" \
                "books-futures-100-10s-freq" "books-futures-10pct-mid-price-10s-freq" "books-futures-full-1h-freq" \
                "books-options-100-10s-freq" "books-options-10pct-mid-price-10s-freq" "books-options-full-1h-freq"; do
  for bucketType in "data" "index"; do
    for year in "2019" "2020" "2021" "2022" "2023"; do
      bucketName=$dataType-$bucketType-$year
      if test -d $src_dir/$bucketName; then
        mc mb -p srv/$bucketName
        mc cp --recursive $src_dir/$bucketName/ srv/$bucketName/
      fi
    done
  done
done

mc mb srv/test

echo "Data has been copied"
