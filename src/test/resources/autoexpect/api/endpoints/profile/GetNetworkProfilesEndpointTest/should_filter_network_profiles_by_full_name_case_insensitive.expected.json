{"status": 200, "headers": {"content-type": "application/json"}, "content": {"data": [{"full_name": "Avalanche C-Chain", "network": "avaxc", "modular_or_monolithic": "Polylithic", "overview": "Avalanche C-Chain network", "consensus_mechanism": "N/A", "hashing_algorithm": "SHA256", "transaction_finality": "N/A", "available_clients": ["Coreth plugin"], "smart_contract_deployment": "Permissionless", "foundation": ["Ava Labs: A team of developers building and maintaining the infrastructure for the Avalanche network."], "founding_team_identity": "Known", "repository": [{"github_repository": "C-Chain Functionality", "github_url": "github.com/ava-labs/coreth"}], "transaction_visibility": "Visible", "native_fee_token": "AVAX", "rollup_info": [{"rollup_type": "N/A", "state_validation": "N/A"}], "network_scaling_solution": "true", "type_of_network": "Blockchain", "chain_explorers": ["https://snowtrace.io", "https://avascan.info", "https://subnets.avax.network"]}, {"full_name": "Bitcoin", "network": "btc", "modular_or_monolithic": "Monolithic", "overview": "Bitcoin network", "consensus_mechanism": "Proof-of-Work", "hashing_algorithm": "SHA256d", "transaction_finality": "No Fixed Finality Threshold", "available_clients": ["Bitcoin Core"], "smart_contract_deployment": "Permissionless", "foundation": ["Bitcoin Core: A primary group of developers who maintain and release client software to validate blocks and improve the bitcoin wallet experience."], "founding_team_identity": "Unknown", "repository": [{"github_repository": "Bitcoin Core", "github_url": "github.com/bitcoin/bitcoin"}], "transaction_visibility": "Visible", "native_fee_token": "BTC", "significant_historical_changes": [{"date": "2008-10-31", "details": "BTC Whitepaper published"}, {"date": "2009-01-03", "details": "First BTC Block:'Chancellor on brink of second bailout for banks'"}, {"date": "2010-05-22", "details": "BTC Pizza Transaction"}, {"date": "2010-07-18", "details": "MtGox is announced"}, {"date": "2010-07-25", "details": "Consensus change to follow chain with most work"}, {"date": "2010-08-15", "details": "Overflow block is reorged out after <PERSON><PERSON> releases fix and adivses to the re-org"}, {"date": "2010-12-12", "details": "Final post from <PERSON><PERSON> on bitcointalk.org"}, {"date": "2011-06-19", "details": "MtGox is hacked"}, {"date": "2011-08-19", "details": "First Bitcoin Improvement Proposal (BIP)"}, {"date": "2012-04-01", "details": "BIP-16 P2SH"}, {"date": "2012-11-29", "details": "First BTC halving"}, {"date": "2013-01-31", "details": "First ASICs are shipped"}, {"date": "2013-09-01", "details": "144k BTC from Silk Road are confiscated"}, {"date": "2014-02-07", "details": "Mt. Gox halts BTC withdrawals declares bancrupcy by end of month"}, {"date": "2014-03-19", "details": "OP_RETURN is introduced"}, {"date": "2015-02-01", "details": "Lightning Whitepaper is published"}, {"date": "2016-02-03", "details": "BIP2 which defines the BIP process"}, {"date": "2016-07-10", "details": "Second halving"}, {"date": "2017-07-20", "details": "BIP 91 (SegWit) activation is locked in"}, {"date": "2017-08-24", "details": "SegWit goes live"}, {"date": "2018-09-01", "details": "CVE-2018-17144 is discovered that would have allowed attackers to crash bitcoin nodes and exceed the 21 million coin limit"}, {"date": "2020-05-16", "details": "Thid bitcoin halving"}, {"date": "2024-01-10", "details": "First Bitcion Spot ETFs approved in the USA"}, {"date": "2024-04-20", "details": "Fourth bitcoin halving"}], "upgrade_history": [{"date": "2014-01-20", "details": "Bitcoin XT - Forked to increase transaction count per second"}, {"date": "2016-02-20", "details": "Bitcoin Classic - Forked to increase block size"}, {"date": "2016-03-20", "details": "Bitcoin Unlimited - Forked to create variable block sizes"}, {"date": "2017-10-20", "details": "Bitcoin Cash - Forked after Segregated Witness upgrade to speed up transaction times"}, {"date": "2017-12-20", "details": "Bitcoin Gold - Forked to adjust algorithm to return to GPU Proof-of-Work mining and disincentivize specialized technology investment"}, {"date": "2018-08-20", "details": "Bitcoin-<PERSON><PERSON>'s Vision - Forked from Bitcoin Cash to increase block size"}], "rollup_info": [{"rollup_type": "N/A", "state_validation": "N/A"}], "network_scaling_solution": "true", "type_of_network": "Blockchain", "chain_explorers": ["https://blockchair.com/bitcoin", "https://mempool.space", "https://btc.com", "https://explorer.coinex.com/btc", "https://live.blockcypher.com/btc"]}, {"full_name": "Ethereum", "network": "eth", "modular_or_monolithic": "Semi Modular", "overview": "Ethereum network", "consensus_mechanism": "Proof-of-Stake", "hashing_algorithm": "KECCAK-256", "transaction_finality": "2 Epochs under normal circumstances", "available_clients": ["Execution Client:", "geth: github.com/ethereum/go-ethereum", "Nethermind: github.com/NethermindEth/nethermind", "Besu: github.com/hyperledger/besu", "Erigon: github.com/ledgerwatch/erigon", "Rust: https://github.com/paradigmxyz/reth", "Consensus Client:", "Lighthouse: github.com/sigp/lighthouse", "Lodestar: github.com/ChainSafe/lodestar", "Nimbus: github.com/status-im/nimbus-eth1", "Prysm: github.com/prysmaticlabs/prysm", "Teku: github.com/ConsenSys/teku"], "smart_contract_deployment": "Permissionless", "foundation": ["Ethereum Foundation: A non-profit organization that supports Ethereum and related technologies. Hosts a platform of communities to join and contribute directly to the Ethereum blockchain."], "founding_team_identity": "Known", "repository": [{"github_repository": "get", "github_url": "github.com/ethereum/go-ethereum"}, {"github_repository": "Nethermind", "github_url": "github.com/NethermindEth/nethermind"}, {"github_repository": "Besu", "github_url": "github.com/hyperledger/besu"}, {"github_repository": "<PERSON><PERSON><PERSON>", "github_url": "github.com/ledgerwatch/erigon"}, {"github_repository": "Lighthouse", "github_url": "github.com/sigp/lighthouse"}, {"github_repository": "Lodestar", "github_url": "github.com/ChainSafe/lodestar"}, {"github_repository": "Nimbus", "github_url": "github.com/status-im/nimbus-eth1"}, {"github_repository": "Prysm", "github_url": "github.com/prysmaticlabs/prysm"}, {"github_repository": "<PERSON><PERSON>", "github_url": "github.com/ConsenSys/teku"}, {"github_repository": "EIPs", "github_url": "github.com/ethereum/EIPs"}], "transaction_visibility": "Visible", "native_fee_token": "ETH", "significant_historical_changes": [{"date": "2016-06-17", "details": "Ethereum DAO was hacked for $50 million"}, {"date": "2021-08-05", "details": "EIP-1559 was introduced restructuring the fee market and introducing a variable base fee that is burned"}, {"date": "2022-09-15", "details": "Ethereum migrated from Proof-of-Work to Proof-of-Stake completing the merge"}, {"date": "2024-03-13", "details": "EIP-4844 introduced to provide dedicated data space or blobspace in blocks for Layer-2 rollups"}], "upgrade_history": [{"date": "2015-03-13", "details": "Frontier Thawing (2015) - Forked to increase security and speed of future forks"}, {"date": "2016-02-13", "details": "Homestead (2016) - Forked for compatibility updates and further design features"}, {"date": "2016-04-13", "details": "DAO Fork (2016) - Roll back after DAO $50 million in DAO funds were stolen, creating Ethereum Classic and today's Ethereum chain"}, {"date": "2016-05-13", "details": "<PERSON><PERSON><PERSON> (2016) - Fork to prevent Denial-of-Service attack spamming empty transactions"}, {"date": "2016-06-13", "details": "Spurious Dragon (2016) - Second Fork to prevent DoS attacks, adjusting opcode fees, and \"enabling 'debloat'\" of blockchain"}, {"date": "2017-07-13", "details": "Byzantium (2017) - Reduced block rewards from 5 ETH to 3 ETH, delayed difficulty bomb by 1 year, increased transaction privacy and Layer-2 network scaling activities"}, {"date": "2019-08-13", "details": "Constantinople (2019) - Reduced block rewards from 3 ETH to 2 ETH and EVM gas optimizations"}, {"date": "2019-03-13", "details": "Istanbul (2019) - Improved DoS prevention, optimized EVM gas costs, increased efficiency of SNARK and STARK proofs on the network"}, {"date": "2020-11-13", "details": "Muir Glacier (2020) - Delayed difficulty bomb due to increased difficulty with Istanbul fork"}, {"date": "2020-10-13", "details": "Beacon Chain Genesis (2020) - Deployment of the Beacon Chain for validators to stake ETH tokens and begin process of transferring to Proof of Stake from Proof of Work"}, {"date": "2021-09-13", "details": "Berlin (2021) - Optimized EVM gas costs"}, {"date": "2021-08-13", "details": "London (2021) - Introduction of EIP-1559 with optimized fee mechanisms separating base fee and priority fee for transaction inclusion in current block"}, {"date": "2021-07-13", "details": "Altair (Beacon Chain Fork) (2021) - Enabled light client interaction and increase validator activity"}, {"date": "2021-06-13", "details": "Arrow Glacier (2021) - Delayed difficulty bomb"}, {"date": "2022-05-13", "details": "Gray Glacier (2022) - Delayed difficulty bomb"}, {"date": "2022-04-13", "details": "Bellatrix (Beacon Chain Fork) (2022) - Increased slashable balances for validators on Beacon Chain in preparation for The Merge"}, {"date": "2022-03-13", "details": "Paris (Consensus Layer Fork) (2022) - Turned off Proof-of-Work and tell execution clients to connect to consensus clients for Proof-of-Stake"}, {"date": "2023-02-13", "details": "Shanghai-Capella (2023) - Enabled staking withdrawals to both execution layer and consensus layer respectively"}, {"date": "2024-01-13", "details": "Cancun<PERSON><PERSON><PERSON> (2024) - EIP-4844 enabled proto-danksharding to improve Layer-2 transaction costs and data availability decreased validator churn rate on consensus layer"}], "rollup_info": [{"rollup_type": "N/A", "state_validation": "N/A"}], "network_scaling_solution": "true", "type_of_network": "Blockchain", "chain_explorers": ["https://etherscan.io", "https://ethplorer.io"]}]}}