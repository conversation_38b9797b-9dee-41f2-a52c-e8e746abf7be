{"status": 200, "headers": {"content-type": "application/json"}, "content": {"data": [{"full_name": "Avalanche P-Chain", "network": "avaxp", "modular_or_monolithic": "Polylithic", "overview": "Avalanche P-Chai network", "consensus_mechanism": "Proof-of-Stake", "hashing_algorithm": "SHA256 and ripemd160", "transaction_finality": "No Fixed Finality Threshold", "available_clients": ["AvalancheGo"], "smart_contract_deployment": "Permissionless", "foundation": ["Ava Labs: A team of developers building and maintaining the infrastructure for the Avalanche network."], "founding_team_identity": "Known", "repository": [{"github_repository": "Avalanche Go Implementation", "github_url": "github.com/ava-labs/avalanchego"}], "transaction_visibility": "Visible", "native_fee_token": "AVAX", "rollup_info": [{"rollup_type": "N/A", "state_validation": "N/A"}], "network_scaling_solution": "true", "type_of_network": "Blockchain", "chain_explorers": ["https://snowtrace.io", "https://avascan.info", "https://subnets.avax.network"]}], "next_page_token": "YnRj", "next_page_url": "http://127.0.0.1:8080/v4/profile/networks?networks=btc,avaxp&api_key=x1&page_size=1&paging_from=start&next_page_token=YnRj"}}