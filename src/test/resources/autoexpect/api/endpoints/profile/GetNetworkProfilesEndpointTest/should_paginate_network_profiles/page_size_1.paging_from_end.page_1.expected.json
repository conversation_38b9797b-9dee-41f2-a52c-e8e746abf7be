{"status": 200, "headers": {"content-type": "application/json"}, "content": {"data": [{"full_name": "Ethereum", "network": "eth", "modular_or_monolithic": "Semi Modular", "overview": "Ethereum network", "consensus_mechanism": "Proof-of-Stake", "hashing_algorithm": "KECCAK-256", "transaction_finality": "2 Epochs under normal circumstances", "available_clients": ["Execution Client:", "geth: github.com/ethereum/go-ethereum", "Nethermind: github.com/NethermindEth/nethermind", "Besu: github.com/hyperledger/besu", "Erigon: github.com/ledgerwatch/erigon", "Rust: https://github.com/paradigmxyz/reth", "Consensus Client:", "Lighthouse: github.com/sigp/lighthouse", "Lodestar: github.com/ChainSafe/lodestar", "Nimbus: github.com/status-im/nimbus-eth1", "Prysm: github.com/prysmaticlabs/prysm", "Teku: github.com/ConsenSys/teku"], "smart_contract_deployment": "Permissionless", "foundation": ["Ethereum Foundation: A non-profit organization that supports Ethereum and related technologies. Hosts a platform of communities to join and contribute directly to the Ethereum blockchain."], "founding_team_identity": "Known", "repository": [{"github_repository": "get", "github_url": "github.com/ethereum/go-ethereum"}, {"github_repository": "Nethermind", "github_url": "github.com/NethermindEth/nethermind"}, {"github_repository": "Besu", "github_url": "github.com/hyperledger/besu"}, {"github_repository": "<PERSON><PERSON><PERSON>", "github_url": "github.com/ledgerwatch/erigon"}, {"github_repository": "Lighthouse", "github_url": "github.com/sigp/lighthouse"}, {"github_repository": "Lodestar", "github_url": "github.com/ChainSafe/lodestar"}, {"github_repository": "Nimbus", "github_url": "github.com/status-im/nimbus-eth1"}, {"github_repository": "Prysm", "github_url": "github.com/prysmaticlabs/prysm"}, {"github_repository": "<PERSON><PERSON>", "github_url": "github.com/ConsenSys/teku"}, {"github_repository": "EIPs", "github_url": "github.com/ethereum/EIPs"}], "transaction_visibility": "Visible", "native_fee_token": "ETH", "significant_historical_changes": [{"date": "2016-06-17", "details": "Ethereum DAO was hacked for $50 million"}, {"date": "2021-08-05", "details": "EIP-1559 was introduced restructuring the fee market and introducing a variable base fee that is burned"}, {"date": "2022-09-15", "details": "Ethereum migrated from Proof-of-Work to Proof-of-Stake completing the merge"}, {"date": "2024-03-13", "details": "EIP-4844 introduced to provide dedicated data space or blobspace in blocks for Layer-2 rollups"}], "upgrade_history": [{"date": "2015-03-13", "details": "Frontier Thawing (2015) - Forked to increase security and speed of future forks"}, {"date": "2016-02-13", "details": "Homestead (2016) - Forked for compatibility updates and further design features"}, {"date": "2016-04-13", "details": "DAO Fork (2016) - Roll back after DAO $50 million in DAO funds were stolen, creating Ethereum Classic and today's Ethereum chain"}, {"date": "2016-05-13", "details": "<PERSON><PERSON><PERSON> (2016) - Fork to prevent Denial-of-Service attack spamming empty transactions"}, {"date": "2016-06-13", "details": "Spurious Dragon (2016) - Second Fork to prevent DoS attacks, adjusting opcode fees, and \"enabling 'debloat'\" of blockchain"}, {"date": "2017-07-13", "details": "Byzantium (2017) - Reduced block rewards from 5 ETH to 3 ETH, delayed difficulty bomb by 1 year, increased transaction privacy and Layer-2 network scaling activities"}, {"date": "2019-08-13", "details": "Constantinople (2019) - Reduced block rewards from 3 ETH to 2 ETH and EVM gas optimizations"}, {"date": "2019-03-13", "details": "Istanbul (2019) - Improved DoS prevention, optimized EVM gas costs, increased efficiency of SNARK and STARK proofs on the network"}, {"date": "2020-11-13", "details": "Muir Glacier (2020) - Delayed difficulty bomb due to increased difficulty with Istanbul fork"}, {"date": "2020-10-13", "details": "Beacon Chain Genesis (2020) - Deployment of the Beacon Chain for validators to stake ETH tokens and begin process of transferring to Proof of Stake from Proof of Work"}, {"date": "2021-09-13", "details": "Berlin (2021) - Optimized EVM gas costs"}, {"date": "2021-08-13", "details": "London (2021) - Introduction of EIP-1559 with optimized fee mechanisms separating base fee and priority fee for transaction inclusion in current block"}, {"date": "2021-07-13", "details": "Altair (Beacon Chain Fork) (2021) - Enabled light client interaction and increase validator activity"}, {"date": "2021-06-13", "details": "Arrow Glacier (2021) - Delayed difficulty bomb"}, {"date": "2022-05-13", "details": "Gray Glacier (2022) - Delayed difficulty bomb"}, {"date": "2022-04-13", "details": "Bellatrix (Beacon Chain Fork) (2022) - Increased slashable balances for validators on Beacon Chain in preparation for The Merge"}, {"date": "2022-03-13", "details": "Paris (Consensus Layer Fork) (2022) - Turned off Proof-of-Work and tell execution clients to connect to consensus clients for Proof-of-Stake"}, {"date": "2023-02-13", "details": "Shanghai-Capella (2023) - Enabled staking withdrawals to both execution layer and consensus layer respectively"}, {"date": "2024-01-13", "details": "Cancun<PERSON><PERSON><PERSON> (2024) - EIP-4844 enabled proto-danksharding to improve Layer-2 transaction costs and data availability decreased validator churn rate on consensus layer"}], "rollup_info": [{"rollup_type": "N/A", "state_validation": "N/A"}], "network_scaling_solution": "true", "type_of_network": "Blockchain", "chain_explorers": ["https://etherscan.io", "https://ethplorer.io"]}], "next_page_token": "YnRj", "next_page_url": "http://127.0.0.1:8080/v4/profile/networks?api_key=x1&page_size=1&paging_from=end&next_page_token=YnRj"}}