{"status": 200, "headers": {"content-type": "application/json"}, "content": {"data": [{"asset": "audio", "full_name": "<PERSON><PERSON>", "description": "Audius is a decentralized content and streaming platform streaming protocol. The AUDIO token incentivizes activity on the Audius platform, which allows artists and fans to transact directly, host and share content, and make governance decisions for the Audius protocol.", "overview": "Audius is a decentralized content and streaming platform streaming protocol. The AUDIO token incentivizes activity on the Audius platform, which allows artists and fans to transact directly, host and share content, and make governance decisions for the Audius protocol. Audius aims to solve the inefficiencies present in the current music industry by creating an economy that allows for more value to be captured by artists, creates more transparency in distribution and intellectual property rights, and directs connections between artists and content consumers. AUDIO can be staked to ensure the security of the protocol, unlock features, and exert influence in governance decisions. The Audius platform also allows for content to be hosted using its decentralized storage on AudSP, the Audius-native extension 5 to IPFS. Run using “Content Nodes”, gives content creators the power of distributing and permissioning content at their discretion. ", "creation_date": "2020-10-21", "website": "https://audius.org", "whitepaper_url": "https://whitepaper.audius.co/AudiusWhitepaper.pdf", "supply_cap": "1115757599"}], "next_page_token": "YmFkZ2Vy", "next_page_url": "http://127.0.0.1:8080/v4/profile/assets?api_key=x1&page_size=1&paging_from=start&next_page_token=YmFkZ2Vy"}}