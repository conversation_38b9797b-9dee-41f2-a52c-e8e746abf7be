{"status": 200, "headers": {"content-type": "application/json"}, "content": {"data": [{"metric": "AdrActCnt", "full_name": "Addresses, active, count", "description": "The sum count of unique addresses that were active in the network (either as a recipient or originator of a ledger change) that interval. All parties in a ledger change action (recipients and originators) are counted. Individual addresses are not double-counted if previously active.", "product": "Network Data", "category": "Addresses", "subcategory": "Active", "unit": "Addresses", "data_type": "bigint", "type": "Sum", "display_name": "Active Addr Cnt", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/addresses/active-addresses"}, {"metric": "volume_trusted_spot_usd_1h", "full_name": "Volume, trusted, spot, USD, one hour", "description": "The sum of all volume from the spot markets of a set of trusted exchanges in units of U.S. dollars.", "product": "Market Data", "category": "Volume", "subcategory": "Trusted", "unit": "USD", "data_type": "decimal", "type": "Sum", "display_name": "Trusted Spot Volume", "constituent_snapshots_url": "http://127.0.0.1:8080/v4/constituent-snapshots/asset-metrics?metric=volume_trusted_spot_usd_1h&api_key=x1", "constituent_timeframes_url": "http://127.0.0.1:8080/v4/constituent-timeframes/asset-metrics?metric=volume_trusted_spot_usd_1h&api_key=x1", "docs_url": "https://docs.coinmetrics.io/market-data/market-data-overview/volume/volume_trusted"}]}}