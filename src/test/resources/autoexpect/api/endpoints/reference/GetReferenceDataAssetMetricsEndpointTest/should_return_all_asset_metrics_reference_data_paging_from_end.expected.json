{"status": 200, "headers": {"content-type": "application/json"}, "content": {"data": [{"metric": "FlowInBSPUSD", "full_name": "Flow, in, to Bitstamp, USD", "description": "The sum USD value sent to Bitstamp that interval.", "product": "Network Data", "category": "Exchange", "subcategory": "Deposits", "unit": "USD", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Bitstamp Deposits (USD)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/deposits"}, {"metric": "FlowInBTXNtv", "full_name": "Flow, in, to Bittrex, native units", "description": "The sum in native units sent to Bittrex that interval.", "product": "Network Data", "category": "Exchange", "subcategory": "Deposits", "unit": "Native units", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Bittrex Deposits (native units)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/deposits"}, {"metric": "FlowInBTXUSD", "full_name": "Flow, in, to Bittrex, USD", "description": "The sum USD value sent to Bittrex that interval.", "product": "Network Data", "category": "Exchange", "subcategory": "Deposits", "unit": "USD", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Bittrex Deposits (USD)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/deposits"}, {"metric": "FlowInExInclNtv", "full_name": "Flow, in, to exchanges, inclusive, native units", "description": "The sum number of native units sent to exchanges that interval, including exchange to exchange activity.", "product": "Network Data", "category": "Exchange", "subcategory": "Deposits", "unit": "Native units", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Exchange Deposits, Incl EtoE (native units)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/deposits"}, {"metric": "FlowInExInclUSD", "full_name": "Flow, in, to exchanges, inclusive, USD", "description": "The sum USD value sent to exchanges that interval, including exchange to exchange activity.", "product": "Network Data", "category": "Exchange", "subcategory": "Deposits", "unit": "USD", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Exchange Deposits, Incl EtoE (USD)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/deposits"}, {"metric": "FlowInExNtv", "full_name": "Flow, in, to exchanges, native units", "description": "The sum number of native units sent to exchanges that interval, excluding exchange to exchange activity.", "product": "Network Data", "category": "Exchange", "subcategory": "Deposits", "unit": "Native units", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Exchange Deposits (native units)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/deposits"}, {"metric": "FlowInExUSD", "full_name": "Flow, in, to exchanges, USD", "description": "The sum USD value sent to exchanges that interval, excluding exchange to exchange activity.", "product": "Network Data", "category": "Exchange", "subcategory": "Deposits", "unit": "USD", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Exchange Deposits (USD)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/deposits"}, {"metric": "FlowInGEMNtv", "full_name": "Flow, in, to Gemini, native units", "description": "The sum in native units sent to Gemini that interval.", "product": "Network Data", "category": "Exchange", "subcategory": "Deposits", "unit": "Native units", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Gemini Deposits (native units)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/deposits"}, {"metric": "FlowInGEMUSD", "full_name": "Flow, in, to Gemini, USD", "description": "The sum USD value sent to Gemini that interval.", "product": "Network Data", "category": "Exchange", "subcategory": "Deposits", "unit": "USD", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Gemini Deposits (USD)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/deposits"}, {"metric": "FlowInKRKNtv", "full_name": "Flow, in, to Kraken, native units", "description": "The sum in native units sent to Kraken that interval.", "product": "Network Data", "category": "Exchange", "subcategory": "Deposits", "unit": "Native units", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Kraken Deposits (native units)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/deposits"}, {"metric": "FlowInKRKUSD", "full_name": "Flow, in, to Kraken, USD", "description": "The sum USD value sent to Kraken that interval.", "product": "Network Data", "category": "Exchange", "subcategory": "Deposits", "unit": "USD", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "<PERSON><PERSON><PERSON> (USD)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/deposits"}, {"metric": "FlowInPOLNtv", "full_name": "Flow, in, to Poloniex, native units", "description": "The sum in native units sent to Poloniex that interval.", "product": "Network Data", "category": "Exchange", "subcategory": "Deposits", "unit": "Native units", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Poloniex Deposits (native units)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/deposits"}, {"metric": "FlowInPOLUSD", "full_name": "Flow, in, to Poloniex, USD", "description": "The sum USD value sent to Poloniex that interval.", "product": "Network Data", "category": "Exchange", "subcategory": "Deposits", "unit": "USD", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "<PERSON><PERSON><PERSON> (USD)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/deposits"}, {"metric": "FlowOutBFXNtv", "full_name": "Flow, out, from Bitfinex, native units", "description": "The sum in native units withdrawn from Bitfinex that interval.", "product": "Network Data", "category": "Exchange", "subcategory": "<PERSON><PERSON><PERSON><PERSON>", "unit": "Native units", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Bitfinex Withdrawals (native units)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/withdrawals"}, {"metric": "FlowOutBFXUSD", "full_name": "Flow, out, from Bitfinex, USD", "description": "The sum USD value withdrawn from Bitfinex that interval.", "product": "Network Data", "category": "Exchange", "subcategory": "<PERSON><PERSON><PERSON><PERSON>", "unit": "USD", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Bitfinex Withdrawals (USD)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/withdrawals"}, {"metric": "FlowOutBNBNtv", "full_name": "Flow, out, from Binance, native units", "description": "The sum in native units withdrawn from Binance that interval.", "product": "Network Data", "category": "Exchange", "subcategory": "<PERSON><PERSON><PERSON><PERSON>", "unit": "Native units", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Binance Withdrawals (native units)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/withdrawals"}, {"metric": "FlowOutBNBUSD", "full_name": "Flow, out, from Binance, USD", "description": "The sum USD value withdrawn from Binance that interval.", "product": "Network Data", "category": "Exchange", "subcategory": "<PERSON><PERSON><PERSON><PERSON>", "unit": "USD", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Binance Withdrawals (USD)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/withdrawals"}, {"metric": "FlowOutBSPNtv", "full_name": "Flow, out, from Bitstamp, native units", "description": "The sum in native units withdrawn from Bitstamp that interval.", "product": "Network Data", "category": "Exchange", "subcategory": "<PERSON><PERSON><PERSON><PERSON>", "unit": "Native units", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Bitstamp Withdrawals (native units)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/withdrawals"}, {"metric": "FlowOutBSPUSD", "full_name": "Flow, out, from Bitstamp, USD", "description": "The sum USD value withdrawn from Bitstamp that interval.", "product": "Network Data", "category": "Exchange", "subcategory": "<PERSON><PERSON><PERSON><PERSON>", "unit": "USD", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Bitstamp Withdrawals (USD)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/withdrawals"}, {"metric": "FlowOutBTXNtv", "full_name": "Flow, out, from Bittrex, native units", "description": "The sum in native units withdrawn from Bittrex that interval.", "product": "Network Data", "category": "Exchange", "subcategory": "<PERSON><PERSON><PERSON><PERSON>", "unit": "Native units", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Bittrex Withdrawals (native units)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/withdrawals"}, {"metric": "FlowOutBTXUSD", "full_name": "Flow, out, from Bittrex, USD", "description": "The sum USD value withdrawn from Bittrex that interval.", "product": "Network Data", "category": "Exchange", "subcategory": "<PERSON><PERSON><PERSON><PERSON>", "unit": "USD", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Bittrex Withdrawals (USD)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/withdrawals"}, {"metric": "FlowOutExInclNtv", "full_name": "Flow, out, from exchanges, inclusive, native units", "description": "The sum in native units withdrawn from exchanges that interval, including exchange to exchange activity.", "product": "Network Data", "category": "Exchange", "subcategory": "<PERSON><PERSON><PERSON><PERSON>", "unit": "Native units", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Exchange Withdrawals, Incl EtoE (native units)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/withdrawals"}, {"metric": "FlowOutExInclUSD", "full_name": "Flow, out, from exchanges, inclusive, USD", "description": "The sum USD value withdrawn from exchanges that interval, including exchange to exchange activity.", "product": "Network Data", "category": "Exchange", "subcategory": "<PERSON><PERSON><PERSON><PERSON>", "unit": "USD", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Exchange Withdrawals, Incl EtoE (USD)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/withdrawals"}, {"metric": "FlowOutExNtv", "full_name": "Flow, out, from exchanges, native units", "description": "The sum in native units withdrawn from exchanges that interval, excluding exchange to exchange activity.", "product": "Network Data", "category": "Exchange", "subcategory": "<PERSON><PERSON><PERSON><PERSON>", "unit": "Native units", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Exchange Withdrawals (native units)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/withdrawals"}, {"metric": "FlowOutExUSD", "full_name": "Flow, out, from exchanges, USD", "description": "The sum USD value withdrawn from exchanges that interval, excluding exchange to exchange activity.", "product": "Network Data", "category": "Exchange", "subcategory": "<PERSON><PERSON><PERSON><PERSON>", "unit": "USD", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Exchange Withdrawals (USD)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/withdrawals"}, {"metric": "FlowOutGEMNtv", "full_name": "Flow, out, from Gemini, native units", "description": "The sum in native units withdrawn from Gemini that interval.", "product": "Network Data", "category": "Exchange", "subcategory": "<PERSON><PERSON><PERSON><PERSON>", "unit": "Native units", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Gemini Withdrawals (native units)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/withdrawals"}, {"metric": "FlowOutGEMUSD", "full_name": "Flow, out, from Gemini, USD", "description": "The sum USD value withdrawn from Gemini that interval.", "product": "Network Data", "category": "Exchange", "subcategory": "<PERSON><PERSON><PERSON><PERSON>", "unit": "USD", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Gemini Withdrawals (USD)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/withdrawals"}, {"metric": "FlowOutKRKNtv", "full_name": "Flow, out, from Kraken, native units", "description": "The sum in native units withdrawn from Kraken that interval.", "product": "Network Data", "category": "Exchange", "subcategory": "<PERSON><PERSON><PERSON><PERSON>", "unit": "Native units", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Kraken Withdrawals (native units)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/withdrawals"}, {"metric": "FlowOutKRKUSD", "full_name": "Flow, out, from Kraken, USD", "description": "The sum USD value withdrawn from Kraken that interval.", "product": "Network Data", "category": "Exchange", "subcategory": "<PERSON><PERSON><PERSON><PERSON>", "unit": "USD", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "<PERSON><PERSON><PERSON> (USD)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/withdrawals"}, {"metric": "FlowOutPOLNtv", "full_name": "Flow, out, from Poloniex, native units", "description": "The sum in native units withdrawn from Poloniex that interval.", "product": "Network Data", "category": "Exchange", "subcategory": "<PERSON><PERSON><PERSON><PERSON>", "unit": "Native units", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Poloniex Withdrawals (native units)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/withdrawals"}, {"metric": "FlowOutPOLUSD", "full_name": "Flow, out, from Poloniex, USD", "description": "The sum USD value withdrawn from Poloniex that interval.", "product": "Network Data", "category": "Exchange", "subcategory": "<PERSON><PERSON><PERSON><PERSON>", "unit": "USD", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "<PERSON><PERSON><PERSON> (USD)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/withdrawals"}, {"metric": "FlowTfrFromExCnt", "full_name": "Flow, transfers, from exchanges, count", "description": "The sum count of transfers from any address belonging to an exchange in that interval. Transfers between exchanges are not counted.", "product": "Network Data", "category": "Exchange", "subcategory": "<PERSON><PERSON><PERSON><PERSON>", "unit": "Transfers", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Exchange Withdrawal Cnt", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/withdrawals"}, {"metric": "FlowTfrFromExInclCnt", "full_name": "Flow, transfers, from exchanges, inclusive, count", "description": "The sum count of transfers from any address belonging to an exchange in that interval. Transfers between exchanges are counted.", "product": "Network Data", "category": "Exchange", "subcategory": "<PERSON><PERSON><PERSON><PERSON>", "unit": "Transfers", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Exchange Withdrawal Cnt, Incl EtoE", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/withdrawals"}, {"metric": "FlowTfrInBFXCnt", "full_name": "Flow, transfers, to Bitfinex, count", "description": "The sum count of transfers to any address belonging to Bitfinex in that interval. If the sender address also belongs to Bitfinex, the transfer is not counted.", "product": "Network Data", "category": "Exchange", "subcategory": "Deposits", "unit": "Transfers", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Bitfinex Deposit Cnt", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/deposits"}, {"metric": "FlowTfrInBNBCnt", "full_name": "Flow, transfers, to Binance, count", "description": "The sum count of transfers to any address belonging to Binance in that interval. If the sender address also belongs to Binance, the transfer is not counted.", "product": "Network Data", "category": "Exchange", "subcategory": "Deposits", "unit": "Transfers", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Binance Deposit Cnt", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/deposits"}, {"metric": "FlowTfrInBSPCnt", "full_name": "Flow, transfers, to Bitstamp, count", "description": "The sum count of transfers to any address belonging to Bitstamp in that interval. If the sender address also belongs to Bitstamp, the transfer is not counted.", "product": "Network Data", "category": "Exchange", "subcategory": "Deposits", "unit": "Transfers", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Bitstamp Deposit Cnt", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/deposits"}, {"metric": "FlowTfrInBTXCnt", "full_name": "Flow, transfers, to Bittrex, count", "description": "The sum count of transfers to any address belonging to Bittrex in that interval. If the sender address also belongs to Bittrex, the transfer is not counted.", "product": "Network Data", "category": "Exchange", "subcategory": "Deposits", "unit": "Transfers", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Bittrex Deposit Cnt", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/deposits"}, {"metric": "FlowTfrInGEMCnt", "full_name": "Flow, transfers, to Gemini, count", "description": "The sum count of transfers to any address belonging to <PERSON> in that interval. If the sender address also belongs to <PERSON>, the transfer is not counted.", "product": "Network Data", "category": "Exchange", "subcategory": "Deposits", "unit": "Transfers", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Gemini Deposit Cnt", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/deposits"}, {"metric": "FlowTfrInKRKCnt", "full_name": "Flow, transfers, to Kraken, count", "description": "The sum count of transfers to any address belonging to K<PERSON><PERSON> in that interval. If the sender address also belongs to Kraken, the transfer is not counted.", "product": "Network Data", "category": "Exchange", "subcategory": "Deposits", "unit": "Transfers", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "<PERSON>rak<PERSON> Deposit Cnt", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/deposits"}, {"metric": "FlowTfrInPOLCnt", "full_name": "Flow, transfers, to Poloniex, count", "description": "The sum count of transfers to any address belonging to Polonie<PERSON> in that interval. If the sender address also belongs to Poloniex, the transfer is not counted.", "product": "Network Data", "category": "Exchange", "subcategory": "Deposits", "unit": "Transfers", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Poloniex Deposit Cnt", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/deposits"}, {"metric": "FlowTfrOutBFXCnt", "full_name": "Flow, transfers, from Bitfinex, count", "description": "The sum count of transfers from any address belonging to Bitfinex in that interval. If the recipient address also belongs to Bitfinex, the transfer is not counted.", "product": "Network Data", "category": "Exchange", "subcategory": "<PERSON><PERSON><PERSON><PERSON>", "unit": "Transfers", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Bitfinex Withdrawal Cnt", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/withdrawals"}, {"metric": "FlowTfrOutBNBCnt", "full_name": "Flow, transfers, from Binance, count", "description": "The sum count of transfers from any address belonging to Binance in that interval. If the recipient address also belongs to Binance, the transfer is not counted.", "product": "Network Data", "category": "Exchange", "subcategory": "<PERSON><PERSON><PERSON><PERSON>", "unit": "Transfers", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Binance Withdrawal Cnt", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/withdrawals"}, {"metric": "FlowTfrOutBSPCnt", "full_name": "Flow, transfers, from Bitstamp, count", "description": "The sum count of transfers from any address belonging to Bitstamp in that interval. If the recipient address also belongs to Bitstamp, the transfer is not counted.", "product": "Network Data", "category": "Exchange", "subcategory": "<PERSON><PERSON><PERSON><PERSON>", "unit": "Transfers", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Bitstamp Withdrawal Cnt", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/withdrawals"}, {"metric": "FlowTfrOutBTXCnt", "full_name": "Flow, transfers, from Bittrex, count", "description": "The sum count of transfers from any address belonging to Bittrex in that interval. If the recipient address also belongs to Bittrex, the transfer is not counted.", "product": "Network Data", "category": "Exchange", "subcategory": "<PERSON><PERSON><PERSON><PERSON>", "unit": "Transfers", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Bittrex Withdrawal Cnt", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/withdrawals"}, {"metric": "FlowTfrOutGEMCnt", "full_name": "Flow, transfers, from Gemini, count", "description": "The sum count of transfers from any address belonging to <PERSON> in that interval. If the recipient address also belongs to <PERSON>, the transfer is not counted.", "product": "Network Data", "category": "Exchange", "subcategory": "<PERSON><PERSON><PERSON><PERSON>", "unit": "Transfers", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Gemini Withdrawal Cnt", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/withdrawals"}, {"metric": "FlowTfrOutKRKCnt", "full_name": "Flow, transfers, from Kraken, count", "description": "The sum count of transfers from any address belonging to K<PERSON><PERSON> in that interval. If the recipient address also belongs to Kraken, the transfer is not counted.", "product": "Network Data", "category": "Exchange", "subcategory": "<PERSON><PERSON><PERSON><PERSON>", "unit": "Transfers", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "<PERSON><PERSON><PERSON>", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/withdrawals"}, {"metric": "FlowTfrOutPOLCnt", "full_name": "Flow, transfers, from Poloniex, count", "description": "The sum count of transfers from any address belonging to <PERSON>nie<PERSON> in that interval. If the recipient address also belongs to Poloniex, the transfer is not counted.", "product": "Network Data", "category": "Exchange", "subcategory": "<PERSON><PERSON><PERSON><PERSON>", "unit": "Transfers", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "<PERSON><PERSON><PERSON>", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/withdrawals"}, {"metric": "FlowTfrToExCnt", "full_name": "Flow, transfers, to exchanges, count", "description": "The sum count of transfers to any address belonging to an exchange in that interval. Transfers between exchanges are not counted.", "product": "Network Data", "category": "Exchange", "subcategory": "Deposits", "unit": "Transfers", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Exchange Deposit Cnt", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/deposits"}, {"metric": "FlowTfrToExInclCnt", "full_name": "Flow, transfers, to exchanges, inclusive, count", "description": "The sum count of transfers to any address belonging to an exchange in that interval. Transfers between exchanges are counted.", "product": "Network Data", "category": "Exchange", "subcategory": "Deposits", "unit": "Transfers", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Exchange Deposit Cnt, Incl EtoE", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/deposits"}, {"metric": "PriceUSD", "full_name": "Price, USD", "description": "The fixed closing price of the asset as of 00:00 UTC the following day (i.e., midnight UTC of the current day) denominated in USD. This price is generated by Coin Metrics' fixing/reference rate service. Real-time PriceUSD is the fixed closing price of the asset as of the timestamp set by the block's miner.", "product": "Network Data", "category": "Market", "subcategory": "Price", "unit": "USD", "data_type": "decimal", "type": "NA", "display_name": "USD Denominated Closing Price", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/market/price"}, {"metric": "ReferenceRate", "full_name": "Reference Rate, USD", "description": "The price of an asset quoted in U.S. dollars using a framework to select high quality constituent markets and a methodology that is resistant manipulation.", "product": "CM Prices", "category": "Market", "subcategory": "Price", "unit": "USD", "data_type": "decimal", "type": "NA", "docs_url": "https://docs.coinmetrics.io/market-data/reference-rates-overview/"}, {"metric": "ReferenceRateETH", "full_name": "Reference Rate, ETH", "description": "The price of an asset quoted in Etherium using a framework to select high quality constituent markets and a methodology that is resistant manipulation.", "product": "CM Prices", "category": "Market", "subcategory": "Price", "unit": "ETH", "data_type": "decimal", "type": "NA", "docs_url": "https://docs.coinmetrics.io/market-data/reference-rates-overview/"}, {"metric": "ReferenceRateEUR", "full_name": "Reference Rate, EUR", "description": "The price of an asset quoted in Euros using a framework to select high quality constituent markets and a methodology that is resistant manipulation.", "product": "CM Prices", "category": "Market", "subcategory": "Price", "unit": "EUR", "data_type": "decimal", "type": "NA", "docs_url": "https://docs.coinmetrics.io/market-data/reference-rates-overview/"}, {"metric": "ReferenceRateUSD", "full_name": "Reference Rate, USD", "description": "The price of an asset quoted in U.S. dollars using a framework to select high quality constituent markets and a methodology that is resistant manipulation.", "product": "CM Prices", "category": "Market", "subcategory": "Price", "unit": "USD", "data_type": "decimal", "type": "NA", "docs_url": "https://docs.coinmetrics.io/market-data/reference-rates-overview/"}, {"metric": "RevNtv", "full_name": "Miner revenue, native units", "description": "The sum native units of all miner revenue (fees plus newly issued native units) that interval.", "product": "Network Data", "category": "Fees and Revenue", "subcategory": "Revenue", "unit": "Native units", "data_type": "decimal", "type": "Sum", "display_name": "Miner Revenue (native units)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/fees-and-revenue/revenue"}, {"metric": "SplyExUSD", "full_name": "Supply, held by exchanges, USD", "description": "The sum USD value of all native units held in hot or cold exchange wallets that interval.", "product": "Network Data", "category": "Exchange", "subcategory": "Exchange Supply", "unit": "USD", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Exchange Supply (USD)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/exchange-supply"}, {"metric": "TxCnt", "full_name": "Transactions, count", "description": "The sum count of transactions that interval. Transactions represent a bundle of intended actions to alter the ledger initiated by a user (human or machine). Transactions are counted whether they execute or not and whether they result in the transfer of native units or not (a transaction can result in no, one, or many transfers). Changes to the ledger mandated by the protocol (and not by a user) or post-launch new issuance issued by a founder or controlling entity are not included here.", "product": "Network Data", "category": "Transactions", "subcategory": "Transactions", "unit": "Transactions", "data_type": "bigint", "type": "Sum", "display_name": "Tx Cnt", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/transactions/transactions"}, {"metric": "TxTfrValMedNtv", "full_name": "Transactions, transfers, value, median, native units", "description": "The median count of native units transferred per transfer (i.e., the median size of a transfer) between distinct addresses that interval.", "product": "Network Data", "category": "Transactions", "subcategory": "Transfer Value", "unit": "Native units", "data_type": "decimal", "type": "Median", "display_name": "Median Tx Size (native units)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/transactions/transfer-value"}, {"metric": "block_count_at_tip", "full_name": "Block, count, at tip, one block", "description": "The number of blocks identified at the chain tip.", "product": "FARUM", "category": "KRI", "subcategory": "Blocks", "unit": "Blocks", "data_type": "bigint", "type": "N/A", "display_name": "1 Block Count of Blocks at the Chain Tip", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/kri/blocks"}, {"metric": "block_count_empty_6b", "full_name": "Block, count, empty, six blocks", "description": "The number of empty blocks in the past 6 blocks.", "product": "FARUM", "category": "KRI", "subcategory": "Blocks", "unit": "Blocks", "data_type": "bigint", "type": "Sum", "display_name": "1 Block Count of Empty Blocks from the Past 6 Blocks", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/kri/blocks"}, {"metric": "block_difficulty", "full_name": "Block, difficulty, one block", "description": "The difficulty of the block.", "product": "FARUM", "category": "KRI", "subcategory": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "data_type": "decimal", "type": "N/A", "display_name": "1 Block Mining Difficulty", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/mining/difficulty"}, {"metric": "block_feerate_min", "full_name": "Block, feerate, min, one block", "description": "Mined block's min mined feerate in native units per vByte.", "product": "FARUM", "category": "KRI", "subcategory": "Transaction feerates", "unit": "Satoshi/vByte", "data_type": "decimal", "type": "Min", "display_name": "1 Block Min Transaction Feerate", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/kri/transaction-feerates"}, {"metric": "block_fees", "full_name": "Block, fees, one block", "description": "The sum of fees in the mined block, in native units.", "product": "FARUM", "category": "KRI", "subcategory": "Transaction fees", "unit": "Native units", "data_type": "decimal", "type": "Sum", "display_name": "1 Block Sum of Transaction Fees", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/kri/transaction-fees"}, {"metric": "block_missed_slots", "full_name": "Missed Slots", "description": "The count of missed slots.", "product": "FARUM", "category": "KRI", "subcategory": "Empty Blocks", "unit": "Slots", "data_type": "bigint", "type": "Sum", "display_name": "Count of missed slots.", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/kri/empty-blocks"}, {"metric": "block_tx_count", "full_name": "Block, transaction count, one block", "description": "Mined block's transaction count.", "product": "FARUM", "category": "KRI", "subcategory": "Transactions", "unit": "Transactions", "data_type": "bigint", "type": "N/A", "display_name": "1 Block Transaction Count", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/kri/transactions"}, {"metric": "futures_aggregate_funding_rate_all_margin_1d_period", "full_name": "Funding rate, aggregated, futures, all-margined, 1 day", "description": "The average funding rate weighted by open interest from all futures markets, regardless of the margin asset, converted to a 1 day period.", "product": "Market Data", "category": "Funding Rates", "subcategory": "Futures", "unit": "Dimensionless", "data_type": "decimal", "type": "Percentage", "display_name": "Aggregated All-Margined Futures Funding Rate, 1 Day", "docs_url": "https://docs.coinmetrics.io/market-data/market-data-overview/funding-rates/"}, {"metric": "futures_aggregate_funding_rate_all_margin_1y_period", "full_name": "Funding rate, aggregated, futures, all-margined, 1 year", "description": "The average funding rate weighted by open interest from all futures markets, regardless of the margin asset, converted to a 1 year period.", "product": "Market Data", "category": "Funding Rates", "subcategory": "Futures", "unit": "Dimensionless", "data_type": "decimal", "type": "Percentage", "display_name": "Aggregated All-Margined Futures Funding Rate, 1 Year", "docs_url": "https://docs.coinmetrics.io/market-data/market-data-overview/funding-rates/"}, {"metric": "futures_aggregate_funding_rate_all_margin_30d_period", "full_name": "Funding rate, aggregated, futures, all-margined, 30 days", "description": "The average funding rate weighted by open interest from all futures markets, regardless of the margin asset, converted to a 30 day period.", "product": "Market Data", "category": "Funding Rates", "subcategory": "Futures", "unit": "Dimensionless", "data_type": "decimal", "type": "Percentage", "display_name": "Aggregated All-Margined Futures Funding Rate, 30 Days", "docs_url": "https://docs.coinmetrics.io/market-data/market-data-overview/funding-rates/"}, {"metric": "futures_aggregate_funding_rate_all_margin_8h_period", "full_name": "Funding rate, aggregated, futures, all-margined, 8 hours", "description": "The average funding rate weighted by open interest from all futures markets, regardless of the margin asset, converted to a 8 hour period.", "product": "Market Data", "category": "Funding Rates", "subcategory": "Futures", "unit": "Dimensionless", "data_type": "decimal", "type": "Percentage", "display_name": "Aggregated All-Margined Futures Funding Rate, 8 Hours", "docs_url": "https://docs.coinmetrics.io/market-data/market-data-overview/funding-rates/"}, {"metric": "futures_aggregate_funding_rate_coin_margin_1d_period", "full_name": "Funding rate, aggregated, futures, coin-margined, 1 day", "description": "The average funding rate weighted by open interest from futures markets where the margin asset is equivalent to the underlying base asset converted to a 1 day period.", "product": "Market Data", "category": "Funding Rates", "subcategory": "Futures", "unit": "Dimensionless", "data_type": "decimal", "type": "Percentage", "display_name": "Aggregated Coin-Margined Futures Funding Rate, 1 Day", "docs_url": "https://docs.coinmetrics.io/market-data/market-data-overview/funding-rates/"}, {"metric": "futures_aggregate_funding_rate_coin_margin_1y_period", "full_name": "Funding rate, aggregated, futures, coin-margined, 1 year", "description": "The average funding rate weighted by open interest from futures markets where the margin asset is equivalent to the underlying base asset converted to a 1 year period.", "product": "Market Data", "category": "Funding Rates", "subcategory": "Futures", "unit": "Dimensionless", "data_type": "decimal", "type": "Percentage", "display_name": "Aggregated Coin-Margined Futures Funding Rate, 1 Year", "docs_url": "https://docs.coinmetrics.io/market-data/market-data-overview/funding-rates/"}, {"metric": "futures_aggregate_funding_rate_coin_margin_30d_period", "full_name": "Funding rate, aggregated, futures, coin-margined, 30 days", "description": "The average funding rate weighted by open interest from futures markets where the margin asset is equivalent to the underlying base asset converted to a 30 day period.", "product": "Market Data", "category": "Funding Rates", "subcategory": "Futures", "unit": "Dimensionless", "data_type": "decimal", "type": "Percentage", "display_name": "Aggregated Coin-Margined Futures Funding Rate, 30 Day", "docs_url": "https://docs.coinmetrics.io/market-data/market-data-overview/funding-rates/"}, {"metric": "futures_aggregate_funding_rate_coin_margin_8h_period", "full_name": "Funding rate, aggregated, futures, coin-margined, 8 hours", "description": "The average funding rate weighted by open interest from futures markets where the margin asset is equivalent to the underlying base asset converted to a 8 hour period.", "product": "Market Data", "category": "Funding Rates", "subcategory": "Futures", "unit": "Dimensionless", "data_type": "decimal", "type": "Percentage", "display_name": "Aggregated Coin-Margined Futures Funding Rate, 8 Hours", "docs_url": "https://docs.coinmetrics.io/market-data/market-data-overview/funding-rates/"}, {"metric": "futures_aggregate_funding_rate_usd_margin_1d_period", "full_name": "Funding rate, aggregated, futures, USD-margined, 1 day", "description": "The average funding rate weighted by open interest from futures markets where the margin asset is U.S. dollars converted to a 1 day period.", "product": "Market Data", "category": "Funding Rates", "subcategory": "Futures", "unit": "Dimensionless", "data_type": "decimal", "type": "Percentage", "display_name": "Aggregated USD-Margined Futures Funding Rate, 1 Day", "docs_url": "https://docs.coinmetrics.io/market-data/market-data-overview/funding-rates/"}, {"metric": "futures_aggregate_funding_rate_usd_margin_1y_period", "full_name": "Funding rate, aggregated, futures, USD-margined, 1 year", "description": "The average funding rate weighted by open interest from futures markets where the margin asset is U.S. dollars converted to a 1 year period.", "product": "Market Data", "category": "Funding Rates", "subcategory": "Futures", "unit": "Dimensionless", "data_type": "decimal", "type": "Percentage", "display_name": "Aggregated USD-Margined Futures Funding Rate, 1 Year", "docs_url": "https://docs.coinmetrics.io/market-data/market-data-overview/funding-rates/"}, {"metric": "futures_aggregate_funding_rate_usd_margin_30d_period", "full_name": "Funding rate, aggregated, futures, USD-margined, 30 days", "description": "The average funding rate weighted by open interest from futures markets where the margin asset is U.S. dollars converted to a 30 day period.", "product": "Market Data", "category": "Funding Rates", "subcategory": "Futures", "unit": "Dimensionless", "data_type": "decimal", "type": "Percentage", "display_name": "Aggregated USD-Margined Futures Funding Rate, 30 Days", "docs_url": "https://docs.coinmetrics.io/market-data/market-data-overview/funding-rates/"}, {"metric": "futures_aggregate_funding_rate_usd_margin_8h_period", "full_name": "Funding rate, aggregated, futures, USD-margined, 8 hours", "description": "The average funding rate weighted by open interest from futures markets where the margin asset is U.S. dollars converted to a 8 hour period.", "product": "Market Data", "category": "Funding Rates", "subcategory": "Futures", "unit": "Dimensionless", "data_type": "decimal", "type": "Percentage", "display_name": "Aggregated USD-Margined Futures Funding Rate, 8 Hours", "docs_url": "https://docs.coinmetrics.io/market-data/market-data-overview//funding-rates"}, {"metric": "liquidations_reported_future_buy_units_1d", "full_name": "Liquidations, reported, future, buys, native units, one day", "description": "The sum of all buy liquidations from perpetual futures markets in native units of the underlying base asset.", "product": "Market Data", "category": "Liquidations", "subcategory": "Futures", "unit": "Native Units", "data_type": "decimal", "type": "Sum", "display_name": "Reported Futures Buy Liquidations, native units", "docs_url": "https://docs.coinmetrics.io/market-data/market-data-overview/liquidations/"}, {"metric": "liquidations_reported_future_buy_units_1h", "full_name": "Liquidations, reported, future, buys, native units, one hour", "description": "The sum of all buy liquidations from perpetual futures markets in native units of the underlying base asset.", "product": "Market Data", "category": "Liquidations", "subcategory": "Futures", "unit": "Native Units", "data_type": "decimal", "type": "Sum", "display_name": "Reported Futures Buy Liquidations, native units", "docs_url": "https://docs.coinmetrics.io/market-data/market-data-overview/liquidations/"}, {"metric": "liquidations_reported_future_buy_units_5m", "full_name": "Liquidations, reported, future, buys, native units, five minutes", "description": "The sum of all buy liquidations from perpetual futures markets in native units of the underlying base asset.", "product": "Market Data", "category": "Liquidations", "subcategory": "Futures", "unit": "Native Units", "data_type": "decimal", "type": "Sum", "display_name": "Reported Futures Buy Liquidations, native units", "docs_url": "https://docs.coinmetrics.io/market-data/market-data-overview/liquidations/"}, {"metric": "liquidations_reported_future_buy_usd_1d", "full_name": "Liquidations, reported, future, buys, USD, one day", "description": "The sum of all buy liquidations from perpetual futures markets in U.S. dollars.", "product": "Market Data", "category": "Liquidations", "subcategory": "Futures", "unit": "USD", "data_type": "decimal", "type": "Sum", "display_name": "Reported Futures Buy Liquidations, USD", "docs_url": "https://docs.coinmetrics.io/market-data/market-data-overview/liquidations/"}, {"metric": "liquidations_reported_future_buy_usd_1h", "full_name": "Liquidations, reported, future, buys, USD, one hour", "description": "The sum of all buy liquidations from perpetual futures markets in U.S. dollars.", "product": "Market Data", "category": "Liquidations", "subcategory": "Futures", "unit": "USD", "data_type": "decimal", "type": "Sum", "display_name": "Reported Futures Buy Liquidations, USD", "docs_url": "https://docs.coinmetrics.io/market-data/market-data-overview/liquidations/"}, {"metric": "liquidations_reported_future_buy_usd_5m", "full_name": "Liquidations, reported, future, buys, USD, five minutes", "description": "The sum of all buy liquidations from perpetual futures markets in U.S. dollars.", "product": "Market Data", "category": "Liquidations", "subcategory": "Futures", "unit": "USD", "data_type": "decimal", "type": "Sum", "display_name": "Reported Futures Buy Liquidations, USD", "docs_url": "https://docs.coinmetrics.io/market-data/market-data-overview/liquidations/"}, {"metric": "liquidations_reported_future_sell_units_1d", "full_name": "Liquidations, reported, future, sells, native units, one day", "description": "The sum of all sell liquidations from perpetual futures markets in native units of the underlying base asset.", "product": "Market Data", "category": "Liquidations", "subcategory": "Futures", "unit": "Native Units", "data_type": "decimal", "type": "Sum", "display_name": "Reported Futures Sell Liquidations, native units", "docs_url": "https://docs.coinmetrics.io/market-data/market-data-overview/liquidations/"}, {"metric": "liquidations_reported_future_sell_units_1h", "full_name": "Liquidations, reported, future, sells, native units, one hour", "description": "The sum of all sell liquidations from perpetual futures markets in native units of the underlying base asset.", "product": "Market Data", "category": "Liquidations", "subcategory": "Futures", "unit": "Native Units", "data_type": "decimal", "type": "Sum", "display_name": "Reported Futures Sell Liquidations, native units", "docs_url": "https://docs.coinmetrics.io/market-data/market-data-overview/liquidations/"}, {"metric": "liquidations_reported_future_sell_units_5m", "full_name": "Liquidations, reported, future, sells, native units, five minutes", "description": "The sum of all sell liquidations from perpetual futures markets in native units of the underlying base asset.", "product": "Market Data", "category": "Liquidations", "subcategory": "Futures", "unit": "Native Units", "data_type": "decimal", "type": "Sum", "display_name": "Reported Futures Sell Liquidations, native units", "docs_url": "https://docs.coinmetrics.io/market-data/market-data-overview/liquidations/"}, {"metric": "liquidations_reported_future_sell_usd_1d", "full_name": "Liquidations, reported, future, sells, USD, one day", "description": "The sum of all sell liquidations from perpetual futures markets in U.S. dollars.", "product": "Market Data", "category": "Liquidations", "subcategory": "Futures", "unit": "USD", "data_type": "decimal", "type": "Sum", "display_name": "Reported Futures Sell Liquidations, USD", "docs_url": "https://docs.coinmetrics.io/market-data/market-data-overview/liquidations/"}, {"metric": "liquidations_reported_future_sell_usd_1h", "full_name": "Liquidations, reported, future, sells, USD, one hour", "description": "The sum of all sell liquidations from perpetual futures markets in U.S. dollars.", "product": "Market Data", "category": "Liquidations", "subcategory": "Futures", "unit": "USD", "data_type": "decimal", "type": "Sum", "display_name": "Reported Futures Sell Liquidations, USD", "docs_url": "https://docs.coinmetrics.io/market-data/market-data-overview/liquidations/"}, {"metric": "liquidations_reported_future_sell_usd_5m", "full_name": "Liquidations, reported, future, sells, USD, five minutes", "description": "The sum of all sell liquidations from perpetual futures markets in U.S. dollars.", "product": "Market Data", "category": "Liquidations", "subcategory": "Futures", "unit": "USD", "data_type": "decimal", "type": "Sum", "display_name": "Reported Futures Sell Liquidations, USD", "docs_url": "https://docs.coinmetrics.io/market-data/market-data-overview/liquidations/"}, {"metric": "mempool_count", "full_name": "Mempool, count, one minute", "description": "The count of all mempool transactions at a point in time.", "product": "FARUM", "category": "KRI", "subcategory": "Transactions", "unit": "Transactions", "data_type": "bigint", "type": "Sum", "display_name": "1 Minute Count of Transactions in the Mempool", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/kri/transactions"}, {"metric": "mempool_fee", "full_name": "Mempool, fee, one minute", "description": "The sum value of all mempool transaction fees at a point in time in native units.", "product": "FARUM", "category": "KRI", "subcategory": "Fees", "unit": "Native units", "data_type": "decimal", "type": "Sum", "display_name": "1 Minute Fees of Mempool Transactions", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/kri/fees"}, {"metric": "mempool_next_block_inclusion_approx_feerate_min", "full_name": "Mempool, next block, inclusion, approximate feerate, min, one minute", "description": "The prediction of next block's minimum mineable feerate in native units per vByte.", "product": "FARUM", "category": "KRI", "subcategory": "Feerates", "unit": "Satoshi/vByte", "data_type": "decimal", "type": "Min", "display_name": "1 Minute Prediction of Min Transaction Feerate for Inclusion into the next Mempool Block", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/kri/feerates"}, {"metric": "open_interest_reported_future_usd", "full_name": "Open interest, reported, future, USD", "description": "The sum of all reported open interest from futures markets in units of U.S. dollars.", "product": "Market Data", "category": "Open Interest", "subcategory": "Futures", "unit": "USD", "data_type": "decimal", "type": "Sum", "display_name": "Reported Future Open Interest", "docs_url": "https://docs.coinmetrics.io/market-data/market-data-overview/open_interest/open_interest_reported"}, {"metric": "principal_market_price_usd", "full_name": "Principal Market Price, USD", "description": "The price of an asset quoted in U.S. dollars derived from the asset's principal market, the market with the most trading volume or activity.", "product": "CM Prices", "category": "Market", "subcategory": "Price", "unit": "USD", "data_type": "decimal", "type": "N/A", "display_name": "Principal Market Price", "docs_url": "https://docs.coinmetrics.io/market-data/reference-rates-overview/"}, {"metric": "principal_market_usd", "full_name": "Principal Market, USD", "description": "The asset's principal market, the market with the most trading volume or activity.", "product": "CM Prices", "category": "Market", "subcategory": "Price", "unit": "USD", "data_type": "text", "type": "N/A", "display_name": "Principal Market", "docs_url": "https://docs.coinmetrics.io/market-data/reference-rates-overview/"}, {"metric": "volatility_realized_usd_rolling_24h", "full_name": "Volatility, realized, USD, rolling, 24 hours", "description": "The 24 hour rolling realized volatility, measured as the standard deviation of the natural log of returns of price in U.S. dollars calculated every 10 minutes over the past 24 hours.", "product": "Market Data", "category": "Market", "subcategory": "Volatility", "unit": "Dimensionless", "data_type": "decimal", "type": "<PERSON><PERSON>", "display_name": "Realized Volatility, USD, 24 Hours", "docs_url": "https://docs.coinmetrics.io/market-data/market-data-overview/volatility"}, {"metric": "volatility_realized_usd_rolling_30d", "full_name": "Volatility, realized, USD, rolling, 30 days", "description": "The 30 day rolling realized volatility, measured as the standard deviation of the natural log of returns of price in U.S. dollars calculated every 10 minutes over the past 30 days.", "product": "Market Data", "category": "Market", "subcategory": "Volatility", "unit": "Dimensionless", "data_type": "decimal", "type": "<PERSON><PERSON>", "display_name": "Realized Volatility, USD, 30 Days", "docs_url": "https://docs.coinmetrics.io/market-data/market-data-overview/volatility"}, {"metric": "volatility_realized_usd_rolling_7d", "full_name": "Volatility, realized, USD, rolling, 7 days", "description": "The 7 day rolling realized volatility, measured as the standard deviation of the natural log of returns of price in U.S. dollars calculated every 10 minutes over the past 7 days.", "product": "Market Data", "category": "Market", "subcategory": "Volatility", "unit": "Dimensionless", "data_type": "decimal", "type": "<PERSON><PERSON>", "display_name": "Realized Volatility, USD, 7 Days", "docs_url": "https://docs.coinmetrics.io/market-data/market-data-overview/volatility"}, {"metric": "volume_trusted_spot_usd_1d", "full_name": "Volume, trusted, spot, USD, one day", "description": "The sum of all volume from the spot markets of a set of trusted exchanges in units of U.S. dollars.", "product": "Market Data", "category": "Volume", "subcategory": "Trusted", "unit": "USD", "data_type": "decimal", "type": "Sum", "display_name": "Trusted Spot Volume", "constituent_snapshots_url": "http://127.0.0.1:8080/v4/constituent-snapshots/asset-metrics?metric=volume_trusted_spot_usd_1d&api_key=x1", "constituent_timeframes_url": "http://127.0.0.1:8080/v4/constituent-timeframes/asset-metrics?metric=volume_trusted_spot_usd_1d&api_key=x1", "docs_url": "https://docs.coinmetrics.io/market-data/market-data-overview/volume/volume_trusted"}, {"metric": "volume_trusted_spot_usd_1h", "full_name": "Volume, trusted, spot, USD, one hour", "description": "The sum of all volume from the spot markets of a set of trusted exchanges in units of U.S. dollars.", "product": "Market Data", "category": "Volume", "subcategory": "Trusted", "unit": "USD", "data_type": "decimal", "type": "Sum", "display_name": "Trusted Spot Volume", "constituent_snapshots_url": "http://127.0.0.1:8080/v4/constituent-snapshots/asset-metrics?metric=volume_trusted_spot_usd_1h&api_key=x1", "constituent_timeframes_url": "http://127.0.0.1:8080/v4/constituent-timeframes/asset-metrics?metric=volume_trusted_spot_usd_1h&api_key=x1", "docs_url": "https://docs.coinmetrics.io/market-data/market-data-overview/volume/volume_trusted"}], "next_page_token": "Rmxvd0luQlNQTnR2", "next_page_url": "http://127.0.0.1:8080/v4/reference-data/asset-metrics?api_key=x1&paging_from=end&next_page_token=Rmxvd0luQlNQTnR2"}}