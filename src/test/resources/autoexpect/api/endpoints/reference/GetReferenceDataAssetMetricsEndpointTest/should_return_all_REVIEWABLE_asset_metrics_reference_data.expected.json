{"status": 200, "headers": {"content-type": "application/json"}, "content": {"data": [{"metric": "FlowInBFXNtv", "full_name": "Flow, in, to Bitfinex, native units", "description": "The sum in native units sent to Bitfinex that interval.", "product": "Network Data", "category": "Exchange", "subcategory": "Deposits", "unit": "Native units", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Bitfinex Deposits (native units)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/deposits"}, {"metric": "FlowInBFXUSD", "full_name": "Flow, in, to Bitfinex, USD", "description": "The sum USD value sent to Bitfinex that interval.", "product": "Network Data", "category": "Exchange", "subcategory": "Deposits", "unit": "USD", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Bitfinex Deposits (USD)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/deposits"}, {"metric": "FlowInBNBNtv", "full_name": "Flow, in, to Binance, native units", "description": "The sum in native units sent to Binance that interval.", "product": "Network Data", "category": "Exchange", "subcategory": "Deposits", "unit": "Native units", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Binance Deposits (native units)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/deposits"}, {"metric": "FlowInBNBUSD", "full_name": "Flow, in, to Binance, USD", "description": "The sum USD value sent to Binance that interval.", "product": "Network Data", "category": "Exchange", "subcategory": "Deposits", "unit": "USD", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Binance Deposits (USD)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/deposits"}, {"metric": "FlowInBSPNtv", "full_name": "Flow, in, to Bitstamp, native units", "description": "The sum in native units sent to Bitstamp that interval.", "product": "Network Data", "category": "Exchange", "subcategory": "Deposits", "unit": "Native units", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Bitstamp Deposits (native units)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/deposits"}, {"metric": "FlowInBSPUSD", "full_name": "Flow, in, to Bitstamp, USD", "description": "The sum USD value sent to Bitstamp that interval.", "product": "Network Data", "category": "Exchange", "subcategory": "Deposits", "unit": "USD", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Bitstamp Deposits (USD)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/deposits"}, {"metric": "FlowInBTXNtv", "full_name": "Flow, in, to Bittrex, native units", "description": "The sum in native units sent to Bittrex that interval.", "product": "Network Data", "category": "Exchange", "subcategory": "Deposits", "unit": "Native units", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Bittrex Deposits (native units)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/deposits"}, {"metric": "FlowInBTXUSD", "full_name": "Flow, in, to Bittrex, USD", "description": "The sum USD value sent to Bittrex that interval.", "product": "Network Data", "category": "Exchange", "subcategory": "Deposits", "unit": "USD", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Bittrex Deposits (USD)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/deposits"}, {"metric": "FlowInExInclNtv", "full_name": "Flow, in, to exchanges, inclusive, native units", "description": "The sum number of native units sent to exchanges that interval, including exchange to exchange activity.", "product": "Network Data", "category": "Exchange", "subcategory": "Deposits", "unit": "Native units", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Exchange Deposits, Incl EtoE (native units)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/deposits"}, {"metric": "FlowInExInclUSD", "full_name": "Flow, in, to exchanges, inclusive, USD", "description": "The sum USD value sent to exchanges that interval, including exchange to exchange activity.", "product": "Network Data", "category": "Exchange", "subcategory": "Deposits", "unit": "USD", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Exchange Deposits, Incl EtoE (USD)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/deposits"}, {"metric": "FlowInExNtv", "full_name": "Flow, in, to exchanges, native units", "description": "The sum number of native units sent to exchanges that interval, excluding exchange to exchange activity.", "product": "Network Data", "category": "Exchange", "subcategory": "Deposits", "unit": "Native units", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Exchange Deposits (native units)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/deposits"}, {"metric": "FlowInExUSD", "full_name": "Flow, in, to exchanges, USD", "description": "The sum USD value sent to exchanges that interval, excluding exchange to exchange activity.", "product": "Network Data", "category": "Exchange", "subcategory": "Deposits", "unit": "USD", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Exchange Deposits (USD)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/deposits"}, {"metric": "FlowInGEMNtv", "full_name": "Flow, in, to Gemini, native units", "description": "The sum in native units sent to Gemini that interval.", "product": "Network Data", "category": "Exchange", "subcategory": "Deposits", "unit": "Native units", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Gemini Deposits (native units)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/deposits"}, {"metric": "FlowInGEMUSD", "full_name": "Flow, in, to Gemini, USD", "description": "The sum USD value sent to Gemini that interval.", "product": "Network Data", "category": "Exchange", "subcategory": "Deposits", "unit": "USD", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Gemini Deposits (USD)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/deposits"}, {"metric": "FlowInKRKNtv", "full_name": "Flow, in, to Kraken, native units", "description": "The sum in native units sent to Kraken that interval.", "product": "Network Data", "category": "Exchange", "subcategory": "Deposits", "unit": "Native units", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Kraken Deposits (native units)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/deposits"}, {"metric": "FlowInKRKUSD", "full_name": "Flow, in, to Kraken, USD", "description": "The sum USD value sent to Kraken that interval.", "product": "Network Data", "category": "Exchange", "subcategory": "Deposits", "unit": "USD", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "<PERSON><PERSON><PERSON> (USD)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/deposits"}, {"metric": "FlowInPOLNtv", "full_name": "Flow, in, to Poloniex, native units", "description": "The sum in native units sent to Poloniex that interval.", "product": "Network Data", "category": "Exchange", "subcategory": "Deposits", "unit": "Native units", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Poloniex Deposits (native units)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/deposits"}, {"metric": "FlowInPOLUSD", "full_name": "Flow, in, to Poloniex, USD", "description": "The sum USD value sent to Poloniex that interval.", "product": "Network Data", "category": "Exchange", "subcategory": "Deposits", "unit": "USD", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "<PERSON><PERSON><PERSON> (USD)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/deposits"}, {"metric": "FlowOutBFXNtv", "full_name": "Flow, out, from Bitfinex, native units", "description": "The sum in native units withdrawn from Bitfinex that interval.", "product": "Network Data", "category": "Exchange", "subcategory": "<PERSON><PERSON><PERSON><PERSON>", "unit": "Native units", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Bitfinex Withdrawals (native units)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/withdrawals"}, {"metric": "FlowOutBFXUSD", "full_name": "Flow, out, from Bitfinex, USD", "description": "The sum USD value withdrawn from Bitfinex that interval.", "product": "Network Data", "category": "Exchange", "subcategory": "<PERSON><PERSON><PERSON><PERSON>", "unit": "USD", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Bitfinex Withdrawals (USD)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/withdrawals"}, {"metric": "FlowOutBNBNtv", "full_name": "Flow, out, from Binance, native units", "description": "The sum in native units withdrawn from Binance that interval.", "product": "Network Data", "category": "Exchange", "subcategory": "<PERSON><PERSON><PERSON><PERSON>", "unit": "Native units", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Binance Withdrawals (native units)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/withdrawals"}, {"metric": "FlowOutBNBUSD", "full_name": "Flow, out, from Binance, USD", "description": "The sum USD value withdrawn from Binance that interval.", "product": "Network Data", "category": "Exchange", "subcategory": "<PERSON><PERSON><PERSON><PERSON>", "unit": "USD", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Binance Withdrawals (USD)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/withdrawals"}, {"metric": "FlowOutBSPNtv", "full_name": "Flow, out, from Bitstamp, native units", "description": "The sum in native units withdrawn from Bitstamp that interval.", "product": "Network Data", "category": "Exchange", "subcategory": "<PERSON><PERSON><PERSON><PERSON>", "unit": "Native units", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Bitstamp Withdrawals (native units)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/withdrawals"}, {"metric": "FlowOutBSPUSD", "full_name": "Flow, out, from Bitstamp, USD", "description": "The sum USD value withdrawn from Bitstamp that interval.", "product": "Network Data", "category": "Exchange", "subcategory": "<PERSON><PERSON><PERSON><PERSON>", "unit": "USD", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Bitstamp Withdrawals (USD)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/withdrawals"}, {"metric": "FlowOutBTXNtv", "full_name": "Flow, out, from Bittrex, native units", "description": "The sum in native units withdrawn from Bittrex that interval.", "product": "Network Data", "category": "Exchange", "subcategory": "<PERSON><PERSON><PERSON><PERSON>", "unit": "Native units", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Bittrex Withdrawals (native units)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/withdrawals"}, {"metric": "FlowOutBTXUSD", "full_name": "Flow, out, from Bittrex, USD", "description": "The sum USD value withdrawn from Bittrex that interval.", "product": "Network Data", "category": "Exchange", "subcategory": "<PERSON><PERSON><PERSON><PERSON>", "unit": "USD", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Bittrex Withdrawals (USD)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/withdrawals"}, {"metric": "FlowOutExInclNtv", "full_name": "Flow, out, from exchanges, inclusive, native units", "description": "The sum in native units withdrawn from exchanges that interval, including exchange to exchange activity.", "product": "Network Data", "category": "Exchange", "subcategory": "<PERSON><PERSON><PERSON><PERSON>", "unit": "Native units", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Exchange Withdrawals, Incl EtoE (native units)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/withdrawals"}, {"metric": "FlowOutExInclUSD", "full_name": "Flow, out, from exchanges, inclusive, USD", "description": "The sum USD value withdrawn from exchanges that interval, including exchange to exchange activity.", "product": "Network Data", "category": "Exchange", "subcategory": "<PERSON><PERSON><PERSON><PERSON>", "unit": "USD", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Exchange Withdrawals, Incl EtoE (USD)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/withdrawals"}, {"metric": "FlowOutExNtv", "full_name": "Flow, out, from exchanges, native units", "description": "The sum in native units withdrawn from exchanges that interval, excluding exchange to exchange activity.", "product": "Network Data", "category": "Exchange", "subcategory": "<PERSON><PERSON><PERSON><PERSON>", "unit": "Native units", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Exchange Withdrawals (native units)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/withdrawals"}, {"metric": "FlowOutExUSD", "full_name": "Flow, out, from exchanges, USD", "description": "The sum USD value withdrawn from exchanges that interval, excluding exchange to exchange activity.", "product": "Network Data", "category": "Exchange", "subcategory": "<PERSON><PERSON><PERSON><PERSON>", "unit": "USD", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Exchange Withdrawals (USD)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/withdrawals"}, {"metric": "FlowOutGEMNtv", "full_name": "Flow, out, from Gemini, native units", "description": "The sum in native units withdrawn from Gemini that interval.", "product": "Network Data", "category": "Exchange", "subcategory": "<PERSON><PERSON><PERSON><PERSON>", "unit": "Native units", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Gemini Withdrawals (native units)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/withdrawals"}, {"metric": "FlowOutGEMUSD", "full_name": "Flow, out, from Gemini, USD", "description": "The sum USD value withdrawn from Gemini that interval.", "product": "Network Data", "category": "Exchange", "subcategory": "<PERSON><PERSON><PERSON><PERSON>", "unit": "USD", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Gemini Withdrawals (USD)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/withdrawals"}, {"metric": "FlowOutKRKNtv", "full_name": "Flow, out, from Kraken, native units", "description": "The sum in native units withdrawn from Kraken that interval.", "product": "Network Data", "category": "Exchange", "subcategory": "<PERSON><PERSON><PERSON><PERSON>", "unit": "Native units", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Kraken Withdrawals (native units)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/withdrawals"}, {"metric": "FlowOutKRKUSD", "full_name": "Flow, out, from Kraken, USD", "description": "The sum USD value withdrawn from Kraken that interval.", "product": "Network Data", "category": "Exchange", "subcategory": "<PERSON><PERSON><PERSON><PERSON>", "unit": "USD", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "<PERSON><PERSON><PERSON> (USD)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/withdrawals"}, {"metric": "FlowOutPOLNtv", "full_name": "Flow, out, from Poloniex, native units", "description": "The sum in native units withdrawn from Poloniex that interval.", "product": "Network Data", "category": "Exchange", "subcategory": "<PERSON><PERSON><PERSON><PERSON>", "unit": "Native units", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Poloniex Withdrawals (native units)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/withdrawals"}, {"metric": "FlowOutPOLUSD", "full_name": "Flow, out, from Poloniex, USD", "description": "The sum USD value withdrawn from Poloniex that interval.", "product": "Network Data", "category": "Exchange", "subcategory": "<PERSON><PERSON><PERSON><PERSON>", "unit": "USD", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "<PERSON><PERSON><PERSON> (USD)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/withdrawals"}, {"metric": "FlowTfrFromExCnt", "full_name": "Flow, transfers, from exchanges, count", "description": "The sum count of transfers from any address belonging to an exchange in that interval. Transfers between exchanges are not counted.", "product": "Network Data", "category": "Exchange", "subcategory": "<PERSON><PERSON><PERSON><PERSON>", "unit": "Transfers", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Exchange Withdrawal Cnt", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/withdrawals"}, {"metric": "FlowTfrFromExInclCnt", "full_name": "Flow, transfers, from exchanges, inclusive, count", "description": "The sum count of transfers from any address belonging to an exchange in that interval. Transfers between exchanges are counted.", "product": "Network Data", "category": "Exchange", "subcategory": "<PERSON><PERSON><PERSON><PERSON>", "unit": "Transfers", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Exchange Withdrawal Cnt, Incl EtoE", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/withdrawals"}, {"metric": "FlowTfrInBFXCnt", "full_name": "Flow, transfers, to Bitfinex, count", "description": "The sum count of transfers to any address belonging to Bitfinex in that interval. If the sender address also belongs to Bitfinex, the transfer is not counted.", "product": "Network Data", "category": "Exchange", "subcategory": "Deposits", "unit": "Transfers", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Bitfinex Deposit Cnt", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/deposits"}, {"metric": "FlowTfrInBNBCnt", "full_name": "Flow, transfers, to Binance, count", "description": "The sum count of transfers to any address belonging to Binance in that interval. If the sender address also belongs to Binance, the transfer is not counted.", "product": "Network Data", "category": "Exchange", "subcategory": "Deposits", "unit": "Transfers", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Binance Deposit Cnt", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/deposits"}, {"metric": "FlowTfrInBSPCnt", "full_name": "Flow, transfers, to Bitstamp, count", "description": "The sum count of transfers to any address belonging to Bitstamp in that interval. If the sender address also belongs to Bitstamp, the transfer is not counted.", "product": "Network Data", "category": "Exchange", "subcategory": "Deposits", "unit": "Transfers", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Bitstamp Deposit Cnt", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/deposits"}, {"metric": "FlowTfrInBTXCnt", "full_name": "Flow, transfers, to Bittrex, count", "description": "The sum count of transfers to any address belonging to Bittrex in that interval. If the sender address also belongs to Bittrex, the transfer is not counted.", "product": "Network Data", "category": "Exchange", "subcategory": "Deposits", "unit": "Transfers", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Bittrex Deposit Cnt", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/deposits"}, {"metric": "FlowTfrInGEMCnt", "full_name": "Flow, transfers, to Gemini, count", "description": "The sum count of transfers to any address belonging to <PERSON> in that interval. If the sender address also belongs to <PERSON>, the transfer is not counted.", "product": "Network Data", "category": "Exchange", "subcategory": "Deposits", "unit": "Transfers", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Gemini Deposit Cnt", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/deposits"}, {"metric": "FlowTfrInKRKCnt", "full_name": "Flow, transfers, to Kraken, count", "description": "The sum count of transfers to any address belonging to K<PERSON><PERSON> in that interval. If the sender address also belongs to Kraken, the transfer is not counted.", "product": "Network Data", "category": "Exchange", "subcategory": "Deposits", "unit": "Transfers", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "<PERSON>rak<PERSON> Deposit Cnt", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/deposits"}, {"metric": "FlowTfrInPOLCnt", "full_name": "Flow, transfers, to Poloniex, count", "description": "The sum count of transfers to any address belonging to Polonie<PERSON> in that interval. If the sender address also belongs to Poloniex, the transfer is not counted.", "product": "Network Data", "category": "Exchange", "subcategory": "Deposits", "unit": "Transfers", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Poloniex Deposit Cnt", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/deposits"}, {"metric": "FlowTfrOutBFXCnt", "full_name": "Flow, transfers, from Bitfinex, count", "description": "The sum count of transfers from any address belonging to Bitfinex in that interval. If the recipient address also belongs to Bitfinex, the transfer is not counted.", "product": "Network Data", "category": "Exchange", "subcategory": "<PERSON><PERSON><PERSON><PERSON>", "unit": "Transfers", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Bitfinex Withdrawal Cnt", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/withdrawals"}, {"metric": "FlowTfrOutBNBCnt", "full_name": "Flow, transfers, from Binance, count", "description": "The sum count of transfers from any address belonging to Binance in that interval. If the recipient address also belongs to Binance, the transfer is not counted.", "product": "Network Data", "category": "Exchange", "subcategory": "<PERSON><PERSON><PERSON><PERSON>", "unit": "Transfers", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Binance Withdrawal Cnt", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/withdrawals"}, {"metric": "FlowTfrOutBSPCnt", "full_name": "Flow, transfers, from Bitstamp, count", "description": "The sum count of transfers from any address belonging to Bitstamp in that interval. If the recipient address also belongs to Bitstamp, the transfer is not counted.", "product": "Network Data", "category": "Exchange", "subcategory": "<PERSON><PERSON><PERSON><PERSON>", "unit": "Transfers", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Bitstamp Withdrawal Cnt", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/withdrawals"}, {"metric": "FlowTfrOutBTXCnt", "full_name": "Flow, transfers, from Bittrex, count", "description": "The sum count of transfers from any address belonging to Bittrex in that interval. If the recipient address also belongs to Bittrex, the transfer is not counted.", "product": "Network Data", "category": "Exchange", "subcategory": "<PERSON><PERSON><PERSON><PERSON>", "unit": "Transfers", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Bittrex Withdrawal Cnt", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/withdrawals"}, {"metric": "FlowTfrOutGEMCnt", "full_name": "Flow, transfers, from Gemini, count", "description": "The sum count of transfers from any address belonging to <PERSON> in that interval. If the recipient address also belongs to <PERSON>, the transfer is not counted.", "product": "Network Data", "category": "Exchange", "subcategory": "<PERSON><PERSON><PERSON><PERSON>", "unit": "Transfers", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Gemini Withdrawal Cnt", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/withdrawals"}, {"metric": "FlowTfrOutKRKCnt", "full_name": "Flow, transfers, from Kraken, count", "description": "The sum count of transfers from any address belonging to K<PERSON><PERSON> in that interval. If the recipient address also belongs to Kraken, the transfer is not counted.", "product": "Network Data", "category": "Exchange", "subcategory": "<PERSON><PERSON><PERSON><PERSON>", "unit": "Transfers", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "<PERSON><PERSON><PERSON>", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/withdrawals"}, {"metric": "FlowTfrOutPOLCnt", "full_name": "Flow, transfers, from Poloniex, count", "description": "The sum count of transfers from any address belonging to <PERSON>nie<PERSON> in that interval. If the recipient address also belongs to Poloniex, the transfer is not counted.", "product": "Network Data", "category": "Exchange", "subcategory": "<PERSON><PERSON><PERSON><PERSON>", "unit": "Transfers", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "<PERSON><PERSON><PERSON>", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/withdrawals"}, {"metric": "FlowTfrToExCnt", "full_name": "Flow, transfers, to exchanges, count", "description": "The sum count of transfers to any address belonging to an exchange in that interval. Transfers between exchanges are not counted.", "product": "Network Data", "category": "Exchange", "subcategory": "Deposits", "unit": "Transfers", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Exchange Deposit Cnt", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/deposits"}, {"metric": "FlowTfrToExInclCnt", "full_name": "Flow, transfers, to exchanges, inclusive, count", "description": "The sum count of transfers to any address belonging to an exchange in that interval. Transfers between exchanges are counted.", "product": "Network Data", "category": "Exchange", "subcategory": "Deposits", "unit": "Transfers", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Exchange Deposit Cnt, Incl EtoE", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/deposits"}, {"metric": "SplyExUSD", "full_name": "Supply, held by exchanges, USD", "description": "The sum USD value of all native units held in hot or cold exchange wallets that interval.", "product": "Network Data", "category": "Exchange", "subcategory": "Exchange Supply", "unit": "USD", "data_type": "decimal", "type": "Sum", "reviewable": true, "display_name": "Exchange Supply (USD)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/exchange/exchange-supply"}]}}