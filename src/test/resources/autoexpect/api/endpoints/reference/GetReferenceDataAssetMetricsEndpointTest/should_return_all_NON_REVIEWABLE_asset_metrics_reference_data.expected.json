{"status": 200, "headers": {"content-type": "application/json"}, "content": {"data": [{"metric": "AdrActCnt", "full_name": "Addresses, active, count", "description": "The sum count of unique addresses that were active in the network (either as a recipient or originator of a ledger change) that interval. All parties in a ledger change action (recipients and originators) are counted. Individual addresses are not double-counted if previously active.", "product": "Network Data", "category": "Addresses", "subcategory": "Active", "unit": "Addresses", "data_type": "bigint", "type": "Sum", "display_name": "Active Addr Cnt", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/addresses/active-addresses"}, {"metric": "BlkHgt", "full_name": "Block, height", "description": "The count of blocks from the genesis (first) block to the last block of that interval on the main chain.", "product": "Network Data", "category": "Network Usage", "subcategory": "Blocks", "unit": "Blocks", "data_type": "bigint", "type": "NA", "display_name": "Block Height", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/network-usage/blocks"}, {"metric": "CapMrktEstUSD", "full_name": "Capitalization, market, estimated supply, USD", "description": "The sum USD value of the estimated supply in circulation. Also referred to as network value or market capitalization.", "product": "Network Data", "category": "Market", "subcategory": "Market Capitalization", "unit": "USD", "data_type": "decimal", "type": "Product", "display_name": "Market Cap Estimated (USD)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/market/market-capitalization"}, {"metric": "PriceUSD", "full_name": "Price, USD", "description": "The fixed closing price of the asset as of 00:00 UTC the following day (i.e., midnight UTC of the current day) denominated in USD. This price is generated by Coin Metrics' fixing/reference rate service. Real-time PriceUSD is the fixed closing price of the asset as of the timestamp set by the block's miner.", "product": "Network Data", "category": "Market", "subcategory": "Price", "unit": "USD", "data_type": "decimal", "type": "NA", "display_name": "USD Denominated Closing Price", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/market/price"}, {"metric": "ReferenceRate", "full_name": "Reference Rate, USD", "description": "The price of an asset quoted in U.S. dollars using a framework to select high quality constituent markets and a methodology that is resistant manipulation.", "product": "CM Prices", "category": "Market", "subcategory": "Price", "unit": "USD", "data_type": "decimal", "type": "NA", "docs_url": "https://docs.coinmetrics.io/market-data/reference-rates-overview/"}, {"metric": "ReferenceRateETH", "full_name": "Reference Rate, ETH", "description": "The price of an asset quoted in Etherium using a framework to select high quality constituent markets and a methodology that is resistant manipulation.", "product": "CM Prices", "category": "Market", "subcategory": "Price", "unit": "ETH", "data_type": "decimal", "type": "NA", "docs_url": "https://docs.coinmetrics.io/market-data/reference-rates-overview/"}, {"metric": "ReferenceRateEUR", "full_name": "Reference Rate, EUR", "description": "The price of an asset quoted in Euros using a framework to select high quality constituent markets and a methodology that is resistant manipulation.", "product": "CM Prices", "category": "Market", "subcategory": "Price", "unit": "EUR", "data_type": "decimal", "type": "NA", "docs_url": "https://docs.coinmetrics.io/market-data/reference-rates-overview/"}, {"metric": "ReferenceRateUSD", "full_name": "Reference Rate, USD", "description": "The price of an asset quoted in U.S. dollars using a framework to select high quality constituent markets and a methodology that is resistant manipulation.", "product": "CM Prices", "category": "Market", "subcategory": "Price", "unit": "USD", "data_type": "decimal", "type": "NA", "docs_url": "https://docs.coinmetrics.io/market-data/reference-rates-overview/"}, {"metric": "RevNtv", "full_name": "Miner revenue, native units", "description": "The sum native units of all miner revenue (fees plus newly issued native units) that interval.", "product": "Network Data", "category": "Fees and Revenue", "subcategory": "Revenue", "unit": "Native units", "data_type": "decimal", "type": "Sum", "display_name": "Miner Revenue (native units)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/fees-and-revenue/revenue"}, {"metric": "TxCnt", "full_name": "Transactions, count", "description": "The sum count of transactions that interval. Transactions represent a bundle of intended actions to alter the ledger initiated by a user (human or machine). Transactions are counted whether they execute or not and whether they result in the transfer of native units or not (a transaction can result in no, one, or many transfers). Changes to the ledger mandated by the protocol (and not by a user) or post-launch new issuance issued by a founder or controlling entity are not included here.", "product": "Network Data", "category": "Transactions", "subcategory": "Transactions", "unit": "Transactions", "data_type": "bigint", "type": "Sum", "display_name": "Tx Cnt", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/transactions/transactions"}, {"metric": "TxTfrValMedNtv", "full_name": "Transactions, transfers, value, median, native units", "description": "The median count of native units transferred per transfer (i.e., the median size of a transfer) between distinct addresses that interval.", "product": "Network Data", "category": "Transactions", "subcategory": "Transfer Value", "unit": "Native units", "data_type": "decimal", "type": "Median", "display_name": "Median Tx Size (native units)", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/transactions/transfer-value"}, {"metric": "block_count_at_tip", "full_name": "Block, count, at tip, one block", "description": "The number of blocks identified at the chain tip.", "product": "FARUM", "category": "KRI", "subcategory": "Blocks", "unit": "Blocks", "data_type": "bigint", "type": "N/A", "display_name": "1 Block Count of Blocks at the Chain Tip", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/kri/blocks"}, {"metric": "block_count_empty_6b", "full_name": "Block, count, empty, six blocks", "description": "The number of empty blocks in the past 6 blocks.", "product": "FARUM", "category": "KRI", "subcategory": "Blocks", "unit": "Blocks", "data_type": "bigint", "type": "Sum", "display_name": "1 Block Count of Empty Blocks from the Past 6 Blocks", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/kri/blocks"}, {"metric": "block_difficulty", "full_name": "Block, difficulty, one block", "description": "The difficulty of the block.", "product": "FARUM", "category": "KRI", "subcategory": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "data_type": "decimal", "type": "N/A", "display_name": "1 Block Mining Difficulty", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/mining/difficulty"}, {"metric": "block_feerate_min", "full_name": "Block, feerate, min, one block", "description": "Mined block's min mined feerate in native units per vByte.", "product": "FARUM", "category": "KRI", "subcategory": "Transaction feerates", "unit": "Satoshi/vByte", "data_type": "decimal", "type": "Min", "display_name": "1 Block Min Transaction Feerate", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/kri/transaction-feerates"}, {"metric": "block_fees", "full_name": "Block, fees, one block", "description": "The sum of fees in the mined block, in native units.", "product": "FARUM", "category": "KRI", "subcategory": "Transaction fees", "unit": "Native units", "data_type": "decimal", "type": "Sum", "display_name": "1 Block Sum of Transaction Fees", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/kri/transaction-fees"}, {"metric": "block_missed_slots", "full_name": "Missed Slots", "description": "The count of missed slots.", "product": "FARUM", "category": "KRI", "subcategory": "Empty Blocks", "unit": "Slots", "data_type": "bigint", "type": "Sum", "display_name": "Count of missed slots.", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/kri/empty-blocks"}, {"metric": "block_tx_count", "full_name": "Block, transaction count, one block", "description": "Mined block's transaction count.", "product": "FARUM", "category": "KRI", "subcategory": "Transactions", "unit": "Transactions", "data_type": "bigint", "type": "N/A", "display_name": "1 Block Transaction Count", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/kri/transactions"}, {"metric": "futures_aggregate_funding_rate_all_margin_1d_period", "full_name": "Funding rate, aggregated, futures, all-margined, 1 day", "description": "The average funding rate weighted by open interest from all futures markets, regardless of the margin asset, converted to a 1 day period.", "product": "Market Data", "category": "Funding Rates", "subcategory": "Futures", "unit": "Dimensionless", "data_type": "decimal", "type": "Percentage", "display_name": "Aggregated All-Margined Futures Funding Rate, 1 Day", "docs_url": "https://docs.coinmetrics.io/market-data/market-data-overview/funding-rates/"}, {"metric": "futures_aggregate_funding_rate_all_margin_1y_period", "full_name": "Funding rate, aggregated, futures, all-margined, 1 year", "description": "The average funding rate weighted by open interest from all futures markets, regardless of the margin asset, converted to a 1 year period.", "product": "Market Data", "category": "Funding Rates", "subcategory": "Futures", "unit": "Dimensionless", "data_type": "decimal", "type": "Percentage", "display_name": "Aggregated All-Margined Futures Funding Rate, 1 Year", "docs_url": "https://docs.coinmetrics.io/market-data/market-data-overview/funding-rates/"}, {"metric": "futures_aggregate_funding_rate_all_margin_30d_period", "full_name": "Funding rate, aggregated, futures, all-margined, 30 days", "description": "The average funding rate weighted by open interest from all futures markets, regardless of the margin asset, converted to a 30 day period.", "product": "Market Data", "category": "Funding Rates", "subcategory": "Futures", "unit": "Dimensionless", "data_type": "decimal", "type": "Percentage", "display_name": "Aggregated All-Margined Futures Funding Rate, 30 Days", "docs_url": "https://docs.coinmetrics.io/market-data/market-data-overview/funding-rates/"}, {"metric": "futures_aggregate_funding_rate_all_margin_8h_period", "full_name": "Funding rate, aggregated, futures, all-margined, 8 hours", "description": "The average funding rate weighted by open interest from all futures markets, regardless of the margin asset, converted to a 8 hour period.", "product": "Market Data", "category": "Funding Rates", "subcategory": "Futures", "unit": "Dimensionless", "data_type": "decimal", "type": "Percentage", "display_name": "Aggregated All-Margined Futures Funding Rate, 8 Hours", "docs_url": "https://docs.coinmetrics.io/market-data/market-data-overview/funding-rates/"}, {"metric": "futures_aggregate_funding_rate_coin_margin_1d_period", "full_name": "Funding rate, aggregated, futures, coin-margined, 1 day", "description": "The average funding rate weighted by open interest from futures markets where the margin asset is equivalent to the underlying base asset converted to a 1 day period.", "product": "Market Data", "category": "Funding Rates", "subcategory": "Futures", "unit": "Dimensionless", "data_type": "decimal", "type": "Percentage", "display_name": "Aggregated Coin-Margined Futures Funding Rate, 1 Day", "docs_url": "https://docs.coinmetrics.io/market-data/market-data-overview/funding-rates/"}, {"metric": "futures_aggregate_funding_rate_coin_margin_1y_period", "full_name": "Funding rate, aggregated, futures, coin-margined, 1 year", "description": "The average funding rate weighted by open interest from futures markets where the margin asset is equivalent to the underlying base asset converted to a 1 year period.", "product": "Market Data", "category": "Funding Rates", "subcategory": "Futures", "unit": "Dimensionless", "data_type": "decimal", "type": "Percentage", "display_name": "Aggregated Coin-Margined Futures Funding Rate, 1 Year", "docs_url": "https://docs.coinmetrics.io/market-data/market-data-overview/funding-rates/"}, {"metric": "futures_aggregate_funding_rate_coin_margin_30d_period", "full_name": "Funding rate, aggregated, futures, coin-margined, 30 days", "description": "The average funding rate weighted by open interest from futures markets where the margin asset is equivalent to the underlying base asset converted to a 30 day period.", "product": "Market Data", "category": "Funding Rates", "subcategory": "Futures", "unit": "Dimensionless", "data_type": "decimal", "type": "Percentage", "display_name": "Aggregated Coin-Margined Futures Funding Rate, 30 Day", "docs_url": "https://docs.coinmetrics.io/market-data/market-data-overview/funding-rates/"}, {"metric": "futures_aggregate_funding_rate_coin_margin_8h_period", "full_name": "Funding rate, aggregated, futures, coin-margined, 8 hours", "description": "The average funding rate weighted by open interest from futures markets where the margin asset is equivalent to the underlying base asset converted to a 8 hour period.", "product": "Market Data", "category": "Funding Rates", "subcategory": "Futures", "unit": "Dimensionless", "data_type": "decimal", "type": "Percentage", "display_name": "Aggregated Coin-Margined Futures Funding Rate, 8 Hours", "docs_url": "https://docs.coinmetrics.io/market-data/market-data-overview/funding-rates/"}, {"metric": "futures_aggregate_funding_rate_usd_margin_1d_period", "full_name": "Funding rate, aggregated, futures, USD-margined, 1 day", "description": "The average funding rate weighted by open interest from futures markets where the margin asset is U.S. dollars converted to a 1 day period.", "product": "Market Data", "category": "Funding Rates", "subcategory": "Futures", "unit": "Dimensionless", "data_type": "decimal", "type": "Percentage", "display_name": "Aggregated USD-Margined Futures Funding Rate, 1 Day", "docs_url": "https://docs.coinmetrics.io/market-data/market-data-overview/funding-rates/"}, {"metric": "futures_aggregate_funding_rate_usd_margin_1y_period", "full_name": "Funding rate, aggregated, futures, USD-margined, 1 year", "description": "The average funding rate weighted by open interest from futures markets where the margin asset is U.S. dollars converted to a 1 year period.", "product": "Market Data", "category": "Funding Rates", "subcategory": "Futures", "unit": "Dimensionless", "data_type": "decimal", "type": "Percentage", "display_name": "Aggregated USD-Margined Futures Funding Rate, 1 Year", "docs_url": "https://docs.coinmetrics.io/market-data/market-data-overview/funding-rates/"}, {"metric": "futures_aggregate_funding_rate_usd_margin_30d_period", "full_name": "Funding rate, aggregated, futures, USD-margined, 30 days", "description": "The average funding rate weighted by open interest from futures markets where the margin asset is U.S. dollars converted to a 30 day period.", "product": "Market Data", "category": "Funding Rates", "subcategory": "Futures", "unit": "Dimensionless", "data_type": "decimal", "type": "Percentage", "display_name": "Aggregated USD-Margined Futures Funding Rate, 30 Days", "docs_url": "https://docs.coinmetrics.io/market-data/market-data-overview/funding-rates/"}, {"metric": "futures_aggregate_funding_rate_usd_margin_8h_period", "full_name": "Funding rate, aggregated, futures, USD-margined, 8 hours", "description": "The average funding rate weighted by open interest from futures markets where the margin asset is U.S. dollars converted to a 8 hour period.", "product": "Market Data", "category": "Funding Rates", "subcategory": "Futures", "unit": "Dimensionless", "data_type": "decimal", "type": "Percentage", "display_name": "Aggregated USD-Margined Futures Funding Rate, 8 Hours", "docs_url": "https://docs.coinmetrics.io/market-data/market-data-overview//funding-rates"}, {"metric": "liquidations_reported_future_buy_units_1d", "full_name": "Liquidations, reported, future, buys, native units, one day", "description": "The sum of all buy liquidations from perpetual futures markets in native units of the underlying base asset.", "product": "Market Data", "category": "Liquidations", "subcategory": "Futures", "unit": "Native Units", "data_type": "decimal", "type": "Sum", "display_name": "Reported Futures Buy Liquidations, native units", "docs_url": "https://docs.coinmetrics.io/market-data/market-data-overview/liquidations/"}, {"metric": "liquidations_reported_future_buy_units_1h", "full_name": "Liquidations, reported, future, buys, native units, one hour", "description": "The sum of all buy liquidations from perpetual futures markets in native units of the underlying base asset.", "product": "Market Data", "category": "Liquidations", "subcategory": "Futures", "unit": "Native Units", "data_type": "decimal", "type": "Sum", "display_name": "Reported Futures Buy Liquidations, native units", "docs_url": "https://docs.coinmetrics.io/market-data/market-data-overview/liquidations/"}, {"metric": "liquidations_reported_future_buy_units_5m", "full_name": "Liquidations, reported, future, buys, native units, five minutes", "description": "The sum of all buy liquidations from perpetual futures markets in native units of the underlying base asset.", "product": "Market Data", "category": "Liquidations", "subcategory": "Futures", "unit": "Native Units", "data_type": "decimal", "type": "Sum", "display_name": "Reported Futures Buy Liquidations, native units", "docs_url": "https://docs.coinmetrics.io/market-data/market-data-overview/liquidations/"}, {"metric": "liquidations_reported_future_buy_usd_1d", "full_name": "Liquidations, reported, future, buys, USD, one day", "description": "The sum of all buy liquidations from perpetual futures markets in U.S. dollars.", "product": "Market Data", "category": "Liquidations", "subcategory": "Futures", "unit": "USD", "data_type": "decimal", "type": "Sum", "display_name": "Reported Futures Buy Liquidations, USD", "docs_url": "https://docs.coinmetrics.io/market-data/market-data-overview/liquidations/"}, {"metric": "liquidations_reported_future_buy_usd_1h", "full_name": "Liquidations, reported, future, buys, USD, one hour", "description": "The sum of all buy liquidations from perpetual futures markets in U.S. dollars.", "product": "Market Data", "category": "Liquidations", "subcategory": "Futures", "unit": "USD", "data_type": "decimal", "type": "Sum", "display_name": "Reported Futures Buy Liquidations, USD", "docs_url": "https://docs.coinmetrics.io/market-data/market-data-overview/liquidations/"}, {"metric": "liquidations_reported_future_buy_usd_5m", "full_name": "Liquidations, reported, future, buys, USD, five minutes", "description": "The sum of all buy liquidations from perpetual futures markets in U.S. dollars.", "product": "Market Data", "category": "Liquidations", "subcategory": "Futures", "unit": "USD", "data_type": "decimal", "type": "Sum", "display_name": "Reported Futures Buy Liquidations, USD", "docs_url": "https://docs.coinmetrics.io/market-data/market-data-overview/liquidations/"}, {"metric": "liquidations_reported_future_sell_units_1d", "full_name": "Liquidations, reported, future, sells, native units, one day", "description": "The sum of all sell liquidations from perpetual futures markets in native units of the underlying base asset.", "product": "Market Data", "category": "Liquidations", "subcategory": "Futures", "unit": "Native Units", "data_type": "decimal", "type": "Sum", "display_name": "Reported Futures Sell Liquidations, native units", "docs_url": "https://docs.coinmetrics.io/market-data/market-data-overview/liquidations/"}, {"metric": "liquidations_reported_future_sell_units_1h", "full_name": "Liquidations, reported, future, sells, native units, one hour", "description": "The sum of all sell liquidations from perpetual futures markets in native units of the underlying base asset.", "product": "Market Data", "category": "Liquidations", "subcategory": "Futures", "unit": "Native Units", "data_type": "decimal", "type": "Sum", "display_name": "Reported Futures Sell Liquidations, native units", "docs_url": "https://docs.coinmetrics.io/market-data/market-data-overview/liquidations/"}, {"metric": "liquidations_reported_future_sell_units_5m", "full_name": "Liquidations, reported, future, sells, native units, five minutes", "description": "The sum of all sell liquidations from perpetual futures markets in native units of the underlying base asset.", "product": "Market Data", "category": "Liquidations", "subcategory": "Futures", "unit": "Native Units", "data_type": "decimal", "type": "Sum", "display_name": "Reported Futures Sell Liquidations, native units", "docs_url": "https://docs.coinmetrics.io/market-data/market-data-overview/liquidations/"}, {"metric": "liquidations_reported_future_sell_usd_1d", "full_name": "Liquidations, reported, future, sells, USD, one day", "description": "The sum of all sell liquidations from perpetual futures markets in U.S. dollars.", "product": "Market Data", "category": "Liquidations", "subcategory": "Futures", "unit": "USD", "data_type": "decimal", "type": "Sum", "display_name": "Reported Futures Sell Liquidations, USD", "docs_url": "https://docs.coinmetrics.io/market-data/market-data-overview/liquidations/"}, {"metric": "liquidations_reported_future_sell_usd_1h", "full_name": "Liquidations, reported, future, sells, USD, one hour", "description": "The sum of all sell liquidations from perpetual futures markets in U.S. dollars.", "product": "Market Data", "category": "Liquidations", "subcategory": "Futures", "unit": "USD", "data_type": "decimal", "type": "Sum", "display_name": "Reported Futures Sell Liquidations, USD", "docs_url": "https://docs.coinmetrics.io/market-data/market-data-overview/liquidations/"}, {"metric": "liquidations_reported_future_sell_usd_5m", "full_name": "Liquidations, reported, future, sells, USD, five minutes", "description": "The sum of all sell liquidations from perpetual futures markets in U.S. dollars.", "product": "Market Data", "category": "Liquidations", "subcategory": "Futures", "unit": "USD", "data_type": "decimal", "type": "Sum", "display_name": "Reported Futures Sell Liquidations, USD", "docs_url": "https://docs.coinmetrics.io/market-data/market-data-overview/liquidations/"}, {"metric": "mempool_count", "full_name": "Mempool, count, one minute", "description": "The count of all mempool transactions at a point in time.", "product": "FARUM", "category": "KRI", "subcategory": "Transactions", "unit": "Transactions", "data_type": "bigint", "type": "Sum", "display_name": "1 Minute Count of Transactions in the Mempool", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/kri/transactions"}, {"metric": "mempool_fee", "full_name": "Mempool, fee, one minute", "description": "The sum value of all mempool transaction fees at a point in time in native units.", "product": "FARUM", "category": "KRI", "subcategory": "Fees", "unit": "Native units", "data_type": "decimal", "type": "Sum", "display_name": "1 Minute Fees of Mempool Transactions", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/kri/fees"}, {"metric": "mempool_next_block_inclusion_approx_feerate_min", "full_name": "Mempool, next block, inclusion, approximate feerate, min, one minute", "description": "The prediction of next block's minimum mineable feerate in native units per vByte.", "product": "FARUM", "category": "KRI", "subcategory": "Feerates", "unit": "Satoshi/vByte", "data_type": "decimal", "type": "Min", "display_name": "1 Minute Prediction of Min Transaction Feerate for Inclusion into the next Mempool Block", "docs_url": "https://docs.coinmetrics.io/network-data/network-data-overview/kri/feerates"}, {"metric": "open_interest_reported_future_usd", "full_name": "Open interest, reported, future, USD", "description": "The sum of all reported open interest from futures markets in units of U.S. dollars.", "product": "Market Data", "category": "Open Interest", "subcategory": "Futures", "unit": "USD", "data_type": "decimal", "type": "Sum", "display_name": "Reported Future Open Interest", "docs_url": "https://docs.coinmetrics.io/market-data/market-data-overview/open_interest/open_interest_reported"}, {"metric": "principal_market_price_usd", "full_name": "Principal Market Price, USD", "description": "The price of an asset quoted in U.S. dollars derived from the asset's principal market, the market with the most trading volume or activity.", "product": "CM Prices", "category": "Market", "subcategory": "Price", "unit": "USD", "data_type": "decimal", "type": "N/A", "display_name": "Principal Market Price", "docs_url": "https://docs.coinmetrics.io/market-data/reference-rates-overview/"}, {"metric": "principal_market_usd", "full_name": "Principal Market, USD", "description": "The asset's principal market, the market with the most trading volume or activity.", "product": "CM Prices", "category": "Market", "subcategory": "Price", "unit": "USD", "data_type": "text", "type": "N/A", "display_name": "Principal Market", "docs_url": "https://docs.coinmetrics.io/market-data/reference-rates-overview/"}, {"metric": "volatility_realized_usd_rolling_24h", "full_name": "Volatility, realized, USD, rolling, 24 hours", "description": "The 24 hour rolling realized volatility, measured as the standard deviation of the natural log of returns of price in U.S. dollars calculated every 10 minutes over the past 24 hours.", "product": "Market Data", "category": "Market", "subcategory": "Volatility", "unit": "Dimensionless", "data_type": "decimal", "type": "<PERSON><PERSON>", "display_name": "Realized Volatility, USD, 24 Hours", "docs_url": "https://docs.coinmetrics.io/market-data/market-data-overview/volatility"}, {"metric": "volatility_realized_usd_rolling_30d", "full_name": "Volatility, realized, USD, rolling, 30 days", "description": "The 30 day rolling realized volatility, measured as the standard deviation of the natural log of returns of price in U.S. dollars calculated every 10 minutes over the past 30 days.", "product": "Market Data", "category": "Market", "subcategory": "Volatility", "unit": "Dimensionless", "data_type": "decimal", "type": "<PERSON><PERSON>", "display_name": "Realized Volatility, USD, 30 Days", "docs_url": "https://docs.coinmetrics.io/market-data/market-data-overview/volatility"}, {"metric": "volatility_realized_usd_rolling_7d", "full_name": "Volatility, realized, USD, rolling, 7 days", "description": "The 7 day rolling realized volatility, measured as the standard deviation of the natural log of returns of price in U.S. dollars calculated every 10 minutes over the past 7 days.", "product": "Market Data", "category": "Market", "subcategory": "Volatility", "unit": "Dimensionless", "data_type": "decimal", "type": "<PERSON><PERSON>", "display_name": "Realized Volatility, USD, 7 Days", "docs_url": "https://docs.coinmetrics.io/market-data/market-data-overview/volatility"}, {"metric": "volume_trusted_spot_usd_1d", "full_name": "Volume, trusted, spot, USD, one day", "description": "The sum of all volume from the spot markets of a set of trusted exchanges in units of U.S. dollars.", "product": "Market Data", "category": "Volume", "subcategory": "Trusted", "unit": "USD", "data_type": "decimal", "type": "Sum", "display_name": "Trusted Spot Volume", "constituent_snapshots_url": "http://127.0.0.1:8080/v4/constituent-snapshots/asset-metrics?metric=volume_trusted_spot_usd_1d&api_key=x1", "constituent_timeframes_url": "http://127.0.0.1:8080/v4/constituent-timeframes/asset-metrics?metric=volume_trusted_spot_usd_1d&api_key=x1", "docs_url": "https://docs.coinmetrics.io/market-data/market-data-overview/volume/volume_trusted"}, {"metric": "volume_trusted_spot_usd_1h", "full_name": "Volume, trusted, spot, USD, one hour", "description": "The sum of all volume from the spot markets of a set of trusted exchanges in units of U.S. dollars.", "product": "Market Data", "category": "Volume", "subcategory": "Trusted", "unit": "USD", "data_type": "decimal", "type": "Sum", "display_name": "Trusted Spot Volume", "constituent_snapshots_url": "http://127.0.0.1:8080/v4/constituent-snapshots/asset-metrics?metric=volume_trusted_spot_usd_1h&api_key=x1", "constituent_timeframes_url": "http://127.0.0.1:8080/v4/constituent-timeframes/asset-metrics?metric=volume_trusted_spot_usd_1h&api_key=x1", "docs_url": "https://docs.coinmetrics.io/market-data/market-data-overview/volume/volume_trusted"}]}}