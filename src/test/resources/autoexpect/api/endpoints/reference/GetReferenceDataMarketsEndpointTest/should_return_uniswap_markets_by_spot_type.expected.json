{"status": 200, "headers": {"content-type": "application/json"}, "content": {"data": [{"market": "binance-btc-usdt-spot", "exchange": "binance", "type": "spot", "base": "btc", "quote": "usdt", "pair": "btc-usdt", "symbol": "BTCUSDT"}, {"market": "binance-eth-btc-spot", "exchange": "binance", "type": "spot", "base": "eth", "quote": "btc", "pair": "eth-btc"}, {"market": "binance-undef314159-usdt-spot", "exchange": "binance", "type": "spot", "base": "undef314159", "quote": "usdt", "pair": "undef314159-usdt", "symbol": "UNDEF314159USDT"}, {"market": "binance.us-btc-usd-spot", "exchange": "binance.us", "type": "spot", "base": "btc", "quote": "usd", "pair": "btc-usd", "symbol": "BTCUSD", "status": "offline", "order_amount_increment": "0.00000001", "order_amount_min": "0.0001", "order_amount_max": "100", "order_price_increment": "0.01", "order_price_min": "0.1", "order_price_max": "100000", "order_size_min": "1", "order_taker_fee": "0.002", "order_maker_fee": "0.002", "margin_trading_enabled": true}, {"market": "bittrex-btc-usd-spot", "exchange": "bittrex", "type": "spot", "base": "btc", "quote": "usd", "pair": "btc-usd", "symbol": "BTC-USD", "status": "online", "order_amount_increment": "0.00001", "order_amount_min": "0.00015382", "order_amount_max": "100", "order_price_increment": "0.001", "order_price_min": "0.0003", "order_price_max": "0.00000001", "order_size_min": "1000"}, {"market": "bittrex-eth-usd-spot", "exchange": "bittrex", "type": "spot", "base": "eth", "quote": "usd", "pair": "eth-usd", "symbol": "ETH-USD", "status": "online", "order_amount_increment": "0.0002", "order_amount_min": "0.00223593", "order_amount_max": "1.01", "order_price_increment": "0.001", "order_price_min": "0.0007", "order_price_max": "0.0000009", "order_size_min": "9000", "order_taker_fee": "0.015", "order_maker_fee": "0.0025", "margin_trading_enabled": false}, {"market": "bittrex-xrp-usd-spot", "exchange": "bittrex", "type": "spot", "base": "xrp", "quote": "usd", "pair": "xrp-usd", "symbol": "XRP-USD", "status": "online", "order_amount_min": "6.85996524", "order_price_increment": "0.00001"}, {"market": "bybit-btc-usd-spot", "exchange": "bybit", "type": "spot", "base": "btc", "quote": "usd", "pair": "btc-usd", "symbol": "BTCUSD", "contract_size": "0.3"}, {"market": "bybit-eth-usd-spot", "exchange": "bybit", "type": "spot", "base": "eth", "quote": "usd", "pair": "eth-usd", "symbol": "ETHUSD"}, {"market": "bybit-trx-usdt-spot", "exchange": "bybit", "type": "spot", "base": "trx", "quote": "usdt", "pair": "trx-usdt", "symbol": "TRXUSDT", "contract_size": "2", "status": "offline", "order_amount_increment": "0.01", "order_amount_min": "14.14", "order_amount_max": "30422878.0042592", "order_price_increment": "0.00001", "order_size_min": "1", "base_native": "TRX", "quote_native": "USDT"}, {"market": "coinbase-eth-usd-spot", "exchange": "coinbase", "type": "spot", "base": "eth", "quote": "usd", "pair": "eth-usd", "symbol": "ETH-USD"}, {"market": "curve_eth-1-cbeth-eth-spot", "exchange": "curve_eth", "type": "spot", "base": "cbeth", "quote": "eth", "pair": "cbeth-eth", "pool_config_id": "1", "contract_address": "6e8d83cce6b7ff23b2ab2d70e416cdb7d6055f51", "price_includes_fee": false, "variable_fee": false, "base_address": "be9895146f7af43049ca1c1ae358b0541ea49704", "experimental": true}, {"market": "curve_eth-1-tusd_2_eth-dai-spot", "exchange": "curve_eth", "type": "spot", "base": "tusd_2_eth", "quote": "dai", "pair": "tusd_2_eth-dai", "pool_config_id": "1", "contract_address": "45f783cce6b7ff23b2ab2d70e416cdb7d6055f51", "price_includes_fee": false, "variable_fee": false, "base_address": "0000000000085d4780b73119b644ae5ecd22b376", "quote_address": "6b175474e89094c44da98b954eedeac495271d0f", "experimental": true}, {"market": "curve_eth-2-ageur_eth-eurc_eth-spot", "exchange": "curve_eth", "type": "spot", "base": "ageur_eth", "quote": "eurc_eth", "pair": "ageur_eth-eurc_eth", "pool_config_id": "2", "contract_address": "f70c5c65cf6a28e7a4483f52511e5a29678e4ffd", "price_includes_fee": false, "variable_fee": false, "base_address": "1a7e4e63778b4f12a199c062f3efdd288afcbce8", "quote_address": "1abaea1f7c830bd89acc67ec4af516284b1bc33c", "experimental": true}, {"market": "deribit-btc-usdc-spot", "exchange": "deribit", "type": "spot", "base": "btc", "quote": "usdc", "pair": "btc-usdc", "symbol": "BTC_USDC", "contract_size": "1", "status": "online", "order_amount_min": "0.0001", "order_price_increment": "1.0", "order_taker_fee": "0.0", "order_maker_fee": "0.0", "base_native": "btc_native"}, {"market": "deribit-eth-usdc-spot", "exchange": "deribit", "type": "spot", "base": "eth", "quote": "usdc", "pair": "eth-usdc", "symbol": "ETH_USDC", "status": "online", "order_amount_min": "0.0001", "order_price_increment": "0.1", "order_taker_fee": "0.0", "order_maker_fee": "0.0", "base_native": "eth_native", "quote_native": "usdc_native"}, {"market": "erisx-btc-usd-spot", "exchange": "erisx", "type": "spot", "base": "btc", "quote": "usd", "pair": "btc-usd", "symbol": "BTCUSD", "status": "offline", "order_amount_increment": "0.00000001", "order_amount_min": "0.0001", "order_amount_max": "100", "order_price_increment": "0.01", "order_price_min": "0.1", "order_price_max": "100000", "order_size_min": "1", "order_taker_fee": "0.002", "order_maker_fee": "0.002", "margin_trading_enabled": true}, {"market": "hitbtc-vanry-btc-spot", "exchange": "hitbtc", "type": "spot", "base": "<PERSON><PERSON>", "quote": "btc", "pair": "vanry-btc", "symbol": "VANRYBTC", "contract_size": "0.1", "quote_native": "btc_native"}, {"market": "huobi-ada-btc-spot", "exchange": "huobi", "type": "spot", "base": "ada", "quote": "btc", "pair": "ada-btc", "symbol": "adabtc"}, {"market": "kraken-pol-usd-spot", "exchange": "kraken", "type": "spot", "base": "pol", "quote": "usd", "pair": "pol-usd", "symbol": "POLUSD"}, {"market": "sushiswap_v1_eth-sushi-usdt_eth-spot", "exchange": "sushiswap_v1_eth", "type": "spot", "base": "sushi", "quote": "usdt_eth", "pair": "sushi-usdt_eth", "contract_address": "680a025da7b1be2c204d7745e809919bce074026", "fee": "0.1", "price_includes_fee": false, "variable_fee": false, "base_address": "6b3595068778dd592e39a122f4f5a5cf09c90fe2", "quote_address": "dac17f958d2ee523a2206206994597c13d831ec7", "experimental": true}, {"market": "uniswap_v2_eth-aave-usdc-spot", "exchange": "uniswap_v2_eth", "type": "spot", "base": "aave", "quote": "usdc", "pair": "aave-usdc", "base_address": "7fc66500c84a76ad7e9c93437bfc5ac33e2ddae9", "quote_address": "a0b86991c6218b36c1d19d4a2e9eb0ce3606eb48", "experimental": true}, {"market": "uniswap_v2_eth-usdc-weth-spot", "exchange": "uniswap_v2_eth", "type": "spot", "base": "usdc", "quote": "weth", "pair": "usdc-weth", "contract_address": "b4e16d0168e52d35cacd2c6185b44281ec28c9dc", "fee": "0.01", "price_includes_fee": false, "variable_fee": false, "base_address": "a0b86991c6218b36c1d19d4a2e9eb0ce3606eb48", "quote_address": "c02aaa39b223fe8d0a0e5c4f27ead9083c756cc2", "experimental": true}, {"market": "uniswap_v3_base-1-usdc_base.eth-cbbtc_base.eth-spot", "exchange": "uniswap_v3_base", "type": "spot", "base": "usdc_base.eth", "quote": "cbbtc_base.eth", "pair": "usdc_base.eth-cbbtc_base.eth", "pool_config_id": "1", "contract_address": "ec558e484cc9f2210714e345298fdc53b253c27d", "fee": "0.03", "price_includes_fee": false, "variable_fee": false, "base_address": "833589fcd6edb6e08f4c7c32d4f71b54bda02913", "quote_address": "cbb7c0000ab88b473b1f5afd9ef808440eed33bf", "experimental": true}, {"market": "uniswap_v3_eth-2-link-weth-spot", "exchange": "uniswap_v3_eth", "type": "spot", "base": "link", "quote": "weth", "pair": "link-weth", "pool_config_id": "2", "contract_address": "5d4f3c6fa16908609bac31ff148bd002aa6b8c83", "fee": "0.05", "price_includes_fee": false, "variable_fee": false, "base_address": "514910771af9ca656af840dff83e8264ecf986ca", "quote_address": "c02aaa39b223fe8d0a0e5c4f27ead9083c756cc2", "experimental": true}, {"market": "uniswap_v3_eth-3-glm-weth-spot", "exchange": "uniswap_v3_eth", "type": "spot", "base": "glm", "quote": "weth", "pair": "glm-weth", "pool_config_id": "3", "contract_address": "fe4ec8f377be9e1e95a49d4e0d20f52d07b1ff0d", "fee": "0.3", "price_includes_fee": false, "variable_fee": false, "base_address": "7dd9c5cba05e151c895fde1cf355c9a1d5da6429", "quote_address": "c02aaa39b223fe8d0a0e5c4f27ead9083c756cc2", "experimental": true}, {"market": "uniswap_v3_eth-agg-usdc-weth-spot", "exchange": "uniswap_v3_eth", "type": "spot", "base": "usdc", "quote": "weth", "pair": "usdc-weth", "base_address": "a0b86991c6218b36c1d19d4a2e9eb0ce3606eb48", "quote_address": "c02aaa39b223fe8d0a0e5c4f27ead9083c756cc2", "experimental": true}]}}