{"status": 200, "headers": {"content-type": "application/json"}, "content": {"data": [{"market": "huobi-BCH201225_NQ-future", "exchange": "huobi", "type": "future", "code": "C1559D8EB9", "pair": "bch-usd", "openinterest_min_time": "2020-07-27T17:27:47.676464000Z", "openinterest_max_time": "2020-07-27T17:27:47.676464000Z", "base": "bch", "quote": "usd", "symbol": "BCH201225_NQ", "size_asset": "usd", "margin_asset": "bch", "contract_size": "10", "tick_size": "0.001", "listing": "2020-06-05T00:00:00.000000000Z", "expiration": "2020-12-25T00:00:00.000000000Z"}, {"market": "huobi-BSV201225_NQ-future", "exchange": "huobi", "type": "future", "code": "C30EA4B665", "pair": "bsv-usd", "openinterest_min_time": "2020-07-27T17:27:47.989984000Z", "openinterest_max_time": "2020-07-27T17:27:47.989984000Z", "base": "bsv", "quote": "usd", "symbol": "BSV201225_NQ", "size_asset": "usd", "margin_asset": "bsv", "contract_size": "10", "tick_size": "0.001", "listing": "2020-06-05T00:00:00.000000000Z", "expiration": "2020-12-25T00:00:00.000000000Z"}, {"market": "huobi-BTC201225_NQ-future", "exchange": "huobi", "type": "future", "code": "C758EA35B0", "pair": "btc-usd", "openinterest_min_time": "2020-07-27T17:27:48.200073000Z", "openinterest_max_time": "2020-07-27T17:27:48.200073000Z", "base": "btc", "quote": "usd", "symbol": "BTC201225_NQ", "size_asset": "usd", "margin_asset": "btc", "contract_size": "100", "tick_size": "0.01", "listing": "2020-06-05T00:00:00.000000000Z", "expiration": "2020-12-25T00:00:00.000000000Z"}, {"market": "huobi-ETC200925_CQ-future", "exchange": "huobi", "type": "future", "code": "CF5236E471", "pair": "etc-usd", "openinterest_min_time": "2020-07-27T17:27:48.011204000Z", "openinterest_max_time": "2020-07-27T17:27:48.011204000Z", "base": "etc", "quote": "usd", "symbol": "ETC200925_CQ", "size_asset": "usd", "margin_asset": "etc", "contract_size": "10", "tick_size": "0.001", "listing": "2020-06-12T00:00:00.000000000Z", "expiration": "2020-09-25T00:00:00.000000000Z"}, {"market": "huobi-ETH200807_NW-future", "exchange": "huobi", "type": "future", "code": "CBBC61A492", "pair": "eth-usd", "openinterest_min_time": "2020-07-27T17:27:48.233104000Z", "openinterest_max_time": "2020-07-27T17:27:48.233104000Z", "base": "eth", "quote": "usd", "symbol": "ETH200807_NW", "size_asset": "usd", "margin_asset": "eth", "contract_size": "10", "tick_size": "0.001", "listing": "2020-07-24T00:00:00.000000000Z", "expiration": "2020-08-07T00:00:00.000000000Z"}, {"market": "huobi-ETH200925_CQ-future", "exchange": "huobi", "type": "future", "code": "CBBC61A492", "pair": "eth-usd", "openinterest_min_time": "2020-07-27T17:27:47.749167000Z", "openinterest_max_time": "2020-07-27T17:27:47.749167000Z", "base": "eth", "quote": "usd", "symbol": "ETH200925_CQ", "size_asset": "usd", "margin_asset": "eth", "contract_size": "10", "tick_size": "0.001", "listing": "2020-06-12T00:00:00.000000000Z", "expiration": "2020-09-25T00:00:00.000000000Z"}], "next_page_token": "aHVvYmktTElOSzIwMDkyNV9DUS1mdXR1cmU", "next_page_url": "http://127.0.0.1:8080/v4/security-master/markets?type=future&api_key=x1&page_size=6&next_page_token=aHVvYmktTElOSzIwMDkyNV9DUS1mdXR1cmU"}}