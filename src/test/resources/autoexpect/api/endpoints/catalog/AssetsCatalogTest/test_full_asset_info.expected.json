{"status": 200, "headers": {"content-type": "application/json"}, "content": {"data": [{"asset": "100x", "full_name": "100xCoin"}, {"asset": "10set", "full_name": "Tenset"}, {"asset": "18c", "full_name": "Block 18"}, {"asset": "1art", "full_name": "ArtWallet"}, {"asset": "1box", "full_name": "1BOX"}, {"asset": "1earth", "full_name": "EarthFund"}, {"asset": "1inch", "full_name": "1inch"}, {"asset": "1inchdown", "full_name": "1INCHDOWN"}, {"asset": "1inchup", "full_name": "1INCHUP"}, {"asset": "1st", "full_name": "FirstBlood"}, {"asset": "1wo", "full_name": "1World"}, {"asset": "2crz", "full_name": "2crazyNFT"}, {"asset": "2give", "full_name": "2GIVE"}, {"asset": "4art", "full_name": "4ART Coin"}, {"asset": "a", "full_name": "Alpha Token"}, {"asset": "a5t", "full_name": "Alpha5"}, {"asset": "aaa", "full_name": "Abulaba"}, {"asset": "aac", "full_name": "Acute Angle Cloud"}, {"asset": "aapl", "full_name": "Apple Tokenized Stock"}, {"asset": "aave", "full_name": "Aave", "exchanges": ["uniswap_v2_eth"], "markets": ["uniswap_v2_eth-aave-usdc-spot"]}, {"asset": "aavedown", "full_name": "AAVEDOWN"}, {"asset": "aaveup", "full_name": "AAVEUP"}, {"asset": "aba", "full_name": "EcosBall"}, {"asset": "abbc", "full_name": "ABBC Coin"}, {"asset": "abl", "full_name": "Airbloc"}, {"asset": "abnb", "full_name": "Airbnb Tokenized Stock"}, {"asset": "abt", "full_name": "Arcblock"}, {"asset": "abtc", "full_name": "AML Bitcoin"}, {"asset": "aby", "full_name": "ArtByte"}, {"asset": "abyss", "full_name": "The Abyss"}, {"asset": "ac", "full_name": "ACoconut"}, {"asset": "ac3", "full_name": "AC3"}, {"asset": "aca", "full_name": "<PERSON><PERSON>a <PERSON>"}, {"asset": "acat", "full_name": "Alphacat"}, {"asset": "acc", "full_name": "Asian-African Capital Chain"}, {"asset": "acd", "full_name": "Alliance Cargo Direct"}, {"asset": "ace", "full_name": "ACENT"}, {"asset": "ach", "full_name": "Alchemy Pay"}, {"asset": "acm", "full_name": "AC Milan Fan Token"}, {"asset": "aco", "full_name": "A!Coin"}, {"asset": "acoin", "full_name": "Alchemy"}, {"asset": "acs", "full_name": "Access Protocol"}, {"asset": "act", "full_name": "Act I The AI Prophecy"}, {"asset": "acxt", "full_name": "AC eXchange Token"}, {"asset": "ad", "full_name": "Asian Dragon"}, {"asset": "ada", "full_name": "Cardano", "metrics": [{"metric": "ReferenceRate", "frequencies": [{"frequency": "1d-ny-close", "min_time": "2024-03-28T20:00:00.000000000Z", "max_time": "2024-03-28T20:00:00.000000000Z", "community": true}, {"frequency": "1h", "min_time": "2024-03-28T18:00:00.000000000Z", "max_time": "2024-03-28T21:00:00.000000000Z", "community": true}]}, {"metric": "ReferenceRateUSD", "frequencies": [{"frequency": "1d-ny-close", "min_time": "2024-03-28T20:00:00.000000000Z", "max_time": "2024-03-28T20:00:00.000000000Z", "community": true}, {"frequency": "1h", "min_time": "2024-03-28T18:00:00.000000000Z", "max_time": "2024-03-28T21:00:00.000000000Z", "community": true}]}], "exchanges": ["huobi"], "markets": ["huobi-ada-btc-spot"]}, {"asset": "<PERSON><PERSON><PERSON>", "full_name": "3X Short Cardano Token"}, {"asset": "<PERSON><PERSON><PERSON>", "full_name": "3X Long Cardano Token"}, {"asset": "adadown", "full_name": "ADADOWN"}, {"asset": "<PERSON><PERSON><PERSON><PERSON>", "full_name": "0.5X Long Cardano Token"}, {"asset": "adahedge", "full_name": "1X Short Cardano Token"}, {"asset": "adapad", "full_name": "ADAPad"}, {"asset": "<PERSON><PERSON><PERSON>", "full_name": "ADAUP"}, {"asset": "adb", "full_name": "adbank"}, {"asset": "adel", "full_name": "Akropolis Delphi"}, {"asset": "adh", "full_name": "AdHive"}, {"asset": "adk", "full_name": "<PERSON><PERSON>"}, {"asset": "ads", "full_name": "Alkimi"}, {"asset": "adt", "full_name": "adToken"}, {"asset": "adx", "full_name": "AdEx"}, {"asset": "ae", "full_name": "Aeternity"}, {"asset": "ae_eth", "full_name": "Aeternity on Ethereum"}, {"asset": "aed", "full_name": "United Arab Emirates Dirham"}, {"asset": "aeon", "full_name": "<PERSON><PERSON>"}, {"asset": "aergo", "full_name": "Aergo"}, {"asset": "afc", "full_name": "Arsenal Fan Token"}, {"asset": "afn", "full_name": "Afghan Afghani"}, {"asset": "age", "full_name": "<PERSON><PERSON>"}, {"asset": "ageur_eth", "full_name": "Angle Protocol on Ethereum", "exchanges": ["curve_eth"], "markets": ["curve_eth-2-ageur_eth-eurc_eth-spot"]}, {"asset": "agi", "full_name": "SingularityNET (Old)"}, {"asset": "agix", "full_name": "SingularityNET"}, {"asset": "agld", "full_name": "Adventure Gold"}, {"asset": "agrs", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "ags", "full_name": "Aegis"}, {"asset": "ai", "full_name": "Multiverse"}, {"asset": "aib", "full_name": "Advanced Internet Blocks"}, {"asset": "aid", "full_name": "AidCoin"}, {"asset": "aidoc", "full_name": "AI Doctor"}, {"asset": "aim", "full_name": "ModiHost"}, {"asset": "ain", "full_name": "AI Network"}, {"asset": "aion", "full_name": "Aion"}, {"asset": "aion_eth", "full_name": "Aion on Ethereum"}, {"asset": "aioz", "full_name": "AIOZ Network"}, {"asset": "air", "full_name": "AirToken"}, {"asset": "ait", "full_name": "AICHAIN"}, {"asset": "akc", "full_name": "Akikcoin"}, {"asset": "<PERSON><PERSON><PERSON>", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "akn", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "akong", "full_name": "AdaKong"}, {"asset": "akro", "full_name": "<PERSON><PERSON>"}, {"asset": "akt", "full_name": "Akash Network"}, {"asset": "alb", "full_name": "Albos"}, {"asset": "albt", "full_name": "AllianceBlock"}, {"asset": "alcx", "full_name": "Alchemix"}, {"asset": "ald", "full_name": "Aludra Network"}, {"asset": "aleph", "full_name": "Aleph.im"}, {"asset": "algo", "full_name": "Algorand", "experimental": true, "atlas": true}, {"asset": "algobear", "full_name": "3X Short Algorand Token"}, {"asset": "algobull", "full_name": "3X Long Algorand Token"}, {"asset": "algohalf", "full_name": "0.5X Long Algorand Token"}, {"asset": "algohedge", "full_name": "1X Short Algorand Token"}, {"asset": "ali", "full_name": "Artificial Liquid Intelligence"}, {"asset": "alice", "full_name": "My Neighbor Alice"}, {"asset": "alix", "full_name": "AlinX"}, {"asset": "all", "full_name": "Albanian Lek"}, {"asset": "allianceblock_nexera", "full_name": "AllianceBlock Nexera"}, {"asset": "alpa", "full_name": "Alpaca City"}, {"asset": "alpaca", "full_name": "Alpaca Finance"}, {"asset": "alpha", "full_name": "<PERSON>"}, {"asset": "alpine", "full_name": "Alpine F1 Team <PERSON>"}, {"asset": "altbear", "full_name": "3X Short Altcoin Index Token"}, {"asset": "altbull", "full_name": "3X Long Altcoin Index Token"}, {"asset": "al<PERSON>f", "full_name": "0.5X Long Altcoin Index Token"}, {"asset": "althedge", "full_name": "1X Short Altcoin Index Token"}, {"asset": "alu", "full_name": "Altura"}, {"asset": "alusd", "full_name": "Alchemix USD"}, {"asset": "alv", "full_name": "Allive"}, {"asset": "alx", "full_name": "ALAX"}, {"asset": "aly", "full_name": "Ally"}, {"asset": "amb", "full_name": "AirDAO"}, {"asset": "amc", "full_name": "AI Meta Club"}, {"asset": "amd", "full_name": "Armenian Dram"}, {"asset": "ame", "full_name": "AMEPAY"}, {"asset": "amio", "full_name": "Amino Network"}, {"asset": "amlt", "full_name": "AMLT"}, {"asset": "amm", "full_name": "MicroMoney"}, {"asset": "amo", "full_name": "AMO Coin"}, {"asset": "amp", "full_name": "Amp"}, {"asset": "ampl", "full_name": "Ampleforth"}, {"asset": "amzn", "full_name": "Amazon Tokenized Stock"}, {"asset": "anc", "full_name": "<PERSON><PERSON>"}, {"asset": "anct", "full_name": "<PERSON><PERSON>"}, {"asset": "ang", "full_name": "Netherlands Antillean Guilder"}, {"asset": "angle", "full_name": "<PERSON><PERSON>"}, {"asset": "anj", "full_name": "Aragon Court"}, {"asset": "ank", "full_name": "AlphaLink"}, {"asset": "ankr", "full_name": "Ankr"}, {"asset": "ant", "full_name": "Aragon", "metrics": [{"metric": "AdrActCnt", "frequencies": [{"frequency": "1d", "min_time": "2020-04-15T00:00:00.000000000Z", "max_time": "2020-04-15T00:00:00.000000000Z", "community": true}]}, {"metric": "TxTfrValMedNtv", "frequencies": [{"frequency": "1d", "min_time": "2020-04-15T00:00:00.000000000Z", "max_time": "2020-04-15T00:00:00.000000000Z", "community": true}]}]}, {"asset": "any", "full_name": "Anyswap"}, {"asset": "aoa", "full_name": "Aurora Chain"}, {"asset": "aos", "full_name": "AOS"}, {"asset": "ape", "full_name": "ApeCoin"}, {"asset": "api3", "full_name": "API3"}, {"asset": "apis", "full_name": "APIS"}, {"asset": "apix", "full_name": "APIX"}, {"asset": "apl", "full_name": "Apollo"}, {"asset": "apm", "full_name": "apM Coin"}, {"asset": "appc", "full_name": "AppCoins"}, {"asset": "apt", "full_name": "Aptos"}, {"asset": "apx", "full_name": "APX"}, {"asset": "apys", "full_name": "APYSwap"}, {"asset": "aqt", "full_name": "Alpha Quark Token"}, {"asset": "aquagoat", "full_name": "AquaGoat.Finance"}, {"asset": "ar", "full_name": "Arweave"}, {"asset": "arb", "full_name": "Arbitrum"}, {"asset": "arc_arcona", "full_name": "Arcona"}, {"asset": "arct", "full_name": "ArbitrageCT"}, {"asset": "arcx", "full_name": "ARC Governance"}, {"asset": "ardr", "full_name": "Ardor"}, {"asset": "ardx", "full_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"asset": "ares", "full_name": "Ares Protocol"}, {"asset": "arg", "full_name": "Argentine Football Association Fan Token"}, {"asset": "argon", "full_name": "Argon"}, {"asset": "aria20", "full_name": "<PERSON><PERSON>"}, {"asset": "ark", "full_name": "Ark"}, {"asset": "arkk", "full_name": "ARK Innovation ETF Tokenized Stock"}, {"asset": "armor", "full_name": "ARMOR"}, {"asset": "arn", "full_name": "Aeron"}, {"asset": "arpa", "full_name": "ARPA"}, {"asset": "arrr", "full_name": "Pirate Chain"}, {"asset": "ars", "full_name": "Argentine Peso"}, {"asset": "art", "full_name": "<PERSON><PERSON>nas"}, {"asset": "arte", "full_name": "Artemine"}, {"asset": "artii", "full_name": "ARTII Token"}, {"asset": "arv", "full_name": "<PERSON><PERSON>"}, {"asset": "arx", "full_name": "ARCS"}, {"asset": "asd", "full_name": "ASD"}, {"asset": "<PERSON><PERSON><PERSON>", "full_name": "0.5X Long AscendEx"}, {"asset": "ash", "full_name": "ASH"}, {"asset": "asi", "full_name": "ASI finance"}, {"asset": "asm", "full_name": "Assemble AI"}, {"asset": "asp", "full_name": "Aspire"}, {"asset": "asr", "full_name": "AS Roma Fan Token"}, {"asset": "ass", "full_name": "Australian Safe Shepherd"}, {"asset": "ast", "full_name": "AirSwap"}, {"asset": "asto", "full_name": "Altered State Token"}, {"asset": "astr", "full_name": "Astar"}, {"asset": "astro", "full_name": "AstroSwap"}, {"asset": "at", "full_name": "AWARE"}, {"asset": "ata", "full_name": "Automata Network"}, {"asset": "atb", "full_name": "ATBCoin"}, {"asset": "atc", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "atd", "full_name": "A2DAO"}, {"asset": "atl", "full_name": "ATLANT"}, {"asset": "atlas", "full_name": "Star Atlas"}, {"asset": "atm", "full_name": "Atletico De Madrid Fan Token"}, {"asset": "atmi", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "atn", "full_name": "ATN"}, {"asset": "atom", "full_name": "Cosmos", "metrics": [{"metric": "ReferenceRate", "frequencies": [{"frequency": "1h", "min_time": "2021-11-04T11:00:00.000000000Z", "max_time": "2021-11-04T12:00:00.000000000Z", "community": true}]}, {"metric": "ReferenceRateETH", "frequencies": [{"frequency": "1h", "min_time": "2021-11-04T11:00:00.000000000Z", "max_time": "2021-11-04T12:00:00.000000000Z", "community": true}]}, {"metric": "ReferenceRateUSD", "frequencies": [{"frequency": "1h", "min_time": "2021-11-04T11:00:00.000000000Z", "max_time": "2021-11-04T12:00:00.000000000Z", "community": true}]}]}, {"asset": "atom3l", "full_name": "Cosmos 3x Leveraged Long Token"}, {"asset": "atom3s", "full_name": "Cosmos 3x Leveraged Short Token"}, {"asset": "atombear", "full_name": "3X Short Cosmos Token"}, {"asset": "atombull", "full_name": "3X Long Cosmos Token"}, {"asset": "atomhalf", "full_name": "0.5X Long Cosmos Token"}, {"asset": "atomhedge", "full_name": "1X Short Cosmos Token"}, {"asset": "atp", "full_name": "Atlas Protocol"}, {"asset": "ats", "full_name": "Authorship"}, {"asset": "auc", "full_name": "Auctus"}, {"asset": "auction", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "aud", "full_name": "Australian Dollar"}, {"asset": "aude_eth", "full_name": "Ettle Australian Dollar on Ethereum"}, {"asset": "audio", "full_name": "<PERSON><PERSON>"}, {"asset": "aurora", "full_name": "Aurora"}, {"asset": "aury", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "auto", "full_name": "Auto"}, {"asset": "ava", "full_name": "AVA"}, {"asset": "avax", "full_name": "Avalanche", "experimental": true}, {"asset": "avaxc", "full_name": "Avalanche C-Chain", "experimental": true}, {"asset": "avaxp", "full_name": "Avalanche P-Chain", "experimental": true}, {"asset": "avaxx", "full_name": "Avalanche X-Chain", "experimental": true}, {"asset": "avg", "full_name": "Avocado DAO Token"}, {"asset": "avh", "full_name": "Animation Vision Cash"}, {"asset": "avt", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "awg", "full_name": "Aruban Florin"}, {"asset": "aws", "full_name": "AurusSILVER"}, {"asset": "awx", "full_name": "AurusDeFi"}, {"asset": "axc", "full_name": "AXIA Coin"}, {"asset": "axis", "full_name": "Axis DeFi"}, {"asset": "axl", "full_name": "<PERSON><PERSON>"}, {"asset": "axl_axlinu", "full_name": "Axl Inu"}, {"asset": "axpr", "full_name": "aXpire"}, {"asset": "axs", "full_name": "Axie Infinity"}, {"asset": "axs_eth", "full_name": "Axie Infinity on Ethereum"}, {"asset": "aya", "full_name": "Aryacoin"}, {"asset": "azero", "full_name": "Aleph Zero"}, {"asset": "azn", "full_name": "Azerbaijani Manat"}, {"asset": "azy", "full_name": "Amazy"}, {"asset": "b20", "full_name": "B20"}, {"asset": "b21", "full_name": "B21"}, {"asset": "b2g", "full_name": "Bitcoiin"}, {"asset": "b2m", "full_name": "Bit2Me"}, {"asset": "b2x", "full_name": "SegWit2x"}, {"asset": "b91", "full_name": "B91"}, {"asset": "baas", "full_name": "BaaSid"}, {"asset": "baba", "full_name": "Alibaba Tokenized Stock"}, {"asset": "baby", "full_name": "BabySwap"}, {"asset": "babydoge", "full_name": "Baby Doge Coin"}, {"asset": "bac", "full_name": "<PERSON><PERSON>"}, {"asset": "bacon", "full_name": "BaconDAO"}, {"asset": "badger", "full_name": "Badger DAO"}, {"asset": "bags", "full_name": "Basis Gold Share"}, {"asset": "bake", "full_name": "BakeryToken"}, {"asset": "bal", "full_name": "Balancer"}, {"asset": "balbear", "full_name": "3X Short Balancer Token"}, {"asset": "balbull", "full_name": "3X Long Balancer Token"}, {"asset": "balhalf", "full_name": "0.5X Long Balancer Token"}, {"asset": "balhedge", "full_name": "1X Short Balancer Token"}, {"asset": "bam", "full_name": "Bosnia-Herzegovina Convertible Mark"}, {"asset": "bamboo", "full_name": "BambooDeFi"}, {"asset": "banana", "full_name": "Banana Gun"}, {"asset": "banca", "full_name": "Banca"}, {"asset": "band", "full_name": "Band Protocol"}, {"asset": "band_eth", "full_name": "Band on Ethereum"}, {"asset": "bank", "full_name": "Bankless DAO"}, {"asset": "bao", "full_name": "Bao Finance"}, {"asset": "bar", "full_name": "FC Barcelona Fan Token"}, {"asset": "bas", "full_name": "BitAsean"}, {"asset": "base", "full_name": "Base Protocol"}, {"asset": "basic", "full_name": "BASIC"}, {"asset": "basid", "full_name": "Basid Coin"}, {"asset": "bat", "full_name": "Basic Attention Token"}, {"asset": "bath", "full_name": "Battle Hero"}, {"asset": "batman", "full_name": "<PERSON>"}, {"asset": "bax", "full_name": "BABB"}, {"asset": "bay", "full_name": "BitBay"}, {"asset": "bb", "full_name": "BounceBit"}, {"asset": "bbank", "full_name": "BlockBank"}, {"asset": "bbc", "full_name": "BULL BTC CLUB"}, {"asset": "bbd", "full_name": "Barbadian Dollar"}, {"asset": "bbeth", "full_name": "BabyEthereum"}, {"asset": "bbf", "full_name": "Bubblefong"}, {"asset": "bbn", "full_name": "Banyan Network"}, {"asset": "bcc", "full_name": "Basis Coin Cash"}, {"asset": "bcd", "full_name": "Bitcoin Diamond"}, {"asset": "bcdn", "full_name": "BlockCDN"}, {"asset": "bch", "full_name": "Bitcoin Cash", "exchanges": ["huobi"], "markets": ["huobi-BCH200731_CW-future", "huobi-BCH201225_NQ-future"]}, {"asset": "bcha", "full_name": "Bitcoin Cash ABC"}, {"asset": "bchbear", "full_name": "3X Short Bitcoin Cash Token"}, {"asset": "bchbull", "full_name": "3X Long Bitcoin Cash Token"}, {"asset": "bchc", "full_name": "BitCherry"}, {"asset": "bchdown", "full_name": "BCHDOWN"}, {"asset": "bchhalf", "full_name": "0.5X Long Bitcoin Cash Token"}, {"asset": "bchhedge", "full_name": "1X Short Bitcoin Cash Token"}, {"asset": "bchn", "full_name": "BCH Node"}, {"asset": "bchup", "full_name": "BCHUP"}, {"asset": "bci", "full_name": "Bitcoin Interest"}, {"asset": "bcmc", "full_name": "Blockchain Monster Hunt"}, {"asset": "bcn", "full_name": "Bytecoin"}, {"asset": "bcpt", "full_name": "Blockmason Credit Protocol"}, {"asset": "bcst", "full_name": "BlockChainStore"}, {"asset": "bcug", "full_name": "Blockchain Cuties Universe Governance"}, {"asset": "bcv", "full_name": "BitCapitalVendor"}, {"asset": "bcvt", "full_name": "BitcoinVend"}, {"asset": "bcw", "full_name": "Bitcoin Wonder"}, {"asset": "bcx", "full_name": "BitcoinX"}, {"asset": "bcy", "full_name": "Bitcrystals"}, {"asset": "bdg", "full_name": "BitDegree"}, {"asset": "bdot", "full_name": "Binance Wrapped DOT"}, {"asset": "bdp", "full_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"asset": "bds", "full_name": "Borderless"}, {"asset": "bdt", "full_name": "Bangladeshi Taka"}, {"asset": "beam", "full_name": "<PERSON><PERSON>"}, {"asset": "bear", "full_name": "3X Short Bitcoin Token (Deprecated)"}, {"asset": "bearshit", "full_name": "3X Short Shitcoin Index Token"}, {"asset": "bec", "full_name": "Beauty Chain"}, {"asset": "bed", "full_name": "Bit Ecological Digital"}, {"asset": "bel", "full_name": "Bella Protocol"}, {"asset": "bela", "full_name": "Bela"}, {"asset": "bepro", "full_name": "Bepro"}, {"asset": "berry", "full_name": "<PERSON>"}, {"asset": "berry_rentberry", "full_name": "Rentberry"}, {"asset": "best", "full_name": "Bitpanda Ecosystem Token"}, {"asset": "bet_da<PERSON>asino", "full_name": "DAO.Casino"}, {"asset": "beta", "full_name": "Beta Finance"}, {"asset": "beth", "full_name": "Binance Beacon ETH"}, {"asset": "betr", "full_name": "BetterBetting"}, {"asset": "betu", "full_name": "BetU"}, {"asset": "bezoge", "full_name": "Bezoge Earth"}, {"asset": "bfc", "full_name": "Bifrost"}, {"asset": "bfc_bitcoinfreecash", "full_name": "Bitcoin Free Cash"}, {"asset": "bfdt", "full_name": "BFD Token"}, {"asset": "bft", "full_name": "BnkToTheFuture"}, {"asset": "bgbp", "full_name": "Binance GBP Stable Coin"}, {"asset": "bgld", "full_name": "Based Gold"}, {"asset": "bgn", "full_name": "Bulgarian Lev"}, {"asset": "bhd", "full_name": "<PERSON><PERSON>"}, {"asset": "bhp", "full_name": "Blockchain of Hash Power"}, {"asset": "bhpc", "full_name": "BHPCash"}, {"asset": "bht", "full_name": "BHEX Token"}, {"asset": "bico", "full_name": "Biconomy"}, {"asset": "bid", "full_name": "CreatorBid"}, {"asset": "bidr", "full_name": "BIDR"}, {"asset": "bif", "full_name": "Burundian Franc"}, {"asset": "bifi", "full_name": "Bitcoin File"}, {"asset": "bifi_beef", "full_name": "Beefy"}, {"asset": "bifi_bifr", "full_name": "BiFi"}, {"asset": "bili", "full_name": "Billibilli Inc Tokenized Stock"}, {"asset": "bin", "full_name": "<PERSON><PERSON>"}, {"asset": "bird", "full_name": "Birdchain"}, {"asset": "bist", "full_name": "Bistroo"}, {"asset": "bit", "full_name": "BitDAO"}, {"asset": "bit_bitrewards", "full_name": "BitRewards"}, {"asset": "bitb", "full_name": "Bean Cash"}, {"asset": "bitci", "full_name": "Bitcicoin"}, {"asset": "bitcny", "full_name": "bitCNY"}, {"asset": "bite", "full_name": "BitEthereum"}, {"asset": "bito", "full_name": "ProShares Bitcoin Strategy ETF Tokenized Stock"}, {"asset": "bits_bitcoinus", "full_name": "Bitcoinus"}, {"asset": "bits_bitswift", "full_name": "Bitswift"}, {"asset": "bix", "full_name": "Bibox Token"}, {"asset": "bizz", "full_name": "BIZZCOIN"}, {"asset": "bkbt", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "bkc", "full_name": "FACTS"}, {"asset": "bkrw", "full_name": "Binance KRW"}, {"asset": "bks", "full_name": "Barkis Network"}, {"asset": "bkx", "full_name": "Bankex"}, {"asset": "black", "full_name": "eosBLACK"}, {"asset": "blade", "full_name": "BladeWarrior"}, {"asset": "bld", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "bles", "full_name": "Blind Boxes"}, {"asset": "blk", "full_name": "BlackCoin"}, {"asset": "bloc_blockcloud", "full_name": "Blockcloud"}, {"asset": "block", "full_name": "Blocknet"}, {"asset": "blok", "full_name": "Bloktopia"}, {"asset": "blt", "full_name": "Bloom"}, {"asset": "bltv", "full_name": "BLTV Token"}, {"asset": "bluesparrow", "full_name": "BlueS<PERSON>row <PERSON>"}, {"asset": "blur", "full_name": "Blur"}, {"asset": "blwa", "full_name": "BlockWarrior"}, {"asset": "bly", "full_name": "<PERSON><PERSON>"}, {"asset": "blz", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "bmars", "full_name": "Binamars"}, {"asset": "bmc", "full_name": "Blackmoon"}, {"asset": "bmd", "full_name": "Bermudan Dollar"}, {"asset": "bmh", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "bmi", "full_name": "Bridge Mutual"}, {"asset": "bmon", "full_name": "Binamon"}, {"asset": "bmp", "full_name": "Brother Music Platform"}, {"asset": "bna", "full_name": "Bananatok"}, {"asset": "bnb", "full_name": "BNB"}, {"asset": "bnb3l", "full_name": "BNB 3x Leveraged Long Token"}, {"asset": "bnb3s", "full_name": "BNB 3x Leveraged Short Token"}, {"asset": "bnb_bc", "full_name": "BNB on Binance Chain"}, {"asset": "bnb_bsc", "full_name": "BNB on Binance Smart Chain"}, {"asset": "bnb_eth", "full_name": "BNB on Ethereum"}, {"asset": "bnbbear", "full_name": "3X Short BNB Token"}, {"asset": "bnbbull", "full_name": "3X Long BNB Token"}, {"asset": "bnbdown", "full_name": "BNBDOWN"}, {"asset": "bnbeer", "full_name": "BNBeer"}, {"asset": "bn<PERSON><PERSON>", "full_name": "0.5X Long BNB Token"}, {"asset": "bnbhedge", "full_name": "1X Short BNB Token"}, {"asset": "bnbup", "full_name": "BNBUP"}, {"asset": "bnc", "full_name": "Bifrost Native Coin"}, {"asset": "bnd", "full_name": "Brunei Dollar"}, {"asset": "bnk", "full_name": "Bankera"}, {"asset": "bns", "full_name": "BNS Token"}, {"asset": "bnt", "full_name": "Bancor"}, {"asset": "bntx", "full_name": "BioNTech Tokenized Stock"}, {"asset": "bnty", "full_name": "Bounty0x"}, {"asset": "bnx", "full_name": "BinaryX"}, {"asset": "bnxold", "full_name": "BinaryX (old)"}, {"asset": "boa", "full_name": "BOSagora"}, {"asset": "bob", "full_name": "BOB"}, {"asset": "boba", "full_name": "Boba Network"}, {"asset": "boe", "full_name": "Bodhi [ETH]"}, {"asset": "bolt", "full_name": "BOLT"}, {"asset": "bond", "full_name": "BarnBridge"}, {"asset": "bondly", "full_name": "<PERSON><PERSON>"}, {"asset": "bone", "full_name": "<PERSON>"}, {"asset": "bonfire", "full_name": "Bonfire"}, {"asset": "bonk", "full_name": "Bonk"}, {"asset": "boo", "full_name": "SpookySwap"}, {"asset": "bor", "full_name": "BoringDAO (Old)"}, {"asset": "bora", "full_name": "BORA"}, {"asset": "boring", "full_name": "BoringDAO"}, {"asset": "bos", "full_name": "BOSCoin"}, {"asset": "boson", "full_name": "Boson Protocol"}, {"asset": "bot", "full_name": "<PERSON><PERSON>"}, {"asset": "botx", "full_name": "botXcoin"}, {"asset": "box", "full_name": "ContentBox"}, {"asset": "boxx", "full_name": "Blockparty (BOXX Token)"}, {"asset": "bp", "full_name": "BunnyPark"}, {"asset": "bpc", "full_name": "BeautyPayCoin"}, {"asset": "bpriva", "full_name": "Privapp Network"}, {"asset": "bps", "full_name": "BitcoinPoS"}, {"asset": "bpt", "full_name": "Blockport"}, {"asset": "bptn", "full_name": "Bit Public Talent Network"}, {"asset": "bqqq", "full_name": "Bitsdaq"}, {"asset": "brc", "full_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"asset": "brd", "full_name": "Bread"}, {"asset": "brdg", "full_name": "Bridge Protocol Utility Token"}, {"asset": "breed", "full_name": "BreederDAO"}, {"asset": "brg", "full_name": "Bridge Oracle"}, {"asset": "bright", "full_name": "Bright <PERSON>"}, {"asset": "brise", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "brk", "full_name": "Breakout"}, {"asset": "brl", "full_name": "Brazilian Real"}, {"asset": "brwl", "full_name": "Blockchain Brawlers"}, {"asset": "brx", "full_name": "Breakout Stake"}, {"asset": "bry", "full_name": "Berry Data"}, {"asset": "brz", "full_name": "Brazilian Digital Token"}, {"asset": "bsb", "full_name": "<PERSON><PERSON>"}, {"asset": "bscpad", "full_name": "BSCPAD"}, {"asset": "bscs", "full_name": "BSC Station"}, {"asset": "bsd_bahamiandollar", "full_name": "Bahamian Dollar"}, {"asset": "bsd_bitsend", "full_name": "BitSend"}, {"asset": "bst", "full_name": "BlockStamp"}, {"asset": "bstn", "full_name": "BitStation"}, {"asset": "bsv", "full_name": "Bitcoin SV", "exchanges": ["huobi"], "markets": ["huobi-BSV201225_NQ-future"]}, {"asset": "bsv3l", "full_name": "Bitcoin SV 3x Leveraged Long Token"}, {"asset": "bsv3s", "full_name": "Bitcoin SV 3x Leveraged Short Token"}, {"asset": "bsvbear", "full_name": "3X Short Bitcoin SV Token"}, {"asset": "bsvbull", "full_name": "3X Long Bitcoin SV Token"}, {"asset": "bsvhalf", "full_name": "0.5X Long Bitcoin SV Token"}, {"asset": "bsvhedge", "full_name": "1X Short Bitcoin SV Token"}, {"asset": "bsw", "full_name": "Biswap"}, {"asset": "bta", "full_name": "<PERSON><PERSON>"}, {"asset": "btb", "full_name": "BitBall"}, {"asset": "btbs", "full_name": "BitBase Token"}, {"asset": "btc", "full_name": "Bitcoin", "metrics": [{"metric": "FlowTfrOutBTXCnt", "frequencies": [{"frequency": "1d", "min_time": "2020-04-08T00:00:00.000000000Z", "max_time": "2020-04-11T00:00:00.000000000Z"}]}, {"metric": "PriceUSD", "frequencies": [{"frequency": "1b", "min_time": "2020-04-16T15:08:13.000000000Z", "max_time": "2020-04-16T15:08:13.000000000Z", "min_height": "1", "max_height": "3", "min_hash": "a1", "max_hash": "b4"}, {"frequency": "1d", "min_time": "2020-04-11T00:00:00.000000000Z", "max_time": "2020-04-15T00:00:00.000000000Z", "community": true}]}, {"metric": "ReferenceRate", "frequencies": [{"frequency": "1d", "min_time": "2019-06-24T00:00:00.000000000Z", "max_time": "2019-06-25T00:00:00.000000000Z", "community": true}, {"frequency": "1d-ny-close", "min_time": "2019-01-23T21:00:00.000000000Z", "max_time": "2019-05-31T20:00:00.000000000Z", "community": true}, {"frequency": "1h", "min_time": "2019-01-23T21:00:00.000000000Z", "max_time": "2019-07-14T01:00:00.000000000Z", "community": true}, {"frequency": "1m", "min_time": "2016-06-13T23:24:00.000000000Z", "max_time": "2020-01-07T20:15:00.000000000Z", "community": true}, {"frequency": "1s", "min_time": "2016-06-13T23:23:54.000000000Z", "max_time": "2020-01-07T20:15:00.000000000Z", "community": true}]}, {"metric": "ReferenceRateEUR", "frequencies": [{"frequency": "1d", "min_time": "2019-06-24T00:00:00.000000000Z", "max_time": "2019-06-25T00:00:00.000000000Z", "community": true}, {"frequency": "1d-ny-close", "min_time": "2019-01-23T21:00:00.000000000Z", "max_time": "2019-05-31T20:00:00.000000000Z", "community": true}, {"frequency": "1h", "min_time": "2019-01-23T21:00:00.000000000Z", "max_time": "2019-07-14T01:00:00.000000000Z", "community": true}, {"frequency": "1m", "min_time": "2016-06-13T23:24:00.000000000Z", "max_time": "2020-01-07T20:15:00.000000000Z", "community": true}, {"frequency": "1s", "min_time": "2016-06-13T23:23:54.000000000Z", "max_time": "2020-01-07T20:15:00.000000000Z", "community": true}]}, {"metric": "ReferenceRateUSD", "frequencies": [{"frequency": "1d", "min_time": "2019-06-24T00:00:00.000000000Z", "max_time": "2019-06-25T00:00:00.000000000Z", "community": true}, {"frequency": "1d-ny-close", "min_time": "2019-01-23T21:00:00.000000000Z", "max_time": "2019-05-31T20:00:00.000000000Z", "community": true}, {"frequency": "1h", "min_time": "2019-01-23T21:00:00.000000000Z", "max_time": "2019-07-14T01:00:00.000000000Z", "community": true}, {"frequency": "1m", "min_time": "2016-06-13T23:24:00.000000000Z", "max_time": "2020-01-07T20:15:00.000000000Z", "community": true}, {"frequency": "1s", "min_time": "2016-06-13T23:23:54.000000000Z", "max_time": "2020-01-07T20:15:00.000000000Z", "community": true}]}, {"metric": "SplyExUSD", "frequencies": [{"frequency": "1d", "min_time": "2020-04-08T00:00:00.000000000Z", "max_time": "2020-04-11T00:00:00.000000000Z"}]}, {"metric": "TxCnt", "frequencies": [{"frequency": "1b", "min_time": "2020-04-16T15:08:13.000000000Z", "max_time": "2020-04-16T15:08:13.000000000Z", "min_height": "1", "max_height": "3", "min_hash": "a1", "max_hash": "b4"}, {"frequency": "1d", "min_time": "2020-04-11T00:00:00.000000000Z", "max_time": "2020-04-15T00:00:00.000000000Z", "community": true}]}, {"metric": "block_count_at_tip", "frequencies": [{"frequency": "1b", "min_time": "2009-01-03T18:15:05.000000000Z", "max_time": "2022-12-28T08:16:11.618750000Z", "min_height": "1", "max_height": "769215", "min_hash": "000000000019d6689c085ae165831e934ff763ae46a2a6c172b3f1b60a8ce26f", "max_hash": "00000000000000000005f11253bdaab14b69109cf5aee0cb7986495e0edcd1ca"}]}, {"metric": "block_count_empty_6b", "frequencies": [{"frequency": "1b", "min_time": "2021-03-09T13:09:03.000000000Z", "max_time": "2021-03-09T13:19:45.000000000Z", "min_height": "673863", "max_height": "673864", "min_hash": "0000000000000000000985863cfae2ba05dee4756d75abc88413b922fce42feb", "max_hash": "000000000000000000018bcd97d245f054da64df5e3d204182d2c9d032032366"}]}, {"metric": "block_difficulty", "frequencies": [{"frequency": "1b", "min_time": "2022-12-28T07:29:48.000000000Z", "max_time": "2022-12-28T08:18:53.000000000Z", "min_height": "769214", "max_height": "769216", "min_hash": "0000000000000000000420500b6ca7b3ff888ef659b0c7311122b1b078136b27", "max_hash": "000000000000000000058c05e97952f12b0307fc4ec8ed2a584284270bb1fd58"}]}, {"metric": "block_feerate_min", "frequencies": [{"frequency": "1b", "min_time": "2009-01-03T18:15:07.000000000Z", "max_time": "2009-01-03T18:15:07.000000000Z", "min_height": "1", "max_height": "6", "min_hash": "00000000839a8e6886ab5951d76f411475428afc90947ee320161bbf18eb6048", "max_hash": "000000003031a0e73735690c5a1ff2a4be82553b2a12b776fbd3a215dc8f778d"}]}, {"metric": "block_fees", "frequencies": [{"frequency": "1b", "min_time": "2009-01-03T18:15:07.000000000Z", "max_time": "2022-12-28T08:18:53.000000000Z", "min_height": "6", "max_height": "769216", "min_hash": "000000003031a0e73735690c5a1ff2a4be82553b2a12b776fbd3a215dc8f778d", "max_hash": "000000000000000000058c05e97952f12b0307fc4ec8ed2a584284270bb1fd58"}]}, {"metric": "confirmation_suggestion_min", "frequencies": [{"frequency": "1m", "min_time": "2009-01-03T18:16:00.000000000Z", "max_time": "2009-01-03T18:17:00.000000000Z"}]}, {"metric": "futures_aggregate_funding_rate_all_margin_1d_period", "frequencies": [{"frequency": "1d", "min_time": "2023-09-14T00:00:00.000000000Z", "max_time": "2023-09-14T00:00:00.000000000Z"}, {"frequency": "1h", "min_time": "2023-09-14T00:00:00.000000000Z", "max_time": "2023-09-14T00:00:00.000000000Z"}]}, {"metric": "futures_aggregate_funding_rate_all_margin_1y_period", "frequencies": [{"frequency": "1d", "min_time": "2023-09-14T00:00:00.000000000Z", "max_time": "2023-09-14T00:00:00.000000000Z"}, {"frequency": "1h", "min_time": "2023-09-14T00:00:00.000000000Z", "max_time": "2023-09-14T00:00:00.000000000Z"}]}, {"metric": "futures_aggregate_funding_rate_all_margin_30d_period", "frequencies": [{"frequency": "1d", "min_time": "2023-09-14T00:00:00.000000000Z", "max_time": "2023-09-14T00:00:00.000000000Z"}, {"frequency": "1h", "min_time": "2023-09-14T00:00:00.000000000Z", "max_time": "2023-09-14T00:00:00.000000000Z"}]}, {"metric": "futures_aggregate_funding_rate_all_margin_8h_period", "frequencies": [{"frequency": "1d", "min_time": "2023-09-14T00:00:00.000000000Z", "max_time": "2023-09-14T00:00:00.000000000Z"}, {"frequency": "1h", "min_time": "2023-09-14T00:00:00.000000000Z", "max_time": "2023-09-14T00:00:00.000000000Z"}]}, {"metric": "futures_aggregate_funding_rate_coin_margin_1d_period", "frequencies": [{"frequency": "1d", "min_time": "2023-09-14T00:00:00.000000000Z", "max_time": "2023-09-14T00:00:00.000000000Z"}, {"frequency": "1h", "min_time": "2023-09-14T00:00:00.000000000Z", "max_time": "2023-09-14T00:00:00.000000000Z"}]}, {"metric": "futures_aggregate_funding_rate_coin_margin_1y_period", "frequencies": [{"frequency": "1d", "min_time": "2023-09-14T00:00:00.000000000Z", "max_time": "2023-09-14T00:00:00.000000000Z"}, {"frequency": "1h", "min_time": "2023-09-14T00:00:00.000000000Z", "max_time": "2023-09-14T00:00:00.000000000Z"}]}, {"metric": "futures_aggregate_funding_rate_coin_margin_30d_period", "frequencies": [{"frequency": "1d", "min_time": "2023-09-14T00:00:00.000000000Z", "max_time": "2023-09-14T00:00:00.000000000Z"}, {"frequency": "1h", "min_time": "2023-09-14T00:00:00.000000000Z", "max_time": "2023-09-14T00:00:00.000000000Z"}]}, {"metric": "futures_aggregate_funding_rate_coin_margin_8h_period", "frequencies": [{"frequency": "1d", "min_time": "2023-09-14T00:00:00.000000000Z", "max_time": "2023-09-14T00:00:00.000000000Z"}, {"frequency": "1h", "min_time": "2023-09-14T00:00:00.000000000Z", "max_time": "2023-09-14T00:00:00.000000000Z"}]}, {"metric": "futures_aggregate_funding_rate_usd_margin_1d_period", "frequencies": [{"frequency": "1d", "min_time": "2023-09-14T00:00:00.000000000Z", "max_time": "2023-09-14T00:00:00.000000000Z"}, {"frequency": "1h", "min_time": "2023-09-14T00:00:00.000000000Z", "max_time": "2023-09-14T00:00:00.000000000Z"}]}, {"metric": "futures_aggregate_funding_rate_usd_margin_1y_period", "frequencies": [{"frequency": "1d", "min_time": "2023-09-14T00:00:00.000000000Z", "max_time": "2023-09-14T00:00:00.000000000Z"}, {"frequency": "1h", "min_time": "2023-09-14T00:00:00.000000000Z", "max_time": "2023-09-14T00:00:00.000000000Z"}]}, {"metric": "futures_aggregate_funding_rate_usd_margin_30d_period", "frequencies": [{"frequency": "1d", "min_time": "2023-09-14T00:00:00.000000000Z", "max_time": "2023-09-14T00:00:00.000000000Z"}, {"frequency": "1h", "min_time": "2023-09-14T00:00:00.000000000Z", "max_time": "2023-09-14T00:00:00.000000000Z"}]}, {"metric": "futures_aggregate_funding_rate_usd_margin_8h_period", "frequencies": [{"frequency": "1d", "min_time": "2023-09-12T00:00:00.000000000Z", "max_time": "2023-09-14T07:00:00.000000000Z"}, {"frequency": "1h", "min_time": "2023-09-12T00:00:00.000000000Z", "max_time": "2023-09-14T07:00:00.000000000Z"}]}, {"metric": "futures_cumulative_funding_rate_all_margin_1d", "frequencies": [{"frequency": "1d", "min_time": "2023-09-14T00:00:00.000000000Z", "max_time": "2023-09-14T00:00:00.000000000Z"}, {"frequency": "1h", "min_time": "2023-09-14T00:00:00.000000000Z", "max_time": "2023-09-14T00:00:00.000000000Z"}]}, {"metric": "futures_cumulative_funding_rate_all_margin_30d", "frequencies": [{"frequency": "1d", "min_time": "2023-09-14T00:00:00.000000000Z", "max_time": "2023-09-14T00:00:00.000000000Z"}, {"frequency": "1h", "min_time": "2023-09-14T00:00:00.000000000Z", "max_time": "2023-09-14T00:00:00.000000000Z"}]}, {"metric": "futures_cumulative_funding_rate_all_margin_7d", "frequencies": [{"frequency": "1d", "min_time": "2023-09-14T00:00:00.000000000Z", "max_time": "2023-09-14T00:00:00.000000000Z"}, {"frequency": "1h", "min_time": "2023-09-14T00:00:00.000000000Z", "max_time": "2023-09-14T00:00:00.000000000Z"}]}, {"metric": "futures_cumulative_funding_rate_coin_margin_1d", "frequencies": [{"frequency": "1d", "min_time": "2023-09-14T00:00:00.000000000Z", "max_time": "2023-09-14T00:00:00.000000000Z"}, {"frequency": "1h", "min_time": "2023-09-14T00:00:00.000000000Z", "max_time": "2023-09-14T00:00:00.000000000Z"}]}, {"metric": "futures_cumulative_funding_rate_coin_margin_30d", "frequencies": [{"frequency": "1d", "min_time": "2023-09-14T00:00:00.000000000Z", "max_time": "2023-09-14T00:00:00.000000000Z"}, {"frequency": "1h", "min_time": "2023-09-14T00:00:00.000000000Z", "max_time": "2023-09-14T00:00:00.000000000Z"}]}, {"metric": "futures_cumulative_funding_rate_coin_margin_7d", "frequencies": [{"frequency": "1d", "min_time": "2023-09-14T00:00:00.000000000Z", "max_time": "2023-09-14T00:00:00.000000000Z"}, {"frequency": "1h", "min_time": "2023-09-14T00:00:00.000000000Z", "max_time": "2023-09-14T00:00:00.000000000Z"}]}, {"metric": "futures_cumulative_funding_rate_usd_margin_1d", "frequencies": [{"frequency": "1d", "min_time": "2023-09-12T00:00:00.000000000Z", "max_time": "2023-09-14T07:00:00.000000000Z"}, {"frequency": "1h", "min_time": "2023-09-12T00:00:00.000000000Z", "max_time": "2023-09-14T07:00:00.000000000Z"}]}, {"metric": "futures_cumulative_funding_rate_usd_margin_30d", "frequencies": [{"frequency": "1d", "min_time": "2023-09-14T00:00:00.000000000Z", "max_time": "2023-09-14T00:00:00.000000000Z"}, {"frequency": "1h", "min_time": "2023-09-14T00:00:00.000000000Z", "max_time": "2023-09-14T00:00:00.000000000Z"}]}, {"metric": "futures_cumulative_funding_rate_usd_margin_7d", "frequencies": [{"frequency": "1d", "min_time": "2023-09-14T00:00:00.000000000Z", "max_time": "2023-09-14T00:00:00.000000000Z"}, {"frequency": "1h", "min_time": "2023-09-14T00:00:00.000000000Z", "max_time": "2023-09-14T00:00:00.000000000Z"}]}, {"metric": "mempool_count", "frequencies": [{"frequency": "1m", "min_time": "2009-01-03T18:15:00.000000000Z", "max_time": "2009-01-03T18:16:00.000000000Z"}]}, {"metric": "mempool_fee", "frequencies": [{"frequency": "1m", "min_time": "2009-01-03T18:15:00.000000000Z", "max_time": "2009-01-03T18:16:00.000000000Z"}]}, {"metric": "mempool_next_block_inclusion_approx_feerate_min", "frequencies": [{"frequency": "1m", "min_time": "2009-01-03T18:16:00.000000000Z", "max_time": "2009-01-03T18:16:00.000000000Z"}]}, {"metric": "open_interest_reported_future_usd", "frequencies": [{"frequency": "1d", "min_time": "2020-12-25T05:00:00.000000000Z", "max_time": "2020-12-27T00:00:00.000000000Z"}, {"frequency": "1h", "min_time": "2020-12-25T05:00:00.000000000Z", "max_time": "2020-12-27T00:00:00.000000000Z"}]}, {"metric": "principal_market_price_usd", "frequencies": [{"frequency": "1d", "min_time": "2022-10-09T00:00:00.000000000Z", "max_time": "2023-09-03T00:00:00.000000000Z", "community": true}, {"frequency": "1d-ny-close", "min_time": "2022-10-09T12:00:00.000000000Z", "max_time": "2022-10-09T12:00:00.000000000Z", "community": true}, {"frequency": "1h", "min_time": "2022-10-08T23:00:00.000000000Z", "max_time": "2023-09-04T22:00:00.000000000Z", "community": true}, {"frequency": "1m", "min_time": "2022-10-09T17:58:00.000000000Z", "max_time": "2023-09-04T17:00:00.000000000Z", "community": true}, {"frequency": "1s", "min_time": "2022-10-09T17:58:00.000000000Z", "max_time": "2023-09-04T17:00:00.000000000Z", "community": true}]}, {"metric": "principal_market_usd", "frequencies": [{"frequency": "1d", "min_time": "2022-10-09T00:00:00.000000000Z", "max_time": "2023-09-03T00:00:00.000000000Z", "community": true}, {"frequency": "1d-ny-close", "min_time": "2022-10-09T12:00:00.000000000Z", "max_time": "2022-10-09T12:00:00.000000000Z", "community": true}, {"frequency": "1h", "min_time": "2022-10-08T23:00:00.000000000Z", "max_time": "2023-09-04T22:00:00.000000000Z", "community": true}, {"frequency": "1m", "min_time": "2022-10-09T17:58:00.000000000Z", "max_time": "2023-09-04T17:00:00.000000000Z", "community": true}, {"frequency": "1s", "min_time": "2022-10-09T17:58:00.000000000Z", "max_time": "2023-09-04T17:00:00.000000000Z", "community": true}]}, {"metric": "volatility_realized_usd_rolling_24h", "frequencies": [{"frequency": "10m", "min_time": "2022-11-28T22:20:00.000000000Z", "max_time": "2023-08-25T01:00:00.000000000Z"}, {"frequency": "1d", "min_time": "2022-11-28T22:20:00.000000000Z", "max_time": "2023-08-25T01:00:00.000000000Z"}, {"frequency": "1h", "min_time": "2022-11-28T22:20:00.000000000Z", "max_time": "2023-08-25T01:00:00.000000000Z"}]}, {"metric": "volatility_realized_usd_rolling_30d", "frequencies": [{"frequency": "10m", "min_time": "2022-11-28T22:20:00.000000000Z", "max_time": "2023-08-25T01:00:00.000000000Z"}, {"frequency": "1d", "min_time": "2022-11-28T22:20:00.000000000Z", "max_time": "2023-08-25T01:00:00.000000000Z"}, {"frequency": "1h", "min_time": "2022-11-28T22:20:00.000000000Z", "max_time": "2023-08-25T01:00:00.000000000Z"}]}, {"metric": "volatility_realized_usd_rolling_7d", "frequencies": [{"frequency": "10m", "min_time": "2022-11-28T22:20:00.000000000Z", "max_time": "2023-08-25T01:00:00.000000000Z"}, {"frequency": "1d", "min_time": "2022-11-28T22:20:00.000000000Z", "max_time": "2023-08-25T01:00:00.000000000Z"}, {"frequency": "1h", "min_time": "2022-11-28T22:20:00.000000000Z", "max_time": "2023-08-25T01:00:00.000000000Z"}]}, {"metric": "volume_trusted_spot_usd_1d", "frequencies": [{"frequency": "1d", "min_time": "2020-12-25T00:00:00.000000000Z", "max_time": "2020-12-29T00:00:00.000000000Z"}]}, {"metric": "volume_trusted_spot_usd_1h", "frequencies": [{"frequency": "1h", "min_time": "2020-12-25T01:00:00.000000000Z", "max_time": "2020-12-25T05:00:00.000000000Z"}]}], "exchanges": ["binance", "binance.us", "bitmex", "bittrex", "bybit", "cme", "coinbase", "deribit", "hitbtc", "huobi"], "markets": ["binance-BTCUSDT-future", "binance-XBTUSD-future", "binance-btc-usdt-spot", "binance-eth-btc-spot", "binance.us-btc-usd-spot", "bitmex-XBTUSD-future", "bittrex-btc-usd-spot", "bybit-btc-usd-spot", "cme-BTCQ1-future", "coinbase-btc-usd-spot", "deribit-BTC-10NOV23-future", "deribit-BTC-15OCT21-60000-C-option", "deribit-BTC-17NOV23-future", "deribit-BTC-1OCT21-75000-C-option", "deribit-BTC-24NOV23-future", "deribit-BTC-26NOV21-60000-C-option", "deribit-BTC-29MAR21-54000-C-option", "deribit-BTC-8OCT21-50000-C-option", "deribit-BTC-9APR21-50000-P-option", "deribit-BTC-9DEC24-102000-C-option", "deribit-btc-usdc-spot", "hitbtc-vanry-btc-spot", "huobi-BTC-9APR21-50000-P4-option", "huobi-BTC201225_NQ-future", "huobi-XBTUSD4-future", "huobi-ada-btc-spot"], "atlas": true}, {"asset": "btc1s", "full_name": "BTC1S"}, {"asset": "btc3l", "full_name": "Bitcoin 3x Leveraged Long Token"}, {"asset": "btc3s", "full_name": "Bitcoin 3x Leveraged Short Token"}, {"asset": "btcauction", "full_name": "Bitcoin Auction"}, {"asset": "btcb", "full_name": "Bitcoin BEP2"}, {"asset": "btcbear", "full_name": "3X Short Bitcoin Token"}, {"asset": "btcbull", "full_name": "3X Long Bitcoin Token"}, {"asset": "btcd", "full_name": "BitcoinDark"}, {"asset": "btcdown", "full_name": "BTCDOWN"}, {"asset": "btcp", "full_name": "Bitcoin Private"}, {"asset": "btcshort", "full_name": "BTCShort"}, {"asset": "btcst", "full_name": "Bitcoin Standard Hashrate Token"}, {"asset": "btcup", "full_name": "BTCUP"}, {"asset": "btcv", "full_name": "Bitcoin Vault"}, {"asset": "bte", "full_name": "BTEcoin"}, {"asset": "btf", "full_name": "Bitcoin Faith"}, {"asset": "btg", "full_name": "Bitcoin Gold"}, {"asset": "bth", "full_name": "Bitcoin Hot"}, {"asset": "btk", "full_name": "Bitcoin Token"}, {"asset": "btl", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "btm", "full_name": "BytomDAO"}, {"asset": "btm_eth", "full_name": "Bytom on Ethereum"}, {"asset": "btmx", "full_name": "Bitmax Token"}, {"asset": "btmxbear", "full_name": "3X Short BitMax Token Token"}, {"asset": "btmxbull", "full_name": "3X Long BitMax Token Token"}, {"asset": "btmxhalf", "full_name": "0.5X Long BitMax Token Token"}, {"asset": "btmxhedge", "full_name": "1X Short BitMax Token Token"}, {"asset": "btn", "full_name": "Bhutanese Ngultrum"}, {"asset": "btnyx", "full_name": "BitOnyx"}, {"asset": "bto", "full_name": "Bo<PERSON>s"}, {"asset": "btp", "full_name": "Bitcoin Pay"}, {"asset": "btr", "full_name": "<PERSON>rue Coin"}, {"asset": "btrn", "full_name": "Biotron"}, {"asset": "btrst", "full_name": "Braintrust"}, {"asset": "bts", "full_name": "BitShares"}, {"asset": "btsc", "full_name": "BTS Chain"}, {"asset": "btt", "full_name": "BitTorrent (old)"}, {"asset": "bttc", "full_name": "BitTorrent (new)"}, {"asset": "btu", "full_name": "BTU Protocol"}, {"asset": "btx", "full_name": "Bitcore"}, {"asset": "bty", "full_name": "Bityuan"}, {"asset": "bu", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "bubo", "full_name": "Budbo"}, {"asset": "buidl", "full_name": "BlackRock USD Institutional Digital Liquidity Fund"}, {"asset": "bull", "full_name": "3X Long Bitcoin Token (Deprecated)"}, {"asset": "bullshit", "full_name": "3X Long Shitcoin Index Token"}, {"asset": "bumn", "full_name": "BUMooN"}, {"asset": "burger", "full_name": "BurgerCities"}, {"asset": "burst", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "bus", "full_name": "BTU support"}, {"asset": "busd", "full_name": "Binance USD"}, {"asset": "busy", "full_name": "Busy DAO"}, {"asset": "but", "full_name": "BitUP Token"}, {"asset": "buu", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "bux", "full_name": "Buxcoin"}, {"asset": "buy", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "bvol", "full_name": "Bitcoin Implied Volatility Token"}, {"asset": "bwf", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "bwp", "full_name": "<PERSON><PERSON>"}, {"asset": "bwx", "full_name": "Blue Whale EXchange"}, {"asset": "bxa", "full_name": "Blockchain Exchange Alliance Token"}, {"asset": "bxc", "full_name": "BonusCloud"}, {"asset": "bxh", "full_name": "BXH Token"}, {"asset": "byn", "full_name": "Belarusian Ruble"}, {"asset": "bynd", "full_name": "Beyond Meat Tokenized Stock"}, {"asset": "byr", "full_name": "Belarusian <PERSON><PERSON><PERSON> (2010-2016)"}, {"asset": "bytz", "full_name": "BYTZ"}, {"asset": "bz", "full_name": "Bit-Z Token"}, {"asset": "bzd", "full_name": "Belize Dollar"}, {"asset": "bzky", "full_name": "Bizkey"}, {"asset": "bznt", "full_name": "Bezant"}, {"asset": "bzrx", "full_name": "bZx Protocol"}, {"asset": "bzz", "full_name": "Swarm"}, {"asset": "c20", "full_name": "CRYPTO20"}, {"asset": "c98", "full_name": "Coin98"}, {"asset": "cad", "full_name": "Canadian Dollar"}, {"asset": "cag", "full_name": "Change"}, {"asset": "cake", "full_name": "PancakeSwap"}, {"asset": "camp", "full_name": "Camp"}, {"asset": "can", "full_name": "Coinwaycoin"}, {"asset": "can_canyacoin", "full_name": "CanYaCoin"}, {"asset": "can_contentandadnetwork", "full_name": "Content and AD Network"}, {"asset": "cann", "full_name": "CannabisCoin"}, {"asset": "capp", "full_name": "Cappasity"}, {"asset": "caps", "full_name": "Ternoa"}, {"asset": "car", "full_name": "CarBlock"}, {"asset": "card", "full_name": "Cardstack"}, {"asset": "cards", "full_name": "CardStarter"}, {"asset": "carr", "full_name": "Carnomaly"}, {"asset": "cart", "full_name": "CryptoArt.Ai"}, {"asset": "cas", "full_name": "Cashaa"}, {"asset": "cat_bitclave", "full_name": "BitClave"}, {"asset": "cate", "full_name": "CateCoin"}, {"asset": "catgirl", "full_name": "Catgirl"}, {"asset": "cbat", "full_name": "Compound BAT"}, {"asset": "cbbtc_base.eth", "full_name": "Coinbase Wrapped Bitcoin on Base", "exchanges": ["uniswap_v3_base"], "markets": ["uniswap_v3_base-1-usdc_base.eth-cbbtc_base.eth-spot"]}, {"asset": "cbc", "full_name": "Casino Betting Coin"}, {"asset": "cbc_cashbetcoin", "full_name": "CashBet Coin"}, {"asset": "cbeth", "full_name": "Coinbase Wrapped Staked ETH", "exchanges": ["curve_eth"], "markets": ["curve_eth-1-cbeth-eth-spot"]}, {"asset": "cbk", "full_name": "Cobak Token"}, {"asset": "cbnt", "full_name": "Create Breaking News Together"}, {"asset": "cbse", "full_name": "Coinbase Pre-IPO Tokenized Stock"}, {"asset": "cbt", "full_name": "CommerceBlock"}, {"asset": "cbx", "full_name": "CropBytes"}, {"asset": "ccar", "full_name": "CryptoCars"}, {"asset": "ccash", "full_name": "CampusCash"}, {"asset": "cce", "full_name": "CloudCoin"}, {"asset": "ccl", "full_name": "CYCLEAN"}, {"asset": "ccomp", "full_name": "Compound Collateral"}, {"asset": "cct", "full_name": "Crystal Clear"}, {"asset": "cctc", "full_name": "CCTC"}, {"asset": "ccxx", "full_name": "Counos X"}, {"asset": "cdai", "full_name": "Compound DAI"}, {"asset": "cdb", "full_name": "Cloudbit Token"}, {"asset": "cdc", "full_name": "Commerce Data Connection"}, {"asset": "cdcc", "full_name": "CDCC"}, {"asset": "cdex", "full_name": "Codex"}, {"asset": "cdf", "full_name": "Congolese Franc"}, {"asset": "cdt", "full_name": "Blox"}, {"asset": "cdx", "full_name": "Commodity Ad Network"}, {"asset": "ceek", "full_name": "CEEK VR"}, {"asset": "cel", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "celo", "full_name": "<PERSON><PERSON>"}, {"asset": "celr", "full_name": "Celer Network"}, {"asset": "celt", "full_name": "Celestial"}, {"asset": "cennz", "full_name": "CENNZnet"}, {"asset": "cere", "full_name": "Cere Network"}, {"asset": "ceth", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "ceur", "full_name": "Celo Euro"}, {"asset": "cfg", "full_name": "Centrifuge"}, {"asset": "cfi", "full_name": "Cofound.it"}, {"asset": "cfx", "full_name": "Conflux"}, {"asset": "cgc", "full_name": "Canopy Growth Corp Tokenized Stock"}, {"asset": "cgg", "full_name": "Chain Guardians"}, {"asset": "cgs", "full_name": "Crypto Gladiator Shards"}, {"asset": "cgt", "full_name": "CACHE Gold"}, {"asset": "chain", "full_name": "Chain Games"}, {"asset": "champ", "full_name": "NFT Champions"}, {"asset": "chat", "full_name": "Chat<PERSON><PERSON>n"}, {"asset": "che", "full_name": "CherrySwap"}, {"asset": "check", "full_name": "Paycheck DeFi"}, {"asset": "chess", "full_name": "Tranchess"}, {"asset": "chex", "full_name": "CHEX Token"}, {"asset": "chf", "full_name": "Swiss Franc"}, {"asset": "chlt", "full_name": "Chellitcoin"}, {"asset": "chopper", "full_name": "Chopper <PERSON>"}, {"asset": "chp", "full_name": "CoinPoker"}, {"asset": "chr", "full_name": "Chromia"}, {"asset": "chs", "full_name": "CheeseSwap"}, {"asset": "chsb", "full_name": "SwissBorg (Old)"}, {"asset": "chx", "full_name": "Own"}, {"asset": "chz", "full_name": "Chiliz"}, {"asset": "chz_eth", "full_name": "Chiliz on Ethereum"}, {"asset": "cind", "full_name": "Cindrum"}, {"asset": "cipx", "full_name": "Colletrix"}, {"asset": "cir", "full_name": "CircleSwap"}, {"asset": "cirus", "full_name": "Cirus"}, {"asset": "cisla", "full_name": "Crypto Island"}, {"asset": "city", "full_name": "Manchester City Fan Token"}, {"asset": "civ", "full_name": "Civitas"}, {"asset": "cix100", "full_name": "Cryptoindex.com 100"}, {"asset": "ckb", "full_name": "Nervos Network"}, {"asset": "ckusd", "full_name": "CK USD"}, {"asset": "cl", "full_name": "CoinLancer"}, {"asset": "clam", "full_name": "Clams"}, {"asset": "clb", "full_name": "Cloudbric"}, {"asset": "cld", "full_name": "Cloud (Old)"}, {"asset": "clf", "full_name": "Chilean Unit of Account"}, {"asset": "cln", "full_name": "Colu Local Network"}, {"asset": "clo", "full_name": "Callisto Network"}, {"asset": "cloak", "full_name": "CloakCoin"}, {"asset": "clp", "full_name": "Chilean Peso"}, {"asset": "clt", "full_name": "CoinLoan"}, {"asset": "clv", "full_name": "CLV"}, {"asset": "cmct", "full_name": "Crowd Machine"}, {"asset": "cmcx", "full_name": "CORE MultiChain"}, {"asset": "cmd", "full_name": "Comodo Coin"}, {"asset": "cmp", "full_name": "<PERSON><PERSON><PERSON><PERSON>"}, {"asset": "cmt", "full_name": "CyberMiles"}, {"asset": "cnd", "full_name": "Cindicator"}, {"asset": "cnet", "full_name": "ContractNet"}, {"asset": "cnh", "full_name": "CNH"}, {"asset": "cnht", "full_name": "Tether CNH"}, {"asset": "cnn", "full_name": "Content Neutrality Network"}, {"asset": "cnns", "full_name": "CNNS"}, {"asset": "cns", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "cntm", "full_name": "Connectome"}, {"asset": "cnx", "full_name": "Cryptonex"}, {"asset": "cny", "full_name": "Chinese Yuan"}, {"asset": "cocos", "full_name": "Cocos-BCX"}, {"asset": "cofi", "full_name": "CoinFi"}, {"asset": "coge", "full_name": "Cogecoin"}, {"asset": "coin", "full_name": "Coinbase Tokenized Stock"}, {"asset": "comb", "full_name": "Combine.finance"}, {"asset": "combo", "full_name": "Furucombo"}, {"asset": "comp", "full_name": "Compound"}, {"asset": "compbear", "full_name": "3X Short Compound Token Token"}, {"asset": "compbull", "full_name": "3X Long Compound Token Token"}, {"asset": "comphedge", "full_name": "1X Short Compound Token Token"}, {"asset": "con", "full_name": "CONUN"}, {"asset": "conv", "full_name": "Convergence"}, {"asset": "cool", "full_name": "COOL Vault (NFTX)"}, {"asset": "cop", "full_name": "Colombian Peso"}, {"asset": "cope", "full_name": "<PERSON>"}, {"asset": "coral", "full_name": "Coral Swap"}, {"asset": "core", "full_name": "Core"}, {"asset": "corn", "full_name": "CORN"}, {"asset": "cos", "full_name": "Contentos"}, {"asset": "cosm", "full_name": "Cosmo Coin"}, {"asset": "coss", "full_name": "COSS"}, {"asset": "coti", "full_name": "COTI"}, {"asset": "cov", "full_name": "Covesting"}, {"asset": "cova", "full_name": "COVA"}, {"asset": "coval", "full_name": "Circuits of Value"}, {"asset": "cover", "full_name": "COVER Protocol"}, {"asset": "cpay", "full_name": "Cryptopay"}, {"asset": "cpc", "full_name": "CPCoin"}, {"asset": "cpc_cpchain", "full_name": "<PERSON><PERSON><PERSON><PERSON>"}, {"asset": "cphr", "full_name": "PolkaCipher"}, {"asset": "cpool", "full_name": "Clearpool"}, {"asset": "cprx", "full_name": "Crypto Perx"}, {"asset": "cpt", "full_name": "Cryptaur"}, {"asset": "cpx", "full_name": "Apex"}, {"asset": "cpy", "full_name": "COPYTRACK"}, {"asset": "cqt", "full_name": "Covalent"}, {"asset": "cra", "full_name": "Crabada"}, {"asset": "crb", "full_name": "CRB Coin"}, {"asset": "crbn", "full_name": "Carbon"}, {"asset": "crc", "full_name": "Costa Rican Colon"}, {"asset": "crdt", "full_name": "CRDT"}, {"asset": "cre", "full_name": "Carry"}, {"asset": "cre8", "full_name": "Creaticles"}, {"asset": "cream", "full_name": "Cream Finance"}, {"asset": "credit", "full_name": "TerraCredit"}, {"asset": "crep", "full_name": "Compound Augur"}, {"asset": "crfi", "full_name": "CrossFi (Old)"}, {"asset": "cro", "full_name": "Cronos"}, {"asset": "cron", "full_name": "Cryptocean"}, {"asset": "crp", "full_name": "CropperFinance"}, {"asset": "crpt", "full_name": "Crypterium"}, {"asset": "crt", "full_name": "Cryptonits"}, {"asset": "crts", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "cru", "full_name": "Crust Network"}, {"asset": "crv", "full_name": "Curve DAO Token"}, {"asset": "crw", "full_name": "Crown"}, {"asset": "cs", "full_name": "Credits"}, {"asset": "csm", "full_name": "Consentium"}, {"asset": "csno", "full_name": "BitDice"}, {"asset": "csp", "full_name": "Caspian"}, {"asset": "cspr", "full_name": "<PERSON>"}, {"asset": "cstr", "full_name": "CoreStarter"}, {"asset": "ctc", "full_name": "Creditcoin"}, {"asset": "cti", "full_name": "ClinTex CTi"}, {"asset": "ctk", "full_name": "<PERSON><PERSON>"}, {"asset": "ctpl", "full_name": "Cultiplan"}, {"asset": "ctsi", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "ctt", "full_name": "CryptoTycoon"}, {"asset": "ctx", "full_name": "Cryptex Finance"}, {"asset": "ctx_c2x", "full_name": "C2X"}, {"asset": "ctxc", "full_name": "<PERSON>rtex"}, {"asset": "cube", "full_name": "Somnium Space Cubes"}, {"asset": "cube_cubenetwork", "full_name": "Cube Network"}, {"asset": "cuc", "full_name": "Cuban Convertible Peso"}, {"asset": "cudos", "full_name": "CUDOS"}, {"asset": "cult", "full_name": "Cult DAO"}, {"asset": "cummies", "full_name": "CumRocket"}, {"asset": "cumstar", "full_name": "CumStar"}, {"asset": "cuni", "full_name": "Compound Uniswap"}, {"asset": "cup", "full_name": "Cuban Peso"}, {"asset": "cur", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "cure", "full_name": "Curecoin"}, {"asset": "cusd", "full_name": "Celo Dollar"}, {"asset": "cusdc", "full_name": "Compound USDC"}, {"asset": "cusdcv3", "full_name": "Compound USDC v3"}, {"asset": "cusdt", "full_name": "Compound USDT"}, {"asset": "cusdtbear", "full_name": "3X Short Compound USDT"}, {"asset": "cusdtbull", "full_name": "3X Long Compound USDT"}, {"asset": "cut", "full_name": "CUTcoin"}, {"asset": "cute", "full_name": "Blockchain Cuties Universe"}, {"asset": "cv", "full_name": "carVertical"}, {"asset": "cva", "full_name": "Crypto Village Accelerator"}, {"asset": "cvc", "full_name": "Civic"}, {"asset": "cve", "full_name": "Cape Verdean Escudo"}, {"asset": "cvh", "full_name": "Curriculum Vitae"}, {"asset": "cvn", "full_name": "CVCoin"}, {"asset": "cvnt", "full_name": "Content Value Network"}, {"asset": "cvp", "full_name": "PowerPool"}, {"asset": "cvt", "full_name": "Cyber<PERSON>ein"}, {"asset": "cvx", "full_name": "Convex Finance"}, {"asset": "cw", "full_name": "CardWallet"}, {"asset": "cwar", "full_name": "Cryowar"}, {"asset": "cwbtc", "full_name": "Compound WBTC"}, {"asset": "cweb", "full_name": "Coinweb"}, {"asset": "cws", "full_name": "Crowns"}, {"asset": "cwv", "full_name": "CWV Chain"}, {"asset": "cxo", "full_name": "CargoX"}, {"asset": "cyclub", "full_name": "CYCLUB"}, {"asset": "cys", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "czk", "full_name": "Czech Republic Koruna"}, {"asset": "czr", "full_name": "<PERSON><PERSON><PERSON><PERSON>"}, {"asset": "czrx", "full_name": "Compound ZRX"}, {"asset": "czz", "full_name": "ClassZZ"}, {"asset": "dac", "full_name": "<PERSON><PERSON><PERSON> Coin"}, {"asset": "dacc", "full_name": "DACC"}, {"asset": "dacs", "full_name": "DACSEE"}, {"asset": "dacxi", "full_name": "Dacxi"}, {"asset": "dad", "full_name": "DAD"}, {"asset": "<PERSON><PERSON><PERSON>", "full_name": "<PERSON>"}, {"asset": "dadi", "full_name": "DADI"}, {"asset": "daf", "full_name": "DaFIN"}, {"asset": "dafi", "full_name": "DAFI Protocol"}, {"asset": "dag", "full_name": "Constellation"}, {"asset": "dai", "full_name": "Dai", "exchanges": ["curve_eth"], "markets": ["curve_eth-1-tusd_2_eth-dai-spot"]}, {"asset": "dal", "full_name": "DAOLaunch"}, {"asset": "dan", "full_name": "<PERSON><PERSON>"}, {"asset": "dana", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "dao", "full_name": "DAO Maker"}, {"asset": "dapp", "full_name": "Pencils Protocol"}, {"asset": "dappt", "full_name": "<PERSON><PERSON>"}, {"asset": "dappx", "full_name": "dAppstore"}, {"asset": "daps", "full_name": "DAPS Token"}, {"asset": "dar", "full_name": "Mines of Dalarnia"}, {"asset": "dark", "full_name": "Dark Frontiers"}, {"asset": "dash", "full_name": "Dash"}, {"asset": "dat", "full_name": "Datum"}, {"asset": "data", "full_name": "Streamr"}, {"asset": "datx", "full_name": "DATx"}, {"asset": "dav", "full_name": "DAV Coin"}, {"asset": "dawn", "full_name": "Dawn Protocol"}, {"asset": "dax", "full_name": "DAEX"}, {"asset": "daxt", "full_name": "Digital Asset Exchange Token"}, {"asset": "day", "full_name": "Chronologic"}, {"asset": "dba", "full_name": "Digital Bank of Africa"}, {"asset": "dbc", "full_name": "DeepBrain Chain"}, {"asset": "dbet", "full_name": "DecentBet"}, {"asset": "dbix", "full_name": "DubaiCoin"}, {"asset": "dbnk", "full_name": "Debunk"}, {"asset": "dbz", "full_name": "Diamond Boyz Coin"}, {"asset": "dc", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "dcc", "full_name": "Distributed Credit Chain"}, {"asset": "dcn", "full_name": "Dentacoin"}, {"asset": "dcr", "full_name": "Decred"}, {"asset": "dct", "full_name": "DECENT"}, {"asset": "ddd", "full_name": "Scry.info"}, {"asset": "ddf", "full_name": "DigitalDevelopersFund"}, {"asset": "ddim", "full_name": "DuckDaoDime"}, {"asset": "ddm", "full_name": "DDM"}, {"asset": "ddmx", "full_name": "DDMX"}, {"asset": "ddos", "full_name": "disBalancer"}, {"asset": "ddrt", "full_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"asset": "ddx", "full_name": "DerivaDAO"}, {"asset": "dec", "full_name": "Decentr"}, {"asset": "defibear", "full_name": "3X Short DeFi Index Token"}, {"asset": "defibull", "full_name": "3X Long DeFi Index Token"}, {"asset": "defih<PERSON><PERSON>", "full_name": "0.5X Long DeFi Index Token"}, {"asset": "defihedge", "full_name": "1X Short DeFi Index Token"}, {"asset": "dego", "full_name": "Dego Finance"}, {"asset": "<PERSON><PERSON><PERSON>", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "dek", "full_name": "DekBox"}, {"asset": "deku", "full_name": "<PERSON><PERSON>"}, {"asset": "dent", "full_name": "<PERSON><PERSON>"}, {"asset": "dep", "full_name": "DEAPcoin"}, {"asset": "derc", "full_name": "DeRace"}, {"asset": "deri", "full_name": "Deri Protocol"}, {"asset": "dero", "full_name": "<PERSON><PERSON>"}, {"asset": "des", "full_name": "DeSpace Protocol"}, {"asset": "desire", "full_name": "DesireNFT"}, {"asset": "deso", "full_name": "Decentralized Social"}, {"asset": "dexa", "full_name": "DEXA COIN"}, {"asset": "dexe", "full_name": "DeXe"}, {"asset": "dexi", "full_name": "Dexioprotocol"}, {"asset": "dext", "full_name": "DEXTools"}, {"asset": "df", "full_name": "dForce"}, {"asset": "dfa", "full_name": "Define"}, {"asset": "dfc", "full_name": "DeFiScale"}, {"asset": "dfch", "full_name": "DeFi.ch"}, {"asset": "dfi", "full_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"asset": "dfl", "full_name": "Defi Land"}, {"asset": "dfnd", "full_name": "dFund"}, {"asset": "dft", "full_name": "DigiFinexToken"}, {"asset": "<PERSON><PERSON>n", "full_name": "Dfyn Network"}, {"asset": "dg", "full_name": "DeGate"}, {"asset": "dgb", "full_name": "DigiByte"}, {"asset": "dgd", "full_name": "DigixDAO"}, {"asset": "dgtx", "full_name": "Digitex"}, {"asset": "dgx", "full_name": "Digix Gold Token"}, {"asset": "dht", "full_name": "dHedge DAO"}, {"asset": "dhv", "full_name": "DeHive"}, {"asset": "dhx", "full_name": "DataHighway"}, {"asset": "dia", "full_name": "DIA"}, {"asset": "dice", "full_name": "Ether<PERSON>"}, {"asset": "digg", "full_name": "DIGG"}, {"asset": "dili", "full_name": "D Community"}, {"asset": "dim", "full_name": "DIMCOIN"}, {"asset": "dino", "full_name": "DinoSwap"}, {"asset": "dip", "full_name": "Etherisc DIP Token"}, {"asset": "dis", "full_name": "TosDis"}, {"asset": "dit", "full_name": "Digital Insurance Token"}, {"asset": "divi", "full_name": "<PERSON><PERSON>"}, {"asset": "djf", "full_name": "Djiboutian Franc"}, {"asset": "dka", "full_name": "d<PERSON><PERSON><PERSON>"}, {"asset": "dkk", "full_name": "Danish Krone"}, {"asset": "dlt", "full_name": "Agrello"}, {"asset": "dlta", "full_name": "delta.theta"}, {"asset": "dlx", "full_name": "Diplexcoin"}, {"asset": "dmd", "full_name": "Diamond"}, {"asset": "dmg", "full_name": "DMM: Governance"}, {"asset": "dmgbear", "full_name": "3X Short DMM Governance Token"}, {"asset": "dmgbull", "full_name": "3X Long DMM Governance Token"}, {"asset": "dmlg", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "dmt", "full_name": "DMarket"}, {"asset": "dmtr", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "dna", "full_name": "EncrypGen"}, {"asset": "dns", "full_name": "XUT"}, {"asset": "dnt", "full_name": "district0x"}, {"asset": "dnxc", "full_name": "DinoX"}, {"asset": "dock", "full_name": "Dock"}, {"asset": "dodo", "full_name": "DODO"}, {"asset": "doe", "full_name": "Dogs Of Elon"}, {"asset": "dog", "full_name": "Dog (Run<PERSON>)"}, {"asset": "doge", "full_name": "<PERSON><PERSON><PERSON><PERSON>", "atlas": true}, {"asset": "doge2", "full_name": "Dogecoin 2.0"}, {"asset": "<PERSON><PERSON><PERSON>", "full_name": "<PERSON><PERSON>"}, {"asset": "<PERSON><PERSON><PERSON>", "full_name": "3X Short Dog<PERSON>oin <PERSON>"}, {"asset": "dogebull", "full_name": "3X Long Dogecoin <PERSON>"}, {"asset": "dogecola", "full_name": "DogeCola"}, {"asset": "<PERSON><PERSON><PERSON>", "full_name": "<PERSON><PERSON>"}, {"asset": "<PERSON><PERSON><PERSON><PERSON>", "full_name": "0.5X Long Dogecoin <PERSON>"}, {"asset": "dogehedge", "full_name": "1X Short Dog<PERSON>oin <PERSON>"}, {"asset": "dogekongzilla", "full_name": "DogeKongZilla"}, {"asset": "dogezilla", "full_name": "DogeZilla"}, {"asset": "doggy", "full_name": "DOGGY"}, {"asset": "doki", "full_name": "Doki Doki Finance"}, {"asset": "dome", "full_name": "Everdome"}, {"asset": "dop", "full_name": "Data Ownership Protocol"}, {"asset": "dope", "full_name": "DopeCoin"}, {"asset": "dor", "full_name": "<PERSON><PERSON>"}, {"asset": "dora", "full_name": "Dora Factory"}, {"asset": "dos", "full_name": "DOS Network"}, {"asset": "dose", "full_name": "DOSE"}, {"asset": "dot", "full_name": "<PERSON><PERSON>t", "experimental": true}, {"asset": "dotdown", "full_name": "DOTDOWN"}, {"asset": "dotup", "full_name": "DOTUP"}, {"asset": "dough", "full_name": "PieDAO DOUGH v2"}, {"asset": "dov", "full_name": "Dovu"}, {"asset": "dows", "full_name": "Shadows"}, {"asset": "dox", "full_name": "Doxxed"}, {"asset": "dpet", "full_name": "My DeFi Pet"}, {"asset": "dpi", "full_name": "DeFi Pulse Index"}, {"asset": "dpn", "full_name": "DIPNET"}, {"asset": "dpr", "full_name": "Deeper Network"}, {"asset": "dpx", "full_name": "Dopex"}, {"asset": "dpy", "full_name": "<PERSON><PERSON>"}, {"asset": "drc", "full_name": "Digital Reserve Currency"}, {"asset": "dream", "full_name": "DreamTeam <PERSON>"}, {"asset": "dreams", "full_name": "Dreams Quest"}, {"asset": "drep", "full_name": "DREP"}, {"asset": "drg", "full_name": "Dragon Coins"}, {"asset": "drgn", "full_name": "Dragonchain"}, {"asset": "drgn<PERSON>ar", "full_name": "3X Short Dragon Index Token"}, {"asset": "drgnbull", "full_name": "3X Long Dragon Index Token"}, {"asset": "d<PERSON><PERSON><PERSON><PERSON>", "full_name": "0.5X Long Dragon Index Token"}, {"asset": "drgnhedge", "full_name": "1X Short Dragon Index Token"}, {"asset": "drop", "full_name": "Dropil"}, {"asset": "drpu", "full_name": "DCORP Utility"}, {"asset": "drt", "full_name": "<PERSON><PERSON><PERSON><PERSON>"}, {"asset": "dsd", "full_name": "Dynamic Set Dollar"}, {"asset": "dsg", "full_name": "D-Skyscraper"}, {"asset": "dsh", "full_name": "Dashcoin"}, {"asset": "dsla", "full_name": "DSLA Protocol"}, {"asset": "dta", "full_name": "DATA"}, {"asset": "dtb", "full_name": "DataBits"}, {"asset": "dth", "full_name": "<PERSON><PERSON>"}, {"asset": "dtr", "full_name": "Dynamic Trading Rights"}, {"asset": "ducato", "full_name": "Ducato Protocol Token"}, {"asset": "duke", "full_name": "DUKE INU TOKEN"}, {"asset": "dusk", "full_name": "Dusk"}, {"asset": "dvc", "full_name": "DragonVein"}, {"asset": "dvi", "full_name": "Dvision Network"}, {"asset": "dvp", "full_name": "Decentralized Vulnerability Platform"}, {"asset": "dws", "full_name": "DWS"}, {"asset": "dx", "full_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"asset": "dxt", "full_name": "Datawallet"}, {"asset": "dydx", "full_name": "dYdX"}, {"asset": "dyn", "full_name": "Dynamic"}, {"asset": "dyp", "full_name": "<PERSON><PERSON><PERSON><PERSON> (Old)"}, {"asset": "dzd", "full_name": "Algerian Dinar"}, {"asset": "eai", "full_name": "Ethereumai"}, {"asset": "earth", "full_name": "Earth Token"}, {"asset": "easy", "full_name": "EasyFi (Old)"}, {"asset": "eauric", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "ebet", "full_name": "EthBet"}, {"asset": "ebkc", "full_name": "Ekkoblock"}, {"asset": "ebso", "full_name": "eBlockStock"}, {"asset": "ebst", "full_name": "eBoost"}, {"asset": "ebtc", "full_name": "EBitcoin"}, {"asset": "ec", "full_name": "Echoin"}, {"asset": "eca", "full_name": "Electra"}, {"asset": "ecell", "full_name": "Consensus Cell Network"}, {"asset": "ech", "full_name": "Etherecash"}, {"asset": "ecoc", "full_name": "ECOChain"}, {"asset": "ecop", "full_name": "Eco DeFi"}, {"asset": "ecox", "full_name": "ECOx"}, {"asset": "eden", "full_name": "Eden"}, {"asset": "edg", "full_name": "<PERSON><PERSON>"}, {"asset": "edgt", "full_name": "Edgecoin"}, {"asset": "edn", "full_name": "Eden (edn)"}, {"asset": "edo", "full_name": "Eidoo"}, {"asset": "edr_endorprotocol", "full_name": "Endor Protocol"}, {"asset": "eds", "full_name": "Endorsit"}, {"asset": "edu", "full_name": "Open Campus"}, {"asset": "eek", "full_name": "Estonian Kroon"}, {"asset": "eet", "full_name": "Energy Eco Token"}, {"asset": "efi", "full_name": "Efinity Token"}, {"asset": "efil", "full_name": "Ethereum Wrapped Filecoin"}, {"asset": "efk", "full_name": "ReFork"}, {"asset": "efx", "full_name": "Effect.AI"}, {"asset": "egame", "full_name": "EVERY GAME"}, {"asset": "egc", "full_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"asset": "egcc", "full_name": "Engine"}, {"asset": "egg", "full_name": "Goose Finance"}, {"asset": "egld", "full_name": "MultiversX"}, {"asset": "egp", "full_name": "Eigenpie"}, {"asset": "egt", "full_name": "Egretia"}, {"asset": "ehash", "full_name": "EHash"}, {"asset": "ejoy", "full_name": "EJOY"}, {"asset": "eko", "full_name": "EchoLink"}, {"asset": "ekt", "full_name": "EDUCare"}, {"asset": "ela", "full_name": "Elastos"}, {"asset": "elama", "full_name": "Elamachain"}, {"asset": "elcash", "full_name": "Electric Cash"}, {"asset": "ele", "full_name": "Elementrem"}, {"asset": "elec", "full_name": "Electrify.Asia"}, {"asset": "elf", "full_name": "aelf"}, {"asset": "<PERSON><PERSON>", "full_name": "ELYFI"}, {"asset": "elmon", "full_name": "<PERSON><PERSON>"}, {"asset": "elon", "full_name": "Dogelon Mars"}, {"asset": "elongate", "full_name": "ELONGATE"}, {"asset": "elongate_old", "full_name": "ELONGATE (old)"}, {"asset": "elt", "full_name": "Element.Black"}, {"asset": "elt_edenloop", "full_name": "EdenLoop"}, {"asset": "ely", "full_name": "<PERSON><PERSON>"}, {"asset": "em", "full_name": "Empow"}, {"asset": "emax", "full_name": "EthereumMax"}, {"asset": "emb", "full_name": "Emblem"}, {"asset": "emc", "full_name": "Emercoin"}, {"asset": "emc2", "full_name": "Einsteineum"}, {"asset": "empire", "full_name": "Empire Token"}, {"asset": "emrx", "full_name": "Emirex <PERSON>"}, {"asset": "eng", "full_name": "Enigma"}, {"asset": "engt", "full_name": "Engagement Token"}, {"asset": "enj", "full_name": "Enjin Coin"}, {"asset": "enno", "full_name": "ENNO Cash"}, {"asset": "enq", "full_name": "Enecuum"}, {"asset": "ens", "full_name": "Ethereum Name Service"}, {"asset": "ent_entcash", "full_name": "ENTCash"}, {"asset": "env", "full_name": "ENV Finance"}, {"asset": "eos", "full_name": "EOS"}, {"asset": "eos_eth", "full_name": "EOS on Ethereum"}, {"asset": "eosbear", "full_name": "3x Short EOS Token"}, {"asset": "<PERSON><PERSON><PERSON>", "full_name": "3x Long EOS Token"}, {"asset": "eosc", "full_name": "EOS Force"}, {"asset": "eosdac", "full_name": "eosDAC"}, {"asset": "eosdown", "full_name": "EOSDOWN"}, {"asset": "<PERSON><PERSON><PERSON><PERSON>", "full_name": "0.5X Long EOS Token"}, {"asset": "eoshedge", "full_name": "1X Short EOS Token"}, {"asset": "eosup", "full_name": "EOSUP"}, {"asset": "epc", "full_name": "Electronic PK Chain"}, {"asset": "epik", "full_name": "EPIK Prime"}, {"asset": "epk", "full_name": "EpiK Protocol"}, {"asset": "eps", "full_name": "Ellipsis (Old)"}, {"asset": "epx", "full_name": "El<PERSON><PERSON>"}, {"asset": "eqx", "full_name": "EQIFI"}, {"asset": "eqz", "full_name": "Equalizer"}, {"asset": "erd", "full_name": "Elrond (erd)"}, {"asset": "erg", "full_name": "Ergo"}, {"asset": "erk", "full_name": "Eureka Coin"}, {"asset": "ern", "full_name": "Ethernity Chain"}, {"asset": "ero", "full_name": "Eroscoin"}, {"asset": "ersdl", "full_name": "UnFederalReserve"}, {"asset": "ert_esportsrewardtoken", "full_name": "Esports Reward Token"}, {"asset": "ertha", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "esd", "full_name": "Empty Set Dollar"}, {"asset": "esh", "full_name": "Switch"}, {"asset": "ess", "full_name": "Essentia"}, {"asset": "est", "full_name": "Esports Token"}, {"asset": "etb", "full_name": "Ethiopian Birr"}, {"asset": "etbs", "full_name": "Ethbits"}, {"asset": "etc", "full_name": "Ethereum Classic", "exchanges": ["huobi"], "markets": ["huobi-ETC200925_CQ-future"], "atlas": true}, {"asset": "etcbear", "full_name": "3X Short Ethereum Classic Token"}, {"asset": "etcbull", "full_name": "3X Long Ethereum Classic Token"}, {"asset": "etchalf", "full_name": "0.5X Long Ethereum Classic Token"}, {"asset": "<PERSON>ge", "full_name": "1X Short Ethereum Classic Token"}, {"asset": "eternal", "full_name": "CryptoMines"}, {"asset": "eth", "full_name": "Ethereum", "metrics": [{"metric": "FlowInGEMNtv", "frequencies": [{"frequency": "1b", "min_time": "2020-04-10T19:43:29.000000000Z", "max_time": "2020-04-10T19:43:45.000000000Z", "min_height": "9846470", "max_height": "9846471", "min_hash": "e2ae25ebc779861756935e7d91a4349b6082c649e8fd98d7984af4d02b2e3618", "max_hash": "35316482a9b9caf7914f24306c89c60623d4d94768752671e152fc7bf05a8c2b"}, {"frequency": "1d", "min_time": "2020-04-11T00:00:00.000000000Z", "max_time": "2020-04-11T00:00:00.000000000Z"}]}, {"metric": "FlowTfrInBFXCnt", "frequencies": [{"frequency": "1b", "min_time": "2020-04-10T19:43:45.000000000Z", "max_time": "2020-04-10T19:43:45.000000000Z", "min_height": "9846471", "max_height": "9846471", "min_hash": "35316482a9b9caf7914f24306c89c60623d4d94768752671e152fc7bf05a8c2b", "max_hash": "35316482a9b9caf7914f24306c89c60623d4d94768752671e152fc7bf05a8c2b"}]}, {"metric": "FlowTfrOutBTXCnt", "frequencies": [{"frequency": "1d", "min_time": "2020-04-08T00:00:00.000000000Z", "max_time": "2020-04-11T00:00:00.000000000Z"}]}, {"metric": "PriceUSD", "frequencies": [{"frequency": "1b", "min_time": "2020-04-12T12:08:13.000000000Z", "max_time": "2020-04-15T15:08:13.000000000Z", "min_height": "1", "max_height": "4", "min_hash": "a1", "max_hash": "a4"}]}, {"metric": "ReferenceRate", "frequencies": [{"frequency": "1d", "min_time": "2019-05-30T00:00:00.000000000Z", "max_time": "2020-01-07T00:00:00.000000000Z", "community": true}, {"frequency": "1h", "min_time": "2019-05-30T00:00:00.000000000Z", "max_time": "2021-11-04T12:00:00.000000000Z", "community": true}, {"frequency": "1m", "min_time": "2016-06-13T23:23:00.000000000Z", "max_time": "2016-06-13T23:23:00.000000000Z", "community": true}, {"frequency": "1s", "min_time": "2016-06-13T23:23:00.000000000Z", "max_time": "2016-06-13T23:23:55.000000000Z", "community": true}]}, {"metric": "ReferenceRateEUR", "frequencies": [{"frequency": "1d", "min_time": "2019-05-30T00:00:00.000000000Z", "max_time": "2019-06-04T00:00:00.000000000Z", "community": true}, {"frequency": "1h", "min_time": "2019-05-30T00:00:00.000000000Z", "max_time": "2019-07-14T01:00:00.000000000Z", "community": true}, {"frequency": "1m", "min_time": "2016-06-13T23:23:00.000000000Z", "max_time": "2016-06-13T23:23:00.000000000Z", "community": true}, {"frequency": "1s", "min_time": "2016-06-13T23:23:00.000000000Z", "max_time": "2016-06-13T23:23:55.000000000Z", "community": true}]}, {"metric": "ReferenceRateUSD", "frequencies": [{"frequency": "1d", "min_time": "2019-05-30T00:00:00.000000000Z", "max_time": "2020-01-07T00:00:00.000000000Z", "community": true}, {"frequency": "1h", "min_time": "2019-05-30T00:00:00.000000000Z", "max_time": "2021-11-04T12:00:00.000000000Z", "community": true}, {"frequency": "1m", "min_time": "2016-06-13T23:23:00.000000000Z", "max_time": "2016-06-13T23:23:00.000000000Z", "community": true}, {"frequency": "1s", "min_time": "2016-06-13T23:23:00.000000000Z", "max_time": "2016-06-13T23:23:55.000000000Z", "community": true}]}, {"metric": "RevNtv", "frequencies": [{"frequency": "1b", "min_time": "2020-04-12T12:08:13.000000000Z", "max_time": "2020-04-15T15:08:13.000000000Z", "min_height": "1", "max_height": "4", "min_hash": "a1", "max_hash": "a4"}]}, {"metric": "SplyExUSD", "frequencies": [{"frequency": "1d", "min_time": "2020-04-08T00:00:00.000000000Z", "max_time": "2020-04-11T00:00:00.000000000Z"}]}, {"metric": "TxCnt", "frequencies": [{"frequency": "1b", "min_time": "2020-04-12T12:08:13.000000000Z", "max_time": "2020-04-15T15:08:13.000000000Z", "min_height": "1", "max_height": "4", "min_hash": "a1", "max_hash": "a4"}, {"frequency": "1d", "min_time": "2020-04-08T00:00:00.000000000Z", "max_time": "2020-04-11T00:00:00.000000000Z", "community": true}]}, {"metric": "block_missed_slots", "frequencies": [{"frequency": "1b", "min_time": "2022-12-28T12:39:59.000000000Z", "max_time": "2022-12-28T12:40:23.000000000Z", "min_height": "16283307", "max_height": "16283309", "min_hash": "40d74de6db8e0e42b180fad9ad93a7ab46e5129d3031637ca769a165abedcf48", "max_hash": "b75604303ed8e8cef23d8bd1748b555030b36848fbd7530ea3172e3818b14470"}]}, {"metric": "block_tx_count", "frequencies": [{"frequency": "1b", "min_time": "2022-12-28T12:39:59.000000000Z", "max_time": "2022-12-28T12:40:23.000000000Z", "min_height": "16283307", "max_height": "16283309", "min_hash": "40d74de6db8e0e42b180fad9ad93a7ab46e5129d3031637ca769a165abedcf48", "max_hash": "b75604303ed8e8cef23d8bd1748b555030b36848fbd7530ea3172e3818b14470"}]}, {"metric": "open_interest_reported_future_usd", "frequencies": [{"frequency": "1d", "min_time": "2020-12-25T05:00:00.000000000Z", "max_time": "2020-12-25T05:00:00.000000000Z"}, {"frequency": "1h", "min_time": "2020-12-25T05:00:00.000000000Z", "max_time": "2020-12-25T05:00:00.000000000Z"}]}, {"metric": "principal_market_price_usd", "frequencies": [{"frequency": "1d", "min_time": "2022-10-09T00:00:00.000000000Z", "max_time": "2022-10-09T00:00:00.000000000Z", "community": true}, {"frequency": "1d-ny-close", "min_time": "2022-10-09T12:00:00.000000000Z", "max_time": "2022-10-09T12:00:00.000000000Z", "community": true}, {"frequency": "1h", "min_time": "2022-10-08T23:00:00.000000000Z", "max_time": "2022-10-09T12:00:00.000000000Z", "community": true}, {"frequency": "1m", "min_time": "2022-10-09T17:58:00.000000000Z", "max_time": "2022-10-09T17:59:00.000000000Z", "community": true}, {"frequency": "1s", "min_time": "2022-10-09T17:58:00.000000000Z", "max_time": "2022-10-09T17:59:49.000000000Z", "community": true}]}, {"metric": "principal_market_usd", "frequencies": [{"frequency": "1d", "min_time": "2022-10-09T00:00:00.000000000Z", "max_time": "2022-10-09T00:00:00.000000000Z", "community": true}, {"frequency": "1d-ny-close", "min_time": "2022-10-09T12:00:00.000000000Z", "max_time": "2022-10-09T12:00:00.000000000Z", "community": true}, {"frequency": "1h", "min_time": "2022-10-08T23:00:00.000000000Z", "max_time": "2022-10-09T12:00:00.000000000Z", "community": true}, {"frequency": "1m", "min_time": "2022-10-09T17:58:00.000000000Z", "max_time": "2022-10-09T17:59:00.000000000Z", "community": true}, {"frequency": "1s", "min_time": "2022-10-09T17:58:00.000000000Z", "max_time": "2022-10-09T17:59:49.000000000Z", "community": true}]}, {"metric": "volatility_realized_usd_rolling_24h", "frequencies": [{"frequency": "10m", "min_time": "2022-11-28T22:20:00.000000000Z", "max_time": "2022-11-29T23:30:00.000000000Z"}, {"frequency": "1d", "min_time": "2022-11-28T22:20:00.000000000Z", "max_time": "2022-11-29T23:30:00.000000000Z"}, {"frequency": "1h", "min_time": "2022-11-28T22:20:00.000000000Z", "max_time": "2022-11-29T23:30:00.000000000Z"}]}, {"metric": "volatility_realized_usd_rolling_30d", "frequencies": [{"frequency": "10m", "min_time": "2022-11-28T22:20:00.000000000Z", "max_time": "2022-11-29T23:30:00.000000000Z"}, {"frequency": "1d", "min_time": "2022-11-28T22:20:00.000000000Z", "max_time": "2022-11-29T23:30:00.000000000Z"}, {"frequency": "1h", "min_time": "2022-11-28T22:20:00.000000000Z", "max_time": "2022-11-29T23:30:00.000000000Z"}]}, {"metric": "volatility_realized_usd_rolling_7d", "frequencies": [{"frequency": "10m", "min_time": "2022-11-28T22:20:00.000000000Z", "max_time": "2022-11-29T23:30:00.000000000Z"}, {"frequency": "1d", "min_time": "2022-11-28T22:20:00.000000000Z", "max_time": "2022-11-29T23:30:00.000000000Z"}, {"frequency": "1h", "min_time": "2022-11-28T22:20:00.000000000Z", "max_time": "2022-11-29T23:30:00.000000000Z"}]}, {"metric": "volume_trusted_spot_usd_1d", "frequencies": [{"frequency": "1d", "min_time": "2020-12-29T00:00:00.000000000Z", "max_time": "2020-12-29T00:00:00.000000000Z"}]}, {"metric": "volume_trusted_spot_usd_1h", "frequencies": [{"frequency": "1h", "min_time": "2020-12-25T05:00:00.000000000Z", "max_time": "2020-12-25T05:00:00.000000000Z"}]}], "exchanges": ["binance", "bittrex", "bybit", "coinbase", "curve_eth", "deribit", "huobi"], "markets": ["binance-eth-btc-spot", "bittrex-eth-usd-spot", "bybit-ETH-30APR23-2010-C-option", "bybit-ETH-30APR23-2010-P-option", "bybit-ETH-31MAY23-2020-P-option", "bybit-eth-usd-spot", "coinbase-eth-usd-spot", "curve_eth-1-cbeth-eth-spot", "deribit-ETH-1OCT21-2850-P-option", "deribit-ETH-1OCT21-3200-P-option", "deribit-ETH-24JUN22-1000-C-option", "deribit-ETH-28JAN24-future", "deribit-ETH-29OCT21-2000-P-option", "deribit-ETH-2APR21-1960-C-option", "deribit-ETH-2OCT21-3250-P-option", "deribit-ETH-30APR23-2010-C-option", "deribit-ETH-30APR23-2010-P-option", "deribit-ETH-30MAR21-1440-P-option", "deribit-ETH-30MAR21-1700-P-option", "deribit-ETH-30MAR21-1720-C-option", "deribit-ETH-30MAR21-1760-P-option", "deribit-ETH-30MAR21-1920-C-option", "deribit-ETH-31DEC21-4000-C-option", "deribit-ETH-31JAN25-4400-P-option", "deribit-ETH-31MAY23-2020-P-option", "deribit-ETH-PERPETUAL-future", "deribit-eth-usdc-spot", "huobi-ETH-30APR23-2010-C-option", "huobi-ETH-30APR23-2010-P-option", "huobi-ETH-31MAY23-2020-P-option", "huobi-ETH200807_NW-future", "huobi-ETH200925_CQ-future"], "atlas": true}, {"asset": "eth2", "full_name": "Eth 2.0 Staking by Pool-X"}, {"asset": "eth2x", "full_name": "Ethereum 2x"}, {"asset": "eth3l", "full_name": "Ethereum 3x Leveraged Long Token"}, {"asset": "eth3s", "full_name": "Ethereum 3x Leveraged Short Token"}, {"asset": "eth_cl", "full_name": "Ethereum Consensus Layer"}, {"asset": "eth_op", "full_name": "Ethereum on Optimism"}, {"asset": "etha", "full_name": "ETHA Lend"}, {"asset": "ethbear", "full_name": "3X Short Ethereum Token"}, {"asset": "ethbnt", "full_name": "ETHBNT Relay"}, {"asset": "eth<PERSON>", "full_name": "3X Long Ethereum Token"}, {"asset": "ethdown", "full_name": "ETHDOWN"}, {"asset": "ethe", "full_name": "ETHEKing"}, {"asset": "ethf", "full_name": "EthereumFair"}, {"asset": "et<PERSON><PERSON>f", "full_name": "0.5X Long Ethereum Token"}, {"asset": "ethhedge", "full_name": "1X Short Ethereum Token"}, {"asset": "etho", "full_name": "Etho Protocol"}, {"asset": "ethos", "full_name": "Voyager Token (ethos)"}, {"asset": "ethup", "full_name": "ETHUP"}, {"asset": "ethw", "full_name": "EthereumPoW"}, {"asset": "etl", "full_name": "Etherlite"}, {"asset": "etm", "full_name": "En-<PERSON>-<PERSON>"}, {"asset": "etn", "full_name": "Electroneum"}, {"asset": "etp", "full_name": "Metaverse ETP"}, {"asset": "etz", "full_name": "<PERSON><PERSON>"}, {"asset": "eul", "full_name": "<PERSON>uler"}, {"asset": "euno", "full_name": "EUNO"}, {"asset": "eur", "full_name": "Euro"}, {"asset": "eurc_eth", "full_name": "EURC on Ethereum", "exchanges": ["curve_eth"], "markets": ["curve_eth-2-ageur_eth-eurc_eth-spot"]}, {"asset": "eurl_eth", "full_name": "Lugh EURO on Ethereum"}, {"asset": "eurn", "full_name": "NokuEUR"}, {"asset": "euroc", "full_name": "EURC (Old)"}, {"asset": "eurs", "full_name": "STASIS EURO"}, {"asset": "eurt", "full_name": "Tether EURt"}, {"asset": "eva", "full_name": "Evanesco Network"}, {"asset": "ever", "full_name": "Everscale"}, {"asset": "evmos", "full_name": "Evmos"}, {"asset": "evry", "full_name": "EVRYNET"}, {"asset": "evt", "full_name": "Ethfinex Voting Tokens"}, {"asset": "evx", "full_name": "Everex"}, {"asset": "ewt", "full_name": "Energy Web Token"}, {"asset": "exc_eximchain", "full_name": "Eximchain"}, {"asset": "exchbear", "full_name": "3X Short Exchange Token Index Token"}, {"asset": "exchbull", "full_name": "3X Long Exchange Token Index Token"}, {"asset": "exchhalf", "full_name": "0.5X Long Exchange Token Index Token"}, {"asset": "exchhedge", "full_name": "1X Short Exchange Token Index Token"}, {"asset": "excl", "full_name": "ExclusiveCoin"}, {"asset": "exe", "full_name": "8X8 PROTOCOL"}, {"asset": "exm", "full_name": "EXMO Coin"}, {"asset": "exn", "full_name": "ExchangeN"}, {"asset": "exp", "full_name": "Expanse"}, {"asset": "exrd", "full_name": "e-Radix"}, {"asset": "ext", "full_name": "Experience Token"}, {"asset": "exy", "full_name": "Experty"}, {"asset": "ez", "full_name": "EasyFi"}, {"asset": "ezt", "full_name": "EZToken"}, {"asset": "face", "full_name": "Faceter"}, {"asset": "fair", "full_name": "Fairmoon"}, {"asset": "fair_fairgame", "full_name": "FairGame"}, {"asset": "fame_famemma", "full_name": "Fame MMA"}, {"asset": "fame_fantommaker", "full_name": "Fantom Maker"}, {"asset": "fan", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "far", "full_name": "Farcana"}, {"asset": "fara", "full_name": "FaraLand"}, {"asset": "farm", "full_name": "Harvest Finance"}, {"asset": "fast", "full_name": "FastSwap"}, {"asset": "fb", "full_name": "Fractal Bitcoin"}, {"asset": "fc", "full_name": "FuturesCoin"}, {"asset": "fcf", "full_name": "French Connection Finance"}, {"asset": "fch", "full_name": "Freecash"}, {"asset": "fcl", "full_name": "Fractal"}, {"asset": "fcn", "full_name": "Fantomcoin"}, {"asset": "fct", "full_name": "Factom"}, {"asset": "fct2", "full_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"asset": "fdx", "full_name": "FidentiaX"}, {"asset": "fdz", "full_name": "Friendz"}, {"asset": "fear", "full_name": "Fear"}, {"asset": "feg", "full_name": "FEG Token"}, {"asset": "fei", "full_name": "Fei USD"}, {"asset": "fei_eth", "full_name": "Fei Protocol on Ethereum"}, {"asset": "fess", "full_name": "Fesschain"}, {"asset": "fet", "full_name": "Artificial Superintelligence Alliance"}, {"asset": "fevr", "full_name": "RealFevr"}, {"asset": "fic", "full_name": "FinCredit Protocol"}, {"asset": "fida", "full_name": "Solana Name Service"}, {"asset": "fil", "full_name": "Filecoin", "exchanges": ["bitmex"], "markets": ["bitmex-FILUSD-future"]}, {"asset": "fil6", "full_name": "FIL6"}, {"asset": "filda", "full_name": "<PERSON><PERSON>"}, {"asset": "fildown", "full_name": "FILDOWN"}, {"asset": "filup", "full_name": "FILUP"}, {"asset": "fin", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "fina", "full_name": "Defina Finance"}, {"asset": "fine", "full_name": "Refinable"}, {"asset": "fio", "full_name": "FIO Protocol"}, {"asset": "fire", "full_name": "Fire Protocol"}, {"asset": "firo", "full_name": "<PERSON><PERSON>"}, {"asset": "fis", "full_name": "StaFi"}, {"asset": "fish", "full_name": "Polycat Finance"}, {"asset": "fit", "full_name": "FINANCIAL INVESTMENT TOKEN"}, {"asset": "fitfi", "full_name": "Step App"}, {"asset": "fjd", "full_name": "Fijian Dollar"}, {"asset": "fkp", "full_name": "Falkland Islands Pound"}, {"asset": "fkx", "full_name": "FortKnoxster"}, {"asset": "flame", "full_name": "FireStarter"}, {"asset": "fldc", "full_name": "FoldingCoin"}, {"asset": "fldt", "full_name": "FairyLand"}, {"asset": "fleta", "full_name": "FLETA"}, {"asset": "flixx", "full_name": "Flixxo"}, {"asset": "flm", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "flo", "full_name": "FLO"}, {"asset": "floki", "full_name": "FLOKI"}, {"asset": "flokin", "full_name": "Flokinomics"}, {"asset": "flow", "full_name": "Flow"}, {"asset": "flp", "full_name": "FLIP"}, {"asset": "flr", "full_name": "Flare"}, {"asset": "flurry", "full_name": "Flurry Finance"}, {"asset": "flux", "full_name": "Flux"}, {"asset": "fluz", "full_name": "Fluz Fluz"}, {"asset": "flx", "full_name": "Reflexer Ungovernance Token"}, {"asset": "fly", "full_name": "<PERSON>"}, {"asset": "fme", "full_name": "FME"}, {"asset": "fn", "full_name": "Filenet"}, {"asset": "fnb", "full_name": "FinexboxToken"}, {"asset": "fnk", "full_name": "FunKeyPay"}, {"asset": "fnx", "full_name": "FinNexus"}, {"asset": "fo", "full_name": "FIBOS"}, {"asset": "foam", "full_name": "FOAM"}, {"asset": "fodl", "full_name": "FODL Finance"}, {"asset": "foin", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "fol", "full_name": "Folder Protocol"}, {"asset": "fomp", "full_name": "Fompound"}, {"asset": "for", "full_name": "ForTube"}, {"asset": "forestplus", "full_name": "The Forbidden Forest"}, {"asset": "form", "full_name": "Four"}, {"asset": "fort", "full_name": "Forta"}, {"asset": "forth", "full_name": "Ampleforth Governance Token"}, {"asset": "fota", "full_name": "Fortuna"}, {"asset": "four", "full_name": "4THPILLAR TECHNOLOGIES"}, {"asset": "fox", "full_name": "Shapeshift FOX Token"}, {"asset": "fpft", "full_name": "Peruvian National Football Team Fan Token"}, {"asset": "fra", "full_name": "Find<PERSON>"}, {"asset": "frax", "full_name": "Frax"}, {"asset": "frec", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "free", "full_name": "FREEdom Coin"}, {"asset": "free_freerossdao", "full_name": "FreeRossDAO"}, {"asset": "frm", "full_name": "Ferrum Network"}, {"asset": "frog", "full_name": "CryptoFrog.Finance"}, {"asset": "front", "full_name": "Frontier"}, {"asset": "frsp", "full_name": "Forkspot"}, {"asset": "fshib", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "fsn", "full_name": "Fusion"}, {"asset": "fst", "full_name": "Futureswap"}, {"asset": "fsw", "full_name": "Falconswap"}, {"asset": "ftc", "full_name": "Feathercoin"}, {"asset": "ftg", "full_name": "fantomGO"}, {"asset": "fti", "full_name": "FansTime"}, {"asset": "ftm", "full_name": "<PERSON><PERSON>"}, {"asset": "ftm_eth", "full_name": "Fantom on Ethereum"}, {"asset": "ftt", "full_name": "FTX Token"}, {"asset": "ftt_facter", "full_name": "Facter"}, {"asset": "ftt_farmatrust", "full_name": "FarmaTrust"}, {"asset": "ftx", "full_name": "FintruX Network"}, {"asset": "fuel", "full_name": "Etherparty"}, {"asset": "fun", "full_name": "FUNToken"}, {"asset": "fund", "full_name": "Unification"}, {"asset": "fwb", "full_name": "Friends With Benefits Pro"}, {"asset": "fx", "full_name": "Function X"}, {"asset": "fxc", "full_name": "Flexacoin"}, {"asset": "fxc_eth", "full_name": "Flexacoin on Ethereum"}, {"asset": "fxf", "full_name": "Finxflo"}, {"asset": "fxs", "full_name": "Frax Share"}, {"asset": "fxt", "full_name": "FuzeX"}, {"asset": "fyd", "full_name": "FYDcoin"}, {"asset": "fyn", "full_name": "FundYourselfNow"}, {"asset": "fyp", "full_name": "FlypMe"}, {"asset": "g999", "full_name": "G999"}, {"asset": "gafi", "full_name": "GameFi"}, {"asset": "gaia", "full_name": "GAIA EVERWorld"}, {"asset": "gal", "full_name": "Galxe"}, {"asset": "gala", "full_name": "Gala"}, {"asset": "galft", "full_name": "Galatasaray Fan Token"}, {"asset": "galt", "full_name": "Haichain"}, {"asset": "gam", "full_name": "Gambit"}, {"asset": "game", "full_name": "$GAME Token"}, {"asset": "gan", "full_name": "Galactic Arena: The NFTverse"}, {"asset": "gard", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "gari", "full_name": "Gari Network"}, {"asset": "gark", "full_name": "Game Ark"}, {"asset": "gart", "full_name": "<PERSON> Art"}, {"asset": "gas", "full_name": "Gas"}, {"asset": "gas_gasdao", "full_name": "Gas DAO"}, {"asset": "gasp", "full_name": "gAsp"}, {"asset": "gat", "full_name": "Global AEX Token"}, {"asset": "gat_gatcoin", "full_name": "Gatcoin"}, {"asset": "gbg", "full_name": "<PERSON><PERSON>"}, {"asset": "gbp", "full_name": "British Pound"}, {"asset": "gbpt", "full_name": "poundtoken"}, {"asset": "gbt", "full_name": "GameBet"}, {"asset": "gbtc", "full_name": "Grayscale Bitcoin Trust Tokenized Stock"}, {"asset": "gbx", "full_name": "GoByte"}, {"asset": "gbyte", "full_name": "Byteball Bytes"}, {"asset": "gdao", "full_name": "Governor <PERSON><PERSON><PERSON>"}, {"asset": "gdoge", "full_name": "Golden Doge"}, {"asset": "gdr", "full_name": "Golden Roots"}, {"asset": "gdt", "full_name": "Globe Derivative Exchange"}, {"asset": "ge", "full_name": "<PERSON><PERSON><PERSON>n <PERSON>"}, {"asset": "gear", "full_name": "Gearbox"}, {"asset": "gear_metagear", "full_name": "Meta<PERSON>ear"}, {"asset": "geeq", "full_name": "<PERSON><PERSON>"}, {"asset": "gel", "full_name": "Georgian Lari"}, {"asset": "gem", "full_name": "Gems"}, {"asset": "gen", "full_name": "DAOstack"}, {"asset": "gene", "full_name": "Genopets"}, {"asset": "gens", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "geo", "full_name": "GeoCoin"}, {"asset": "gera", "full_name": "Gera Coin"}, {"asset": "get_getprotocol", "full_name": "GET Protocol"}, {"asset": "get_themis", "full_name": "<PERSON>is"}, {"asset": "geth", "full_name": "Guarded <PERSON><PERSON>"}, {"asset": "gf", "full_name": "GuildFi"}, {"asset": "gfi", "full_name": "Goldfinch"}, {"asset": "gft", "full_name": "<PERSON><PERSON>"}, {"asset": "gft_galaxyfinance", "full_name": "Galaxy Finance"}, {"asset": "gft_gamefantasy", "full_name": "Game Fantasy"}, {"asset": "ggc", "full_name": "Global Game Coin"}, {"asset": "ggg", "full_name": "Good Games Guild"}, {"asset": "ggp", "full_name": "Guernsey Pound"}, {"asset": "ghc", "full_name": "Galaxy Heroes Coin"}, {"asset": "ghd", "full_name": "Giftedhands"}, {"asset": "ghost", "full_name": "GHOST"}, {"asset": "ghs", "full_name": "<PERSON><PERSON>"}, {"asset": "ghst", "full_name": "<PERSON><PERSON><PERSON><PERSON>"}, {"asset": "ghx", "full_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"asset": "gim", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "gip", "full_name": "Gibraltar Pound"}, {"asset": "gl", "full_name": "Green Light"}, {"asset": "glc", "full_name": "GoldCoin"}, {"asset": "gld", "full_name": "SPDR Gold Trust Tokenized Stock"}, {"asset": "glm", "full_name": "Golem", "exchanges": ["uniswap_v3_eth"], "markets": ["uniswap_v3_eth-3-glm-weth-spot"]}, {"asset": "glmr", "full_name": "Moonbeam"}, {"asset": "glq", "full_name": "Graphlinq Protocol"}, {"asset": "gls", "full_name": "<PERSON><PERSON>"}, {"asset": "glxy", "full_name": "Galaxy Digital Holdings Tokenized Stock"}, {"asset": "glyph", "full_name": "GLYPH Vault (NFTX)"}, {"asset": "gm", "full_name": "GM Wagmi"}, {"asset": "gm_goldminer", "full_name": "GoldMiner"}, {"asset": "gmat", "full_name": "GoWithMi"}, {"asset": "gmb", "full_name": "GMB"}, {"asset": "gmc", "full_name": "GokuMarket Credit"}, {"asset": "gmcoin", "full_name": "GMCoin"}, {"asset": "gmd", "full_name": "Gambian Dalasi"}, {"asset": "gmee", "full_name": "GAMEE"}, {"asset": "gmt", "full_name": "GMT"}, {"asset": "gmt_eth", "full_name": "STEPN Governance token on Ethereum"}, {"asset": "gmt_gmttoken", "full_name": "GMT Token"}, {"asset": "gmx", "full_name": "GMX"}, {"asset": "gn", "full_name": "GN"}, {"asset": "gnbt", "full_name": "Genebank Token"}, {"asset": "gnc", "full_name": "GALAXY NETWORK"}, {"asset": "gnf", "full_name": "Guinean Franc"}, {"asset": "gno", "full_name": "Gnosis"}, {"asset": "gns", "full_name": "Gains Network"}, {"asset": "gnt", "full_name": "Golem (gnt)"}, {"asset": "gnx", "full_name": "Genaro Network"}, {"asset": "gny", "full_name": "GNY"}, {"asset": "go", "full_name": "<PERSON><PERSON><PERSON><PERSON>"}, {"asset": "goal", "full_name": "TOPGOAL"}, {"asset": "god", "full_name": "Bitcoin God"}, {"asset": "gods", "full_name": "Gods Unchained"}, {"asset": "gof", "full_name": "<PERSON><PERSON>"}, {"asset": "gofx", "full_name": "GooseFX"}, {"asset": "gog", "full_name": "Guild of Guardians"}, {"asset": "goku", "full_name": "GOKU INU"}, {"asset": "gold", "full_name": "Golden Goose"}, {"asset": "goldr", "full_name": "Golden Ratio Coin"}, {"asset": "golos", "full_name": "<PERSON><PERSON>"}, {"asset": "googl", "full_name": "Google Tokenized Stock"}, {"asset": "got", "full_name": "ParkinGO"}, {"asset": "govi", "full_name": "<PERSON><PERSON>"}, {"asset": "gpt", "full_name": "CryptoGPT"}, {"asset": "gpyx", "full_name": "GoldenPyrex"}, {"asset": "grail", "full_name": "<PERSON><PERSON>"}, {"asset": "gram", "full_name": "Gram"}, {"asset": "grbe", "full_name": "Green Beli"}, {"asset": "grc", "full_name": "Grid<PERSON><PERSON><PERSON>"}, {"asset": "grin", "full_name": "<PERSON><PERSON>"}, {"asset": "grmd", "full_name": "GreenMed"}, {"asset": "grs", "full_name": "Groestlcoin"}, {"asset": "grt", "full_name": "The Graph"}, {"asset": "gs", "full_name": "Genesis Shards"}, {"asset": "gsc", "full_name": "Global Social Chain"}, {"asset": "gse", "full_name": "GSENetwork"}, {"asset": "gst", "full_name": "<PERSON> (SOL)"}, {"asset": "gst_grearn", "full_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"asset": "gst_gstcoin", "full_name": "Gstcoin"}, {"asset": "gt", "full_name": "GateToken"}, {"asset": "gt_eth", "full_name": "GateToken on Ethereum"}, {"asset": "gtc", "full_name": "Gitcoin"}, {"asset": "gtc_gamecom", "full_name": "Game.com"}, {"asset": "gth", "full_name": "<PERSON><PERSON>"}, {"asset": "gto", "full_name": "<PERSON><PERSON> (Old)"}, {"asset": "gtq", "full_name": "Guatemalan <PERSON>"}, {"asset": "gum", "full_name": "Gourmet Galaxy"}, {"asset": "gup", "full_name": "Matchpool"}, {"asset": "gusd", "full_name": "Gemini Dollar"}, {"asset": "gve", "full_name": "Globalvillage Ecosystem"}, {"asset": "gvt", "full_name": "Genesis Vision"}, {"asset": "gxs", "full_name": "GXChain"}, {"asset": "gyd", "full_name": "Guyanaese Dollar"}, {"asset": "gyen", "full_name": "GYEN"}, {"asset": "gze", "full_name": "GazeCoin"}, {"asset": "gzil", "full_name": "governance ZIL"}, {"asset": "gzone", "full_name": "GameZone"}, {"asset": "hac", "full_name": "Hackspace Capital"}, {"asset": "hai", "full_name": "Hacken Token"}, {"asset": "haka", "full_name": "TribeOne"}, {"asset": "half", "full_name": "0.5X Long Bitcoin Token"}, {"asset": "halfshit", "full_name": "0.5X Long Shitcoin Index Token"}, {"asset": "hand", "full_name": "ShowHand"}, {"asset": "hapi", "full_name": "HAPI Protocol"}, {"asset": "hard", "full_name": "<PERSON><PERSON>"}, {"asset": "hash", "full_name": "Provenance Blockchain"}, {"asset": "hbar", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "hbc", "full_name": "HBTC Captain <PERSON><PERSON>"}, {"asset": "hbd", "full_name": "Hive Dollar"}, {"asset": "hbot", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "hbtc", "full_name": "Huobi BTC"}, {"asset": "hbz", "full_name": "Helbiz"}, {"asset": "hc_hypercash", "full_name": "HyperCash"}, {"asset": "hct", "full_name": "HurricaneSwap <PERSON>"}, {"asset": "hdac", "full_name": "Hdac"}, {"asset": "hdao", "full_name": "HyperDAO"}, {"asset": "he", "full_name": "Heroes & Empires"}, {"asset": "hedg", "full_name": "<PERSON><PERSON><PERSON><PERSON>"}, {"asset": "hedge", "full_name": "1x Short Bitcoin Token"}, {"asset": "hedgeshit", "full_name": "1X Short Shitcoin Index Token"}, {"asset": "hegic", "full_name": "<PERSON><PERSON>"}, {"asset": "helios", "full_name": "Mission Helios"}, {"asset": "her", "full_name": "HeroNode"}, {"asset": "hera", "full_name": "Hero Arena"}, {"asset": "hero", "full_name": "Metahero"}, {"asset": "hero_hero", "full_name": "Hero"}, {"asset": "hero_stephero", "full_name": "Step Hero"}, {"asset": "hex", "full_name": "HEX"}, {"asset": "hez", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "hft", "full_name": "Hashflow"}, {"asset": "hget", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "hgt", "full_name": "HelloGold"}, {"asset": "hifi", "full_name": "Hifi Finance"}, {"asset": "high", "full_name": "Highstreet"}, {"asset": "hiko", "full_name": "HIKOBABA"}, {"asset": "hint", "full_name": "<PERSON>nt<PERSON><PERSON>"}, {"asset": "hire", "full_name": "HireMatch"}, {"asset": "hit", "full_name": "<PERSON><PERSON><PERSON><PERSON>"}, {"asset": "hive", "full_name": "Hive"}, {"asset": "hkd", "full_name": "Hong Kong Dollar"}, {"asset": "hkun", "full_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (new)"}, {"asset": "hlc", "full_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"asset": "hmc_himutualsociety", "full_name": "Hi Mutual Society"}, {"asset": "hmq", "full_name": "Humaniq"}, {"asset": "hmr", "full_name": "<PERSON><PERSON>"}, {"asset": "hmt", "full_name": "Human"}, {"asset": "hnl", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "hns", "full_name": "Handshake"}, {"asset": "hnt", "full_name": "Helium"}, {"asset": "hod", "full_name": "HoDooi.com"}, {"asset": "hodl", "full_name": "HODL 2.0"}, {"asset": "hoge", "full_name": "Hoge Finance"}, {"asset": "hokk", "full_name": "Hokkaidu Inu"}, {"asset": "holy", "full_name": "Holy Trinity"}, {"asset": "hood", "full_name": "Hood AI"}, {"asset": "hook", "full_name": "Hooked Protocol"}, {"asset": "hopr", "full_name": "HOPR"}, {"asset": "hord", "full_name": "<PERSON><PERSON>"}, {"asset": "hot_holo", "full_name": "Holo"}, {"asset": "hot_hottoken", "full_name": "HOT Token"}, {"asset": "hot_hydroprotocol", "full_name": "Hydro Protocol (Old)"}, {"asset": "hotc", "full_name": "HOTChain"}, {"asset": "hotcross", "full_name": "Hot Cross"}, {"asset": "hpb", "full_name": "High Performance Blockchain"}, {"asset": "hpc", "full_name": "Happycoin"}, {"asset": "hpt", "full_name": "Huobi Pool Token"}, {"asset": "hpy", "full_name": "Hyper Pay"}, {"asset": "hqx", "full_name": "HOQU"}, {"asset": "hrk", "full_name": "Croatian Kuna"}, {"asset": "hro", "full_name": "CryptoDiceHero"}, {"asset": "hsc", "full_name": "Hash<PERSON><PERSON>n"}, {"asset": "hsr", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "hst", "full_name": "Decision Token"}, {"asset": "ht", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "htbear", "full_name": "3X Short Huobi Token Token"}, {"asset": "htbull", "full_name": "3X Long Huobi Token Token"}, {"asset": "htdf", "full_name": "Orient Walt"}, {"asset": "htg", "full_name": "Haitian Gourde"}, {"asset": "hthalf", "full_name": "0.5X Long Huobi Token Token"}, {"asset": "hthedge", "full_name": "1X Short Huobi Token Token"}, {"asset": "html", "full_name": "HTMLCOIN"}, {"asset": "htmoon", "full_name": "HTMoon"}, {"asset": "htr", "full_name": "Hathor"}, {"asset": "hub", "full_name": "<PERSON><PERSON>"}, {"asset": "huc", "full_name": "<PERSON><PERSON><PERSON><PERSON>"}, {"asset": "huf", "full_name": "Hungarian Forint"}, {"asset": "hum", "full_name": "Humanscape"}, {"asset": "husd", "full_name": "HUSD"}, {"asset": "hvn", "full_name": "Hive Project"}, {"asset": "hx", "full_name": "HyperExchange"}, {"asset": "hxro", "full_name": "Hxro"}, {"asset": "hyc", "full_name": "HYCON"}, {"asset": "hydra", "full_name": "Hydra"}, {"asset": "hydro", "full_name": "Hydro"}, {"asset": "hyn", "full_name": "Hyperion"}, {"asset": "hyve", "full_name": "Hyve"}, {"asset": "hzm", "full_name": "HZM Coin"}, {"asset": "iag", "full_name": "IAGON"}, {"asset": "ibvol", "full_name": "Inverse Bitcoin Volatility Token"}, {"asset": "ica", "full_name": "Icarus Finance"}, {"asset": "ice", "full_name": "Popsicle Finance"}, {"asset": "ice_decentralgamesice", "full_name": "Decentral Games ICE"}, {"asset": "icn", "full_name": "Iconomi"}, {"asset": "ico", "full_name": "ICOBI"}, {"asset": "icons", "full_name": "SportsIcon"}, {"asset": "icos", "full_name": "ICOS"}, {"asset": "icp", "full_name": "Internet Computer", "experimental": true}, {"asset": "icx", "full_name": "ICON"}, {"asset": "icx_eth", "full_name": "ICON on Ethereum"}, {"asset": "id", "full_name": "SPACE ID"}, {"asset": "idea", "full_name": "Ideaology"}, {"asset": "idex", "full_name": "IDEX"}, {"asset": "idh", "full_name": "indaHash"}, {"asset": "idia", "full_name": "Impossible Decentralized Incubator Access"}, {"asset": "idr", "full_name": "Indonesian Rupiah"}, {"asset": "idrt", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "idt", "full_name": "InvestDigital"}, {"asset": "idv", "full_name": "Idavoll Network"}, {"asset": "ift", "full_name": "InvestFeed"}, {"asset": "ignis", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "igu", "full_name": "IguVerse"}, {"asset": "ihf", "full_name": "Invictus Hyperion Fund"}, {"asset": "iht", "full_name": "IHT Real Estate Protocol"}, {"asset": "iic", "full_name": "Intelligent Investment Chain"}, {"asset": "ila", "full_name": "Infinite Launch"}, {"asset": "ils", "full_name": "Israeli New <PERSON><PERSON>"}, {"asset": "ilv", "full_name": "Illuvium"}, {"asset": "imc", "full_name": "i Money Crypto"}, {"asset": "imi", "full_name": "Influencer"}, {"asset": "imp", "full_name": "Isle of Man Pound"}, {"asset": "imx", "full_name": "Immutable"}, {"asset": "inb", "full_name": "Insight Chain"}, {"asset": "inc", "full_name": "Influence Chain"}, {"asset": "incnt", "full_name": "Incent"}, {"asset": "ind", "full_name": "Indorse <PERSON>"}, {"asset": "index", "full_name": "Index Cooperative"}, {"asset": "indi", "full_name": "IndiGG"}, {"asset": "inj", "full_name": "Injective"}, {"asset": "ink", "full_name": "Ink"}, {"asset": "ino", "full_name": "INO COIN"}, {"asset": "inr", "full_name": "Indian Rupee"}, {"asset": "ins", "full_name": "INS Ecosystem"}, {"asset": "inst", "full_name": "Instadapp"}, {"asset": "instar", "full_name": "Insights Network"}, {"asset": "insur", "full_name": "InsurAce"}, {"asset": "int", "full_name": "Internet Node Token"}, {"asset": "inter", "full_name": "Inter Milan Fan Token"}, {"asset": "intr", "full_name": "Interlay"}, {"asset": "inv", "full_name": "Inverse Finance"}, {"asset": "inx", "full_name": "Insight Protocol"}, {"asset": "inxt", "full_name": "Internxt"}, {"asset": "ioc", "full_name": "I/O Coin"}, {"asset": "ioen", "full_name": "Internet of Energy Network"}, {"asset": "ioex", "full_name": "IOEX"}, {"asset": "ioi", "full_name": "IOI Token"}, {"asset": "ion", "full_name": "ION"}, {"asset": "ionx", "full_name": "Charged Particles"}, {"asset": "iop", "full_name": "Internet of People"}, {"asset": "iost", "full_name": "IOST"}, {"asset": "iotx", "full_name": "IoTeX"}, {"asset": "iov", "full_name": "Carlive Chain"}, {"asset": "ip3", "full_name": "CRIPCO"}, {"asset": "ipad", "full_name": "Infinity Pad"}, {"asset": "ipc", "full_name": "IPChain"}, {"asset": "ipl", "full_name": "VouchForMe"}, {"asset": "ipsx", "full_name": "IP Exchange"}, {"asset": "ipx", "full_name": "Tachyon Protocol"}, {"asset": "iq", "full_name": "IQ"}, {"asset": "iqd", "full_name": "Iraqi <PERSON>"}, {"asset": "iqn", "full_name": "IQeon"}, {"asset": "iqx", "full_name": "IQToken"}, {"asset": "iris", "full_name": "IRISnet"}, {"asset": "irr", "full_name": "Iranian Rial"}, {"asset": "isk", "full_name": "Icelandic Krona"}, {"asset": "isp", "full_name": "Ispolink"}, {"asset": "itc", "full_name": "IoT Chain"}, {"asset": "itgr", "full_name": "Integral"}, {"asset": "ivy", "full_name": "Ivy"}, {"asset": "ix", "full_name": "XBlock"}, {"asset": "ixs", "full_name": "IX Swap"}, {"asset": "ixt", "full_name": "IXT"}, {"asset": "jam", "full_name": "Tune.Fm"}, {"asset": "jam_geojam", "full_name": "<PERSON><PERSON><PERSON><PERSON>"}, {"asset": "jar", "full_name": "<PERSON>+"}, {"asset": "jasmy", "full_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"asset": "jbc", "full_name": "Japan Brand Coin"}, {"asset": "jep", "full_name": "Jersey Pound"}, {"asset": "jet", "full_name": "Satoshi Airline"}, {"asset": "jewel", "full_name": "DeFi Kingdoms"}, {"asset": "jfi", "full_name": "JackPool.finance"}, {"asset": "jgn", "full_name": "Juggernaut"}, {"asset": "jind", "full_name": "JINDO INU"}, {"asset": "jmd", "full_name": "Jamaican Dollar"}, {"asset": "jnt", "full_name": "Jibrel Network"}, {"asset": "job", "full_name": "Jobchain"}, {"asset": "jod", "full_name": "<PERSON><PERSON>"}, {"asset": "joe", "full_name": "JOE"}, {"asset": "jot", "full_name": "Jury.Online Token"}, {"asset": "jpy", "full_name": "Japanese Yen"}, {"asset": "jst", "full_name": "JUST"}, {"asset": "jul", "full_name": "JustLiquidity"}, {"asset": "julb", "full_name": "JustLiquidity Binance"}, {"asset": "juld", "full_name": "JulSwap"}, {"asset": "juno", "full_name": "JUNO"}, {"asset": "jup", "full_name": "Jupiter"}, {"asset": "jus", "full_name": "JUST NETWORK"}, {"asset": "juv", "full_name": "Juventus Fan Token"}, {"asset": "k21", "full_name": "K21"}, {"asset": "ka<PERSON>u", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "kaby", "full_name": "Kaby Arena"}, {"asset": "kai", "full_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"asset": "kainet", "full_name": "KAINET"}, {"asset": "kala", "full_name": "Kala Finance"}, {"asset": "kalm", "full_name": "Kalmar"}, {"asset": "kan", "full_name": "BitKan"}, {"asset": "kar", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "kat", "full_name": "Kambria"}, {"asset": "kava", "full_name": "<PERSON><PERSON>"}, {"asset": "kawa", "full_name": "<PERSON><PERSON><PERSON><PERSON>"}, {"asset": "kbc", "full_name": "Karatgold Coin"}, {"asset": "kbr", "full_name": "Kubera Coin"}, {"asset": "kcal", "full_name": "KCAL"}, {"asset": "kcash", "full_name": "Kcash"}, {"asset": "kcs", "full_name": "<PERSON><PERSON><PERSON><PERSON>"}, {"asset": "kda", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "kdag", "full_name": "King DAG"}, {"asset": "kdc", "full_name": "<PERSON><PERSON><PERSON><PERSON>"}, {"asset": "kdoe", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "keanu", "full_name": "<PERSON><PERSON>"}, {"asset": "keep", "full_name": "Keep Network"}, {"asset": "kes", "full_name": "Kenyan Shilling"}, {"asset": "kex", "full_name": "Kira Network"}, {"asset": "key", "full_name": "Self<PERSON>ey"}, {"asset": "kfc", "full_name": "Chicken"}, {"asset": "kft", "full_name": "Knit Finance"}, {"asset": "kgc", "full_name": "Krypton Galaxy Coin"}, {"asset": "kgs", "full_name": "Kyrgystani Som"}, {"asset": "khr", "full_name": "Cambodian Riel"}, {"asset": "kick", "full_name": "KickCoin"}, {"asset": "kicks", "full_name": "KicksPad"}, {"asset": "kif", "full_name": "KittenFinance"}, {"asset": "kilt", "full_name": "KILT Protocol"}, {"asset": "kimchi", "full_name": "KIMCHI.finance"}, {"asset": "kin", "full_name": "<PERSON>n"}, {"asset": "kin1", "full_name": "KIN1"}, {"asset": "kin3", "full_name": "KIN3"}, {"asset": "kind", "full_name": "Kind Ads Token"}, {"asset": "king", "full_name": "CryptoBlades Kingdoms"}, {"asset": "kings<PERSON>b", "full_name": "King <PERSON><PERSON>"}, {"asset": "kint", "full_name": "Kintsugi"}, {"asset": "<PERSON><PERSON><PERSON>", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "kishu", "full_name": "<PERSON><PERSON>"}, {"asset": "klay", "full_name": "Klaytn"}, {"asset": "klayg", "full_name": "KlayGames"}, {"asset": "klo", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "klv", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "kma", "full_name": "Calamari Network"}, {"asset": "kmd", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "kmf", "full_name": "<PERSON><PERSON>"}, {"asset": "kmon", "full_name": "Kryptomon"}, {"asset": "knc", "full_name": "Kyber Network Crystal"}, {"asset": "<PERSON><PERSON><PERSON><PERSON>", "full_name": "3X Short Kyber Network Token"}, {"asset": "<PERSON><PERSON><PERSON>", "full_name": "3X Long Kyber Network Token"}, {"asset": "<PERSON><PERSON><PERSON>", "full_name": "0.5X Long Kyber Network Token"}, {"asset": "knchedge", "full_name": "1X Short Kyber Network Token"}, {"asset": "knight", "full_name": "<PERSON>"}, {"asset": "kok", "full_name": "KOK"}, {"asset": "kol", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "kon", "full_name": "KONPAY"}, {"asset": "kono", "full_name": "Konomi Network"}, {"asset": "kore", "full_name": "<PERSON><PERSON>"}, {"asset": "koro<PERSON><PERSON>", "full_name": "KOROMARU"}, {"asset": "kp3r", "full_name": "Keep3rV1"}, {"asset": "kpad", "full_name": "KickPad"}, {"asset": "kpw", "full_name": "North Korean Won"}, {"asset": "krl", "full_name": "Kryll"}, {"asset": "krm", "full_name": "<PERSON>rma"}, {"asset": "krt", "full_name": "TerraKRW"}, {"asset": "krw", "full_name": "Korean won"}, {"asset": "kshib", "full_name": "<PERSON><PERSON>"}, {"asset": "ksm", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "kst", "full_name": "KSM Starter"}, {"asset": "ktn", "full_name": "Kattana"}, {"asset": "kton", "full_name": "Darwinia Commitment Token"}, {"asset": "kub", "full_name": "Bitkub Coin"}, {"asset": "kuma", "full_name": "<PERSON><PERSON>"}, {"asset": "kwd", "full_name": "<PERSON><PERSON>"}, {"asset": "kyd", "full_name": "Cayman Islands Dollar"}, {"asset": "kzt", "full_name": "<PERSON><PERSON>"}, {"asset": "l3p", "full_name": "<PERSON><PERSON><PERSON><PERSON>"}, {"asset": "la", "full_name": "LATOKEN"}, {"asset": "labs", "full_name": "LABS Group"}, {"asset": "lak", "full_name": "<PERSON><PERSON>"}, {"asset": "lala", "full_name": "LALA World"}, {"asset": "lamb", "full_name": "Lambda"}, {"asset": "land", "full_name": "Landshare"}, {"asset": "larix", "full_name": "<PERSON><PERSON>"}, {"asset": "las", "full_name": "LNAsolution Coin"}, {"asset": "lat", "full_name": "PlatON"}, {"asset": "latte", "full_name": "LatteSwap"}, {"asset": "lava", "full_name": "LavaSwap"}, {"asset": "layer", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "lazio", "full_name": "S.S. Lazio Fan Token"}, {"asset": "lba", "full_name": "<PERSON><PERSON>"}, {"asset": "lbc", "full_name": "LBRY Credits"}, {"asset": "lbk", "full_name": "LBK"}, {"asset": "lbl", "full_name": "LABEL AI"}, {"asset": "lbp", "full_name": "Lebanese Pound"}, {"asset": "lbtc_lightningbitcoin", "full_name": "Lightning Bitcoin"}, {"asset": "lcc", "full_name": "Litecoin Cash"}, {"asset": "lcs", "full_name": "LocalCoinSwap"}, {"asset": "lcx", "full_name": "LCX"}, {"asset": "ldc", "full_name": "Leadcoin"}, {"asset": "ldo", "full_name": "Lido DAO"}, {"asset": "leash", "full_name": "<PERSON><PERSON>"}, {"asset": "<PERSON>u", "full_name": "Education Ecosystem"}, {"asset": "lemd", "full_name": "Lemond"}, {"asset": "lemo", "full_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"asset": "lend", "full_name": "Aave (lend)"}, {"asset": "leo", "full_name": "UNUS SED LEO"}, {"asset": "leo_eos", "full_name": "UNUS SED LEO on EOS"}, {"asset": "leo_eth", "full_name": "UNUS SED LEO on Ethereum"}, {"asset": "leobear", "full_name": "3X Short LEO Token"}, {"asset": "leob<PERSON>", "full_name": "3X Long LEO Token"}, {"asset": "<PERSON><PERSON><PERSON><PERSON>", "full_name": "0.5X Long LEO Token"}, {"asset": "leohedge", "full_name": "1X Short LEO Token"}, {"asset": "leopard", "full_name": "LEOPARD"}, {"asset": "let", "full_name": "LinkEye"}, {"asset": "lev", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "lever", "full_name": "LeverFi"}, {"asset": "levl", "full_name": "Levolution"}, {"asset": "lf", "full_name": "Life DAO"}, {"asset": "lfw", "full_name": "Legend of Fantasy War"}, {"asset": "lgcy", "full_name": "LGCY Network"}, {"asset": "lgo", "full_name": "LGO Exchange"}, {"asset": "lhb", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "libre", "full_name": "Libre DeFi"}, {"asset": "lid", "full_name": "Liquidity Dividends Protocol"}, {"asset": "lien", "full_name": "<PERSON><PERSON>"}, {"asset": "life", "full_name": "LIFE"}, {"asset": "light", "full_name": "Light<PERSON>hain"}, {"asset": "like", "full_name": "Only1"}, {"asset": "lime", "full_name": "iMe Lab"}, {"asset": "lina", "full_name": "Linear"}, {"asset": "link", "full_name": "Chainlink", "exchanges": ["huobi", "uniswap_v3_eth"], "markets": ["huobi-LINK200925_CQ-future", "uniswap_v3_eth-2-link-weth-spot"]}, {"asset": "linkbear", "full_name": "3X Short Chainlink Token"}, {"asset": "linkbull", "full_name": "3X Long Chainlink Token"}, {"asset": "linkdown", "full_name": "LINKDOWN"}, {"asset": "linkhalf", "full_name": "0.5X Long Chainlink Token"}, {"asset": "linkhedge", "full_name": "1X Short Chainlink"}, {"asset": "linkup", "full_name": "LINKUP"}, {"asset": "lion", "full_name": "Lion Token"}, {"asset": "liq", "full_name": "LIQ Protocol"}, {"asset": "lit", "full_name": "Litentry"}, {"asset": "lith", "full_name": "Lithium"}, {"asset": "live", "full_name": "TRONbetLive"}, {"asset": "lkn", "full_name": "Link<PERSON><PERSON>n <PERSON>"}, {"asset": "lkr", "full_name": "Sri Lankan Rupee"}, {"asset": "lky", "full_name": "<PERSON><PERSON>"}, {"asset": "llt", "full_name": "LLToken"}, {"asset": "lm", "full_name": "LM Token"}, {"asset": "lmc", "full_name": "LoMoCoin"}, {"asset": "lmch", "full_name": "Latamcash"}, {"asset": "lmr", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "lnc_linkercoin", "full_name": "Linker Coin"}, {"asset": "lnchx", "full_name": "LaunchX"}, {"asset": "lnd", "full_name": "Lendingblock"}, {"asset": "loc", "full_name": "LockTrip"}, {"asset": "locg", "full_name": "LOCGame"}, {"asset": "loka", "full_name": "League of Kingdoms Arena"}, {"asset": "loki", "full_name": "<PERSON>"}, {"asset": "lol", "full_name": "EMOGI Network"}, {"asset": "lon", "full_name": "Tokenlon Network Token"}, {"asset": "looks", "full_name": "LooksRare"}, {"asset": "loom", "full_name": "Loom Network"}, {"asset": "loon", "full_name": "Loon Network"}, {"asset": "lot", "full_name": "Lukki Operating Token"}, {"asset": "lowb", "full_name": "Loser Coin"}, {"asset": "lpool", "full_name": "Launchpool"}, {"asset": "lpt", "full_name": "Livepeer"}, {"asset": "lqty", "full_name": "Liquity"}, {"asset": "lrc", "full_name": "Loopring"}, {"asset": "lrc_eth", "full_name": "Loopring on Ethereum"}, {"asset": "lrd", "full_name": "Liberian Dollar"}, {"asset": "lrg", "full_name": "Largo Coin"}, {"asset": "lrn", "full_name": "Loopring [NEO]"}, {"asset": "lsk", "full_name": "Lisk"}, {"asset": "lsl", "full_name": "Lesotho Loti"}, {"asset": "lsp", "full_name": "Lumenswap"}, {"asset": "lss", "full_name": "Lossless"}, {"asset": "ltc", "full_name": "Litecoin", "exchanges": ["huobi"], "markets": ["huobi-LTC200807_NW-future"], "atlas": true}, {"asset": "ltcbear", "full_name": "3x Short Litecoin <PERSON>"}, {"asset": "ltcbull", "full_name": "3x Long Litecoin <PERSON>"}, {"asset": "ltcdown", "full_name": "LTCDOWN"}, {"asset": "ltchalf", "full_name": "0.5X Long Litecoin <PERSON>"}, {"asset": "ltchedge", "full_name": "1X Short Litecoin Token"}, {"asset": "ltcup", "full_name": "LTCUP"}, {"asset": "ltg", "full_name": "LiteGold"}, {"asset": "ltl", "full_name": "Lithuanian Litas"}, {"asset": "ltnm", "full_name": "Bitcoin Latinum"}, {"asset": "lto", "full_name": "LTO Network"}, {"asset": "ltx", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "lua", "full_name": "LuaSwap"}, {"asset": "luc", "full_name": "Level Up Coin"}, {"asset": "lucky", "full_name": "LUCKY"}, {"asset": "lucy", "full_name": "LUCY"}, {"asset": "lud", "full_name": "Ludos Protocol"}, {"asset": "luffy", "full_name": "LUFFY"}, {"asset": "lumi", "full_name": "LUMI Credits"}, {"asset": "lun", "full_name": "Lunyr"}, {"asset": "luna", "full_name": "Terra Classic", "experimental": true}, {"asset": "luna2", "full_name": "Terra 2.0"}, {"asset": "lunapad", "full_name": "Luna-Pad"}, {"asset": "lusd", "full_name": "Liquity USD"}, {"asset": "lvl", "full_name": "Latvian Lats"}, {"asset": "lvn", "full_name": "<PERSON><PERSON>"}, {"asset": "lwf", "full_name": "Local World Forwarders"}, {"asset": "lxt", "full_name": "Litex"}, {"asset": "lyd", "full_name": "Libyan Dinar"}, {"asset": "lym", "full_name": "Lympo"}, {"asset": "lyxe", "full_name": "LUKSO (Old)"}, {"asset": "m", "full_name": "M Chain"}, {"asset": "mad", "full_name": "Moroccan <PERSON><PERSON><PERSON>"}, {"asset": "mag_maggie", "full_name": "<PERSON>"}, {"asset": "magic", "full_name": "Treasure"}, {"asset": "maha", "full_name": "MAHA.xyz"}, {"asset": "mai", "full_name": "Mi<PERSON>"}, {"asset": "maid", "full_name": "MaidSafeCoin"}, {"asset": "maki", "full_name": "MakiSwap"}, {"asset": "man", "full_name": "Matrix AI Network"}, {"asset": "mana", "full_name": "Decentraland"}, {"asset": "map", "full_name": "MarcoPolo Protocol"}, {"asset": "maps", "full_name": "MAPS"}, {"asset": "maro", "full_name": "Maro"}, {"asset": "mars", "full_name": "Mars"}, {"asset": "mars4", "full_name": "MARS4"}, {"asset": "marsh", "full_name": "<PERSON><PERSON><PERSON><PERSON>"}, {"asset": "marsinu", "full_name": "Mars <PERSON>"}, {"asset": "marsrise", "full_name": "MarsRise"}, {"asset": "mask", "full_name": "Mask Network"}, {"asset": "mass", "full_name": "MASS"}, {"asset": "mat", "full_name": "My Master War"}, {"asset": "math", "full_name": "MATH"}, {"asset": "matic", "full_name": "Polygon"}, {"asset": "matic_eth", "full_name": "<PERSON><PERSON> on Ethereum"}, {"asset": "matic_polygon", "full_name": "Polygon Matic"}, {"asset": "maticbear", "full_name": "3X Short Matic <PERSON>"}, {"asset": "matic<PERSON>", "full_name": "3X Long Matic <PERSON>ken"}, {"asset": "<PERSON><PERSON><PERSON><PERSON>", "full_name": "0.5X <PERSON> Mat<PERSON>"}, {"asset": "matichedge", "full_name": "1X Short Matic <PERSON>"}, {"asset": "matter", "full_name": "<PERSON><PERSON><PERSON><PERSON>"}, {"asset": "mbf", "full_name": "MoonBear Finance"}, {"asset": "mbl", "full_name": "MovieBloc"}, {"asset": "mbox", "full_name": "MOBOX"}, {"asset": "mbs", "full_name": "UNKJD"}, {"asset": "mc", "full_name": "Merit Circle"}, {"asset": "mcap", "full_name": "MCAP"}, {"asset": "mcash", "full_name": "Monsoon Finance"}, {"asset": "mcb", "full_name": "MUX Protocol"}, {"asset": "mcc", "full_name": "Moving Cloud Coin"}, {"asset": "mch", "full_name": "MeconCash"}, {"asset": "mco", "full_name": "MCO"}, {"asset": "mco2", "full_name": "Moss Carbon Credit"}, {"asset": "mcrn", "full_name": "MacaronSwap"}, {"asset": "mcrt", "full_name": "MagicCraft"}, {"asset": "mda", "full_name": "Moeda Loyalty Points"}, {"asset": "mdc", "full_name": "<PERSON>"}, {"asset": "mdf", "full_name": "MatrixETF"}, {"asset": "mdl", "full_name": "Moldovan Leu"}, {"asset": "mds", "full_name": "MediShares"}, {"asset": "mdt", "full_name": "Measurable Data Token"}, {"asset": "mdx", "full_name": "Mdex"}, {"asset": "me", "full_name": "Magic Eden"}, {"asset": "med", "full_name": "MediBloc"}, {"asset": "meda", "full_name": "Medacoin"}, {"asset": "media", "full_name": "Media Network"}, {"asset": "medx", "full_name": "Medibloc [ERC20]"}, {"asset": "meet", "full_name": "CoinMeet"}, {"asset": "meetone", "full_name": "MEET.ONE"}, {"asset": "meli", "full_name": "MELI Games"}, {"asset": "mem", "full_name": "Memecoin (Old)"}, {"asset": "meme", "full_name": "Memecoin"}, {"asset": "mepad", "full_name": "MemePad"}, {"asset": "mer", "full_name": "Mercurial Finance"}, {"asset": "meri", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "mesh", "full_name": "MeshBox"}, {"asset": "messe", "full_name": "MESSE TOKEN"}, {"asset": "met", "full_name": "Metronome"}, {"asset": "meta", "full_name": "Metadium"}, {"asset": "metax", "full_name": "MetaverseX"}, {"asset": "metis", "full_name": "<PERSON><PERSON>"}, {"asset": "mewtwo", "full_name": "Mewtwo Inu"}, {"asset": "mex", "full_name": "MEX"}, {"asset": "mexp", "full_name": "MOJI Experience Points"}, {"asset": "mflokiada", "full_name": "MiniFlokiADA"}, {"asset": "mft", "full_name": "Hifi Finance (Old)"}, {"asset": "mg", "full_name": "MinerGate Token"}, {"asset": "mga", "full_name": "Malagasy Ariary"}, {"asset": "mgo", "full_name": "MobileGo"}, {"asset": "mhc", "full_name": "#MetaHash"}, {"asset": "microshib", "full_name": "MicroSHIBA"}, {"asset": "midbear", "full_name": "3X Short Midcap Index Token"}, {"asset": "midbull", "full_name": "3X Long Midcap Index Token"}, {"asset": "midhalf", "full_name": "0.5X Long Midcap Index Token"}, {"asset": "midhedge", "full_name": "1X Short Midcap Index Token"}, {"asset": "mim", "full_name": "Magic Internet Money"}, {"asset": "mim_mintmarble", "full_name": "Mint Marble"}, {"asset": "<PERSON>mir", "full_name": "<PERSON><PERSON>"}, {"asset": "min", "full_name": "MINDOL"}, {"asset": "mina", "full_name": "Mina"}, {"asset": "mine", "full_name": "SpaceMine"}, {"asset": "minidoge", "full_name": "MiniDOGE"}, {"asset": "minisaitama", "full_name": "Mini Saitama"}, {"asset": "mint", "full_name": "Mint Blockchain"}, {"asset": "mintys", "full_name": "MintySwap"}, {"asset": "miota", "full_name": "IOTA"}, {"asset": "mir", "full_name": "Mirror Protocol"}, {"asset": "mis", "full_name": "Mi<PERSON>ril Share"}, {"asset": "mist", "full_name": "Alchemist"}, {"asset": "mith", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "mitx", "full_name": "Morpheus Labs"}, {"asset": "mix", "full_name": "MixMarvel"}, {"asset": "miyazaki", "full_name": "Miyazaki Inu"}, {"asset": "mkcy", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "mkd", "full_name": "Macedonian Denar"}, {"asset": "mkr", "full_name": "Maker"}, {"asset": "mkrbear", "full_name": "3X Short Maker Token"}, {"asset": "mk<PERSON><PERSON>", "full_name": "3X Long Maker Token"}, {"asset": "mld", "full_name": "MOLD"}, {"asset": "mlk", "full_name": "MiL.k"}, {"asset": "mln", "full_name": "Enzyme"}, {"asset": "mm", "full_name": "MM Token"}, {"asset": "mmaon", "full_name": "MMAON"}, {"asset": "mmk", "full_name": "Myanmar Kyat"}, {"asset": "mnc", "full_name": "Moneynet"}, {"asset": "mnde", "full_name": "Marinade"}, {"asset": "mne", "full_name": "Minereum"}, {"asset": "mnet", "full_name": "MINE Network"}, {"asset": "mngo", "full_name": "Mango"}, {"asset": "mnst", "full_name": "MoonStarter"}, {"asset": "mnstrs", "full_name": "Block Monsters"}, {"asset": "mnt", "full_name": "Mantle"}, {"asset": "mntp", "full_name": "GoldMint"}, {"asset": "mnw", "full_name": "Morpheus.Network"}, {"asset": "mnx", "full_name": "MinexCoin"}, {"asset": "mo", "full_name": "Morality"}, {"asset": "moac", "full_name": "MOAC"}, {"asset": "mob", "full_name": "MobileCoin"}, {"asset": "mobi", "full_name": "Mobius"}, {"asset": "moc", "full_name": "Moss Coin"}, {"asset": "mochi", "full_name": "<PERSON><PERSON>"}, {"asset": "mod", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "moda", "full_name": "MODA DAO"}, {"asset": "mof", "full_name": "Molecular Future"}, {"asset": "mogx", "full_name": "Mo<PERSON>"}, {"asset": "moma", "full_name": "Mochi Market"}, {"asset": "mon", "full_name": "MON Protocol"}, {"asset": "mona", "full_name": "MonaCoin"}, {"asset": "mona_monavale", "full_name": "Monavale"}, {"asset": "moni", "full_name": "Monsta Infinite"}, {"asset": "mons", "full_name": "Monsters Clan"}, {"asset": "moonrise", "full_name": "MoonRise"}, {"asset": "moov", "full_name": "<PERSON><PERSON><PERSON><PERSON>"}, {"asset": "mop", "full_name": "Macanese <PERSON>"}, {"asset": "more_morecoin", "full_name": "More Coin"}, {"asset": "morph", "full_name": "Morpheus Token"}, {"asset": "mot", "full_name": "Olympus Labs"}, {"asset": "mov", "full_name": "MOTIV Protocol"}, {"asset": "movez", "full_name": "MOVEZ"}, {"asset": "movr", "full_name": "Moonriver"}, {"asset": "mph", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "mpl", "full_name": "Maple"}, {"asset": "mplx", "full_name": "Metaplex"}, {"asset": "mrch", "full_name": "MerchDAO"}, {"asset": "mrk", "full_name": "MARK.SPACE"}, {"asset": "mrna", "full_name": "Moderna Tokenized Stock"}, {"asset": "mro", "full_name": "Mauritanian <PERSON><PERSON><PERSON><PERSON> (old)"}, {"asset": "mrs", "full_name": "Margin<PERSON>"}, {"asset": "mru", "full_name": "Mauritanian Ouguiya"}, {"asset": "msb", "full_name": "Misbloc"}, {"asset": "msol", "full_name": "Marinade Staked SOL"}, {"asset": "msp", "full_name": "Mothership"}, {"asset": "mstr", "full_name": "Monsterra"}, {"asset": "mswap", "full_name": "MoneySwap"}, {"asset": "mt", "full_name": "MyToken"}, {"asset": "mta", "full_name": "mStable Governance Token: Meta (MTA)"}, {"asset": "mtc", "full_name": "Metacoin"}, {"asset": "mtc_docademic", "full_name": "Docademic"}, {"asset": "mtc_mtcmeshnetwork", "full_name": "MTC Mesh Network"}, {"asset": "mth", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "mtl_malteselira", "full_name": "Maltese Lira"}, {"asset": "mtl_metal", "full_name": "Metal DAO", "metrics": [{"metric": "ReferenceRate", "frequencies": [{"frequency": "1s", "min_time": "2016-06-13T23:23:54.000000000Z", "max_time": "2016-06-13T23:23:54.000000000Z", "community": true}]}, {"metric": "ReferenceRateUSD", "frequencies": [{"frequency": "1s", "min_time": "2016-06-13T23:23:54.000000000Z", "max_time": "2016-06-13T23:23:54.000000000Z", "community": true}]}]}, {"asset": "mtlx", "full_name": "Mettalex"}, {"asset": "mtn", "full_name": "Medicalchain"}, {"asset": "mtr", "full_name": "Meter Stable"}, {"asset": "mtrg", "full_name": "Meter Governance"}, {"asset": "mts", "full_name": "<PERSON><PERSON> (Old)"}, {"asset": "mts_metastrike", "full_name": "MTS"}, {"asset": "mtv", "full_name": "MultiVAC"}, {"asset": "mtx", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "mue", "full_name": "MonetaryUnit"}, {"asset": "multi", "full_name": "Multichain"}, {"asset": "mur", "full_name": "Mauritian Rupee"}, {"asset": "musd", "full_name": "mStable USD"}, {"asset": "muse", "full_name": "Muse"}, {"asset": "music", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "musk", "full_name": "MUSK Token"}, {"asset": "mv", "full_name": "<PERSON><PERSON><PERSON><PERSON>"}, {"asset": "mvc", "full_name": "Maverick Chain"}, {"asset": "mvl", "full_name": "MVL"}, {"asset": "mvp", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "mvr", "full_name": "Maldivian Rufiyaa"}, {"asset": "mw", "full_name": "MasterWin"}, {"asset": "mwat", "full_name": "Restart Energy MWAT"}, {"asset": "mwk", "full_name": "<PERSON><PERSON>"}, {"asset": "mx", "full_name": "MX Token"}, {"asset": "mx_marsx", "full_name": "MarsX"}, {"asset": "mxc", "full_name": "Moonchain"}, {"asset": "mxm", "full_name": "<PERSON><PERSON>in"}, {"asset": "mxn", "full_name": "Mexican Peso"}, {"asset": "mxnt", "full_name": "Mexican Peso Tether"}, {"asset": "mxt", "full_name": "MixTrust"}, {"asset": "mxw", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "myb", "full_name": "MyBit Token"}, {"asset": "myc", "full_name": "Mycelium"}, {"asset": "myce", "full_name": "MY Ceremonial Event"}, {"asset": "myid", "full_name": "MY IDENTITY COIN"}, {"asset": "myr", "full_name": "Malaysian Ringgit"}, {"asset": "myra", "full_name": "Mytheria"}, {"asset": "myst", "full_name": "Mysterium"}, {"asset": "myth", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "mzn", "full_name": "Mozambican Metical"}, {"asset": "nabox", "full_name": "Nabox"}, {"asset": "nad", "full_name": "Namibian Dollar"}, {"asset": "naft", "full_name": "Nafter"}, {"asset": "naka", "full_name": "Nakamoto Games"}, {"asset": "nam", "full_name": "NAM COIN"}, {"asset": "nami", "full_name": "<PERSON><PERSON>"}, {"asset": "nanj", "full_name": "NANJCOIN"}, {"asset": "nano", "full_name": "<PERSON><PERSON> (New)"}, {"asset": "naos", "full_name": "NAOS Finance"}, {"asset": "nap", "full_name": "Napoli Fan <PERSON>"}, {"asset": "nas", "full_name": "<PERSON><PERSON><PERSON><PERSON>"}, {"asset": "nas_eth", "full_name": "Nebulas on Ethereum"}, {"asset": "nasadoge", "full_name": "<PERSON><PERSON>"}, {"asset": "nav", "full_name": "Navcoin"}, {"asset": "navi", "full_name": "<PERSON><PERSON><PERSON><PERSON>"}, {"asset": "nax", "full_name": "NextDAO"}, {"asset": "nbl", "full_name": "Nobility"}, {"asset": "nbot", "full_name": "<PERSON><PERSON>"}, {"asset": "nbp", "full_name": "NFTBomb"}, {"asset": "nbs", "full_name": "New BitShares"}, {"asset": "nbt", "full_name": "Nanobyte Token"}, {"asset": "nbtc", "full_name": "NEOBITCOIN"}, {"asset": "ncash", "full_name": "Nitro Network"}, {"asset": "ncc", "full_name": "Neur<PERSON><PERSON>"}, {"asset": "nct", "full_name": "PolySwarm"}, {"asset": "ndau", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "ndc", "full_name": "NEVERDIE"}, {"asset": "ndn", "full_name": "NDN Link"}, {"asset": "near", "full_name": "NEAR Protocol"}, {"asset": "nebl", "full_name": "Neblio"}, {"asset": "nec", "full_name": "Nectar"}, {"asset": "neo", "full_name": "Neo"}, {"asset": "neos", "full_name": "NeosCoin"}, {"asset": "nest", "full_name": "NEST Protocol"}, {"asset": "net_nimiqexchangetoken", "full_name": "Nimiq Exchange Token"}, {"asset": "neu", "full_name": "Neumark"}, {"asset": "nevada", "full_name": "Nevada"}, {"asset": "new", "full_name": "<PERSON>"}, {"asset": "newos", "full_name": "NewsToken"}, {"asset": "nex", "full_name": "Nash Exchange"}, {"asset": "nexo", "full_name": "Nexo"}, {"asset": "next", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "nflx", "full_name": "Netflix Tokenized Stock"}, {"asset": "nft", "full_name": "APENFT"}, {"asset": "nft_nftprotocol", "full_name": "NFT Protocol"}, {"asset": "nftart", "full_name": "NFT Art Finance"}, {"asset": "nftb", "full_name": "NFTb"}, {"asset": "nftd", "full_name": "NFTrade"}, {"asset": "nftx", "full_name": "NFTX"}, {"asset": "ngc", "full_name": "NAGA"}, {"asset": "ngl", "full_name": "Gold Fever"}, {"asset": "ngm", "full_name": "e-Money"}, {"asset": "ngn", "full_name": "Nigerian Naira"}, {"asset": "nhbtc", "full_name": "nHBTC"}, {"asset": "nif", "full_name": "Unifty"}, {"asset": "nift", "full_name": "Niftify"}, {"asset": "nii", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "<PERSON><PERSON>i", "full_name": "Nii<PERSON><PERSON>"}, {"asset": "nim", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "nio", "full_name": "Nio Inc Tokenized Stock"}, {"asset": "nio_autonio", "full_name": "Autonio"}, {"asset": "nio_nicaraguancordoba", "full_name": "Nicaraguan Cordoba"}, {"asset": "nix", "full_name": "NIX"}, {"asset": "nkc", "full_name": "Nework"}, {"asset": "nkclc", "full_name": "NKCL Classic"}, {"asset": "nkn", "full_name": "NKN"}, {"asset": "nlc2", "full_name": "NoLimitCoin"}, {"asset": "nlg", "full_name": "Gulden"}, {"asset": "nmc", "full_name": "Namecoin"}, {"asset": "nmr", "full_name": "Numeraire"}, {"asset": "nmt", "full_name": "NFTMart <PERSON>"}, {"asset": "noa", "full_name": "NOA PLAY"}, {"asset": "noah", "full_name": "Noah Coin"}, {"asset": "node", "full_name": "Whole Network"}, {"asset": "nodl", "full_name": "Nodle"}, {"asset": "noia", "full_name": "Syntropy"}, {"asset": "nok", "full_name": "Norwegian Krone"}, {"asset": "noku", "full_name": "<PERSON><PERSON>"}, {"asset": "nom", "full_name": "Onomy Protocol"}, {"asset": "nora", "full_name": "SnowCrash Token"}, {"asset": "nord", "full_name": "Nord Finance"}, {"asset": "nper", "full_name": "NPER"}, {"asset": "nplc", "full_name": "PlusCoin"}, {"asset": "npr", "full_name": "Nepalese Rupee"}, {"asset": "npt", "full_name": "NEOPIN"}, {"asset": "npxs", "full_name": "<PERSON>und<PERSON> X (Old)"}, {"asset": "nrg", "full_name": "Energi"}, {"asset": "nrv", "full_name": "Nerve Finance"}, {"asset": "ntk_neurotoken", "full_name": "Neurotoken"}, {"asset": "nto", "full_name": "Fujinto"}, {"asset": "ntvrk", "full_name": "Netvrk"}, {"asset": "ntx", "full_name": "NuNet"}, {"asset": "nu", "full_name": "NuCypher"}, {"asset": "nuls", "full_name": "NULS"}, {"asset": "num", "full_name": "Numbers Protocol"}, {"asset": "nut", "full_name": "Native Utility Token"}, {"asset": "nux", "full_name": "Peanut"}, {"asset": "nvda", "full_name": "NVIDIA Corporation Tokenized Stock"}, {"asset": "nvt", "full_name": "NerveNetwork"}, {"asset": "nwc", "full_name": "Numerico"}, {"asset": "nxc", "full_name": "Nexium"}, {"asset": "nxm", "full_name": "Nexus Mutual"}, {"asset": "nxs", "full_name": "Nexus"}, {"asset": "nxt", "full_name": "Nxt"}, {"asset": "nym", "full_name": "NYM"}, {"asset": "nyzo", "full_name": "Nyzo"}, {"asset": "nzd", "full_name": "New Zealand Dollar"}, {"asset": "o3", "full_name": "O3 Swap"}, {"asset": "oak", "full_name": "Acorn Collective Token"}, {"asset": "oas", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "oax", "full_name": "OAX"}, {"asset": "oc", "full_name": "OceanChain"}, {"asset": "ocean", "full_name": "Ocean Protocol", "metrics": [{"metric": "CapMrktEstUSD", "frequencies": [{"frequency": "1d", "min_time": "2019-07-17T00:00:00.000000000Z", "max_time": "2019-07-26T00:00:00.000000000Z"}]}]}, {"asset": "ocn", "full_name": "Odyssey"}, {"asset": "oct", "full_name": "Oracle<PERSON>hain"}, {"asset": "octo", "full_name": "OctoFi"}, {"asset": "oda", "full_name": "<PERSON><PERSON><PERSON><PERSON>"}, {"asset": "oddz", "full_name": "<PERSON><PERSON>"}, {"asset": "ode", "full_name": "ODEM"}, {"asset": "odn", "full_name": "Obsidian"}, {"asset": "of", "full_name": "OFCOIN"}, {"asset": "og", "full_name": "OG Fan Token"}, {"asset": "ogn", "full_name": "Origin Protocol"}, {"asset": "ogo", "full_name": "Origo"}, {"asset": "ogv", "full_name": "Origin DeFi Governance"}, {"asset": "ohm", "full_name": "Olympus"}, {"asset": "oin", "full_name": "OIN Finance"}, {"asset": "ok", "full_name": "OKCash"}, {"asset": "okb", "full_name": "OKB"}, {"asset": "<PERSON><PERSON><PERSON>", "full_name": "3X Short OKB Token"}, {"asset": "okbbull", "full_name": "3X Long OKB Token"}, {"asset": "okbhedge", "full_name": "1X Short OKB Token"}, {"asset": "okt", "full_name": "OKT Chain"}, {"asset": "ole", "full_name": "OpenLeverage"}, {"asset": "olt", "full_name": "OneLedger"}, {"asset": "oly", "full_name": "Olyseum"}, {"asset": "om", "full_name": "MANTRA"}, {"asset": "omg", "full_name": "OMG Network"}, {"asset": "omi", "full_name": "ECOMI"}, {"asset": "omni", "full_name": "Omni"}, {"asset": "omnis", "full_name": "OMNIS"}, {"asset": "omr", "full_name": "Omani R<PERSON>"}, {"asset": "onc", "full_name": "One Cash"}, {"asset": "one", "full_name": "Menlo One"}, {"asset": "one_harmony", "full_name": "Harmony"}, {"asset": "ones", "full_name": "OneSwap DAO Token"}, {"asset": "oneusd", "full_name": "1 Dollar"}, {"asset": "ong", "full_name": "SoMee.Social"}, {"asset": "ong_ong", "full_name": "ONG"}, {"asset": "ong_ontologygas", "full_name": "Ontology Gas"}, {"asset": "onion", "full_name": "DeepOnion"}, {"asset": "onit", "full_name": "ONBUFF"}, {"asset": "onl", "full_name": "On.Live"}, {"asset": "onot", "full_name": "ONOToken"}, {"asset": "ons", "full_name": "One Share"}, {"asset": "onston", "full_name": "ONSTON"}, {"asset": "ont", "full_name": "Ontology"}, {"asset": "onx", "full_name": "OnX Finance"}, {"asset": "ooe", "full_name": "OpenOcean"}, {"asset": "ooki", "full_name": "Ooki Protocol"}, {"asset": "oot", "full_name": "Utrum"}, {"asset": "op", "full_name": "Optimism"}, {"asset": "open", "full_name": "Open Platform"}, {"asset": "opium", "full_name": "Opium"}, {"asset": "opnn", "full_name": "Opennity"}, {"asset": "opq", "full_name": "Opacity"}, {"asset": "ops", "full_name": "Octopus Protocol"}, {"asset": "opt", "full_name": "Opus"}, {"asset": "opul", "full_name": "Opulous"}, {"asset": "opx", "full_name": "Opes Protocol"}, {"asset": "orai", "full_name": "<PERSON><PERSON><PERSON><PERSON>"}, {"asset": "orao", "full_name": "ORAO Network"}, {"asset": "orb", "full_name": "OrbCity"}, {"asset": "orbit", "full_name": "Orbit Token"}, {"asset": "orbs", "full_name": "Or<PERSON>"}, {"asset": "orc", "full_name": "Orbit Chain"}, {"asset": "orca", "full_name": "Orca"}, {"asset": "orion", "full_name": "Orion Money"}, {"asset": "orme", "full_name": "<PERSON><PERSON>us <PERSON>"}, {"asset": "orn", "full_name": "Orion"}, {"asset": "ors", "full_name": "ORS Group"}, {"asset": "osa", "full_name": "Optimal Shelf Availability Token"}, {"asset": "osmo", "full_name": "Osmosis"}, {"asset": "ost", "full_name": "OST"}, {"asset": "otn", "full_name": "Open Trading Network"}, {"asset": "otx", "full_name": "Octanox"}, {"asset": "ousd", "full_name": "Origin Dollar"}, {"asset": "ovo", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "ovr", "full_name": "OVR"}, {"asset": "oxen", "full_name": "Oxen"}, {"asset": "oxt", "full_name": "Orchid"}, {"asset": "oxy", "full_name": "Oxygen"}, {"asset": "pab", "full_name": "Panamanian Balboa"}, {"asset": "paf", "full_name": "Pacific"}, {"asset": "pai", "full_name": "Project Pai"}, {"asset": "paid", "full_name": "PAID Network"}, {"asset": "pal", "full_name": "Pal Network"}, {"asset": "pamp", "full_name": "Pamp Network"}, {"asset": "pan", "full_name": "<PERSON><PERSON>"}, {"asset": "panda", "full_name": "<PERSON><PERSON><PERSON><PERSON>"}, {"asset": "pando", "full_name": "Pando"}, {"asset": "par", "full_name": "Parachute"}, {"asset": "para", "full_name": "Paralink Network"}, {"asset": "part", "full_name": "Particl"}, {"asset": "pasc", "full_name": "<PERSON>"}, {"asset": "pass_blockpass", "full_name": "Blockpass"}, {"asset": "pat", "full_name": "<PERSON><PERSON>"}, {"asset": "path", "full_name": "PathDAO"}, {"asset": "paw_paw", "full_name": "PAW"}, {"asset": "paw_pawzone", "full_name": "PAWZONE"}, {"asset": "pax", "full_name": "Paxos Standard"}, {"asset": "paxg", "full_name": "PAX Gold"}, {"asset": "paxgbear", "full_name": "3X Short PAX Gold Token"}, {"asset": "pax<PERSON><PERSON>", "full_name": "3X Long PAX Gold Token"}, {"asset": "pay", "full_name": "TenX"}, {"asset": "<PERSON>azzi", "full_name": "Paparazzi"}, {"asset": "pbr", "full_name": "PolkaBridge"}, {"asset": "pbt", "full_name": "Primalbase Token"}, {"asset": "pbtc35a", "full_name": "pBTC35A"}, {"asset": "pbtt", "full_name": "Purple Butterfly Trading"}, {"asset": "pbx", "full_name": "Paribus"}, {"asset": "pc", "full_name": "Promotion Coin"}, {"asset": "pch", "full_name": "POPCHAIN"}, {"asset": "pcl", "full_name": "Peculium"}, {"asset": "pcnt", "full_name": "Playcent"}, {"asset": "pcx", "full_name": "ChainX"}, {"asset": "pdex", "full_name": "Polkadex"}, {"asset": "pdf", "full_name": "Port of DeFi Network"}, {"asset": "pdt", "full_name": "ParagonsDAO"}, {"asset": "pdx_pdxblockchain", "full_name": "PDX Blockchain"}, {"asset": "peak", "full_name": "PEAKDEFI"}, {"asset": "pearl", "full_name": "Pearl"}, {"asset": "pen", "full_name": "Peruvian Nuevo Sol"}, {"asset": "pendle", "full_name": "<PERSON><PERSON>"}, {"asset": "people", "full_name": "ConstitutionDAO"}, {"asset": "pepecash", "full_name": "<PERSON><PERSON><PERSON> Cash"}, {"asset": "peppa", "full_name": "Peppa Network"}, {"asset": "pera", "full_name": "Pera Finance"}, {"asset": "peri", "full_name": "PERI Finance"}, {"asset": "perl", "full_name": "PERL.eco"}, {"asset": "perp", "full_name": "Perpetual Protocol"}, {"asset": "perx", "full_name": "PeerEx"}, {"asset": "pet", "full_name": "Battle Pets"}, {"asset": "peth", "full_name": "<PERSON>d <PERSON>"}, {"asset": "pets", "full_name": "MicroPets"}, {"asset": "pfe", "full_name": "PPfizer Inc. Tokenized Stock"}, {"asset": "pgk", "full_name": "Papua New Guinean Kina"}, {"asset": "pha", "full_name": "Phala Network"}, {"asset": "phb", "full_name": "Phoenix"}, {"asset": "phi", "full_name": "PHI Token"}, {"asset": "phm", "full_name": "Phantom Protocol"}, {"asset": "phnx", "full_name": "PhoenixDAO"}, {"asset": "php", "full_name": "Philippine Peso"}, {"asset": "phtr", "full_name": "Phuture"}, {"asset": "phx", "full_name": "Phoenix Global"}, {"asset": "pi", "full_name": "Pi"}, {"asset": "pickle", "full_name": "Pickle Finance"}, {"asset": "pig", "full_name": "Pig Finance"}, {"asset": "ping", "full_name": "CryptoPing"}, {"asset": "pink", "full_name": "PinkCoin"}, {"asset": "pinu", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "pist", "full_name": "Pist Trust"}, {"asset": "pitch", "full_name": "Pitch"}, {"asset": "pivx", "full_name": "PIVX"}, {"asset": "pix", "full_name": "Lampix"}, {"asset": "pixel", "full_name": "Pixels"}, {"asset": "pkf", "full_name": "PolkaFoundry"}, {"asset": "pkmon", "full_name": "PolkaMonster"}, {"asset": "pkr", "full_name": "Pakistani Rupee"}, {"asset": "pkt", "full_name": "Playkey"}, {"asset": "pla", "full_name": "PlayDapp (Old)"}, {"asset": "pla_playchip", "full_name": "PlayChip"}, {"asset": "planets", "full_name": "PlanetWatch"}, {"asset": "play", "full_name": "Play Token"}, {"asset": "plbt", "full_name": "Polybius"}, {"asset": "plc", "full_name": "PLATINCOIN"}, {"asset": "plf", "full_name": "PlayFuel"}, {"asset": "pli", "full_name": "Plugin"}, {"asset": "pln", "full_name": "Polish Zloty"}, {"asset": "plr", "full_name": "<PERSON><PERSON>"}, {"asset": "plspad", "full_name": "PulsePad"}, {"asset": "plt", "full_name": "Add.xyz"}, {"asset": "pltc", "full_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"asset": "plu", "full_name": "Pluton"}, {"asset": "plug", "full_name": "PL^Gnet"}, {"asset": "plugcn", "full_name": "Plug Chain"}, {"asset": "ply", "full_name": "PlayCoin"}, {"asset": "pma", "full_name": "PumaPay"}, {"asset": "pmgt", "full_name": "Perth Mint Gold Token"}, {"asset": "pmnt", "full_name": "<PERSON><PERSON>"}, {"asset": "pmon", "full_name": "<PERSON><PERSON>"}, {"asset": "pn", "full_name": "Probably Nothing"}, {"asset": "png", "full_name": "Pangolin"}, {"asset": "pnk", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "pnl", "full_name": "True PNL"}, {"asset": "pnt", "full_name": "Penta"}, {"asset": "pnt_pnetwork", "full_name": "pNetwork"}, {"asset": "poa", "full_name": "POA Network"}, {"asset": "poe", "full_name": "Po.et"}, {"asset": "pok", "full_name": "Pokmonsters"}, {"asset": "pokt", "full_name": "Pocket Network"}, {"asset": "pol", "full_name": "POL (ex-MATIC)", "exchanges": ["kraken"], "markets": ["kraken-pol-usd-spot"]}, {"asset": "polc", "full_name": "Polkacity"}, {"asset": "poli", "full_name": "Polinate"}, {"asset": "polis", "full_name": "Star Atlas DAO"}, {"asset": "polk", "full_name": "Polkamarkets"}, {"asset": "poll", "full_name": "ClearPoll"}, {"asset": "polo", "full_name": "NftyPlay"}, {"asset": "pols", "full_name": "Polkastarter"}, {"asset": "polx", "full_name": "Polylastic"}, {"asset": "poly", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "polybunny", "full_name": "Pancake Bunny Polygon"}, {"asset": "polydoge", "full_name": "PolyDoge"}, {"asset": "polyx", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "pond", "full_name": "<PERSON><PERSON>"}, {"asset": "ponyo", "full_name": "Ponyo-Inu"}, {"asset": "poodl", "full_name": "Poodl Token"}, {"asset": "pool", "full_name": "PoolTogether"}, {"asset": "poolz", "full_name": "Poolz Finance (Old)"}, {"asset": "popk", "full_name": "POPKON"}, {"asset": "por", "full_name": "Portugal National Team Fan Token"}, {"asset": "por_portuma", "full_name": "Portuma"}, {"asset": "pornrocket", "full_name": "PORNROCKET"}, {"asset": "port", "full_name": "Port Finance"}, {"asset": "portal", "full_name": "Portal"}, {"asset": "porto", "full_name": "FC Porto Fan Token"}, {"asset": "post", "full_name": "PostCoin"}, {"asset": "pot", "full_name": "PotCoin"}, {"asset": "potterinu", "full_name": "<PERSON>"}, {"asset": "powr", "full_name": "Powerledger"}, {"asset": "ppay", "full_name": "Plasma Finance"}, {"asset": "ppc", "full_name": "Peercoin"}, {"asset": "ppp", "full_name": "PayPie"}, {"asset": "ppt", "full_name": "Populous"}, {"asset": "pra", "full_name": "<PERSON><PERSON><PERSON><PERSON>"}, {"asset": "prare", "full_name": "POLKARARE"}, {"asset": "pre", "full_name": "Presearch"}, {"asset": "prg", "full_name": "Paragon"}, {"asset": "primal", "full_name": "PRIMAL"}, {"asset": "prime", "full_name": "Echelon Prime"}, {"asset": "prints", "full_name": "FingerprintsDAO"}, {"asset": "prism", "full_name": "Prism"}, {"asset": "privbear", "full_name": "3X Short Privacy Index Token"}, {"asset": "privbull", "full_name": "3X Long Privacy Index Token"}, {"asset": "privhalf", "full_name": "0.5X Long Privacy Index Token"}, {"asset": "privhedge", "full_name": "1X Short Privacy Index Token"}, {"asset": "prmx", "full_name": "PREMA"}, {"asset": "pro", "full_name": "Propy"}, {"asset": "proc", "full_name": "ProCurrency"}, {"asset": "prom", "full_name": "Prom"}, {"asset": "props", "full_name": "Props Token"}, {"asset": "pros", "full_name": "Prosper"}, {"asset": "prq", "full_name": "PARSIQ"}, {"asset": "prt", "full_name": "Portion"}, {"asset": "psg", "full_name": "Paris Saint-Germain Fan Token"}, {"asset": "psi", "full_name": "TridentDAO"}, {"asset": "psl", "full_name": "Pastel"}, {"asset": "psp", "full_name": "ParaSwap"}, {"asset": "pst", "full_name": "Prima<PERSON>"}, {"asset": "pstake", "full_name": "pSTAKE Finance"}, {"asset": "psy", "full_name": "PsyOptions"}, {"asset": "pton", "full_name": "PTON"}, {"asset": "ptoy", "full_name": "Patientory"}, {"asset": "ptt", "full_name": "Proton Token"}, {"asset": "ptu", "full_name": "Pintu <PERSON>"}, {"asset": "pumlx", "full_name": "PUMLx"}, {"asset": "pundix", "full_name": "Pundi X"}, {"asset": "punk", "full_name": "<PERSON> (NFTX)"}, {"asset": "push", "full_name": "Push Protocol"}, {"asset": "put_profileutilitytoken", "full_name": "Profile Utility Token"}, {"asset": "pvt", "full_name": "Pivot Token"}, {"asset": "pwar", "full_name": "<PERSON><PERSON><PERSON><PERSON>"}, {"asset": "pxc_pixiecoin", "full_name": "Pixie Coin"}, {"asset": "pyg", "full_name": "Paraguayan <PERSON>"}, {"asset": "pylnt", "full_name": "Pylon Network"}, {"asset": "pypl", "full_name": "PayPal Holdings Inc Tokenized Stock"}, {"asset": "pyr", "full_name": "Vulcan Forged (PYR)"}, {"asset": "qanx", "full_name": "QANplatform"}, {"asset": "qar", "full_name": "<PERSON><PERSON> R<PERSON>"}, {"asset": "qash", "full_name": "QASH"}, {"asset": "qau", "full_name": "Quantum"}, {"asset": "qbit", "full_name": "Qubitica"}, {"asset": "qbt_qbao", "full_name": "Qbao"}, {"asset": "qc", "full_name": "Qcash"}, {"asset": "qi", "full_name": "BENQI"}, {"asset": "qkc", "full_name": "QuarkChain"}, {"asset": "qlc", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "qnt", "full_name": "Quant"}, {"asset": "qntu", "full_name": "Quanta Utility Token"}, {"asset": "qrdo", "full_name": "<PERSON><PERSON>"}, {"asset": "qrl", "full_name": "Quantum Resistant Ledger"}, {"asset": "qsp", "full_name": "Quantstamp"}, {"asset": "qtcon", "full_name": "Quiztok"}, {"asset": "qtf", "full_name": "Quantfury"}, {"asset": "qtum", "full_name": "Qtum"}, {"asset": "qtum_eth", "full_name": "Qtum on Ethereum"}, {"asset": "qube", "full_name": "Qube"}, {"asset": "quick", "full_name": "QuickSwap (Old)"}, {"asset": "quid", "full_name": "<PERSON><PERSON>"}, {"asset": "qun", "full_name": "<PERSON><PERSON><PERSON><PERSON>"}, {"asset": "qvt", "full_name": "Qvolta"}, {"asset": "qwark", "full_name": "Qwark"}, {"asset": "rabbit", "full_name": "Rabbit Finance"}, {"asset": "raca", "full_name": "RadioCaca"}, {"asset": "rad", "full_name": "Radworks"}, {"asset": "radar", "full_name": "DappRadar"}, {"asset": "rads", "full_name": "Radium"}, {"asset": "rai", "full_name": "Rai Reflex Index"}, {"asset": "rai_eth", "full_name": "Rai Reflex Index on Ethereum"}, {"asset": "raise", "full_name": "Raise <PERSON>"}, {"asset": "ram", "full_name": "Ramifi Protocol"}, {"asset": "ramp", "full_name": "RAMP"}, {"asset": "ranker", "full_name": "RankerDAO"}, {"asset": "rare", "full_name": "SuperRare"}, {"asset": "rari", "full_name": "RARI"}, {"asset": "rating", "full_name": "DPRating"}, {"asset": "ray", "full_name": "Raydium"}, {"asset": "raze", "full_name": "Raze Network"}, {"asset": "razor", "full_name": "Razor Network"}, {"asset": "rbase", "full_name": "rbase.finance"}, {"asset": "rbc", "full_name": "Rubic"}, {"asset": "rblx", "full_name": "Rublix"}, {"asset": "rbn", "full_name": "Ribbon Finance"}, {"asset": "rbt_rimbit", "full_name": "Rimbit"}, {"asset": "rbtc", "full_name": "RSK Smart Bitcoin"}, {"asset": "rccc", "full_name": "Renaissance Culture Centre Chain"}, {"asset": "rckt", "full_name": "Rocket Launchpad"}, {"asset": "rcn_ripiocreditnetwork", "full_name": "Ripio Credit Network"}, {"asset": "rct", "full_name": "RealChain"}, {"asset": "rd", "full_name": "RocketDoge"}, {"asset": "rdd", "full_name": "ReddCoin"}, {"asset": "rdn", "full_name": "Raiden Network Token"}, {"asset": "rdnt", "full_name": "Radiant Capital"}, {"asset": "read", "full_name": "Read"}, {"asset": "real", "full_name": "Realy"}, {"asset": "reap", "full_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"asset": "rebl", "full_name": "REBL"}, {"asset": "red_red", "full_name": "RED"}, {"asset": "red_redcommunitytoken", "full_name": "Red Community Token"}, {"asset": "reef", "full_name": "Reef"}, {"asset": "ref", "full_name": "RefToken"}, {"asset": "rei", "full_name": "REI Network"}, {"asset": "rem", "full_name": "<PERSON><PERSON>"}, {"asset": "ren", "full_name": "Ren"}, {"asset": "rena", "full_name": "Warena"}, {"asset": "renbtc", "full_name": "renBTC"}, {"asset": "rep", "full_name": "<PERSON>ur"}, {"asset": "req", "full_name": "Request"}, {"asset": "ret", "full_name": "RealTract"}, {"asset": "rev", "full_name": "<PERSON><PERSON>"}, {"asset": "rev_eth", "full_name": "Revain on Ethereum"}, {"asset": "rev_rchain", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "revo", "full_name": "Revomon"}, {"asset": "revv", "full_name": "REVV"}, {"asset": "rex", "full_name": "imbrex"}, {"asset": "rfox", "full_name": "RFOX"}, {"asset": "rfr", "full_name": "Refereum"}, {"asset": "rfuel", "full_name": "RioDeFi"}, {"asset": "rgt", "full_name": "Rari Go<PERSON>ken"}, {"asset": "rhoc", "full_name": "<PERSON><PERSON><PERSON> (Old)"}, {"asset": "rice", "full_name": "DAOSquare"}, {"asset": "ridge", "full_name": "Ridge"}, {"asset": "rif", "full_name": "RSK Infrastructure Framework"}, {"asset": "rik", "full_name": "RIK Coin"}, {"asset": "rin", "full_name": "Aldrin"}, {"asset": "ring", "full_name": "RING Financial"}, {"asset": "rio", "full_name": "Realio Network"}, {"asset": "rise", "full_name": "Rise"}, {"asset": "rlc", "full_name": "iExec RLC"}, {"asset": "rly", "full_name": "Rally"}, {"asset": "rmrk", "full_name": "RMRK"}, {"asset": "rnb", "full_name": "Rentible"}, {"asset": "rndr", "full_name": "Render Token"}, {"asset": "rnt", "full_name": "OneRoot Network"}, {"asset": "rntb", "full_name": "BitRent"}, {"asset": "road", "full_name": "ROAD"}, {"asset": "roc", "full_name": "Roxe"}, {"asset": "rom", "full_name": "ROMToken"}, {"asset": "ron", "full_name": "<PERSON><PERSON>"}, {"asset": "roobee", "full_name": "ROOBEE"}, {"asset": "rook", "full_name": "Rook"}, {"asset": "room", "full_name": "OptionRoom"}, {"asset": "rose", "full_name": "Oasis"}, {"asset": "rosn", "full_name": "Roseon Finance"}, {"asset": "route", "full_name": "Router Protocol"}, {"asset": "rox", "full_name": "Robotina"}, {"asset": "rpl", "full_name": "Rocket Pool"}, {"asset": "rpm_renderpayment", "full_name": "Render Payment"}, {"asset": "rpx", "full_name": "Red Pulse"}, {"asset": "rpzx", "full_name": "Rapidz"}, {"asset": "rrb", "full_name": "Renren<PERSON>"}, {"asset": "rrc", "full_name": "R<PERSON>oin"}, {"asset": "rrt", "full_name": "Recovery Right Token"}, {"asset": "rsd", "full_name": "Serbian Dinar"}, {"asset": "rsr", "full_name": "Reserve Rights"}, {"asset": "rss3", "full_name": "RSS3"}, {"asset": "rsv", "full_name": "Reserve"}, {"asset": "rte", "full_name": "Rate3"}, {"asset": "rtf", "full_name": "Regiment Finance"}, {"asset": "rth", "full_name": "Rotharium"}, {"asset": "rub", "full_name": "Russian Ruble"}, {"asset": "rub_rubychain", "full_name": "<PERSON><PERSON><PERSON><PERSON>"}, {"asset": "ruff", "full_name": "<PERSON><PERSON>"}, {"asset": "rune", "full_name": "THORChain"}, {"asset": "rvc", "full_name": "Ravencoin Classic"}, {"asset": "rvn", "full_name": "Ravencoin"}, {"asset": "rvr", "full_name": "RevolutionVR"}, {"asset": "rvst", "full_name": "Revest Finance"}, {"asset": "rvt", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "rwf", "full_name": "Rwandan <PERSON>"}, {"asset": "<PERSON><PERSON>", "full_name": "Ryoshis Vision"}, {"asset": "safe", "full_name": "Safe"}, {"asset": "safemars", "full_name": "Safemars"}, {"asset": "safemoon", "full_name": "SafeMoon"}, {"asset": "sai", "full_name": "<PERSON>"}, {"asset": "sait", "full_name": "SAIT Token"}, {"asset": "saitama", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "saito", "full_name": "Sai<PERSON>"}, {"asset": "sake", "full_name": "SakeToken"}, {"asset": "sal", "full_name": "SalPay"}, {"asset": "salt", "full_name": "SALT"}, {"asset": "samo", "full_name": "Samoyedcoin"}, {"asset": "san", "full_name": "Santiment Network Token"}, {"asset": "sana", "full_name": "Storage Area Network Anywhere"}, {"asset": "sand", "full_name": "The Sandbox"}, {"asset": "sanshu", "full_name": "<PERSON><PERSON>"}, {"asset": "santos", "full_name": "Santos FC Fan Token"}, {"asset": "sao", "full_name": "<PERSON>r"}, {"asset": "sar", "full_name": "Saudi Riyal"}, {"asset": "sashimi", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "sat", "full_name": "Social Activity Token"}, {"asset": "satt", "full_name": "SaTT"}, {"asset": "sbd_solomonislandsdollar", "full_name": "Solomon Islands Dollar"}, {"asset": "sbd_steemdollars", "full_name": "Steem Dollars"}, {"asset": "sbr", "full_name": "<PERSON><PERSON>"}, {"asset": "sbree", "full_name": "CBDAO"}, {"asset": "sbt", "full_name": "SOLBIT"}, {"asset": "sbtc", "full_name": "Super Bitcoin"}, {"asset": "sbtc_eth", "full_name": "sBTC on Ethereum"}, {"asset": "sc", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "scc_stockchain", "full_name": "<PERSON><PERSON><PERSON><PERSON>"}, {"asset": "sch", "full_name": "Schilling-Coin"}, {"asset": "scl", "full_name": "Sociall"}, {"asset": "sclp", "full_name": "<PERSON><PERSON><PERSON> (Old)"}, {"asset": "scr", "full_name": "<PERSON><PERSON>"}, {"asset": "scrt", "full_name": "Secret"}, {"asset": "sd", "full_name": "<PERSON><PERSON>"}, {"asset": "sda", "full_name": "Six Domain Chain"}, {"asset": "sdao", "full_name": "SingularityDAO"}, {"asset": "sdc", "full_name": "SD Coin"}, {"asset": "sdg", "full_name": "Sudanese Pound"}, {"asset": "sdl", "full_name": "Saddle"}, {"asset": "sdn", "full_name": "Shiden Network"}, {"asset": "sdog", "full_name": "Snowdog"}, {"asset": "sdt", "full_name": "Terra SDT"}, {"asset": "seal", "full_name": "Seal Network"}, {"asset": "seco", "full_name": "Serum Ecosystem Token"}, {"asset": "seed", "full_name": "TreeDefi"}, {"asset": "seele", "full_name": "<PERSON><PERSON>"}, {"asset": "seer", "full_name": "SEER"}, {"asset": "sek", "full_name": "Swedish Krona"}, {"asset": "sen", "full_name": "Consensus"}, {"asset": "senate", "full_name": "SENATE"}, {"asset": "senc", "full_name": "Sentinel Chain"}, {"asset": "senso", "full_name": "SENSO"}, {"asset": "sent", "full_name": "Sentinel"}, {"asset": "seos", "full_name": "sEOS"}, {"asset": "seq", "full_name": "Sequence"}, {"asset": "sero", "full_name": "Super Zero Protocol"}, {"asset": "serv", "full_name": "Serve"}, {"asset": "seth", "full_name": "<PERSON><PERSON>"}, {"asset": "sexc", "full_name": "ShareX"}, {"asset": "sfc", "full_name": "SafeCap Token"}, {"asset": "sfg", "full_name": "S.Finance"}, {"asset": "sfi", "full_name": "saffron.finance"}, {"asset": "sfp", "full_name": "SafePal"}, {"asset": "sfund", "full_name": "Seedify.fund"}, {"asset": "sg", "full_name": "SocialGood"}, {"asset": "sga", "full_name": "Saga (sga)"}, {"asset": "sgb", "full_name": "Songbird"}, {"asset": "sgc", "full_name": "Stargram Coin"}, {"asset": "sgcc", "full_name": "Super Game Chain"}, {"asset": "sgd", "full_name": "Singapore Dollar"}, {"asset": "sgn", "full_name": "Signals Network"}, {"asset": "sha", "full_name": "Safe Haven"}, {"asset": "share", "full_name": "Seigniorage Shares"}, {"asset": "she", "full_name": "<PERSON><PERSON><PERSON><PERSON>"}, {"asset": "shft", "full_name": "Shyft Network"}, {"asset": "shi", "full_name": "<PERSON><PERSON>um"}, {"asset": "shib", "full_name": "Shiba Inu"}, {"asset": "shibelon", "full_name": "ShibElon"}, {"asset": "shift", "full_name": "Shift"}, {"asset": "shill", "full_name": "SHILL Token"}, {"asset": "ship", "full_name": "ShipChain"}, {"asset": "shir<PERSON>u", "full_name": "<PERSON><PERSON>"}, {"asset": "shoe", "full_name": "ShoeFy"}, {"asset": "shopx", "full_name": "<PERSON><PERSON><PERSON><PERSON>"}, {"asset": "show", "full_name": "Show"}, {"asset": "shp", "full_name": "St. <PERSON>"}, {"asset": "shping", "full_name": "SHPING"}, {"asset": "shr", "full_name": "ShareToken"}, {"asset": "shx", "full_name": "Stronghold Token"}, {"asset": "sib", "full_name": "SIBCoin"}, {"asset": "sidus", "full_name": "SIDUS"}, {"asset": "sig", "full_name": "Spectiv"}, {"asset": "signa", "full_name": "Signum"}, {"asset": "silk", "full_name": "<PERSON><PERSON><PERSON><PERSON>"}, {"asset": "sis", "full_name": "Symbiosis"}, {"asset": "six", "full_name": "SIX"}, {"asset": "sjcx", "full_name": "Storjcoin X"}, {"asset": "skale", "full_name": "SKALE Network (skale)"}, {"asset": "skeb", "full_name": "S<PERSON>b Coin"}, {"asset": "skey", "full_name": "SmartKey"}, {"asset": "skill", "full_name": "CryptoBlades"}, {"asset": "skin", "full_name": "SkinCoin"}, {"asset": "skl", "full_name": "SKALE"}, {"asset": "skm", "full_name": "Skrumble Network"}, {"asset": "skrt", "full_name": "Sekuritance"}, {"asset": "skt", "full_name": "Sukhavati Network"}, {"asset": "sku", "full_name": "Sakura"}, {"asset": "sky", "full_name": "Skycoin"}, {"asset": "skyrim", "full_name": "Skyrim Finance"}, {"asset": "sla", "full_name": "SUPERLAUNCH"}, {"asset": "slice", "full_name": "Tranche Finance"}, {"asset": "slim", "full_name": "Solanium"}, {"asset": "slink", "full_name": "Soft Link"}, {"asset": "sll", "full_name": "Sierra Leonean Leone"}, {"asset": "slm", "full_name": "Salmon"}, {"asset": "slnd", "full_name": "Save"}, {"asset": "slot", "full_name": "SLOT"}, {"asset": "slp", "full_name": "Smooth Love Potion"}, {"asset": "slp_eth", "full_name": "Smooth Love Potion on Ethereum"}, {"asset": "slr", "full_name": "SolarCoin"}, {"asset": "slrs", "full_name": "Solrise Finance"}, {"asset": "sls", "full_name": "SaluS"}, {"asset": "slt_smartlands", "full_name": "Smartlands"}, {"asset": "slt_sociallendingtoken", "full_name": "Social Lending Token"}, {"asset": "slv", "full_name": "iShares Silver Trust Tokenized Stock"}, {"asset": "slx", "full_name": "Slate"}, {"asset": "smart", "full_name": "SmartCash"}, {"asset": "smartcredit", "full_name": "SmartCredit Token"}, {"asset": "smbswap", "full_name": "Simbcoin Swap"}, {"asset": "smc", "full_name": "SmartCoin"}, {"asset": "smd", "full_name": "SMD COIN"}, {"asset": "smg", "full_name": "Smaugs NFT"}, {"asset": "smoon", "full_name": "SaylorMoon"}, {"asset": "smrat", "full_name": "Secured MoonRat Token"}, {"asset": "sms", "full_name": "Speed Mining Services"}, {"asset": "smt", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "smty", "full_name": "Smoothy"}, {"asset": "snbl", "full_name": "Snowball"}, {"asset": "snc", "full_name": "SunContract"}, {"asset": "snet", "full_name": "Snetwork"}, {"asset": "snft", "full_name": "Spain National Fan Token"}, {"asset": "sngls", "full_name": "SingularDTV"}, {"asset": "snip", "full_name": "SnipCoin"}, {"asset": "snk", "full_name": "Snook"}, {"asset": "snm", "full_name": "SONM"}, {"asset": "snov", "full_name": "Snovio"}, {"asset": "snt", "full_name": "Status"}, {"asset": "sntr", "full_name": "Silent Notary"}, {"asset": "sntvt", "full_name": "Sentivate"}, {"asset": "snx", "full_name": "Synthetix"}, {"asset": "sny", "full_name": "Synthetify"}, {"asset": "soc", "full_name": "All Sports"}, {"asset": "sol", "full_name": "Solana", "experimental": true, "metrics": [{"metric": "BlkHgt", "frequencies": [{"frequency": "1b", "min_time": "2022-04-04T08:20:00.000000000Z", "max_time": "2022-04-04T08:20:01.000000000Z", "min_height": "116119303", "max_height": "116119305", "min_hash": "8XEvUB6gqKyvLATsWwfuNtEpALoMc9cn5LDkfUMAg4tM", "max_hash": "********************************************", "experimental": true}]}, {"metric": "PriceUSD", "frequencies": [{"frequency": "1b", "min_time": "2022-04-04T08:19:59.000000000Z", "max_time": "2022-04-04T08:20:01.000000000Z", "min_height": "116119302", "max_height": "116119305", "min_hash": "9w4UzaPVUVwhLbhR7zAeyNMTDqfEauJzApgicsgkNgB6", "max_hash": "********************************************"}]}, {"metric": "TxCnt", "frequencies": [{"frequency": "1b", "min_time": "2022-04-04T08:20:00.000000000Z", "max_time": "2022-04-04T08:20:01.000000000Z", "min_height": "116119303", "max_height": "116119305", "min_hash": "8XEvUB6gqKyvLATsWwfuNtEpALoMc9cn5LDkfUMAg4tM", "max_hash": "********************************************", "experimental": true}]}], "exchanges": ["ftx"], "markets": ["ftx-SOL-0925-future"]}, {"asset": "solo", "full_name": "Sologenic"}, {"asset": "solr", "full_name": "SolRazr"}, {"asset": "solve", "full_name": "SOLVE"}, {"asset": "son", "full_name": "<PERSON>"}, {"asset": "sonar", "full_name": "SonarWatch"}, {"asset": "sop", "full_name": "SoPay"}, {"asset": "sos", "full_name": "OpenDAO"}, {"asset": "soul", "full_name": "CryptoSoul"}, {"asset": "soul_phantasma", "full_name": "Phantas<PERSON>"}, {"asset": "source", "full_name": "ReSource Protocol"}, {"asset": "sov", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "spa", "full_name": "Sperax"}, {"asset": "sparta", "full_name": "Spartan Protocol"}, {"asset": "spc", "full_name": "SpaceChain"}, {"asset": "spd_spindle", "full_name": "SPINDLE"}, {"asset": "spe", "full_name": "SavePlanetEarth"}, {"asset": "spell", "full_name": "Spell Token"}, {"asset": "spf", "full_name": "SportyCo"}, {"asset": "sphr", "full_name": "Sphere"}, {"asset": "sphri", "full_name": "Spherium"}, {"asset": "sphtx", "full_name": "SophiaTX"}, {"asset": "spice", "full_name": "Spice"}, {"asset": "spin", "full_name": "SPIN Protocol"}, {"asset": "spirit", "full_name": "SpiritSwap"}, {"asset": "spk", "full_name": "Sparks"}, {"asset": "spn", "full_name": "Sapien"}, {"asset": "spnd", "full_name": "Spendcoin"}, {"asset": "spo", "full_name": "Spores Network"}, {"asset": "sps", "full_name": "Splintershards"}, {"asset": "spwn", "full_name": "Bitspawn"}, {"asset": "spy", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "sq", "full_name": "Block Inc Tokenized Stock"}, {"asset": "squid", "full_name": "Squid Inu"}, {"asset": "srd", "full_name": "Surinamese Dollar"}, {"asset": "srk", "full_name": "SparkPoint"}, {"asset": "srm", "full_name": "Serum"}, {"asset": "srn", "full_name": "SIRIN LABS Token"}, {"asset": "srp", "full_name": "Starpunk"}, {"asset": "ssc", "full_name": "SelfSell"}, {"asset": "ssn", "full_name": "Supersonic Finance"}, {"asset": "ssp", "full_name": "South Sudanese Pound"}, {"asset": "sss", "full_name": "Sharechain"}, {"asset": "ssv", "full_name": "ssv.network"}, {"asset": "ssx", "full_name": "SOMESING (Old)"}, {"asset": "sta", "full_name": "STABLE ASSET"}, {"asset": "stac", "full_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"asset": "stacs", "full_name": "STACS"}, {"asset": "stak", "full_name": "STRAKS"}, {"asset": "stake", "full_name": "xDai"}, {"asset": "star", "full_name": "Starbase"}, {"asset": "starl", "full_name": "StarLink"}, {"asset": "stars", "full_name": "Mogul Productions"}, {"asset": "stbu", "full_name": "Stobox <PERSON>"}, {"asset": "stc", "full_name": "Student Coin"}, {"asset": "stc_satoshiisland", "full_name": "Satoshi Island"}, {"asset": "stc_starchain", "full_name": "Starchain"}, {"asset": "stc_starcoin", "full_name": "Starcoin"}, {"asset": "std", "full_name": "Sao Tome & Principe <PERSON> (old)"}, {"asset": "steem", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "step", "full_name": "Step Finance"}, {"asset": "step_step", "full_name": "STEP"}, {"asset": "steth", "full_name": "Lido Staked ETH"}, {"asset": "steth_lido", "full_name": "Staked Ether By <PERSON>"}, {"asset": "stf", "full_name": "Safe Trip Finance"}, {"asset": "stg", "full_name": "Stargate Finance"}, {"asset": "stk", "full_name": "STK"}, {"asset": "stkaave", "full_name": "Staked Aave"}, {"asset": "stluna", "full_name": "Staked LUNA"}, {"asset": "stmx", "full_name": "StormX"}, {"asset": "stn", "full_name": "Sao Tome & Principe Dobra"}, {"asset": "stnd", "full_name": "Standard"}, {"asset": "store", "full_name": "Bit.Store"}, {"asset": "storj", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "storm", "full_name": "Storm"}, {"asset": "stos", "full_name": "Stratos"}, {"asset": "stp", "full_name": "STPAY"}, {"asset": "stpl", "full_name": "Stream Protocol"}, {"asset": "stpt", "full_name": "STP"}, {"asset": "stq", "full_name": "Storiqa"}, {"asset": "str", "full_name": "<PERSON><PERSON>"}, {"asset": "strat", "full_name": "<PERSON><PERSON><PERSON> (strat)"}, {"asset": "strax", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "strk", "full_name": "Stark<PERSON>"}, {"asset": "strm", "full_name": "StreamCoin"}, {"asset": "strong", "full_name": "Strong"}, {"asset": "strp", "full_name": "Strips Finance"}, {"asset": "stsol", "full_name": "Lido Staked SOL"}, {"asset": "stu", "full_name": "bitJob"}, {"asset": "stx", "full_name": "Stacks"}, {"asset": "stx_stox", "full_name": "Stox"}, {"asset": "sub", "full_name": "Substratum"}, {"asset": "sudo", "full_name": "sudoswap"}, {"asset": "suku", "full_name": "SUKU"}, {"asset": "sum", "full_name": "<PERSON><PERSON>"}, {"asset": "sun", "full_name": "Sun Token"}, {"asset": "sunc", "full_name": "<PERSON><PERSON><PERSON><PERSON>"}, {"asset": "sunny", "full_name": "<PERSON> Aggregator"}, {"asset": "sup", "full_name": "SUP"}, {"asset": "super", "full_name": "SuperVerse"}, {"asset": "superbid", "full_name": "Superbid"}, {"asset": "sur", "full_name": "Suretly"}, {"asset": "surfmoon", "full_name": "SurfMoon"}, {"asset": "susd", "full_name": "sUSD"}, {"asset": "sushi", "full_name": "SushiSwap", "exchanges": ["sushiswap_v1_eth"], "markets": ["sushiswap_v1_eth-sushi-usdt_eth-spot"]}, {"asset": "sushibear", "full_name": "3X Short Sushi Token"}, {"asset": "<PERSON><PERSON><PERSON>", "full_name": "3X Long Sushi Token"}, {"asset": "sushiup", "full_name": "SUSHIUP"}, {"asset": "suter", "full_name": "suter<PERSON>u"}, {"asset": "svc", "full_name": "Salvadoran <PERSON>"}, {"asset": "svd", "full_name": "savedroid"}, {"asset": "swap", "full_name": "TrustSwap"}, {"asset": "swash", "full_name": "Swash"}, {"asset": "sweat", "full_name": "Sweat Economy"}, {"asset": "sweet", "full_name": "<PERSON>"}, {"asset": "swftc", "full_name": "SwftCoin"}, {"asset": "swingby", "full_name": "<PERSON><PERSON>"}, {"asset": "swise", "full_name": "StakeWise"}, {"asset": "swm", "full_name": "Swarm (swm)"}, {"asset": "swop", "full_name": "Swop"}, {"asset": "swp", "full_name": "<PERSON><PERSON>"}, {"asset": "swrv", "full_name": "Swerve"}, {"asset": "swt", "full_name": "Swarm City"}, {"asset": "swth", "full_name": "Switcheo"}, {"asset": "sxp", "full_name": "Solar"}, {"asset": "sxpbear", "full_name": "3X Short Swipe Token"}, {"asset": "sxpbull", "full_name": "3X Long Swipe Token"}, {"asset": "sxpdown", "full_name": "SXPDOWN"}, {"asset": "sxphalf", "full_name": "0.5X Long Swipe Token"}, {"asset": "sxphedge", "full_name": "1X Short Swipe Token"}, {"asset": "sxpup", "full_name": "SXPUP"}, {"asset": "sxut", "full_name": "Spectre.ai Utility Token"}, {"asset": "sybc", "full_name": "SYB Coin"}, {"asset": "sylo", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "syn", "full_name": "Synapse"}, {"asset": "synx", "full_name": "Syndicate"}, {"asset": "syp", "full_name": "Syrian Pound"}, {"asset": "sys", "full_name": "Syscoin"}, {"asset": "szl", "full_name": "Swazi Lilangeni"}, {"asset": "t", "full_name": "<PERSON><PERSON><PERSON><PERSON>"}, {"asset": "taas", "full_name": "TaaS"}, {"asset": "taboo", "full_name": "TABOO TOKEN"}, {"asset": "tac", "full_name": "Traceability Chain"}, {"asset": "talk", "full_name": "<PERSON><PERSON>"}, {"asset": "tape", "full_name": "ToolApe"}, {"asset": "tara", "full_name": "Taraxa"}, {"asset": "taste", "full_name": "TasteNFT"}, {"asset": "tau", "full_name": "Lamden"}, {"asset": "taud_eth", "full_name": "TrustToken True Australian Dollar on Ethereum"}, {"asset": "taur", "full_name": "Marnot<PERSON><PERSON>"}, {"asset": "tbe", "full_name": "TrustBase"}, {"asset": "tbtc", "full_name": "tBTC"}, {"asset": "tcad_eth", "full_name": "TrustToken True Canadian Dollar on Ethereum"}, {"asset": "tcg2", "full_name": "TCGCoin 2.0"}, {"asset": "tcn", "full_name": "TCOIN"}, {"asset": "tcp", "full_name": "The Crypto Prophecies"}, {"asset": "tcr", "full_name": "TecraCoin"}, {"asset": "tct", "full_name": "TokenClub"}, {"asset": "tdp", "full_name": "TrueDeck"}, {"asset": "tds", "full_name": "TokenDesk"}, {"asset": "tea", "full_name": "Tea Token"}, {"asset": "team", "full_name": "TEAM (TokenStars)"}, {"asset": "teer", "full_name": "Integritee"}, {"asset": "tel", "full_name": "Telcoin"}, {"asset": "tem_temtum", "full_name": "Temtum"}, {"asset": "temco", "full_name": "TEMCO"}, {"asset": "ten", "full_name": "Tokenomy"}, {"asset": "tend", "full_name": "Tendies"}, {"asset": "tent", "full_name": "TENT"}, {"asset": "tep", "full_name": "Te<PERSON>ton"}, {"asset": "texo", "full_name": "tEXO"}, {"asset": "tfc", "full_name": "TheFutbolCoin"}, {"asset": "tfd", "full_name": "TE-FOOD (old)"}, {"asset": "tfl", "full_name": "TrueFlip"}, {"asset": "tfuel", "full_name": "Theta Fuel"}, {"asset": "tgbp_eth", "full_name": "TrustToken True Great Britain Pound on Ethereum"}, {"asset": "tgc", "full_name": "Tigercoin"}, {"asset": "tgt", "full_name": "Target Coin"}, {"asset": "thb", "full_name": "Thai Baht"}, {"asset": "thc", "full_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"asset": "theos", "full_name": "<PERSON><PERSON>"}, {"asset": "theta", "full_name": "Theta Network"}, {"asset": "thetabear", "full_name": "3X Short Theta Network Token"}, {"asset": "the<PERSON><PERSON>", "full_name": "3X Long Theta Network Token"}, {"asset": "thetahalf", "full_name": "0.5X Long Theta Network Token"}, {"asset": "thetahedge", "full_name": "1X Short Theta Network Token"}, {"asset": "thg", "full_name": "Thetan World"}, {"asset": "thkd_eth", "full_name": "TrustToken True Hong Kong Dollar on Ethereum"}, {"asset": "thn", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "thr", "full_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"asset": "thrt", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "tidal", "full_name": "Tidal Finance"}, {"asset": "time", "full_name": "Chrono.tech"}, {"asset": "time_wonderlandtime", "full_name": "Wonderland Time"}, {"asset": "tio", "full_name": "Trade Token"}, {"asset": "tips", "full_name": "FedoraCoin"}, {"asset": "tita", "full_name": "Titan Hunters"}, {"asset": "titan", "full_name": "BitUnits Titan"}, {"asset": "tiv", "full_name": "TI-Value"}, {"asset": "tix", "full_name": "Blocktix"}, {"asset": "tjs", "full_name": "<PERSON>i Somoni"}, {"asset": "tka", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "tkn", "full_name": "TokenCard"}, {"asset": "tko", "full_name": "<PERSON><PERSON>"}, {"asset": "tkr", "full_name": "CryptoInsight"}, {"asset": "tks", "full_name": "<PERSON><PERSON>"}, {"asset": "tkt", "full_name": "Twinkle"}, {"asset": "tkx", "full_name": "Token TKX"}, {"asset": "tky", "full_name": "THEKEY"}, {"asset": "tlibra", "full_name": "Testnet Libra"}, {"asset": "tlm", "full_name": "Alien Worlds"}, {"asset": "tlos", "full_name": "Telos"}, {"asset": "tmt", "full_name": "Turkmenistani Manat"}, {"asset": "tmtg", "full_name": "The Midas Touch Gold"}, {"asset": "tnb", "full_name": "Time New Bank"}, {"asset": "tnc", "full_name": "Trinity Network Credit"}, {"asset": "tnc_tnccoin", "full_name": "TNC Coin"}, {"asset": "tnd", "full_name": "Tunisian Dinar"}, {"asset": "tns", "full_name": "Transcodium"}, {"asset": "tnt", "full_name": "Tierion"}, {"asset": "tokau", "full_name": "Tokyo AU"}, {"asset": "toke", "full_name": "Tokemak"}, {"asset": "toko", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "toll", "full_name": "Bridge Protocol"}, {"asset": "tomo", "full_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"asset": "tomobear", "full_name": "3X Short <PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"asset": "<PERSON><PERSON><PERSON>", "full_name": "3X Long TomoChain <PERSON>"}, {"asset": "<PERSON><PERSON><PERSON><PERSON>", "full_name": "0.5X Long TomoChain <PERSON>"}, {"asset": "tomohedge", "full_name": "1X Short Tom<PERSON><PERSON><PERSON>n <PERSON>"}, {"asset": "ton", "full_name": "<PERSON><PERSON>in"}, {"asset": "ton_tokamaknetwork", "full_name": "Tokamak Network"}, {"asset": "ton_tontoken", "full_name": "TON Token"}, {"asset": "tone", "full_name": "TE-FOOD"}, {"asset": "top", "full_name": "Tongan <PERSON>"}, {"asset": "topc", "full_name": "TopChain"}, {"asset": "torn", "full_name": "Tornado Cash"}, {"asset": "tos", "full_name": "ThingsOperatingSystem"}, {"asset": "totm", "full_name": "TotemFi"}, {"asset": "totoro", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "tower", "full_name": "TOWER"}, {"asset": "town", "full_name": "Town Star"}, {"asset": "tpay", "full_name": "TokenPay"}, {"asset": "tpos", "full_name": "The Philosophers Stone"}, {"asset": "tpt", "full_name": "TokenPocket"}, {"asset": "tra", "full_name": "Trabzonspor Fan Token"}, {"asset": "trac", "full_name": "OriginTrail"}, {"asset": "trad", "full_name": "Tradcoin"}, {"asset": "trade", "full_name": "Polytrade"}, {"asset": "trade_unitrade", "full_name": "Unitrade"}, {"asset": "trak", "full_name": "TrakInvest"}, {"asset": "trb", "full_name": "<PERSON><PERSON>"}, {"asset": "tree", "full_name": "Tree Defi"}, {"asset": "trees", "full_name": "SAFETREES"}, {"asset": "trg", "full_name": "The Rug Game"}, {"asset": "trias", "full_name": "<PERSON><PERSON>"}, {"asset": "tribe", "full_name": "Tribe"}, {"asset": "tribl", "full_name": "Tribal Finance"}, {"asset": "trig", "full_name": "Triggers"}, {"asset": "trio", "full_name": "Tripio"}, {"asset": "trix", "full_name": "TriumphX"}, {"asset": "troy", "full_name": "TROY"}, {"asset": "trr", "full_name": "Terran Coin"}, {"asset": "trst", "full_name": "WeTrust"}, {"asset": "trtl", "full_name": "<PERSON><PERSON><PERSON><PERSON>"}, {"asset": "tru", "full_name": "TrueFi"}, {"asset": "true", "full_name": "<PERSON><PERSON><PERSON><PERSON>"}, {"asset": "trumplose", "full_name": "<PERSON>"}, {"asset": "trumpwin", "full_name": "Trump Wins Token"}, {"asset": "trv", "full_name": "TrustVerse"}, {"asset": "trvl", "full_name": "TRVL"}, {"asset": "trx", "full_name": "TRON", "exchanges": ["bybit", "huobi"], "markets": ["bybit-TRXUSDT-future", "bybit-trx-usdt-spot", "huobi-TRX200925_CQ-future"]}, {"asset": "trx3l", "full_name": "TRON 3x Leveraged Long Token"}, {"asset": "trx3s", "full_name": "TRON 3x Leveraged Short Token"}, {"asset": "trx_eth", "full_name": "Tron on Ethereum"}, {"asset": "trxbear", "full_name": "3X Short TRX Token"}, {"asset": "trxbull", "full_name": "3X Long TRX Token"}, {"asset": "trxdown", "full_name": "TRXDOWN"}, {"asset": "trxhalf", "full_name": "0.5X Long TRX Token"}, {"asset": "trxhedge", "full_name": "1X Short TRX Token"}, {"asset": "trxup", "full_name": "TRXUP"}, {"asset": "try", "full_name": "Turkish Lira"}, {"asset": "tryb", "full_name": "BiLira"}, {"asset": "<PERSON><PERSON>ar", "full_name": "3X Short BiLira Token"}, {"asset": "<PERSON><PERSON><PERSON>", "full_name": "3X Long BiLira Token"}, {"asset": "<PERSON><PERSON><PERSON>", "full_name": "0.5X Long BiLira Token"}, {"asset": "trybhedge", "full_name": "1X Short BiLira Token"}, {"asset": "tshp", "full_name": "12Ships"}, {"asset": "tsl", "full_name": "Energo"}, {"asset": "tsla", "full_name": "Tesla Tokenized Stock"}, {"asset": "tsm", "full_name": "Taiwan Semiconductor Mfg. Co. Ltd. Tokenized Stock"}, {"asset": "tsr", "full_name": "Te<PERSON><PERSON>"}, {"asset": "tsuka", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "tt", "full_name": "ThunderCore"}, {"asset": "ttc_ttcprotocol", "full_name": "TTC Protocol"}, {"asset": "ttd", "full_name": "Trinidad & Tobago Dollar"}, {"asset": "ttk", "full_name": "The Three Kingdoms"}, {"asset": "ttt", "full_name": "TrustNote"}, {"asset": "ttu", "full_name": "TaTaTu"}, {"asset": "tube", "full_name": "BitTube"}, {"asset": "tuda", "full_name": "Tutor's Diary"}, {"asset": "tulip", "full_name": "Tulip Protocol"}, {"asset": "tup", "full_name": "TenUp"}, {"asset": "tusd", "full_name": "TrueUSD"}, {"asset": "tusd_2_eth", "full_name": "TrueUSD v2 on Ethereum", "exchanges": ["curve_eth"], "markets": ["curve_eth-1-tusd_2_eth-dai-spot"]}, {"asset": "tv", "full_name": "Tokenville"}, {"asset": "tvk", "full_name": "Virtua"}, {"asset": "twd", "full_name": "New Taiwan Dollar"}, {"asset": "twt", "full_name": "Trust Wallet Token"}, {"asset": "twtr", "full_name": "Twitter Tokenized Stock"}, {"asset": "tx", "full_name": "TransferCoin"}, {"asset": "txt", "full_name": "Taxa Token"}, {"asset": "tyc", "full_name": "Tycoon"}, {"asset": "tzki", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "tzs", "full_name": "Tanzanian <PERSON>"}, {"asset": "uah", "full_name": "Ukrainian Hryvnia"}, {"asset": "uber", "full_name": "Uber Technologies Inc Tokenized Stock"}, {"asset": "ubex", "full_name": "Ubex"}, {"asset": "ubq", "full_name": "Ubiq"}, {"asset": "ubt", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "ubtc", "full_name": "United Bitcoin"}, {"asset": "ubxt", "full_name": "UpBots"}, {"asset": "uc", "full_name": "YouLive Coin"}, {"asset": "uca", "full_name": "UCA Coin"}, {"asset": "uct", "full_name": "Ubique Chain Of Things"}, {"asset": "udo", "full_name": "Unus Dao"}, {"asset": "udoo", "full_name": "Hyprr (Howdoo)"}, {"asset": "udt", "full_name": "Unlock Protocol"}, {"asset": "uet", "full_name": "Useless Ethereum Token"}, {"asset": "ufc", "full_name": "Union Fair Coin"}, {"asset": "ufo", "full_name": "UFO Gaming"}, {"asset": "ufr", "full_name": "Upfiring"}, {"asset": "uft", "full_name": "UniLend"}, {"asset": "ugas", "full_name": "UGAS"}, {"asset": "ugc", "full_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"asset": "ugx", "full_name": "Ugandan <PERSON>"}, {"asset": "uip", "full_name": "UnlimitedIP"}, {"asset": "ukg", "full_name": "Un<PERSON>in <PERSON>"}, {"asset": "ulti", "full_name": "Ultiverse"}, {"asset": "ultra", "full_name": "UltraSafe"}, {"asset": "ulu", "full_name": "Universal Liquidity Union"}, {"asset": "uma", "full_name": "UMA"}, {"asset": "umb", "full_name": "Umbrella Network"}, {"asset": "umee", "full_name": "<PERSON><PERSON>"}, {"asset": "umi", "full_name": "UMI"}, {"asset": "umt", "full_name": "Universal Mobile Token"}, {"asset": "umx", "full_name": "UniMex Network"}, {"asset": "unb", "full_name": "Unbound"}, {"asset": "unc", "full_name": "Unigame"}, {"asset": "uncx", "full_name": "UniCrypt"}, {"asset": "unfi", "full_name": "Unifi Protocol DAO"}, {"asset": "uni", "full_name": "Uniswap"}, {"asset": "unic", "full_name": "Unicly"}, {"asset": "unidown", "full_name": "UNIDOWN"}, {"asset": "unistake", "full_name": "Unistake"}, {"asset": "unit", "full_name": "Universal Currency"}, {"asset": "uniup", "full_name": "UNIUP"}, {"asset": "unn", "full_name": "UNION Protocol Governance Token"}, {"asset": "uno", "full_name": "Unobtanium"}, {"asset": "uop", "full_name": "Utopia Genesis Foundation"}, {"asset": "uos", "full_name": "Ultra"}, {"asset": "up", "full_name": "UpToken"}, {"asset": "upeur", "full_name": "Universal Euro"}, {"asset": "upi", "full_name": "Pawtocol"}, {"asset": "upp", "full_name": "Sentinel Protocol"}, {"asset": "ups", "full_name": "UPFI Network"}, {"asset": "upt", "full_name": "Universal Protocol Token"}, {"asset": "upusd", "full_name": "Universal US Dollar"}, {"asset": "upxau", "full_name": "Universal Gold"}, {"asset": "uqc", "full_name": "Uquid Coin"}, {"asset": "urac", "full_name": "Uranus"}, {"asset": "urqa", "full_name": "UREEQA"}, {"asset": "usc", "full_name": "Coinfix"}, {"asset": "usd", "full_name": "US Dollar", "exchanges": ["binance", "binance.us", "bitmex", "bittrex", "bybit", "cme", "coinbase", "deribit", "ftx", "huobi", "kraken"], "markets": ["binance-XBTUSD-future", "binance.us-btc-usd-spot", "bitmex-FILUSD-future", "bitmex-XBTUSD-future", "bittrex-btc-usd-spot", "bittrex-eth-usd-spot", "bittrex-xrp-usd-spot", "bybit-ETH-30APR23-2010-C-option", "bybit-ETH-30APR23-2010-P-option", "bybit-ETH-31MAY23-2020-P-option", "bybit-btc-usd-spot", "bybit-eth-usd-spot", "cme-BTCQ1-future", "coinbase-btc-usd-spot", "coinbase-eth-usd-spot", "deribit-BTC-10NOV23-future", "deribit-BTC-15OCT21-60000-C-option", "deribit-BTC-17NOV23-future", "deribit-BTC-1OCT21-75000-C-option", "deribit-BTC-24NOV23-future", "deribit-BTC-26NOV21-60000-C-option", "deribit-BTC-29MAR21-54000-C-option", "deribit-BTC-8OCT21-50000-C-option", "deribit-BTC-9APR21-50000-P-option", "deribit-BTC-9DEC24-102000-C-option", "deribit-ETH-1OCT21-2850-P-option", "deribit-ETH-1OCT21-3200-P-option", "deribit-ETH-24JUN22-1000-C-option", "deribit-ETH-28JAN24-future", "deribit-ETH-29OCT21-2000-P-option", "deribit-ETH-2APR21-1960-C-option", "deribit-ETH-2OCT21-3250-P-option", "deribit-ETH-30APR23-2010-C-option", "deribit-ETH-30APR23-2010-P-option", "deribit-ETH-30MAR21-1440-P-option", "deribit-ETH-30MAR21-1700-P-option", "deribit-ETH-30MAR21-1720-C-option", "deribit-ETH-30MAR21-1760-P-option", "deribit-ETH-30MAR21-1920-C-option", "deribit-ETH-31DEC21-4000-C-option", "deribit-ETH-31JAN25-4400-P-option", "deribit-ETH-31MAY23-2020-P-option", "deribit-ETH-PERPETUAL-future", "deribit-XRP-31JAN24-future", "deribit-XRP-31SEP23-2023-P-option", "ftx-SOL-0925-future", "huobi-BCH200731_CW-future", "huobi-BCH201225_NQ-future", "huobi-BSV201225_NQ-future", "huobi-BTC-9APR21-50000-P4-option", "huobi-BTC201225_NQ-future", "huobi-ETC200925_CQ-future", "huobi-ETH-30APR23-2010-C-option", "huobi-ETH-30APR23-2010-P-option", "huobi-ETH-31MAY23-2020-P-option", "huobi-ETH200807_NW-future", "huobi-ETH200925_CQ-future", "huobi-LINK200925_CQ-future", "huobi-LTC200807_NW-future", "huobi-TRX200925_CQ-future", "huobi-XBTUSD4-future", "huobi-XRP-31AUG23-2023-P-option", "kraken-pol-usd-spot"]}, {"asset": "usdc", "full_name": "USDC", "exchanges": ["deribit", "uniswap_v2_eth", "uniswap_v3_eth"], "markets": ["deribit-XRP_USDC-9DEC24-2d6-P-option", "deribit-btc-usdc-spot", "deribit-eth-usdc-spot", "uniswap_v2_eth-aave-usdc-spot", "uniswap_v2_eth-usdc-weth-spot", "uniswap_v3_eth-agg-usdc-weth-spot"], "atlas": true}, {"asset": "usdc_base.eth", "full_name": "USDC on Base", "exchanges": ["uniswap_v3_base"], "markets": ["uniswap_v3_base-1-usdc_base.eth-cbbtc_base.eth-spot"]}, {"asset": "usdd", "full_name": "USDD"}, {"asset": "usdd_eth", "full_name": "USDD on Ethereum"}, {"asset": "usdg", "full_name": "USDG"}, {"asset": "usdk", "full_name": "USDK"}, {"asset": "usdn", "full_name": "Neutrino USD"}, {"asset": "usdn_eth", "full_name": "Neutrino USD on Ethereum"}, {"asset": "usdp", "full_name": "Pax Dollar"}, {"asset": "usds_stableusd", "full_name": "StableUSD"}, {"asset": "usdsb", "full_name": "StableUSD BEP2"}, {"asset": "usdt", "full_name": "<PERSON><PERSON>", "exchanges": ["binance", "bybit"], "markets": ["binance-BTCUSDT-future", "binance-btc-usdt-spot", "binance-undef314159-usdt-spot", "bybit-TRXUSDT-future", "bybit-trx-usdt-spot"]}, {"asset": "usdt_eth", "full_name": "Tether on Ethereum", "exchanges": ["sushiswap_v1_eth"], "markets": ["sushiswap_v1_eth-sushi-usdt_eth-spot"]}, {"asset": "usdt_omni", "full_name": "<PERSON><PERSON>"}, {"asset": "usdt_trx", "full_name": "<PERSON>ther on TRON"}, {"asset": "usdtbear", "full_name": "3X Short Tether <PERSON>ken"}, {"asset": "usdtbull", "full_name": "3X Long Tether Token"}, {"asset": "use", "full_name": "Usechain Token"}, {"asset": "usf", "full_name": "Unslashed Finance"}, {"asset": "usg", "full_name": "USGold"}, {"asset": "usnbt", "full_name": "NuBits"}, {"asset": "ust", "full_name": "TerraClassicUSD", "experimental": true}, {"asset": "ut", "full_name": "<PERSON><PERSON>"}, {"asset": "utk", "full_name": "xMoney"}, {"asset": "utnp", "full_name": "Universa"}, {"asset": "utt", "full_name": "United Traders Token"}, {"asset": "uuu", "full_name": "U Network"}, {"asset": "uyu", "full_name": "Uruguayan Peso"}, {"asset": "uzs", "full_name": "Uzbekistani Som"}, {"asset": "<PERSON><PERSON><PERSON><PERSON>", "full_name": "<PERSON><PERSON><PERSON><PERSON>"}, {"asset": "v", "full_name": "Version"}, {"asset": "vai", "full_name": "VAIOT"}, {"asset": "val", "full_name": "Validity"}, {"asset": "valor", "full_name": "Valor <PERSON>"}, {"asset": "value", "full_name": "Value Liquidity"}, {"asset": "<PERSON><PERSON>", "full_name": "Vanar Chain", "exchanges": ["hitbtc"], "markets": ["hitbtc-vanry-btc-spot"]}, {"asset": "vany", "full_name": "Vanywhere"}, {"asset": "vbk", "full_name": "VeriBlock"}, {"asset": "vbt", "full_name": "VBT"}, {"asset": "vck", "full_name": "28VCK"}, {"asset": "vct", "full_name": "ValueCyberToken"}, {"asset": "vdg", "full_name": "VeriDocGlobal"}, {"asset": "vdr", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "vdx", "full_name": "Vodi X"}, {"asset": "vee", "full_name": "BLOCKv"}, {"asset": "vee_veefinance", "full_name": "Vee Finance"}, {"asset": "vef", "full_name": "Venezuelan Bolivar (before August 20, 2018)"}, {"asset": "vega", "full_name": "Vega Protocol"}, {"asset": "velo", "full_name": "<PERSON><PERSON>"}, {"asset": "vemp", "full_name": "VEMP"}, {"asset": "ven", "full_name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (ERC20 era)"}, {"asset": "vena", "full_name": "Vena Network"}, {"asset": "vent", "full_name": "Vent Finance"}, {"asset": "veo", "full_name": "Amoveo"}, {"asset": "vera", "full_name": "<PERSON>"}, {"asset": "veri", "full_name": "Veritaseum"}, {"asset": "ves", "full_name": "Venezuelan Bolivar"}, {"asset": "vet", "full_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"asset": "vet_eth", "full_name": "<PERSON><PERSON><PERSON><PERSON><PERSON> on Ethereum"}, {"asset": "vetbear", "full_name": "3X Short VeChain <PERSON>"}, {"asset": "vetbull", "full_name": "3X Long VeChain <PERSON>"}, {"asset": "vethedge", "full_name": "1X Short VeChain <PERSON>"}, {"asset": "vgx", "full_name": "VGX Token"}, {"asset": "vi", "full_name": "Vid"}, {"asset": "via", "full_name": "Viacoin"}, {"asset": "vib", "full_name": "Viberate"}, {"asset": "vibe", "full_name": "VIBE"}, {"asset": "vid", "full_name": "Vivid Labs"}, {"asset": "vidt", "full_name": "VIDT DAO"}, {"asset": "vidy", "full_name": "VIDY"}, {"asset": "vidyx", "full_name": "VidyX"}, {"asset": "vikings", "full_name": "Vikings Inu"}, {"asset": "vinu", "full_name": "Vita Inu"}, {"asset": "vio", "full_name": "Vio"}, {"asset": "vit", "full_name": "Vice Industry Token"}, {"asset": "vitae", "full_name": "<PERSON><PERSON>"}, {"asset": "vite", "full_name": "VITE"}, {"asset": "viu", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "vld", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "vlx", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "vlxpad", "full_name": "VelasPad"}, {"asset": "vme", "full_name": "VeriME"}, {"asset": "vnd", "full_name": "Vietnamese Dong"}, {"asset": "vnt_vntchain", "full_name": "V<PERSON><PERSON><PERSON>n"}, {"asset": "vnx", "full_name": "VisionX"}, {"asset": "voco", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "voise", "full_name": "<PERSON><PERSON>"}, {"asset": "vollar", "full_name": "V-Dimension"}, {"asset": "volt", "full_name": "Volt Inu"}, {"asset": "vow", "full_name": "Vow"}, {"asset": "voxel", "full_name": "Voxies"}, {"asset": "vr", "full_name": "Victoria VR"}, {"asset": "vra", "full_name": "Verasity"}, {"asset": "vrc", "full_name": "<PERSON>eri<PERSON><PERSON><PERSON>"}, {"asset": "vrm", "full_name": "VeriumReserve"}, {"asset": "vrt", "full_name": "<PERSON> <PERSON>"}, {"asset": "vrx", "full_name": "VEROX"}, {"asset": "vso", "full_name": "Verso"}, {"asset": "vsp", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "vsys", "full_name": "v.systems"}, {"asset": "vtc", "full_name": "Vertcoin"}, {"asset": "vtho", "full_name": "<PERSON><PERSON><PERSON><PERSON>"}, {"asset": "vuu", "full_name": "<PERSON><PERSON><PERSON><PERSON>"}, {"asset": "vuv", "full_name": "Vanuatu Vatu"}, {"asset": "vvt", "full_name": "VersoView"}, {"asset": "vxv", "full_name": "Vectorspace AI"}, {"asset": "vzt", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "wabi", "full_name": "WaBi"}, {"asset": "wag", "full_name": "Waggle Network"}, {"asset": "wampl", "full_name": "Warpped Ampleforth"}, {"asset": "wan", "full_name": "Wanchain"}, {"asset": "wana", "full_name": "Wanaka Farm"}, {"asset": "war", "full_name": "NFT Wars"}, {"asset": "waves", "full_name": "Waves"}, {"asset": "waxe", "full_name": "WAXE"}, {"asset": "waxl", "full_name": "Wrapped <PERSON><PERSON>"}, {"asset": "waxp", "full_name": "WAX"}, {"asset": "wbtc", "full_name": "Wrapped Bitcoin"}, {"asset": "wcelo", "full_name": "Wrapped Celo"}, {"asset": "wcfg", "full_name": "Wrapped Centrifuge"}, {"asset": "wcusd", "full_name": "Wrapped Celo USD"}, {"asset": "well", "full_name": "<PERSON><PERSON>"}, {"asset": "wemix", "full_name": "WEMIX"}, {"asset": "west", "full_name": "Waves Enterprise"}, {"asset": "wet", "full_name": "WeShow Token"}, {"asset": "weth", "full_name": "Wrapped Ether", "exchanges": ["uniswap_v2_eth", "uniswap_v3_eth"], "markets": ["uniswap_v2_eth-usdc-weth-spot", "uniswap_v3_eth-2-link-weth-spot", "uniswap_v3_eth-3-glm-weth-spot", "uniswap_v3_eth-agg-usdc-weth-spot"]}, {"asset": "wex", "full_name": "WaultSwap"}, {"asset": "weyu", "full_name": "WEYU"}, {"asset": "wfil", "full_name": "Wrapped Filecoin"}, {"asset": "wflow", "full_name": "Wrapped Flow"}, {"asset": "wgmi", "full_name": "WGMI"}, {"asset": "wgp", "full_name": "W Green Pay"}, {"asset": "wgrt", "full_name": "WaykiChain Governance Coin"}, {"asset": "whale", "full_name": "WHALE"}, {"asset": "white", "full_name": "<PERSON>heart"}, {"asset": "wib", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "wicc", "full_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"asset": "wiken", "full_name": "Project WITH"}, {"asset": "wiki", "full_name": "Wiki Token"}, {"asset": "wild", "full_name": "<PERSON>"}, {"asset": "win", "full_name": "Winstex"}, {"asset": "win_wcoin", "full_name": "WCOIN"}, {"asset": "win_wink", "full_name": "WINkLink"}, {"asset": "wing", "full_name": "Wing Finance"}, {"asset": "wings", "full_name": "Wings"}, {"asset": "winry", "full_name": "<PERSON><PERSON>"}, {"asset": "wis", "full_name": "Experty Wisdom Token"}, {"asset": "wiz", "full_name": "CrowdWizToken"}, {"asset": "wlk", "full_name": "Wolk"}, {"asset": "wlkn", "full_name": "<PERSON><PERSON>"}, {"asset": "wlo", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "wluna", "full_name": "Wrapped LUNA Token"}, {"asset": "wncg", "full_name": "Wrapped NCG"}, {"asset": "wnd", "full_name": "WonderHero"}, {"asset": "wndr", "full_name": "Wonder<PERSON>i <PERSON> Stock"}, {"asset": "wnxm", "full_name": "Wrapped NXM"}, {"asset": "wolfgirl", "full_name": "Wolf Girl"}, {"asset": "wolverinu", "full_name": "WOLVERINU"}, {"asset": "wom", "full_name": "WOM Protocol"}, {"asset": "woo", "full_name": "WOO"}, {"asset": "woof", "full_name": "WoofWork.io"}, {"asset": "wool", "full_name": "Wolf Game Wool"}, {"asset": "woop", "full_name": "Woonkly Power"}, {"asset": "wozx", "full_name": "EFFORCE"}, {"asset": "wpr", "full_name": "<PERSON><PERSON><PERSON><PERSON>"}, {"asset": "wrc", "full_name": "Worldcore"}, {"asset": "wrx", "full_name": "WazirX"}, {"asset": "wsg", "full_name": "Wall Street Games"}, {"asset": "w<PERSON><PERSON>", "full_name": "Sienna [ERC-20]"}, {"asset": "wst", "full_name": "Samoan <PERSON>"}, {"asset": "wsteth", "full_name": "Wrapped liquid staked Ether 2.0"}, {"asset": "wtc", "full_name": "Waltonchain"}, {"asset": "wust", "full_name": "Wrapped TerraUSD (ERC-20)"}, {"asset": "wwb", "full_name": "Wowbit"}, {"asset": "wwy", "full_name": "WeWay"}, {"asset": "wxbtc", "full_name": "Wrapped xBTC"}, {"asset": "wxt", "full_name": "<PERSON><PERSON>"}, {"asset": "wzec", "full_name": "Wrapped Zcoin"}, {"asset": "x", "full_name": "GIBX Swap"}, {"asset": "x2p", "full_name": "Xenon Pay"}, {"asset": "xaf", "full_name": "Central African CFA Franc"}, {"asset": "xai", "full_name": "Xai"}, {"asset": "xas", "full_name": "<PERSON><PERSON>"}, {"asset": "xaur", "full_name": "Xaurum"}, {"asset": "xaut", "full_name": "Tether Gold"}, {"asset": "<PERSON><PERSON><PERSON><PERSON>", "full_name": "3X Short Tether Gold Token"}, {"asset": "<PERSON><PERSON><PERSON>", "full_name": "3X Long Tether Gold Token"}, {"asset": "xauthalf", "full_name": "0.5X Long Tether Gold Token"}, {"asset": "xauthedge", "full_name": "1X Short Tether Gold Token"}, {"asset": "xava", "full_name": "Avalaunch"}, {"asset": "xbc", "full_name": "Bitcoin Plus"}, {"asset": "xbn", "full_name": "Elastic BNB"}, {"asset": "xbp", "full_name": "BlitzPredict"}, {"asset": "xcad", "full_name": "XCAD Network"}, {"asset": "xcc", "full_name": "Chives Coin"}, {"asset": "xcd", "full_name": "East Caribbean Dollar"}, {"asset": "xch", "full_name": "<PERSON><PERSON>"}, {"asset": "xchf", "full_name": "CryptoFranc"}, {"asset": "xclr", "full_name": "ClearCoin"}, {"asset": "xcn", "full_name": "Onyxcoin"}, {"asset": "xcon", "full_name": "Connect Coin"}, {"asset": "xcp", "full_name": "Counterparty"}, {"asset": "xcur", "full_name": "Curate"}, {"asset": "xcv", "full_name": "XCarnival"}, {"asset": "xdb", "full_name": "XDB CHAIN"}, {"asset": "xdc", "full_name": "XDC Network"}, {"asset": "xdefi", "full_name": "XDEFI"}, {"asset": "xdn", "full_name": "DigitalNote"}, {"asset": "xdoge", "full_name": "ClassicDoge"}, {"asset": "xec", "full_name": "eCash"}, {"asset": "xed", "full_name": "Exeedme"}, {"asset": "xel", "full_name": "Elastic"}, {"asset": "xels", "full_name": "XELS"}, {"asset": "xem", "full_name": "NEM"}, {"asset": "xen", "full_name": "XEN Crypto"}, {"asset": "xend", "full_name": "Xend Finance"}, {"asset": "xep", "full_name": "Electra Protocol"}, {"asset": "xes", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "xeta", "full_name": "XANA"}, {"asset": "xgold", "full_name": "XGOLD COIN"}, {"asset": "xhdx", "full_name": "HydraDX"}, {"asset": "xhv", "full_name": "Haven Protocol"}, {"asset": "xiasi", "full_name": "<PERSON><PERSON>"}, {"asset": "xidr_eth", "full_name": "StraitsX Indonesian Rupiah on Ethereum"}, {"asset": "xlm", "full_name": "Stellar"}, {"asset": "xlmbear", "full_name": "3X Short Stellar Token"}, {"asset": "xlmbull", "full_name": "3X Long Stellar Token"}, {"asset": "xlmdown", "full_name": "XLMDOWN"}, {"asset": "xlmup", "full_name": "XLMUP"}, {"asset": "xlr", "full_name": "Solar<PERSON>"}, {"asset": "xmark", "full_name": "xMARK"}, {"asset": "xmc", "full_name": "Monero Classic"}, {"asset": "xmct", "full_name": "XMCT"}, {"asset": "xmo", "full_name": "Monero Original"}, {"asset": "xmon", "full_name": "XMON"}, {"asset": "xmr", "full_name": "<PERSON><PERSON>"}, {"asset": "xmt", "full_name": "MetalSwap"}, {"asset": "xmx", "full_name": "XMax"}, {"asset": "xmy", "full_name": "Myriad"}, {"asset": "xnft", "full_name": "xNFT Protocol"}, {"asset": "xnk", "full_name": "Ink Protocol"}, {"asset": "xnl", "full_name": "Chronicle"}, {"asset": "xno", "full_name": "<PERSON><PERSON>"}, {"asset": "xno_xeno", "full_name": "Xeno"}, {"asset": "xns", "full_name": "Insolar"}, {"asset": "xof", "full_name": "West African CFA Franc"}, {"asset": "xor", "full_name": "Oracolxor"}, {"asset": "xp", "full_name": "PolkaFantasy"}, {"asset": "xpf", "full_name": "CFP Franc"}, {"asset": "xpla", "full_name": "XPLA"}, {"asset": "xpm", "full_name": "Primecoin"}, {"asset": "xpnet", "full_name": "XP NETWORK"}, {"asset": "xpo", "full_name": "X-power Chain"}, {"asset": "xpr", "full_name": "XPR Network"}, {"asset": "xpress", "full_name": "CryptoXpress"}, {"asset": "xprt", "full_name": "Persistence One"}, {"asset": "xra", "full_name": "Ratecoin"}, {"asset": "xrc", "full_name": "Bitcoin Rhodium"}, {"asset": "xrd", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "xrp", "full_name": "XRP", "exchanges": ["bittrex", "deribit", "huobi"], "markets": ["bittrex-xrp-usd-spot", "deribit-XRP-31JAN24-future", "deribit-XRP-31SEP23-2023-P-option", "deribit-XRP_USDC-9DEC24-2d6-P-option", "huobi-XRP-31AUG23-2023-P-option"]}, {"asset": "xrpbear", "full_name": "3x Short XRP Token"}, {"asset": "xrpbull", "full_name": "3x Long XRP Token"}, {"asset": "xrpdown", "full_name": "XRPDOWN"}, {"asset": "xrphalf", "full_name": "0.5X Long XRP Token"}, {"asset": "xrphedge", "full_name": "1X Short XRP Token"}, {"asset": "xrpup", "full_name": "XRPUP"}, {"asset": "xrune", "full_name": "Thorstarter"}, {"asset": "xsgd", "full_name": "XSGD"}, {"asset": "xsn", "full_name": "Stakenet"}, {"asset": "xsr", "full_name": "Xensor"}, {"asset": "xst", "full_name": "Stealth"}, {"asset": "xstar", "full_name": "StarCurve"}, {"asset": "xsushi", "full_name": "xSUSHI"}, {"asset": "xtag", "full_name": "xHashtag"}, {"asset": "xtm", "full_name": "<PERSON><PERSON>"}, {"asset": "xtp", "full_name": "Tap"}, {"asset": "xtz", "full_name": "Tezos", "experimental": true}, {"asset": "xtzbear", "full_name": "3x Short Tezos <PERSON>"}, {"asset": "xtzbull", "full_name": "3x Long Tezos <PERSON>"}, {"asset": "xtzdown", "full_name": "XTZDOWN"}, {"asset": "xtzhalf", "full_name": "0.5X Long Tezos Token"}, {"asset": "xtzhedge", "full_name": "1X Short Tezos Token"}, {"asset": "xtzup", "full_name": "XTZUP"}, {"asset": "xuc", "full_name": "Exchange Union"}, {"asset": "xvg", "full_name": "Verge"}, {"asset": "xvix", "full_name": "XVIX"}, {"asset": "xvs", "full_name": "Venus"}, {"asset": "xwc", "full_name": "WhiteCoin"}, {"asset": "xwg", "full_name": "X World Games"}, {"asset": "xym", "full_name": "Symbol"}, {"asset": "xyo", "full_name": "XYO"}, {"asset": "xzc", "full_name": "Zcoin"}, {"asset": "yam", "full_name": "YAM"}, {"asset": "yamv2", "full_name": "YAMv2"}, {"asset": "ycc", "full_name": "Yuan Chain Coin"}, {"asset": "yct", "full_name": "<PERSON><PERSON><PERSON><PERSON>"}, {"asset": "yee", "full_name": "YEE"}, {"asset": "yeed", "full_name": "YGGDRASH"}, {"asset": "yefi", "full_name": "Yearn Ethereum Finance"}, {"asset": "yer", "full_name": "Yemeni R<PERSON>"}, {"asset": "yffii", "full_name": "YFFII Finance"}, {"asset": "yfi", "full_name": "yearn.finance"}, {"asset": "yfidown", "full_name": "YFIDOWN"}, {"asset": "yfii", "full_name": "DFI.Money"}, {"asset": "yfiup", "full_name": "YFIUP"}, {"asset": "yfl", "full_name": "YF Link"}, {"asset": "yfv", "full_name": "YFValue"}, {"asset": "yfx", "full_name": "Your Futures Exchange"}, {"asset": "ygg", "full_name": "Yield Guild Games"}, {"asset": "yield", "full_name": "Yield Protocol"}, {"asset": "yin", "full_name": "YIN Finance"}, {"asset": "yld", "full_name": "<PERSON><PERSON>"}, {"asset": "yoc", "full_name": "Yocoin"}, {"asset": "<PERSON><PERSON>", "full_name": "<PERSON><PERSON><PERSON><PERSON>"}, {"asset": "yop", "full_name": "Yield Optimization Platform & Protocol"}, {"asset": "you", "full_name": "YOU COIN"}, {"asset": "yoyow", "full_name": "YOYOW"}, {"asset": "yuct", "full_name": "Yucreat"}, {"asset": "yye", "full_name": "YYE Energy"}, {"asset": "zap", "full_name": "Zap"}, {"asset": "zar", "full_name": "South African Rand"}, {"asset": "zb", "full_name": "ZB"}, {"asset": "zbc", "full_name": "Zebec"}, {"asset": "zcl", "full_name": "ZClassic"}, {"asset": "zcn", "full_name": "0chain"}, {"asset": "zco", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "zcx", "full_name": "Unizen"}, {"asset": "zec", "full_name": "Zcash"}, {"asset": "<PERSON><PERSON><PERSON><PERSON>", "full_name": "3X Short Zcash Token"}, {"asset": "<PERSON><PERSON><PERSON>", "full_name": "3X Long Zcash Token"}, {"asset": "zee", "full_name": "ZeroSwap"}, {"asset": "zel", "full_name": "<PERSON><PERSON>"}, {"asset": "zen", "full_name": "Ho<PERSON><PERSON>"}, {"asset": "zengold", "full_name": "ZenGold"}, {"asset": "zil", "full_name": "<PERSON><PERSON><PERSON><PERSON>"}, {"asset": "zil_eth", "full_name": "Zilliqa on Ethereum"}, {"asset": "zild", "full_name": "Zild Finance"}, {"asset": "zinu", "full_name": "Zombie Inu"}, {"asset": "zip", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "zjlt", "full_name": "ZJLT Distributed Factoring Network"}, {"asset": "zks", "full_name": "ZKSpace"}, {"asset": "zla", "full_name": "Zilla"}, {"asset": "zlot", "full_name": "zLOT"}, {"asset": "zlw", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "zm", "full_name": "Zoom Video Communications Inc Tokenized Stock"}, {"asset": "zmk", "full_name": "Zambia<PERSON> (before December 31, 2012)"}, {"asset": "zmt", "full_name": "Zipmex Token"}, {"asset": "zmw", "full_name": "Zambian <PERSON>"}, {"asset": "zoe", "full_name": "<PERSON>"}, {"asset": "zoon", "full_name": "CryptoZoon"}, {"asset": "zoot", "full_name": "Zoo Token"}, {"asset": "zort", "full_name": "ZORT"}, {"asset": "zpr", "full_name": "ZPER"}, {"asset": "zpt", "full_name": "<PERSON><PERSON><PERSON>"}, {"asset": "zptc", "full_name": "Zeptacoin"}, {"asset": "zrc", "full_name": "Zircuit"}, {"asset": "zrx", "full_name": "0x"}, {"asset": "zsc", "full_name": "Zeusshield"}, {"asset": "zusd", "full_name": "ZUSD"}, {"asset": "zwap", "full_name": "Zilswap"}, {"asset": "zwl", "full_name": "Zimbabwean Dollar"}, {"asset": "zyn", "full_name": "Zynecoin"}, {"asset": "zyro", "full_name": "Zyr<PERSON>"}]}}