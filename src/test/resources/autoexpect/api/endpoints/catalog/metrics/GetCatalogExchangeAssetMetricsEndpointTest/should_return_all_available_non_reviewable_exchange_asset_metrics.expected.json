{"status": 200, "headers": {"content-type": "application/json"}, "content": {"data": [{"metric": "basis_annualized_30d_exp", "full_name": "Basis, annualized, 30 day expiration ", "description": "The relative difference between the price of a futures contract that expires in 30 days and the price of its underlying spot market.", "product": "Market Data", "category": "<PERSON><PERSON>", "subcategory": "Futures", "unit": "Dimensionless", "data_type": "decimal", "type": "Percentage", "frequencies": [{"frequency": "1h", "exchange_assets": ["cme-ezeth"]}, {"frequency": "1d", "exchange_assets": ["cme-ezeth"]}], "display_name": "Annualized Futures Basis, 30 day expiration"}, {"metric": "futures_aggregate_funding_rate_all_margin_1d_period", "full_name": "Funding rate, aggregated, futures, all-margined, 1 day", "description": "The average funding rate weighted by open interest from all futures markets, regardless of the margin asset, converted to a 1 day period.", "product": "Market Data", "category": "Funding Rates", "subcategory": "Futures", "unit": "Dimensionless", "data_type": "decimal", "type": "Percentage", "frequencies": [{"frequency": "1h", "exchange_assets": ["binance-btc"]}, {"frequency": "1d", "exchange_assets": ["binance-btc"]}], "display_name": "Aggregated All-Margined Futures Funding Rate, 1 Day"}, {"metric": "futures_aggregate_funding_rate_all_margin_1y_period", "full_name": "Funding rate, aggregated, futures, all-margined, 1 year", "description": "The average funding rate weighted by open interest from all futures markets, regardless of the margin asset, converted to a 1 year period.", "product": "Market Data", "category": "Funding Rates", "subcategory": "Futures", "unit": "Dimensionless", "data_type": "decimal", "type": "Percentage", "frequencies": [{"frequency": "1h", "exchange_assets": ["binance-btc"]}, {"frequency": "1d", "exchange_assets": ["binance-btc"]}], "display_name": "Aggregated All-Margined Futures Funding Rate, 1 Year"}, {"metric": "futures_aggregate_funding_rate_all_margin_30d_period", "full_name": "Funding rate, aggregated, futures, all-margined, 30 days", "description": "The average funding rate weighted by open interest from all futures markets, regardless of the margin asset, converted to a 30 day period.", "product": "Market Data", "category": "Funding Rates", "subcategory": "Futures", "unit": "Dimensionless", "data_type": "decimal", "type": "Percentage", "frequencies": [{"frequency": "1h", "exchange_assets": ["binance-btc"]}, {"frequency": "1d", "exchange_assets": ["binance-btc"]}], "display_name": "Aggregated All-Margined Futures Funding Rate, 30 Days"}, {"metric": "futures_aggregate_funding_rate_all_margin_8h_period", "full_name": "Funding rate, aggregated, futures, all-margined, 8 hours", "description": "The average funding rate weighted by open interest from all futures markets, regardless of the margin asset, converted to a 8 hour period.", "product": "Market Data", "category": "Funding Rates", "subcategory": "Futures", "unit": "Dimensionless", "data_type": "decimal", "type": "Percentage", "frequencies": [{"frequency": "1h", "exchange_assets": ["binance-btc"]}, {"frequency": "1d", "exchange_assets": ["binance-btc"]}], "display_name": "Aggregated All-Margined Futures Funding Rate, 8 Hours"}, {"metric": "futures_aggregate_funding_rate_coin_margin_1d_period", "full_name": "Funding rate, aggregated, futures, coin-margined, 1 day", "description": "The average funding rate weighted by open interest from futures markets where the margin asset is equivalent to the underlying base asset converted to a 1 day period.", "product": "Market Data", "category": "Funding Rates", "subcategory": "Futures", "unit": "Dimensionless", "data_type": "decimal", "type": "Percentage", "frequencies": [{"frequency": "1h", "exchange_assets": ["binance-btc"]}, {"frequency": "1d", "exchange_assets": ["binance-btc"]}], "display_name": "Aggregated Coin-Margined Futures Funding Rate, 1 Day"}, {"metric": "futures_aggregate_funding_rate_coin_margin_1y_period", "full_name": "Funding rate, aggregated, futures, coin-margined, 1 year", "description": "The average funding rate weighted by open interest from futures markets where the margin asset is equivalent to the underlying base asset converted to a 1 year period.", "product": "Market Data", "category": "Funding Rates", "subcategory": "Futures", "unit": "Dimensionless", "data_type": "decimal", "type": "Percentage", "frequencies": [{"frequency": "1h", "exchange_assets": ["binance-btc"]}, {"frequency": "1d", "exchange_assets": ["binance-btc"]}], "display_name": "Aggregated Coin-Margined Futures Funding Rate, 1 Year"}, {"metric": "futures_aggregate_funding_rate_coin_margin_30d_period", "full_name": "Funding rate, aggregated, futures, coin-margined, 30 days", "description": "The average funding rate weighted by open interest from futures markets where the margin asset is equivalent to the underlying base asset converted to a 30 day period.", "product": "Market Data", "category": "Funding Rates", "subcategory": "Futures", "unit": "Dimensionless", "data_type": "decimal", "type": "Percentage", "frequencies": [{"frequency": "1h", "exchange_assets": ["binance-btc"]}, {"frequency": "1d", "exchange_assets": ["binance-btc"]}], "display_name": "Aggregated Coin-Margined Futures Funding Rate, 30 Day"}, {"metric": "futures_aggregate_funding_rate_coin_margin_8h_period", "full_name": "Funding rate, aggregated, futures, coin-margined, 8 hours", "description": "The average funding rate weighted by open interest from futures markets where the margin asset is equivalent to the underlying base asset converted to a 8 hour period.", "product": "Market Data", "category": "Funding Rates", "subcategory": "Futures", "unit": "Dimensionless", "data_type": "decimal", "type": "Percentage", "frequencies": [{"frequency": "1h", "exchange_assets": ["binance-btc"]}, {"frequency": "1d", "exchange_assets": ["binance-btc"]}], "display_name": "Aggregated Coin-Margined Futures Funding Rate, 8 Hours"}, {"metric": "futures_aggregate_funding_rate_usd_margin_1d_period", "full_name": "Funding rate, aggregated, futures, USD-margined, 1 day", "description": "The average funding rate weighted by open interest from futures markets where the margin asset is U.S. dollars converted to a 1 day period.", "product": "Market Data", "category": "Funding Rates", "subcategory": "Futures", "unit": "Dimensionless", "data_type": "decimal", "type": "Percentage", "frequencies": [{"frequency": "1h", "exchange_assets": ["binance-btc"]}, {"frequency": "1d", "exchange_assets": ["binance-btc"]}], "display_name": "Aggregated USD-Margined Futures Funding Rate, 1 Day"}, {"metric": "futures_aggregate_funding_rate_usd_margin_1y_period", "full_name": "Funding rate, aggregated, futures, USD-margined, 1 year", "description": "The average funding rate weighted by open interest from futures markets where the margin asset is U.S. dollars converted to a 1 year period.", "product": "Market Data", "category": "Funding Rates", "subcategory": "Futures", "unit": "Dimensionless", "data_type": "decimal", "type": "Percentage", "frequencies": [{"frequency": "1h", "exchange_assets": ["binance-btc"]}, {"frequency": "1d", "exchange_assets": ["binance-btc"]}], "display_name": "Aggregated USD-Margined Futures Funding Rate, 1 Year"}, {"metric": "futures_aggregate_funding_rate_usd_margin_30d_period", "full_name": "Funding rate, aggregated, futures, USD-margined, 30 days", "description": "The average funding rate weighted by open interest from futures markets where the margin asset is U.S. dollars converted to a 30 day period.", "product": "Market Data", "category": "Funding Rates", "subcategory": "Futures", "unit": "Dimensionless", "data_type": "decimal", "type": "Percentage", "frequencies": [{"frequency": "1h", "exchange_assets": ["binance-btc"]}, {"frequency": "1d", "exchange_assets": ["binance-btc"]}], "display_name": "Aggregated USD-Margined Futures Funding Rate, 30 Days"}, {"metric": "futures_aggregate_funding_rate_usd_margin_8h_period", "full_name": "Funding rate, aggregated, futures, USD-margined, 8 hours", "description": "The average funding rate weighted by open interest from futures markets where the margin asset is U.S. dollars converted to a 8 hour period.", "product": "Market Data", "category": "Funding Rates", "subcategory": "Futures", "unit": "Dimensionless", "data_type": "decimal", "type": "Percentage", "frequencies": [{"frequency": "1h", "exchange_assets": ["binance-btc"]}, {"frequency": "1d", "exchange_assets": ["binance-btc"]}], "display_name": "Aggregated USD-Margined Futures Funding Rate, 8 Hours"}, {"metric": "liquidations_reported_future_sell_usd_5m", "full_name": "Liquidations, reported, future, sells, USD, five minutes", "description": "The sum of all sell liquidations from perpetual futures markets in U.S. dollars.", "product": "Market Data", "category": "Liquidations", "subcategory": "Futures", "unit": "USD", "data_type": "decimal", "type": "Sum", "frequencies": [{"frequency": "5m", "exchange_assets": ["binance-btc"]}], "display_name": "Reported Futures Sell Liquidations, USD"}, {"metric": "open_interest_reported_future_coin_margined_usd", "full_name": "Open interest, reported, future, coin-margined, USD", "description": "The sum of all reported open interest from futures markets where the margin asset is equivalent to the underlying base asset in units of U.S. dollars.", "product": "Market Data", "category": "Open Interest", "subcategory": "Futures", "unit": "USD", "data_type": "decimal", "type": "Sum", "frequencies": [{"frequency": "1h", "exchange_assets": ["binance-aave"]}, {"frequency": "1d", "exchange_assets": ["binance-aave"]}], "display_name": "Reported Coin-Margined Future Open Interest"}, {"metric": "open_interest_reported_future_tether_margined_usd", "full_name": "Open interest, reported, future, tether-margined, USD", "description": "The sum of all reported open interest from futures markets where the margin asset is Tether in units of U.S. dollars.", "product": "Market Data", "category": "Open Interest", "subcategory": "Futures", "unit": "USD", "data_type": "decimal", "type": "Sum", "frequencies": [{"frequency": "1h", "exchange_assets": ["binance-aave"]}, {"frequency": "1d", "exchange_assets": ["binance-aave"]}], "display_name": "Reported Tether-Margined Future Open Interest"}, {"metric": "open_interest_reported_future_usd", "full_name": "Open interest, reported, future, USD", "description": "The sum of all reported open interest from futures markets in units of U.S. dollars.", "product": "Market Data", "category": "Open Interest", "subcategory": "Futures", "unit": "USD", "data_type": "decimal", "type": "Sum", "frequencies": [{"frequency": "1h", "exchange_assets": ["binance-btc", "binance-eth"]}, {"frequency": "1d", "exchange_assets": ["binance-btc", "binance-eth"]}], "display_name": "Reported Future Open Interest"}, {"metric": "volume_trusted_spot_usd_1d", "full_name": "Volume, trusted, spot, USD, one day", "description": "The sum of all volume from the spot markets of a set of trusted exchanges in units of U.S. dollars.", "product": "Market Data", "category": "Volume", "subcategory": "Trusted", "unit": "USD", "data_type": "decimal", "type": "Sum", "frequencies": [{"frequency": "1d", "exchange_assets": ["binance-btc", "binance-eth"]}], "display_name": "Trusted Spot Volume"}, {"metric": "volume_trusted_spot_usd_1h", "full_name": "Volume, trusted, spot, USD, one hour", "description": "The sum of all volume from the spot markets of a set of trusted exchanges in units of U.S. dollars.", "product": "Market Data", "category": "Volume", "subcategory": "Trusted", "unit": "USD", "data_type": "decimal", "type": "Sum", "frequencies": [{"frequency": "1h", "exchange_assets": ["binance-btc", "binance-eth"]}], "display_name": "Trusted Spot Volume"}]}}