{"status": 200, "headers": {"content-type": "application/json"}, "content": {"data": [{"metric": "FlowInGEMNtv", "full_name": "Flow, in, to Gemini, native units", "description": "The sum in native units sent to Gemini that interval.", "category": "Exchange", "subcategory": "Deposits", "unit": "Native units", "data_type": "decimal", "type": "Sum", "frequencies": [{"frequency": "1b", "assets": ["eth"]}, {"frequency": "1d", "assets": ["eth"]}], "reviewable": true, "display_name": "Gemini Deposits (native units)"}]}}