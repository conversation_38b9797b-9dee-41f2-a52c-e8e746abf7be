{"status": 200, "headers": {"content-type": "application/json"}, "content": {"data": [{"metric": "AdrActCnt", "full_name": "Addresses, active, count", "description": "The sum count of unique addresses that were active in the network (either as a recipient or originator of a ledger change) that interval. All parties in a ledger change action (recipients and originators) are counted. Individual addresses are not double-counted if previously active.", "product": "Network Data", "category": "Addresses", "subcategory": "Active", "unit": "Addresses", "data_type": "bigint", "type": "Sum", "frequencies": [{"frequency": "1d", "assets": ["ant"]}], "display_name": "Active Addr Cnt"}, {"metric": "BlkHgt", "full_name": "Block, height", "description": "The count of blocks from the genesis (first) block to the last block of that interval on the main chain.", "product": "Network Data", "category": "Network Usage", "subcategory": "Blocks", "unit": "Blocks", "data_type": "bigint", "type": "NA", "frequencies": [{"frequency": "1b", "assets": ["sol"]}], "display_name": "Block Height"}, {"metric": "CapMrktEstUSD", "full_name": "Capitalization, market, estimated supply, USD", "description": "The sum USD value of the estimated supply in circulation. Also referred to as network value or market capitalization.", "product": "Network Data", "category": "Market", "subcategory": "Market Capitalization", "unit": "USD", "data_type": "decimal", "type": "Product", "frequencies": [{"frequency": "1d", "assets": ["ocean"]}], "display_name": "Market Cap Estimated (USD)"}, {"metric": "FlowInGEMNtv", "full_name": "Flow, in, to Gemini, native units", "description": "The sum in native units sent to Gemini that interval.", "product": "Network Data", "category": "Exchange", "subcategory": "Deposits", "unit": "Native units", "data_type": "decimal", "type": "Sum", "frequencies": [{"frequency": "1b", "assets": ["eth"]}, {"frequency": "1d", "assets": ["eth"]}], "reviewable": true, "display_name": "Gemini Deposits (native units)"}, {"metric": "FlowTfrInBFXCnt", "full_name": "Flow, transfers, to Bitfinex, count", "description": "The sum count of transfers to any address belonging to Bitfinex in that interval. If the sender address also belongs to Bitfinex, the transfer is not counted.", "product": "Network Data", "category": "Exchange", "subcategory": "Deposits", "unit": "Transfers", "data_type": "decimal", "type": "Sum", "frequencies": [{"frequency": "1b", "assets": ["eth"]}], "reviewable": true, "display_name": "Bitfinex Deposit Cnt"}, {"metric": "FlowTfrOutBTXCnt", "full_name": "Flow, transfers, from Bittrex, count", "description": "The sum count of transfers from any address belonging to Bittrex in that interval. If the recipient address also belongs to Bittrex, the transfer is not counted.", "product": "Network Data", "category": "Exchange", "subcategory": "<PERSON><PERSON><PERSON><PERSON>", "unit": "Transfers", "data_type": "decimal", "type": "Sum", "frequencies": [{"frequency": "1d", "assets": ["btc", "eth"]}], "reviewable": true, "display_name": "Bittrex Withdrawal Cnt"}, {"metric": "PriceUSD", "full_name": "Price, USD", "description": "The fixed closing price of the asset as of 00:00 UTC the following day (i.e., midnight UTC of the current day) denominated in USD. This price is generated by Coin Metrics' fixing/reference rate service. Real-time PriceUSD is the fixed closing price of the asset as of the timestamp set by the block's miner.", "product": "Network Data", "category": "Market", "subcategory": "Price", "unit": "USD", "data_type": "decimal", "type": "NA", "frequencies": [{"frequency": "1b", "assets": ["btc", "eth", "sol"]}, {"frequency": "1d", "assets": ["btc"]}], "display_name": "USD Denominated Closing Price"}, {"metric": "ReferenceRate", "full_name": "Reference Rate, USD", "description": "The price of an asset quoted in U.S. dollars using a framework to select high quality constituent markets and a methodology that is resistant manipulation.", "product": "CM Prices", "category": "Market", "subcategory": "Price", "unit": "USD", "data_type": "decimal", "type": "NA", "frequencies": [{"frequency": "1s", "assets": ["btc", "eth", "mtl_metal"]}, {"frequency": "1m", "assets": ["btc", "eth"]}, {"frequency": "1h", "assets": ["ada", "atom", "btc", "eth"]}, {"frequency": "1d", "assets": ["btc", "eth"]}, {"frequency": "1d-ny-close", "assets": ["ada", "btc"]}]}, {"metric": "ReferenceRateETH", "full_name": "Reference Rate, ETH", "description": "The price of an asset quoted in Etherium using a framework to select high quality constituent markets and a methodology that is resistant manipulation.", "product": "CM Prices", "category": "Market", "subcategory": "Price", "unit": "ETH", "data_type": "decimal", "type": "NA", "frequencies": [{"frequency": "1h", "assets": ["atom"]}]}, {"metric": "ReferenceRateEUR", "full_name": "Reference Rate, EUR", "description": "The price of an asset quoted in Euros using a framework to select high quality constituent markets and a methodology that is resistant manipulation.", "product": "CM Prices", "category": "Market", "subcategory": "Price", "unit": "EUR", "data_type": "decimal", "type": "NA", "frequencies": [{"frequency": "1s", "assets": ["btc", "eth"]}, {"frequency": "1m", "assets": ["btc", "eth"]}, {"frequency": "1h", "assets": ["btc", "eth"]}, {"frequency": "1d", "assets": ["btc", "eth"]}, {"frequency": "1d-ny-close", "assets": ["btc"]}]}, {"metric": "ReferenceRateUSD", "full_name": "Reference Rate, USD", "description": "The price of an asset quoted in U.S. dollars using a framework to select high quality constituent markets and a methodology that is resistant manipulation.", "product": "CM Prices", "category": "Market", "subcategory": "Price", "unit": "USD", "data_type": "decimal", "type": "NA", "frequencies": [{"frequency": "1s", "assets": ["btc", "eth", "mtl_metal"]}, {"frequency": "1m", "assets": ["btc", "eth"]}, {"frequency": "1h", "assets": ["ada", "atom", "btc", "eth"]}, {"frequency": "1d", "assets": ["btc", "eth"]}, {"frequency": "1d-ny-close", "assets": ["ada", "btc"]}]}, {"metric": "RevNtv", "full_name": "Miner revenue, native units", "description": "The sum native units of all miner revenue (fees plus newly issued native units) that interval.", "product": "Network Data", "category": "Fees and Revenue", "subcategory": "Revenue", "unit": "Native units", "data_type": "decimal", "type": "Sum", "frequencies": [{"frequency": "1b", "assets": ["eth"]}], "display_name": "Miner Revenue (native units)"}, {"metric": "SplyExUSD", "full_name": "Supply, held by exchanges, USD", "description": "The sum USD value of all native units held in hot or cold exchange wallets that interval.", "product": "Network Data", "category": "Exchange", "subcategory": "Exchange Supply", "unit": "USD", "data_type": "decimal", "type": "Sum", "frequencies": [{"frequency": "1d", "assets": ["btc", "eth"]}], "reviewable": true, "display_name": "Exchange Supply (USD)"}, {"metric": "TxCnt", "full_name": "Transactions, count", "description": "The sum count of transactions that interval. Transactions represent a bundle of intended actions to alter the ledger initiated by a user (human or machine). Transactions are counted whether they execute or not and whether they result in the transfer of native units or not (a transaction can result in no, one, or many transfers). Changes to the ledger mandated by the protocol (and not by a user) or post-launch new issuance issued by a founder or controlling entity are not included here.", "product": "Network Data", "category": "Transactions", "subcategory": "Transactions", "unit": "Transactions", "data_type": "bigint", "type": "Sum", "frequencies": [{"frequency": "1b", "assets": ["btc", "eth", "sol"]}, {"frequency": "1d", "assets": ["btc", "eth"]}], "display_name": "Tx Cnt"}, {"metric": "TxTfrValMedNtv", "full_name": "Transactions, transfers, value, median, native units", "description": "The median count of native units transferred per transfer (i.e., the median size of a transfer) between distinct addresses that interval.", "product": "Network Data", "category": "Transactions", "subcategory": "Transfer Value", "unit": "Native units", "data_type": "decimal", "type": "Median", "frequencies": [{"frequency": "1d", "assets": ["ant"]}], "display_name": "Median Tx Size (native units)"}, {"metric": "block_count_at_tip", "full_name": "Block, count, at tip, one block", "description": "The number of blocks identified at the chain tip.", "product": "FARUM", "category": "KRI", "subcategory": "Blocks", "unit": "Blocks", "data_type": "bigint", "type": "N/A", "frequencies": [{"frequency": "1b", "assets": ["btc"]}], "display_name": "1 Block Count of Blocks at the Chain Tip"}, {"metric": "block_count_empty_6b", "full_name": "Block, count, empty, six blocks", "description": "The number of empty blocks in the past 6 blocks.", "product": "FARUM", "category": "KRI", "subcategory": "Blocks", "unit": "Blocks", "data_type": "bigint", "type": "Sum", "frequencies": [{"frequency": "1b", "assets": ["btc"]}], "display_name": "1 Block Count of Empty Blocks from the Past 6 Blocks"}, {"metric": "block_difficulty", "full_name": "Block, difficulty, one block", "description": "The difficulty of the block.", "product": "FARUM", "category": "KRI", "subcategory": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "data_type": "decimal", "type": "N/A", "frequencies": [{"frequency": "1b", "assets": ["btc"]}], "display_name": "1 Block Mining Difficulty"}, {"metric": "block_feerate_min", "full_name": "Block, feerate, min, one block", "description": "Mined block's min mined feerate in native units per vByte.", "product": "FARUM", "category": "KRI", "subcategory": "Transaction feerates", "unit": "Satoshi/vByte", "data_type": "decimal", "type": "Min", "frequencies": [{"frequency": "1b", "assets": ["btc"]}], "display_name": "1 Block Min Transaction Feerate"}, {"metric": "block_fees", "full_name": "Block, fees, one block", "description": "The sum of fees in the mined block, in native units.", "product": "FARUM", "category": "KRI", "subcategory": "Transaction fees", "unit": "Native units", "data_type": "decimal", "type": "Sum", "frequencies": [{"frequency": "1b", "assets": ["btc"]}], "display_name": "1 Block Sum of Transaction Fees"}, {"metric": "block_missed_slots", "full_name": "Missed Slots", "description": "The count of missed slots.", "product": "FARUM", "category": "KRI", "subcategory": "Empty Blocks", "unit": "Slots", "data_type": "bigint", "type": "Sum", "frequencies": [{"frequency": "1b", "assets": ["eth"]}], "display_name": "Count of missed slots."}, {"metric": "block_tx_count", "full_name": "Block, transaction count, one block", "description": "Mined block's transaction count.", "product": "FARUM", "category": "KRI", "subcategory": "Transactions", "unit": "Transactions", "data_type": "bigint", "type": "N/A", "frequencies": [{"frequency": "1b", "assets": ["eth"]}], "display_name": "1 Block Transaction Count"}, {"metric": "futures_aggregate_funding_rate_all_margin_1d_period", "full_name": "Funding rate, aggregated, futures, all-margined, 1 day", "description": "The average funding rate weighted by open interest from all futures markets, regardless of the margin asset, converted to a 1 day period.", "product": "Market Data", "category": "Funding Rates", "subcategory": "Futures", "unit": "Dimensionless", "data_type": "decimal", "type": "Percentage", "frequencies": [{"frequency": "1h", "assets": ["btc"]}, {"frequency": "1d", "assets": ["btc"]}], "display_name": "Aggregated All-Margined Futures Funding Rate, 1 Day"}, {"metric": "futures_aggregate_funding_rate_all_margin_1y_period", "full_name": "Funding rate, aggregated, futures, all-margined, 1 year", "description": "The average funding rate weighted by open interest from all futures markets, regardless of the margin asset, converted to a 1 year period.", "product": "Market Data", "category": "Funding Rates", "subcategory": "Futures", "unit": "Dimensionless", "data_type": "decimal", "type": "Percentage", "frequencies": [{"frequency": "1h", "assets": ["btc"]}, {"frequency": "1d", "assets": ["btc"]}], "display_name": "Aggregated All-Margined Futures Funding Rate, 1 Year"}, {"metric": "futures_aggregate_funding_rate_all_margin_30d_period", "full_name": "Funding rate, aggregated, futures, all-margined, 30 days", "description": "The average funding rate weighted by open interest from all futures markets, regardless of the margin asset, converted to a 30 day period.", "product": "Market Data", "category": "Funding Rates", "subcategory": "Futures", "unit": "Dimensionless", "data_type": "decimal", "type": "Percentage", "frequencies": [{"frequency": "1h", "assets": ["btc"]}, {"frequency": "1d", "assets": ["btc"]}], "display_name": "Aggregated All-Margined Futures Funding Rate, 30 Days"}, {"metric": "futures_aggregate_funding_rate_all_margin_8h_period", "full_name": "Funding rate, aggregated, futures, all-margined, 8 hours", "description": "The average funding rate weighted by open interest from all futures markets, regardless of the margin asset, converted to a 8 hour period.", "product": "Market Data", "category": "Funding Rates", "subcategory": "Futures", "unit": "Dimensionless", "data_type": "decimal", "type": "Percentage", "frequencies": [{"frequency": "1h", "assets": ["btc"]}, {"frequency": "1d", "assets": ["btc"]}], "display_name": "Aggregated All-Margined Futures Funding Rate, 8 Hours"}, {"metric": "futures_aggregate_funding_rate_coin_margin_1d_period", "full_name": "Funding rate, aggregated, futures, coin-margined, 1 day", "description": "The average funding rate weighted by open interest from futures markets where the margin asset is equivalent to the underlying base asset converted to a 1 day period.", "product": "Market Data", "category": "Funding Rates", "subcategory": "Futures", "unit": "Dimensionless", "data_type": "decimal", "type": "Percentage", "frequencies": [{"frequency": "1h", "assets": ["btc"]}, {"frequency": "1d", "assets": ["btc"]}], "display_name": "Aggregated Coin-Margined Futures Funding Rate, 1 Day"}, {"metric": "futures_aggregate_funding_rate_coin_margin_1y_period", "full_name": "Funding rate, aggregated, futures, coin-margined, 1 year", "description": "The average funding rate weighted by open interest from futures markets where the margin asset is equivalent to the underlying base asset converted to a 1 year period.", "product": "Market Data", "category": "Funding Rates", "subcategory": "Futures", "unit": "Dimensionless", "data_type": "decimal", "type": "Percentage", "frequencies": [{"frequency": "1h", "assets": ["btc"]}, {"frequency": "1d", "assets": ["btc"]}], "display_name": "Aggregated Coin-Margined Futures Funding Rate, 1 Year"}, {"metric": "futures_aggregate_funding_rate_coin_margin_30d_period", "full_name": "Funding rate, aggregated, futures, coin-margined, 30 days", "description": "The average funding rate weighted by open interest from futures markets where the margin asset is equivalent to the underlying base asset converted to a 30 day period.", "product": "Market Data", "category": "Funding Rates", "subcategory": "Futures", "unit": "Dimensionless", "data_type": "decimal", "type": "Percentage", "frequencies": [{"frequency": "1h", "assets": ["btc"]}, {"frequency": "1d", "assets": ["btc"]}], "display_name": "Aggregated Coin-Margined Futures Funding Rate, 30 Day"}, {"metric": "futures_aggregate_funding_rate_coin_margin_8h_period", "full_name": "Funding rate, aggregated, futures, coin-margined, 8 hours", "description": "The average funding rate weighted by open interest from futures markets where the margin asset is equivalent to the underlying base asset converted to a 8 hour period.", "product": "Market Data", "category": "Funding Rates", "subcategory": "Futures", "unit": "Dimensionless", "data_type": "decimal", "type": "Percentage", "frequencies": [{"frequency": "1h", "assets": ["btc"]}, {"frequency": "1d", "assets": ["btc"]}], "display_name": "Aggregated Coin-Margined Futures Funding Rate, 8 Hours"}, {"metric": "futures_aggregate_funding_rate_usd_margin_1d_period", "full_name": "Funding rate, aggregated, futures, USD-margined, 1 day", "description": "The average funding rate weighted by open interest from futures markets where the margin asset is U.S. dollars converted to a 1 day period.", "product": "Market Data", "category": "Funding Rates", "subcategory": "Futures", "unit": "Dimensionless", "data_type": "decimal", "type": "Percentage", "frequencies": [{"frequency": "1h", "assets": ["btc"]}, {"frequency": "1d", "assets": ["btc"]}], "display_name": "Aggregated USD-Margined Futures Funding Rate, 1 Day"}, {"metric": "futures_aggregate_funding_rate_usd_margin_1y_period", "full_name": "Funding rate, aggregated, futures, USD-margined, 1 year", "description": "The average funding rate weighted by open interest from futures markets where the margin asset is U.S. dollars converted to a 1 year period.", "product": "Market Data", "category": "Funding Rates", "subcategory": "Futures", "unit": "Dimensionless", "data_type": "decimal", "type": "Percentage", "frequencies": [{"frequency": "1h", "assets": ["btc"]}, {"frequency": "1d", "assets": ["btc"]}], "display_name": "Aggregated USD-Margined Futures Funding Rate, 1 Year"}, {"metric": "futures_aggregate_funding_rate_usd_margin_30d_period", "full_name": "Funding rate, aggregated, futures, USD-margined, 30 days", "description": "The average funding rate weighted by open interest from futures markets where the margin asset is U.S. dollars converted to a 30 day period.", "product": "Market Data", "category": "Funding Rates", "subcategory": "Futures", "unit": "Dimensionless", "data_type": "decimal", "type": "Percentage", "frequencies": [{"frequency": "1h", "assets": ["btc"]}, {"frequency": "1d", "assets": ["btc"]}], "display_name": "Aggregated USD-Margined Futures Funding Rate, 30 Days"}, {"metric": "futures_aggregate_funding_rate_usd_margin_8h_period", "full_name": "Funding rate, aggregated, futures, USD-margined, 8 hours", "description": "The average funding rate weighted by open interest from futures markets where the margin asset is U.S. dollars converted to a 8 hour period.", "product": "Market Data", "category": "Funding Rates", "subcategory": "Futures", "unit": "Dimensionless", "data_type": "decimal", "type": "Percentage", "frequencies": [{"frequency": "1h", "assets": ["btc"]}, {"frequency": "1d", "assets": ["btc"]}], "display_name": "Aggregated USD-Margined Futures Funding Rate, 8 Hours"}, {"metric": "mempool_count", "full_name": "Mempool, count, one minute", "description": "The count of all mempool transactions at a point in time.", "product": "FARUM", "category": "KRI", "subcategory": "Transactions", "unit": "Transactions", "data_type": "bigint", "type": "Sum", "frequencies": [{"frequency": "1m", "assets": ["btc"]}], "display_name": "1 Minute Count of Transactions in the Mempool"}, {"metric": "mempool_fee", "full_name": "Mempool, fee, one minute", "description": "The sum value of all mempool transaction fees at a point in time in native units.", "product": "FARUM", "category": "KRI", "subcategory": "Fees", "unit": "Native units", "data_type": "decimal", "type": "Sum", "frequencies": [{"frequency": "1m", "assets": ["btc"]}], "display_name": "1 Minute Fees of Mempool Transactions"}, {"metric": "mempool_next_block_inclusion_approx_feerate_min", "full_name": "Mempool, next block, inclusion, approximate feerate, min, one minute", "description": "The prediction of next block's minimum mineable feerate in native units per vByte.", "product": "FARUM", "category": "KRI", "subcategory": "Feerates", "unit": "Satoshi/vByte", "data_type": "decimal", "type": "Min", "frequencies": [{"frequency": "1m", "assets": ["btc"]}], "display_name": "1 Minute Prediction of Min Transaction Feerate for Inclusion into the next Mempool Block"}, {"metric": "open_interest_reported_future_usd", "full_name": "Open interest, reported, future, USD", "description": "The sum of all reported open interest from futures markets in units of U.S. dollars.", "product": "Market Data", "category": "Open Interest", "subcategory": "Futures", "unit": "USD", "data_type": "decimal", "type": "Sum", "frequencies": [{"frequency": "1h", "assets": ["btc", "eth"]}, {"frequency": "1d", "assets": ["btc", "eth"]}], "display_name": "Reported Future Open Interest"}, {"metric": "principal_market_price_usd", "full_name": "Principal Market Price, USD", "description": "The price of an asset quoted in U.S. dollars derived from the asset's principal market, the market with the most trading volume or activity.", "product": "CM Prices", "category": "Market", "subcategory": "Price", "unit": "USD", "data_type": "decimal", "type": "N/A", "frequencies": [{"frequency": "1s", "assets": ["btc", "eth"]}, {"frequency": "1m", "assets": ["btc", "eth"]}, {"frequency": "1h", "assets": ["btc", "eth"]}, {"frequency": "1d", "assets": ["btc", "eth"]}, {"frequency": "1d-ny-close", "assets": ["btc", "eth"]}], "display_name": "Principal Market Price"}, {"metric": "principal_market_usd", "full_name": "Principal Market, USD", "description": "The asset's principal market, the market with the most trading volume or activity.", "product": "CM Prices", "category": "Market", "subcategory": "Price", "unit": "USD", "data_type": "text", "type": "N/A", "frequencies": [{"frequency": "1s", "assets": ["btc", "eth"]}, {"frequency": "1m", "assets": ["btc", "eth"]}, {"frequency": "1h", "assets": ["btc", "eth"]}, {"frequency": "1d", "assets": ["btc", "eth"]}, {"frequency": "1d-ny-close", "assets": ["btc", "eth"]}], "display_name": "Principal Market"}, {"metric": "volatility_realized_usd_rolling_24h", "full_name": "Volatility, realized, USD, rolling, 24 hours", "description": "The 24 hour rolling realized volatility, measured as the standard deviation of the natural log of returns of price in U.S. dollars calculated every 10 minutes over the past 24 hours.", "product": "Market Data", "category": "Market", "subcategory": "Volatility", "unit": "Dimensionless", "data_type": "decimal", "type": "<PERSON><PERSON>", "frequencies": [{"frequency": "10m", "assets": ["btc", "eth"]}, {"frequency": "1h", "assets": ["btc", "eth"]}, {"frequency": "1d", "assets": ["btc", "eth"]}], "display_name": "Realized Volatility, USD, 24 Hours"}, {"metric": "volatility_realized_usd_rolling_30d", "full_name": "Volatility, realized, USD, rolling, 30 days", "description": "The 30 day rolling realized volatility, measured as the standard deviation of the natural log of returns of price in U.S. dollars calculated every 10 minutes over the past 30 days.", "product": "Market Data", "category": "Market", "subcategory": "Volatility", "unit": "Dimensionless", "data_type": "decimal", "type": "<PERSON><PERSON>", "frequencies": [{"frequency": "10m", "assets": ["btc", "eth"]}, {"frequency": "1h", "assets": ["btc", "eth"]}, {"frequency": "1d", "assets": ["btc", "eth"]}], "display_name": "Realized Volatility, USD, 30 Days"}, {"metric": "volatility_realized_usd_rolling_7d", "full_name": "Volatility, realized, USD, rolling, 7 days", "description": "The 7 day rolling realized volatility, measured as the standard deviation of the natural log of returns of price in U.S. dollars calculated every 10 minutes over the past 7 days.", "product": "Market Data", "category": "Market", "subcategory": "Volatility", "unit": "Dimensionless", "data_type": "decimal", "type": "<PERSON><PERSON>", "frequencies": [{"frequency": "10m", "assets": ["btc", "eth"]}, {"frequency": "1h", "assets": ["btc", "eth"]}, {"frequency": "1d", "assets": ["btc", "eth"]}], "display_name": "Realized Volatility, USD, 7 Days"}, {"metric": "volume_trusted_spot_usd_1d", "full_name": "Volume, trusted, spot, USD, one day", "description": "The sum of all volume from the spot markets of a set of trusted exchanges in units of U.S. dollars.", "product": "Market Data", "category": "Volume", "subcategory": "Trusted", "unit": "USD", "data_type": "decimal", "type": "Sum", "frequencies": [{"frequency": "1d", "assets": ["btc", "eth"]}], "display_name": "Trusted Spot Volume"}, {"metric": "volume_trusted_spot_usd_1h", "full_name": "Volume, trusted, spot, USD, one hour", "description": "The sum of all volume from the spot markets of a set of trusted exchanges in units of U.S. dollars.", "product": "Market Data", "category": "Volume", "subcategory": "Trusted", "unit": "USD", "data_type": "decimal", "type": "Sum", "frequencies": [{"frequency": "1h", "assets": ["btc", "eth"]}], "display_name": "Trusted Spot Volume"}]}}