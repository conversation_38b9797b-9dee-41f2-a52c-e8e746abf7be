package io.coinmetrics.api.utils

import io.coinmetrics.api.ApiError
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test

class WildcardUtilsTest {
    private class TestWrapper(
        val value: String,
    ) {
        override fun toString() = value
    }

    private fun unwrapWildcards(
        items: Collection<String>,
        universeOfItems: List<String>,
    ): Pair<List<String>, Boolean> {
        val expanded =
            WildcardUtils
                .unwrapWildcards(items, "", universeOfItems.asSequence().constrainOnce())
                .getOrElse { error(it.toString()) }
                .let {
                    it.first.sorted() to it.second
                }
        val expandedWithItemParser =
            WildcardUtils
                .unwrapWildcards(items, "", universeOfItems.map { TestWrapper(it) }.asSequence().constrainOnce(), itemParser = {
                    TestWrapper(it)
                })
                .getOrElse { error(it.toString()) }
                .let {
                    it.first.map { it.value }.sorted() to it.second
                }
        assertEquals(expanded, expandedWithItemParser)
        return expanded
    }

    @Test
    fun `exchange level filtering`() {
        assertEquals(
            listOf("exchange1-BTCUSDT-future", "exchange1-btc-usd-spot") to true,
            unwrapWildcards(
                listOf("exchange1-*"),
                listOf("exchange1-btc-usd-spot", "exchange2-btc-usd-spot", "exchange1-BTCUSDT-future"),
            ),
        )
    }

    @Test
    fun `market type filtering`() {
        assertEquals(
            listOf("exchange1-btc-usd-spot", "exchange2-eth-usd-spot") to true,
            unwrapWildcards(
                listOf("*-spot"),
                listOf("exchange1-btc-usd-spot", "exchange2-eth-usd-spot", "exchange1-BTCUSDT-future"),
            ),
        )
    }

    @Test
    fun `instrument filtering`() {
        assertEquals(
            listOf("exchange1-BTCUSDT-future", "exchange1-ETHUSDT-future") to true,
            unwrapWildcards(
                listOf("exchange1-*USDT-future"),
                listOf(
                    "exchange1-btc-usd-spot",
                    "exchange2-btc-usd-spot",
                    "exchange1-BTCUSDT-future",
                    "exchange1-ETHUSDT-future",
                    "exchange1-ETHBTC-future",
                ),
            ),
        )
    }

    @Test
    fun `non-patterns`() {
        val input = (1..100).map { "item-$it" }.sorted()
        assertEquals(
            input to false,
            unwrapWildcards(input, listOf()),
        )
        assertEquals(
            input to false,
            unwrapWildcards(input, listOf("a", "b")),
        )
    }

    @Test
    fun `non-patterns with non-expandable patterns`() {
        val input = (1..100).map { "item-$it" }.sorted()
        assertEquals(
            input to true,
            unwrapWildcards(input + listOf("foo*bar"), listOf()),
        )
        assertEquals(
            input to true,
            unwrapWildcards(input + listOf("foo*bar"), listOf("a", "b")),
        )
    }

    @Test
    fun `multiple patterns and non-patterns`() {
        assertEquals(
            listOf(
                "asdbaz",
                "fobar",
                "foo_bar",
                "foobar",
                "foobar",
                "foobaz",
                "fr",
                "qux",
                "quxxx",
            ) to true,
            unwrapWildcards(
                listOf(
                    "foo*bar",
                    "zoo*baz",
                    "foobar*foobar",
                    "foobar",
                    "qux*",
                    "*baz",
                    "f*r",
                ),
                listOf(
                    "foobar",
                    "foo_bar",
                    "qux",
                    "zoobar",
                    "foobaz",
                    "fobar",
                    "quxxx",
                    "asdbaz",
                    "fr",
                ),
            ),
        )
    }

    @Test
    fun `returns bad parameter if multiple asterisks in pattern`() {
        for (pattern in listOf("**", "a*b*", "a**b", "**x")) {
            val expected =
                ApiError
                    .BadParameter(
                        "test-param",
                        "Only one '*' per pattern is supported in the 'test-param' parameter.",
                    ).toResponseObject()
            assertEquals(
                expected,
                WildcardUtils.unwrapWildcards(listOf(pattern), "test-param", sequenceOf<String>()).getFailureOrNull()?.toResponseObject(),
            )
        }
    }

    @Test
    fun `returns empty list if patterns expand to nothing`() {
        val (actualList, actualPatternRequested) = WildcardUtils.unwrapWildcards(listOf("*"), "test-param", sequenceOf()).getOrNull()!!

        assertEquals(actualList, emptyList<String>())
        assertTrue(actualPatternRequested)
    }

    @Test
    fun `non-local return from itemParser`() {
        assertEquals(
            42,
            run {
                WildcardUtils.unwrapWildcards(listOf("x"), "", sequenceOf<String>(), itemParser = { return@run 42 })
            },
        )
        assertEquals(
            42,
            run {
                WildcardUtils.unwrapWildcards(listOf("x", "*"), "", sequenceOf<String>(), itemParser = { return@run 42 })
            },
        )
    }
}
