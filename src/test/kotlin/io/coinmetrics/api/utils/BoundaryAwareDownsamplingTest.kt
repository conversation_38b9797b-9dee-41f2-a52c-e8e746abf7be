package io.coinmetrics.api.utils

import io.coinmetrics.api.utils.DataUtils.boundaryAwareDownsample
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Assertions.assertEquals
import java.time.Instant
import java.time.temporal.ChronoUnit

class BoundaryAwareDownsamplingTest {

    data class TestRecord(override val time: Instant, val value: String) : WithTime

    @Test
    fun `should select records closest to hourly boundaries`() = runBlocking {
        // Test data similar to the problem described
        val testData = listOf(
            TestRecord(Instant.parse("2025-05-21T00:59:59.964640Z"), "00:59:59"),
            TestRecord(Instant.parse("2025-05-21T01:59:59.871018Z"), "01:59:59"),
            TestRecord(Instant.parse("2025-05-21T15:00:03.174476Z"), "15:00:03"), // 3 seconds after boundary
            TestRecord(Instant.parse("2025-05-21T15:59:59.964903Z"), "15:59:59"), // 37ms before next boundary - CLOSER!
            TestRecord(Instant.parse("2025-05-21T17:00:02.121097Z"), "17:00:02"), // 2 seconds after boundary
            TestRecord(Instant.parse("2025-05-21T17:59:59.971981Z"), "17:59:59"), // 28ms before next boundary - CLOSER!
            TestRecord(Instant.parse("2025-05-21T20:00:02.707587Z"), "20:00:02"), // 2.7 seconds after boundary
            TestRecord(Instant.parse("2025-05-21T20:59:59.960705Z"), "20:59:59"), // 39ms before next boundary - CLOSER!
        )

        val config = TimeUtils.StatefulDownSamplerConfig(
            pagingFromStart = true,
            comparisonFun = Comparator.naturalOrder(),
            nextTimeFun = { time -> time.truncatedTo(ChronoUnit.HOURS).plus(1, ChronoUnit.HOURS) }
        )

        val result = flowOf(*testData.toTypedArray())
            .boundaryAwareDownsample(config)
            .toList()

        // Expected results: should pick the records closest to each hourly boundary
        val expectedValues = listOf(
            "00:59:59", // closest to 01:00
            "01:59:59", // closest to 02:00
            "15:59:59", // closest to 16:00 (not 15:00:03)
            "17:59:59", // closest to 18:00 (not 17:00:02)
            "20:59:59", // closest to 21:00 (not 20:00:02)
        )

        assertEquals(expectedValues.size, result.size, "Should have correct number of results")
        
        result.forEachIndexed { index, record ->
            assertEquals(
                expectedValues[index], 
                record.value, 
                "Record at index $index should be ${expectedValues[index]} but was ${record.value}"
            )
        }
    }

    @Test
    fun `should handle single record per boundary`() = runBlocking {
        val testData = listOf(
            TestRecord(Instant.parse("2025-05-21T01:30:00Z"), "01:30:00"),
            TestRecord(Instant.parse("2025-05-21T02:45:00Z"), "02:45:00"),
            TestRecord(Instant.parse("2025-05-21T03:15:00Z"), "03:15:00"),
        )

        val config = TimeUtils.StatefulDownSamplerConfig(
            pagingFromStart = true,
            comparisonFun = Comparator.naturalOrder(),
            nextTimeFun = { time -> time.truncatedTo(ChronoUnit.HOURS).plus(1, ChronoUnit.HOURS) }
        )

        val result = flowOf(*testData.toTypedArray())
            .boundaryAwareDownsample(config)
            .toList()

        // Should include all records since there's only one per boundary
        assertEquals(3, result.size)
        assertEquals("01:30:00", result[0].value)
        assertEquals("02:45:00", result[1].value)
        assertEquals("03:15:00", result[2].value)
    }

    @Test
    fun `should handle records exactly on boundaries`() = runBlocking {
        val testData = listOf(
            TestRecord(Instant.parse("2025-05-21T01:00:00Z"), "01:00:00"), // exactly on boundary
            TestRecord(Instant.parse("2025-05-21T01:30:00Z"), "01:30:00"), // 30 minutes after
            TestRecord(Instant.parse("2025-05-21T02:00:00Z"), "02:00:00"), // exactly on boundary
        )

        val config = TimeUtils.StatefulDownSamplerConfig(
            pagingFromStart = true,
            comparisonFun = Comparator.naturalOrder(),
            nextTimeFun = { time -> time.truncatedTo(ChronoUnit.HOURS).plus(1, ChronoUnit.HOURS) }
        )

        val result = flowOf(*testData.toTypedArray())
            .boundaryAwareDownsample(config)
            .toList()

        // Should prefer records exactly on boundaries
        assertEquals(2, result.size)
        assertEquals("01:00:00", result[0].value) // closest to 01:00 boundary
        assertEquals("02:00:00", result[1].value) // exactly on 02:00 boundary
    }
}
