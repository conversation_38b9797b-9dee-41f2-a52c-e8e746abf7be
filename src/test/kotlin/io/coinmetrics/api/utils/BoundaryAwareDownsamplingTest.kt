package io.coinmetrics.api.utils

import io.coinmetrics.api.utils.DataUtils.boundaryAwareDownsample
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Assertions.assertEquals
import java.time.Instant
import java.time.temporal.ChronoUnit

class BoundaryAwareDownsamplingTest {

    data class TestRecord(override val time: Instant, val value: String) : WithTime

    @Test
    fun `should select records closest to hourly boundaries - real problem data`() = runBlocking {
        // Test data exactly from the problem described
        val testData = listOf(
            TestRecord(Instant.parse("2025-05-21T00:59:59.964640Z"), "00:59:59"),
            TestRecord(Instant.parse("2025-05-21T01:59:59.871018Z"), "01:59:59"),
            TestRecord(Instant.parse("2025-05-21T02:59:59.983335Z"), "02:59:59"),
            TestRecord(Instant.parse("2025-05-21T03:59:59.957179Z"), "03:59:59"),
            TestRecord(Instant.parse("2025-05-21T04:59:59.990160Z"), "04:59:59"),
            TestRecord(Instant.parse("2025-05-21T05:59:59.990548Z"), "05:59:59"),
            TestRecord(Instant.parse("2025-05-21T06:59:59.982003Z"), "06:59:59"),
            TestRecord(Instant.parse("2025-05-21T07:59:59.972245Z"), "07:59:59"),
            TestRecord(Instant.parse("2025-05-21T08:59:59.985095Z"), "08:59:59"),
            TestRecord(Instant.parse("2025-05-21T09:59:59.989766Z"), "09:59:59"),
            TestRecord(Instant.parse("2025-05-21T10:59:59.987527Z"), "10:59:59"),
            TestRecord(Instant.parse("2025-05-21T11:59:59.968081Z"), "11:59:59"),
            TestRecord(Instant.parse("2025-05-21T12:59:59.986768Z"), "12:59:59"),
            TestRecord(Instant.parse("2025-05-21T13:59:59.971911Z"), "13:59:59"),
            TestRecord(Instant.parse("2025-05-21T15:00:03.174476Z"), "15:00:03"), // 3.17 seconds after 15:00 boundary
            TestRecord(Instant.parse("2025-05-21T15:59:59.964903Z"), "15:59:59"), // 35ms before 16:00 boundary - CLOSER!
            TestRecord(Instant.parse("2025-05-21T17:00:02.121097Z"), "17:00:02"), // 2.12 seconds after 17:00 boundary
            TestRecord(Instant.parse("2025-05-21T17:59:59.971981Z"), "17:59:59"), // 28ms before 18:00 boundary - CLOSER!
            TestRecord(Instant.parse("2025-05-21T18:59:59.988299Z"), "18:59:59"),
            TestRecord(Instant.parse("2025-05-21T20:00:02.707587Z"), "20:00:02"), // 2.71 seconds after 20:00 boundary
            TestRecord(Instant.parse("2025-05-21T20:59:59.960705Z"), "20:59:59"), // 39ms before 21:00 boundary - CLOSER!
            TestRecord(Instant.parse("2025-05-21T21:59:59.970871Z"), "21:59:59"),
            TestRecord(Instant.parse("2025-05-21T22:59:59.940328Z"), "22:59:59"),
            TestRecord(Instant.parse("2025-05-21T23:59:59.889678Z"), "23:59:59"),
        )

        val config = TimeUtils.StatefulDownSamplerConfig(
            pagingFromStart = true,
            comparisonFun = Comparator.naturalOrder(),
            nextTimeFun = { time -> time.truncatedTo(ChronoUnit.HOURS).plus(1, ChronoUnit.HOURS) }
        )

        val result = flowOf(*testData.toTypedArray())
            .boundaryAwareDownsample(config)
            .toList()

        // Expected results: should pick the records closest to each hourly boundary
        // The missing records from your API response should now be included
        val expectedValues = listOf(
            "00:59:59", // closest to 01:00
            "01:59:59", // closest to 02:00
            "02:59:59", // closest to 03:00
            "03:59:59", // closest to 04:00
            "04:59:59", // closest to 05:00
            "05:59:59", // closest to 06:00
            "06:59:59", // closest to 07:00
            "07:59:59", // closest to 08:00
            "08:59:59", // closest to 09:00
            "09:59:59", // closest to 10:00
            "10:59:59", // closest to 11:00
            "11:59:59", // closest to 12:00
            "12:59:59", // closest to 13:00
            "13:59:59", // closest to 14:00
            "15:00:03", // closest to 15:00 (only record for this boundary)
            "15:59:59", // closest to 16:00 (SHOULD BE INCLUDED - was missing before!)
            "17:00:02", // closest to 17:00 (only record for this boundary)
            "17:59:59", // closest to 18:00 (SHOULD BE INCLUDED - was missing before!)
            "18:59:59", // closest to 19:00
            "20:00:02", // closest to 20:00 (only record for this boundary)
            "20:59:59", // closest to 21:00 (SHOULD BE INCLUDED - was missing before!)
            "21:59:59", // closest to 22:00
            "22:59:59", // closest to 23:00
            "23:59:59", // closest to 24:00
        )

        assertEquals(expectedValues.size, result.size, "Should have correct number of results")

        result.forEachIndexed { index, record ->
            assertEquals(
                expectedValues[index],
                record.value,
                "Record at index $index should be ${expectedValues[index]} but was ${record.value}"
            )
        }
    }

    @Test
    fun `should handle single record per boundary`() = runBlocking {
        val testData = listOf(
            TestRecord(Instant.parse("2025-05-21T01:30:00Z"), "01:30:00"),
            TestRecord(Instant.parse("2025-05-21T02:45:00Z"), "02:45:00"),
            TestRecord(Instant.parse("2025-05-21T03:15:00Z"), "03:15:00"),
        )

        val config = TimeUtils.StatefulDownSamplerConfig(
            pagingFromStart = true,
            comparisonFun = Comparator.naturalOrder(),
            nextTimeFun = { time -> time.truncatedTo(ChronoUnit.HOURS).plus(1, ChronoUnit.HOURS) }
        )

        val result = flowOf(*testData.toTypedArray())
            .boundaryAwareDownsample(config)
            .toList()

        // Should include all records since there's only one per boundary
        assertEquals(3, result.size)
        assertEquals("01:30:00", result[0].value)
        assertEquals("02:45:00", result[1].value)
        assertEquals("03:15:00", result[2].value)
    }

    @Test
    fun `should handle records exactly on boundaries`() = runBlocking {
        val testData = listOf(
            TestRecord(Instant.parse("2025-05-21T01:00:00Z"), "01:00:00"), // exactly on boundary
            TestRecord(Instant.parse("2025-05-21T01:30:00Z"), "01:30:00"), // 30 minutes after
            TestRecord(Instant.parse("2025-05-21T02:00:00Z"), "02:00:00"), // exactly on boundary
        )

        val config = TimeUtils.StatefulDownSamplerConfig(
            pagingFromStart = true,
            comparisonFun = Comparator.naturalOrder(),
            nextTimeFun = { time -> time.truncatedTo(ChronoUnit.HOURS).plus(1, ChronoUnit.HOURS) }
        )

        val result = flowOf(*testData.toTypedArray())
            .boundaryAwareDownsample(config)
            .toList()

        // Should prefer records exactly on boundaries
        assertEquals(2, result.size)
        assertEquals("01:00:00", result[0].value) // closest to 01:00 boundary
        assertEquals("02:00:00", result[1].value) // exactly on 02:00 boundary
    }
}
