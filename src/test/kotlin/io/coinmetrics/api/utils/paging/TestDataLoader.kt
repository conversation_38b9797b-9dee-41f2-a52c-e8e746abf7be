package io.coinmetrics.api.utils.paging

import io.coinmetrics.api.models.PagingFrom
import io.coinmetrics.api.utils.RangeQuery
import java.math.BigInteger

/**
 * Emulates database query.
 */
internal fun <T> createTestLoader(
    list: List<T>,
    pagingFrom: PagingFrom,
    originalQuery: RangeQuery.BigIntegerRangeQuery,
    keyValueExtractor: (T) -> BigInteger,
): suspend (PageToken.BigIntegerPageToken?, Int) -> List<T> =
    { state, limit ->
        val query = originalQuery.withPageToken(state)
        list
            .filter {
                val keyValue = keyValueExtractor.invoke(it)
                (query.startKey == null || (query.startInclusive && keyValue >= query.startKey) || (!query.startInclusive && keyValue > query.startKey)) &&
                    (query.endKey == null || (query.endInclusive && keyValue <= query.endKey) || (!query.endInclusive && keyValue < query.endKey))
            }.let {
                if (pagingFrom == PagingFrom.START) {
                    it
                } else {
                    it.asReversed()
                }
            }.take(limit)
    }
