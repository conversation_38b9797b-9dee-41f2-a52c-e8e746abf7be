package io.coinmetrics.api.utils.paging

import io.coinmetrics.api.models.PagingFrom
import io.coinmetrics.api.models.PagingFrom.END
import io.coinmetrics.api.models.PagingFrom.START
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertIterableEquals
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

class ListPagingUtilsTest {
    companion object {
        val testData = listOf('a', 'b', 'c', 'd', 'e')

        @JvmStatic
        private fun pageLoaderTestCases(): List<Arguments> =
            listOf(
                Arguments.arguments(-1, 0, START, emptyList<Char>()),
                Arguments.arguments(-1, 0, END, emptyList<Char>()),
                Arguments.arguments(0, 0, START, emptyList<Char>()),
                Arguments.arguments(0, 0, END, emptyList<Char>()),
                Arguments.arguments(0, 2, START, listOf('a', 'b')),
                Arguments.arguments(0, 2, END, listOf('d', 'e')),
                Arguments.arguments(2, 2, START, listOf('c', 'd')),
                Arguments.arguments(2, 2, END, listOf('b', 'c')),
                Arguments.arguments(4, 2, START, listOf('e')),
                Arguments.arguments(4, 2, END, listOf('a')),
                Arguments.arguments(0, 100, START, listOf('a', 'b', 'c', 'd', 'e')),
                Arguments.arguments(0, 100, END, listOf('a', 'b', 'c', 'd', 'e')),
                Arguments.arguments(1, 100, START, listOf('b', 'c', 'd', 'e')),
                Arguments.arguments(1, 100, END, listOf('a', 'b', 'c', 'd')),
                Arguments.arguments(2, 100, START, listOf('c', 'd', 'e')),
                Arguments.arguments(2, 100, END, listOf('a', 'b', 'c')),
                Arguments.arguments(3, 100, START, listOf('d', 'e')),
                Arguments.arguments(3, 100, END, listOf('a', 'b')),
                Arguments.arguments(4, 100, START, listOf('e')),
                Arguments.arguments(4, 100, END, listOf('a')),
                Arguments.arguments(5, 100, START, emptyList<Char>()),
                Arguments.arguments(5, 100, END, emptyList<Char>()),
                Arguments.arguments(6, 100, START, emptyList<Char>()),
                Arguments.arguments(6, 100, END, emptyList<Char>()),
                Arguments.arguments(6, 0, START, emptyList<Char>()),
                Arguments.arguments(6, 0, END, emptyList<Char>()),
            )

        @JvmStatic
        private fun pageTokenResolverTestCases(): List<Arguments> =
            listOf(
                Arguments.arguments(null, 0, 4, 0),
                Arguments.arguments(null, 1, 4, 1),
                Arguments.arguments(null, 100, 4, 4),
                Arguments.arguments(0, 0, 4, 0),
                Arguments.arguments(0, 1, 4, 1),
                Arguments.arguments(0, 100, 4, 4),
                Arguments.arguments(100, 100, 4, 4),
                Arguments.arguments(-1, 100, 4, 4),
                Arguments.arguments(-100, 100, 4, 4),
            )
    }

    @ParameterizedTest
    @MethodSource("pageTokenResolverTestCases")
    fun testPageTokenValueResolver(
        state: Int?,
        pageSize: Int,
        lastIndex: Int,
        expected: Int,
    ) {
        val actual = ListPagingUtils.pageTokenValueResolver(state, pageSize, lastIndex)
        assertEquals(expected, actual.value)
    }

    @ParameterizedTest
    @MethodSource("pageLoaderTestCases")
    fun testPageLoader(
        startIndex: Int,
        pageSize: Int,
        pagingFrom: PagingFrom,
        expected: List<Char>,
    ) = runBlocking {
        val pageToken = PageToken.IntPageToken(startIndex)
        val pageLoader = ListPagingUtils.pageLoader(testData, pagingFrom)
        val actual = pageLoader(pageToken, pageSize)
        assertIterableEquals(expected, actual)
    }
}
