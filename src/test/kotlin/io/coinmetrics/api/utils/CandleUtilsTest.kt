package io.coinmetrics.api.utils

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.ApiError.UnsupportedParameterValue
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.models.PagingFrom
import io.coinmetrics.api.utils.CandleUtils.adjustForFrequency
import io.coinmetrics.api.utils.RangeQuery.TimeRangeQuery
import io.coinmetrics.api.utils.TimeUtils.NormalizedFrequencyOffset
import io.coinmetrics.api.utils.TimeUtils.NormalizedFrequencyOffset.Companion.DEFAULT
import org.junit.jupiter.api.Assertions.assertAll
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.Arguments.of
import org.junit.jupiter.params.provider.MethodSource
import java.time.Instant
import java.time.ZoneId
import java.time.temporal.ChronoUnit.HOURS

class CandleUtilsTest {
    @ParameterizedTest
    @MethodSource("parseFrequencyTestArgs")
    fun parseFrequency(
        frequency: String,
        timezone: String,
        isCommunity: Boolean,
        expectedResultSuccess: Pair<String, NormalizedFrequencyOffset>?,
        expectedResultFailure: ApiError?,
    ) {
        when (val actualResult = CandleUtils.parseFrequency(frequency, timezone, isCommunity)) {
            is FunctionResult.Success -> {
                assertAll(
                    { assertEquals(expectedResultSuccess, actualResult.value) },
                    {
                        val (_, expectedOffset) = expectedResultSuccess!!
                        val (_, actualOffset) = actualResult.value
                        assertEquals(expectedOffset.default, actualOffset.default)
                    },
                )
            }
            is FunctionResult.Failure -> assertEquals(expectedResultFailure?.message, actualResult.value.message)
        }
    }

    @ParameterizedTest
    @MethodSource("adjustForFrequencyTestArgs")
    fun adjustForFrequency(
        offset: NormalizedFrequencyOffset,
        timezone: String,
        query: TimeRangeQuery,
        expected: Instant,
    ) {
        val actual = query.adjustForFrequency(offset, timezone)
        assertEquals(expected, actual.endKey)
    }

    companion object {
        private const val UTC = "UTC"

        @JvmStatic
        private fun parseFrequencyTestArgs(): List<Arguments> =
            listOf(
                of("1d", UTC, true, "1d" to DEFAULT, null),
                of("1d", UTC, false, "1d" to DEFAULT, null),
                of("1d", "America/New_York", true, "1d" to NormalizedFrequencyOffset(0, 0, "America/New_York"), null),
                of("1d", "America/New_York", false, "1d" to NormalizedFrequencyOffset(0, 0, "America/New_York"), null),
                of("1d-00:00", UTC, false, "1d" to NormalizedFrequencyOffset(0, 0, null), null),
                of("1h-12:00", UTC, true, null, UnsupportedParameterValue("frequency", "1h-12:00")),
                of("1h-13:00", UTC, false, null, UnsupportedParameterValue("frequency", "1h-13:00")),
                of("1d-12:30", UTC, true, null, UnsupportedParameterValue("frequency", "1d-12:30")),
                of("1d-13:30", UTC, false, null, UnsupportedParameterValue("frequency", "1d-13:30")),
                of("1d-12+00", UTC, true, null, UnsupportedParameterValue("frequency", "1d-12+00")),
                of("1d-13+00", UTC, false, null, UnsupportedParameterValue("frequency", "1d-13+00")),
                of("1d-12:00", UTC, true, null, ApiError.ForbiddenWithMessage("Requested frequency '1d-12:00' is not available with supplied credentials.")),
                of("1d-13:00", UTC, false, "1d" to NormalizedFrequencyOffset(13, 0, null), null),
            )

        @JvmStatic
        private fun adjustForFrequencyTestArgs(): List<Arguments> {
            val now = Instant.now()
            val timeRangeQuery =
                TimeRangeQuery(
                    startKey = now.minus(1_000L, HOURS),
                    startInclusive = true,
                    endKey = now,
                    endInclusive = true,
                    pagingFrom = PagingFrom.END,
                )
            return listOf(
                of(DEFAULT, UTC, timeRangeQuery, timeRangeQuery.endKey),
                of(
                    NormalizedFrequencyOffset(12, 0, null),
                    UTC,
                    timeRangeQuery,
                    expectedEndKey().invoke(timeRangeQuery.endKey, ZoneId.of(UTC)),
                ),
                of(
                    NormalizedFrequencyOffset(12, 0, "America/New_York"),
                    UTC,
                    timeRangeQuery,
                    expectedEndKey().invoke(timeRangeQuery.endKey, ZoneId.of("America/New_York")),
                ),
                of(
                    NormalizedFrequencyOffset(12, 0, UTC),
                    "America/New_York",
                    timeRangeQuery,
                    expectedEndKey().invoke(timeRangeQuery.endKey, ZoneId.of(UTC)),
                ),
            )
        }

        private fun expectedEndKey(): (Instant, ZoneId) -> Instant =
            { endKey, tz ->
                endKey
                    .atZone(tz)
                    .toLocalDateTime()
                    .plusHours(23)
                    .atZone(tz)
                    .toInstant()
            }
    }
}
