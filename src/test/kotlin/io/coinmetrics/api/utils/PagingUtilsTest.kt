package io.coinmetrics.api.utils

import io.coinmetrics.api.models.PagingFrom
import io.coinmetrics.api.utils.paging.PageToken
import io.coinmetrics.api.utils.paging.createTestLoader
import io.coinmetrics.api.utils.paging.getPage
import io.coinmetrics.api.utils.paging.getPageGrouped
import io.coinmetrics.api.utils.streams.BufferedSuspendableStream
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.yield
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import java.util.TreeMap
import java.util.concurrent.CopyOnWriteArrayList
import java.util.concurrent.Executors

@Suppress("DEPRECATION")
internal class PagingUtilsTest {
    private val pageTokenValuesResolver: (Int) -> PageToken.BigIntegerPageToken = { value ->
        PageToken.BigIntegerPageToken(value.toBigInteger())
    }

    @Test
    internal fun `load empty page`() {
        runBlocking {
            val data = emptyList<Int>()
            val requestedPageSize = 2
            val pagingFrom = PagingFrom.START
            val filter: (Int) -> Boolean = { it % 2 == 0 }

            val rangeQuery =
                RangeQuery.BigIntegerRangeQuery(
                    startKey = null,
                    startInclusive = true,
                    endKey = null,
                    endInclusive = true,
                    pagingFrom = pagingFrom,
                )

            val pageLoader = createTestLoader(data, pagingFrom, rangeQuery) { it.toBigInteger() }

            val stream =
                BufferedSuspendableStream(
                    initialState = null,
                    bufferSize = 2,
                    stateResolver = pageTokenValuesResolver,
                    bufferLoader = pageLoader,
                ).filter(filter)
            val page = stream.getPage(requestedPageSize, pagingFrom == PagingFrom.START)

            assertTrue(page.items.isEmpty())
            assertNull(page.nextPageToken)
        }
    }

    @Test
    internal fun `load page with even numbers`() {
        runBlocking {
            val data = listOf(1, 2, 3, 4, 5, 6)
            val requestedPageSize = 2
            val pagingFrom = PagingFrom.START
            val filter: (Int) -> Boolean = { it % 2 == 0 }

            val rangeQuery =
                RangeQuery.BigIntegerRangeQuery(
                    startKey = null,
                    startInclusive = true,
                    endKey = null,
                    endInclusive = true,
                    pagingFrom = pagingFrom,
                )

            val pageLoader = createTestLoader(data, pagingFrom, rangeQuery) { it.toBigInteger() }

            val stream =
                BufferedSuspendableStream(
                    initialState = null,
                    bufferSize = 2,
                    stateResolver = pageTokenValuesResolver,
                    bufferLoader = pageLoader,
                ).filter(filter)
            val page = stream.getPage(requestedPageSize, pagingFrom == PagingFrom.START)

            assertEquals(listOf(2, 4), page.items)
            assertEquals("NA", page.nextPageToken)

            // next page
            val stream2 =
                BufferedSuspendableStream(
                    initialState = PageToken.BigIntegerPageToken.parse(page.nextPageToken!!),
                    bufferSize = 2,
                    stateResolver = pageTokenValuesResolver,
                    bufferLoader = pageLoader,
                ).filter(filter)
            val page2 = stream2.getPage(requestedPageSize, pagingFrom == PagingFrom.START)

            assertEquals(listOf(6), page2.items)
            assertNull(page2.nextPageToken)
        }
    }

    @Test
    fun `load page with even numbers and paging from end`() {
        runBlocking {
            val data = listOf(1, 2, 3, 4, 5, 6)
            val requestedPageSize = 2
            val pagingFrom = PagingFrom.END
            val filter: (Int) -> Boolean = { it % 2 == 0 }

            val rangeQuery =
                RangeQuery.BigIntegerRangeQuery(
                    startKey = null,
                    startInclusive = true,
                    endKey = null,
                    endInclusive = true,
                    pagingFrom = pagingFrom,
                )

            val pageLoader = createTestLoader(data, pagingFrom, rangeQuery) { it.toBigInteger() }

            val stream =
                BufferedSuspendableStream(
                    initialState = null,
                    bufferSize = 2,
                    stateResolver = pageTokenValuesResolver,
                    bufferLoader = pageLoader,
                ).filter(filter)
            val page = stream.getPage(requestedPageSize, pagingFrom == PagingFrom.START)

            assertEquals(listOf(4, 6), page.items)
            assertEquals("NA", page.nextPageToken)

            // next page
            val stream2 =
                BufferedSuspendableStream(
                    initialState = PageToken.BigIntegerPageToken.parse(page.nextPageToken!!),
                    bufferSize = 2,
                    stateResolver = pageTokenValuesResolver,
                    bufferLoader = pageLoader,
                ).filter(filter)
            val page2 = stream2.getPage(requestedPageSize, pagingFrom == PagingFrom.START)

            assertEquals(listOf(2), page2.items)
            assertNull(page2.nextPageToken)
        }
    }

    @Test
    internal fun `load page with odd numbers`() {
        runBlocking {
            val data = listOf(1, 2, 3, 4, 5, 6)
            val requestedPageSize = 2
            val pagingFrom = PagingFrom.START
            val filter: (Int) -> Boolean = { it % 2 != 0 }

            val rangeQuery =
                RangeQuery.BigIntegerRangeQuery(
                    startKey = null,
                    startInclusive = true,
                    endKey = null,
                    endInclusive = true,
                    pagingFrom = pagingFrom,
                )

            val pageLoader = createTestLoader(data, pagingFrom, rangeQuery) { it.toBigInteger() }

            val stream =
                BufferedSuspendableStream(
                    initialState = null,
                    bufferSize = 2,
                    stateResolver = pageTokenValuesResolver,
                    bufferLoader = pageLoader,
                ).filter(filter)
            val page = stream.getPage(requestedPageSize, pagingFrom == PagingFrom.START)

            assertEquals(listOf(1, 3), page.items)
            assertEquals("Mw", page.nextPageToken)

            // next page
            val stream2 =
                BufferedSuspendableStream(
                    initialState = PageToken.BigIntegerPageToken.parse(page.nextPageToken!!),
                    bufferSize = 2,
                    stateResolver = pageTokenValuesResolver,
                    bufferLoader = pageLoader,
                ).filter(filter)
            val page2 = stream2.getPage(requestedPageSize, pagingFrom == PagingFrom.START)

            assertEquals(listOf(5), page2.items)
            assertNull(page2.nextPageToken)
        }
    }

    @Test
    internal fun `load page with odd numbers and paging from end`() {
        runBlocking {
            val data = listOf(1, 2, 3, 4, 5, 6)
            val requestedPageSize = 2
            val pagingFrom = PagingFrom.END
            val filter: (Int) -> Boolean = { it % 2 != 0 }

            val rangeQuery =
                RangeQuery.BigIntegerRangeQuery(
                    startKey = null,
                    startInclusive = true,
                    endKey = null,
                    endInclusive = true,
                    pagingFrom = pagingFrom,
                )

            val pageLoader = createTestLoader(data, pagingFrom, rangeQuery) { it.toBigInteger() }

            val stream =
                BufferedSuspendableStream(
                    initialState = null,
                    bufferSize = 2,
                    stateResolver = pageTokenValuesResolver,
                    bufferLoader = pageLoader,
                ).filter(filter)
            val page = stream.getPage(requestedPageSize, pagingFrom == PagingFrom.START)

            assertEquals(listOf(3, 5), page.items)
            assertEquals("Mw", page.nextPageToken)

            // next page
            val stream2 =
                BufferedSuspendableStream(
                    initialState = PageToken.BigIntegerPageToken.parse(page.nextPageToken!!),
                    bufferSize = 2,
                    stateResolver = pageTokenValuesResolver,
                    bufferLoader = pageLoader,
                ).filter(filter)
            val page2 = stream2.getPage(requestedPageSize, pagingFrom == PagingFrom.START)

            assertEquals(listOf(1), page2.items)
            assertNull(page2.nextPageToken)
        }
    }

    @Test
    fun `database data updated in real-time`() {
        runBlocking {
            val data = CopyOnWriteArrayList(listOf(1, 2, 3))
            val pageSize = 3
            val elements = 100
            val pagingFrom = PagingFrom.START

            val rangeQuery =
                RangeQuery.BigIntegerRangeQuery(
                    startKey = null,
                    startInclusive = true,
                    endKey = null,
                    endInclusive = true,
                    pagingFrom = pagingFrom,
                )

            val pageLoader = createTestLoader(data, pagingFrom, rangeQuery) { it.toBigInteger() }

            Executors.newSingleThreadExecutor().asCoroutineDispatcher().use { backgroundDispatcher ->
                // new items are added in the background thread
                launch(backgroundDispatcher) {
                    repeat(elements - data.size) { n ->
                        data.add(4 + n)
                        delay(10)
                    }
                }

                // repeat query & fetch pages until all data is received
                val receivedItems = HashSet<Int>()
                var pageToken: String? = null
                while (true) {
                    val stream =
                        BufferedSuspendableStream(
                            initialState = pageToken?.let { PageToken.BigIntegerPageToken.parse(it) },
                            bufferSize = 2,
                            stateResolver = pageTokenValuesResolver,
                            bufferLoader = pageLoader,
                        )
                    val page = stream.getPage(pageSize, pagingFrom == PagingFrom.START)

                    receivedItems.addAll(page.items)
                    if (page.nextPageToken == null) {
                        if (receivedItems.size == elements) break
                        // else repeat query again
                    } else {
                        // fetch a next page
                        pageToken = page.nextPageToken
                    }
                    yield()
                }
            }
        }
    }

    @Test
    internal fun `load page grouped by key`() {
        runBlocking {
            val data = listOf(1 to 2, 1 to 3, 2 to 4, 3 to 5)
            val requestedPageSize = 2
            val pagingFrom = PagingFrom.START

            val rangeQuery =
                RangeQuery.BigIntegerRangeQuery(
                    startKey = null,
                    startInclusive = true,
                    endKey = null,
                    endInclusive = true,
                    pagingFrom = pagingFrom,
                )

            val pageTokenValuesResolver: (Pair<Int, Int>) -> PageToken.BigIntegerPageToken = { value ->
                PageToken.BigIntegerPageToken(value.first.toBigInteger())
            }

            val pageLoader: suspend (PageToken.BigIntegerPageToken?, Int) -> List<Pair<Int, Int>> =
                createTestLoader(data, pagingFrom, rangeQuery) { it.first.toBigInteger() }

            val stream =
                BufferedSuspendableStream(
                    initialState = null,
                    bufferSize = 2,
                    stateResolver = pageTokenValuesResolver,
                    bufferLoader = pageLoader,
                )

            val page = stream.getPageGrouped(requestedPageSize, pagingFrom) { it.first }
            val items: List<Map.Entry<Int, List<Pair<Int, Int>>>> = page.items

            assertEquals(
                mapOf(
                    1 to listOf(1 to 2, 1 to 3),
                    2 to listOf(2 to 4),
                ).let { TreeMap(it) }
                    .entries
                    .toList(),
                items,
            )
            assertEquals("Mg", page.nextPageToken)
        }
    }

    @Test
    internal fun `load page grouped by key paging from end`() {
        runBlocking {
            val data = listOf(1 to 2, 1 to 3, 2 to 4, 3 to 5)
            val requestedPageSize = 2
            val pagingFrom = PagingFrom.END

            val rangeQuery =
                RangeQuery.BigIntegerRangeQuery(
                    startKey = null,
                    startInclusive = true,
                    endKey = null,
                    endInclusive = true,
                    pagingFrom = pagingFrom,
                )

            val pageTokenValuesResolver: (Pair<Int, Int>) -> PageToken.BigIntegerPageToken = { value ->
                PageToken.BigIntegerPageToken(value.first.toBigInteger())
            }

            val pageLoader: suspend (PageToken.BigIntegerPageToken?, Int) -> List<Pair<Int, Int>> =
                createTestLoader(data, pagingFrom, rangeQuery) { it.first.toBigInteger() }

            val stream =
                BufferedSuspendableStream(
                    initialState = null,
                    bufferSize = 2,
                    stateResolver = pageTokenValuesResolver,
                    bufferLoader = pageLoader,
                )

            val page = stream.getPageGrouped(requestedPageSize, pagingFrom) { it.first }
            val items: List<Map.Entry<Int, List<Pair<Int, Int>>>> = page.items

            assertEquals(
                mapOf(
                    2 to listOf(2 to 4),
                    3 to listOf(3 to 5),
                ).let { TreeMap(it) }
                    .entries
                    .toList(),
                items,
            )
            assertEquals("Mg", page.nextPageToken)

            val nextPageStream =
                BufferedSuspendableStream(
                    initialState = page.nextPageToken?.let { PageToken.BigIntegerPageToken.parse(it) },
                    bufferSize = 2,
                    stateResolver = pageTokenValuesResolver,
                    bufferLoader = pageLoader,
                )

            val nextPage = nextPageStream.getPageGrouped(requestedPageSize, pagingFrom) { it.first }
            val nextItems: List<Map.Entry<Int, List<Pair<Int, Int>>>> = nextPage.items

            assertEquals(
                mapOf(
                    1 to listOf(1 to 3, 1 to 2),
                ).let { TreeMap(it) }
                    .entries
                    .toList(),
                nextItems,
            )
            assertNull(nextPage.nextPageToken)
        }
    }
}
