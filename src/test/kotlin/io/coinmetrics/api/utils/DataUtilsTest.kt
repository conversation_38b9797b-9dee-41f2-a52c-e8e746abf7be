package io.coinmetrics.api.utils

import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.utils.DataUtils.adjust
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.fail
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import java.time.Instant
import java.util.stream.Stream

class DataUtilsTest {
    val instantNow = Instant.now()
    val instant2020 = Instant.parse("2020-01-01T00:00:00Z")
    val instant2021 = Instant.parse("2021-01-01T00:00:00Z")

    @Test
    fun `all params null`() {
        val now = Instant.now()
        when (
            val result =
                DataUtils.getStartEndTimeLimitedByBoundaries(
                    null,
                    null,
                    "UTC",
                    false,
                    false,
                    null,
                    null,
                )
        ) {
            is FunctionResult.Success -> {
                assertThat(result.value.first).isEqualTo(Instant.EPOCH)
                assertThat(result.value.second).isAfterOrEqualTo(now)
            }
            is FunctionResult.Failure -> fail("Should not happen")
        }
    }

    @Test
    fun `start param invalid`() {
        when (
            val result =
                DataUtils.getStartEndTimeLimitedByBoundaries(
                    "invalid",
                    null,
                    "UTC",
                    false,
                    false,
                    null,
                    null,
                )
        ) {
            is FunctionResult.Success -> fail("Should not happen")
            is FunctionResult.Failure -> assertThat(result.value.type).isEqualTo("bad_parameter")
        }
    }

    @Test
    fun `end param invalid`() {
        when (
            val result =
                DataUtils.getStartEndTimeLimitedByBoundaries(
                    null,
                    "invalid",
                    "UTC",
                    false,
                    false,
                    null,
                    null,
                )
        ) {
            is FunctionResult.Success -> fail("Should not happen")
            is FunctionResult.Failure -> assertThat(result.value.type).isEqualTo("bad_parameter")
        }
    }

    @Test
    fun `start param after end param`() {
        when (
            val result =
                DataUtils.getStartEndTimeLimitedByBoundaries(
                    "2010-11-15T22:26:23",
                    "2010-11-15T22:26:20",
                    "UTC",
                    false,
                    false,
                    null,
                    null,
                )
        ) {
            is FunctionResult.Success -> fail("Should not happen")
            is FunctionResult.Failure -> assertThat(result.value.type).isEqualTo("bad_parameter")
        }
    }

    @Test
    fun `end param null and no configured boundaries`() {
        when (
            val result =
                DataUtils.getStartEndTimeLimitedByBoundaries(
                    "2014-04-22T07:57:52.000000000Z",
                    null,
                    "UTC",
                    false,
                    false,
                    null,
                    null,
                )
        ) {
            is FunctionResult.Success -> assertThat(result.value.first).isEqualTo(Instant.parse("2014-04-22T07:57:52.000000000Z"))
            is FunctionResult.Failure -> fail("Should not happen")
        }
    }

    @Test
    fun `start param null and no configured boundaries`() {
        when (
            val result =
                DataUtils.getStartEndTimeLimitedByBoundaries(
                    null,
                    "2014-04-22T07:57:52.000000000Z",
                    "UTC",
                    false,
                    false,
                    null,
                    null,
                )
        ) {
            is FunctionResult.Success -> assertThat(result.value.second).isEqualTo(Instant.parse("2014-04-22T07:57:52.000000000Z"))
            is FunctionResult.Failure -> fail("Should not happen")
        }
    }

    @Test
    fun `start and end params valid and no configured boundaries`() {
        when (
            val result =
                DataUtils.getStartEndTimeLimitedByBoundaries(
                    "2014-04-22T06:57:52.000000000Z",
                    "2014-04-22T07:57:52.000000000Z",
                    "UTC",
                    false,
                    false,
                    null,
                    null,
                )
        ) {
            is FunctionResult.Success ->
                assertThat(result.value).isEqualTo(
                    Instant.parse("2014-04-22T06:57:52.000000000Z") to Instant.parse("2014-04-22T07:57:52.000000000Z"),
                )
            is FunctionResult.Failure -> fail("Should not happen")
        }
    }

    @Test
    fun `start and end params valid and start boundary before start`() {
        when (
            val result =
                DataUtils.getStartEndTimeLimitedByBoundaries(
                    "2021-04-22T06:57:52.000000000Z",
                    "2021-04-22T07:57:52.000000000Z",
                    "UTC",
                    false,
                    false,
                    instant2020,
                    null,
                )
        ) {
            is FunctionResult.Success ->
                assertThat(result.value).isEqualTo(
                    Instant.parse("2021-04-22T06:57:52.000000000Z") to Instant.parse("2021-04-22T07:57:52.000000000Z"),
                )
            is FunctionResult.Failure -> fail("Should not happen")
        }
    }

    @Test
    fun `start and end params valid and start boundary after start`() {
        when (
            val result =
                DataUtils.getStartEndTimeLimitedByBoundaries(
                    "2019-04-22T06:57:52.000000000Z",
                    "2021-04-22T07:57:52.000000000Z",
                    "UTC",
                    false,
                    false,
                    instant2020,
                    null,
                )
        ) {
            is FunctionResult.Success ->
                assertThat(result.value).isEqualTo(
                    instant2020 to Instant.parse("2021-04-22T07:57:52.000000000Z"),
                )
            is FunctionResult.Failure -> fail("Should not happen")
        }
    }

    @Test
    fun `start and end params valid and end boundary after both`() {
        when (
            val result =
                DataUtils.getStartEndTimeLimitedByBoundaries(
                    "2019-04-22T06:57:52.000000000Z",
                    "2021-04-22T07:57:52.000000000Z",
                    "UTC",
                    false,
                    false,
                    null,
                    instantNow,
                )
        ) {
            is FunctionResult.Success ->
                assertThat(result.value).isEqualTo(
                    Instant.parse("2019-04-22T06:57:52.000000000Z") to Instant.parse("2021-04-22T07:57:52.000000000Z"),
                )
            is FunctionResult.Failure -> fail("Should not happen")
        }
    }

    @Test
    fun `start and end params valid and start boundary after both`() {
        when (
            val result =
                DataUtils.getStartEndTimeLimitedByBoundaries(
                    "2019-04-22T06:57:52.000000000Z",
                    "2019-04-22T07:57:52.000000000Z",
                    "UTC",
                    false,
                    false,
                    instant2020,
                    null,
                )
        ) {
            is FunctionResult.Success -> assertThat(result.value).isEqualTo(instant2020 to Instant.parse("2019-04-22T07:57:52.000000000Z"))
            is FunctionResult.Failure -> fail("Should not happen")
        }
    }

    @Test
    fun `start and end params valid and end boundary between start and end`() {
        when (
            val result =
                DataUtils.getStartEndTimeLimitedByBoundaries(
                    "2019-04-22T06:57:52.000000000Z",
                    "2021-04-22T07:57:52.000000000Z",
                    "UTC",
                    false,
                    false,
                    null,
                    instant2020,
                )
        ) {
            is FunctionResult.Success ->
                assertThat(result.value).isEqualTo(
                    Instant.parse("2019-04-22T06:57:52.000000000Z") to instant2020,
                )
            is FunctionResult.Failure -> fail("Should not happen")
        }
    }

    @Test
    fun `start and end params valid and start and end boundaries between both`() {
        when (
            val result =
                DataUtils.getStartEndTimeLimitedByBoundaries(
                    "2019-04-22T06:57:52.000000000Z",
                    "2022-04-22T07:57:52.000000000Z",
                    "UTC",
                    false,
                    false,
                    instant2020,
                    instant2021,
                )
        ) {
            is FunctionResult.Success -> assertThat(result.value).isEqualTo(instant2020 to instant2021)
            is FunctionResult.Failure -> fail("Should not happen")
        }
    }

    companion object {
        @JvmStatic
        private fun instants(): Stream<Arguments?>? =
            Stream.of(
                Arguments.of(
                    Instant.ofEpochSecond(4),
                    Instant.ofEpochSecond(6),
                    Instant.ofEpochSecond(2),
                    Instant.ofEpochSecond(8),
                    true,
                ),
                Arguments.of(
                    Instant.ofEpochSecond(2),
                    Instant.ofEpochSecond(8),
                    Instant.ofEpochSecond(4),
                    Instant.ofEpochSecond(6),
                    true,
                ),
                Arguments.of(
                    Instant.ofEpochSecond(2),
                    Instant.ofEpochSecond(6),
                    Instant.ofEpochSecond(4),
                    Instant.ofEpochSecond(8),
                    true,
                ),
                Arguments.of(
                    Instant.ofEpochSecond(4),
                    Instant.ofEpochSecond(8),
                    Instant.ofEpochSecond(2),
                    Instant.ofEpochSecond(6),
                    true,
                ),
                Arguments.of(
                    Instant.ofEpochSecond(2),
                    Instant.ofEpochSecond(4),
                    Instant.ofEpochSecond(2),
                    Instant.ofEpochSecond(4),
                    true,
                ),
                Arguments.of(
                    Instant.ofEpochSecond(2),
                    Instant.ofEpochSecond(4),
                    Instant.ofEpochSecond(4),
                    Instant.ofEpochSecond(6),
                    true,
                ),
                Arguments.of(
                    Instant.ofEpochSecond(4),
                    Instant.ofEpochSecond(6),
                    Instant.ofEpochSecond(2),
                    Instant.ofEpochSecond(4),
                    true,
                ),
                Arguments.of(
                    Instant.ofEpochSecond(2),
                    Instant.ofEpochSecond(4),
                    Instant.ofEpochSecond(6),
                    Instant.ofEpochSecond(8),
                    false,
                ),
                Arguments.of(
                    Instant.ofEpochSecond(6),
                    Instant.ofEpochSecond(8),
                    Instant.ofEpochSecond(2),
                    Instant.ofEpochSecond(4),
                    false,
                ),
            )
    }

    @ParameterizedTest
    @MethodSource("instants")
    fun isOverlap(
        startTime1: Instant,
        endTime1: Instant,
        startTime2: Instant,
        endTime2: Instant,
        expected: Boolean,
    ) {
        assertThat(DataUtils.isOverlap(startTime1, endTime1, startTime2, endTime2)).isEqualTo(expected)
    }

    @Test
    fun `adjust when the enforced end time is later than the requested start time`() {
        val (start, end) =
            Pair(Instant.parse("2025-06-02T19:59:57Z"), Instant.parse("2025-06-02T23:55:03.462436434Z")).adjust(
                keyEnforcedStartTime = Instant.parse("2025-06-02T00:59:57Z"),
                keyEnforcedEndTime = Instant.parse("2025-06-02T21:55:03.462436434Z"),
            )!!

        Assertions.assertEquals(Instant.parse("2025-06-02T19:59:57Z"), start)
        Assertions.assertEquals(Instant.parse("2025-06-02T21:55:03.462436434Z"), end)
    }

    @Test
    fun `adjust when the community start time is later than the requested start time`() {
        val (start, end) =
            Pair(Instant.parse("2025-06-02T19:59:57Z"), Instant.parse("2025-06-02T23:55:03.462436434Z")).adjust(
                communityEnforcedStartTime = Instant.parse("2025-06-02T21:59:57Z"),
            )!!

        Assertions.assertEquals(Instant.parse("2025-06-02T21:59:57Z"), start)
        Assertions.assertEquals(Instant.parse("2025-06-02T23:55:03.462436434Z"), end)
    }

    @Test
    fun `adjust when all ranges intersect`() {
        val (start, end) =
            Pair(Instant.parse("2025-06-02T19:59:57Z"), Instant.parse("2025-06-02T23:55:03.462436434Z")).adjust(
                communityEnforcedStartTime = Instant.parse("2025-06-02T21:00:57Z"),
                keyEnforcedStartTime = Instant.parse("2025-06-02T00:59:57Z"),
                keyEnforcedEndTime = Instant.parse("2025-06-02T21:55:03.462436434Z"),
            )!!

        Assertions.assertEquals(Instant.parse("2025-06-02T21:00:57Z"), start)
        Assertions.assertEquals(Instant.parse("2025-06-02T21:55:03.462436434Z"), end)
    }

    @Test
    fun `adjust when the enforced range does not intersect with requested range`() {
        val result =
            Pair(Instant.parse("2025-06-02T19:59:57Z"), Instant.parse("2025-06-02T23:55:03.462436434Z")).adjust(
                keyEnforcedStartTime = Instant.parse("2025-06-04T00:59:57Z"),
                keyEnforcedEndTime = Instant.parse("2025-06-04T13:55:03.462436434Z"),
            )

        assertNull(result)
    }

    @Test
    fun `adjust when the community range does not intersect with requested range`() {
        val result =
            Pair(Instant.parse("2025-06-02T19:59:57Z"), Instant.parse("2025-06-02T23:55:03.462436434Z")).adjust(
                communityEnforcedStartTime = Instant.parse("2025-06-03T21:59:57Z"),
            )

        assertNull(result)
    }

    @Test
    fun `adjust when all ranges do not intersect`() {
        val result =
            Pair(Instant.parse("2025-06-02T19:59:57Z"), Instant.parse("2025-06-02T23:55:03.462436434Z")).adjust(
                communityEnforcedStartTime = Instant.parse("2025-06-03T01:33:44Z"),
                keyEnforcedStartTime = Instant.parse("2025-06-02T00:59:57Z"),
                keyEnforcedEndTime = Instant.parse("2025-06-02T13:55:03.462436434Z"),
            )

        assertNull(result)
    }
}
