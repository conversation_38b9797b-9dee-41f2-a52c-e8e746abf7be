package io.coinmetrics.api.utils

import io.coinmetrics.api.utils.DataUtils.boundaryAwareDownsampleTimedByteArray
import io.coinmetrics.api.utils.TimeUtils.createStatefulDownSamplerConfig
import io.coinmetrics.s3databases.read.Reader
import kotlinx.coroutines.flow.asFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertArrayEquals
import org.junit.jupiter.api.Test
import java.time.Instant

class DataUtilsBoundaryAwareWithAlignmentTest {
    @Test
    fun `boundary aware 1h down sampling, paging_from=start with alignment`(): Unit =
        runBlocking {
            val result =
                listOf(
                    Reader.TimedByteArray("01".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T00:59:59Z"))),
                    Reader.TimedByteArray("02".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T01:59:59Z"))),
                    Reader.TimedByteArray("03".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T02:59:59Z"))),
                    Reader.TimedByteArray("04".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T03:59:59Z"))),
                    Reader.TimedByteArray("05".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T04:59:59Z"))),
                    Reader.TimedByteArray("06".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T05:59:59Z"))),
                    Reader.TimedByteArray("07".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T06:59:59Z"))),
                    Reader.TimedByteArray("08".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T07:59:59Z"))),
                    Reader.TimedByteArray("09".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T08:59:59Z"))),
                    Reader.TimedByteArray("10".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T09:59:59Z"))),
                    Reader.TimedByteArray("11".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T10:59:59Z"))),
                    Reader.TimedByteArray("12".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T11:59:59Z"))),
                    Reader.TimedByteArray("13".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T12:59:59Z"))),
                    Reader.TimedByteArray("14".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T13:59:59Z"))),
                    Reader.TimedByteArray("15".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T15:00:03Z"))),
                    Reader.TimedByteArray("16".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T15:59:59Z"))),
                    Reader.TimedByteArray("17".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T17:00:02Z"))),
                    Reader.TimedByteArray("18".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T17:59:59Z"))),
                    Reader.TimedByteArray("19".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T18:59:59Z"))),
                    Reader.TimedByteArray("20".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T20:00:02Z"))),
                    Reader.TimedByteArray("21".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T20:59:59Z"))),
                    Reader.TimedByteArray("22".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T21:59:59Z"))),
                    Reader.TimedByteArray("23".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T22:59:59Z"))),
                    Reader.TimedByteArray("24".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T23:59:59Z"))),
                ).asFlow()
                    .boundaryAwareDownsampleTimedByteArray(
                        createStatefulDownSamplerConfig(
                            granularity = "1h",
                            pagingFromStart = true,
                            withAlignment = true,
                        ).getOrNull()!!,
                    ).map { it.time.value }
                    .toList()
            println(result.joinToString("\n"))
            assertArrayEquals(
                arrayOf(
                    Instant.parse("2025-05-21T00:59:59Z"),
                    Instant.parse("2025-05-21T01:59:59Z"),
                    Instant.parse("2025-05-21T02:59:59Z"),
                    Instant.parse("2025-05-21T03:59:59Z"),
                    Instant.parse("2025-05-21T04:59:59Z"),
                    Instant.parse("2025-05-21T05:59:59Z"),
                    Instant.parse("2025-05-21T06:59:59Z"),
                    Instant.parse("2025-05-21T07:59:59Z"),
                    Instant.parse("2025-05-21T08:59:59Z"),
                    Instant.parse("2025-05-21T09:59:59Z"),
                    Instant.parse("2025-05-21T10:59:59Z"),
                    Instant.parse("2025-05-21T11:59:59Z"),
                    Instant.parse("2025-05-21T12:59:59Z"),
                    Instant.parse("2025-05-21T13:59:59Z"),
                    Instant.parse("2025-05-21T15:00:03Z"),
                    Instant.parse("2025-05-21T15:59:59Z"),
                    Instant.parse("2025-05-21T17:00:02Z"),
                    Instant.parse("2025-05-21T17:59:59Z"),
                    Instant.parse("2025-05-21T18:59:59Z"),
                    Instant.parse("2025-05-21T20:00:02Z"),
                    Instant.parse("2025-05-21T20:59:59Z"),
                    Instant.parse("2025-05-21T21:59:59Z"),
                    Instant.parse("2025-05-21T22:59:59Z"),
                    Instant.parse("2025-05-21T23:59:59Z"),
                ),
                result.toTypedArray(),
            )
        }

    @Test
    fun `boundary aware 1h down sampling, paging_from=start with alignment and redundant values`(): Unit =
        runBlocking {
            val result =
                listOf(
                    Reader.TimedByteArray("01".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T00:59:59Z"))),
                    Reader.TimedByteArray("01.1".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T01:30:59Z"))),
                    Reader.TimedByteArray("02".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T01:59:59Z"))),
                    Reader.TimedByteArray("02.1".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T02:30:59Z"))),
                    Reader.TimedByteArray("03".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T02:59:59Z"))),
                    Reader.TimedByteArray("03.1".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T03:30:59Z"))),
                    Reader.TimedByteArray("04".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T03:59:59Z"))),
                    Reader.TimedByteArray("05".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T04:59:59Z"))),
                    Reader.TimedByteArray("06".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T05:59:59Z"))),
                    Reader.TimedByteArray("07".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T06:59:59Z"))),
                    Reader.TimedByteArray("08".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T07:59:59Z"))),
                    Reader.TimedByteArray("08.1".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T08:57:59Z"))),
                    Reader.TimedByteArray("08.2".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T08:58:59Z"))),
                    Reader.TimedByteArray("09".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T08:59:59Z"))),
                    Reader.TimedByteArray("10".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T09:59:59Z"))),
                    Reader.TimedByteArray("11".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T10:59:59Z"))),
                    Reader.TimedByteArray("12".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T11:59:59Z"))),
                    Reader.TimedByteArray("13".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T12:59:59Z"))),
                    Reader.TimedByteArray("14".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T13:59:59Z"))),
                    Reader.TimedByteArray("15".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T15:00:03Z"))),
                    Reader.TimedByteArray("15.1".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T15:00:04Z"))),
                    Reader.TimedByteArray("15.2".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T15:00:05Z"))),
                    Reader.TimedByteArray("16".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T15:59:59Z"))),
                    Reader.TimedByteArray("17".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T17:00:02Z"))),
                    Reader.TimedByteArray("18".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T17:59:59Z"))),
                    Reader.TimedByteArray("19".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T18:59:59Z"))),
                    Reader.TimedByteArray("20".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T20:00:02Z"))),
                    Reader.TimedByteArray("21".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T20:59:59Z"))),
                    Reader.TimedByteArray("22".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T21:59:59Z"))),
                    Reader.TimedByteArray("22.1".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T22:00:02Z"))),
                    Reader.TimedByteArray("23".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T22:59:59Z"))),
                    Reader.TimedByteArray("24".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T23:59:59Z"))),
                ).asFlow()
                    .boundaryAwareDownsampleTimedByteArray(
                        createStatefulDownSamplerConfig(
                            granularity = "1h",
                            pagingFromStart = true,
                            withAlignment = true,
                        ).getOrNull()!!,
                    ).map { it.time.value }
                    .toList()
            println(result.joinToString("\n"))
            assertArrayEquals(
                arrayOf(
                    Instant.parse("2025-05-21T00:59:59Z"),
                    Instant.parse("2025-05-21T01:59:59Z"),
                    Instant.parse("2025-05-21T02:59:59Z"),
                    Instant.parse("2025-05-21T03:59:59Z"),
                    Instant.parse("2025-05-21T04:59:59Z"),
                    Instant.parse("2025-05-21T05:59:59Z"),
                    Instant.parse("2025-05-21T06:59:59Z"),
                    Instant.parse("2025-05-21T07:59:59Z"),
                    Instant.parse("2025-05-21T08:59:59Z"),
                    Instant.parse("2025-05-21T09:59:59Z"),
                    Instant.parse("2025-05-21T10:59:59Z"),
                    Instant.parse("2025-05-21T11:59:59Z"),
                    Instant.parse("2025-05-21T12:59:59Z"),
                    Instant.parse("2025-05-21T13:59:59Z"),
                    Instant.parse("2025-05-21T15:00:03Z"),
                    Instant.parse("2025-05-21T15:59:59Z"),
                    Instant.parse("2025-05-21T17:00:02Z"),
                    Instant.parse("2025-05-21T17:59:59Z"),
                    Instant.parse("2025-05-21T18:59:59Z"),
                    Instant.parse("2025-05-21T20:00:02Z"),
                    Instant.parse("2025-05-21T20:59:59Z"),
                    Instant.parse("2025-05-21T21:59:59Z"),
                    Instant.parse("2025-05-21T22:59:59Z"),
                    Instant.parse("2025-05-21T23:59:59Z"),
                ),
                result.toTypedArray(),
            )
        }

    @Test
    fun `boundary aware 1h down sampling, paging_from=start with alignment and simple redundant values`(): Unit =
        runBlocking {
            val result =
                listOf(
                    Reader.TimedByteArray("01".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T00:59:57Z"))),
                    Reader.TimedByteArray("02".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T00:59:58Z"))),
                    Reader.TimedByteArray("03".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T01:00:01Z"))),
                    Reader.TimedByteArray("04".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T01:00:02Z"))),
                    Reader.TimedByteArray("05".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T01:00:03Z"))),
                    Reader.TimedByteArray("06".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T01:59:59Z"))),
                    Reader.TimedByteArray("07".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T02:00:10Z"))),
                ).asFlow()
                    .boundaryAwareDownsampleTimedByteArray(
                        createStatefulDownSamplerConfig(
                            granularity = "1h",
                            pagingFromStart = true,
                            withAlignment = true,
                        ).getOrNull()!!,
                    ).map { it.time.value }
                    .toList()
            println(result.joinToString("\n"))
            assertArrayEquals(
                arrayOf(
                    Instant.parse("2025-05-21T01:00:01Z"),
                    Instant.parse("2025-05-21T01:59:59Z"),
                ),
                result.toTypedArray(),
            )
        }

    @Test
    fun `boundary aware 1h down sampling, paging_from=start with alignment and duplicates`(): Unit =
        runBlocking {
            val result =
                listOf(
                    Reader.TimedByteArray("01".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T00:59:57Z"))),
                    Reader.TimedByteArray("01d".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T00:59:57Z"))),
                    Reader.TimedByteArray("02".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T00:59:58Z"))),
                    Reader.TimedByteArray("03".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T01:00:01Z"))),
                    Reader.TimedByteArray("03d".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T01:00:01Z"))),
                    Reader.TimedByteArray("04".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T01:00:02Z"))),
                    Reader.TimedByteArray("05".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T01:00:03Z"))),
                    Reader.TimedByteArray("06".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T01:59:59Z"))),
                    Reader.TimedByteArray("06d".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T01:59:59Z"))),
                    Reader.TimedByteArray("07".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T02:00:10Z"))),
                ).asFlow()
                    .boundaryAwareDownsampleTimedByteArray(
                        createStatefulDownSamplerConfig(
                            granularity = "1h",
                            pagingFromStart = true,
                            withAlignment = true,
                        ).getOrNull()!!,
                    ).map { it.time.value }
                    .toList()
            println(result.joinToString("\n"))
            assertArrayEquals(
                arrayOf(
                    Instant.parse("2025-05-21T01:00:01Z"),
                    Instant.parse("2025-05-21T01:59:59Z"),
                ),
                result.toTypedArray(),
            )
        }

    @Test
    fun `boundary aware 1h down sampling, paging_from=start with alignment and truncated values`(): Unit =
        runBlocking {
            val result =
                listOf(
                    Reader.TimedByteArray("01".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T00:59:57Z"))),
                    Reader.TimedByteArray("02".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T00:59:58Z"))),
                    Reader.TimedByteArray("03".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T01:00:00Z"))),
                    Reader.TimedByteArray("04".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T01:00:01Z"))),
                    Reader.TimedByteArray("05".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T01:00:02Z"))),
                    Reader.TimedByteArray("06".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T01:00:03Z"))),
                    Reader.TimedByteArray("07".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T01:59:59Z"))),
                    Reader.TimedByteArray("08".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T02:00:10Z"))),
                    Reader.TimedByteArray("09".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T03:00:00Z"))),
                    Reader.TimedByteArray("10".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T03:00:00Z"))),
                    Reader.TimedByteArray("11".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T03:59:59Z"))),
                    Reader.TimedByteArray("12".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T05:59:59Z"))),
                    Reader.TimedByteArray("13".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T06:00:00Z"))),
                    Reader.TimedByteArray("14".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T06:00:01Z"))),
                ).asFlow()
                    .boundaryAwareDownsampleTimedByteArray(
                        createStatefulDownSamplerConfig(
                            granularity = "1h",
                            pagingFromStart = true,
                            withAlignment = true,
                        ).getOrNull()!!,
                    ).map { it.time.value }
                    .toList()
            println(result.joinToString("\n"))
            assertArrayEquals(
                arrayOf(
                    Instant.parse("2025-05-21T01:00:00Z"),
                    Instant.parse("2025-05-21T01:59:59Z"),
                    Instant.parse("2025-05-21T03:00:00Z"),
                    Instant.parse("2025-05-21T03:59:59Z"),
                    Instant.parse("2025-05-21T06:00:00Z"),
                ),
                result.toTypedArray(),
            )
        }

    @Test
    fun `boundary aware 1h down sampling, paging_from=start with alignment and page size=1 and truncated value`(): Unit =
        runBlocking {
            val result =
                listOf(
                    Reader.TimedByteArray("01".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T00:00:00Z"))),
                ).asFlow()
                    .boundaryAwareDownsampleTimedByteArray(
                        createStatefulDownSamplerConfig(
                            granularity = "1h",
                            pagingFromStart = true,
                            withAlignment = true,
                        ).getOrNull()!!,
                    ).map { it.time.value }
                    .toList()
            println(result.joinToString("\n"))
            assertArrayEquals(
                arrayOf(
                    Instant.parse("2025-05-21T00:00:00Z"),
                ),
                result.toTypedArray(),
            )
        }

    @Test
    fun `boundary aware 1h down sampling, paging_from=start with alignment and page size=1 and 1 second value`(): Unit =
        runBlocking {
            val result =
                listOf(
                    Reader.TimedByteArray("01".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T00:00:01Z"))),
                ).asFlow()
                    .boundaryAwareDownsampleTimedByteArray(
                        createStatefulDownSamplerConfig(
                            granularity = "1h",
                            pagingFromStart = true,
                            withAlignment = true,
                        ).getOrNull()!!,
                    ).map { it.time.value }
                    .toList()
            println(result.joinToString("\n"))
            assertArrayEquals(
                arrayOf(
                    Instant.parse("2025-05-21T00:00:01Z"),
                ),
                result.toTypedArray(),
            )
        }

    @Test
    fun `boundary aware 1h down sampling, paging_from=start with alignment and page size=1 and 59 minute value`(): Unit =
        runBlocking {
            val result =
                listOf(
                    Reader.TimedByteArray("01".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T00:59:59Z"))),
                ).asFlow()
                    .boundaryAwareDownsampleTimedByteArray(
                        createStatefulDownSamplerConfig(
                            granularity = "1h",
                            pagingFromStart = true,
                            withAlignment = true,
                        ).getOrNull()!!,
                    ).map { it.time.value }
                    .toList()
            println(result.joinToString("\n"))
            assertArrayEquals(
                arrayOf(
                    Instant.parse("2025-05-21T00:59:59Z"),
                ),
                result.toTypedArray(),
            )
        }

    @Test
    fun `boundary aware 1h down sampling, paging_from=start with alignment and middle values`(): Unit =
        runBlocking {
            val result =
                listOf(
                    Reader.TimedByteArray("01".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T00:59:59Z"))),
                    Reader.TimedByteArray("02".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T01:30:00Z"))),
                    Reader.TimedByteArray("03".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T02:30:00Z"))),
                    Reader.TimedByteArray("04".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T03:00:00Z"))),
                    Reader.TimedByteArray("05".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T03:30:00Z"))),
                ).asFlow()
                    .boundaryAwareDownsampleTimedByteArray(
                        createStatefulDownSamplerConfig(
                            granularity = "1h",
                            pagingFromStart = true,
                            withAlignment = true,
                        ).getOrNull()!!,
                    ).map { it.time.value }
                    .toList()
            println(result.joinToString("\n"))
            assertArrayEquals(
                arrayOf(
                    Instant.parse("2025-05-21T00:59:59Z"),
                    Instant.parse("2025-05-21T02:30:00Z"),
                    Instant.parse("2025-05-21T03:00:00Z"),
                ),
                result.toTypedArray(),
            )
        }

    @Test
    fun `boundary aware 10s down sampling, paging_from=start with alignment and middle values`(): Unit =
        runBlocking {
            val result =
                listOf(
                    Reader.TimedByteArray("01".toByteArray(), { error("") }, lazyOf(Instant.parse("2024-09-11T08:05:00Z"))),
                    Reader.TimedByteArray("02".toByteArray(), { error("") }, lazyOf(Instant.parse("2024-09-11T09:05:00Z"))),
                    Reader.TimedByteArray("03".toByteArray(), { error("") }, lazyOf(Instant.parse("2024-09-11T09:08:20Z"))),
                    Reader.TimedByteArray("04".toByteArray(), { error("") }, lazyOf(Instant.parse("2024-09-11T09:08:35Z"))),
                    Reader.TimedByteArray("05".toByteArray(), { error("") }, lazyOf(Instant.parse("2024-09-11T09:08:40Z"))),
                    Reader.TimedByteArray("06".toByteArray(), { error("") }, lazyOf(Instant.parse("2024-09-11T09:08:50Z"))),
                ).asFlow()
                    .boundaryAwareDownsampleTimedByteArray(
                        createStatefulDownSamplerConfig(
                            granularity = "10s",
                            pagingFromStart = true,
                            withAlignment = true,
                        ).getOrNull()!!,
                    ).map { it.time.value }
                    .toList()
            println(result.joinToString("\n"))
            assertArrayEquals(
                arrayOf(
                    Instant.parse("2024-09-11T08:05:00Z"),
                    Instant.parse("2024-09-11T09:05:00Z"),
                    Instant.parse("2024-09-11T09:08:20Z"),
                    Instant.parse("2024-09-11T09:08:35Z"),
                    Instant.parse("2024-09-11T09:08:40Z"),
                    Instant.parse("2024-09-11T09:08:50Z"),
                ),
                result.toTypedArray(),
            )
        }

    @Test
    fun `boundary aware 1m down sampling, paging_from=start with alignment and gaps`(): Unit =
        runBlocking {
            val result =
                listOf(
                    Reader.TimedByteArray("01".toByteArray(), { error("") }, lazyOf(Instant.parse("2023-08-09T00:00:00Z"))),
                    Reader.TimedByteArray("02".toByteArray(), { error("") }, lazyOf(Instant.parse("2023-08-09T01:00:00Z"))),
                    Reader.TimedByteArray("03".toByteArray(), { error("") }, lazyOf(Instant.parse("2023-08-09T01:59:50Z"))),
                    Reader.TimedByteArray("04".toByteArray(), { error("") }, lazyOf(Instant.parse("2023-08-09T02:00:00Z"))),
                    Reader.TimedByteArray("05".toByteArray(), { error("") }, lazyOf(Instant.parse("2023-08-09T02:01:00Z"))),
                ).asFlow()
                    .boundaryAwareDownsampleTimedByteArray(
                        createStatefulDownSamplerConfig(
                            granularity = "1m",
                            pagingFromStart = true,
                            withAlignment = true,
                        ).getOrNull()!!,
                    ).map { it.time.value }
                    .toList()
            println(result.joinToString("\n"))
            assertArrayEquals(
                arrayOf(
                    Instant.parse("2023-08-09T00:00:00Z"),
                    Instant.parse("2023-08-09T01:00:00Z"),
                    Instant.parse("2023-08-09T02:00:00Z"),
                    Instant.parse("2023-08-09T02:01:00Z"),
                ),
                result.toTypedArray(),
            )
        }

    @Test
    fun `boundary aware 1h down sampling, paging_from=start with alignment and middle values and gaps`(): Unit =
        runBlocking {
            val result =
                listOf(
                    Reader.TimedByteArray("01".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-07-01T01:00:00Z"))),
                    Reader.TimedByteArray("02".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-07-01T01:59:59Z"))),
                    Reader.TimedByteArray("03".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-07-01T04:30:00Z"))),
                    Reader.TimedByteArray("04".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-07-01T05:00:00Z"))),
                ).asFlow()
                    .boundaryAwareDownsampleTimedByteArray(
                        createStatefulDownSamplerConfig(
                            granularity = "1h",
                            pagingFromStart = true,
                            withAlignment = true,
                        ).getOrNull()!!,
                    ).map { it.time.value }
                    .toList()
            println(result.joinToString("\n"))
            assertArrayEquals(
                arrayOf(
                    Instant.parse("2025-07-01T01:00:00Z"),
                    Instant.parse("2025-07-01T01:59:59Z"),
                    Instant.parse("2025-07-01T04:30:00Z"),
                    Instant.parse("2025-07-01T05:00:00Z"),
                ),
                result.toTypedArray(),
            )
        }

    @Test
    fun `boundary aware 1h down sampling, paging_from=start with alignment and around middle values`(): Unit =
        runBlocking {
            val result =
                listOf(
                    Reader.TimedByteArray("01".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T00:29:59Z"))),
                    Reader.TimedByteArray("02".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T00:30:01Z"))),
                    Reader.TimedByteArray("03".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T01:30:00Z"))),
                    Reader.TimedByteArray("04".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T02:30:00Z"))),
                    Reader.TimedByteArray("05".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T03:29:59Z"))),
                    Reader.TimedByteArray("06".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T04:30:01Z"))),
                ).asFlow()
                    .boundaryAwareDownsampleTimedByteArray(
                        createStatefulDownSamplerConfig(
                            granularity = "1h",
                            pagingFromStart = true,
                            withAlignment = true,
                        ).getOrNull()!!,
                    ).map { it.time.value }
                    .toList()
            println(result.joinToString("\n"))
            assertArrayEquals(
                arrayOf(
                    Instant.parse("2025-05-21T00:29:59Z"),
                    Instant.parse("2025-05-21T00:30:01Z"),
                    Instant.parse("2025-05-21T02:30:00Z"),
                    Instant.parse("2025-05-21T03:29:59Z"),
                    Instant.parse("2025-05-21T04:30:01Z"),
                ),
                result.toTypedArray(),
            )
        }

    @Test
    fun `boundary aware 1h down sampling, paging_from=end with alignment`(): Unit =
        runBlocking {
            val result =
                listOf(
                    Reader.TimedByteArray("24".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T23:59:59Z"))),
                    Reader.TimedByteArray("23".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T22:59:59Z"))),
                    Reader.TimedByteArray("22".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T21:59:59Z"))),
                    Reader.TimedByteArray("21".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T20:59:59Z"))),
                    Reader.TimedByteArray("20".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T20:00:02Z"))),
                    Reader.TimedByteArray("19".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T18:59:59Z"))),
                    Reader.TimedByteArray("18".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T17:59:59Z"))),
                    Reader.TimedByteArray("17".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T17:00:02Z"))),
                    Reader.TimedByteArray("16".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T15:59:59Z"))),
                    Reader.TimedByteArray("15".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T15:00:03Z"))),
                    Reader.TimedByteArray("14".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T13:59:59Z"))),
                    Reader.TimedByteArray("13".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T12:59:59Z"))),
                    Reader.TimedByteArray("12".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T11:59:59Z"))),
                    Reader.TimedByteArray("11".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T10:59:59Z"))),
                    Reader.TimedByteArray("10".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T09:59:59Z"))),
                    Reader.TimedByteArray("09".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T08:59:59Z"))),
                    Reader.TimedByteArray("08".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T07:59:59Z"))),
                    Reader.TimedByteArray("07".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T06:59:59Z"))),
                    Reader.TimedByteArray("06".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T05:59:59Z"))),
                    Reader.TimedByteArray("05".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T04:59:59Z"))),
                    Reader.TimedByteArray("04".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T03:59:59Z"))),
                    Reader.TimedByteArray("03".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T02:59:59Z"))),
                    Reader.TimedByteArray("02".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T01:59:59Z"))),
                    Reader.TimedByteArray("01".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T00:59:59Z"))),
                ).asFlow()
                    .boundaryAwareDownsampleTimedByteArray(
                        createStatefulDownSamplerConfig(
                            granularity = "1h",
                            pagingFromStart = false,
                            withAlignment = true,
                        ).getOrNull()!!,
                    ).map { it.time.value }
                    .toList()
            println(result.joinToString("\n"))
            assertArrayEquals(
                arrayOf(
                    Instant.parse("2025-05-21T23:59:59Z"),
                    Instant.parse("2025-05-21T22:59:59Z"),
                    Instant.parse("2025-05-21T21:59:59Z"),
                    Instant.parse("2025-05-21T20:59:59Z"),
                    Instant.parse("2025-05-21T20:00:02Z"),
                    Instant.parse("2025-05-21T18:59:59Z"),
                    Instant.parse("2025-05-21T17:59:59Z"),
                    Instant.parse("2025-05-21T17:00:02Z"),
                    Instant.parse("2025-05-21T15:59:59Z"),
                    Instant.parse("2025-05-21T15:00:03Z"),
                    Instant.parse("2025-05-21T13:59:59Z"),
                    Instant.parse("2025-05-21T12:59:59Z"),
                    Instant.parse("2025-05-21T11:59:59Z"),
                    Instant.parse("2025-05-21T10:59:59Z"),
                    Instant.parse("2025-05-21T09:59:59Z"),
                    Instant.parse("2025-05-21T08:59:59Z"),
                    Instant.parse("2025-05-21T07:59:59Z"),
                    Instant.parse("2025-05-21T06:59:59Z"),
                    Instant.parse("2025-05-21T05:59:59Z"),
                    Instant.parse("2025-05-21T04:59:59Z"),
                    Instant.parse("2025-05-21T03:59:59Z"),
                    Instant.parse("2025-05-21T02:59:59Z"),
                    Instant.parse("2025-05-21T01:59:59Z"),
                    Instant.parse("2025-05-21T00:59:59Z"),
                ),
                result.toTypedArray(),
            )
        }

    @Test
    fun `boundary aware 1h down sampling, paging_from=end with alignment with redundant values`(): Unit =
        runBlocking {
            val result =
                listOf(
                    Reader.TimedByteArray("24".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T23:59:59Z"))),
                    Reader.TimedByteArray("23.1".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T23:59:58Z"))),
                    Reader.TimedByteArray("23.2".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T23:59:57Z"))),
                    Reader.TimedByteArray("23".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T22:59:59Z"))),
                    Reader.TimedByteArray("22".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T21:59:59Z"))),
                    Reader.TimedByteArray("21".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T20:59:59Z"))),
                    Reader.TimedByteArray("20".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T20:00:02Z"))),
                    Reader.TimedByteArray("19".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T18:59:59Z"))),
                    Reader.TimedByteArray("18".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T17:59:59Z"))),
                    Reader.TimedByteArray("17".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T17:00:02Z"))),
                    Reader.TimedByteArray("16".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T15:59:59Z"))),
                    Reader.TimedByteArray("15".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T15:00:03Z"))),
                    Reader.TimedByteArray("14".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T13:59:59Z"))),
                    Reader.TimedByteArray("13".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T12:59:59Z"))),
                    Reader.TimedByteArray("12".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T11:59:59Z"))),
                    Reader.TimedByteArray("11".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T10:59:59Z"))),
                    Reader.TimedByteArray("10".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T09:59:59Z"))),
                    Reader.TimedByteArray("09".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T08:59:59Z"))),
                    Reader.TimedByteArray("08".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T07:59:59Z"))),
                    Reader.TimedByteArray("07".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T06:59:59Z"))),
                    Reader.TimedByteArray("06".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T05:59:59Z"))),
                    Reader.TimedByteArray("05".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T04:59:59Z"))),
                    Reader.TimedByteArray("04".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T03:59:59Z"))),
                    Reader.TimedByteArray("03".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T02:59:59Z"))),
                    Reader.TimedByteArray("02".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T01:59:59Z"))),
                    Reader.TimedByteArray("01".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T00:59:59Z"))),
                ).asFlow()
                    .boundaryAwareDownsampleTimedByteArray(
                        createStatefulDownSamplerConfig(
                            granularity = "1h",
                            pagingFromStart = false,
                            withAlignment = true,
                        ).getOrNull()!!,
                    ).map { it.time.value }
                    .toList()
            println(result.joinToString("\n"))
            assertArrayEquals(
                arrayOf(
                    Instant.parse("2025-05-21T23:59:59Z"),
                    Instant.parse("2025-05-21T22:59:59Z"),
                    Instant.parse("2025-05-21T21:59:59Z"),
                    Instant.parse("2025-05-21T20:59:59Z"),
                    Instant.parse("2025-05-21T20:00:02Z"),
                    Instant.parse("2025-05-21T18:59:59Z"),
                    Instant.parse("2025-05-21T17:59:59Z"),
                    Instant.parse("2025-05-21T17:00:02Z"),
                    Instant.parse("2025-05-21T15:59:59Z"),
                    Instant.parse("2025-05-21T15:00:03Z"),
                    Instant.parse("2025-05-21T13:59:59Z"),
                    Instant.parse("2025-05-21T12:59:59Z"),
                    Instant.parse("2025-05-21T11:59:59Z"),
                    Instant.parse("2025-05-21T10:59:59Z"),
                    Instant.parse("2025-05-21T09:59:59Z"),
                    Instant.parse("2025-05-21T08:59:59Z"),
                    Instant.parse("2025-05-21T07:59:59Z"),
                    Instant.parse("2025-05-21T06:59:59Z"),
                    Instant.parse("2025-05-21T05:59:59Z"),
                    Instant.parse("2025-05-21T04:59:59Z"),
                    Instant.parse("2025-05-21T03:59:59Z"),
                    Instant.parse("2025-05-21T02:59:59Z"),
                    Instant.parse("2025-05-21T01:59:59Z"),
                    Instant.parse("2025-05-21T00:59:59Z"),
                ),
                result.toTypedArray(),
            )
        }

    @Test
    fun `boundary aware 1h down sampling, paging_from=end with alignment and simple redundant values`(): Unit =
        runBlocking {
            val result =
                listOf(
                    Reader.TimedByteArray("07".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T02:00:10Z"))),
                    Reader.TimedByteArray("06".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T01:59:59Z"))),
                    Reader.TimedByteArray("05".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T01:00:03Z"))),
                    Reader.TimedByteArray("04".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T01:00:02Z"))),
                    Reader.TimedByteArray("03".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T01:00:01Z"))),
                    Reader.TimedByteArray("02".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T00:59:58Z"))),
                    Reader.TimedByteArray("01".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T00:59:57Z"))),
                ).asFlow()
                    .boundaryAwareDownsampleTimedByteArray(
                        createStatefulDownSamplerConfig(
                            granularity = "1h",
                            pagingFromStart = false,
                            withAlignment = true,
                        ).getOrNull()!!,
                    ).map { it.time.value }
                    .toList()
            println(result.joinToString("\n"))
            assertArrayEquals(
                arrayOf(
                    Instant.parse("2025-05-21T01:59:59Z"),
                    Instant.parse("2025-05-21T01:00:01Z"),
                ),
                result.toTypedArray(),
            )
        }

    @Test
    fun `boundary aware 1h down sampling, paging_from=end with alignment and duplicates`(): Unit =
        runBlocking {
            val result =
                listOf(
                    Reader.TimedByteArray("07".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T02:00:10Z"))),
                    Reader.TimedByteArray("06d".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T01:59:59Z"))),
                    Reader.TimedByteArray("06".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T01:59:59Z"))),
                    Reader.TimedByteArray("05".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T01:00:03Z"))),
                    Reader.TimedByteArray("04".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T01:00:02Z"))),
                    Reader.TimedByteArray("03d".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T01:00:01Z"))),
                    Reader.TimedByteArray("03".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T01:00:01Z"))),
                    Reader.TimedByteArray("02".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T00:59:58Z"))),
                    Reader.TimedByteArray("01d".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T00:59:57Z"))),
                    Reader.TimedByteArray("01".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T00:59:57Z"))),
                ).asFlow()
                    .boundaryAwareDownsampleTimedByteArray(
                        createStatefulDownSamplerConfig(
                            granularity = "1h",
                            pagingFromStart = false,
                            withAlignment = true,
                        ).getOrNull()!!,
                    ).map { it.time.value }
                    .toList()
            println(result.joinToString("\n"))
            assertArrayEquals(
                arrayOf(
                    Instant.parse("2025-05-21T01:59:59Z"),
                    Instant.parse("2025-05-21T01:00:01Z"),
                ),
                result.toTypedArray(),
            )
        }

    @Test
    fun `boundary aware 1h down sampling, paging_from=end with alignment and truncated values`(): Unit =
        runBlocking {
            val result =
                listOf(
                    Reader.TimedByteArray("14".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T06:00:01Z"))),
                    Reader.TimedByteArray("13".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T06:00:00Z"))),
                    Reader.TimedByteArray("12".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T05:59:59Z"))),
                    Reader.TimedByteArray("11".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T03:59:59Z"))),
                    Reader.TimedByteArray("10".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T03:00:00Z"))),
                    Reader.TimedByteArray("09".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T03:00:00Z"))),
                    Reader.TimedByteArray("08".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T02:00:10Z"))),
                    Reader.TimedByteArray("07".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T01:59:59Z"))),
                    Reader.TimedByteArray("06".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T01:00:03Z"))),
                    Reader.TimedByteArray("05".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T01:00:02Z"))),
                    Reader.TimedByteArray("04".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T01:00:01Z"))),
                    Reader.TimedByteArray("03".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T01:00:00Z"))),
                    Reader.TimedByteArray("02".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T00:59:58Z"))),
                    Reader.TimedByteArray("01".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T00:59:57Z"))),
                ).asFlow()
                    .boundaryAwareDownsampleTimedByteArray(
                        createStatefulDownSamplerConfig(
                            granularity = "1h",
                            pagingFromStart = false,
                            withAlignment = true,
                        ).getOrNull()!!,
                    ).map { it.time.value }
                    .toList()
            println(result.joinToString("\n"))
            assertArrayEquals(
                arrayOf(
                    Instant.parse("2025-05-21T06:00:00Z"),
                    Instant.parse("2025-05-21T05:59:59Z"),
                    Instant.parse("2025-05-21T03:59:59Z"),
                    Instant.parse("2025-05-21T03:00:00Z"),
                    Instant.parse("2025-05-21T01:59:59Z"),
                    Instant.parse("2025-05-21T01:00:00Z"),
                ),
                result.toTypedArray(),
            )
        }

    @Test
    fun `boundary aware 1h down sampling, paging_from=end with alignment and page size=1 and truncated value`(): Unit =
        runBlocking {
            val result =
                listOf(
                    Reader.TimedByteArray("01".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T00:00:00Z"))),
                ).asFlow()
                    .boundaryAwareDownsampleTimedByteArray(
                        createStatefulDownSamplerConfig(
                            granularity = "1h",
                            pagingFromStart = false,
                            withAlignment = true,
                        ).getOrNull()!!,
                    ).map { it.time.value }
                    .toList()
            println(result.joinToString("\n"))
            assertArrayEquals(
                arrayOf(
                    Instant.parse("2025-05-21T00:00:00Z"),
                ),
                result.toTypedArray(),
            )
        }

    @Test
    fun `boundary aware 1h down sampling, paging_from=end with alignment and page size=1 and 1 second value`(): Unit =
        runBlocking {
            val result =
                listOf(
                    Reader.TimedByteArray("01".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T00:00:01Z"))),
                ).asFlow()
                    .boundaryAwareDownsampleTimedByteArray(
                        createStatefulDownSamplerConfig(
                            granularity = "1h",
                            pagingFromStart = false,
                            withAlignment = true,
                        ).getOrNull()!!,
                    ).map { it.time.value }
                    .toList()
            println(result.joinToString("\n"))
            assertArrayEquals(
                arrayOf(
                    Instant.parse("2025-05-21T00:00:01Z"),
                ),
                result.toTypedArray(),
            )
        }

    @Test
    fun `boundary aware 1h down sampling, paging_from=end with alignment and page size=1 and 59 minute value`(): Unit =
        runBlocking {
            val result =
                listOf(
                    Reader.TimedByteArray("01".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T00:59:59Z"))),
                ).asFlow()
                    .boundaryAwareDownsampleTimedByteArray(
                        createStatefulDownSamplerConfig(
                            granularity = "1h",
                            pagingFromStart = false,
                            withAlignment = true,
                        ).getOrNull()!!,
                    ).map { it.time.value }
                    .toList()
            println(result.joinToString("\n"))
            assertArrayEquals(
                arrayOf(
                    Instant.parse("2025-05-21T00:59:59Z"),
                ),
                result.toTypedArray(),
            )
        }

    @Test
    fun `boundary aware 1h down sampling, paging_from=end with alignment and middle values`(): Unit =
        runBlocking {
            val result =
                listOf(
                    Reader.TimedByteArray("05".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T03:30:00Z"))),
                    Reader.TimedByteArray("04".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T03:00:00Z"))),
                    Reader.TimedByteArray("03".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T02:30:00Z"))),
                    Reader.TimedByteArray("02".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T01:30:00Z"))),
                    Reader.TimedByteArray("01".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-05-21T00:59:59Z"))),
                ).asFlow()
                    .boundaryAwareDownsampleTimedByteArray(
                        createStatefulDownSamplerConfig(
                            granularity = "1h",
                            pagingFromStart = false,
                            withAlignment = true,
                        ).getOrNull()!!,
                    ).map { it.time.value }
                    .toList()
            println(result.joinToString("\n"))
            assertArrayEquals(
                arrayOf(
                    Instant.parse("2025-05-21T03:30:00Z"),
                    Instant.parse("2025-05-21T03:00:00Z"),
                    Instant.parse("2025-05-21T01:30:00Z"),
                    Instant.parse("2025-05-21T00:59:59Z"),
                ),
                result.toTypedArray(),
            )
        }

    @Test
    fun `boundary aware 10s down sampling, paging_from=end with alignment and middle values`(): Unit =
        runBlocking {
            val result =
                listOf(
                    Reader.TimedByteArray("06".toByteArray(), { error("") }, lazyOf(Instant.parse("2024-09-11T09:08:50Z"))),
                    Reader.TimedByteArray("05".toByteArray(), { error("") }, lazyOf(Instant.parse("2024-09-11T09:08:40Z"))),
                    Reader.TimedByteArray("04".toByteArray(), { error("") }, lazyOf(Instant.parse("2024-09-11T09:08:35Z"))),
                    Reader.TimedByteArray("03".toByteArray(), { error("") }, lazyOf(Instant.parse("2024-09-11T09:08:20Z"))),
                    Reader.TimedByteArray("02".toByteArray(), { error("") }, lazyOf(Instant.parse("2024-09-11T09:05:00Z"))),
                    Reader.TimedByteArray("01".toByteArray(), { error("") }, lazyOf(Instant.parse("2024-09-11T08:05:00Z"))),
                ).asFlow()
                    .boundaryAwareDownsampleTimedByteArray(
                        createStatefulDownSamplerConfig(
                            granularity = "10s",
                            pagingFromStart = false,
                            withAlignment = true,
                        ).getOrNull()!!,
                    ).map { it.time.value }
                    .toList()
            println(result.joinToString("\n"))
            assertArrayEquals(
                arrayOf(
                    Instant.parse("2024-09-11T09:08:50Z"),
                    Instant.parse("2024-09-11T09:08:40Z"),
                    Instant.parse("2024-09-11T09:08:35Z"),
                    Instant.parse("2024-09-11T09:08:20Z"),
                    Instant.parse("2024-09-11T09:05:00Z"),
                    Instant.parse("2024-09-11T08:05:00Z"),
                ),
                result.toTypedArray(),
            )
        }

    @Test
    fun `boundary aware 1m down sampling, paging_from=end with alignment and gaps`(): Unit =
        runBlocking {
            val result =
                listOf(
                    Reader.TimedByteArray("05".toByteArray(), { error("") }, lazyOf(Instant.parse("2023-08-09T02:01:00Z"))),
                    Reader.TimedByteArray("04".toByteArray(), { error("") }, lazyOf(Instant.parse("2023-08-09T02:00:00Z"))),
                    Reader.TimedByteArray("03".toByteArray(), { error("") }, lazyOf(Instant.parse("2023-08-09T01:59:50Z"))),
                    Reader.TimedByteArray("02".toByteArray(), { error("") }, lazyOf(Instant.parse("2023-08-09T01:00:00Z"))),
                    Reader.TimedByteArray("01".toByteArray(), { error("") }, lazyOf(Instant.parse("2023-08-09T00:00:00Z"))),
                ).asFlow()
                    .boundaryAwareDownsampleTimedByteArray(
                        createStatefulDownSamplerConfig(
                            granularity = "1m",
                            pagingFromStart = false,
                            withAlignment = true,
                        ).getOrNull()!!,
                    ).map { it.time.value }
                    .toList()
            println(result.joinToString("\n"))
            assertArrayEquals(
                arrayOf(
                    Instant.parse("2023-08-09T02:01:00Z"),
                    Instant.parse("2023-08-09T02:00:00Z"),
                    Instant.parse("2023-08-09T01:59:50Z"),
                    Instant.parse("2023-08-09T01:00:00Z"),
                    Instant.parse("2023-08-09T00:00:00Z"),
                ),
                result.toTypedArray(),
            )
        }

    @Test
    fun `boundary aware 1h down sampling, paging_from=end with alignment and middle values and gaps`(): Unit =
        runBlocking {
            val result =
                listOf(
                    Reader.TimedByteArray("04".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-07-01T05:00:00Z"))),
                    Reader.TimedByteArray("03".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-07-01T04:30:00Z"))),
                    Reader.TimedByteArray("02".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-07-01T01:59:59Z"))),
                    Reader.TimedByteArray("01".toByteArray(), { error("") }, lazyOf(Instant.parse("2025-07-01T01:00:00Z"))),
                ).asFlow()
                    .boundaryAwareDownsampleTimedByteArray(
                        createStatefulDownSamplerConfig(
                            granularity = "1h",
                            pagingFromStart = false,
                            withAlignment = true,
                        ).getOrNull()!!,
                    ).map { it.time.value }
                    .toList()
            println(result.joinToString("\n"))
            assertArrayEquals(
                arrayOf(
                    Instant.parse("2025-07-01T05:00:00Z"),
                    Instant.parse("2025-07-01T04:30:00Z"),
                    Instant.parse("2025-07-01T01:59:59Z"),
                    Instant.parse("2025-07-01T01:00:00Z"),
                ),
                result.toTypedArray(),
            )
        }
}
