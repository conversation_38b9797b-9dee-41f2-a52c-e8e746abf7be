package io.coinmetrics.api.utils

import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertArrayEquals
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import java.time.Duration
import java.time.Instant
import java.util.concurrent.ThreadLocalRandom

class UtilsTest {
    @Test
    fun `Flow of ByteArray to InputStream`() {
        val flow = flowOf(byteArrayOf(-1, 0, -128), byteArrayOf(127), byteArrayOf(5, 6))
        val ins = flow.toInputStream()
        assertEquals(255, ins.read())

        val buf = ByteArray(6)
        ThreadLocalRandom.current().nextBytes(buf)
        assertEquals(4, ins.readNBytes(buf, 1, 4))
        assertArrayEquals(byteArrayOf(buf[0], 0, -128, 127, 5, buf[5]), buf)

        assertArrayEquals(byteArrayOf(6), ins.readNBytes(2))

        assertEquals(-1, ins.read())
        assertEquals(-1, ins.read(buf))
    }

    @Test
    fun `OutputStream as Flow of ByteArray`() =
        runBlocking {
            val actual =
                toFlow {
                    it.write(-1)
                    it.write(-129)
                    it.write(byteArrayOf(-128, 127, 0, 1))
                    it.write(byteArrayOf(42, 2, 3, 4, 42), 1, 3)
                }.toList()
            assertEquals(
                listOf(
                    byteArrayOf(-1),
                    byteArrayOf(127),
                    byteArrayOf(-128, 127, 0, 1),
                    byteArrayOf(2, 3, 4),
                ).map { it.toList() },
                actual.map { it.toList() },
            )
        }

    @Test
    fun `effective limitPerEntity, days`() {
        assertEquals(
            false to 1,
            Utils.getEffectiveLimitPerEntity(
                limitPerEntity = 1,
                normalizedFrequency = Duration.ofDays(1),
                startTime = Instant.EPOCH,
                startTimeInclusive = true,
                endTime = Instant.parse("2023-04-12T00:00:00Z"),
                endTimeInclusive = true,
                pageSize = 100,
                entitiesCount = 2,
            ),
        )
        assertEquals(
            false to null,
            Utils.getEffectiveLimitPerEntity(
                limitPerEntity = null,
                normalizedFrequency = Duration.ofDays(1),
                startTime = Instant.EPOCH,
                startTimeInclusive = true,
                endTime = Instant.parse("2023-04-12T00:00:00Z"),
                endTimeInclusive = true,
                pageSize = 100,
                entitiesCount = 2,
            ),
        )
        assertEquals(
            true to 1,
            Utils.getEffectiveLimitPerEntity(
                limitPerEntity = null,
                normalizedFrequency = Duration.ofDays(1),
                startTime = Instant.parse("2023-04-12T00:00:00Z"),
                startTimeInclusive = true,
                endTime = Instant.parse("2023-04-12T00:00:00Z"),
                endTimeInclusive = true,
                pageSize = 100,
                entitiesCount = 2,
            ),
        )
        assertEquals(
            true to 2,
            Utils.getEffectiveLimitPerEntity(
                limitPerEntity = null,
                normalizedFrequency = Duration.ofDays(1),
                startTime = Instant.parse("2023-04-12T00:00:00Z"),
                startTimeInclusive = true,
                endTime = Instant.parse("2023-04-13T00:00:00Z"),
                endTimeInclusive = true,
                pageSize = 100,
                entitiesCount = 2,
            ),
        )
        assertEquals(
            true to 2,
            Utils.getEffectiveLimitPerEntity(
                limitPerEntity = null,
                normalizedFrequency = Duration.ofDays(1),
                startTime = Instant.parse("2023-04-12T00:00:00Z"),
                startTimeInclusive = true,
                endTime = Instant.parse("2023-04-13T00:00:00.000000001Z"),
                endTimeInclusive = true,
                pageSize = 100,
                entitiesCount = 2,
            ),
        )
        assertEquals(
            true to 1,
            Utils.getEffectiveLimitPerEntity(
                limitPerEntity = null,
                normalizedFrequency = Duration.ofDays(1),
                startTime = Instant.parse("2023-04-12T00:00:00Z"),
                startTimeInclusive = true,
                endTime = Instant.parse("2023-04-12T23:59:59.999999999Z"),
                endTimeInclusive = true,
                pageSize = 100,
                entitiesCount = 2,
            ),
        )
        assertEquals(
            true to 1,
            Utils.getEffectiveLimitPerEntity(
                limitPerEntity = null,
                normalizedFrequency = Duration.ofDays(1),
                startTime = Instant.parse("2023-04-12T00:00:00Z"),
                startTimeInclusive = true,
                endTime = Instant.parse("2023-04-13T00:00:00Z"),
                endTimeInclusive = false,
                pageSize = 100,
                entitiesCount = 2,
            ),
        )
        assertEquals(
            true to 1,
            Utils.getEffectiveLimitPerEntity(
                limitPerEntity = null,
                normalizedFrequency = Duration.ofDays(1),
                startTime = Instant.parse("2023-04-12T00:00:00Z"),
                startTimeInclusive = false,
                endTime = Instant.parse("2023-04-13T00:00:00Z"),
                endTimeInclusive = false,
                pageSize = 100,
                entitiesCount = 2,
            ),
        )
        assertEquals(
            true to 2,
            Utils.getEffectiveLimitPerEntity(
                limitPerEntity = null,
                normalizedFrequency = Duration.ofDays(1),
                startTime = Instant.parse("2023-04-11T23:59:59.999999999Z"),
                startTimeInclusive = false,
                endTime = Instant.parse("2023-04-13T00:00:00Z"),
                endTimeInclusive = true,
                pageSize = 100,
                entitiesCount = 2,
            ),
        )
    }

    @Test
    fun `effective limitPerEntity, minutes`() {
        assertEquals(
            true to 1,
            Utils.getEffectiveLimitPerEntity(
                limitPerEntity = null,
                normalizedFrequency = Duration.ofMinutes(1),
                startTime = Instant.parse("2023-04-12T00:00:00Z"),
                startTimeInclusive = true,
                endTime = Instant.parse("2023-04-12T00:00:00Z"),
                endTimeInclusive = true,
                pageSize = 100,
                entitiesCount = 2,
            ),
        )
        assertEquals(
            true to 2,
            Utils.getEffectiveLimitPerEntity(
                limitPerEntity = null,
                normalizedFrequency = Duration.ofMinutes(1),
                startTime = Instant.parse("2023-04-12T00:00:00Z"),
                startTimeInclusive = true,
                endTime = Instant.parse("2023-04-12T00:01:00Z"),
                endTimeInclusive = true,
                pageSize = 100,
                entitiesCount = 2,
            ),
        )
        assertEquals(
            true to 1,
            Utils.getEffectiveLimitPerEntity(
                limitPerEntity = null,
                normalizedFrequency = Duration.ofMinutes(1),
                startTime = Instant.parse("2023-04-12T00:00:00Z"),
                startTimeInclusive = true,
                endTime = Instant.parse("2023-04-12T00:01:00Z"),
                endTimeInclusive = false,
                pageSize = 100,
                entitiesCount = 2,
            ),
        )
        assertEquals(
            false to null,
            Utils.getEffectiveLimitPerEntity(
                limitPerEntity = null,
                normalizedFrequency = Duration.ofSeconds(1),
                startTime = Instant.parse("1001-04-12T00:00:00Z"),
                startTimeInclusive = true,
                endTime = Instant.parse("2099-04-12T00:01:00Z"),
                endTimeInclusive = false,
                pageSize = 100,
                entitiesCount = 2,
            ),
        )
    }
}
