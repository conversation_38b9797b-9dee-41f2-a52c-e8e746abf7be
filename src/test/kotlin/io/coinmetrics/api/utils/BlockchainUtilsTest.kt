package io.coinmetrics.api.utils

import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource

class BlockchainUtilsTest {
    @Test
    fun isHexEncodedString() {
        assertTrue(BlockchainUtils.isHexEncodedString("aa"))
        assertFalse(BlockchainUtils.isHexEncodedString("a"))
        assertFalse(BlockchainUtils.isHexEncodedString("z"))
        assertFalse(BlockchainUtils.isHexEncodedString("zz"))

        assertTrue(BlockchainUtils.isHexEncodedString("00"))
        assertTrue(BlockchainUtils.isHexEncodedString("aa99"))
        assertTrue(BlockchainUtils.isHexEncodedString("aaff"))
        assertTrue(BlockchainUtils.isHexEncodedString("aabb"))
        assertTrue(BlockchainUtils.isHexEncodedString("00bb"))
        assertFalse(BlockchainUtils.isHexEncodedString("00bb1"))
    }

    @Test
    fun isValidBtcTransactionId() {
        assertTrue(BlockchainUtils.isValidBtcTransactionId("abcdefABCDEF0123456789abcdefABCDEF0123456789abcdefABCDEF01234567"))
    }

    @ParameterizedTest
    @ValueSource(
        strings = [
            "",
            " ",
            "abcdefABCDEF0123456789abcdefABCDEF0123456789abcdefABCDEF0123456",
            "abcdefABCDEF0123456789abcdefABCDEF0123456789abcdefABCDEF012345678",
        ],
    )
    fun isNotValidBtcTransactionId(s: String) {
        assertFalse(BlockchainUtils.isValidBtcTransactionId(s))
    }
}
