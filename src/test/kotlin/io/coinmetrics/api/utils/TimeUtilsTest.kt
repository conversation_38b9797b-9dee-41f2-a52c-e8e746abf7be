package io.coinmetrics.api.utils

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.model.DataAvailabilityTimeRange
import io.coinmetrics.api.models.ErrorObject
import io.coinmetrics.api.models.ErrorResponse
import io.coinmetrics.api.utils.LazyInstant.Companion.toLazyInstant
import io.coinmetrics.api.utils.TimeUtils.createStatefulDownSampler
import io.coinmetrics.api.utils.TimeUtils.createStatefulDownSamplerConfig
import io.coinmetrics.api.utils.TimeUtils.dateTimeFormatter
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertArrayEquals
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Assertions.fail
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import java.time.Instant
import java.time.ZoneId
import java.time.ZoneOffset
import java.time.temporal.ChronoUnit
import java.util.TimeZone

internal class TimeUtilsTest {
    @Test
    internal fun `date parsing`() {
        var result = TimeUtils.parseTimeAndRound("2020-03-13", ZoneOffset.UTC, false)
        result as FunctionResult.Success
        assertEquals("2020-03-13T00:00:00.000000000Z", dateTimeFormatter.format(result.value))

        result = TimeUtils.parseTimeAndRound("2020-03-13", ZoneOffset.UTC, true)
        result as FunctionResult.Success
        assertEquals("2020-03-13T23:59:59.999999999Z", dateTimeFormatter.format(result.value))

        result = TimeUtils.parseTimeAndRound("20200313", ZoneOffset.UTC, false)
        result as FunctionResult.Success
        assertEquals("2020-03-13T00:00:00.000000000Z", dateTimeFormatter.format(result.value))

        result = TimeUtils.parseTimeAndRound("20200313", ZoneOffset.UTC, true)
        result as FunctionResult.Success
        assertEquals("2020-03-13T23:59:59.999999999Z", dateTimeFormatter.format(result.value))
    }

    @Test
    internal fun `time parsing without nanos and Z`() {
        var result = TimeUtils.parseTimeAndRound("2020-03-13T15:25:15", ZoneOffset.UTC, false)
        result as FunctionResult.Success
        assertEquals("2020-03-13T15:25:15.000000000Z", dateTimeFormatter.format(result.value))

        result = TimeUtils.parseTimeAndRound("2020-03-13T15:25:15", ZoneOffset.UTC, true)
        result as FunctionResult.Success
        assertEquals("2020-03-13T15:25:15.999999999Z", dateTimeFormatter.format(result.value))

        result = TimeUtils.parseTimeAndRound("2020-03-13T152515", ZoneOffset.UTC, false)
        result as FunctionResult.Success
        assertEquals("2020-03-13T15:25:15.000000000Z", dateTimeFormatter.format(result.value))

        result = TimeUtils.parseTimeAndRound("2020-03-13T152515", ZoneOffset.UTC, true)
        result as FunctionResult.Success
        assertEquals("2020-03-13T15:25:15.999999999Z", dateTimeFormatter.format(result.value))
    }

    @Test
    internal fun `time parsing without nanos, but with Z`() {
        var result = TimeUtils.parseTimeAndRound("2020-03-13T15:25:15Z", ZoneOffset.UTC, false)
        result as FunctionResult.Success
        assertEquals("2020-03-13T15:25:15.000000000Z", dateTimeFormatter.format(result.value))

        result = TimeUtils.parseTimeAndRound("2020-03-13T15:25:15Z", ZoneOffset.UTC, true)
        result as FunctionResult.Success
        assertEquals("2020-03-13T15:25:15.999999999Z", dateTimeFormatter.format(result.value))

        result = TimeUtils.parseTimeAndRound("2020-03-13T152515Z", ZoneOffset.UTC, false)
        result as FunctionResult.Success
        assertEquals("2020-03-13T15:25:15.000000000Z", dateTimeFormatter.format(result.value))

        result = TimeUtils.parseTimeAndRound("2020-03-13T152515Z", ZoneOffset.UTC, true)
        result as FunctionResult.Success
        assertEquals("2020-03-13T15:25:15.999999999Z", dateTimeFormatter.format(result.value))
    }

    @Test
    internal fun `time parsing with nanos`() {
        var result = TimeUtils.parseTimeAndRound("2020-03-13T15:25:15.123456789", ZoneOffset.UTC, false)
        result as FunctionResult.Success
        assertEquals("2020-03-13T15:25:15.123456789Z", dateTimeFormatter.format(result.value))

        result = TimeUtils.parseTimeAndRound("2020-03-13T15:25:15.123456789", ZoneOffset.UTC, true)
        result as FunctionResult.Success
        assertEquals("2020-03-13T15:25:15.123456789Z", dateTimeFormatter.format(result.value))

        result = TimeUtils.parseTimeAndRound("2020-03-13T152515.123456789", ZoneOffset.UTC, false)
        result as FunctionResult.Success
        assertEquals("2020-03-13T15:25:15.123456789Z", dateTimeFormatter.format(result.value))

        result = TimeUtils.parseTimeAndRound("2020-03-13T152515.123456789", ZoneOffset.UTC, true)
        result as FunctionResult.Success
        assertEquals("2020-03-13T15:25:15.123456789Z", dateTimeFormatter.format(result.value))
    }

    @Test
    internal fun `time parsing with millis`() {
        var result = TimeUtils.parseTimeAndRound("2020-03-13T15:25:15.123", ZoneOffset.UTC, false)
        result as FunctionResult.Success
        assertEquals("2020-03-13T15:25:15.123000000Z", dateTimeFormatter.format(result.value))

        result = TimeUtils.parseTimeAndRound("2020-03-13T15:25:15.123", ZoneOffset.UTC, true)
        result as FunctionResult.Success
        assertEquals("2020-03-13T15:25:15.123999999Z", dateTimeFormatter.format(result.value))

        result = TimeUtils.parseTimeAndRound("2020-03-13T152515.123", ZoneOffset.UTC, false)
        result as FunctionResult.Success
        assertEquals("2020-03-13T15:25:15.123000000Z", dateTimeFormatter.format(result.value))

        result = TimeUtils.parseTimeAndRound("2020-03-13T152515.123", ZoneOffset.UTC, true)
        result as FunctionResult.Success
        assertEquals("2020-03-13T15:25:15.123999999Z", dateTimeFormatter.format(result.value))
    }

    @Test
    internal fun `time parsing with micros`() {
        var result = TimeUtils.parseTimeAndRound("2020-03-13T15:25:15.123456", ZoneOffset.UTC, false)
        result as FunctionResult.Success
        assertEquals("2020-03-13T15:25:15.123456000Z", dateTimeFormatter.format(result.value))

        result = TimeUtils.parseTimeAndRound("2020-03-13T15:25:15.123456", ZoneOffset.UTC, true)
        result as FunctionResult.Success
        assertEquals("2020-03-13T15:25:15.123456999Z", dateTimeFormatter.format(result.value))

        result = TimeUtils.parseTimeAndRound("2020-03-13T152515.123456", ZoneOffset.UTC, false)
        result as FunctionResult.Success
        assertEquals("2020-03-13T15:25:15.123456000Z", dateTimeFormatter.format(result.value))

        result = TimeUtils.parseTimeAndRound("2020-03-13T152515.123456", ZoneOffset.UTC, true)
        result as FunctionResult.Success
        assertEquals("2020-03-13T15:25:15.123456999Z", dateTimeFormatter.format(result.value))
    }

    @Test
    internal fun `round to sql time`() {
        // it is important thing to work with java.sql.Timestamp
        TimeZone.setDefault(TimeZone.getTimeZone("UTC"))

        assertEquals(
            "> '2020-01-01 10:10:10.123456'::timestamp",
            TimeUtils.toSqlCompareExpression(">=", Instant.parse("2020-01-01T10:10:10.123456789Z")),
        )
        assertEquals(
            "< '2020-01-01 10:10:10.123457'::timestamp",
            TimeUtils.toSqlCompareExpression("<=", Instant.parse("2020-01-01T10:10:10.123456789Z")),
        )
        assertEquals(
            ">= '2020-01-01 10:10:10.123456'::timestamp",
            TimeUtils.toSqlCompareExpression(">=", Instant.parse("2020-01-01T10:10:10.123456Z")),
        )
        assertEquals(
            "<= '2020-01-01 10:10:10.123456'::timestamp",
            TimeUtils.toSqlCompareExpression("<=", Instant.parse("2020-01-01T10:10:10.123456Z")),
        )
    }

    @Suppress("ktlint:standard:indent")
    private fun createStatefulDownSampler(
        granularity: String,
        pagingFromStart: Boolean,
    ): FunctionResult<
            ApiError,
            (
                (Instant) -> Boolean
            )?,
            > = createStatefulDownSamplerConfig(granularity, pagingFromStart).map { createStatefulDownSampler(it!!) }

    @Suppress("ktlint:standard:indent")
    private fun createStatefulDownSamplerWithAlignment(
        granularity: String,
        pagingFromStart: Boolean,
        timezone: ZoneId = ZoneOffset.UTC,
    ): FunctionResult<
            ApiError,
            (
                (Instant) -> Boolean
            )?,
            > =
        createStatefulDownSamplerConfig(
            granularity = granularity,
            pagingFromStart = pagingFromStart,
            withAlignment = true,
            timezone = timezone,
        ).map { createStatefulDownSampler(it!!) }

    @Test
    fun `1d down sampling, paging_from=start`(): Unit =
        runBlocking {
            createStatefulDownSampler("1d", pagingFromStart = true)
                .map { downSampler ->
                    downSampler!!
                    assertTrue(downSampler.invoke(Instant.parse("2020-01-01T00:00:00Z")))
                    assertTrue(downSampler.invoke(Instant.parse("2020-01-02T00:00:00Z")))
                    assertFalse(downSampler.invoke(Instant.parse("2020-01-02T10:00:00Z")))
                    assertFalse(downSampler.invoke(Instant.parse("2020-01-02T23:59:59Z")))
                    assertTrue(downSampler.invoke(Instant.parse("2020-01-03T00:00:00Z")))
                }.getOrElse {
                    fail(it.toResponseObject().toString())
                }
        }

    @Test
    fun `1d down sampling, paging_from=end`(): Unit =
        runBlocking {
            createStatefulDownSampler("1d", pagingFromStart = false)
                .map { downSampler ->
                    downSampler!!
                    assertTrue(downSampler.invoke(Instant.parse("2020-01-03T00:00:00Z")))
                    assertFalse(downSampler.invoke(Instant.parse("2020-01-02T23:59:59Z")))
                    assertFalse(downSampler.invoke(Instant.parse("2020-01-02T10:00:00Z")))
                    assertTrue(downSampler.invoke(Instant.parse("2020-01-02T00:00:00Z")))
                    assertTrue(downSampler.invoke(Instant.parse("2020-01-01T00:00:00Z")))
                }.getOrElse {
                    fail(it.toResponseObject().toString())
                }
        }

    @Test
    fun `10s down sampling, paging_from=end with alignment`(): Unit =
        runBlocking {
            createStatefulDownSamplerWithAlignment(
                "10s",
                pagingFromStart = false,
            ).map { downSampler ->
                val result =
                    listOf(
                        Instant.parse("2025-01-03T01:46:00.321Z"),
                        Instant.parse("2025-01-03T01:45:55Z"),
                        Instant.parse("2025-01-03T01:45:40Z"),
                        Instant.parse("2025-01-03T01:45:35.111Z"),
                        Instant.parse("2025-01-03T01:45:30.111Z"),
                        Instant.parse("2025-01-03T01:45:29.333Z"),
                        Instant.parse("2025-01-03T01:45:22Z"),
                        Instant.parse("2025-01-03T01:45:21.555Z"),
                    ).filter {
                        downSampler!!.invoke(it)
                    }
                println(result)
                assertArrayEquals(
                    arrayOf(
                        Instant.parse("2025-01-03T01:46:00.321Z"),
                        Instant.parse("2025-01-03T01:45:55Z"),
                        Instant.parse("2025-01-03T01:45:40Z"),
                        Instant.parse("2025-01-03T01:45:29.333Z"),
                    ),
                    result.toTypedArray(),
                )
            }.getOrElse {
                fail(it.toResponseObject().toString())
            }
        }

    @Test
    fun `10s down sampling, paging_from=start with alignment`(): Unit =
        runBlocking {
            createStatefulDownSamplerWithAlignment(
                granularity = "10s",
                pagingFromStart = true,
            ).map { downSampler ->
                val result =
                    listOf(
                        Instant.parse("2025-01-03T01:45:21.555Z"),
                        Instant.parse("2025-01-03T01:45:22Z"),
                        Instant.parse("2025-01-03T01:45:29.333Z"),
                        Instant.parse("2025-01-03T01:45:30.111Z"),
                        Instant.parse("2025-01-03T01:45:35.111Z"),
                        Instant.parse("2025-01-03T01:45:40Z"),
                        Instant.parse("2025-01-03T01:45:55Z"),
                        Instant.parse("2025-01-03T01:46:00.321Z"),
                    ).filter {
                        downSampler!!.invoke(it)
                    }
                println(result)
                assertArrayEquals(
                    arrayOf(
                        Instant.parse("2025-01-03T01:45:21.555Z"),
                        Instant.parse("2025-01-03T01:45:30.111Z"),
                        Instant.parse("2025-01-03T01:45:40Z"),
                        Instant.parse("2025-01-03T01:45:55Z"),
                        Instant.parse("2025-01-03T01:46:00.321Z"),
                    ),
                    result.toTypedArray(),
                )
            }.getOrElse {
                fail(it.toResponseObject().toString())
            }
        }

    @Test
    fun `10s down sampling, paging_from=end with alignment and timezone`(): Unit =
        runBlocking {
            createStatefulDownSamplerWithAlignment(
                "10s",
                pagingFromStart = false,
                timezone = ZoneId.of("GMT+03:00"),
            ).map { downSampler ->
                val result =
                    listOf(
                        Instant.parse("2025-01-03T01:46:00.321Z"),
                        Instant.parse("2025-01-03T01:45:55Z"),
                        Instant.parse("2025-01-03T01:45:40Z"),
                        Instant.parse("2025-01-03T01:45:35.111Z"),
                        Instant.parse("2025-01-03T01:45:30.111Z"),
                        Instant.parse("2025-01-03T01:45:29.333Z"),
                        Instant.parse("2025-01-03T01:45:22Z"),
                        Instant.parse("2025-01-03T01:45:21.555Z"),
                    ).filter {
                        downSampler!!.invoke(it)
                    }
                println(result)
                assertArrayEquals(
                    arrayOf(
                        Instant.parse("2025-01-03T01:46:00.321Z"),
                        Instant.parse("2025-01-03T01:45:55Z"),
                        Instant.parse("2025-01-03T01:45:40Z"),
                        Instant.parse("2025-01-03T01:45:29.333Z"),
                    ),
                    result.toTypedArray(),
                )
            }.getOrElse {
                fail(it.toResponseObject().toString())
            }
        }

    @Test
    fun `10s down sampling, paging_from=start with alignment and timezone`(): Unit =
        runBlocking {
            createStatefulDownSamplerWithAlignment(
                granularity = "10s",
                pagingFromStart = true,
                timezone = ZoneId.of("GMT+03:00"),
            ).map { downSampler ->
                val result =
                    listOf(
                        Instant.parse("2025-01-03T01:45:21.555Z"),
                        Instant.parse("2025-01-03T01:45:22Z"),
                        Instant.parse("2025-01-03T01:45:29.333Z"),
                        Instant.parse("2025-01-03T01:45:30.111Z"),
                        Instant.parse("2025-01-03T01:45:35.111Z"),
                        Instant.parse("2025-01-03T01:45:40Z"),
                        Instant.parse("2025-01-03T01:45:55Z"),
                        Instant.parse("2025-01-03T01:46:00.321Z"),
                    ).filter {
                        downSampler!!.invoke(it)
                    }
                println(result)
                assertArrayEquals(
                    arrayOf(
                        Instant.parse("2025-01-03T01:45:21.555Z"),
                        Instant.parse("2025-01-03T01:45:30.111Z"),
                        Instant.parse("2025-01-03T01:45:40Z"),
                        Instant.parse("2025-01-03T01:45:55Z"),
                        Instant.parse("2025-01-03T01:46:00.321Z"),
                    ),
                    result.toTypedArray(),
                )
            }.getOrElse {
                fail(it.toResponseObject().toString())
            }
        }

    @Test
    fun `10s down sampling, paging_from=end`(): Unit =
        runBlocking {
            createStatefulDownSampler(
                "10s",
                pagingFromStart = false,
            ).map { downSampler ->
                val result =
                    listOf(
                        Instant.parse("2025-01-03T01:46:00.321Z"),
                        Instant.parse("2025-01-03T01:45:55Z"),
                        Instant.parse("2025-01-03T01:45:40Z"),
                        Instant.parse("2025-01-03T01:45:35.111Z"),
                        Instant.parse("2025-01-03T01:45:30.111Z"),
                        Instant.parse("2025-01-03T01:45:29.333Z"),
                        Instant.parse("2025-01-03T01:45:22Z"),
                        Instant.parse("2025-01-03T01:45:21.555Z"),
                    ).filter {
                        downSampler!!.invoke(it)
                    }
                println(result)
                assertArrayEquals(
                    arrayOf(
                        Instant.parse("2025-01-03T01:46:00.321Z"),
                        Instant.parse("2025-01-03T01:45:40Z"),
                        Instant.parse("2025-01-03T01:45:29.333Z"),
                    ),
                    result.toTypedArray(),
                )
            }.getOrElse {
                fail(it.toResponseObject().toString())
            }
        }

    @Test
    fun `10s down sampling, paging_from=start`(): Unit =
        runBlocking {
            createStatefulDownSampler(
                granularity = "10s",
                pagingFromStart = true,
            ).map { downSampler ->
                val result =
                    listOf(
                        Instant.parse("2025-01-03T01:45:21.555Z"),
                        Instant.parse("2025-01-03T01:45:22Z"),
                        Instant.parse("2025-01-03T01:45:29.333Z"),
                        Instant.parse("2025-01-03T01:45:30.111Z"),
                        Instant.parse("2025-01-03T01:45:35.111Z"),
                        Instant.parse("2025-01-03T01:45:40Z"),
                        Instant.parse("2025-01-03T01:45:55Z"),
                        Instant.parse("2025-01-03T01:46:00.321Z"),
                    ).filter {
                        downSampler!!.invoke(it)
                    }
                println(result)
                assertArrayEquals(
                    arrayOf(
                        Instant.parse("2025-01-03T01:45:21.555Z"),
                        Instant.parse("2025-01-03T01:45:35.111Z"),
                        Instant.parse("2025-01-03T01:45:55Z"),
                    ),
                    result.toTypedArray(),
                )
            }.getOrElse {
                fail(it.toResponseObject().toString())
            }
        }

    @Test
    fun `1m down sampling, paging_from=end with alignment`(): Unit =
        runBlocking {
            createStatefulDownSamplerWithAlignment(
                "1m",
                pagingFromStart = false,
            ).map { downSampler ->
                val result =
                    listOf(
                        Instant.parse("2024-01-03T02:00:30Z"),
                        Instant.parse("2024-01-03T01:59:59Z"),
                        Instant.parse("2024-01-03T01:58:03Z"),
                        Instant.parse("2024-01-03T01:58:02Z"),
                        Instant.parse("2024-01-03T01:58:01Z"),
                        Instant.parse("2024-01-03T01:57:00Z"),
                        Instant.parse("2024-01-03T01:55:00Z"),
                        Instant.parse("2024-01-03T01:53:30Z"),
                        Instant.parse("2024-01-03T01:53:10Z"),
                        Instant.parse("2024-01-03T01:51:35Z"),
                        Instant.parse("2024-01-03T01:51:15Z"),
                        Instant.parse("2024-01-03T01:49:00Z"),
                        Instant.parse("2024-01-03T01:47:49Z"),
                        Instant.parse("2024-01-03T01:47:40Z"),
                        Instant.parse("2024-01-03T01:47:25Z"),
                        Instant.parse("2024-01-03T01:45:21Z"),
                    ).filter {
                        downSampler!!.invoke(it)
                    }
                println(result)
                assertArrayEquals(
                    arrayOf(
                        Instant.parse("2024-01-03T02:00:30Z"),
                        Instant.parse("2024-01-03T01:59:59Z"),
                        Instant.parse("2024-01-03T01:58:03Z"),
                        Instant.parse("2024-01-03T01:57:00Z"),
                        Instant.parse("2024-01-03T01:55:00Z"),
                        Instant.parse("2024-01-03T01:53:30Z"),
                        Instant.parse("2024-01-03T01:51:35Z"),
                        Instant.parse("2024-01-03T01:49:00Z"),
                        Instant.parse("2024-01-03T01:47:49Z"),
                        Instant.parse("2024-01-03T01:45:21Z"),
                    ),
                    result.toTypedArray(),
                )
            }.getOrElse {
                fail(it.toResponseObject().toString())
            }
        }

    @Test
    fun `1m down sampling, paging_from=start with alignment`(): Unit =
        runBlocking {
            createStatefulDownSamplerWithAlignment(
                granularity = "1m",
                pagingFromStart = true,
            ).map { downSampler ->
                val result =
                    listOf(
                        Instant.parse("2024-01-03T01:45:21Z"),
                        Instant.parse("2024-01-03T01:47:25Z"),
                        Instant.parse("2024-01-03T01:47:40Z"),
                        Instant.parse("2024-01-03T01:47:49Z"),
                        Instant.parse("2024-01-03T01:49:00Z"),
                        Instant.parse("2024-01-03T01:51:15Z"),
                        Instant.parse("2024-01-03T01:53:10Z"),
                        Instant.parse("2024-01-03T01:55:00Z"),
                        Instant.parse("2024-01-03T01:57:00Z"),
                        Instant.parse("2024-01-03T01:58:01Z"),
                        Instant.parse("2024-01-03T01:59:59Z"),
                        Instant.parse("2024-01-03T02:00:30Z"),
                    ).filter {
                        downSampler!!.invoke(it)
                    }
                println(result)
                assertArrayEquals(
                    arrayOf(
                        Instant.parse("2024-01-03T01:45:21Z"),
                        Instant.parse("2024-01-03T01:47:25Z"),
                        Instant.parse("2024-01-03T01:49:00Z"),
                        Instant.parse("2024-01-03T01:51:15Z"),
                        Instant.parse("2024-01-03T01:53:10Z"),
                        Instant.parse("2024-01-03T01:55:00Z"),
                        Instant.parse("2024-01-03T01:57:00Z"),
                        Instant.parse("2024-01-03T01:58:01Z"),
                        Instant.parse("2024-01-03T01:59:59Z"),
                        Instant.parse("2024-01-03T02:00:30Z"),
                    ),
                    result.toTypedArray(),
                )
            }.getOrElse {
                fail(it.toResponseObject().toString())
            }
        }

    @Test
    fun `1m down sampling, paging_from=end with alignment and timezone`(): Unit =
        runBlocking {
            createStatefulDownSamplerWithAlignment(
                "1m",
                pagingFromStart = false,
                timezone = ZoneId.of("GMT+03:00"),
            ).map { downSampler ->
                val result =
                    listOf(
                        Instant.parse("2024-01-03T02:00:30Z"),
                        Instant.parse("2024-01-03T01:59:59Z"),
                        Instant.parse("2024-01-03T01:58:01Z"),
                        Instant.parse("2024-01-03T01:57:00Z"),
                        Instant.parse("2024-01-03T01:55:00Z"),
                        Instant.parse("2024-01-03T01:53:10Z"),
                        Instant.parse("2024-01-03T01:51:15Z"),
                        Instant.parse("2024-01-03T01:49:00Z"),
                        Instant.parse("2024-01-03T01:47:49Z"),
                        Instant.parse("2024-01-03T01:47:40Z"),
                        Instant.parse("2024-01-03T01:47:25Z"),
                        Instant.parse("2024-01-03T01:45:21Z"),
                    ).filter {
                        downSampler!!.invoke(it)
                    }
                println(result)
                assertArrayEquals(
                    arrayOf(
                        Instant.parse("2024-01-03T02:00:30Z"),
                        Instant.parse("2024-01-03T01:59:59Z"),
                        Instant.parse("2024-01-03T01:58:01Z"),
                        Instant.parse("2024-01-03T01:57:00Z"),
                        Instant.parse("2024-01-03T01:55:00Z"),
                        Instant.parse("2024-01-03T01:53:10Z"),
                        Instant.parse("2024-01-03T01:51:15Z"),
                        Instant.parse("2024-01-03T01:49:00Z"),
                        Instant.parse("2024-01-03T01:47:49Z"),
                        Instant.parse("2024-01-03T01:45:21Z"),
                    ),
                    result.toTypedArray(),
                )
            }.getOrElse {
                fail(it.toResponseObject().toString())
            }
        }

    @Test
    fun `1m down sampling, paging_from=start with alignment and timezone`(): Unit =
        runBlocking {
            createStatefulDownSamplerWithAlignment(
                granularity = "1m",
                pagingFromStart = true,
                timezone = ZoneId.of("GMT+03:00"),
            ).map { downSampler ->
                val result =
                    listOf(
                        Instant.parse("2024-01-03T01:45:21Z"),
                        Instant.parse("2024-01-03T01:47:25Z"),
                        Instant.parse("2024-01-03T01:47:40Z"),
                        Instant.parse("2024-01-03T01:47:49Z"),
                        Instant.parse("2024-01-03T01:49:00Z"),
                        Instant.parse("2024-01-03T01:51:15Z"),
                        Instant.parse("2024-01-03T01:53:10Z"),
                        Instant.parse("2024-01-03T01:55:00Z"),
                        Instant.parse("2024-01-03T01:57:00Z"),
                        Instant.parse("2024-01-03T01:58:01Z"),
                        Instant.parse("2024-01-03T01:59:59Z"),
                        Instant.parse("2024-01-03T02:00:30Z"),
                    ).filter {
                        downSampler!!.invoke(it)
                    }
                println(result)
                assertArrayEquals(
                    arrayOf(
                        Instant.parse("2024-01-03T01:45:21Z"),
                        Instant.parse("2024-01-03T01:47:25Z"),
                        Instant.parse("2024-01-03T01:49:00Z"),
                        Instant.parse("2024-01-03T01:51:15Z"),
                        Instant.parse("2024-01-03T01:53:10Z"),
                        Instant.parse("2024-01-03T01:55:00Z"),
                        Instant.parse("2024-01-03T01:57:00Z"),
                        Instant.parse("2024-01-03T01:58:01Z"),
                        Instant.parse("2024-01-03T01:59:59Z"),
                        Instant.parse("2024-01-03T02:00:30Z"),
                    ),
                    result.toTypedArray(),
                )
            }.getOrElse {
                fail(it.toResponseObject().toString())
            }
        }

    @Test
    fun `1h down sampling, paging_from=end with alignment`(): Unit =
        runBlocking {
            createStatefulDownSamplerWithAlignment(
                "1h",
                pagingFromStart = false,
            ).map { downSampler ->
                val result =
                    listOf(
                        Instant.parse("2024-01-03T02:00:00Z"),
                        Instant.parse("2024-01-03T00:30:00Z"),
                        Instant.parse("2024-01-03T00:00:00Z"),
                        Instant.parse("2024-01-02T23:59:59Z"),
                        Instant.parse("2024-01-02T10:00:00Z"),
                        Instant.parse("2024-01-02T00:00:01Z"),
                        Instant.parse("2024-01-01T00:59:00Z"),
                        Instant.parse("2024-01-01T00:58:00Z"),
                        Instant.parse("2024-01-01T00:01:00Z"),
                        Instant.parse("2024-01-01T00:00:00Z"),
                        Instant.parse("2023-12-31T23:00:00Z"),
                        Instant.parse("2023-12-31T15:00:00Z"),
                        Instant.parse("2023-12-31T01:00:00Z"),
                        Instant.parse("2023-12-30T23:59:00Z"),
                        Instant.parse("2023-12-30T13:13:00Z"),
                        Instant.parse("2023-12-30T00:00:00Z"),
                        Instant.parse("2023-12-29T09:00:00Z"),
                        Instant.parse("2023-12-29T08:30:00Z"),
                        Instant.parse("2023-12-29T05:59:00Z"),
                        Instant.parse("2023-12-29T04:04:00Z"),
                    ).filter {
                        downSampler!!.invoke(it)
                    }
                println(result)
                assertArrayEquals(
                    arrayOf(
                        Instant.parse("2024-01-03T02:00:00Z"),
                        Instant.parse("2024-01-03T00:30:00Z"),
                        Instant.parse("2024-01-03T00:00:00Z"),
                        Instant.parse("2024-01-02T10:00:00Z"),
                        Instant.parse("2024-01-02T00:00:01Z"),
                        Instant.parse("2024-01-01T00:59:00Z"),
                        Instant.parse("2024-01-01T00:00:00Z"),
                        Instant.parse("2023-12-31T23:00:00Z"),
                        Instant.parse("2023-12-31T15:00:00Z"),
                        Instant.parse("2023-12-31T01:00:00Z"),
                        Instant.parse("2023-12-30T23:59:00Z"),
                        Instant.parse("2023-12-30T13:13:00Z"),
                        Instant.parse("2023-12-30T00:00:00Z"),
                        Instant.parse("2023-12-29T09:00:00Z"),
                        Instant.parse("2023-12-29T05:59:00Z"),
                        Instant.parse("2023-12-29T04:04:00Z"),
                    ),
                    result.toTypedArray(),
                )
            }.getOrElse {
                fail(it.toResponseObject().toString())
            }
        }

    @Test
    fun `1h down sampling, paging_from=start with alignment`(): Unit =
        runBlocking {
            createStatefulDownSamplerWithAlignment(
                granularity = "1h",
                pagingFromStart = true,
            ).map { downSampler ->
                val result =
                    listOf(
                        Instant.parse("2023-12-29T04:04:00Z"),
                        Instant.parse("2023-12-29T05:59:00Z"),
                        Instant.parse("2023-12-29T08:30:00Z"),
                        Instant.parse("2023-12-29T09:00:00Z"),
                        Instant.parse("2023-12-30T00:00:00Z"),
                        Instant.parse("2023-12-30T13:13:00Z"),
                        Instant.parse("2023-12-30T23:59:00Z"),
                        Instant.parse("2023-12-31T01:00:00Z"),
                        Instant.parse("2023-12-31T15:00:00Z"),
                        Instant.parse("2023-12-31T23:00:00Z"),
                        Instant.parse("2024-01-01T00:00:00Z"),
                        Instant.parse("2024-01-01T00:01:00Z"),
                        Instant.parse("2024-01-01T00:58:00Z"),
                        Instant.parse("2024-01-01T00:59:00Z"),
                        Instant.parse("2024-01-02T00:00:01Z"),
                        Instant.parse("2024-01-02T10:00:00Z"),
                        Instant.parse("2024-01-02T23:59:59Z"),
                        Instant.parse("2024-01-03T00:00:00Z"),
                        Instant.parse("2024-01-03T00:30:00Z"),
                        Instant.parse("2024-01-03T02:00:00Z"),
                    ).filter {
                        downSampler!!.invoke(it)
                    }
                println(result)
                assertArrayEquals(
                    arrayOf(
                        Instant.parse("2023-12-29T04:04:00Z"),
                        Instant.parse("2023-12-29T05:59:00Z"),
                        Instant.parse("2023-12-29T08:30:00Z"),
                        Instant.parse("2023-12-29T09:00:00Z"),
                        Instant.parse("2023-12-30T00:00:00Z"),
                        Instant.parse("2023-12-30T13:13:00Z"),
                        Instant.parse("2023-12-30T23:59:00Z"),
                        Instant.parse("2023-12-31T01:00:00Z"),
                        Instant.parse("2023-12-31T15:00:00Z"),
                        Instant.parse("2023-12-31T23:00:00Z"),
                        Instant.parse("2024-01-01T00:00:00Z"),
                        Instant.parse("2024-01-02T00:00:01Z"),
                        Instant.parse("2024-01-02T10:00:00Z"),
                        Instant.parse("2024-01-02T23:59:59Z"),
                        Instant.parse("2024-01-03T00:00:00Z"),
                        Instant.parse("2024-01-03T02:00:00Z"),
                    ),
                    result.toTypedArray(),
                )
            }.getOrElse {
                fail(it.toResponseObject().toString())
            }
        }

    @Test
    fun `1d down sampling, paging_from=end with alignment`(): Unit =
        runBlocking {
            createStatefulDownSamplerWithAlignment(
                granularity = "1d",
                pagingFromStart = false,
            ).map { downSampler ->
                val result =
                    listOf(
                        Instant.parse("2024-01-03T00:30:00Z"),
                        Instant.parse("2024-01-03T00:00:00Z"),
                        Instant.parse("2024-01-02T23:59:59Z"),
                        Instant.parse("2024-01-02T10:00:00Z"),
                        Instant.parse("2024-01-02T00:00:01Z"),
                        Instant.parse("2024-01-01T00:59:00Z"),
                        Instant.parse("2024-01-01T00:58:00Z"),
                        Instant.parse("2024-01-01T00:01:00Z"),
                        Instant.parse("2024-01-01T00:00:00Z"),
                        Instant.parse("2023-12-31T23:00:00Z"),
                        Instant.parse("2023-12-31T15:00:00Z"),
                        Instant.parse("2023-12-31T01:00:00Z"),
                        Instant.parse("2023-12-30T23:59:00Z"),
                        Instant.parse("2023-12-30T13:13:00Z"),
                        Instant.parse("2023-12-30T00:00:00Z"),
                        Instant.parse("2023-12-29T09:00:00Z"),
                    ).filter {
                        downSampler!!.invoke(it)
                    }
                println(result)
                assertArrayEquals(
                    arrayOf(
                        Instant.parse("2024-01-03T00:30:00Z"),
                        Instant.parse("2024-01-03T00:00:00Z"),
                        Instant.parse("2024-01-01T00:59:00Z"),
                        Instant.parse("2024-01-01T00:00:00Z"),
                        Instant.parse("2023-12-30T23:59:00Z"),
                        Instant.parse("2023-12-30T00:00:00Z"),
                    ),
                    result.toTypedArray(),
                )
            }.getOrElse {
                fail(it.toResponseObject().toString())
            }
        }

    @Test
    fun `1d down sampling, paging_from=start with alignment`(): Unit =
        runBlocking {
            createStatefulDownSamplerWithAlignment(
                granularity = "1d",
                pagingFromStart = true,
            ).map { downSampler ->
                val result =
                    listOf(
                        Instant.parse("2023-12-29T09:00:00Z"),
                        Instant.parse("2023-12-30T00:00:00Z"),
                        Instant.parse("2023-12-30T13:13:00Z"),
                        Instant.parse("2023-12-30T23:59:00Z"),
                        Instant.parse("2023-12-31T01:00:00Z"),
                        Instant.parse("2023-12-31T15:00:00Z"),
                        Instant.parse("2023-12-31T23:00:00Z"),
                        Instant.parse("2024-01-01T00:00:00Z"),
                        Instant.parse("2024-01-01T00:01:00Z"),
                        Instant.parse("2024-01-01T00:58:00Z"),
                        Instant.parse("2024-01-01T00:59:00Z"),
                        Instant.parse("2024-01-02T00:00:01Z"),
                        Instant.parse("2024-01-02T10:00:00Z"),
                        Instant.parse("2024-01-02T23:59:59Z"),
                        Instant.parse("2024-01-03T00:00:00Z"),
                        Instant.parse("2024-01-03T00:30:00Z"),
                    ).filter {
                        downSampler!!.invoke(it)
                    }
                println(result)
                assertArrayEquals(
                    arrayOf(
                        Instant.parse("2023-12-29T09:00:00Z"),
                        Instant.parse("2023-12-30T00:00:00Z"),
                        Instant.parse("2023-12-31T01:00:00Z"),
                        Instant.parse("2024-01-01T00:00:00Z"),
                        Instant.parse("2024-01-02T00:00:01Z"),
                        Instant.parse("2024-01-03T00:00:00Z"),
                    ),
                    result.toTypedArray(),
                )
            }.getOrElse {
                fail(it.toResponseObject().toString())
            }
        }

    @Test
    fun `1d down sampling, paging_from=end with alignment and timezone`(): Unit =
        runBlocking {
            createStatefulDownSamplerWithAlignment(
                granularity = "1d",
                pagingFromStart = false,
                timezone = ZoneId.of("GMT+03:00"),
            ).map { downSampler ->
                val result =
                    listOf(
                        Instant.parse("2024-01-03T00:30:00Z"),
                        Instant.parse("2024-01-03T00:00:00Z"),
                        Instant.parse("2024-01-02T23:59:59Z"),
                        Instant.parse("2024-01-02T10:00:00Z"),
                        Instant.parse("2024-01-02T00:00:01Z"),
                        Instant.parse("2024-01-01T00:59:00Z"),
                        Instant.parse("2024-01-01T00:58:00Z"),
                        Instant.parse("2024-01-01T00:01:00Z"),
                        Instant.parse("2024-01-01T00:00:00Z"),
                        Instant.parse("2023-12-31T23:00:00Z"),
                        Instant.parse("2023-12-31T15:00:00Z"),
                        Instant.parse("2023-12-31T01:00:00Z"),
                        Instant.parse("2023-12-30T23:59:00Z"),
                        Instant.parse("2023-12-30T13:13:00Z"),
                        Instant.parse("2023-12-30T00:00:00Z"),
                        Instant.parse("2023-12-29T09:00:00Z"),
                    ).filter {
                        downSampler!!.invoke(it)
                    }
                println(result)
                assertArrayEquals(
                    arrayOf(
                        Instant.parse("2024-01-03T00:30:00Z"),
                        Instant.parse("2024-01-02T10:00:00Z"),
                        Instant.parse("2024-01-01T00:59:00Z"),
                        Instant.parse("2023-12-31T15:00:00Z"),
                        Instant.parse("2023-12-30T13:13:00Z"),
                        Instant.parse("2023-12-29T09:00:00Z"),
                    ),
                    result.toTypedArray(),
                )
            }.getOrElse {
                fail(it.toResponseObject().toString())
            }
        }

    @Test
    fun `1d down sampling, paging_from=start with alignment and timezone`(): Unit =
        runBlocking {
            createStatefulDownSamplerWithAlignment(
                granularity = "1d",
                pagingFromStart = true,
                timezone = ZoneId.of("GMT+03:00"),
            ).map { downSampler ->
                val result =
                    listOf(
                        Instant.parse("2023-12-29T09:00:00Z"),
                        Instant.parse("2023-12-30T00:00:00Z"),
                        Instant.parse("2023-12-30T13:13:00Z"),
                        Instant.parse("2023-12-30T23:59:00Z"),
                        Instant.parse("2023-12-31T01:00:00Z"),
                        Instant.parse("2023-12-31T15:00:00Z"),
                        Instant.parse("2023-12-31T23:00:00Z"),
                        Instant.parse("2024-01-01T00:00:00Z"),
                        Instant.parse("2024-01-01T00:01:00Z"),
                        Instant.parse("2024-01-01T00:58:00Z"),
                        Instant.parse("2024-01-01T00:59:00Z"),
                        Instant.parse("2024-01-02T00:00:01Z"),
                        Instant.parse("2024-01-02T10:00:00Z"),
                        Instant.parse("2024-01-02T23:59:59Z"),
                        Instant.parse("2024-01-03T00:00:00Z"),
                        Instant.parse("2024-01-03T00:30:00Z"),
                    ).filter {
                        downSampler!!.invoke(it)
                    }
                println(result)
                assertArrayEquals(
                    arrayOf(
                        Instant.parse("2023-12-29T09:00:00Z"),
                        Instant.parse("2023-12-30T00:00:00Z"),
                        Instant.parse("2023-12-30T23:59:00Z"),
                        Instant.parse("2023-12-31T23:00:00Z"),
                        Instant.parse("2024-01-02T00:00:01Z"),
                        Instant.parse("2024-01-02T23:59:59Z"),
                    ),
                    result.toTypedArray(),
                )
            }.getOrElse {
                fail(it.toResponseObject().toString())
            }
        }

    @Test
    fun `1d down sampling, paging_from=end with alignment and timezone and 30 minutes values`(): Unit =
        runBlocking {
            createStatefulDownSamplerWithAlignment(
                granularity = "1d",
                pagingFromStart = false,
                timezone = ZoneId.of("GMT+03:00"),
            ).map { downSampler ->
                val result =
                    listOf(
                        Instant.parse("2024-01-03T01:00:00Z"),
                        Instant.parse("2024-01-03T00:30:00Z"),
                        Instant.parse("2024-01-03T00:00:00Z"),
                        Instant.parse("2024-01-02T23:00:00Z"),
                        Instant.parse("2024-01-02T22:00:00Z"),
                        Instant.parse("2024-01-02T21:30:00Z"),
                        Instant.parse("2024-01-02T21:00:00Z"),
                        Instant.parse("2024-01-02T20:30:00Z"),
                        Instant.parse("2024-01-02T20:00:00Z"),
                        Instant.parse("2024-01-02T01:00:00Z"),
                        Instant.parse("2024-01-02T00:30:00Z"),
                        Instant.parse("2024-01-02T00:00:00Z"),
                        Instant.parse("2024-01-01T23:00:00Z"),
                        Instant.parse("2024-01-01T22:00:00Z"),
                        Instant.parse("2024-01-01T21:30:00Z"),
                        Instant.parse("2024-01-01T21:00:00Z"),
                        Instant.parse("2024-01-01T20:30:00Z"),
                        Instant.parse("2024-01-01T20:00:00Z"),
                    ).filter {
                        downSampler!!.invoke(it)
                    }
                println(result)
                assertArrayEquals(
                    arrayOf(
                        Instant.parse("2024-01-03T01:00:00Z"),
                        Instant.parse("2024-01-02T21:00:00Z"),
                        Instant.parse("2024-01-01T21:00:00Z"),
                    ),
                    result.toTypedArray(),
                )
            }.getOrElse {
                fail(it.toResponseObject().toString())
            }
        }

    @Test
    fun `1d down sampling, paging_from=start with alignment and timezone and 30 minutes values`(): Unit =
        runBlocking {
            createStatefulDownSamplerWithAlignment(
                granularity = "1d",
                pagingFromStart = true,
                timezone = ZoneId.of("GMT+03:00"),
            ).map { downSampler ->
                val result =
                    listOf(
                        Instant.parse("2024-01-01T20:00:00Z"),
                        Instant.parse("2024-01-01T20:30:00Z"),
                        Instant.parse("2024-01-01T21:00:00Z"),
                        Instant.parse("2024-01-01T21:30:00Z"),
                        Instant.parse("2024-01-01T22:00:00Z"),
                        Instant.parse("2024-01-01T23:00:00Z"),
                        Instant.parse("2024-01-02T00:00:00Z"),
                        Instant.parse("2024-01-02T00:30:00Z"),
                        Instant.parse("2024-01-02T01:00:00Z"),
                        Instant.parse("2024-01-02T20:00:00Z"),
                        Instant.parse("2024-01-02T20:30:00Z"),
                        Instant.parse("2024-01-02T21:00:00Z"),
                        Instant.parse("2024-01-02T21:30:00Z"),
                        Instant.parse("2024-01-02T22:00:00Z"),
                        Instant.parse("2024-01-02T23:00:00Z"),
                        Instant.parse("2024-01-03T00:00:00Z"),
                        Instant.parse("2024-01-03T00:30:00Z"),
                        Instant.parse("2024-01-03T01:00:00Z"),
                    ).filter {
                        downSampler!!.invoke(it)
                    }
                println(result)
                assertArrayEquals(
                    arrayOf(
                        Instant.parse("2024-01-01T20:00:00Z"),
                        Instant.parse("2024-01-01T21:00:00Z"),
                        Instant.parse("2024-01-02T21:00:00Z"),
                    ),
                    result.toTypedArray(),
                )
            }.getOrElse {
                fail(it.toResponseObject().toString())
            }
        }

    @Test
    fun `1h down sampling, paging_from=start`(): Unit =
        runBlocking {
            createStatefulDownSampler("1h", pagingFromStart = true)
                .map { downSampler ->
                    downSampler!!
                    assertTrue(downSampler.invoke(Instant.parse("2020-01-01T00:00:00Z")))
                    assertTrue(downSampler.invoke(Instant.parse("2020-01-01T01:00:00Z")))
                    assertFalse(downSampler.invoke(Instant.parse("2020-01-01T01:30:00Z")))
                    assertFalse(downSampler.invoke(Instant.parse("2020-01-01T01:59:59Z")))
                    assertTrue(downSampler.invoke(Instant.parse("2020-01-01T02:00:00Z")))
                }.getOrElse {
                    fail(it.toResponseObject().toString())
                }
        }

    @Test
    fun `1h down sampling, paging_from=end`(): Unit =
        runBlocking {
            createStatefulDownSampler("1h", pagingFromStart = false)
                .map { downSampler ->
                    downSampler!!
                    assertTrue(downSampler.invoke(Instant.parse("2020-01-01T02:00:00Z")))
                    assertFalse(downSampler.invoke(Instant.parse("2020-01-01T01:59:59Z")))
                    assertFalse(downSampler.invoke(Instant.parse("2020-01-01T01:30:00Z")))
                    assertTrue(downSampler.invoke(Instant.parse("2020-01-01T01:00:00Z")))
                    assertTrue(downSampler.invoke(Instant.parse("2020-01-01T00:00:00Z")))
                }.getOrElse {
                    fail(it.toResponseObject().toString())
                }
        }

    @Test
    fun `1m down sampling, paging_from=start`(): Unit =
        runBlocking {
            createStatefulDownSampler("1m", pagingFromStart = true)
                .map { downSampler ->
                    downSampler!!
                    assertTrue(downSampler.invoke(Instant.parse("2020-01-01T00:00:00Z")))
                    assertTrue(downSampler.invoke(Instant.parse("2020-01-01T00:01:00Z")))
                    assertFalse(downSampler.invoke(Instant.parse("2020-01-01T00:01:30Z")))
                    assertFalse(downSampler.invoke(Instant.parse("2020-01-01T00:01:59Z")))
                    assertTrue(downSampler.invoke(Instant.parse("2020-01-01T00:02:00Z")))
                }.getOrElse {
                    fail(it.toResponseObject().toString())
                }
        }

    @Test
    fun `1m down sampling, paging_from=end`(): Unit =
        runBlocking {
            createStatefulDownSampler("1m", pagingFromStart = false)
                .map { downSampler ->
                    downSampler!!
                    assertTrue(downSampler.invoke(Instant.parse("2020-01-01T00:02:00Z")))
                    assertFalse(downSampler.invoke(Instant.parse("2020-01-01T00:01:59Z")))
                    assertFalse(downSampler.invoke(Instant.parse("2020-01-01T00:01:30Z")))
                    assertTrue(downSampler.invoke(Instant.parse("2020-01-01T00:01:00Z")))
                    assertTrue(downSampler.invoke(Instant.parse("2020-01-01T00:00:00Z")))
                }.getOrElse {
                    fail(it.toResponseObject().toString())
                }
        }

    @Test
    fun `1s down sampling, paging_from=start`(): Unit =
        runBlocking {
            createStatefulDownSampler("1s", pagingFromStart = true)
                .map { downSampler ->
                    downSampler!!
                    assertTrue(downSampler.invoke(Instant.parse("2020-01-01T00:00:00Z")))
                    assertTrue(downSampler.invoke(Instant.parse("2020-01-01T00:00:01Z")))
                    assertFalse(downSampler.invoke(Instant.parse("2020-01-01T00:00:01.500Z")))
                    assertFalse(downSampler.invoke(Instant.parse("2020-01-01T00:00:01.999Z")))
                    assertTrue(downSampler.invoke(Instant.parse("2020-01-01T00:00:02Z")))
                }.getOrElse {
                    fail(it.toResponseObject().toString())
                }
        }

    @Test
    fun `1s down sampling, paging_from=end`(): Unit =
        runBlocking {
            createStatefulDownSampler("1s", pagingFromStart = false)
                .map { downSampler ->
                    downSampler!!
                    assertTrue(downSampler.invoke(Instant.parse("2020-01-01T00:00:02Z")))
                    assertFalse(downSampler.invoke(Instant.parse("2020-01-01T00:00:01.999Z")))
                    assertFalse(downSampler.invoke(Instant.parse("2020-01-01T00:00:01.500Z")))
                    assertTrue(downSampler.invoke(Instant.parse("2020-01-01T00:00:01Z")))
                    assertTrue(downSampler.invoke(Instant.parse("2020-01-01T00:00:00Z")))
                }.getOrElse {
                    fail(it.toResponseObject().toString())
                }
        }

    @Test
    fun `unsupported down sampling`(): Unit =
        runBlocking {
            createStatefulDownSampler("5s", pagingFromStart = true)
                .map {
                    fail<Unit>("Must fail")
                }.getOrElse {
                    assertEquals(
                        ErrorResponse(
                            ErrorObject(
                                type = "bad_parameter",
                                message = "Bad parameter 'granularity'. Value '5s' is not supported. Supported values are 'raw', '1d', '1h', '1m', '1s'.",
                            ),
                        ),
                        it.toResponseObject(),
                    )
                }
        }

    // ↓ Test cases for TimeUtils.getEnforcedStartTime(List<String>, Instant, Instant) ↓

    @ParameterizedTest(name = "should return correct enforce start time when time restriction {0}")
    @MethodSource("io.coinmetrics.api.utils.TimeUtilsTest\$EnforcedStartTimeTestData#arguments")
    fun `test enforce start time`(
        timeRestrictions: List<String>,
        maxTimeValue: String,
        requestReceivedAt: Instant,
        expected: Instant?,
    ) {
        val actual = TimeUtils.getEnforcedStartTime(timeRestrictions, ZoneOffset.UTC, maxTimeValue, requestReceivedAt)
        assertEquals(expected, actual)
    }

    private class EnforcedStartTimeTestData {
        companion object {
            val statistics: DataAvailabilityTimeRange =
                DataAvailabilityTimeRange(
                    minTime = Instant.now().minus(365, ChronoUnit.DAYS).toLazyInstant(),
                    maxTime = Instant.now().minus(1, ChronoUnit.DAYS).toLazyInstant(),
                )
            private val requestReceivedAt: Instant = Instant.now()

            @JvmStatic
            private fun arguments(): List<Arguments?> =
                listOf(
                    Arguments.of(
                        listOf("null"),
                        statistics.maxTime.toString(),
                        requestReceivedAt,
                        null,
                    ),
                    Arguments.of(
                        listOf("null", "now-30d"),
                        statistics.maxTime.toString(),
                        requestReceivedAt,
                        null,
                    ),
                    Arguments.of(
                        listOf("latest-30d", "latest-60d", "now-30d"),
                        statistics.maxTime.toString(),
                        requestReceivedAt,
                        requestReceivedAt.minus(30, ChronoUnit.DAYS), // now-30d
                    ),
                    Arguments.of(
                        listOf("latest-30d", "latest-60d"),
                        statistics.maxTime.toString(),
                        requestReceivedAt,
                        statistics.maxTime.value.minus(30, ChronoUnit.DAYS), // latest-30d
                    ),
                )
        }
    }
}
