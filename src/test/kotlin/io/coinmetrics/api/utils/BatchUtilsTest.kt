package io.coinmetrics.api.utils

import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.models.PagingFrom
import io.coinmetrics.api.utils.paging.getPage
import io.coinmetrics.api.utils.streams.BufferedSuspendableStream
import io.coinmetrics.api.utils.streams.SuspendableStream
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.slf4j.LoggerFactory
import java.util.concurrent.atomic.AtomicBoolean
import kotlin.coroutines.coroutineContext

class BatchUtilsTest {
    private val data =
        listOf(
            Item("a", 1),
            Item("a", 2),
            Item("a", 3),
            Item("b", 4),
            Item("c", 5),
            Item("c", 6),
        )

    @Test
    fun `batch of one item`() {
        runBlocking {
            for (pageSize in 1..5) {
                assertEquals(listOf(1, 2, 3), getAllPages("a", pageSize)) { "Test failed with pageSize $pageSize." }
            }

            for (pageSize in 1..5) {
                assertEquals(listOf(4), getAllPages("b", pageSize)) { "Test failed with pageSize $pageSize." }
            }

            for (pageSize in 1..5) {
                assertEquals(listOf(5, 6), getAllPages("c", pageSize)) { "Test failed with pageSize $pageSize." }
            }
        }
    }

    @Test
    fun `batch of two items`() {
        runBlocking {
            for (pageSize in 1..8) {
                assertEquals(listOf(1, 2, 3, 5, 6), getAllPages("a,c", pageSize)) { "Test failed with pageSize $pageSize." }
            }

            for (pageSize in 1..8) {
                assertEquals(listOf(1, 2, 3, 5, 6), getAllPages("c,a", pageSize)) { "Test failed with pageSize $pageSize." }
            }
        }
    }

    @Test
    fun `batch of three items`() {
        runBlocking {
            for (pageSize in 1..10) {
                assertEquals(listOf(1, 2, 3, 4, 5, 6), getAllPages("a,b,c", pageSize)) { "Test failed with pageSize $pageSize." }
            }

            for (pageSize in 1..10) {
                assertEquals(listOf(1, 2, 3, 4, 5, 6), getAllPages("c,b,a", pageSize)) { "Test failed with pageSize $pageSize." }
            }
        }
    }

    private suspend fun getAllPages(
        ids: String,
        pageSize: Int,
    ): List<Int> {
        var pageToken: String? = null
        val numbers = ArrayList<Int>()
        do {
            val response = batch(ids, pageSize, pageToken)
            numbers.addAll(response.data.map { it.value })
            pageToken = response.nextPageToken
        } while (pageToken != null)
        return numbers
    }

    private suspend fun batch(
        ids: String,
        pageSize: Int,
        pageToken: String?,
    ): Response {
        fun makeStream(
            id: String,
            state: Int?,
        ) = BufferedSuspendableStream(
            initialState = state?.let { State(it) },
            bufferSize = 2,
            bufferLoader =
                bufferLoader(
                    dataSource = data,
                    pagingFromStart = true,
                    filter = id,
                ),
            stateResolver = { State(it.value) },
        )
        val streamSupplierCalled = AtomicBoolean()
        val streamSupplier: (suspend (streamId: String, initialState: Int?, streamSpecificData: String) -> SuspendableStream<Item, *>) = { id, state, streamSpecificData ->
            streamSupplierCalled.set(true)
            assertEquals("streamSpecificData: $id", streamSpecificData)
            makeStream(id, state)
        }

        val stream =
            (
                BatchUtils.sortIdsAndConcatStreams(
                    streams = ids.split(",").asSequence().map { it to "streamSpecificData: $it" },
                    streamIdsAreResolvedDynamically = false,
                    pagingFrom = PagingFrom.START,
                    nextPageToken = pageToken,
                    initialStreamStateParser = { it.toInt() },
                    streamSupplier = streamSupplier,
                    httpRequestCoroutineContext = coroutineContext,
                    logger = LoggerFactory.getLogger(javaClass),
                ) as FunctionResult.Success
            ).value

        assertFalse(streamSupplierCalled.get())

        @Suppress("DEPRECATION")
        val page = stream.getPage(pageSize, pagingFromStart = true)

        assertTrue(streamSupplierCalled.get())

        return Response(
            data = page.items.toTypedArray(),
            nextPageToken = page.nextPageToken,
        )
    }

    private fun bufferLoader(
        dataSource: List<Item>,
        pagingFromStart: Boolean,
        filter: String,
    ): suspend (State?, Int) -> List<Item> {
        // state is a metric "time"
        return { state, bufferSize ->
            when {
                state == null -> dataSource.filter { it.id == filter }.take(bufferSize)
                pagingFromStart -> dataSource.filter { it.value > state.value && it.id == filter }.take(bufferSize)
                else -> dataSource.filter { it.value < state.value && it.id == filter }.take(bufferSize)
            }
        }
    }

    private class Item(
        val id: String,
        val value: Int,
    )

    private class Response(
        val data: Array<Item>,
        val nextPageToken: String?,
    )

    private data class State(
        val value: Int,
    ) : SuspendableStream.State {
        override fun serialize() = value.toString()
    }
}
