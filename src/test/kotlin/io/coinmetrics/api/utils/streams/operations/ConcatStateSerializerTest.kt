package io.coinmetrics.api.utils.streams.operations

import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.models.PagingFrom
import io.coinmetrics.api.utils.streams.SuspendableStream
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test

class ConcatStateSerializerTest {
    @Test
    fun `paging_from=start`() {
        val streamIds = listOf("a", "b", "c", "d")
        val concatState =
            ConcatState(
                activeStreamId = "c",
                activeStreamState = State("123"),
                activeStreamIndex = null,
                activeStreamEmitted = 3,
            )
        val pageToken = ConcatStateSerializer.serialize(concatState)
        val result = ConcatStateSerializer.deserialize(pageToken, streamIds, pagingFrom = PagingFrom.START) as FunctionResult.Success
        Assertions.assertEquals(2, result.value.activeStreamIndex)
        Assertions.assertEquals(concatState.activeStreamState, State(result.value.activeStreamState!!))
        Assertions.assertEquals(concatState.activeStreamEmitted, result.value.activeStreamEmitted)
    }

    @Test
    fun `paging_from=end`() {
        val streamIds = listOf("d", "c", "b", "a")
        val concatState =
            ConcatState(
                activeStreamId = "c",
                activeStreamState = State("123"),
                activeStreamIndex = null,
                activeStreamEmitted = 3,
            )
        val pageToken = ConcatStateSerializer.serialize(concatState)
        val result = ConcatStateSerializer.deserialize(pageToken, streamIds, pagingFrom = PagingFrom.END) as FunctionResult.Success
        Assertions.assertEquals(1, result.value.activeStreamIndex)
        Assertions.assertEquals(concatState.activeStreamState, State(result.value.activeStreamState!!))
        Assertions.assertEquals(concatState.activeStreamEmitted, result.value.activeStreamEmitted)
    }
}

private data class State(
    val value: String,
) : SuspendableStream.State {
    override fun serialize() = value
}
