package io.coinmetrics.api.utils.streams.operations

import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.utils.ComparablePair
import io.coinmetrics.api.utils.paging.getPage
import io.coinmetrics.api.utils.streams.BufferedSuspendableStream
import io.coinmetrics.api.utils.streams.SuspendableStream
import io.coinmetrics.api.utils.streams.asMergeSource
import kotlinx.coroutines.flow.asFlow
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import kotlin.math.ceil
import kotlin.math.min

@Suppress("DEPRECATION")
class OperationsTest {
    private fun bufferLoader(
        dataSource: List<Metric>,
        pagingFromStart: Boolean = true,
    ): suspend (State?, Int) -> List<Metric> {
        // state is a metric "time"
        return { state, bufferSize ->
            when {
                state == null -> dataSource.subList(0, min(bufferSize, dataSource.size))
                pagingFromStart -> dataSource.filter { it.time > state.value }.take(bufferSize)
                else -> dataSource.filter { it.time < state.value }.take(bufferSize)
            }
        }
    }

    @ParameterizedTest
    @ValueSource(ints = [1, 2, 3, 4, 5, 6, 7])
    fun `join by time`(bufferSize: Int) =
        runBlocking {
            val metric1DataSource =
                listOf(
                    Metric(
                        1,
                        101,
                    ),
                    Metric(
                        2,
                        102,
                    ),
                    Metric(
                        3,
                        103,
                    ),
                    Metric(
                        4,
                        104,
                    ),
                )
            val metric2DataSource =
                listOf(
                    Metric(
                        1,
                        201,
                    ),
                    Metric(
                        2,
                        202,
                    ),
                    Metric(
                        3,
                        203,
                    ),
                    Metric(
                        4,
                        204,
                    ),
                )

            val stream1 =
                BufferedSuspendableStream(
                    initialState = null,
                    bufferSize = bufferSize,
                    bufferLoader = bufferLoader(metric1DataSource, true),
                    stateResolver = { State(it.time) },
                ).asMergeSource { it.time } // merge key "time"

            val stream2 =
                BufferedSuspendableStream(
                    initialState = null,
                    bufferSize = bufferSize,
                    bufferLoader = bufferLoader(metric2DataSource, true),
                    stateResolver = { State(it.time) },
                ).asMergeSource { it.time } // merge key "time"

            val resultStream =
                listOf(stream1, stream2).mergeJoin(null, true) {
                    val item1 = it[0] as Metric
                    val item2 = it[1] as Metric
                    MergedMetrics(
                        item1.time,
                        item1.value,
                        item2.value,
                    )
                }

            val page = resultStream.getPage(4, true)
            assertEquals(
                listOf(
                    MergedMetrics(
                        1,
                        101,
                        201,
                    ),
                    MergedMetrics(
                        2,
                        102,
                        202,
                    ),
                    MergedMetrics(
                        3,
                        103,
                        203,
                    ),
                    MergedMetrics(
                        4,
                        104,
                        204,
                    ),
                ),
                page.items,
            )
            assertNull(page.nextPageToken)
        }

    @ParameterizedTest
    @ValueSource(ints = [1, 2, 3, 4, 5, 6, 7])
    fun `join by time, missing data`(bufferSize: Int) =
        runBlocking {
            val metric1DataSource =
                listOf(
                    Metric(
                        1,
                        101,
                    ),
                    Metric(
                        2,
                        102,
                    ),
                    Metric(
                        3,
                        103,
                    ),
                    Metric(
                        4,
                        104,
                    ),
                )
            val metric2DataSource =
                listOf(
                    Metric(
                        1,
                        201,
                    ),
                    Metric(
                        3,
                        203,
                    ),
                    Metric(
                        4,
                        204,
                    ),
                )

            val stream1 =
                BufferedSuspendableStream(
                    initialState = null,
                    bufferSize = bufferSize,
                    bufferLoader = bufferLoader(metric1DataSource, true),
                    stateResolver = { State(it.time) },
                ).asMergeSource { it.time } // merge key "time"

            val stream2 =
                BufferedSuspendableStream(
                    initialState = null,
                    bufferSize = bufferSize,
                    bufferLoader = bufferLoader(metric2DataSource, true),
                    stateResolver = { State(it.time) },
                ).asMergeSource { it.time } // merge key "time"

            val resultStream =
                listOf(stream1, stream2).mergeJoin(null, true) {
                    val item1 = it[0]
                    val item2 = it[1]
                    MergedMetrics(
                        item1?.time ?: item2!!.time,
                        item1?.value,
                        item2?.value,
                    )
                }

            val page = resultStream.getPage(4, true)
            assertEquals(
                listOf(
                    MergedMetrics(
                        1,
                        101,
                        201,
                    ),
                    MergedMetrics(
                        2,
                        102,
                        null,
                    ),
                    MergedMetrics(
                        3,
                        103,
                        203,
                    ),
                    MergedMetrics(
                        4,
                        104,
                        204,
                    ),
                ),
                page.items,
            )
            assertNull(page.nextPageToken)
        }

    @ParameterizedTest
    @ValueSource(ints = [1, 2, 3, 4, 5, 6, 7])
    fun `join by time, reverse`(bufferSize: Int) =
        runBlocking {
            val metric1DataSource =
                listOf(
                    Metric(
                        4,
                        104,
                    ),
                    Metric(
                        3,
                        103,
                    ),
                    Metric(
                        2,
                        102,
                    ),
                    Metric(
                        1,
                        101,
                    ),
                )
            val metric2DataSource =
                listOf(
                    Metric(
                        4,
                        204,
                    ),
                    Metric(
                        3,
                        203,
                    ),
                    Metric(
                        2,
                        202,
                    ),
                    Metric(
                        1,
                        201,
                    ),
                )

            val stream1 =
                BufferedSuspendableStream(
                    initialState = null,
                    bufferSize = bufferSize,
                    bufferLoader = bufferLoader(metric1DataSource, false),
                    stateResolver = { State(it.time) },
                ).asMergeSource { it.time } // merge key "time"

            val stream2 =
                BufferedSuspendableStream(
                    initialState = null,
                    bufferSize = bufferSize,
                    bufferLoader = bufferLoader(metric2DataSource, false),
                    stateResolver = { State(it.time) },
                ).asMergeSource { it.time } // merge key "time"

            val resultStream =
                listOf(stream1, stream2).mergeJoin(null, false) {
                    val item1 = it[0] as Metric
                    val item2 = it[1] as Metric
                    MergedMetrics(
                        item1.time,
                        item1.value,
                        item2.value,
                    )
                }

            val page = resultStream.getPage(4, false)
            assertEquals(
                listOf(
                    MergedMetrics(
                        1,
                        101,
                        201,
                    ),
                    MergedMetrics(
                        2,
                        102,
                        202,
                    ),
                    MergedMetrics(
                        3,
                        103,
                        203,
                    ),
                    MergedMetrics(
                        4,
                        104,
                        204,
                    ),
                ),
                page.items,
            )
            assertNull(page.nextPageToken)
        }

    @ParameterizedTest
    @ValueSource(ints = [1, 2, 3, 4, 5, 6, 7])
    fun `join by time, reverse, missing data`(bufferSize: Int) =
        runBlocking {
            val metric1DataSource =
                listOf(
                    Metric(
                        4,
                        104,
                    ),
                    Metric(
                        3,
                        103,
                    ),
                    Metric(
                        2,
                        102,
                    ),
                    Metric(
                        1,
                        101,
                    ),
                )
            val metric2DataSource =
                listOf(
                    Metric(
                        4,
                        204,
                    ),
                    Metric(
                        3,
                        203,
                    ),
                    Metric(
                        1,
                        201,
                    ),
                )

            val stream1 =
                BufferedSuspendableStream(
                    initialState = null,
                    bufferSize = bufferSize,
                    bufferLoader = bufferLoader(metric1DataSource, false),
                    stateResolver = { State(it.time) },
                ).asMergeSource { it.time } // merge key "time"

            val stream2 =
                BufferedSuspendableStream(
                    initialState = null,
                    bufferSize = bufferSize,
                    bufferLoader = bufferLoader(metric2DataSource, false),
                    stateResolver = { State(it.time) },
                ).asMergeSource { it.time } // merge key "time"

            val resultStream =
                listOf(stream1, stream2).mergeJoin(null, false) {
                    val item1 = it[0]
                    val item2 = it[1]
                    MergedMetrics(
                        item1?.time ?: item2!!.time,
                        item1?.value,
                        item2?.value,
                    )
                }

            val page = resultStream.getPage(4, false)
            assertEquals(
                listOf(
                    MergedMetrics(
                        1,
                        101,
                        201,
                    ),
                    MergedMetrics(
                        2,
                        102,
                        null,
                    ),
                    MergedMetrics(
                        3,
                        103,
                        203,
                    ),
                    MergedMetrics(
                        4,
                        104,
                        204,
                    ),
                ),
                page.items,
            )
            assertNull(page.nextPageToken)
        }

    @ParameterizedTest
    @ValueSource(ints = [1, 2, 3, 4, 5, 6, 7])
    fun `join by time, missing data, peek`(bufferSize: Int) =
        runBlocking {
            val metric1DataSource =
                listOf(
                    Metric(
                        1,
                        101,
                    ),
                    Metric(
                        2,
                        102,
                    ),
                    Metric(
                        3,
                        103,
                    ),
                    Metric(
                        4,
                        104,
                    ),
                )
            val metric2DataSource =
                listOf(
                    Metric(
                        1,
                        201,
                    ),
                    Metric(
                        3,
                        203,
                    ),
                    Metric(
                        4,
                        204,
                    ),
                )

            val stream1 =
                BufferedSuspendableStream(
                    initialState = null,
                    bufferSize = bufferSize,
                    bufferLoader = bufferLoader(metric1DataSource, true),
                    stateResolver = { State(it.time) },
                ).asMergeSource { it.time } // merge key "time"

            val stream2 =
                BufferedSuspendableStream(
                    initialState = null,
                    bufferSize = bufferSize,
                    bufferLoader = bufferLoader(metric2DataSource, true),
                    stateResolver = { State(it.time) },
                ).asMergeSource { it.time } // merge key "time"

            val resultStream =
                listOf(stream1, stream2).mergeJoin(null, true) {
                    val item1 = it[0]
                    val item2 = it[1]
                    MergedMetrics(
                        item1?.time ?: item2!!.time,
                        item1?.value,
                        item2?.value,
                    )
                }

            assertEquals(
                MergedMetrics(
                    1,
                    101,
                    201,
                ),
                resultStream.peek(),
            )
            assertEquals(
                MergedMetrics(
                    1,
                    101,
                    201,
                ),
                resultStream.peek(),
            )
            assertEquals(
                MergedMetrics(
                    1,
                    101,
                    201,
                ),
                resultStream.poll(),
            )

            assertEquals(
                MergedMetrics(
                    2,
                    102,
                    null,
                ),
                resultStream.peek(),
            )
            assertEquals(
                MergedMetrics(
                    2,
                    102,
                    null,
                ),
                resultStream.peek(),
            )
            assertEquals(
                MergedMetrics(
                    2,
                    102,
                    null,
                ),
                resultStream.poll(),
            )

            assertEquals(
                MergedMetrics(
                    3,
                    103,
                    203,
                ),
                resultStream.peek(),
            )
            assertEquals(
                MergedMetrics(
                    3,
                    103,
                    203,
                ),
                resultStream.peek(),
            )
            assertEquals(
                MergedMetrics(
                    3,
                    103,
                    203,
                ),
                resultStream.poll(),
            )

            assertEquals(
                MergedMetrics(
                    4,
                    104,
                    204,
                ),
                resultStream.peek(),
            )
            assertEquals(
                MergedMetrics(
                    4,
                    104,
                    204,
                ),
                resultStream.peek(),
            )
            assertEquals(
                MergedMetrics(
                    4,
                    104,
                    204,
                ),
                resultStream.poll(),
            )

            assertNull(resultStream.peek())
            assertNull(resultStream.peek())
            assertNull(resultStream.poll())
            assertNull(resultStream.poll())
        }

    @ParameterizedTest
    @ValueSource(ints = [1, 2, 3, 4, 5, 6, 7])
    fun `join by time, page by page`(pageSize: Int) =
        runBlocking {
            val metric1DataSource =
                listOf(
                    Metric(
                        1,
                        101,
                    ),
                    Metric(
                        2,
                        102,
                    ),
                    Metric(
                        3,
                        103,
                    ),
                    Metric(
                        4,
                        104,
                    ),
                )
            val metric2DataSource =
                listOf(
                    Metric(
                        1,
                        201,
                    ),
                    Metric(
                        2,
                        202,
                    ),
                    Metric(
                        3,
                        203,
                    ),
                    Metric(
                        4,
                        204,
                    ),
                )
            val bufferSize = 2

            val result = ArrayList<MergedMetrics>()
            var mergingState: String? = null
            var pageCount = 0

            while (true) {
                val state = (MergingStateSerializer.deserialize(mergingState, 2) as FunctionResult.Success).value

                val stream1 =
                    BufferedSuspendableStream(
                        initialState = state.states[0]?.toInt()?.let { State(it) },
                        bufferSize = bufferSize,
                        bufferLoader = bufferLoader(metric1DataSource, true),
                        stateResolver = { State(it.time) },
                    ).asMergeSource { it.time } // merge key "time"

                val stream2 =
                    BufferedSuspendableStream(
                        initialState = state.states[1]?.toInt()?.let { State(it) },
                        bufferSize = bufferSize,
                        bufferLoader = bufferLoader(metric2DataSource, true),
                        stateResolver = { State(it.time) },
                    ).asMergeSource { it.time } // merge key "time"

                val resultStream =
                    listOf(stream1, stream2).mergeJoin(null, true) {
                        val item1 = it[0] as Metric
                        val item2 = it[1] as Metric
                        MergedMetrics(
                            item1.time,
                            item1.value,
                            item2.value,
                        )
                    }

                val page = resultStream.getPage(pageSize, true)
                result.addAll(page.items)
                pageCount++
                if (page.nextPageToken == null) break
                mergingState = page.nextPageToken
            }

            assertEquals(
                listOf(
                    MergedMetrics(
                        1,
                        101,
                        201,
                    ),
                    MergedMetrics(
                        2,
                        102,
                        202,
                    ),
                    MergedMetrics(
                        3,
                        103,
                        203,
                    ),
                    MergedMetrics(
                        4,
                        104,
                        204,
                    ),
                ),
                result,
            )

            assertEquals(ceil(4.0 / pageSize).toInt(), pageCount)
        }

    @ParameterizedTest
    @ValueSource(ints = [1, 2, 3, 4, 5, 6, 7])
    fun `sort by time and asset`(bufferSize: Int) =
        runBlocking {
            // asset A
            val metric1DataSource =
                listOf(
                    Metric(
                        1,
                        101,
                    ),
                    Metric(
                        2,
                        102,
                    ),
                    Metric(
                        3,
                        103,
                    ),
                    Metric(
                        4,
                        104,
                    ),
                )
            // asset B
            val metric2DataSource =
                listOf(
                    Metric(
                        1,
                        201,
                    ),
                    Metric(
                        2,
                        202,
                    ),
                    Metric(
                        3,
                        203,
                    ),
                    Metric(
                        4,
                        204,
                    ),
                )

            val stream1 =
                BufferedSuspendableStream(
                    initialState = null,
                    bufferSize = bufferSize,
                    bufferLoader = bufferLoader(metric1DataSource, true),
                    stateResolver = { State(it.time) },
                ).asMergeSource { ComparablePair(it.time, "A") } // merge key "time, asset"

            val stream2 =
                BufferedSuspendableStream(
                    initialState = null,
                    bufferSize = bufferSize,
                    bufferLoader = bufferLoader(metric2DataSource, true),
                    stateResolver = { State(it.time) },
                ).asMergeSource { ComparablePair(it.time, "B") } // merge key "time, asset"

            val resultStream = listOf(stream1, stream2).mergeSort(true)

            val page = resultStream.getPage(8, true)
            assertEquals(
                listOf(
                    Metric(
                        1,
                        101,
                    ),
                    Metric(
                        1,
                        201,
                    ),
                    Metric(
                        2,
                        102,
                    ),
                    Metric(
                        2,
                        202,
                    ),
                    Metric(
                        3,
                        103,
                    ),
                    Metric(
                        3,
                        203,
                    ),
                    Metric(
                        4,
                        104,
                    ),
                    Metric(
                        4,
                        204,
                    ),
                ),
                page.items,
            )
            assertNull(page.nextPageToken)
        }

    @ParameterizedTest
    @ValueSource(ints = [1, 2, 3, 4, 5, 6, 7])
    fun `sort by time and asset, reverse`(bufferSize: Int) =
        runBlocking {
            // asset A
            val metric1DataSource =
                listOf(
                    Metric(
                        4,
                        104,
                    ),
                    Metric(
                        3,
                        103,
                    ),
                    Metric(
                        2,
                        102,
                    ),
                    Metric(
                        1,
                        101,
                    ),
                )
            // asset B
            val metric2DataSource =
                listOf(
                    Metric(
                        4,
                        204,
                    ),
                    Metric(
                        3,
                        203,
                    ),
                    Metric(
                        2,
                        202,
                    ),
                    Metric(
                        1,
                        201,
                    ),
                )

            val stream1 =
                BufferedSuspendableStream(
                    initialState = null,
                    bufferSize = bufferSize,
                    bufferLoader = bufferLoader(metric1DataSource, false),
                    stateResolver = { State(it.time) },
                ).asMergeSource { ComparablePair(it.time, "A") } // merge key "time, asset"

            val stream2 =
                BufferedSuspendableStream(
                    initialState = null,
                    bufferSize = bufferSize,
                    bufferLoader = bufferLoader(metric2DataSource, false),
                    stateResolver = { State(it.time) },
                ).asMergeSource { ComparablePair(it.time, "B") } // merge key "time, asset"

            val resultStream = listOf(stream1, stream2).mergeSort(false)

            val page = resultStream.getPage(8, false)
            assertEquals(
                listOf(
                    Metric(
                        1,
                        101,
                    ),
                    Metric(
                        1,
                        201,
                    ),
                    Metric(
                        2,
                        102,
                    ),
                    Metric(
                        2,
                        202,
                    ),
                    Metric(
                        3,
                        103,
                    ),
                    Metric(
                        3,
                        203,
                    ),
                    Metric(
                        4,
                        104,
                    ),
                    Metric(
                        4,
                        204,
                    ),
                ),
                page.items,
            )
            assertNull(page.nextPageToken)
        }

    @ParameterizedTest
    @ValueSource(ints = [1, 2, 3, 4, 5, 6, 7, 8, 9])
    fun `sort by time and asset, page by page`(pageSize: Int) =
        runBlocking {
            // asset A
            val metric1DataSource =
                listOf(
                    Metric(
                        1,
                        101,
                    ),
                    Metric(
                        2,
                        102,
                    ),
                    Metric(
                        3,
                        103,
                    ),
                    Metric(
                        4,
                        104,
                    ),
                )
            // asset B
            val metric2DataSource =
                listOf(
                    Metric(
                        1,
                        201,
                    ),
                    Metric(
                        2,
                        202,
                    ),
                    Metric(
                        3,
                        203,
                    ),
                    Metric(
                        4,
                        204,
                    ),
                )

            val bufferSize = 2
            val result = ArrayList<Metric>()
            var mergingState: String? = null

            while (true) {
                val state = (MergingSortStateSerializer.deserialize(mergingState, 2) as FunctionResult.Success).value

                val stream1 =
                    BufferedSuspendableStream(
                        initialState =
                            state.states[0]
                                ?.first
                                ?.toInt()
                                ?.let { State(it) },
                        bufferSize = bufferSize,
                        bufferLoader = bufferLoader(metric1DataSource, true),
                        stateResolver = { State(it.time) },
                    ).asMergeSource {
                        ComparablePair(
                            it.time,
                            "A",
                        )
                    } // merge key "time, asset"

                val stream2 =
                    BufferedSuspendableStream(
                        initialState =
                            state.states[1]
                                ?.first
                                ?.toInt()
                                ?.let { State(it) },
                        bufferSize = bufferSize,
                        bufferLoader = bufferLoader(metric2DataSource, true),
                        stateResolver = { State(it.time) },
                    ).asMergeSource {
                        ComparablePair(
                            it.time,
                            "B",
                        )
                    } // merge key "time, asset"

                val resultStream = listOf(stream1, stream2).mergeSort(true)

                val page = resultStream.getPage(pageSize, true)
                result.addAll(page.items)
                if (page.nextPageToken == null) break
                mergingState = page.nextPageToken
            }

            assertEquals(
                listOf(
                    Metric(
                        1,
                        101,
                    ),
                    Metric(
                        1,
                        201,
                    ),
                    Metric(
                        2,
                        102,
                    ),
                    Metric(
                        2,
                        202,
                    ),
                    Metric(
                        3,
                        103,
                    ),
                    Metric(
                        3,
                        203,
                    ),
                    Metric(
                        4,
                        104,
                    ),
                    Metric(
                        4,
                        204,
                    ),
                ),
                result,
            )
        }

    @ParameterizedTest
    @ValueSource(ints = [1, 2, 3, 4, 5, 6, 7, 8, 9])
    fun `concat, page by page`(pageSize: Int) =
        runBlocking {
            // asset A
            val metric1DataSource =
                listOf(
                    Metric(
                        1,
                        101,
                    ),
                    Metric(
                        2,
                        102,
                    ),
                    Metric(
                        3,
                        103,
                    ),
                    Metric(
                        4,
                        104,
                    ),
                )
            // asset B
            val metric2DataSource =
                listOf(
                    Metric(
                        1,
                        201,
                    ),
                    Metric(
                        2,
                        202,
                    ),
                    Metric(
                        3,
                        203,
                    ),
                    Metric(
                        4,
                        204,
                    ),
                )

            val bufferSize = 2
            val result = ArrayList<Metric>()
            var serializedState: String? = null

            val dataSources = listOf(metric1DataSource, metric2DataSource)

            while (true) {
                val (activeStreamIndex, _, token) = (ConcatStateSerializer.deserialize(serializedState) as FunctionResult.Success).value

                val streams =
                    dataSources
                        .subList(activeStreamIndex, dataSources.size)
                        .mapIndexed { index, ds ->
                            val streamState: Int? =
                                if (index == 0) {
                                    token?.toIntOrNull()
                                } else {
                                    null
                                }
                            BufferedSuspendableStream(
                                initialState = streamState?.let { State(it) },
                                bufferSize = bufferSize,
                                bufferLoader = bufferLoader(ds, true),
                                stateResolver = { State(it.time) },
                            )
                        }.asFlow()

                val resultStream =
                    streams.concat(
                        coroutineContext = coroutineContext,
                        firstStreamIndex = activeStreamIndex,
                    )

                val page = resultStream.getPage(pageSize, true)
                result.addAll(page.items)
                if (page.nextPageToken == null) break
                serializedState = page.nextPageToken
            }

            assertEquals(
                listOf(
                    Metric(
                        1,
                        101,
                    ),
                    Metric(
                        2,
                        102,
                    ),
                    Metric(
                        3,
                        103,
                    ),
                    Metric(
                        4,
                        104,
                    ),
                    Metric(
                        1,
                        201,
                    ),
                    Metric(
                        2,
                        202,
                    ),
                    Metric(
                        3,
                        203,
                    ),
                    Metric(
                        4,
                        204,
                    ),
                ),
                result,
            )
        }

    private data class Metric(
        val time: Int,
        val value: Int,
    )

    private data class MergedMetrics(
        val time: Int,
        val value1: Int?,
        val value2: Int?,
    )

    private data class State(
        val value: Int,
    ) : SuspendableStream.State {
        override fun serialize() = value.toString()
    }
}
