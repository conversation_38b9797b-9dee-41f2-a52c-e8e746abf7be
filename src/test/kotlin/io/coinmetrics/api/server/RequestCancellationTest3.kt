package io.coinmetrics.api.server

import io.coinmetrics.api.ChunkedResponseFormat
import io.coinmetrics.api.Response
import io.coinmetrics.api.endpoints.Endpoint
import io.coinmetrics.api.helper.BaseTest
import io.coinmetrics.api.modules.common.CommonConfig
import io.coinmetrics.api.utils.awaitUntilAsserted
import io.coinmetrics.api.utils.get
import io.coinmetrics.httpserver.HttpRequest
import kotlinx.coroutines.CompletableDeferred
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withTimeout
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import java.net.InetSocketAddress
import java.net.Socket
import java.util.concurrent.atomic.AtomicInteger

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class RequestCancellationTest3 : BaseTest() {
    private val requestReceivedFuture = CompletableDeferred<Unit>()
    private val flowCompletedFuture = CompletableDeferred<Unit>()
    private val flowsStarted = AtomicInteger(0)

    override fun commonConfig(): CommonConfig {
        return super.commonConfig().copy(
            parallelHttpRequestLimitPerKeyDefault = 1,
            customEndpoints =
                listOf(
                    "/v4/chunked-stream" to
                        object : Endpoint<Nothing> {
                            override suspend fun handle(httpRequest: HttpRequest): Response<Nothing> {
                                requestReceivedFuture.complete(Unit)
                                val flow =
                                    flow {
                                        while (true) {
                                            emit("{}")
                                        }
                                    }.onCompletion {
                                        flowCompletedFuture.complete(Unit)
                                    }.onStart {
                                        flowsStarted.incrementAndGet()
                                    }
                                return Response.chunkedResponse(flow, format = ChunkedResponseFormat.JsonStream)
                            }
                        },
                ),
        )
    }

    @Test
    fun `a disconnection while waiting for a permit from ActiveRequestCountLimitService`(): Unit =
        runBlocking {
            val sock = Socket()
            val sock2 = Socket()

            sock.connect(InetSocketAddress("127.0.0.1", server.actualPort()!!))
            sock.getOutputStream().write("GET /v4/chunked-stream?api_key=1 HTTP/1.1\r\n\r\n".toByteArray())
            try {
                sock.getInputStream()
                withTimeout(5_000) {
                    requestReceivedFuture.await()
                }

                awaitUntilAsserted {
                    assertEquals(
                        0.0,
                        commonModule.monitoring.requestsQueuePending.get("endpoint" to "/v4/chunked-stream", "api_key" to "1"),
                    )
                    assertEquals(1.0, commonModule.monitoring.requestsActive.get("endpoint" to "/v4/chunked-stream", "api_key" to "1"))
                    assertEquals(1, flowsStarted.get())
                }

                sock2.connect(InetSocketAddress("127.0.0.1", server.actualPort()!!))
                sock2.getOutputStream().write("GET /v4/chunked-stream?api_key=1 HTTP/1.1\r\n\r\n".toByteArray())
                sock2.getInputStream()

                awaitUntilAsserted {
                    assertEquals(
                        1.0,
                        commonModule.monitoring.requestsQueuePending.get("endpoint" to "/v4/chunked-stream", "api_key" to "1"),
                    )
                    assertEquals(1.0, commonModule.monitoring.requestsActive.get("endpoint" to "/v4/chunked-stream", "api_key" to "1"))
                    assertEquals(1, flowsStarted.get())
                }
            } finally {
                sock2.close()
                // this delay is needed to preserve the ordering of "disconnect" events in the Server
                delay(100)
                sock.close()
            }

            awaitUntilAsserted {
                assertEquals(0.0, commonModule.monitoring.requestsQueuePending.get("endpoint" to "/v4/chunked-stream", "api_key" to "1"))
                assertEquals(0.0, commonModule.monitoring.requestsActive.get("endpoint" to "/v4/chunked-stream", "api_key" to "1"))
                assertEquals(1, flowsStarted.get())
            }
        }
}
