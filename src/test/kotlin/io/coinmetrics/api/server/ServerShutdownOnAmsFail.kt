package io.coinmetrics.api.server

import io.coinmetrics.api.Server
import io.coinmetrics.api.helper.Containers
import io.coinmetrics.api.modules.common.CommonConfig
import io.coinmetrics.api.modules.common.CommonModule
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertThrows
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.api.Timeout
import java.util.concurrent.TimeUnit

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class ServerShutdownOnAmsFail {
    private lateinit var server: Server

    @Test
    @Timeout(value = 30, unit = TimeUnit.SECONDS)
    fun `shutdown on ams not up`() {
        try {
            val exception =
                assertThrows(IllegalStateException::class.java) {
                    startServerAmsConnectionRefused()
                }
            val expectedMessage = "AMS Status is not returning 200 on server start: Failed(status=-1, message='Status AMS request failed with the following reason: java.net.ConnectException')"
            assertEquals(expectedMessage, exception.message)
        } finally {
            runBlocking {
                if (this@ServerShutdownOnAmsFail::server.isInitialized) server.close()
            }
        }
    }

    private val defaultEnvs =
        mapOf(
            "API_DB_SCHEMA" to "test",
            "API_AMS_REQUEST_TIMEOUT_MS" to "20000",
            "API_DB_THREADS" to "10",
            "API_DB_QUERY_TIMEOUT_SEC" to "40",
            "API_DB_SLOW_QUERY_MS" to "3000",
            "API_DEFI_ENABLED" to "true",
            "API_PORT" to "8080",
            "API_TRADE_KEYS_CACHE_SIZE" to "1000",
            "API_DB" to "postgresql://${Containers.postgreSQLContainer.host}:${Containers.postgreSQLContainer.port}/test?user=postgres",
            "API_STATISTICS_S3_ENDPOINT" to "http://localhost",
            "API_AMS_HOST" to (
                Containers.amsContainer.host.takeIf {
                    it != "localhost"
                } ?: "127.0.0.1"
                // this is a host that we know will fail for ams
            ),
            // this is a port that we know will fail for ams
            "API_AMS_PORT" to "1221",
        )

    private val envVariablesResolverAmsFailure = { name: String ->
        CommonConfig.defaultEnvVariablesResolver.invoke(name) ?: defaultEnvs[name]
    }

    private fun startServerAmsConnectionRefused() =
        runBlocking {
            val commonModule =
                CommonModule(
                    CommonConfig(envVariablesResolver = envVariablesResolverAmsFailure),
                )
            server = Server(commonModule)
            server.start()
        }
}
