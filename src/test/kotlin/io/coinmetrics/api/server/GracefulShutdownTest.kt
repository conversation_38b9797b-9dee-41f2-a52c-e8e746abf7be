package io.coinmetrics.api.server

import io.coinmetrics.api.Response
import io.coinmetrics.api.endpoints.Endpoint
import io.coinmetrics.api.helper.BaseTest
import io.coinmetrics.api.modules.common.CommonConfig
import io.coinmetrics.api.utils.awaitUntilAsserted
import io.coinmetrics.httpserver.HttpRequest
import io.coinmetrics.httpserver.HttpResponse
import kotlinx.coroutines.CompletableDeferred
import kotlinx.coroutines.async
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import java.time.Instant

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class GracefulShutdownTest : BaseTest() {
    private val requestReceived = CompletableDeferred<Unit?>()
    private val readyToRespond = CompletableDeferred<Unit?>()

    override fun commonConfig(): CommonConfig {
        return super.commonConfig().copy(
            customEndpoints =
                listOf(
                    "/v4/slow-test" to
                        object : Endpoint<Nothing> {
                            override suspend fun handle(httpRequest: HttpRequest): Response<Nothing> {
                                log.info("Request: {}", httpRequest)
                                requestReceived.complete(null)
                                readyToRespond.await()
                                return Response.rawHttpResponse(HttpResponse())
                            }
                        },
                ),
        )
    }

    @Test
    fun `graceful shutdown test`() {
        runBlocking {
            var response1Time: Instant? = null
            val response1Future =
                async {
                    val future = getResponseAsync("/v4/slow-test?api_key=1")
                    response1Time = Instant.now()
                    future
                }

            // give a chance to the previous request to reach the server
            requestReceived.await()

            var serverClosedTime: Instant? = null
            launch {
                serverClosedTime = Instant.now()
                server.close()
            }

            // give a chance to the server to initiate a shutdown procedure
            delay(10)

            // new requests must not be accepted from the moment
            val response2 =
                awaitUntilAsserted(maxWaitingTimeMs = 5000) {
                    val response = getResponseAsync("/v4/slow-test?api_key=1")
                    assertEquals(503, response.status) {
                        "Response body: ${response.body}."
                    }
                    response
                }
            assertEquals("Service unavailable.", response2.body)

            readyToRespond.complete(null)

            // but old request must be completed successfully
            val response1 = response1Future.await()
            assertEquals(200, response1.status) {
                "Response body: ${response1.body}."
            }

            assertNotNull(response1Time)
            assertNotNull(serverClosedTime)
            // and old request must be completed after shutdown
            assertTrue(response1Time!! > serverClosedTime!!) {
                "$response1Time should be greater than $serverClosedTime."
            }
        }
    }
}
