package io.coinmetrics.api.server

import io.coinmetrics.api.Response
import io.coinmetrics.api.endpoints.Endpoint
import io.coinmetrics.api.helper.BaseTest
import io.coinmetrics.api.helper.TestWebSocketListener
import io.coinmetrics.api.modules.common.CommonConfig
import io.coinmetrics.httpserver.HttpRequest
import io.coinmetrics.httpserver.HttpResponse
import kotlinx.coroutines.CompletableDeferred
import kotlinx.coroutines.async
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.api.fail
import java.net.http.WebSocketHandshakeException

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class GracefulShutdownWsTest : BaseTest() {
    private val readyToRespond = CompletableDeferred<Unit?>()

    override fun commonConfig(): CommonConfig {
        return super.commonConfig().copy(
            customEndpoints =
                super.customEndpoints() +
                    listOf(
                        "/v4/slow-test" to
                            object : Endpoint<Nothing> {
                                override suspend fun handle(httpRequest: HttpRequest): Response<Nothing> {
                                    log.info("Request: {}", httpRequest)
                                    readyToRespond.await()
                                    return Response.rawHttpResponse(HttpResponse())
                                }
                            },
                    ),
        )
    }

    @Test
    fun test(): Unit =
        runBlocking {
            // execute long-running request in background to prevent shutting down the server
            launch { getResponseAsync("/v4/slow-test?api_key=1") }

            val listener = TestWebSocketListener()
            connectToWebSocketAsync("/v4/timeseries-stream/slow-test", listener)

            // give a chance to the previous request to reach the server
            delay(50)

            val closeFuture = async { server.close() }

            // give a chance to a server to initiate shutdown procedure
            delay(50)

            // try to reconnect again, new requests must not be accepted from the moment
            try {
                val listener2 = TestWebSocketListener()
                connectToWebSocketAsync("/v4/timeseries-stream/slow-test", listener2)
                fail("Should not be reached.")
            } catch (e: WebSocketHandshakeException) {
                // expected
                assertEquals(503, e.response.statusCode())
            }

            // signal the server to close
            readyToRespond.complete(null)

            closeFuture.await()

            // old WebSocket connection must be disconnected with appropriate status and reason
            val closeMessage = listener.waitForClose(5_000)
            val expectedCloseMessage = TestWebSocketListener.CloseMessage(1001, "Server is going away. Please reconnect.")
            assertEquals(expectedCloseMessage, closeMessage)
        }
}
