package io.coinmetrics.api.server

import io.coinmetrics.api.ChunkedResponseFormat
import io.coinmetrics.api.Response
import io.coinmetrics.api.endpoints.Endpoint
import io.coinmetrics.api.helper.BaseTest
import io.coinmetrics.api.modules.common.CommonConfig
import io.coinmetrics.api.utils.awaitUntilAsserted
import io.coinmetrics.api.utils.get
import io.coinmetrics.httpserver.HttpRequest
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.CompletableDeferred
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withTimeout
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import java.net.InetSocketAddress
import java.net.Socket
import java.util.concurrent.atomic.AtomicBoolean

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class RequestCancellationTest : BaseTest() {
    private val requestReceivedFuture = CompletableDeferred<Unit>()
    private val closeFuture = CompletableDeferred<Unit>()
    private val flowStarted = AtomicBoolean(false)
    private val cancelledFuture = CompletableDeferred<Unit>()

    override fun commonConfig(): CommonConfig {
        return super.commonConfig().copy(
            customEndpoints =
                listOf(
                    "/v4/chunked-stream" to
                        object : Endpoint<Nothing> {
                            override suspend fun handle(httpRequest: HttpRequest): Response<Nothing> {
                                requestReceivedFuture.complete(Unit)
                                try {
                                    closeFuture.await()
                                } catch (e: CancellationException) {
                                    cancelledFuture.complete(Unit)
                                    throw e
                                }
                                // the flow should not even be started
                                val flow =
                                    flow {
                                        while (true) {
                                            emit("{}")
                                        }
                                    }.onStart {
                                        flowStarted.set(true)
                                    }
                                return Response.chunkedResponse(flow, format = ChunkedResponseFormat.JsonStream)
                            }
                        },
                ),
        )
    }

    @Test
    fun `a disconnection before a chunked stream has started`(): Unit =
        runBlocking {
            val sock = Socket()
            sock.connect(InetSocketAddress("127.0.0.1", server.actualPort()!!))
            sock.getOutputStream().write("GET /v4/chunked-stream?api_key=1 HTTP/1.1\r\n\r\n".toByteArray())
            try {
                withTimeout(5_000) {
                    requestReceivedFuture.await()
                }
                assertEquals(0.0, commonModule.monitoring.requestsQueuePending.get("endpoint" to "/v4/chunked-stream", "api_key" to "1"))
                assertEquals(1.0, commonModule.monitoring.requestsActive.get("endpoint" to "/v4/chunked-stream", "api_key" to "1"))
            } finally {
                sock.close()
            }
            withTimeout(5_000) {
                cancelledFuture.await()
            }
            closeFuture.complete(Unit)
            awaitUntilAsserted {
                assertFalse(flowStarted.get())
                assertEquals(0.0, commonModule.monitoring.requestsQueuePending.get("endpoint" to "/v4/chunked-stream", "api_key" to "1"))
                assertEquals(0.0, commonModule.monitoring.requestsActive.get("endpoint" to "/v4/chunked-stream", "api_key" to "1"))
            }
        }
}
