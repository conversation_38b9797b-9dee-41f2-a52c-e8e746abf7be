package io.coinmetrics.api.statistics

import io.coinmetrics.api.utils.awaitUntilAsserted
import kotlinx.coroutines.CompletableDeferred
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.cancelAndJoin
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.runInterruptible
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertSame
import org.junit.jupiter.api.RepeatedTest
import org.slf4j.LoggerFactory
import java.io.Closeable
import java.time.Duration
import java.util.concurrent.Executors
import java.util.concurrent.atomic.AtomicInteger
import java.util.concurrent.atomic.AtomicReference
import java.util.concurrent.locks.ReentrantLock
import kotlin.concurrent.withLock

class StatisticsRepositoryTest {
    val closeables = mutableListOf<suspend () -> Unit>()

    @AfterEach
    fun tearDown() =
        runBlocking {
            for (closeable in closeables) {
                runCatching {
                    closeable()
                }.onFailure {
                    log.warn("Error closing closeable: ${it.message}", it)
                }
            }
        }

    @RepeatedTest(10)
    fun aggregations() =
        runBlocking {
            val concurrency = 16
            val dispatcher = Executors.newFixedThreadPool(concurrency + 5).asCoroutineDispatcher().closeAfterTest()
            var itemNameToValueSetter = mapOf<String, (String) -> Unit>()
            val repo =
                object : StatisticsRepository(dispatcher) {
                    override suspend fun start(items: Collection<ItemImpl<Any>>) {
                        itemNameToValueSetter =
                            items.associateBy({ it.descriptor.name }) { item ->
                                {
                                    val newValue = "${item.descriptor.name}:$it"
                                    item.setValueAndHash(newValue, "")
                                }
                            }
                        for (valueSetter in itemNameToValueSetter.values) {
                            valueSetter("")
                        }
                    }

                    override suspend fun doClose() {
                    }
                }.closeAfterTest { close() }

            val agg1Counter = AtomicInteger()
            val agg1Lock = ReentrantLock()
            val agg1Exception = AtomicReference<Throwable>()
            val agg1 =
                repo.createAggregation {
                    val item1 = getUpstreamItem(StatisticsDescriptor.create<String>("item1"))
                    val item2 = getUpstreamItem(StatisticsDescriptor.create<String>("item2"))
                    return@createAggregation {
                        agg1Lock.withLock {
                            agg1Counter.getAndIncrement()
                            agg1Exception.get()?.also { throw it }
                            "${item1.value}|${item2.value}"
                        }
                    }
                }
            val agg2CallCounter = AtomicInteger()
            val agg2Lock = ReentrantLock()
            val agg2Exception = AtomicReference<Throwable>()
            val agg2 =
                repo.createAggregation {
                    val item2 = getUpstreamItem(StatisticsDescriptor.create<String>("item2"))
                    val item3 = getUpstreamItem(StatisticsDescriptor.create<String>("item3"))
                    return@createAggregation {
                        agg2Lock.withLock {
                            agg2CallCounter.getAndIncrement()
                            agg2Exception.get()?.also { throw it }
                            "${item2.value}|${item3.value}"
                        }
                    }
                }

            fun getAgg(agg: () -> String): String {
                val value = agg()
                repeat(5) {
                    assertEquals(value, agg())
                }
                return value
            }

            suspend fun blockAgg(
                aggLock: ReentrantLock,
                duration: Duration = Duration.ofHours(1),
            ): suspend () -> Unit {
                val locked = CompletableDeferred<Unit>()
                val job =
                    launch(dispatcher) {
                        aggLock.lock()
                        try {
                            locked.complete(Unit)
                            runInterruptible {
                                Thread.sleep(duration.toMillis())
                            }
                        } finally {
                            aggLock.unlock()
                        }
                    }
                locked.await()
                return {
                    job.cancelAndJoin()
                }
            }

            suspend fun inParallel(block: suspend () -> Unit) =
                coroutineScope {
                    repeat(concurrency) {
                        launch(dispatcher) {
                            repeat(1000) {
                                block()
                            }
                        }
                    }
                }

            // Simulate aggregator functions blocking to make sure start() is synchronized properly.
            blockAgg(agg1Lock, Duration.ofMillis(100))
            blockAgg(agg2Lock, Duration.ofMillis(200))

            repo.start()
            inParallel {
                assertEquals("item1:|item2:", getAgg(agg1))
                assertEquals("item2:|item3:", getAgg(agg2))
                assertEquals(1, agg1Counter.get())
                assertEquals(1, agg2CallCounter.get())
            }

            val unblockAgg1 = blockAgg(agg1Lock)
            itemNameToValueSetter.getValue("item1")("1")
            inParallel {
                // Since the aggregator function is blocked, value shouldn't update, but also the accesses shouldn't block.
                assertEquals("item1:|item2:", getAgg(agg1))
                assertEquals("item2:|item3:", getAgg(agg2))
                assertEquals(1, agg1Counter.get())
                assertEquals(1, agg2CallCounter.get())
            }

            unblockAgg1()
            inParallel {
                awaitUntilAsserted {
                    assertEquals("item1:1|item2:", getAgg(agg1))
                }
                assertEquals("item2:|item3:", getAgg(agg2))
                assertEquals(2, agg1Counter.get())
                assertEquals(1, agg2CallCounter.get())
            }

            itemNameToValueSetter.getValue("item2")("2")
            inParallel {
                awaitUntilAsserted {
                    assertEquals("item1:1|item2:2", getAgg(agg1))
                }
                awaitUntilAsserted {
                    assertEquals("item2:2|item3:", getAgg(agg2))
                }
                assertEquals(3, agg1Counter.get())
                assertEquals(2, agg2CallCounter.get())
            }

            // Check exception in aggregator function.
            agg1Exception.set(Exception("agg1Exception"))
            agg2Exception.set(Exception("agg2Exception"))
            itemNameToValueSetter.getValue("item2")("3")
            awaitUntilAsserted {
                assertSame(agg1Exception.get(), runCatching { getAgg(agg1) }.exceptionOrNull())
            }
            awaitUntilAsserted {
                assertSame(agg2Exception.get(), runCatching { getAgg(agg2) }.exceptionOrNull())
            }
            assertEquals(4, agg1Counter.get())
            assertEquals(3, agg2CallCounter.get())

            // Check recovery from exception.
            agg1Exception.set(null)
            agg2Exception.set(null)
            itemNameToValueSetter.getValue("item2")("4")
            awaitUntilAsserted {
                assertEquals("item1:1|item2:4", runCatching { getAgg(agg1) }.getOrNull())
            }
            awaitUntilAsserted {
                assertEquals("item2:4|item3:", runCatching { getAgg(agg2) }.getOrNull())
            }
        }

    private fun <T : Closeable> T.closeAfterTest() = closeAfterTest { close() }

    private fun <T> T.closeAfterTest(closer: suspend T.() -> Unit): T {
        closeables.add { closer() }
        return this
    }

    companion object {
        private val log = LoggerFactory.getLogger(StatisticsRepositoryTest::class.java)
    }
}
