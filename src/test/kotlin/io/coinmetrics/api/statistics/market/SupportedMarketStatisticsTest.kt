package io.coinmetrics.api.statistics.market

import io.coinmetrics.api.statistics.market.SupportedMarketStatistics.Companion.getSupportedMarkets
import io.coinmetrics.api.utils.CommonUtils
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class SupportedMarketStatisticsTest {
    @Test
    fun getSupportedMarkets() {
        fun create(markets: List<String>) =
            object : SupportedMarketStatistics {
                override fun isMarketSupported(market: String) = markets.contains(market)

                override fun isExchangeSupported(exchange: String): Bo<PERSON>an {
                    error("Should not be called")
                }

                override fun isSymbolSupported(symbol: String): <PERSON><PERSON><PERSON> {
                    error("Should not be called")
                }

                override fun isBaseSupported(base: String): <PERSON><PERSON><PERSON> {
                    error("Should not be called")
                }

                override fun isQuoteSupported(quote: String): Boolean {
                    error("Should not be called")
                }

                override val supportedMarketsHash: Int
                    get() = error("Should not be called")

                override fun getSupportedMarkets(reverseOrder: Bo<PERSON>an) =
                    markets
                        .let {
                            if (reverseOrder) it.reversed() else it
                        }.map {
                            it to (CommonUtils.parseMarket(it) ?: error("Bad market: $it"))
                        }.associateBy({ it.first }) { it.second }
                        .asSequence()
                        .also { it }
            }

        fun test(
            expected: List<String>,
            markets: List<List<String>>,
        ) {
            val stats = markets.map { create(it) }
            val expectedVal = expected.associateWith { CommonUtils.parseMarket(it)!! }.entries.toList()
            assertEquals(expectedVal, stats.getSupportedMarkets(reverseOrder = false).toList())
            assertEquals(expectedVal.reversed(), stats.getSupportedMarkets(reverseOrder = true).toList())
        }

        test(
            listOf("binance-MARKET-1-future", "binance-MARKET-2-spot", "binance-MARKET-3-future", "binance-MARKET-3-spot", "bitmex-MARKET-1-future"),
            listOf(listOf("binance-MARKET-1-future", "binance-MARKET-3-spot"), listOf("binance-MARKET-1-future", "binance-MARKET-3-future"), listOf("binance-MARKET-2-spot", "bitmex-MARKET-1-future")),
        )
    }
}
