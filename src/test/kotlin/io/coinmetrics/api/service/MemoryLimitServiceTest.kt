package io.coinmetrics.api.service

import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.collectIndexed
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.util.concurrent.Executors

class MemoryLimitServiceTest {
    @Test
    fun reserveMemAndExecute(): Unit =
        runBlocking {
            val memoryLimit = 10L
            val service = MemoryLimitService(memoryLimit)

            val iterations = 100

            Executors.newFixedThreadPool(1).asCoroutineDispatcher().use { dispatcher ->
                val result =
                    (1..iterations)
                        .map { num ->
                            async(dispatcher) {
                                val size = 1 + num % 10
                                service.execute(key = "test$num", apiKey = "test", taskMemSizeBytes = size.toLong()) {
                                    delay(size.toLong())
                                    val remainingBytes = service.memoryUsage()
                                    assert(remainingBytes >= 0) { "remainingBytes is negative: $remainingBytes" }
                                    num
                                }
                            }
                        }.awaitAll()

                assertEquals(0, service.memoryUsage())
                assertEquals((1..iterations).toList(), result)
            }
        }

    @Test
    fun `cancellation in the middle of streaming`(): Unit =
        runBlocking {
            val memoryLimit = 10L
            val service = MemoryLimitService(memoryLimit)

            val flow =
                flow {
                    (0..2).forEach {
                        emit(it)
                    }
                }

            assertThrows<CancellationException> {
                service.execute(key = "test", apiKey = "test", taskMemSizeBytes = 5) {
                    flow.collectIndexed { index, _ ->
                        if (index == 1) throw CancellationException()
                    }
                }
            }

            assertEquals(0, service.memoryUsage())
        }
}
