package io.coinmetrics.api

import io.coinmetrics.api.helper.BaseTest
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.api.fail
import java.net.URI
import java.net.http.HttpClient
import java.net.http.WebSocket
import java.time.Duration
import java.util.concurrent.CompletableFuture
import java.util.concurrent.CompletionStage

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class SimpleWsTest : BaseTest() {
    @Test
    fun `test ws endpoint`() {
        val client = HttpClient.newBuilder().connectTimeout(Duration.ofSeconds(5)).build()

        val messages = ArrayList<String>()
        val closeStatusFuture = CompletableFuture<Int>()

        val webSocketListener =
            object : WebSocket.Listener {
                override fun onText(
                    webSocket: WebSocket,
                    data: CharSequence,
                    last: Bo<PERSON>an,
                ): CompletionStage<*>? {
                    messages.add(data.toString())
                    webSocket.request(1)
                    return null
                }

                override fun onClose(
                    webSocket: WebSocket,
                    statusCode: Int,
                    reason: String?,
                ): CompletionStage<*>? {
                    closeStatusFuture.complete(statusCode)
                    return null
                }

                override fun onError(
                    webSocket: WebSocket?,
                    error: Throwable?,
                ) {
                    fail(error)
                }
            }

        val webSocket =
            client
                .newWebSocketBuilder()
                .buildAsync(
                    URI.create("ws://127.0.0.1:${server.actualPort()}/v4/timeseries-stream/test"),
                    webSocketListener,
                ).join()

        webSocket.sendText("Should be mirrored.", true)

        val closeStatus = closeStatusFuture.join()

        assertEquals(listOf("Should be mirrored.", "1", "2", "3"), messages)
        assertEquals(1000, closeStatus)
    }
}
