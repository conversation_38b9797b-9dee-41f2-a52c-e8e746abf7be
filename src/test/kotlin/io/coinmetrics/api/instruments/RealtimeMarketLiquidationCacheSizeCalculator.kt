package io.coinmetrics.api.instruments

import io.coinmetrics.api.utils.Quadruple
import java.math.BigDecimal
import java.math.RoundingMode

/**
 * This helper class calculates the cache size for real-time market liquidations for each exchange, prorated based on data volume.
 *
 * The total cache size is limited to 20M items, shared among all exchanges according to their trade volume over the last 7 days.
 * The code generates an SQL query that needs to be executed. Paste the pipe-delimited result into the `result` variable.
 * Subsequent runs will produce the environment variables with new values.
 */
@Suppress("ktlint:standard:string-template-indent")
fun main() {
    val supportedExchanges = listOf(2, 4, 6, 9, 10, 34, 37, 42, 59)

    fun generateQueryForExchange(exchangeId: Int): String =
        """
        WITH futures_trades_count AS (SELECT coalesce(SUM(candle_trades_count), 0) AS count
                                      FROM production.candles_market_futures_${exchangeId}_1d
                                      WHERE candle_start_time >
                                            (SELECT DATE_TRUNC('day', candle_start_time AT TIME ZONE 'utc' - '7 days'::INTERVAL)
                                             FROM production.candles_market_futures_${exchangeId}_1d
                                             ORDER BY candle_start_time DESC
                                             LIMIT 1))
        SELECT $exchangeId AS exchange_id, (SELECT count AS futures FROM futures_trades_count) AS total, ceil((SELECT count AS futures FROM futures_trades_count) / 7.0) AS avg_per_day
        """.trimIndent()

    // Generate a query to gather statistics
    println(supportedExchanges.joinToString(separator = "\nUNION ALL\n") { "(${generateQueryForExchange(it)})" })

    // Run query and paste results here as pipe-delimited values
    val result =
/*
 exchange_id |   total   | avg_per_day
-------------+-----------+-------------
 */
        """
           2 |   2323120 |      331875
           4 | 971551906 |   138793130
           6 |   4813723 |      687675
           9 | 418849322 |    59835618
          10 |   9652326 |     1378904
          34 |   2349963 |      335709
          37 |   4114918 |      587846
          42 | 437228211 |    62461173
          59 |    905551 |      129365
        """.trimIndent()

    val parsedResult =
        result.split("\n").map {
            val items = it.split("|")
            val exchangeId = items.first().trim().toInt()
            val totalPerDay = items[items.lastIndex - 1].trim().toLong()
            exchangeId to totalPerDay
        }

    val totalMaxCacheSize = 20_000_000
    val totalPerDaySum = parsedResult.sumOf { it.second }
    val totalPerDayMin = parsedResult.filter { it.second != 0L }.minOf { it.second }

    val coefficient = BigDecimal(totalMaxCacheSize).divide(BigDecimal(totalPerDaySum), 10, RoundingMode.HALF_UP)
    println("Coefficient = $coefficient.")

    val calculatedResults =
        parsedResult
            .map {
                val weight = it.second / totalPerDayMin
                val cacheSize = BigDecimal(it.second).multiply(coefficient).setScale(2, RoundingMode.HALF_UP)
                Quadruple(it.first, weight, it.second, cacheSize)
            }.sortedByDescending { it.t2 }

    calculatedResults.forEach { println("${it.t1}\t\t${it.t2}\t\t${it.t3 / 24}\t\t${it.t4.toInt()}") }

    println("Total cache size = ${calculatedResults.sumOf { it.t4 }}.")
    val exchangesEnv =
        calculatedResults
            .sortedBy { it.t1 }
            .joinToString(separator = ", ") { "${it.t1}:${it.t4.toInt()}" }
    println("KAFKA_LIQUIDATIONS_0_EXCHANGES: \"$exchangesEnv\"")
}
