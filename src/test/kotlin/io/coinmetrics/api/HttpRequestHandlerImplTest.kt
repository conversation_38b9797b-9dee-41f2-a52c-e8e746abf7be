package io.coinmetrics.api

import io.coinmetrics.api.helper.BaseTest
import io.coinmetrics.httpserver.HttpRequest
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import kotlin.coroutines.EmptyCoroutineContext

class HttpRequestHandlerImplTest : BaseTest() {
    @Test
    fun `test http request is community`() {
        listOf(
            false to
                listOf(
                    null,
                    "",
                    "api.coinmetrics.io",
                    "api.coinmetrics2.io",
                    "api-stg.coinmetrics.io",
                    "api-cp1.coinmetrics.io",
                ),
            true to
                listOf(
                    "community-api.coinmetrics.io",
                    "community-api.coinmetrics2.io",
                    "community-api-stg.coinmetrics.io",
                    "community-api-cp1.coinmetrics.io",
                ),
        ).flatMap { (isCommunityForHosts, hosts) ->
            hosts
                .map { it to isCommunityForHosts }
                .flatMap { (host, isCommunity) ->
                    listOf(
                        mapOf("api_key" to commonModule.config.communityApiKey),
                        mapOf("api_key" to "x2"),
                        emptyMap(),
                    ).map { queryParameters -> Triple(isCommunity, host, queryParameters) }
                }
        }.forEach { (isCommunity, host, queryParameters) ->
            assertEquals(
                isCommunity,
                HttpRequest(
                    method = "GET",
                    path = "/",
                    headers = host?.let { mapOf("host" to host) } ?: emptyMap(),
                    queryParameters = queryParameters,
                    timeSec = 0,
                    coroutineContext = EmptyCoroutineContext,
                ).isCommunity(),
                "Host `$host` and query parameters $queryParameters should return: $isCommunity",
            )
        }
    }
}
