package io.coinmetrics.api

import io.coinmetrics.api.helper.BaseTest
import io.coinmetrics.api.helper.POSTGRES_IMAGE
import io.coinmetrics.api.helper.TEST_API_KEY
import io.coinmetrics.api.modules.common.CommonConfig
import io.coinmetrics.api.modules.main.MainApiConfig
import io.coinmetrics.api.utils.awaitUntilAsserted
import io.coinmetrics.api.utils.modifyDatabases
import io.coinmetrics.databases.DatabaseImpl
import io.coinmetrics.databases.DbConfig
import io.coinmetrics.databases.NopDbMonitoring
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.AfterAll
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.testcontainers.containers.PostgreSQLContainer
import org.testcontainers.junit.jupiter.Container
import org.testcontainers.junit.jupiter.Testcontainers
import java.time.Duration
import java.time.Instant
import java.time.temporal.ChronoUnit

@Testcontainers
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class DynamicTradesTest : BaseTest() {
    companion object {
        @Container
        val postgres =
            PostgreSQLContainer<Nothing>(POSTGRES_IMAGE).apply {
                withInitScript("postgres/init_db_streaming.sql")
            }
    }

    override fun commonConfig(): CommonConfig = super.commonConfig().copy(statisticsPollInterval = Duration.ofMillis(200))

    override fun mainApiConfig(): MainApiConfig =
        super.mainApiConfig().modifyDatabases {
            copy(
                tradesDeriv =
                    DbConfig(
                        appName = "API",
                        dbConfigName = "TRADES_DERIV",
                        env = "dev",
                        jdbcUrl = "jdbc:postgresql://${postgres.host}:${postgres.firstMappedPort}/${postgres.databaseName}?user=${postgres.username}&password=${postgres.password}",
                        envVariablesResolver = envVariablesResolver,
                    ),
            )
        }

    override fun statisticsConfig() =
        super.statisticsConfig().copy(
            marketTradesStatisticsRefreshIntervalMs = 100,
        )

    @AfterAll
    internal fun tearDown() {
        super.stopServer()
        postgres.close()
    }

    @Test
    fun `test cme trades for delay`() =
        runBlocking {
            val db = DatabaseImpl(statisticsConfig.databases.tradesDeriv, NopDbMonitoring)

            val edgeTime = Instant.parse("2025-04-01T10:00:00Z")

            val oldestRecord = edgeTime.minusSeconds(181).truncatedTo(ChronoUnit.SECONDS)
            val midRecord = edgeTime.minusSeconds(20).truncatedTo(ChronoUnit.SECONDS)
            val newestRecord = edgeTime.plusSeconds(1800).truncatedTo(ChronoUnit.SECONDS)

            db.update(
                """
                INSERT INTO ${statisticsConfig.databases.tradesDeriv.schema}.trades_futures_40 (trade_id,trade_symbol,trade_amount,trade_price,trade_buy,trade_time,trade_database_time)
                VALUES 
                    (1,'BTCQ1',0.1,1.00001,false,to_timestamp(${oldestRecord.epochSecond}),to_timestamp(${oldestRecord.epochSecond})),
                    (2,'BTCQ1',0.2,2.00001,true,to_timestamp(${midRecord.epochSecond}),to_timestamp(${midRecord.epochSecond})),
                    (3,'BTCQ1',0.3,3.00001,false,to_timestamp(${newestRecord.epochSecond}),to_timestamp(${newestRecord.epochSecond}))
                """.trimIndent(),
            ) {
                it.execute()
            }
            clock.instant = Instant.now()

            val expectedResponse = """{"data":[{"market":"cme-BTCQ1-future","time":"2025-04-01T09:56:59.000000000Z","coin_metrics_id":"1","amount":"0.1","price":"1.00001","database_time":"2025-04-01T09:56:59.000000000Z","side":"sell"},{"market":"cme-BTCQ1-future","time":"2025-04-01T09:59:40.000000000Z","coin_metrics_id":"2","amount":"0.2","price":"2.00001","database_time":"2025-04-01T09:59:40.000000000Z","side":"buy"},{"market":"cme-BTCQ1-future","time":"2025-04-01T10:30:00.000000000Z","coin_metrics_id":"3","amount":"0.3","price":"3.00001","database_time":"2025-04-01T10:30:00.000000000Z","side":"sell"}]}"""
            awaitUntilAsserted {
                assertResponse(200, expectedResponse, "/v4/timeseries/market-trades?markets=cme-BTCQ1-future&api_key=$TEST_API_KEY")
            }
        }

    @Test
    fun `test markets paging for dynamic market addition`() =
        runBlocking {
            val config = mainApiModule.config
            val db = DatabaseImpl(config.databases.tradesDeriv, NopDbMonitoring)

            db.update(
                """
                INSERT INTO ${config.databases.tradesDeriv.schema}.trades_futures_4 (trade_id,trade_symbol,trade_amount,trade_price,trade_buy,trade_time,trade_database_time)
                VALUES 
                    (1,'A',0.1,1.00001,false,to_timestamp(1625518134),to_timestamp(1625518131)),
                    (2,'B',0.2,2.00001,true,to_timestamp(1625518135),to_timestamp(1625518131)),
                    (3,'D',0.3,3.00001,false,to_timestamp(1625518136),to_timestamp(1625518131))
                """.trimIndent(),
            ) {
                it.execute()
            }

            val expectedResponse1 =
                """{"data":[{"market":"binance-B-future","time":"2021-07-05T20:48:55.000000000Z","coin_metrics_id":"2","amount":"0.2","price":"2.00001","database_time":"2021-07-05T20:48:51.000000000Z","side":"buy"},{"market":"binance-D-future","time":"2021-07-05T20:48:56.000000000Z","coin_metrics_id":"3","amount":"0.3","price":"3.00001","database_time":"2021-07-05T20:48:51.000000000Z","side":"sell"}],"next_page_token":"YmluYW5jZS1CLWZ1dHVyZQ.MjAyMS0wNy0wNVQyMDo0ODo1NVp8Mg","next_page_url":"http://127.0.0.1:8080/v4/timeseries/market-trades?markets=binance-*-future&page_size=2&api_key=x1&next_page_token=YmluYW5jZS1CLWZ1dHVyZQ.MjAyMS0wNy0wNVQyMDo0ODo1NVp8Mg"}"""
            awaitUntilAsserted {
                assertResponse(200, expectedResponse1, "/v4/timeseries/market-trades?markets=binance-*-future&page_size=2&api_key=$TEST_API_KEY")
            }

            db.update(
                """
                INSERT INTO ${config.databases.tradesDeriv.schema}.trades_futures_4 (trade_id,trade_symbol,trade_amount,trade_price,trade_buy,trade_time,trade_database_time)
                VALUES 
                    (1,'C',0.1,1.00001,false,to_timestamp(1625518137),to_timestamp(1625518131))
                """.trimIndent(),
            ) {
                it.execute()
            }

            val expectedResponse2 =
                """{"data":[{"market":"binance-A-future","time":"2021-07-05T20:48:54.000000000Z","coin_metrics_id":"1","amount":"0.1","price":"1.00001","database_time":"2021-07-05T20:48:51.000000000Z","side":"sell"}]}"""
            awaitUntilAsserted {
                assertResponse(
                    200,
                    expectedResponse2,
                    "/v4/timeseries/market-trades?markets=binance-*-future&page_size=2&api_key=x1&next_page_token=YmluYW5jZS1CLWZ1dHVyZQ.MjAyMS0wNy0wNVQyMDo0ODo1NVp8Mg",
                )
            }
        }
}
