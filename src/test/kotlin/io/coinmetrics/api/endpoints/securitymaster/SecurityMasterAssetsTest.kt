package io.coinmetrics.api.endpoints.securitymaster

import io.coinmetrics.api.helper.TEST_API_KEY
import io.coinmetrics.api.helper.TEST_API_KEY_2
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.testcontainers.junit.jupiter.Testcontainers
import java.nio.file.Paths

@Testcontainers
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class SecurityMasterAssetsTest : SecurityMasterBaseTest() {
    @BeforeAll
    fun refreshTestData() =
        runBlocking {
            super.refreshSecurityMasterData()

            val assetProfilesTestDataPath = SecurityMasterAssetsTest::class.java.getResource("/asset-profiles")!!.toURI()
            mainApiModule.assetProfilesLocalStorage.refresh(Paths.get(assetProfilesTestDataPath))
        }

    @Test
    fun `should return 403 when key has no access`() {
        val expectedResponse =
            """{"error":{"type":"forbidden","message":"Requested resource is not available with supplied credentials."}}"""
        assertResponse(403, expectedResponse, "/v4/security-master/assets?&api_key=$TEST_API_KEY_2")
    }

    @Test
    fun `should return 400 when 'assets' and 'codes' specified in one request`() {
        val expectedResponse =
            """{"error":{"type":"bad_parameters","message":"Filters 'assets' and 'codes' are mutually exclusive and can't be specified in the same request."}}"""
        assertResponse(400, expectedResponse, "/v4/security-master/assets?assets=btc,eth&codes=C758EA35B0,CBBC61A492&api_key=$TEST_API_KEY")
    }

    @Test
    fun `should return first page of data when no filters specified`() {
        getResponse("/v4/security-master/assets?&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should return last page of data when no filters specified and paging_from=end`() {
        getResponse("/v4/security-master/assets?&api_key=$TEST_API_KEY&paging_from=end").assertResponse()
    }

    @Test
    fun `should return assets when 'assets' parameter specified`() {
        val expectedResponse =
            """{"data":[{"asset":"audio","code":"CB568F357F","description":"Audius is a decentralized content and streaming platform streaming protocol. The AUDIO token incentivizes activity on the Audius platform, which allows artists and fans to transact directly, host and share content, and make governance decisions for the Audius protocol.","overview":"Audius is a decentralized content and streaming platform streaming protocol. The AUDIO token incentivizes activity on the Audius platform, which allows artists and fans to transact directly, host and share content, and make governance decisions for the Audius protocol. Audius aims to solve the inefficiencies present in the current music industry by creating an economy that allows for more value to be captured by artists, creates more transparency in distribution and intellectual property rights, and directs connections between artists and content consumers. AUDIO can be staked to ensure the security of the protocol, unlock features, and exert influence in governance decisions. The Audius platform also allows for content to be hosted using its decentralized storage on AudSP, the Audius-native extension 5 to IPFS. Run using “Content Nodes”, gives content creators the power of distributing and permissioning content at their discretion. ","website":"https://audius.org","whitepaper":"https://whitepaper.audius.co/AudiusWhitepaper.pdf","decimals":"18","creation_date":"2020-10-22","type":"erc20","parent_asset":"eth","erc20_token_contract":"18aaa7115705e8be94bffebde57af9bfc265b998"},{"asset":"badger","code":"C9E3139EE9","description":"Badger is a DAO focused on building infrastructure to connect Bitcoin to DeFi applications across multiple blockchains.","overview":"Badger is a DAO focused on building infrastructure to connect Bitcoin to DeFi applications across multiple blockchains. BadgerDAO is a community that builds products that integrate Bitcoin into DeFi. BadgerDAO incentivizes builders in the ecosystem using its native token, BADGER. The BADGER token also gives the user governance rights within the Badger ecosystem. BADGER can be staked across various protocols to receive bBADGER, a claim token on interest accrued from staking BADGER.","website":"https://badger.com/","whitepaper":"https://docs.badger.com/badger-finance/","decimals":"18","creation_date":"2020-11-28","type":"erc20","parent_asset":"eth","erc20_token_contract":"****************************************"},{"asset":"coti","code":"C23DA3E3AF","description":"COTI, Currency of the Internet, is a base layer trust-based payments protocol focused on high transaction throughput and low fees.","overview":"COTI, Currency of the Internet, is a base layer trust-based payments protocol focused on high transaction throughput and low fees. The COTI token is the native currency for network participants of the COTI protocol, a low-cost high transaction throughput trust-based payment system. Network participants can earn COTI by validating transactions, participating in Arbitration Service, and collecting network fees. The COTI protocol uses a directed acyclic graph (DAG) based ledger to allow for the transaction throughput to scale as network activity grows, contrasting blockchains that tend to have the opposite effect. The COTI protocol can achieve fast consensus using its “Trustchain Algorithm” by relying on validators with high “trust scores” based on historical behavior and known properties of validators. Transactions are validated once a cumulative “trust score” threshold is reached. ","website":"https://coti.io/","whitepaper":"https://coti.io/files/COTI-technical-whitepaper.pdf","creation_date":"2019-06-22","type":"other"},{"asset":"crv","code":"C60A2CCEDA","description":"Curve Finance is an automated market maker that focuses on efficient stablecoin trading.","overview":"Curve Finance is an automated market maker that focuses on efficient stablecoin trading. Curve Finance aims to provide efficient stablecoin trading with low slippage fees using its StableSwap mechanism. The Curve AMM consists of liquidity pools of various kinds: Plain pools (stablecoin pairs), Lending pools (wrapped token pairs where the underlying is lent out to some other protocol), and Metapools (stablecoin is paired against the LP token). Its native token CRV is used for voting on governance proposals and value accrual from staking on liquidity pools. ","website":"https://curve.fi","whitepaper":"https://curve.fi/whitepaper","decimals":"18","creation_date":"2020-08-12","type":"erc20","parent_asset":"eth","erc20_token_contract":"d533a949740bb3306d119cc777fa900ba034cd52"},{"asset":"dydx","code":"C859A917F1","description":"dYdX is a decentralized margin trading and derivatives trading platform.","overview":"dYdX is a decentralized margin trading and derivatives trading platform. The protocol issues the dYdX governance tokens which can be staked for yield and used to vote on governance protocols. dYdX uses off-chain order books with on-chain settlement to facilitate efficient markets. dYdX allows for the trading of digital assets and financial products of such assets without the need for centralized authority. The main features of the dYdX platform are its Margin Trading Protocol and Options Protocol. The Margin Trading Protocol allows traders to profit off of price movements, modify leverage, and provide low-risk loans in exchange for interest-bearing dYdX tokens. The dYdX Options Protocol can be used to manage risk and create options for and trade ERC-20 tokens.","website":"https://dydx.exchange/","whitepaper":"https://whitepaper.dydx.exchange/","creation_date":"2021-09-08","type":"other"}]}"""
        assertResponse(200, expectedResponse, "/v4/security-master/assets?assets=audio,badger,coti,crv,dydx&api_key=$TEST_API_KEY")
    }

    @Test
    fun `should return first page when 'assets' parameter specified`() {
        val expectedResponse =
            """{"data":[{"asset":"audio","code":"CB568F357F","description":"Audius is a decentralized content and streaming platform streaming protocol. The AUDIO token incentivizes activity on the Audius platform, which allows artists and fans to transact directly, host and share content, and make governance decisions for the Audius protocol.","overview":"Audius is a decentralized content and streaming platform streaming protocol. The AUDIO token incentivizes activity on the Audius platform, which allows artists and fans to transact directly, host and share content, and make governance decisions for the Audius protocol. Audius aims to solve the inefficiencies present in the current music industry by creating an economy that allows for more value to be captured by artists, creates more transparency in distribution and intellectual property rights, and directs connections between artists and content consumers. AUDIO can be staked to ensure the security of the protocol, unlock features, and exert influence in governance decisions. The Audius platform also allows for content to be hosted using its decentralized storage on AudSP, the Audius-native extension 5 to IPFS. Run using “Content Nodes”, gives content creators the power of distributing and permissioning content at their discretion. ","website":"https://audius.org","whitepaper":"https://whitepaper.audius.co/AudiusWhitepaper.pdf","decimals":"18","creation_date":"2020-10-22","type":"erc20","parent_asset":"eth","erc20_token_contract":"18aaa7115705e8be94bffebde57af9bfc265b998"},{"asset":"badger","code":"C9E3139EE9","description":"Badger is a DAO focused on building infrastructure to connect Bitcoin to DeFi applications across multiple blockchains.","overview":"Badger is a DAO focused on building infrastructure to connect Bitcoin to DeFi applications across multiple blockchains. BadgerDAO is a community that builds products that integrate Bitcoin into DeFi. BadgerDAO incentivizes builders in the ecosystem using its native token, BADGER. The BADGER token also gives the user governance rights within the Badger ecosystem. BADGER can be staked across various protocols to receive bBADGER, a claim token on interest accrued from staking BADGER.","website":"https://badger.com/","whitepaper":"https://docs.badger.com/badger-finance/","decimals":"18","creation_date":"2020-11-28","type":"erc20","parent_asset":"eth","erc20_token_contract":"****************************************"}],"next_page_token":"Mg","next_page_url":"http://127.0.0.1:8080/v4/security-master/assets?assets=audio,badger,coti,crv,dydx&api_key=x1&page_size=2&next_page_token=Mg"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/security-master/assets?assets=audio,badger,coti,crv,dydx&api_key=$TEST_API_KEY&page_size=2",
        )
    }

    @Test
    fun `should return second page when 'assets' parameter specified`() {
        val expectedResponse =
            """{"data":[{"asset":"coti","code":"C23DA3E3AF","description":"COTI, Currency of the Internet, is a base layer trust-based payments protocol focused on high transaction throughput and low fees.","overview":"COTI, Currency of the Internet, is a base layer trust-based payments protocol focused on high transaction throughput and low fees. The COTI token is the native currency for network participants of the COTI protocol, a low-cost high transaction throughput trust-based payment system. Network participants can earn COTI by validating transactions, participating in Arbitration Service, and collecting network fees. The COTI protocol uses a directed acyclic graph (DAG) based ledger to allow for the transaction throughput to scale as network activity grows, contrasting blockchains that tend to have the opposite effect. The COTI protocol can achieve fast consensus using its “Trustchain Algorithm” by relying on validators with high “trust scores” based on historical behavior and known properties of validators. Transactions are validated once a cumulative “trust score” threshold is reached. ","website":"https://coti.io/","whitepaper":"https://coti.io/files/COTI-technical-whitepaper.pdf","creation_date":"2019-06-22","type":"other"},{"asset":"crv","code":"C60A2CCEDA","description":"Curve Finance is an automated market maker that focuses on efficient stablecoin trading.","overview":"Curve Finance is an automated market maker that focuses on efficient stablecoin trading. Curve Finance aims to provide efficient stablecoin trading with low slippage fees using its StableSwap mechanism. The Curve AMM consists of liquidity pools of various kinds: Plain pools (stablecoin pairs), Lending pools (wrapped token pairs where the underlying is lent out to some other protocol), and Metapools (stablecoin is paired against the LP token). Its native token CRV is used for voting on governance proposals and value accrual from staking on liquidity pools. ","website":"https://curve.fi","whitepaper":"https://curve.fi/whitepaper","decimals":"18","creation_date":"2020-08-12","type":"erc20","parent_asset":"eth","erc20_token_contract":"d533a949740bb3306d119cc777fa900ba034cd52"}],"next_page_token":"NA","next_page_url":"http://127.0.0.1:8080/v4/security-master/assets?assets=audio,badger,coti,crv,dydx&api_key=x1&page_size=2&next_page_token=NA"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/security-master/assets?assets=audio,badger,coti,crv,dydx&api_key=$TEST_API_KEY&page_size=2&next_page_token=Mg",
        )
    }

    @Test
    fun `should return last page when 'assets' parameter specified`() {
        val expectedResponse =
            """{"data":[{"asset":"dydx","code":"C859A917F1","description":"dYdX is a decentralized margin trading and derivatives trading platform.","overview":"dYdX is a decentralized margin trading and derivatives trading platform. The protocol issues the dYdX governance tokens which can be staked for yield and used to vote on governance protocols. dYdX uses off-chain order books with on-chain settlement to facilitate efficient markets. dYdX allows for the trading of digital assets and financial products of such assets without the need for centralized authority. The main features of the dYdX platform are its Margin Trading Protocol and Options Protocol. The Margin Trading Protocol allows traders to profit off of price movements, modify leverage, and provide low-risk loans in exchange for interest-bearing dYdX tokens. The dYdX Options Protocol can be used to manage risk and create options for and trade ERC-20 tokens.","website":"https://dydx.exchange/","whitepaper":"https://whitepaper.dydx.exchange/","creation_date":"2021-09-08","type":"other"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/security-master/assets?assets=audio,badger,coti,crv,dydx&api_key=$TEST_API_KEY&page_size=2&next_page_token=NA",
        )
    }

    @Test
    fun `should return assets when 'assets' parameter specified and paging from end`() {
        val expectedResponse =
            """{"data":[{"asset":"dydx","code":"C859A917F1","description":"dYdX is a decentralized margin trading and derivatives trading platform.","overview":"dYdX is a decentralized margin trading and derivatives trading platform. The protocol issues the dYdX governance tokens which can be staked for yield and used to vote on governance protocols. dYdX uses off-chain order books with on-chain settlement to facilitate efficient markets. dYdX allows for the trading of digital assets and financial products of such assets without the need for centralized authority. The main features of the dYdX platform are its Margin Trading Protocol and Options Protocol. The Margin Trading Protocol allows traders to profit off of price movements, modify leverage, and provide low-risk loans in exchange for interest-bearing dYdX tokens. The dYdX Options Protocol can be used to manage risk and create options for and trade ERC-20 tokens.","website":"https://dydx.exchange/","whitepaper":"https://whitepaper.dydx.exchange/","creation_date":"2021-09-08","type":"other"},{"asset":"crv","code":"C60A2CCEDA","description":"Curve Finance is an automated market maker that focuses on efficient stablecoin trading.","overview":"Curve Finance is an automated market maker that focuses on efficient stablecoin trading. Curve Finance aims to provide efficient stablecoin trading with low slippage fees using its StableSwap mechanism. The Curve AMM consists of liquidity pools of various kinds: Plain pools (stablecoin pairs), Lending pools (wrapped token pairs where the underlying is lent out to some other protocol), and Metapools (stablecoin is paired against the LP token). Its native token CRV is used for voting on governance proposals and value accrual from staking on liquidity pools. ","website":"https://curve.fi","whitepaper":"https://curve.fi/whitepaper","decimals":"18","creation_date":"2020-08-12","type":"erc20","parent_asset":"eth","erc20_token_contract":"d533a949740bb3306d119cc777fa900ba034cd52"},{"asset":"coti","code":"C23DA3E3AF","description":"COTI, Currency of the Internet, is a base layer trust-based payments protocol focused on high transaction throughput and low fees.","overview":"COTI, Currency of the Internet, is a base layer trust-based payments protocol focused on high transaction throughput and low fees. The COTI token is the native currency for network participants of the COTI protocol, a low-cost high transaction throughput trust-based payment system. Network participants can earn COTI by validating transactions, participating in Arbitration Service, and collecting network fees. The COTI protocol uses a directed acyclic graph (DAG) based ledger to allow for the transaction throughput to scale as network activity grows, contrasting blockchains that tend to have the opposite effect. The COTI protocol can achieve fast consensus using its “Trustchain Algorithm” by relying on validators with high “trust scores” based on historical behavior and known properties of validators. Transactions are validated once a cumulative “trust score” threshold is reached. ","website":"https://coti.io/","whitepaper":"https://coti.io/files/COTI-technical-whitepaper.pdf","creation_date":"2019-06-22","type":"other"},{"asset":"badger","code":"C9E3139EE9","description":"Badger is a DAO focused on building infrastructure to connect Bitcoin to DeFi applications across multiple blockchains.","overview":"Badger is a DAO focused on building infrastructure to connect Bitcoin to DeFi applications across multiple blockchains. BadgerDAO is a community that builds products that integrate Bitcoin into DeFi. BadgerDAO incentivizes builders in the ecosystem using its native token, BADGER. The BADGER token also gives the user governance rights within the Badger ecosystem. BADGER can be staked across various protocols to receive bBADGER, a claim token on interest accrued from staking BADGER.","website":"https://badger.com/","whitepaper":"https://docs.badger.com/badger-finance/","decimals":"18","creation_date":"2020-11-28","type":"erc20","parent_asset":"eth","erc20_token_contract":"****************************************"},{"asset":"audio","code":"CB568F357F","description":"Audius is a decentralized content and streaming platform streaming protocol. The AUDIO token incentivizes activity on the Audius platform, which allows artists and fans to transact directly, host and share content, and make governance decisions for the Audius protocol.","overview":"Audius is a decentralized content and streaming platform streaming protocol. The AUDIO token incentivizes activity on the Audius platform, which allows artists and fans to transact directly, host and share content, and make governance decisions for the Audius protocol. Audius aims to solve the inefficiencies present in the current music industry by creating an economy that allows for more value to be captured by artists, creates more transparency in distribution and intellectual property rights, and directs connections between artists and content consumers. AUDIO can be staked to ensure the security of the protocol, unlock features, and exert influence in governance decisions. The Audius platform also allows for content to be hosted using its decentralized storage on AudSP, the Audius-native extension 5 to IPFS. Run using “Content Nodes”, gives content creators the power of distributing and permissioning content at their discretion. ","website":"https://audius.org","whitepaper":"https://whitepaper.audius.co/AudiusWhitepaper.pdf","decimals":"18","creation_date":"2020-10-22","type":"erc20","parent_asset":"eth","erc20_token_contract":"18aaa7115705e8be94bffebde57af9bfc265b998"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/security-master/assets?assets=audio,badger,coti,crv,dydx&api_key=$TEST_API_KEY&paging_from=end",
        )
    }

    @Test
    fun `should return first page when 'assets' parameter specified and paging from end`() {
        val expectedResponse =
            """{"data":[{"asset":"dydx","code":"C859A917F1","description":"dYdX is a decentralized margin trading and derivatives trading platform.","overview":"dYdX is a decentralized margin trading and derivatives trading platform. The protocol issues the dYdX governance tokens which can be staked for yield and used to vote on governance protocols. dYdX uses off-chain order books with on-chain settlement to facilitate efficient markets. dYdX allows for the trading of digital assets and financial products of such assets without the need for centralized authority. The main features of the dYdX platform are its Margin Trading Protocol and Options Protocol. The Margin Trading Protocol allows traders to profit off of price movements, modify leverage, and provide low-risk loans in exchange for interest-bearing dYdX tokens. The dYdX Options Protocol can be used to manage risk and create options for and trade ERC-20 tokens.","website":"https://dydx.exchange/","whitepaper":"https://whitepaper.dydx.exchange/","creation_date":"2021-09-08","type":"other"},{"asset":"crv","code":"C60A2CCEDA","description":"Curve Finance is an automated market maker that focuses on efficient stablecoin trading.","overview":"Curve Finance is an automated market maker that focuses on efficient stablecoin trading. Curve Finance aims to provide efficient stablecoin trading with low slippage fees using its StableSwap mechanism. The Curve AMM consists of liquidity pools of various kinds: Plain pools (stablecoin pairs), Lending pools (wrapped token pairs where the underlying is lent out to some other protocol), and Metapools (stablecoin is paired against the LP token). Its native token CRV is used for voting on governance proposals and value accrual from staking on liquidity pools. ","website":"https://curve.fi","whitepaper":"https://curve.fi/whitepaper","decimals":"18","creation_date":"2020-08-12","type":"erc20","parent_asset":"eth","erc20_token_contract":"d533a949740bb3306d119cc777fa900ba034cd52"}],"next_page_token":"Mg","next_page_url":"http://127.0.0.1:8080/v4/security-master/assets?assets=audio,badger,coti,crv,dydx&api_key=x1&paging_from=end&page_size=2&next_page_token=Mg"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/security-master/assets?assets=audio,badger,coti,crv,dydx&api_key=$TEST_API_KEY&paging_from=end&page_size=2",
        )
    }

    @Test
    fun `should return  second page when 'assets' parameter specified and paging from end`() {
        val expectedResponse =
            """{"data":[{"asset":"coti","code":"C23DA3E3AF","description":"COTI, Currency of the Internet, is a base layer trust-based payments protocol focused on high transaction throughput and low fees.","overview":"COTI, Currency of the Internet, is a base layer trust-based payments protocol focused on high transaction throughput and low fees. The COTI token is the native currency for network participants of the COTI protocol, a low-cost high transaction throughput trust-based payment system. Network participants can earn COTI by validating transactions, participating in Arbitration Service, and collecting network fees. The COTI protocol uses a directed acyclic graph (DAG) based ledger to allow for the transaction throughput to scale as network activity grows, contrasting blockchains that tend to have the opposite effect. The COTI protocol can achieve fast consensus using its “Trustchain Algorithm” by relying on validators with high “trust scores” based on historical behavior and known properties of validators. Transactions are validated once a cumulative “trust score” threshold is reached. ","website":"https://coti.io/","whitepaper":"https://coti.io/files/COTI-technical-whitepaper.pdf","creation_date":"2019-06-22","type":"other"},{"asset":"badger","code":"C9E3139EE9","description":"Badger is a DAO focused on building infrastructure to connect Bitcoin to DeFi applications across multiple blockchains.","overview":"Badger is a DAO focused on building infrastructure to connect Bitcoin to DeFi applications across multiple blockchains. BadgerDAO is a community that builds products that integrate Bitcoin into DeFi. BadgerDAO incentivizes builders in the ecosystem using its native token, BADGER. The BADGER token also gives the user governance rights within the Badger ecosystem. BADGER can be staked across various protocols to receive bBADGER, a claim token on interest accrued from staking BADGER.","website":"https://badger.com/","whitepaper":"https://docs.badger.com/badger-finance/","decimals":"18","creation_date":"2020-11-28","type":"erc20","parent_asset":"eth","erc20_token_contract":"****************************************"}],"next_page_token":"NA","next_page_url":"http://127.0.0.1:8080/v4/security-master/assets?assets=audio,badger,coti,crv,dydx&api_key=x1&paging_from=end&page_size=2&next_page_token=NA"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/security-master/assets?assets=audio,badger,coti,crv,dydx&api_key=$TEST_API_KEY&paging_from=end&page_size=2&next_page_token=Mg",
        )
    }

    @Test
    fun `should return  last page when 'assets' parameter specified and paging from end`() {
        val expectedResponse =
            """{"data":[{"asset":"audio","code":"CB568F357F","description":"Audius is a decentralized content and streaming platform streaming protocol. The AUDIO token incentivizes activity on the Audius platform, which allows artists and fans to transact directly, host and share content, and make governance decisions for the Audius protocol.","overview":"Audius is a decentralized content and streaming platform streaming protocol. The AUDIO token incentivizes activity on the Audius platform, which allows artists and fans to transact directly, host and share content, and make governance decisions for the Audius protocol. Audius aims to solve the inefficiencies present in the current music industry by creating an economy that allows for more value to be captured by artists, creates more transparency in distribution and intellectual property rights, and directs connections between artists and content consumers. AUDIO can be staked to ensure the security of the protocol, unlock features, and exert influence in governance decisions. The Audius platform also allows for content to be hosted using its decentralized storage on AudSP, the Audius-native extension 5 to IPFS. Run using “Content Nodes”, gives content creators the power of distributing and permissioning content at their discretion. ","website":"https://audius.org","whitepaper":"https://whitepaper.audius.co/AudiusWhitepaper.pdf","decimals":"18","creation_date":"2020-10-22","type":"erc20","parent_asset":"eth","erc20_token_contract":"18aaa7115705e8be94bffebde57af9bfc265b998"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/security-master/assets?assets=audio,badger,coti,crv,dydx&api_key=$TEST_API_KEY&paging_from=end&page_size=2&next_page_token=NA",
        )
    }

    @Test
    fun `should return assets when 'codes' parameter specified`() {
        val expectedResponse =
            """{"data":[{"asset":"audio","code":"CB568F357F","description":"Audius is a decentralized content and streaming platform streaming protocol. The AUDIO token incentivizes activity on the Audius platform, which allows artists and fans to transact directly, host and share content, and make governance decisions for the Audius protocol.","overview":"Audius is a decentralized content and streaming platform streaming protocol. The AUDIO token incentivizes activity on the Audius platform, which allows artists and fans to transact directly, host and share content, and make governance decisions for the Audius protocol. Audius aims to solve the inefficiencies present in the current music industry by creating an economy that allows for more value to be captured by artists, creates more transparency in distribution and intellectual property rights, and directs connections between artists and content consumers. AUDIO can be staked to ensure the security of the protocol, unlock features, and exert influence in governance decisions. The Audius platform also allows for content to be hosted using its decentralized storage on AudSP, the Audius-native extension 5 to IPFS. Run using “Content Nodes”, gives content creators the power of distributing and permissioning content at their discretion. ","website":"https://audius.org","whitepaper":"https://whitepaper.audius.co/AudiusWhitepaper.pdf","decimals":"18","creation_date":"2020-10-22","type":"erc20","parent_asset":"eth","erc20_token_contract":"18aaa7115705e8be94bffebde57af9bfc265b998"},{"asset":"badger","code":"C9E3139EE9","description":"Badger is a DAO focused on building infrastructure to connect Bitcoin to DeFi applications across multiple blockchains.","overview":"Badger is a DAO focused on building infrastructure to connect Bitcoin to DeFi applications across multiple blockchains. BadgerDAO is a community that builds products that integrate Bitcoin into DeFi. BadgerDAO incentivizes builders in the ecosystem using its native token, BADGER. The BADGER token also gives the user governance rights within the Badger ecosystem. BADGER can be staked across various protocols to receive bBADGER, a claim token on interest accrued from staking BADGER.","website":"https://badger.com/","whitepaper":"https://docs.badger.com/badger-finance/","decimals":"18","creation_date":"2020-11-28","type":"erc20","parent_asset":"eth","erc20_token_contract":"****************************************"},{"asset":"coti","code":"C23DA3E3AF","description":"COTI, Currency of the Internet, is a base layer trust-based payments protocol focused on high transaction throughput and low fees.","overview":"COTI, Currency of the Internet, is a base layer trust-based payments protocol focused on high transaction throughput and low fees. The COTI token is the native currency for network participants of the COTI protocol, a low-cost high transaction throughput trust-based payment system. Network participants can earn COTI by validating transactions, participating in Arbitration Service, and collecting network fees. The COTI protocol uses a directed acyclic graph (DAG) based ledger to allow for the transaction throughput to scale as network activity grows, contrasting blockchains that tend to have the opposite effect. The COTI protocol can achieve fast consensus using its “Trustchain Algorithm” by relying on validators with high “trust scores” based on historical behavior and known properties of validators. Transactions are validated once a cumulative “trust score” threshold is reached. ","website":"https://coti.io/","whitepaper":"https://coti.io/files/COTI-technical-whitepaper.pdf","creation_date":"2019-06-22","type":"other"},{"asset":"crv","code":"C60A2CCEDA","description":"Curve Finance is an automated market maker that focuses on efficient stablecoin trading.","overview":"Curve Finance is an automated market maker that focuses on efficient stablecoin trading. Curve Finance aims to provide efficient stablecoin trading with low slippage fees using its StableSwap mechanism. The Curve AMM consists of liquidity pools of various kinds: Plain pools (stablecoin pairs), Lending pools (wrapped token pairs where the underlying is lent out to some other protocol), and Metapools (stablecoin is paired against the LP token). Its native token CRV is used for voting on governance proposals and value accrual from staking on liquidity pools. ","website":"https://curve.fi","whitepaper":"https://curve.fi/whitepaper","decimals":"18","creation_date":"2020-08-12","type":"erc20","parent_asset":"eth","erc20_token_contract":"d533a949740bb3306d119cc777fa900ba034cd52"},{"asset":"dydx","code":"C859A917F1","description":"dYdX is a decentralized margin trading and derivatives trading platform.","overview":"dYdX is a decentralized margin trading and derivatives trading platform. The protocol issues the dYdX governance tokens which can be staked for yield and used to vote on governance protocols. dYdX uses off-chain order books with on-chain settlement to facilitate efficient markets. dYdX allows for the trading of digital assets and financial products of such assets without the need for centralized authority. The main features of the dYdX platform are its Margin Trading Protocol and Options Protocol. The Margin Trading Protocol allows traders to profit off of price movements, modify leverage, and provide low-risk loans in exchange for interest-bearing dYdX tokens. The dYdX Options Protocol can be used to manage risk and create options for and trade ERC-20 tokens.","website":"https://dydx.exchange/","whitepaper":"https://whitepaper.dydx.exchange/","creation_date":"2021-09-08","type":"other"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/security-master/assets?codes=C859A917F1,CB568F357F,C9E3139EE9,C23DA3E3AF,C60A2CCEDA&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `should return first page when 'codes' parameter specified`() {
        val expectedResponse =
            """{"data":[{"asset":"audio","code":"CB568F357F","description":"Audius is a decentralized content and streaming platform streaming protocol. The AUDIO token incentivizes activity on the Audius platform, which allows artists and fans to transact directly, host and share content, and make governance decisions for the Audius protocol.","overview":"Audius is a decentralized content and streaming platform streaming protocol. The AUDIO token incentivizes activity on the Audius platform, which allows artists and fans to transact directly, host and share content, and make governance decisions for the Audius protocol. Audius aims to solve the inefficiencies present in the current music industry by creating an economy that allows for more value to be captured by artists, creates more transparency in distribution and intellectual property rights, and directs connections between artists and content consumers. AUDIO can be staked to ensure the security of the protocol, unlock features, and exert influence in governance decisions. The Audius platform also allows for content to be hosted using its decentralized storage on AudSP, the Audius-native extension 5 to IPFS. Run using “Content Nodes”, gives content creators the power of distributing and permissioning content at their discretion. ","website":"https://audius.org","whitepaper":"https://whitepaper.audius.co/AudiusWhitepaper.pdf","decimals":"18","creation_date":"2020-10-22","type":"erc20","parent_asset":"eth","erc20_token_contract":"18aaa7115705e8be94bffebde57af9bfc265b998"},{"asset":"badger","code":"C9E3139EE9","description":"Badger is a DAO focused on building infrastructure to connect Bitcoin to DeFi applications across multiple blockchains.","overview":"Badger is a DAO focused on building infrastructure to connect Bitcoin to DeFi applications across multiple blockchains. BadgerDAO is a community that builds products that integrate Bitcoin into DeFi. BadgerDAO incentivizes builders in the ecosystem using its native token, BADGER. The BADGER token also gives the user governance rights within the Badger ecosystem. BADGER can be staked across various protocols to receive bBADGER, a claim token on interest accrued from staking BADGER.","website":"https://badger.com/","whitepaper":"https://docs.badger.com/badger-finance/","decimals":"18","creation_date":"2020-11-28","type":"erc20","parent_asset":"eth","erc20_token_contract":"****************************************"}],"next_page_token":"Mg","next_page_url":"http://127.0.0.1:8080/v4/security-master/assets?codes=C859A917F1,CB568F357F,C9E3139EE9,C23DA3E3AF,C60A2CCEDA&api_key=x1&page_size=2&next_page_token=Mg"}""".trimIndent()
        assertResponse(
            200,
            expectedResponse,
            "/v4/security-master/assets?codes=C859A917F1,CB568F357F,C9E3139EE9,C23DA3E3AF,C60A2CCEDA&api_key=$TEST_API_KEY&page_size=2",
        )
    }

    @Test
    fun `should return second page when 'codes' parameter specified`() {
        val expectedResponse =
            """{"data":[{"asset":"coti","code":"C23DA3E3AF","description":"COTI, Currency of the Internet, is a base layer trust-based payments protocol focused on high transaction throughput and low fees.","overview":"COTI, Currency of the Internet, is a base layer trust-based payments protocol focused on high transaction throughput and low fees. The COTI token is the native currency for network participants of the COTI protocol, a low-cost high transaction throughput trust-based payment system. Network participants can earn COTI by validating transactions, participating in Arbitration Service, and collecting network fees. The COTI protocol uses a directed acyclic graph (DAG) based ledger to allow for the transaction throughput to scale as network activity grows, contrasting blockchains that tend to have the opposite effect. The COTI protocol can achieve fast consensus using its “Trustchain Algorithm” by relying on validators with high “trust scores” based on historical behavior and known properties of validators. Transactions are validated once a cumulative “trust score” threshold is reached. ","website":"https://coti.io/","whitepaper":"https://coti.io/files/COTI-technical-whitepaper.pdf","creation_date":"2019-06-22","type":"other"},{"asset":"crv","code":"C60A2CCEDA","description":"Curve Finance is an automated market maker that focuses on efficient stablecoin trading.","overview":"Curve Finance is an automated market maker that focuses on efficient stablecoin trading. Curve Finance aims to provide efficient stablecoin trading with low slippage fees using its StableSwap mechanism. The Curve AMM consists of liquidity pools of various kinds: Plain pools (stablecoin pairs), Lending pools (wrapped token pairs where the underlying is lent out to some other protocol), and Metapools (stablecoin is paired against the LP token). Its native token CRV is used for voting on governance proposals and value accrual from staking on liquidity pools. ","website":"https://curve.fi","whitepaper":"https://curve.fi/whitepaper","decimals":"18","creation_date":"2020-08-12","type":"erc20","parent_asset":"eth","erc20_token_contract":"d533a949740bb3306d119cc777fa900ba034cd52"}],"next_page_token":"NA","next_page_url":"http://127.0.0.1:8080/v4/security-master/assets?codes=C859A917F1,CB568F357F,C9E3139EE9,C23DA3E3AF,C60A2CCEDA&api_key=x1&page_size=2&next_page_token=NA"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/security-master/assets?codes=C859A917F1,CB568F357F,C9E3139EE9,C23DA3E3AF,C60A2CCEDA&api_key=$TEST_API_KEY&page_size=2&next_page_token=Mg",
        )
    }

    @Test
    fun `should return last page when 'codes' parameter specified`() {
        val expectedResponse =
            """{"data":[{"asset":"dydx","code":"C859A917F1","description":"dYdX is a decentralized margin trading and derivatives trading platform.","overview":"dYdX is a decentralized margin trading and derivatives trading platform. The protocol issues the dYdX governance tokens which can be staked for yield and used to vote on governance protocols. dYdX uses off-chain order books with on-chain settlement to facilitate efficient markets. dYdX allows for the trading of digital assets and financial products of such assets without the need for centralized authority. The main features of the dYdX platform are its Margin Trading Protocol and Options Protocol. The Margin Trading Protocol allows traders to profit off of price movements, modify leverage, and provide low-risk loans in exchange for interest-bearing dYdX tokens. The dYdX Options Protocol can be used to manage risk and create options for and trade ERC-20 tokens.","website":"https://dydx.exchange/","whitepaper":"https://whitepaper.dydx.exchange/","creation_date":"2021-09-08","type":"other"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/security-master/assets?codes=C859A917F1,CB568F357F,C9E3139EE9,C23DA3E3AF,C60A2CCEDA&api_key=$TEST_API_KEY&page_size=2&next_page_token=NA",
        )
    }

    @Test
    fun `should return assets when 'codes' parameter specified and paging from end`() {
        val expectedResponse =
            """{"data":[{"asset":"dydx","code":"C859A917F1","description":"dYdX is a decentralized margin trading and derivatives trading platform.","overview":"dYdX is a decentralized margin trading and derivatives trading platform. The protocol issues the dYdX governance tokens which can be staked for yield and used to vote on governance protocols. dYdX uses off-chain order books with on-chain settlement to facilitate efficient markets. dYdX allows for the trading of digital assets and financial products of such assets without the need for centralized authority. The main features of the dYdX platform are its Margin Trading Protocol and Options Protocol. The Margin Trading Protocol allows traders to profit off of price movements, modify leverage, and provide low-risk loans in exchange for interest-bearing dYdX tokens. The dYdX Options Protocol can be used to manage risk and create options for and trade ERC-20 tokens.","website":"https://dydx.exchange/","whitepaper":"https://whitepaper.dydx.exchange/","creation_date":"2021-09-08","type":"other"},{"asset":"crv","code":"C60A2CCEDA","description":"Curve Finance is an automated market maker that focuses on efficient stablecoin trading.","overview":"Curve Finance is an automated market maker that focuses on efficient stablecoin trading. Curve Finance aims to provide efficient stablecoin trading with low slippage fees using its StableSwap mechanism. The Curve AMM consists of liquidity pools of various kinds: Plain pools (stablecoin pairs), Lending pools (wrapped token pairs where the underlying is lent out to some other protocol), and Metapools (stablecoin is paired against the LP token). Its native token CRV is used for voting on governance proposals and value accrual from staking on liquidity pools. ","website":"https://curve.fi","whitepaper":"https://curve.fi/whitepaper","decimals":"18","creation_date":"2020-08-12","type":"erc20","parent_asset":"eth","erc20_token_contract":"d533a949740bb3306d119cc777fa900ba034cd52"},{"asset":"coti","code":"C23DA3E3AF","description":"COTI, Currency of the Internet, is a base layer trust-based payments protocol focused on high transaction throughput and low fees.","overview":"COTI, Currency of the Internet, is a base layer trust-based payments protocol focused on high transaction throughput and low fees. The COTI token is the native currency for network participants of the COTI protocol, a low-cost high transaction throughput trust-based payment system. Network participants can earn COTI by validating transactions, participating in Arbitration Service, and collecting network fees. The COTI protocol uses a directed acyclic graph (DAG) based ledger to allow for the transaction throughput to scale as network activity grows, contrasting blockchains that tend to have the opposite effect. The COTI protocol can achieve fast consensus using its “Trustchain Algorithm” by relying on validators with high “trust scores” based on historical behavior and known properties of validators. Transactions are validated once a cumulative “trust score” threshold is reached. ","website":"https://coti.io/","whitepaper":"https://coti.io/files/COTI-technical-whitepaper.pdf","creation_date":"2019-06-22","type":"other"},{"asset":"badger","code":"C9E3139EE9","description":"Badger is a DAO focused on building infrastructure to connect Bitcoin to DeFi applications across multiple blockchains.","overview":"Badger is a DAO focused on building infrastructure to connect Bitcoin to DeFi applications across multiple blockchains. BadgerDAO is a community that builds products that integrate Bitcoin into DeFi. BadgerDAO incentivizes builders in the ecosystem using its native token, BADGER. The BADGER token also gives the user governance rights within the Badger ecosystem. BADGER can be staked across various protocols to receive bBADGER, a claim token on interest accrued from staking BADGER.","website":"https://badger.com/","whitepaper":"https://docs.badger.com/badger-finance/","decimals":"18","creation_date":"2020-11-28","type":"erc20","parent_asset":"eth","erc20_token_contract":"****************************************"},{"asset":"audio","code":"CB568F357F","description":"Audius is a decentralized content and streaming platform streaming protocol. The AUDIO token incentivizes activity on the Audius platform, which allows artists and fans to transact directly, host and share content, and make governance decisions for the Audius protocol.","overview":"Audius is a decentralized content and streaming platform streaming protocol. The AUDIO token incentivizes activity on the Audius platform, which allows artists and fans to transact directly, host and share content, and make governance decisions for the Audius protocol. Audius aims to solve the inefficiencies present in the current music industry by creating an economy that allows for more value to be captured by artists, creates more transparency in distribution and intellectual property rights, and directs connections between artists and content consumers. AUDIO can be staked to ensure the security of the protocol, unlock features, and exert influence in governance decisions. The Audius platform also allows for content to be hosted using its decentralized storage on AudSP, the Audius-native extension 5 to IPFS. Run using “Content Nodes”, gives content creators the power of distributing and permissioning content at their discretion. ","website":"https://audius.org","whitepaper":"https://whitepaper.audius.co/AudiusWhitepaper.pdf","decimals":"18","creation_date":"2020-10-22","type":"erc20","parent_asset":"eth","erc20_token_contract":"18aaa7115705e8be94bffebde57af9bfc265b998"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/security-master/assets?codes=C859A917F1,CB568F357F,C9E3139EE9,C23DA3E3AF,C60A2CCEDA&api_key=$TEST_API_KEY&paging_from=end",
        )
    }

    @Test
    fun `should return first page when 'codes' parameter specified and paging from end`() {
        val expectedResponse =
            """{"data":[{"asset":"dydx","code":"C859A917F1","description":"dYdX is a decentralized margin trading and derivatives trading platform.","overview":"dYdX is a decentralized margin trading and derivatives trading platform. The protocol issues the dYdX governance tokens which can be staked for yield and used to vote on governance protocols. dYdX uses off-chain order books with on-chain settlement to facilitate efficient markets. dYdX allows for the trading of digital assets and financial products of such assets without the need for centralized authority. The main features of the dYdX platform are its Margin Trading Protocol and Options Protocol. The Margin Trading Protocol allows traders to profit off of price movements, modify leverage, and provide low-risk loans in exchange for interest-bearing dYdX tokens. The dYdX Options Protocol can be used to manage risk and create options for and trade ERC-20 tokens.","website":"https://dydx.exchange/","whitepaper":"https://whitepaper.dydx.exchange/","creation_date":"2021-09-08","type":"other"},{"asset":"crv","code":"C60A2CCEDA","description":"Curve Finance is an automated market maker that focuses on efficient stablecoin trading.","overview":"Curve Finance is an automated market maker that focuses on efficient stablecoin trading. Curve Finance aims to provide efficient stablecoin trading with low slippage fees using its StableSwap mechanism. The Curve AMM consists of liquidity pools of various kinds: Plain pools (stablecoin pairs), Lending pools (wrapped token pairs where the underlying is lent out to some other protocol), and Metapools (stablecoin is paired against the LP token). Its native token CRV is used for voting on governance proposals and value accrual from staking on liquidity pools. ","website":"https://curve.fi","whitepaper":"https://curve.fi/whitepaper","decimals":"18","creation_date":"2020-08-12","type":"erc20","parent_asset":"eth","erc20_token_contract":"d533a949740bb3306d119cc777fa900ba034cd52"}],"next_page_token":"Mg","next_page_url":"http://127.0.0.1:8080/v4/security-master/assets?codes=C859A917F1,CB568F357F,C9E3139EE9,C23DA3E3AF,C60A2CCEDA&api_key=x1&paging_from=end&page_size=2&next_page_token=Mg"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/security-master/assets?codes=C859A917F1,CB568F357F,C9E3139EE9,C23DA3E3AF,C60A2CCEDA&api_key=$TEST_API_KEY&paging_from=end&page_size=2",
        )
    }

    @Test
    fun `should return  second page when 'codes' parameter specified and paging from end`() {
        val expectedResponse =
            """{"data":[{"asset":"coti","code":"C23DA3E3AF","description":"COTI, Currency of the Internet, is a base layer trust-based payments protocol focused on high transaction throughput and low fees.","overview":"COTI, Currency of the Internet, is a base layer trust-based payments protocol focused on high transaction throughput and low fees. The COTI token is the native currency for network participants of the COTI protocol, a low-cost high transaction throughput trust-based payment system. Network participants can earn COTI by validating transactions, participating in Arbitration Service, and collecting network fees. The COTI protocol uses a directed acyclic graph (DAG) based ledger to allow for the transaction throughput to scale as network activity grows, contrasting blockchains that tend to have the opposite effect. The COTI protocol can achieve fast consensus using its “Trustchain Algorithm” by relying on validators with high “trust scores” based on historical behavior and known properties of validators. Transactions are validated once a cumulative “trust score” threshold is reached. ","website":"https://coti.io/","whitepaper":"https://coti.io/files/COTI-technical-whitepaper.pdf","creation_date":"2019-06-22","type":"other"},{"asset":"badger","code":"C9E3139EE9","description":"Badger is a DAO focused on building infrastructure to connect Bitcoin to DeFi applications across multiple blockchains.","overview":"Badger is a DAO focused on building infrastructure to connect Bitcoin to DeFi applications across multiple blockchains. BadgerDAO is a community that builds products that integrate Bitcoin into DeFi. BadgerDAO incentivizes builders in the ecosystem using its native token, BADGER. The BADGER token also gives the user governance rights within the Badger ecosystem. BADGER can be staked across various protocols to receive bBADGER, a claim token on interest accrued from staking BADGER.","website":"https://badger.com/","whitepaper":"https://docs.badger.com/badger-finance/","decimals":"18","creation_date":"2020-11-28","type":"erc20","parent_asset":"eth","erc20_token_contract":"****************************************"}],"next_page_token":"NA","next_page_url":"http://127.0.0.1:8080/v4/security-master/assets?codes=C859A917F1,CB568F357F,C9E3139EE9,C23DA3E3AF,C60A2CCEDA&api_key=x1&paging_from=end&page_size=2&next_page_token=NA"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/security-master/assets?codes=C859A917F1,CB568F357F,C9E3139EE9,C23DA3E3AF,C60A2CCEDA&api_key=$TEST_API_KEY&paging_from=end&page_size=2&next_page_token=Mg",
        )
    }

    @Test
    fun `should return  last page when 'codes' parameter specified and paging from end`() {
        val expectedResponse =
            """{"data":[{"asset":"audio","code":"CB568F357F","description":"Audius is a decentralized content and streaming platform streaming protocol. The AUDIO token incentivizes activity on the Audius platform, which allows artists and fans to transact directly, host and share content, and make governance decisions for the Audius protocol.","overview":"Audius is a decentralized content and streaming platform streaming protocol. The AUDIO token incentivizes activity on the Audius platform, which allows artists and fans to transact directly, host and share content, and make governance decisions for the Audius protocol. Audius aims to solve the inefficiencies present in the current music industry by creating an economy that allows for more value to be captured by artists, creates more transparency in distribution and intellectual property rights, and directs connections between artists and content consumers. AUDIO can be staked to ensure the security of the protocol, unlock features, and exert influence in governance decisions. The Audius platform also allows for content to be hosted using its decentralized storage on AudSP, the Audius-native extension 5 to IPFS. Run using “Content Nodes”, gives content creators the power of distributing and permissioning content at their discretion. ","website":"https://audius.org","whitepaper":"https://whitepaper.audius.co/AudiusWhitepaper.pdf","decimals":"18","creation_date":"2020-10-22","type":"erc20","parent_asset":"eth","erc20_token_contract":"18aaa7115705e8be94bffebde57af9bfc265b998"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/security-master/assets?codes=C859A917F1,CB568F357F,C9E3139EE9,C23DA3E3AF,C60A2CCEDA&api_key=$TEST_API_KEY&paging_from=end&page_size=2&next_page_token=NA",
        )
    }
}
