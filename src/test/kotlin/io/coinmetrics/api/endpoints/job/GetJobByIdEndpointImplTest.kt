package io.coinmetrics.api.endpoints.job

import io.coinmetrics.api.endpoints.BaseJobTest
import io.coinmetrics.api.helper.TEST_API_KEY
import io.coinmetrics.api.models.JobDetails
import io.coinmetrics.api.models.JobStatus
import io.coinmetrics.api.utils.awaitUntilAsserted
import io.coinmetrics.jobs.networkdata.GeneralWorkflowExecutionResult
import io.coinmetrics.testing.autoexpect.AutoExpect
import io.temporal.activity.ActivityInterface
import io.temporal.activity.ActivityOptions
import io.temporal.common.RetryOptions
import io.temporal.workflow.Workflow
import io.temporal.workflow.WorkflowInterface
import io.temporal.workflow.WorkflowMethod
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.fail
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import java.net.URLEncoder
import java.nio.charset.StandardCharsets
import java.time.Duration
import java.time.Instant
import java.util.Base64
import java.util.UUID
import java.util.concurrent.CompletableFuture
import java.util.concurrent.Future

@ExtendWith(AutoExpect::class)
class GetJobByIdEndpointImplTest : BaseJobTest() {
    private lateinit var completion: CompletableFuture<GeneralWorkflowExecutionResult>

    @BeforeEach
    fun setUpEach() {
        completion = CompletableFuture()
    }

    @AfterEach
    fun tearDownEach() {
        completion.completeExceptionally(Exception("tearDown"))
    }

    override fun workflowImplementationTypes() = listOf(TestWorkflowImpl::class.java)

    override fun activityImplementations() = listOf(TestActivityImpl { completion })

    @Test
    fun `should return 400 when invalid job specified`() {
        getResponse("/v4/jobs?ids=ODQwYTk0MDAtY2Q0ZS00MzI1LTlhNGEtOGViMDVlMGYzOWJk&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should return 400 when invalid job specified 2`() {
        val invalidIds =
            listOf(
                "",
                " ",
                "     ",
                "1",
                "asdk as",
            )
        for (invalidId in invalidIds) {
            assertEquals(400, getResponse("/v4/jobs?ids=${URLEncoder.encode(invalidId, StandardCharsets.UTF_8)}&api_key=$TEST_API_KEY").status)
        }
    }

    @Test
    fun `should return empty when job not found`() {
        val validJobId = Base64.getEncoder().encodeToString("${UUID.randomUUID()}:${UUID.randomUUID()}".toByteArray())
        getResponse("/v4/jobs?ids=$validJobId&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should return empty when Temporal service thinks runId is invalid`() {
        val jobId = Base64.getEncoder().encodeToString("${UUID.randomUUID()}:8ab33f52-7eae-3fc7-a703-575295c776aX".toByteArray())
        getResponse("/v4/jobs?ids=$jobId&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun statuses() =
        runBlocking {
            var jobId = ""

            suspend fun runJob() {
                jobId =
                    jobService
                        .executeNewOrReturnRunning(
                            origin = "",
                            apiKey = "",
                            TestWorkflow::class.java,
                            workflowParam = TestParam(),
                            workflowMethod = TestWorkflow::run,
                            workflowId = UUID.randomUUID().toString(),
                        ).getOrElse { fail(it.toString()) }
                        .jobId
            }

            val expectedMinCreationTime = Instant.now()
            var now = expectedMinCreationTime
            runJob()

            fun getJob() = getJobById(TEST_API_KEY, jobId, receivedTime = now)

            var jobDetails = getJob()
            assertEquals(JobStatus.RUNNING, jobDetails.status)
            AutoExpect.id("running").verifyJson(jobDetails.toExpectations(jobId, expectedMinCreationTime))

            completion.complete(GeneralWorkflowExecutionResult(listOf("link1", "link2")))
            awaitUntilAsserted {
                jobDetails = getJob()
                assertEquals(JobStatus.COMPLETED, jobDetails.status)
            }

            AutoExpect.id("completed").verifyJson(jobDetails.toExpectations(jobId, expectedMinCreationTime))

            now = Instant.parse(jobDetails.expirationTime!!)
            jobDetails = getJob()
            assertEquals(JobStatus.EXPIRED, jobDetails.status)
            AutoExpect.id("expired").verifyJson(jobDetails.toExpectations(jobId, expectedMinCreationTime))

            now = Instant.now()
            completion = CompletableFuture()
            completion.completeExceptionally(Exception("test"))
            runJob()

            awaitUntilAsserted {
                jobDetails = getJob()
                assertEquals(JobStatus.FAILED, jobDetails.status)
            }
            AutoExpect.id("failed").verifyJson(jobDetails.toExpectations(jobId, now))

            // Failed job never transitions to "expired" state.
            now = Instant.MAX
            val jobDetails2 = getJob()
            assertEquals(jobDetails, jobDetails2)
        }

    private fun JobDetails?.toExpectations(
        jobId: String,
        expectedMinCreationTime: Instant,
    ): JobDetails {
        assertNotNull(this)
        this!!
        assertEquals(jobId, id)

        val creationTimeInstant = Instant.parse(creationTime)
        val completionTimeInstant = completionTime?.let { Instant.parse(it) }
        val expirationTimeInstant = expirationTime?.let { Instant.parse(it) }
        val now = Instant.now()
        require(expectedMinCreationTime <= now)

        assertTrue(creationTimeInstant >= expectedMinCreationTime)
        assertTrue(completionTimeInstant == null || completionTimeInstant > creationTimeInstant && completionTimeInstant <= now)
        assertTrue(expirationTimeInstant == null || expirationTimeInstant == completionTimeInstant!! + mainApiModule.config.temporalConfig!!.expirationPeriod)

        return copy(
            id = "<VALID>",
            creationTime = "<VALID>",
            completionTime = completionTime?.let { "<VALID>" },
            expirationTime = expirationTime?.let { "<VALID>" },
        )
    }
}

class TestParam

@WorkflowInterface
interface TestWorkflow {
    @WorkflowMethod
    fun run(param: TestParam): GeneralWorkflowExecutionResult
}

@ActivityInterface
interface TestActivity {
    fun run(param: TestParam): GeneralWorkflowExecutionResult
}

class TestWorkflowImpl : TestWorkflow {
    private val activity =
        Workflow.newActivityStub(
            TestActivity::class.java,
            ActivityOptions {
                setRetryOptions(
                    RetryOptions {
                        setMaximumAttempts(1)
                    },
                )
                setScheduleToCloseTimeout(Duration.ofMinutes(5))
            },
        )

    override fun run(param: TestParam) = activity.run(param)
}

class TestActivityImpl(
    private val completion: () -> Future<GeneralWorkflowExecutionResult>,
) : TestActivity {
    override fun run(param: TestParam): GeneralWorkflowExecutionResult = completion().get()
}
