package io.coinmetrics.api.endpoints.txtracker

import io.coinmetrics.api.helper.BaseTest
import io.coinmetrics.api.helper.TEST_API_KEY
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class BtcTxTrackerTest : BaseTest() {
    @Test
    fun `request without txids parameter`() {
        val expectedResponse =
            """{"data":[{"txid":"00000000000000000000000000000000000000000000000000000000000000bb","time":"2021-05-31T14:54:55.000000000Z","first_seen_time":"2021-03-28T07:59:00.000000000Z","status":"CONFIRMED","status_update_time":"2021-03-28T07:59:00.000000000Z","status_updates":[{"time":"2021-03-28T07:59:00.000000000Z","status":"CONFIRMED","block_hash":"00000000839a8e6886ab5951d76f411475428afc90947ee320161bbf18eb6048","height":"1"}],"details":{"amount":"10","version":"1","replace_by_fee_supported":true,"fee":"1777","feerate":"0.2","mempool_feerate_mean_at_first_seen_time":"0.2","mempool_feerate_min_at_first_seen_time":"0.1","consensus_size":"11","physical_size":"12"},"block_hash":"00000000839a8e6886ab5951d76f411475428afc90947ee320161bbf18eb6048","height":"1","n_confirmations":"3","geo":[{"location":"LOCATION_0","seen_time":"2021-03-28T07:59:00.000000000Z"}],"outputs":[{"address":"**********************************"}]},{"txid":"00000000000000000000000000000000000000000000000000000000000000cc","time":"2021-05-31T14:54:55.000000000Z","first_seen_time":"2021-03-28T07:59:00.000000000Z","status":"UNCONFIRMED","status_update_time":"2021-03-28T07:59:00.000000000Z","status_updates":[{"time":"2021-03-28T07:59:00.000000000Z","status":"UNCONFIRMED"}],"details":{"amount":"10","version":"1","replace_by_fee_supported":true,"fee":"1777","feerate":"0.9","mempool_feerate_mean_at_first_seen_time":"0.2","mempool_feerate_min_at_first_seen_time":"0.1","consensus_size":"11","physical_size":"12"},"mempool_approx_queue_position":"66/66","next_block_approx_settlement_probability_pct":"0.01","geo":[{"location":"LOCATION_0","seen_time":"2021-03-28T07:59:00.000000000Z"}],"replacement_for_txid":"00000000000000000000000000000000000000000000000000000000000000aa"},{"txid":"00000000000000000000000000000000000000000000000000000000000000dd","time":"2021-05-31T14:54:55.000000000Z","first_seen_time":"2021-03-28T07:59:00.000000000Z","status":"UNCONFIRMED","status_update_time":"2021-03-28T07:59:00.000000000Z","status_updates":[{"time":"2021-03-28T07:59:00.000000000Z","status":"UNCONFIRMED"}],"details":{"amount":"10","version":"1","replace_by_fee_supported":true,"fee":"1777","feerate":"1.1","mempool_feerate_mean_at_first_seen_time":"0.2","mempool_feerate_min_at_first_seen_time":"0.1","consensus_size":"11","physical_size":"12"},"mempool_approx_queue_position":"60/66","next_block_approx_settlement_probability_pct":"9.09","geo":[{"location":"LOCATION_0","seen_time":"2021-03-28T07:59:00.000000000Z"}]},{"txid":"00000000000000000000000000000000000000000000000000000000000000ee","time":"2021-05-31T14:54:55.000000000Z","first_seen_time":"2021-03-28T07:59:00.000000000Z","status":"REMOVED","status_update_time":"2021-03-28T07:59:00.000000000Z","status_updates":[{"time":"2021-03-28T07:59:00.000000000Z","status":"REMOVED","removal_reason":"REPLACED","replacement_txid":"00000000000000000000000000000000000000000000000000000000000000ff"}],"details":{"amount":"10","version":"1","replace_by_fee_supported":true,"fee":"1777","feerate":"3.1","mempool_feerate_mean_at_first_seen_time":"0.2","mempool_feerate_min_at_first_seen_time":"0.1","consensus_size":"11","physical_size":"12"},"geo":[{"location":"LOCATION_0","seen_time":"2021-03-28T07:59:00.000000000Z"}]},{"txid":"000000000000000000000000000000000000000000000000000000000000bb11","time":"2021-05-31T14:54:55.000000000Z","first_seen_time":"2021-03-28T07:59:00.000000000Z","status":"CONFIRMED","status_update_time":"2021-03-28T07:59:00.000000000Z","status_updates":[{"time":"2021-03-28T07:59:00.000000000Z","status":"CONFIRMED","block_hash":"00000000000000000004dc714bac4b60c159b6eace778d127fa9199e9ffea9a8","height":"10"}],"details":{"amount":"10","version":"1","replace_by_fee_supported":true,"fee":"1777","feerate":"0.2","mempool_feerate_mean_at_first_seen_time":"0.2","mempool_feerate_min_at_first_seen_time":"0.1","consensus_size":"11","physical_size":"12"},"block_hash":"00000000000000000004dc714bac4b60c159b6eace778d127fa9199e9ffea9a8","height":"10","n_confirmations":"1","geo":[]},{"txid":"000000000000000000000000000000000000000000000000000000000000bb22","time":"2021-05-31T14:54:55.000000000Z","first_seen_time":"2021-03-28T07:59:00.000000000Z","status":"CONFIRMED","status_update_time":"2021-03-28T07:59:00.000000000Z","status_updates":[{"time":"2021-03-28T07:59:00.000000000Z","status":"CONFIRMED","block_hash":"000000000000000000045aa49809e529b515d72dcc62a8304aae837aef2dcf01","height":"12"}],"details":{"amount":"10","version":"1","replace_by_fee_supported":true,"fee":"1777","feerate":"0.2","mempool_feerate_mean_at_first_seen_time":"0.2","mempool_feerate_min_at_first_seen_time":"0.1","consensus_size":"11","physical_size":"12"},"block_hash":"000000000000000000045aa49809e529b515d72dcc62a8304aae837aef2dcf01","height":"12","n_confirmations":"1","geo":[]},{"txid":"000000000000000000000000000000000000000000000000000000000000bb33","time":"2021-05-31T14:54:55.000000000Z","first_seen_time":"2021-03-28T07:59:00.000000000Z","status":"UNCONFIRMED","status_update_time":"2021-03-28T07:59:00.000000000Z","status_updates":[{"time":"2021-03-28T07:59:00.000000000Z","status":"UNCONFIRMED"}],"details":{"amount":"10","version":"1","replace_by_fee_supported":true,"fee":"1777","feerate":"1.9","mempool_feerate_mean_at_first_seen_time":"0.2","mempool_feerate_min_at_first_seen_time":"0.1","consensus_size":"11","physical_size":"12"},"mempool_approx_queue_position":"60/66","next_block_approx_settlement_probability_pct":"9.09","geo":[],"inputs":[{"address":"**********************************"},{"address":"**********************************"}],"outputs":[{"address":"**********************************"},{"address":"**********************************"}]},{"txid":"000000000000000000000000000000000000000000000000000000000000bb44","time":"2021-05-31T14:54:55.000000000Z","first_seen_time":"2021-03-28T07:59:00.000000000Z","status":"UNCONFIRMED","status_update_time":"2021-03-28T07:59:00.000000000Z","status_updates":[{"time":"2021-03-28T07:59:00.000000000Z","status":"UNCONFIRMED"}],"details":{"amount":"10","version":"1","replace_by_fee_supported":true,"fee":"1777","feerate":"2.1","mempool_feerate_mean_at_first_seen_time":"0.2","mempool_feerate_min_at_first_seen_time":"0.1","consensus_size":"11","physical_size":"12"},"mempool_approx_queue_position":"44/66","next_block_approx_settlement_probability_pct":"33.33","geo":[]},{"txid":"000000000000000000000000000000000000000000000000000000000000bb55","time":"2021-05-31T14:54:55.000000000Z","first_seen_time":"2021-03-28T07:59:00.000000000Z","status":"REMOVED","status_update_time":"2021-03-28T07:59:00.000000000Z","status_updates":[{"time":"2021-03-28T07:59:00.000000000Z","status":"REMOVED"}],"details":{"amount":"10","version":"1","replace_by_fee_supported":true,"fee":"1777","feerate":"0.2","mempool_feerate_mean_at_first_seen_time":"0.2","mempool_feerate_min_at_first_seen_time":"0.1","consensus_size":"11","physical_size":"12"},"geo":[]},{"txid":"000000000000000000000000000000000000000000000000000000000000bb66","time":"2021-05-31T14:54:55.000000000Z","first_seen_time":"2021-03-28T07:59:00.000000000Z","status":"CONFIRMED","status_update_time":"2021-03-28T07:59:00.000000000Z","status_updates":[{"time":"2021-03-28T07:59:00.000000000Z","status":"CONFIRMED","block_hash":"000000000000000000000000000000000000000000000000000000000000beef","height":"123"}],"details":{"amount":"10","version":"1","replace_by_fee_supported":true,"fee":"1777","feerate":"0.2","mempool_feerate_mean_at_first_seen_time":"0.2","mempool_feerate_min_at_first_seen_time":"0.1","consensus_size":"11","physical_size":"12"},"block_hash":"000000000000000000000000000000000000000000000000000000000000beef","height":"123","n_confirmations":"1","geo":[]},{"txid":"00000000000000000000000000000000000000000000000000000000000000aa","time":"2021-05-31T14:54:55.000000000Z","first_seen_time":"2021-03-29T07:59:00.000000000Z","status":"REMOVED","status_update_time":"2021-03-29T07:59:00.000000000Z","status_updates":[{"time":"2021-03-29T07:59:00.000000000Z","status":"REMOVED","removal_reason":"REPLACED","replacement_txid":"00000000000000000000000000000000000000000000000000000000000000cc"}],"details":{"amount":"10","version":"1","replace_by_fee_supported":true,"fee":"1777","feerate":"0.1","mempool_feerate_mean_at_first_seen_time":"0.2","mempool_feerate_min_at_first_seen_time":"0.1","consensus_size":"11","physical_size":"12"},"geo":[{"location":"LOCATION_1","seen_time":"2021-03-29T07:59:00.000000000Z"},{"location":"LOCATION_0","seen_time":"2021-03-29T08:00:00.000000000Z"}],"inputs":[{"address":"**********************************"}],"outputs":[{"address":"**********************************"}]},{"txid":"00000000000000000000000000000000000000000000000000000000000000ff","time":"2021-05-31T14:54:55.000000000Z","first_seen_time":"2021-03-29T07:59:00.000000000Z","status":"UNCONFIRMED","status_update_time":"2021-03-29T07:59:00.000000000Z","status_updates":[{"time":"2021-03-29T07:59:00.000000000Z","status":"UNCONFIRMED"}],"details":{"amount":"10","version":"1","replace_by_fee_supported":true,"fee":"1777","feerate":"4.1","mempool_feerate_mean_at_first_seen_time":"0.2","mempool_feerate_min_at_first_seen_time":"0.1","consensus_size":"11","physical_size":"12"},"mempool_approx_queue_position":"0/66","next_block_approx_settlement_probability_pct":"99.99","geo":[{"location":"LOCATION_0","seen_time":"2021-03-29T07:59:00.000000000Z"}],"replacement_for_txid":"00000000000000000000000000000000000000000000000000000000000000ee","inputs":[{"address":"**********************************"},{"address":"**********************************"}],"outputs":[{"address":"**********************************"},{"address":"**********************************"}]}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain/btc/transaction-tracker?api_key=$TEST_API_KEY",
            "X-Request-Timestamp",
            "1622472895000",
        )
    }

    @Test
    fun `start_time is set`() {
        val expectedResponse =
            """{"data":[{"txid":"00000000000000000000000000000000000000000000000000000000000000aa","time":"2021-05-31T14:54:55.000000000Z","first_seen_time":"2021-03-29T07:59:00.000000000Z","status":"REMOVED","status_update_time":"2021-03-29T07:59:00.000000000Z","status_updates":[{"time":"2021-03-29T07:59:00.000000000Z","status":"REMOVED","removal_reason":"REPLACED","replacement_txid":"00000000000000000000000000000000000000000000000000000000000000cc"}],"details":{"amount":"10","version":"1","replace_by_fee_supported":true,"fee":"1777","feerate":"0.1","mempool_feerate_mean_at_first_seen_time":"0.2","mempool_feerate_min_at_first_seen_time":"0.1","consensus_size":"11","physical_size":"12"},"geo":[{"location":"LOCATION_1","seen_time":"2021-03-29T07:59:00.000000000Z"},{"location":"LOCATION_0","seen_time":"2021-03-29T08:00:00.000000000Z"}],"inputs":[{"address":"**********************************"}],"outputs":[{"address":"**********************************"}]},{"txid":"00000000000000000000000000000000000000000000000000000000000000ff","time":"2021-05-31T14:54:55.000000000Z","first_seen_time":"2021-03-29T07:59:00.000000000Z","status":"UNCONFIRMED","status_update_time":"2021-03-29T07:59:00.000000000Z","status_updates":[{"time":"2021-03-29T07:59:00.000000000Z","status":"UNCONFIRMED"}],"details":{"amount":"10","version":"1","replace_by_fee_supported":true,"fee":"1777","feerate":"4.1","mempool_feerate_mean_at_first_seen_time":"0.2","mempool_feerate_min_at_first_seen_time":"0.1","consensus_size":"11","physical_size":"12"},"mempool_approx_queue_position":"0/66","next_block_approx_settlement_probability_pct":"99.99","geo":[{"location":"LOCATION_0","seen_time":"2021-03-29T07:59:00.000000000Z"}],"replacement_for_txid":"00000000000000000000000000000000000000000000000000000000000000ee","inputs":[{"address":"**********************************"},{"address":"**********************************"}],"outputs":[{"address":"**********************************"},{"address":"**********************************"}]}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain/btc/transaction-tracker?api_key=$TEST_API_KEY&start_time=2021-03-29",
            "X-Request-Timestamp",
            "1622472895000",
        )
    }

    @Test
    fun `start_time is out of range`() {
        val expectedResponse =
            """{"data":[]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain/btc/transaction-tracker?api_key=$TEST_API_KEY&start_time=2021-03-30",
            "X-Request-Timestamp",
            "1622472895000",
        )
    }

    @Test
    fun `bad api_key`() {
        val expectedResponse =
            """{"error":{"type":"wrong_credentials","message":"Supplied credentials are not valid."}}"""
        assertResponse(
            401,
            expectedResponse,
            "/v4/blockchain/btc/transaction-tracker?api_key=aaa&txids=aaa",
        )
    }

    @Test
    fun `unsupported asset`() {
        val expectedResponse =
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'asset'. Value 'xxx' is not supported."}}"""
        assertResponse(
            400,
            expectedResponse,
            "/v4/blockchain/xxx/transaction-tracker?api_key=$TEST_API_KEY&txids=aaa",
        )
    }

    @Test
    fun `request with txids parameter`() {
        val expectedResponse =
            """{"data":[{"txid":"00000000000000000000000000000000000000000000000000000000000000bb","time":"2021-05-31T14:54:55.000000000Z","first_seen_time":"2021-03-28T07:59:00.000000000Z","status":"CONFIRMED","status_update_time":"2021-03-28T07:59:00.000000000Z","status_updates":[{"time":"2021-03-28T07:59:00.000000000Z","status":"CONFIRMED","block_hash":"00000000839a8e6886ab5951d76f411475428afc90947ee320161bbf18eb6048","height":"1"}],"details":{"amount":"10","version":"1","replace_by_fee_supported":true,"fee":"1777","feerate":"0.2","mempool_feerate_mean_at_first_seen_time":"0.2","mempool_feerate_min_at_first_seen_time":"0.1","consensus_size":"11","physical_size":"12"},"block_hash":"00000000839a8e6886ab5951d76f411475428afc90947ee320161bbf18eb6048","height":"1","n_confirmations":"3","geo":[{"location":"LOCATION_0","seen_time":"2021-03-28T07:59:00.000000000Z"}],"outputs":[{"address":"**********************************"}]},{"txid":"00000000000000000000000000000000000000000000000000000000000000aa","time":"2021-05-31T14:54:55.000000000Z","first_seen_time":"2021-03-29T07:59:00.000000000Z","status":"REMOVED","status_update_time":"2021-03-29T07:59:00.000000000Z","status_updates":[{"time":"2021-03-29T07:59:00.000000000Z","status":"REMOVED","removal_reason":"REPLACED","replacement_txid":"00000000000000000000000000000000000000000000000000000000000000cc"}],"details":{"amount":"10","version":"1","replace_by_fee_supported":true,"fee":"1777","feerate":"0.1","mempool_feerate_mean_at_first_seen_time":"0.2","mempool_feerate_min_at_first_seen_time":"0.1","consensus_size":"11","physical_size":"12"},"geo":[{"location":"LOCATION_1","seen_time":"2021-03-29T07:59:00.000000000Z"},{"location":"LOCATION_0","seen_time":"2021-03-29T08:00:00.000000000Z"}],"inputs":[{"address":"**********************************"}],"outputs":[{"address":"**********************************"}]}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain/btc/transaction-tracker?api_key=$TEST_API_KEY&txids=00000000000000000000000000000000000000000000000000000000000000bb,00000000000000000000000000000000000000000000000000000000000000aa",
            "X-Request-Timestamp",
            "1622472895000",
        )
    }

    @Test
    fun confirmed() {
        val expectedResponse =
            """{"data":[{"txid":"00000000000000000000000000000000000000000000000000000000000000bb","time":"2021-05-31T14:54:55.000000000Z","first_seen_time":"2021-03-28T07:59:00.000000000Z","status":"CONFIRMED","status_update_time":"2021-03-28T07:59:00.000000000Z","status_updates":[{"time":"2021-03-28T07:59:00.000000000Z","status":"CONFIRMED","block_hash":"00000000839a8e6886ab5951d76f411475428afc90947ee320161bbf18eb6048","height":"1"}],"details":{"amount":"10","version":"1","replace_by_fee_supported":true,"fee":"1777","feerate":"0.2","mempool_feerate_mean_at_first_seen_time":"0.2","mempool_feerate_min_at_first_seen_time":"0.1","consensus_size":"11","physical_size":"12"},"block_hash":"00000000839a8e6886ab5951d76f411475428afc90947ee320161bbf18eb6048","height":"1","n_confirmations":"3","geo":[{"location":"LOCATION_0","seen_time":"2021-03-28T07:59:00.000000000Z"}],"outputs":[{"address":"**********************************"}]}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain/btc/transaction-tracker?api_key=$TEST_API_KEY&txids=00000000000000000000000000000000000000000000000000000000000000bb",
            "X-Request-Timestamp",
            "1622472895000",
        )
    }

    @Test
    fun `incorrect hex txid`() {
        val expectedResponse =
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'txids'. Invalid format of txid 'd'."}}"""
        assertResponse(
            400,
            expectedResponse,
            "/v4/blockchain/btc/transaction-tracker?api_key=$TEST_API_KEY&txids=d",
        )
    }

    @Test
    fun `page 1`() {
        val expectedResponse =
            """{"data":[{"txid":"00000000000000000000000000000000000000000000000000000000000000aa","time":"2021-05-31T14:54:55.000000000Z","first_seen_time":"2021-03-29T07:59:00.000000000Z","status":"REMOVED","status_update_time":"2021-03-29T07:59:00.000000000Z","status_updates":[{"time":"2021-03-29T07:59:00.000000000Z","status":"REMOVED","removal_reason":"REPLACED","replacement_txid":"00000000000000000000000000000000000000000000000000000000000000cc"}],"details":{"amount":"10","version":"1","replace_by_fee_supported":true,"fee":"1777","feerate":"0.1","mempool_feerate_mean_at_first_seen_time":"0.2","mempool_feerate_min_at_first_seen_time":"0.1","consensus_size":"11","physical_size":"12"},"geo":[{"location":"LOCATION_1","seen_time":"2021-03-29T07:59:00.000000000Z"},{"location":"LOCATION_0","seen_time":"2021-03-29T08:00:00.000000000Z"}],"inputs":[{"address":"**********************************"}],"outputs":[{"address":"**********************************"}]},{"txid":"00000000000000000000000000000000000000000000000000000000000000ff","time":"2021-05-31T14:54:55.000000000Z","first_seen_time":"2021-03-29T07:59:00.000000000Z","status":"UNCONFIRMED","status_update_time":"2021-03-29T07:59:00.000000000Z","status_updates":[{"time":"2021-03-29T07:59:00.000000000Z","status":"UNCONFIRMED"}],"details":{"amount":"10","version":"1","replace_by_fee_supported":true,"fee":"1777","feerate":"4.1","mempool_feerate_mean_at_first_seen_time":"0.2","mempool_feerate_min_at_first_seen_time":"0.1","consensus_size":"11","physical_size":"12"},"mempool_approx_queue_position":"0/66","next_block_approx_settlement_probability_pct":"99.99","geo":[{"location":"LOCATION_0","seen_time":"2021-03-29T07:59:00.000000000Z"}],"replacement_for_txid":"00000000000000000000000000000000000000000000000000000000000000ee","inputs":[{"address":"**********************************"},{"address":"**********************************"}],"outputs":[{"address":"**********************************"},{"address":"**********************************"}]}],"next_page_token":"MjAyMS0wMy0yOVQwNzo1OTowMFp8QUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFLbw","next_page_url":"http://127.0.0.1:8080/v4/blockchain/btc/transaction-tracker?api_key=x1&page_size=2&next_page_token=MjAyMS0wMy0yOVQwNzo1OTowMFp8QUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFLbw"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain/btc/transaction-tracker?api_key=$TEST_API_KEY&page_size=2",
            "X-Request-Timestamp",
            "1622472895000",
        )
    }

    @Test
    fun `page 2`() {
        val expectedResponse =
            """{"data":[{"txid":"000000000000000000000000000000000000000000000000000000000000bb55","time":"2021-05-31T14:54:55.000000000Z","first_seen_time":"2021-03-28T07:59:00.000000000Z","status":"REMOVED","status_update_time":"2021-03-28T07:59:00.000000000Z","status_updates":[{"time":"2021-03-28T07:59:00.000000000Z","status":"REMOVED"}],"details":{"amount":"10","version":"1","replace_by_fee_supported":true,"fee":"1777","feerate":"0.2","mempool_feerate_mean_at_first_seen_time":"0.2","mempool_feerate_min_at_first_seen_time":"0.1","consensus_size":"11","physical_size":"12"},"geo":[]},{"txid":"000000000000000000000000000000000000000000000000000000000000bb66","time":"2021-05-31T14:54:55.000000000Z","first_seen_time":"2021-03-28T07:59:00.000000000Z","status":"CONFIRMED","status_update_time":"2021-03-28T07:59:00.000000000Z","status_updates":[{"time":"2021-03-28T07:59:00.000000000Z","status":"CONFIRMED","block_hash":"000000000000000000000000000000000000000000000000000000000000beef","height":"123"}],"details":{"amount":"10","version":"1","replace_by_fee_supported":true,"fee":"1777","feerate":"0.2","mempool_feerate_mean_at_first_seen_time":"0.2","mempool_feerate_min_at_first_seen_time":"0.1","consensus_size":"11","physical_size":"12"},"block_hash":"000000000000000000000000000000000000000000000000000000000000beef","height":"123","n_confirmations":"1","geo":[]}],"next_page_token":"MjAyMS0wMy0yOFQwNzo1OTowMFp8QUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQXUxVQ","next_page_url":"http://127.0.0.1:8080/v4/blockchain/btc/transaction-tracker?api_key=x1&page_size=2&next_page_token=MjAyMS0wMy0yOFQwNzo1OTowMFp8QUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQXUxVQ"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain/btc/transaction-tracker?api_key=$TEST_API_KEY&page_size=2&next_page_token=MjAyMS0wMy0yOVQwNzo1OTowMFp8QUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFLbw",
            "X-Request-Timestamp",
            "1622472895000",
        )
    }

    @Test
    fun `page 3`() {
        val expectedResponse =
            """{"data":[{"txid":"000000000000000000000000000000000000000000000000000000000000bb22","time":"2021-05-31T14:54:55.000000000Z","first_seen_time":"2021-03-28T07:59:00.000000000Z","status":"CONFIRMED","status_update_time":"2021-03-28T07:59:00.000000000Z","status_updates":[{"time":"2021-03-28T07:59:00.000000000Z","status":"CONFIRMED","block_hash":"000000000000000000045aa49809e529b515d72dcc62a8304aae837aef2dcf01","height":"12"}],"details":{"amount":"10","version":"1","replace_by_fee_supported":true,"fee":"1777","feerate":"0.2","mempool_feerate_mean_at_first_seen_time":"0.2","mempool_feerate_min_at_first_seen_time":"0.1","consensus_size":"11","physical_size":"12"},"block_hash":"000000000000000000045aa49809e529b515d72dcc62a8304aae837aef2dcf01","height":"12","n_confirmations":"1","geo":[]},{"txid":"000000000000000000000000000000000000000000000000000000000000bb33","time":"2021-05-31T14:54:55.000000000Z","first_seen_time":"2021-03-28T07:59:00.000000000Z","status":"UNCONFIRMED","status_update_time":"2021-03-28T07:59:00.000000000Z","status_updates":[{"time":"2021-03-28T07:59:00.000000000Z","status":"UNCONFIRMED"}],"details":{"amount":"10","version":"1","replace_by_fee_supported":true,"fee":"1777","feerate":"1.9","mempool_feerate_mean_at_first_seen_time":"0.2","mempool_feerate_min_at_first_seen_time":"0.1","consensus_size":"11","physical_size":"12"},"mempool_approx_queue_position":"60/66","next_block_approx_settlement_probability_pct":"9.09","geo":[],"inputs":[{"address":"**********************************"},{"address":"**********************************"}],"outputs":[{"address":"**********************************"},{"address":"**********************************"}]}],"next_page_token":"MjAyMS0wMy0yOFQwNzo1OTowMFp8QUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQXV5SQ","next_page_url":"http://127.0.0.1:8080/v4/blockchain/btc/transaction-tracker?api_key=x1&page_size=2&next_page_token=MjAyMS0wMy0yOFQwNzo1OTowMFp8QUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQXV5SQ"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain/btc/transaction-tracker?api_key=$TEST_API_KEY&page_size=2&next_page_token=MjAyMS0wMy0yOFQwNzo1OTowMFp8QUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQXUwUQ",
            "X-Request-Timestamp",
            "1622472895000",
        )
    }

    @Test
    fun `request with replacements_for_txids and txids parameters`() {
        val expectedResponse =
            """{"error":{"type":"bad_parameters","message":"Filters 'replacements_for_txids' and 'txids' are mutually exclusive and can't be specified in the same request."}}"""
        assertResponse(
            400,
            expectedResponse,
            "/v4/blockchain/btc/transaction-tracker?api_key=$TEST_API_KEY&txids=00000000000000000000000000000000000000000000000000000000000000bb,00000000000000000000000000000000000000000000000000000000000000aa&replacements_for_txids=00000000000000000000000000000000000000000000000000000000000000bb,00000000000000000000000000000000000000000000000000000000000000aa",
            "X-Request-Timestamp",
            "1622472895000",
        )
    }

    @Test
    fun `request with replacements_for_txids parameter`() {
        val expectedResponse =
            """{"data":[{"txid":"00000000000000000000000000000000000000000000000000000000000000cc","time":"2021-05-31T14:54:55.000000000Z","first_seen_time":"2021-03-28T07:59:00.000000000Z","status":"UNCONFIRMED","status_update_time":"2021-03-28T07:59:00.000000000Z","status_updates":[{"time":"2021-03-28T07:59:00.000000000Z","status":"UNCONFIRMED"}],"details":{"amount":"10","version":"1","replace_by_fee_supported":true,"fee":"1777","feerate":"0.9","mempool_feerate_mean_at_first_seen_time":"0.2","mempool_feerate_min_at_first_seen_time":"0.1","consensus_size":"11","physical_size":"12"},"mempool_approx_queue_position":"66/66","next_block_approx_settlement_probability_pct":"0.01","geo":[{"location":"LOCATION_0","seen_time":"2021-03-28T07:59:00.000000000Z"}],"replacement_for_txid":"00000000000000000000000000000000000000000000000000000000000000aa"},{"txid":"00000000000000000000000000000000000000000000000000000000000000ff","time":"2021-05-31T14:54:55.000000000Z","first_seen_time":"2021-03-29T07:59:00.000000000Z","status":"UNCONFIRMED","status_update_time":"2021-03-29T07:59:00.000000000Z","status_updates":[{"time":"2021-03-29T07:59:00.000000000Z","status":"UNCONFIRMED"}],"details":{"amount":"10","version":"1","replace_by_fee_supported":true,"fee":"1777","feerate":"4.1","mempool_feerate_mean_at_first_seen_time":"0.2","mempool_feerate_min_at_first_seen_time":"0.1","consensus_size":"11","physical_size":"12"},"mempool_approx_queue_position":"0/66","next_block_approx_settlement_probability_pct":"99.99","geo":[{"location":"LOCATION_0","seen_time":"2021-03-29T07:59:00.000000000Z"}],"replacement_for_txid":"00000000000000000000000000000000000000000000000000000000000000ee","inputs":[{"address":"**********************************"},{"address":"**********************************"}],"outputs":[{"address":"**********************************"},{"address":"**********************************"}]}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain/btc/transaction-tracker?api_key=$TEST_API_KEY&replacements_for_txids=00000000000000000000000000000000000000000000000000000000000000bb,00000000000000000000000000000000000000000000000000000000000000aa,00000000000000000000000000000000000000000000000000000000000000ee",
            "X-Request-Timestamp",
            "1622472895000",
        )
    }

    @Test
    fun `request with replacements_only parameter`() {
        val expectedResponse =
            """{"data":[{"txid":"00000000000000000000000000000000000000000000000000000000000000cc","time":"2021-05-31T14:54:55.000000000Z","first_seen_time":"2021-03-28T07:59:00.000000000Z","status":"UNCONFIRMED","status_update_time":"2021-03-28T07:59:00.000000000Z","status_updates":[{"time":"2021-03-28T07:59:00.000000000Z","status":"UNCONFIRMED"}],"details":{"amount":"10","version":"1","replace_by_fee_supported":true,"fee":"1777","feerate":"0.9","mempool_feerate_mean_at_first_seen_time":"0.2","mempool_feerate_min_at_first_seen_time":"0.1","consensus_size":"11","physical_size":"12"},"mempool_approx_queue_position":"66/66","next_block_approx_settlement_probability_pct":"0.01","geo":[{"location":"LOCATION_0","seen_time":"2021-03-28T07:59:00.000000000Z"}],"replacement_for_txid":"00000000000000000000000000000000000000000000000000000000000000aa"},{"txid":"00000000000000000000000000000000000000000000000000000000000000ff","time":"2021-05-31T14:54:55.000000000Z","first_seen_time":"2021-03-29T07:59:00.000000000Z","status":"UNCONFIRMED","status_update_time":"2021-03-29T07:59:00.000000000Z","status_updates":[{"time":"2021-03-29T07:59:00.000000000Z","status":"UNCONFIRMED"}],"details":{"amount":"10","version":"1","replace_by_fee_supported":true,"fee":"1777","feerate":"4.1","mempool_feerate_mean_at_first_seen_time":"0.2","mempool_feerate_min_at_first_seen_time":"0.1","consensus_size":"11","physical_size":"12"},"mempool_approx_queue_position":"0/66","next_block_approx_settlement_probability_pct":"99.99","geo":[{"location":"LOCATION_0","seen_time":"2021-03-29T07:59:00.000000000Z"}],"replacement_for_txid":"00000000000000000000000000000000000000000000000000000000000000ee","inputs":[{"address":"**********************************"},{"address":"**********************************"}],"outputs":[{"address":"**********************************"},{"address":"**********************************"}]}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain/btc/transaction-tracker?api_key=$TEST_API_KEY&replacements_only=true",
            "X-Request-Timestamp",
            "1622472895000",
        )
    }

    @Test
    fun `request with txids and replacements_only parameters`() {
        val expectedResponse =
            """{"data":[{"txid":"00000000000000000000000000000000000000000000000000000000000000cc","time":"2021-05-31T14:54:55.000000000Z","first_seen_time":"2021-03-28T07:59:00.000000000Z","status":"UNCONFIRMED","status_update_time":"2021-03-28T07:59:00.000000000Z","status_updates":[{"time":"2021-03-28T07:59:00.000000000Z","status":"UNCONFIRMED"}],"details":{"amount":"10","version":"1","replace_by_fee_supported":true,"fee":"1777","feerate":"0.9","mempool_feerate_mean_at_first_seen_time":"0.2","mempool_feerate_min_at_first_seen_time":"0.1","consensus_size":"11","physical_size":"12"},"mempool_approx_queue_position":"66/66","next_block_approx_settlement_probability_pct":"0.01","geo":[{"location":"LOCATION_0","seen_time":"2021-03-28T07:59:00.000000000Z"}],"replacement_for_txid":"00000000000000000000000000000000000000000000000000000000000000aa"},{"txid":"00000000000000000000000000000000000000000000000000000000000000ff","time":"2021-05-31T14:54:55.000000000Z","first_seen_time":"2021-03-29T07:59:00.000000000Z","status":"UNCONFIRMED","status_update_time":"2021-03-29T07:59:00.000000000Z","status_updates":[{"time":"2021-03-29T07:59:00.000000000Z","status":"UNCONFIRMED"}],"details":{"amount":"10","version":"1","replace_by_fee_supported":true,"fee":"1777","feerate":"4.1","mempool_feerate_mean_at_first_seen_time":"0.2","mempool_feerate_min_at_first_seen_time":"0.1","consensus_size":"11","physical_size":"12"},"mempool_approx_queue_position":"0/66","next_block_approx_settlement_probability_pct":"99.99","geo":[{"location":"LOCATION_0","seen_time":"2021-03-29T07:59:00.000000000Z"}],"replacement_for_txid":"00000000000000000000000000000000000000000000000000000000000000ee","inputs":[{"address":"**********************************"},{"address":"**********************************"}],"outputs":[{"address":"**********************************"},{"address":"**********************************"}]}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain/btc/transaction-tracker?api_key=$TEST_API_KEY&txids=00000000000000000000000000000000000000000000000000000000000000bb,00000000000000000000000000000000000000000000000000000000000000cc,00000000000000000000000000000000000000000000000000000000000000ff&replacements_only=true",
            "X-Request-Timestamp",
            "1622472895000",
        )
    }

    @Test
    fun `request with addresses parameter and single address`() {
        val expectedResponse =
            """{"data":[{"txid":"000000000000000000000000000000000000000000000000000000000000bb33","time":"2021-05-31T14:54:55.000000000Z","first_seen_time":"2021-03-28T07:59:00.000000000Z","status":"UNCONFIRMED","status_update_time":"2021-03-28T07:59:00.000000000Z","status_updates":[{"time":"2021-03-28T07:59:00.000000000Z","status":"UNCONFIRMED"}],"details":{"amount":"10","version":"1","replace_by_fee_supported":true,"fee":"1777","feerate":"1.9","mempool_feerate_mean_at_first_seen_time":"0.2","mempool_feerate_min_at_first_seen_time":"0.1","consensus_size":"11","physical_size":"12"},"mempool_approx_queue_position":"60/66","next_block_approx_settlement_probability_pct":"9.09","geo":[],"inputs":[{"address":"**********************************"},{"address":"**********************************"}],"outputs":[{"address":"**********************************"},{"address":"**********************************"}]},{"txid":"00000000000000000000000000000000000000000000000000000000000000ff","time":"2021-05-31T14:54:55.000000000Z","first_seen_time":"2021-03-29T07:59:00.000000000Z","status":"UNCONFIRMED","status_update_time":"2021-03-29T07:59:00.000000000Z","status_updates":[{"time":"2021-03-29T07:59:00.000000000Z","status":"UNCONFIRMED"}],"details":{"amount":"10","version":"1","replace_by_fee_supported":true,"fee":"1777","feerate":"4.1","mempool_feerate_mean_at_first_seen_time":"0.2","mempool_feerate_min_at_first_seen_time":"0.1","consensus_size":"11","physical_size":"12"},"mempool_approx_queue_position":"0/66","next_block_approx_settlement_probability_pct":"99.99","geo":[{"location":"LOCATION_0","seen_time":"2021-03-29T07:59:00.000000000Z"}],"replacement_for_txid":"00000000000000000000000000000000000000000000000000000000000000ee","inputs":[{"address":"**********************************"},{"address":"**********************************"}],"outputs":[{"address":"**********************************"},{"address":"**********************************"}]}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain/btc/transaction-tracker?api_key=$TEST_API_KEY&addresses=**********************************",
            "X-Request-Timestamp",
            "1622472895000",
        )
    }

    @Test
    fun `request with addresses parameter and multiple addresses`() {
        val expectedResponse =
            """{"data":[{"txid":"000000000000000000000000000000000000000000000000000000000000bb33","time":"2021-05-31T14:54:55.000000000Z","first_seen_time":"2021-03-28T07:59:00.000000000Z","status":"UNCONFIRMED","status_update_time":"2021-03-28T07:59:00.000000000Z","status_updates":[{"time":"2021-03-28T07:59:00.000000000Z","status":"UNCONFIRMED"}],"details":{"amount":"10","version":"1","replace_by_fee_supported":true,"fee":"1777","feerate":"1.9","mempool_feerate_mean_at_first_seen_time":"0.2","mempool_feerate_min_at_first_seen_time":"0.1","consensus_size":"11","physical_size":"12"},"mempool_approx_queue_position":"60/66","next_block_approx_settlement_probability_pct":"9.09","geo":[],"inputs":[{"address":"**********************************"},{"address":"**********************************"}],"outputs":[{"address":"**********************************"},{"address":"**********************************"}]},{"txid":"00000000000000000000000000000000000000000000000000000000000000ff","time":"2021-05-31T14:54:55.000000000Z","first_seen_time":"2021-03-29T07:59:00.000000000Z","status":"UNCONFIRMED","status_update_time":"2021-03-29T07:59:00.000000000Z","status_updates":[{"time":"2021-03-29T07:59:00.000000000Z","status":"UNCONFIRMED"}],"details":{"amount":"10","version":"1","replace_by_fee_supported":true,"fee":"1777","feerate":"4.1","mempool_feerate_mean_at_first_seen_time":"0.2","mempool_feerate_min_at_first_seen_time":"0.1","consensus_size":"11","physical_size":"12"},"mempool_approx_queue_position":"0/66","next_block_approx_settlement_probability_pct":"99.99","geo":[{"location":"LOCATION_0","seen_time":"2021-03-29T07:59:00.000000000Z"}],"replacement_for_txid":"00000000000000000000000000000000000000000000000000000000000000ee","inputs":[{"address":"**********************************"},{"address":"**********************************"}],"outputs":[{"address":"**********************************"},{"address":"**********************************"}]}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain/btc/transaction-tracker?api_key=$TEST_API_KEY&addresses=**********************************,**********************************",
            "X-Request-Timestamp",
            "1622472895000",
        )
    }

    @Test
    fun `request with addresses parameter and unconfirmed_only=false`() {
        val expectedResponse =
            """{"data":[{"txid":"00000000000000000000000000000000000000000000000000000000000000bb","time":"2021-05-31T14:54:55.000000000Z","first_seen_time":"2021-03-28T07:59:00.000000000Z","status":"CONFIRMED","status_update_time":"2021-03-28T07:59:00.000000000Z","status_updates":[{"time":"2021-03-28T07:59:00.000000000Z","status":"CONFIRMED","block_hash":"00000000839a8e6886ab5951d76f411475428afc90947ee320161bbf18eb6048","height":"1"}],"details":{"amount":"10","version":"1","replace_by_fee_supported":true,"fee":"1777","feerate":"0.2","mempool_feerate_mean_at_first_seen_time":"0.2","mempool_feerate_min_at_first_seen_time":"0.1","consensus_size":"11","physical_size":"12"},"block_hash":"00000000839a8e6886ab5951d76f411475428afc90947ee320161bbf18eb6048","height":"1","n_confirmations":"3","geo":[{"location":"LOCATION_0","seen_time":"2021-03-28T07:59:00.000000000Z"}],"outputs":[{"address":"**********************************"}]},{"txid":"000000000000000000000000000000000000000000000000000000000000bb33","time":"2021-05-31T14:54:55.000000000Z","first_seen_time":"2021-03-28T07:59:00.000000000Z","status":"UNCONFIRMED","status_update_time":"2021-03-28T07:59:00.000000000Z","status_updates":[{"time":"2021-03-28T07:59:00.000000000Z","status":"UNCONFIRMED"}],"details":{"amount":"10","version":"1","replace_by_fee_supported":true,"fee":"1777","feerate":"1.9","mempool_feerate_mean_at_first_seen_time":"0.2","mempool_feerate_min_at_first_seen_time":"0.1","consensus_size":"11","physical_size":"12"},"mempool_approx_queue_position":"60/66","next_block_approx_settlement_probability_pct":"9.09","geo":[],"inputs":[{"address":"**********************************"},{"address":"**********************************"}],"outputs":[{"address":"**********************************"},{"address":"**********************************"}]}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain/btc/transaction-tracker?api_key=$TEST_API_KEY&addresses=**********************************&unconfirmed_only=false",
            "X-Request-Timestamp",
            "1622472895000",
        )
    }

    @Test
    fun `request with addresses parameter and unconfirmed_only=false and page_size=2`() {
        val expectedPage1 =
            """{"data":[{"txid":"00000000000000000000000000000000000000000000000000000000000000aa","time":"2021-05-31T14:54:55.000000000Z","first_seen_time":"2021-03-29T07:59:00.000000000Z","status":"REMOVED","status_update_time":"2021-03-29T07:59:00.000000000Z","status_updates":[{"time":"2021-03-29T07:59:00.000000000Z","status":"REMOVED","removal_reason":"REPLACED","replacement_txid":"00000000000000000000000000000000000000000000000000000000000000cc"}],"details":{"amount":"10","version":"1","replace_by_fee_supported":true,"fee":"1777","feerate":"0.1","mempool_feerate_mean_at_first_seen_time":"0.2","mempool_feerate_min_at_first_seen_time":"0.1","consensus_size":"11","physical_size":"12"},"geo":[{"location":"LOCATION_1","seen_time":"2021-03-29T07:59:00.000000000Z"},{"location":"LOCATION_0","seen_time":"2021-03-29T08:00:00.000000000Z"}],"inputs":[{"address":"**********************************"}],"outputs":[{"address":"**********************************"}]},{"txid":"00000000000000000000000000000000000000000000000000000000000000ff","time":"2021-05-31T14:54:55.000000000Z","first_seen_time":"2021-03-29T07:59:00.000000000Z","status":"UNCONFIRMED","status_update_time":"2021-03-29T07:59:00.000000000Z","status_updates":[{"time":"2021-03-29T07:59:00.000000000Z","status":"UNCONFIRMED"}],"details":{"amount":"10","version":"1","replace_by_fee_supported":true,"fee":"1777","feerate":"4.1","mempool_feerate_mean_at_first_seen_time":"0.2","mempool_feerate_min_at_first_seen_time":"0.1","consensus_size":"11","physical_size":"12"},"mempool_approx_queue_position":"0/66","next_block_approx_settlement_probability_pct":"99.99","geo":[{"location":"LOCATION_0","seen_time":"2021-03-29T07:59:00.000000000Z"}],"replacement_for_txid":"00000000000000000000000000000000000000000000000000000000000000ee","inputs":[{"address":"**********************************"},{"address":"**********************************"}],"outputs":[{"address":"**********************************"},{"address":"**********************************"}]}],"next_page_token":"MjAyMS0wMy0yOVQwNzo1OTowMFp8QUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFLbw","next_page_url":"http://127.0.0.1:8080/v4/blockchain/btc/transaction-tracker?api_key=x1&addresses=**********************************,**********************************&unconfirmed_only=false&page_size=2&next_page_token=MjAyMS0wMy0yOVQwNzo1OTowMFp8QUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFLbw"}"""
        assertResponse(
            200,
            expectedPage1,
            "/v4/blockchain/btc/transaction-tracker?api_key=$TEST_API_KEY&addresses=**********************************,**********************************&unconfirmed_only=false&page_size=2",
            "X-Request-Timestamp",
            "1622472895000",
        )

        val expectedPage2 =
            """{"data":[{"txid":"00000000000000000000000000000000000000000000000000000000000000bb","time":"2021-05-31T14:54:56.000000000Z","first_seen_time":"2021-03-28T07:59:00.000000000Z","status":"CONFIRMED","status_update_time":"2021-03-28T07:59:00.000000000Z","status_updates":[{"time":"2021-03-28T07:59:00.000000000Z","status":"CONFIRMED","block_hash":"00000000839a8e6886ab5951d76f411475428afc90947ee320161bbf18eb6048","height":"1"}],"details":{"amount":"10","version":"1","replace_by_fee_supported":true,"fee":"1777","feerate":"0.2","mempool_feerate_mean_at_first_seen_time":"0.2","mempool_feerate_min_at_first_seen_time":"0.1","consensus_size":"11","physical_size":"12"},"block_hash":"00000000839a8e6886ab5951d76f411475428afc90947ee320161bbf18eb6048","height":"1","n_confirmations":"3","geo":[{"location":"LOCATION_0","seen_time":"2021-03-28T07:59:00.000000000Z"}],"outputs":[{"address":"**********************************"}]},{"txid":"000000000000000000000000000000000000000000000000000000000000bb33","time":"2021-05-31T14:54:56.000000000Z","first_seen_time":"2021-03-28T07:59:00.000000000Z","status":"UNCONFIRMED","status_update_time":"2021-03-28T07:59:00.000000000Z","status_updates":[{"time":"2021-03-28T07:59:00.000000000Z","status":"UNCONFIRMED"}],"details":{"amount":"10","version":"1","replace_by_fee_supported":true,"fee":"1777","feerate":"1.9","mempool_feerate_mean_at_first_seen_time":"0.2","mempool_feerate_min_at_first_seen_time":"0.1","consensus_size":"11","physical_size":"12"},"mempool_approx_queue_position":"60/66","next_block_approx_settlement_probability_pct":"9.09","geo":[],"inputs":[{"address":"**********************************"},{"address":"**********************************"}],"outputs":[{"address":"**********************************"},{"address":"**********************************"}]}]}"""
        assertResponse(
            200,
            expectedPage2,
            "/v4/blockchain/btc/transaction-tracker?api_key=$TEST_API_KEY&addresses=**********************************,**********************************&unconfirmed_only=false&page_size=2&next_page_token=MjAyMS0wMy0yOVQwNzo1OTowMFp8QUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFLbw",
            "X-Request-Timestamp",
            "1622472896000",
        )
    }

    @Test
    fun `request with addresses parameter and unconfirmed_only=false and page_size=2 and paging_from=start`() {
        val expectedPage1 =
            """{"data":[{"txid":"00000000000000000000000000000000000000000000000000000000000000bb","time":"2021-05-31T14:54:55.000000000Z","first_seen_time":"2021-03-28T07:59:00.000000000Z","status":"CONFIRMED","status_update_time":"2021-03-28T07:59:00.000000000Z","status_updates":[{"time":"2021-03-28T07:59:00.000000000Z","status":"CONFIRMED","block_hash":"00000000839a8e6886ab5951d76f411475428afc90947ee320161bbf18eb6048","height":"1"}],"details":{"amount":"10","version":"1","replace_by_fee_supported":true,"fee":"1777","feerate":"0.2","mempool_feerate_mean_at_first_seen_time":"0.2","mempool_feerate_min_at_first_seen_time":"0.1","consensus_size":"11","physical_size":"12"},"block_hash":"00000000839a8e6886ab5951d76f411475428afc90947ee320161bbf18eb6048","height":"1","n_confirmations":"3","geo":[{"location":"LOCATION_0","seen_time":"2021-03-28T07:59:00.000000000Z"}],"outputs":[{"address":"**********************************"}]},{"txid":"000000000000000000000000000000000000000000000000000000000000bb33","time":"2021-05-31T14:54:55.000000000Z","first_seen_time":"2021-03-28T07:59:00.000000000Z","status":"UNCONFIRMED","status_update_time":"2021-03-28T07:59:00.000000000Z","status_updates":[{"time":"2021-03-28T07:59:00.000000000Z","status":"UNCONFIRMED"}],"details":{"amount":"10","version":"1","replace_by_fee_supported":true,"fee":"1777","feerate":"1.9","mempool_feerate_mean_at_first_seen_time":"0.2","mempool_feerate_min_at_first_seen_time":"0.1","consensus_size":"11","physical_size":"12"},"mempool_approx_queue_position":"60/66","next_block_approx_settlement_probability_pct":"9.09","geo":[],"inputs":[{"address":"**********************************"},{"address":"**********************************"}],"outputs":[{"address":"**********************************"},{"address":"**********************************"}]}],"next_page_token":"MjAyMS0wMy0yOFQwNzo1OTowMFp8QUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQXV6TQ","next_page_url":"http://127.0.0.1:8080/v4/blockchain/btc/transaction-tracker?api_key=x1&addresses=**********************************,**********************************&unconfirmed_only=false&page_size=2&paging_from=start&next_page_token=MjAyMS0wMy0yOFQwNzo1OTowMFp8QUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQXV6TQ"}"""
        assertResponse(
            200,
            expectedPage1,
            "/v4/blockchain/btc/transaction-tracker?api_key=$TEST_API_KEY&addresses=**********************************,**********************************&unconfirmed_only=false&page_size=2&paging_from=start",
            "X-Request-Timestamp",
            "1622472895000",
        )

        val expectedPage2 =
            """{"data":[{"txid":"00000000000000000000000000000000000000000000000000000000000000aa","time":"2021-05-31T14:54:56.000000000Z","first_seen_time":"2021-03-29T07:59:00.000000000Z","status":"REMOVED","status_update_time":"2021-03-29T07:59:00.000000000Z","status_updates":[{"time":"2021-03-29T07:59:00.000000000Z","status":"REMOVED","removal_reason":"REPLACED","replacement_txid":"00000000000000000000000000000000000000000000000000000000000000cc"}],"details":{"amount":"10","version":"1","replace_by_fee_supported":true,"fee":"1777","feerate":"0.1","mempool_feerate_mean_at_first_seen_time":"0.2","mempool_feerate_min_at_first_seen_time":"0.1","consensus_size":"11","physical_size":"12"},"geo":[{"location":"LOCATION_1","seen_time":"2021-03-29T07:59:00.000000000Z"},{"location":"LOCATION_0","seen_time":"2021-03-29T08:00:00.000000000Z"}],"inputs":[{"address":"**********************************"}],"outputs":[{"address":"**********************************"}]},{"txid":"00000000000000000000000000000000000000000000000000000000000000ff","time":"2021-05-31T14:54:56.000000000Z","first_seen_time":"2021-03-29T07:59:00.000000000Z","status":"UNCONFIRMED","status_update_time":"2021-03-29T07:59:00.000000000Z","status_updates":[{"time":"2021-03-29T07:59:00.000000000Z","status":"UNCONFIRMED"}],"details":{"amount":"10","version":"1","replace_by_fee_supported":true,"fee":"1777","feerate":"4.1","mempool_feerate_mean_at_first_seen_time":"0.2","mempool_feerate_min_at_first_seen_time":"0.1","consensus_size":"11","physical_size":"12"},"mempool_approx_queue_position":"0/66","next_block_approx_settlement_probability_pct":"99.99","geo":[{"location":"LOCATION_0","seen_time":"2021-03-29T07:59:00.000000000Z"}],"replacement_for_txid":"00000000000000000000000000000000000000000000000000000000000000ee","inputs":[{"address":"**********************************"},{"address":"**********************************"}],"outputs":[{"address":"**********************************"},{"address":"**********************************"}]}]}"""
        assertResponse(
            200,
            expectedPage2,
            "/v4/blockchain/btc/transaction-tracker?api_key=$TEST_API_KEY&addresses=**********************************,**********************************&unconfirmed_only=false&page_size=2&paging_from=start&next_page_token=MjAyMS0wMy0yOFQwNzo1OTowMFp8QUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQXV6TQ",
            "X-Request-Timestamp",
            "1622472896000",
        )
    }

    @Test
    fun `request with addresses parameter and unconfirmed_only=false and start_time`() {
        val expectedResponse =
            """{"data":[{"txid":"00000000000000000000000000000000000000000000000000000000000000aa","time":"2021-05-31T14:54:55.000000000Z","first_seen_time":"2021-03-29T07:59:00.000000000Z","status":"REMOVED","status_update_time":"2021-03-29T07:59:00.000000000Z","status_updates":[{"time":"2021-03-29T07:59:00.000000000Z","status":"REMOVED","removal_reason":"REPLACED","replacement_txid":"00000000000000000000000000000000000000000000000000000000000000cc"}],"details":{"amount":"10","version":"1","replace_by_fee_supported":true,"fee":"1777","feerate":"0.1","mempool_feerate_mean_at_first_seen_time":"0.2","mempool_feerate_min_at_first_seen_time":"0.1","consensus_size":"11","physical_size":"12"},"geo":[{"location":"LOCATION_1","seen_time":"2021-03-29T07:59:00.000000000Z"},{"location":"LOCATION_0","seen_time":"2021-03-29T08:00:00.000000000Z"}],"inputs":[{"address":"**********************************"}],"outputs":[{"address":"**********************************"}]},{"txid":"00000000000000000000000000000000000000000000000000000000000000ff","time":"2021-05-31T14:54:55.000000000Z","first_seen_time":"2021-03-29T07:59:00.000000000Z","status":"UNCONFIRMED","status_update_time":"2021-03-29T07:59:00.000000000Z","status_updates":[{"time":"2021-03-29T07:59:00.000000000Z","status":"UNCONFIRMED"}],"details":{"amount":"10","version":"1","replace_by_fee_supported":true,"fee":"1777","feerate":"4.1","mempool_feerate_mean_at_first_seen_time":"0.2","mempool_feerate_min_at_first_seen_time":"0.1","consensus_size":"11","physical_size":"12"},"mempool_approx_queue_position":"0/66","next_block_approx_settlement_probability_pct":"99.99","geo":[{"location":"LOCATION_0","seen_time":"2021-03-29T07:59:00.000000000Z"}],"replacement_for_txid":"00000000000000000000000000000000000000000000000000000000000000ee","inputs":[{"address":"**********************************"},{"address":"**********************************"}],"outputs":[{"address":"**********************************"},{"address":"**********************************"}]}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain/btc/transaction-tracker?api_key=$TEST_API_KEY&addresses=**********************************,**********************************&unconfirmed_only=false&start_time=2021-03-29",
            "X-Request-Timestamp",
            "1622472895000",
        )
    }

    @Test
    fun `request with addresses parameter and unconfirmed_only=false and end_time`() {
        val expectedResponse =
            """{"data":[{"txid":"00000000000000000000000000000000000000000000000000000000000000bb","time":"2021-05-31T14:54:55.000000000Z","first_seen_time":"2021-03-28T07:59:00.000000000Z","status":"CONFIRMED","status_update_time":"2021-03-28T07:59:00.000000000Z","status_updates":[{"time":"2021-03-28T07:59:00.000000000Z","status":"CONFIRMED","block_hash":"00000000839a8e6886ab5951d76f411475428afc90947ee320161bbf18eb6048","height":"1"}],"details":{"amount":"10","version":"1","replace_by_fee_supported":true,"fee":"1777","feerate":"0.2","mempool_feerate_mean_at_first_seen_time":"0.2","mempool_feerate_min_at_first_seen_time":"0.1","consensus_size":"11","physical_size":"12"},"block_hash":"00000000839a8e6886ab5951d76f411475428afc90947ee320161bbf18eb6048","height":"1","n_confirmations":"3","geo":[{"location":"LOCATION_0","seen_time":"2021-03-28T07:59:00.000000000Z"}],"outputs":[{"address":"**********************************"}]},{"txid":"000000000000000000000000000000000000000000000000000000000000bb33","time":"2021-05-31T14:54:55.000000000Z","first_seen_time":"2021-03-28T07:59:00.000000000Z","status":"UNCONFIRMED","status_update_time":"2021-03-28T07:59:00.000000000Z","status_updates":[{"time":"2021-03-28T07:59:00.000000000Z","status":"UNCONFIRMED"}],"details":{"amount":"10","version":"1","replace_by_fee_supported":true,"fee":"1777","feerate":"1.9","mempool_feerate_mean_at_first_seen_time":"0.2","mempool_feerate_min_at_first_seen_time":"0.1","consensus_size":"11","physical_size":"12"},"mempool_approx_queue_position":"60/66","next_block_approx_settlement_probability_pct":"9.09","geo":[],"inputs":[{"address":"**********************************"},{"address":"**********************************"}],"outputs":[{"address":"**********************************"},{"address":"**********************************"}]}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain/btc/transaction-tracker?api_key=$TEST_API_KEY&addresses=**********************************,**********************************&unconfirmed_only=false&end_time=2021-03-28",
            "X-Request-Timestamp",
            "1622472895000",
        )
    }

    @Test
    fun `request with addresses parameter set to unknown addresses`() {
        val expectedResponse =
            """{"data":[]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain/btc/transaction-tracker?api_key=$TEST_API_KEY&addresses=**********************************,unknown",
            "X-Request-Timestamp",
            "1622472895000",
        )
    }

    @Test
    fun `request with unconfirmed_only without addresses`() {
        val expectedResponse =
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'unconfirmed_only'. 'unconfirmed_only' parameter can be used only in conjunction with 'addresses' parameter."}}"""
        assertResponse(
            400,
            expectedResponse,
            "/v4/blockchain/btc/transaction-tracker?api_key=$TEST_API_KEY&unconfirmed_only=true",
            "X-Request-Timestamp",
            "1622472895000",
        )
    }

    @Test
    fun `request with addresses parameter and txids parameter`() {
        val expectedResponse =
            """{"error":{"type":"bad_parameters","message":"Filters 'txids' and 'addresses' are mutually exclusive and can't be specified in the same request."}}"""
        assertResponse(
            400,
            expectedResponse,
            "/v4/blockchain/btc/transaction-tracker?api_key=$TEST_API_KEY&addresses=**********************************&txids=00000000000000000000000000000000000000000000000000000000000000bb",
            "X-Request-Timestamp",
            "1622472895000",
        )
    }

    @Test
    fun `request with addresses parameter and replacements_for_txids parameter`() {
        val expectedResponse =
            """{"error":{"type":"bad_parameters","message":"Filters 'replacements_for_txids' and 'addresses' are mutually exclusive and can't be specified in the same request."}}"""
        assertResponse(
            400,
            expectedResponse,
            "/v4/blockchain/btc/transaction-tracker?api_key=$TEST_API_KEY&addresses=**********************************&replacements_for_txids=00000000000000000000000000000000000000000000000000000000000000bb",
            "X-Request-Timestamp",
            "1622472895000",
        )
    }
}
