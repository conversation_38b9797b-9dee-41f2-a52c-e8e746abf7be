package io.coinmetrics.api.endpoints.streaming.asset

import io.coinmetrics.api.endpoints.stream.asset.AssetPrincipalPriceKafkaDataProvider
import io.coinmetrics.api.endpoints.stream.asset.AssetRatesKafkaDataProvider
import io.coinmetrics.api.endpoints.streaming.asset.principalprice.PRINCIPAL_PRICE_TOPIC_NAME
import io.coinmetrics.api.endpoints.streaming.asset.principalprice.WsPrincipalPriceBaseTest.Companion.testPrincipalPriceTopics
import io.coinmetrics.api.endpoints.streaming.asset.principalprice.WsPrincipalPriceTestUtils
import io.coinmetrics.api.endpoints.streaming.asset.rates.ONE_SECOND_RATES_TOPIC_NAME
import io.coinmetrics.api.endpoints.streaming.asset.rates.WsRatesBaseTest.Companion.testReferenceRateTopics
import io.coinmetrics.api.endpoints.streaming.asset.rates.WsRefRatesTestUtils
import io.coinmetrics.api.helper.BaseTest
import io.coinmetrics.api.helper.Containers
import io.coinmetrics.api.helper.POSTGRES_IMAGE
import io.coinmetrics.api.helper.TEST_API_KEY
import io.coinmetrics.api.helper.TestWebSocketListener
import io.coinmetrics.api.modules.common.CommonConfig
import io.coinmetrics.api.modules.main.MainApiConfig
import io.coinmetrics.api.statistics.metrics.AssetMetricStatistics
import io.coinmetrics.api.utils.TimeUtils
import io.coinmetrics.api.utils.modifyDatabases
import io.coinmetrics.databases.DatabaseImpl
import io.coinmetrics.databases.DbConfig
import io.coinmetrics.databases.NopDbMonitoring
import io.coinmetrics.queues.QueueProducer
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.AfterAll
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Test
import org.testcontainers.containers.PostgreSQLContainer
import org.testcontainers.junit.jupiter.Container
import org.testcontainers.junit.jupiter.Testcontainers
import java.time.Instant
import java.time.temporal.ChronoUnit

@Testcontainers
class WsAssetMetricsErrorTest : BaseTest() {
    companion object {
        @Container
        val postgres =
            PostgreSQLContainer<Nothing>(POSTGRES_IMAGE).apply {
                withInitScript("postgres/init_db_streaming.sql")
            }
    }

    private val rrProducer by lazy {
        QueueProducer<AssetRatesKafkaDataProvider.RateInput>(
            host = Containers.kafka1.value.host,
            port = Containers.kafka1.value.port,
        )
    }

    private val ppProducer by lazy {
        QueueProducer<AssetPrincipalPriceKafkaDataProvider.PrincipalPriceInput>(
            host = Containers.kafka1.value.host,
            port = Containers.kafka1.value.port,
        )
    }

    override fun commonConfig(): CommonConfig = super.commonConfig().copy(disabledStatistics = setOf(AssetMetricStatistics.descriptor))

    override fun mainApiConfig(): MainApiConfig =
        super
            .mainApiConfig()
            .copy(
                realtimeMetricsUpdateFrequencyMs = 200,
                ratesConfigs =
                    testReferenceRateTopics.map { (frequency, topicName) ->
                        Pair(
                            frequency,
                            MainApiConfig.KafkaSourceConfig(
                                configName = MainApiConfig.RATES_CONFIG_NAME,
                                topicName = topicName,
                                env = "dev",
                                kafkaServers =
                                    MainApiConfig
                                        .KafkaConfig(
                                            configName = "KAFKA_RATES",
                                            env = "dev",
                                            serverUrl = "${Containers.kafka1.value.host}:${Containers.kafka1.value.port}",
                                        ).toListOfAllKafkaServers(),
                            ),
                        )
                    },
                principalPriceConfigs =
                    testPrincipalPriceTopics.map { topicName ->
                        MainApiConfig.KafkaSourceConfig(
                            configName = MainApiConfig.PRINCIPAL_PRICE_CONFIG_NAME,
                            topicName = topicName,
                            env = "dev",
                            kafkaServers =
                                MainApiConfig
                                    .KafkaConfig(
                                        configName = "KAFKA_PRINCIPAL_PRICE",
                                        env = "dev",
                                        serverUrl = "${Containers.kafka1.value.host}:${Containers.kafka1.value.port}",
                                    ).toListOfAllKafkaServers(),
                        )
                    },
            ).modifyDatabases {
                copy(
                    network =
                        DbConfig(
                            appName = "API",
                            dbConfigName = "NETWORK",
                            env = "dev",
                            jdbcUrl = "jdbc:postgresql://${postgres.host}:${postgres.firstMappedPort}/${postgres.databaseName}?user=${postgres.username}&password=${postgres.password}",
                            envVariablesResolver = envVariablesResolver,
                        ),
                )
            }

    @AfterAll
    internal fun tearDown() {
        rrProducer.close()
        ppProducer.close()
        super.stopServer()
        postgres.close()
    }

    @Test
    fun `should return error when ignore_unsupported_errors=false`() =
        runBlocking {
            val inWebSocketListener = TestWebSocketListener()
            connectToWebSocketAsync(
                "/v4/timeseries-stream/asset-metrics?assets=btc1,eth1,eth2,btc&metrics=BlkHgt,AdrActCnt,FlowInGEMNtv&api_key=$TEST_API_KEY&backfill=none&ignore_unsupported_errors=false",
                inWebSocketListener,
            )

            assertEquals(
                """{"error":{"type":"bad_parameter","message":"Bad parameter 'assets'. Value 'btc1' is not supported."}}""",
                inWebSocketListener.takeNextMessage(),
            )

            // no more messages
            assertFalse(inWebSocketListener.hasNextMessage())
        }

    @Test
    fun `should return data when ignore_unsupported_errors=false and no errors raised`() =
        runBlocking {
            val db = DatabaseImpl(mainApiModule.config.databases.network, NopDbMonitoring)
            val schema = mainApiModule.config.databases.network.schema
            val inWebSocketListener = TestWebSocketListener()
            connectToWebSocketAsync(
                "/v4/timeseries-stream/asset-metrics?assets=eth&metrics=BlkHgt,AdrActCnt&api_key=$TEST_API_KEY&backfill=none&ignore_unsupported_errors=false",
                inWebSocketListener,
            )

            db.update(
                """
                INSERT INTO $schema.statistics_realtime (block_hash,parent_block_hash,asset,metric,time,height,value,computed_at,computed_by)
                VALUES
                    ('a0','a','eth','BlkHgt',to_timestamp(1563583374),0,0,to_timestamp(1563583374),'0a'),
                    ('a0','a','eth','AdrActCnt',to_timestamp(1563583374),0,0.34123412,to_timestamp(1563583374),'0a')
                """.trimIndent(),
            ) {
                it.execute()
            }

            assertEquals(
                """{"time":"2019-07-20T00:42:54.000000000Z","asset":"eth","height":"0","hash":"a0","parent_hash":"a","type":"new_block","AdrActCnt":"0.34123412","BlkHgt":"0","cm_sequence_id":"0"}""",
                inWebSocketListener.takeNextMessage(),
            )

            // no more messages
            assertFalse(inWebSocketListener.hasNextMessage())
        }

    @Test
    fun `should return available data when ignore_unsupported_errors=true`() =
        runBlocking {
            val db = DatabaseImpl(mainApiModule.config.databases.network, NopDbMonitoring)
            val schema = mainApiModule.config.databases.network.schema
            val inWebSocketListener = TestWebSocketListener()
            connectToWebSocketAsync(
                "/v4/timeseries-stream/asset-metrics?assets=btc1,eth1,btc&metrics=BlkHgt,AdrActCnt&api_key=$TEST_API_KEY&backfill=none&ignore_unsupported_errors=true",
                inWebSocketListener,
            )

            db.update(
                """
                INSERT INTO $schema.statistics_realtime (block_hash,parent_block_hash,asset,metric,time,height,value,computed_at,computed_by)
                VALUES
                    ('a7','a6','btc','BlkHgt',to_timestamp(1563583374),6,7549,to_timestamp(1563583374),'0a'),
                    ('a7','a6','btc','AdrActCnt',to_timestamp(1563583374),6,7549.34123412,to_timestamp(1563583374),'0a')
                """.trimIndent(),
            ) {
                it.execute()
            }

            assertEquals(
                """{"time":"2019-07-20T00:42:54.000000000Z","asset":"btc","height":"6","hash":"a7","parent_hash":"a6","type":"new_block","AdrActCnt":"7549.34123412","BlkHgt":"7549","cm_sequence_id":"0"}""",
                inWebSocketListener.takeNextMessage(),
            )

            // no more messages
            assertFalse(inWebSocketListener.hasNextMessage())
        }

    @Test
    fun `should return forbidden error when ignore_unsupported_errors=true and ignore_forbidden_errors=false`() =
        runBlocking {
            val inWebSocketListener = TestWebSocketListener()
            connectToWebSocketAsync(
                "/v4/timeseries-stream/asset-metrics?assets=btc1,eth1,eth2,btc&metrics=BlkHgt,AdrActCnt,FlowInGEMNtv,ReferenceRate&frequency=1s&api_key=x3&backfill=none&ignore_unsupported_errors=true&ignore_forbidden_errors=false",
                inWebSocketListener,
            )

            assertEquals(
                """{"error":{"type":"forbidden","message":"Requested metrics are not available with supplied credentials: ReferenceRate."}}""",
                inWebSocketListener.takeNextMessage(),
            )

            // no more messages
            assertFalse(inWebSocketListener.hasNextMessage())
        }

    @Test
    fun `should return allowed RR and skip forbidden PP when ignore_unsupported_errors=true and ignore_forbidden_errors=true`() =
        runBlocking {
            val time = Instant.now()
            mainApiModule.assetRatesDataProviders.forEach { provider ->
                provider.dataSources.forEach { it.consumer.nextMessageOffset() }
            }
            mainApiModule.assetPrincipalPriceDataProviders.forEach { provider ->
                provider.dataSources.forEach { it.consumer.nextMessageOffset() }
            }
            val inWebSocketListener = TestWebSocketListener()
            connectToWebSocketAsync(
                "/v4/timeseries-stream/asset-metrics?assets=btc1,eth1,eth2,btc&metrics=BlkHgt,AdrActCnt,FlowInGEMNtv,ReferenceRate,principal_market_price_usd&frequency=1s&api_key=x9&backfill=none&ignore_unsupported_errors=true&ignore_forbidden_errors=true",
                inWebSocketListener,
            )
            ppProducer.sendAsync(PRINCIPAL_PRICE_TOPIC_NAME, WsPrincipalPriceTestUtils.createKafkaPrincipalPrice(time = time)).await()
            rrProducer.sendAsync(ONE_SECOND_RATES_TOPIC_NAME, WsRefRatesTestUtils.createKafkaReferenceRate(time = time)).await()

            assertEquals(
                """{"time":"${TimeUtils.dateTimeFormatter.format(time.truncatedTo(ChronoUnit.MILLIS))}","asset":"btc","ReferenceRate":"1.01","cm_sequence_id":"0"}""",
                inWebSocketListener.takeNextMessage(),
            )

            // no more messages
            assertFalse(inWebSocketListener.hasNextMessage())
        }

    @Test
    fun `should return allowed PP and skip forbidden RR when ignore_unsupported_errors=true and ignore_forbidden_errors=true`() =
        runBlocking {
            val time = Instant.now()
            mainApiModule.assetRatesDataProviders.forEach { provider ->
                provider.dataSources.forEach { it.consumer.nextMessageOffset() }
            }
            mainApiModule.assetPrincipalPriceDataProviders.forEach { provider ->
                provider.dataSources.forEach { it.consumer.nextMessageOffset() }
            }
            val inWebSocketListener = TestWebSocketListener()
            connectToWebSocketAsync(
                "/v4/timeseries-stream/asset-metrics?assets=btc1,eth1,eth2,btc&metrics=BlkHgt,AdrActCnt,FlowInGEMNtv,ReferenceRate,principal_market_price_usd&frequency=1s&api_key=x10&backfill=none&ignore_unsupported_errors=true&ignore_forbidden_errors=true",
                inWebSocketListener,
            )
            ppProducer.sendAsync(PRINCIPAL_PRICE_TOPIC_NAME, WsPrincipalPriceTestUtils.createKafkaPrincipalPrice(time = time)).await()
            rrProducer.sendAsync(ONE_SECOND_RATES_TOPIC_NAME, WsRefRatesTestUtils.createKafkaReferenceRate(time = time)).await()

            assertEquals(
                """{"time":"${TimeUtils.dateTimeFormatter.format(time.truncatedTo(ChronoUnit.MILLIS))}","asset":"btc","principal_market_price_usd":"1.01","cm_sequence_id":"0"}""",
                inWebSocketListener.takeNextMessage(),
            )

            // no more messages
            assertFalse(inWebSocketListener.hasNextMessage())
        }

    @Test
    fun `should return forbidden error when api key lacks access to realtime_asset_metrics`() =
        runBlocking {
            val inWebSocketListener = TestWebSocketListener()
            connectToWebSocketAsync(
                "/v4/timeseries-stream/asset-metrics?assets=sol&metrics=PriceUSD&frequency=1s&api_key=assetMetricsWsKey",
                inWebSocketListener,
            )

            assertEquals(
                """{"error":{"type":"forbidden","message":"Requested metrics are not available with supplied credentials: PriceUSD."}}""",
                inWebSocketListener.takeNextMessage(),
            )

            // no more messages
            assertFalse(inWebSocketListener.hasNextMessage())
        }
}
