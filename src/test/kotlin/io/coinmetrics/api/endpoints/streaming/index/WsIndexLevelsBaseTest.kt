package io.coinmetrics.api.endpoints.streaming.index

import io.coinmetrics.api.endpoints.stream.index.levels.IndexLevelInput
import io.coinmetrics.api.endpoints.stream.index.levels.IndexVerification
import io.coinmetrics.api.helper.BaseTest
import io.coinmetrics.api.helper.Containers
import io.coinmetrics.api.helper.TestWebSocketListener
import io.coinmetrics.api.modules.main.MainApiConfig
import io.coinmetrics.queues.QueueProducer
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.AfterAll
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.TestInstance
import org.testcontainers.junit.jupiter.Testcontainers
import java.time.Instant
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicLong

@Testcontainers
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
open class WsIndexLevelsBaseTest : BaseTest() {
    companion object {
        val timeProvider = TimeProvider()
    }

    protected val topics = listOf("cmbi_realtime_all", "cmbi_index_realtime_all")
    protected val producer by lazy {
        QueueProducer<IndexLevelInput>(
            host = Containers.kafka1.value.host,
            port = Containers.kafka1.value.port,
        )
    }

    override fun mainApiConfig(): MainApiConfig =
        super.mainApiConfig().copy(
            indexLevelsConfigs =
                topics.map { topic ->
                    MainApiConfig.KafkaSourceConfig(
                        configName = MainApiConfig.INDEX_LEVELS_CONFIG_NAME,
                        env = "dev",
                        topicName = topic,
                        kafkaServers =
                            MainApiConfig
                                .KafkaConfig(
                                    configName = "KAFKA_${MainApiConfig.INDEX_LEVELS_CONFIG_NAME}",
                                    env = "dev",
                                    serverUrl = "${Containers.kafka1.value.host}:${Containers.kafka1.value.port}",
                                ).toListOfAllKafkaServers(),
                        envVariablesResolver = envVariablesResolver,
                    )
                },
        )

    @AfterAll
    fun tearDown() {
        producer.close()
    }

    protected fun connect(uri: String): TestWebSocketListener {
        val listener = TestWebSocketListener()
        connectToWebSocket(uri, listener)
        return listener
    }

    protected fun createIndexLevelInput(
        indexId: Int = 1,
        time: Instant,
        verification: IndexVerification? = null,
    ): IndexLevelInput =
        IndexLevelInput(
            indexId = indexId,
            indexTime = time.toEpochMilli(),
            indexPrice = "1.0",
            verification = verification,
        )

    @BeforeEach
    open fun setUp() =
        runBlocking {
            mainApiModule.indexLevelsDataProviders.forEach { provider ->
                provider.dataSources.filter { it.initialized }.forEach { it.consumer.nextMessageOffset() }
            }
        }
}

class TimeProvider(
    private val currentTimeMillis: Long = System.currentTimeMillis(),
    private val delta: AtomicLong = AtomicLong(),
) {
    fun now(): Instant = Instant.ofEpochMilli(currentTimeMillis + delta.addAndGet(TimeUnit.SECONDS.toMillis(5)))
}
