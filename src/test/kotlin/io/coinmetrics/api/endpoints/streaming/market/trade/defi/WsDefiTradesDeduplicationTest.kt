package io.coinmetrics.api.endpoints.streaming.market.trade.defi

import io.coinmetrics.api.endpoints.streaming.market.trade.TestTradesUtils
import io.coinmetrics.api.helper.TEST_API_KEY
import io.coinmetrics.api.helper.TestWebSocketListener
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import java.time.Instant

class WsDefiTradesDeduplicationTest : WsDefiTradesBaseTest() {
    override fun streamingTradesApiConfig() =
        TestTradesUtils.createStreamingTradesApiConfig(
            commonConfig = commonModule.config,
            envVariablesResolver = envVariablesResolver,
            supportedExchangeIds = supportedExchangeIds,
            kafkaTopicNameResolver = TestTradesUtils::defiKafkaTopicName,
            cacheSize = 2,
        )

    @Test
    fun `should ignore last trade by deduplication`(): Unit =
        runBlocking {
            val url =
                "/v4/timeseries-stream/market-trades?markets=uniswap_v3_eth-agg-usdc-weth-spot&api_key=$TEST_API_KEY&backfill=none"
            val inWebSocketListener = TestWebSocketListener()
            connectToWebSocket(url, inWebSocketListener)
            val now = Instant.now()
            val tradeTime = "${now.epochSecond}.${now.nano}"
            val trades =
                listOf(
                    // trade #1
                    toTrade(
                        """
                        {"tx":{"block":{"hash":"QbDBX3K82dwhmz7GytLV1Mg9THoqykyM+rX4KSuzdck=","parentHash":"LT/d0HMc7bzII9/7wM74cuckEo6cICg1PCjLfa5EDGY=","height":17643652,"timestamp":$tradeTime},"hash":"LJWpA1tkdDMf9pWVoj1Yf3QiIqWG3zNGFKg8XtClfZ8=","logIndex":312},"initiator":"6h6iK1sSUv8FvD36YwOB5yuH8AI=","sender":"P8kaOv1wOVzUlsZH1abMnUsrf60=","beneficiary":"P8kaOv1wOVzUlsZH1abMnUsrf60=","poolConfigId":-1,"baseId":912,"quoteId":1383,"amount":"254024.722645","price":"0.0005362103963923547","buy":true,"cmTradeId":"trade-001"}
                        """.trimIndent(),
                    ),
                    // trade #2
                    toTrade(
                        """
                        {"tx":{"block":{"hash":"mtwRvD6nRsJ0loqdFfqVcmK78px6ProerZt3epB3qLM=","parentHash":"zubF0r+Q2jpDdo+X94nbF2X41QWLttOFK/Hfyc5Sk/o=","height":17643749,"timestamp":$tradeTime},"hash":"BGmh7d7G2MlgJFElnNDkDKkt+Dr/BjINA0+3UuoCPLI=","logIndex":322},"initiator":"XToQ5M0RUxHtNZm+JqYKvyeYcfI=","sender":"6M+tTHWl4cr5Of2Ar8+Dfd40Cmk=","beneficiary":"6M+tTHWl4cr5Of2Ar8+Dfd40Cmk=","poolConfigId":-1,"baseId":912,"quoteId":1383,"amount":"254024.722645","price":"0.0005362103963923547","buy":true,"cmTradeId":"trade-022"}
                        """.trimIndent(),
                    ),
                    // duplicate of trade #1
                    toTrade(
                        """
                        {"tx":{"block":{"hash":"QbDBX3K82dwhmz7GytLV1Mg9THoqykyM+rX4KSuzdck=","parentHash":"LT/d0HMc7bzII9/7wM74cuckEo6cICg1PCjLfa5EDGY=","height":17643652,"timestamp":$tradeTime},"hash":"LJWpA1tkdDMf9pWVoj1Yf3QiIqWG3zNGFKg8XtClfZ8=","logIndex":312},"initiator":"6h6iK1sSUv8FvD36YwOB5yuH8AI=","sender":"P8kaOv1wOVzUlsZH1abMnUsrf60=","beneficiary":"P8kaOv1wOVzUlsZH1abMnUsrf60=","poolConfigId":-1,"baseId":912,"quoteId":1383,"amount":"254024.722645","price":"0.0005362103963923547","buy":true,"cmTradeId":"trade-001"}
                        """.trimIndent(),
                    ),
                )
            trades.forEach { producer.send("trades_45", it) }

            inWebSocketListener.takeNextMessage()
            inWebSocketListener.takeNextMessage()
            Assertions.assertNull(inWebSocketListener.takeNextMessageOrNull(1_000))
        }

    @Test
    fun `should ignore existing trade, then accept new one, then accept existing one because it was removed from deduplication cache`(): Unit =
        runBlocking {
            val url = "/v4/timeseries-stream/market-trades?markets=uniswap_v3_eth-agg-usdc-weth-spot&api_key=$TEST_API_KEY&backfill=none"
            val inWebSocketListener = TestWebSocketListener()
            connectToWebSocket(url, inWebSocketListener)

            // create 2 trades when cache size is 2
            val now = Instant.now()
            val tradeTime = "${now.epochSecond}.${now.nano}"
            listOf(
                // trade #1
                toTrade(
                    """
                    {"tx":{"block":{"hash":"QbDBX3K82dwhmz7GytLV1Mg9THoqykyM+rX4KSuzdck=","parentHash":"LT/d0HMc7bzII9/7wM74cuckEo6cICg1PCjLfa5EDGY=","height":17643652,"timestamp":$tradeTime},"hash":"LJWpA1tkdDMf9pWVoj1Yf3QiIqWG3zNGFKg8XtClfZ8=","logIndex":312},"initiator":"6h6iK1sSUv8FvD36YwOB5yuH8AI=","sender":"P8kaOv1wOVzUlsZH1abMnUsrf60=","beneficiary":"P8kaOv1wOVzUlsZH1abMnUsrf60=","poolConfigId":-1,"baseId":912,"quoteId":1383,"amount":"254024.722645","price":"0.0005362103963923547","buy":true,"cmTradeId":"trade-011"}
                    """.trimIndent(),
                ),
                // trade #2
                toTrade(
                    """
                    {"tx":{"block":{"hash":"mtwRvD6nRsJ0loqdFfqVcmK78px6ProerZt3epB3qLM=","parentHash":"zubF0r+Q2jpDdo+X94nbF2X41QWLttOFK/Hfyc5Sk/o=","height":17643749,"timestamp":$tradeTime},"hash":"BGmh7d7G2MlgJFElnNDkDKkt+Dr/BjINA0+3UuoCPLI=","logIndex":322},"initiator":"XToQ5M0RUxHtNZm+JqYKvyeYcfI=","sender":"6M+tTHWl4cr5Of2Ar8+Dfd40Cmk=","beneficiary":"6M+tTHWl4cr5Of2Ar8+Dfd40Cmk=","poolConfigId":-1,"baseId":912,"quoteId":1383,"amount":"254024.722645","price":"0.0005362103963923547","buy":true,"cmTradeId":"trade-012"}
                    """.trimIndent(),
                ),
            ).forEach { producer.send("trades_45", it) }
            inWebSocketListener.takeNextMessage()
            inWebSocketListener.takeNextMessage()

            // ignore trade with ID we already have
            val duplicateTrade =
                listOf(
                    // duplicate of trade #1
                    toTrade(
                        """
                        {"tx":{"block":{"hash":"QbDBX3K82dwhmz7GytLV1Mg9THoqykyM+rX4KSuzdck=","parentHash":"LT/d0HMc7bzII9/7wM74cuckEo6cICg1PCjLfa5EDGY=","height":17643652,"timestamp":$tradeTime},"hash":"LJWpA1tkdDMf9pWVoj1Yf3QiIqWG3zNGFKg8XtClfZ8=","logIndex":312},"initiator":"6h6iK1sSUv8FvD36YwOB5yuH8AI=","sender":"P8kaOv1wOVzUlsZH1abMnUsrf60=","beneficiary":"P8kaOv1wOVzUlsZH1abMnUsrf60=","poolConfigId":-1,"baseId":912,"quoteId":1383,"amount":"254024.722645","price":"0.0005362103963923547","buy":true,"cmTradeId":"trade-011"}
                        """.trimIndent(),
                    ),
                )
            duplicateTrade.forEach { producer.send("md_trades_uni_v3", it) }
            Assertions.assertNull(inWebSocketListener.takeNextMessageOrNull(1_000))

            // accept new trade
            listOf(
                // trade#3
                toTrade(
                    """
                    {"tx":{"block":{"hash":"gF+L2TmOwvRHHmdt8d6xhjrZbpoVejK1zNescjnpBYo=","parentHash":"gigTvT95bvcMMKdLZftGwMTtdQbruNmlk0tzL6xZooo=","height":17644730,"timestamp":$tradeTime},"hash":"/6e/yao5hjZDH+8YpfmS98Zk4MP2AY9TNImZYk1BMiQ=","logIndex":324},"initiator":"MGQZGXU0zFvU7naTxxYJahSGoHE=","sender":"yqAKr2+8dp1ifYJbT67cOq2IBZc=","beneficiary":"yqAKr2+8dp1ifYJbT67cOq2IBZc=","poolConfigId":-1,"baseId":912,"quoteId":1383,"amount":"254024.722645","price":"0.0005362103963923547","buy":true,"cmTradeId":"trade-013"}
                    """.trimIndent(),
                ),
            ).forEach { producer.send("trades_45", it) }
            inWebSocketListener.takeNextMessage()

            // accept trade with ID we already have, but it was removed from deduplication cache

            Assertions.assertNull(inWebSocketListener.takeNextMessageOrNull(1_000))
            duplicateTrade.forEach { producer.send("trades_45", it) }
            inWebSocketListener.takeNextMessage()

            // assert no more messages
            Assertions.assertNull(inWebSocketListener.takeNextMessageOrNull(1_000))
            Assertions.assertFalse(inWebSocketListener.hasNextMessage())
        }
}
