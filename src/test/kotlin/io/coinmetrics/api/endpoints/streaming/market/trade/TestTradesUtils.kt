package io.coinmetrics.api.endpoints.streaming.market.trade

import io.coinmetrics.api.helper.Containers
import io.coinmetrics.api.modules.common.CommonConfig
import io.coinmetrics.api.modules.main.MainApiConfig
import io.coinmetrics.api.modules.streamingtrades.StreamingTradesApiConfig
import io.coinmetrics.api.resources.Resources
import io.coinmetrics.api.utils.TimeUtils
import io.coinmetrics.proto.MarketDataFeed.TradeEntry
import java.time.Instant

object TestTradesUtils {
    fun protoKafkaTopicName(exchangeId: Int): String = "trades_$exchangeId.proto"

    fun defiKafkaTopicName(exchangeId: Int): String = "trades_$exchangeId"

    fun createStreamingTradesApiConfig(
        commonConfig: CommonConfig,
        envVariablesResolver: (String) -> String?,
        kafkaTopicNameResolver: (Int) -> String = ::protoKafkaTopicName,
        supportedExchangeIds: Collection<Int> = WsTradesBaseTest.supportedExchangeIds,
        mainDataSourceCheckForSilentIntervalMs: Long = 100,
        cacheSize: Int = 100,
        serverUrl: String = "${Containers.kafka1.value.host}:${Containers.kafka1.value.port},${Containers.kafka2.value.host}:${Containers.kafka2.value.port}",
    ) = StreamingTradesApiConfig(
        common = commonConfig,
        kafkaTradesConfigsPerExchange =
            supportedExchangeIds.mapNotNull { exchangeId ->
                Resources.getExchangeById(exchangeId).getOrNull()?.let { exchange ->
                    Triple(
                        exchange,
                        MainApiConfig.KafkaSourceConfig(
                            configName = MainApiConfig.TRADES_CONFIG_NAME,
                            env = "dev",
                            topicName = kafkaTopicNameResolver(exchangeId),
                            kafkaServers =
                                MainApiConfig
                                    .KafkaConfig(
                                        configName = "KAFKA_TRADES",
                                        env = "dev",
                                        serverUrl = serverUrl,
                                    ).toListOfAllKafkaServers(),
                            envVariablesResolver = envVariablesResolver,
                            mainDataSourceCheckForSilentIntervalMs = mainDataSourceCheckForSilentIntervalMs,
                        ),
                        cacheSize,
                    )
                }
            },
    )

    fun createTrades(
        exchangeTime: Instant = Instant.now(),
        scraperTime: Instant = Instant.now(),
        startTradeId: Int = 1,
        baseId: Int? = 0,
        quoteId: Int? = 100,
        price: String = "1.01",
        amount: String = "2.01",
        direction: String = "buy",
        marketType: String = "spot",
        symbol: String = "",
        numberOfTrades: Int = 1,
        markPrice: String? = null,
        indexPrice: String? = null,
        ivTrade: String? = null,
        liquidation: String? = null,
    ): List<TradeEntry> =
        (startTradeId until (startTradeId + numberOfTrades)).map { id ->
            val builder = TradeEntry.newBuilder()
            if (baseId != null) builder.setBaseId(baseId)
            if (quoteId != null) builder.setQuoteId(quoteId)
            builder
                .setSymbol(symbol)
                .setAmount(amount)
                .setScraperReceiveTime(TimeUtils.toMicros(scraperTime))
                .setMarketType(TradeEntry.MarketTypes.valueOf(marketType.uppercase()))
                .setExchangeTime(TimeUtils.toMicros(exchangeTime))
                .setId(id.toString())
                .setPrice(price)
                .setBuy(TradeEntry.Direction.valueOf(direction.uppercase()))

            if (markPrice != null) {
                builder.setMarkPrice(markPrice)
            }

            if (indexPrice != null) {
                builder.setIndexPrice(indexPrice)
            }

            if (ivTrade != null) {
                builder.setImpliedVolatility(ivTrade)
            }

            if (liquidation != null) {
                builder.setLiquidation(liquidation)
            }

            builder.build()
        }

    fun createDerivativesTradesNew(
        exchangeTime: Instant = Instant.now(),
        scraperTime: Instant = Instant.now(),
        startTradeId: Int = 1,
        symbol: String = "BTCUSDT",
        type: String = "future",
        price: String = "1.01",
        amount: String = "2.01",
        direction: String = "buy",
        numberOfTrades: Int = 1,
        markPrice: String? = null,
        indexPrice: String? = null,
        ivTrade: String? = null,
        liquidation: String? = null,
    ): List<TradeEntry> =
        (startTradeId until (startTradeId + numberOfTrades)).map { id ->
            val builder = TradeEntry.newBuilder()

            builder
                .setSymbol(symbol)
                .setAmount(amount)
                .setScraperReceiveTime(TimeUtils.toMicros(scraperTime))
                .setMarketType(TradeEntry.MarketTypes.valueOf(type.uppercase()))
                .setExchangeTime(TimeUtils.toMicros(exchangeTime))
                .setId(id.toString())
                .setPrice(price)
                .setBuy(TradeEntry.Direction.valueOf(direction.uppercase()))

            if (markPrice != null) {
                builder.setMarkPrice(markPrice)
            }

            if (indexPrice != null) {
                builder.setIndexPrice(indexPrice)
            }

            if (ivTrade != null) {
                builder.setImpliedVolatility(ivTrade)
            }

            if (liquidation != null) {
                builder.setLiquidation(liquidation)
            }

            builder.build()
        }
}
