package io.coinmetrics.api.endpoints.streaming.spread.asset

import io.coinmetrics.api.endpoints.stream.spread.SpreadQuotesKafkaDataProvider.AggregatedSpreadQuoteInput
import io.coinmetrics.api.endpoints.stream.spread.SpreadQuotesKafkaDataProvider.Pair
import io.coinmetrics.api.helper.TEST_API_KEY
import io.coinmetrics.api.helper.TestWebSocketListener
import io.coinmetrics.api.utils.TimeUtils.dateTimeFormatter
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import java.time.Instant

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class WsAssetQuotesDeduplicationTest : WsAssetQuotesBaseTest() {
    @Test
    fun `should dedup messages when duplicates received from kafka`() =
        runBlocking {
            val webSocketListener = TestWebSocketListener()
            val uri = "/v4/timeseries-stream/asset-quotes?assets=btc&api_key=$TEST_API_KEY&backfill=none"
            connectToWebSocket(uri, webSocketListener)

            val time1 = Instant.now().toEpochMilli()
            val time2 = Instant.now().plusMillis(500L).toEpochMilli()
            val btc = Pair(0, 3)
            val testQuote1 =
                AggregatedSpreadQuoteInput(btc, time1, askPrice = "1", askSize = "1", bidPrice = "2", bidSize = "2", midPrice = "1.5", spread = "0.5", "btc", "usd")
            val testQuote2 = testQuote1.copy(time = time2)
            val quotes = listOf(testQuote1, testQuote2, testQuote1, testQuote2)

            quotes.forEach { quote -> producer.send(ASSET_QUOTES_TOPIC_NAME, quote) }

            val expectedMessage1 =
                with(testQuote1) {
                    val expectedTime = dateTimeFormatter.format(Instant.ofEpochMilli(time))
                    """{"pair":"$baseAsset-$quoteAsset","time":"$expectedTime","ask_price":"$askPrice","ask_size":"$askSize","bid_price":"$bidPrice","bid_size":"$bidSize","mid_price":"$midPrice","spread":"$spread","cm_sequence_id":"0"}"""
                }
            assertEquals(expectedMessage1, webSocketListener.takeNextMessage())

            val expectedMessage2 =
                with(testQuote2) {
                    val expectedTime = dateTimeFormatter.format(Instant.ofEpochMilli(time))
                    """{"pair":"$baseAsset-$quoteAsset","time":"$expectedTime","ask_price":"$askPrice","ask_size":"$askSize","bid_price":"$bidPrice","bid_size":"$bidSize","mid_price":"$midPrice","spread":"$spread","cm_sequence_id":"1"}"""
                }
            assertEquals(expectedMessage2, webSocketListener.takeNextMessage())

            assertFalse(webSocketListener.hasNextMessage())
        }
}
