package io.coinmetrics.api.endpoints.streaming.market.orderbooks

import io.coinmetrics.api.endpoints.stream.market.orderbooks.test.TestBook
import io.coinmetrics.api.endpoints.stream.market.orderbooks.test.TestPriceLevel
import io.coinmetrics.api.helper.POSTGRES_IMAGE
import io.coinmetrics.api.helper.TEST_API_KEY
import io.coinmetrics.api.helper.TestWebSocketListener
import io.coinmetrics.api.modules.common.CommonConfig
import io.coinmetrics.api.modules.main.MainApiConfig
import io.coinmetrics.api.utils.awaitUntilAsserted
import io.coinmetrics.api.utils.modifyDatabases
import io.coinmetrics.bookstreams.Depth
import io.coinmetrics.bookstreams.MarketId
import io.coinmetrics.bookstreams.MarketKind
import io.coinmetrics.databases.DatabaseImpl
import io.coinmetrics.databases.DbConfig
import io.coinmetrics.databases.NopDbMonitoring
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.AfterAll
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Test
import org.testcontainers.containers.PostgreSQLContainer
import org.testcontainers.junit.jupiter.Container
import org.testcontainers.junit.jupiter.Testcontainers
import java.time.Duration
import java.time.Instant

@Testcontainers
class DynamicOrderBookTest : BaseGetTimeseriesStreamMarketOrderbooksEndpointImplTest() {
    companion object {
        @Container
        val postgres =
            PostgreSQLContainer<Nothing>(POSTGRES_IMAGE).apply {
                withInitScript("postgres/init_db_streaming.sql")
            }
    }

    override fun commonConfig(): CommonConfig = super.commonConfig().copy(statisticsPollInterval = Duration.ofMillis(200))

    override fun mainApiConfig(): MainApiConfig =
        super.mainApiConfig().modifyDatabases {
            copy(
                tradesSpot =
                    DbConfig(
                        appName = "API",
                        dbConfigName = "TRADES_SPOT",
                        env = "dev",
                        jdbcUrl = "jdbc:postgresql://${postgres.host}:${postgres.firstMappedPort}/${postgres.databaseName}?user=${postgres.username}&password=${postgres.password}",
                        envVariablesResolver = envVariablesResolver,
                    ),
                books =
                    DbConfig(
                        appName = "API",
                        dbConfigName = "BOOKS",
                        env = "dev",
                        jdbcUrl = "jdbc:postgresql://${postgres.host}:${postgres.firstMappedPort}/${postgres.databaseName}?user=${postgres.username}&password=${postgres.password}",
                        envVariablesResolver = envVariablesResolver,
                    ),
            )
        }

    override fun statisticsConfig() =
        super.statisticsConfig().copy(
            marketHotBooksStatisticsRefreshIntervalMs = 200,
        )

    override fun streamingBooksApiConfig() =
        super.streamingBooksApiConfig().copy(
            statisticsUpdateIntervalMs = 100,
        )

    @AfterAll
    internal fun tearDown() {
        super.stopServer()
        postgres.close()
    }

    private val receivedFromExchangeTime = Instant.parse("2025-02-14T01:23:34.123456789Z")

    @Test
    fun `basic positive scenario`() =
        runBlocking {
            val inWebSocketListener = createWebSocketListener()

            sendInitialFutureMarketMessage(inWebSocketListener)
            sendInitialSpotMarketMessage(inWebSocketListener)
            sendNotYetNewFutureMarketMessage(inWebSocketListener)
            sendNewFutureMarketMessage(inWebSocketListener)
            sendNotYetNewSpotMarketMessage(inWebSocketListener)
            sendNewSpotMarketMessage(inWebSocketListener)
            sendNonPatternMarketMessage(inWebSocketListener)
        }

    private fun createWebSocketListener(): TestWebSocketListener {
        val inWebSocketListener = TestWebSocketListener()
        connectToWebSocket(
            "/v4/timeseries-stream/market-orderbooks?markets=binance-*,coinbase-ETHUSD-future&depth_limit=100&backfill=none&api_key=$TEST_API_KEY",
            inWebSocketListener,
        )
        return inWebSocketListener
    }

    private suspend fun sendInitialFutureMarketMessage(inWebSocketListener: TestWebSocketListener) {
        val market = MarketId(exchangeId = 4, "BTCUSDT", MarketKind.Future)

        /**
         * WebSocket connections subscribe to market updates asynchronously,
         * so there is a risk that a message might be sent before the subscription is fully completed.
         */
        awaitUntilAsserted {
            send(
                depth = Depth.Top(100),
                books =
                    listOf(market).map {
                        TestBook(
                            marketId = it,
                            receivedFromExchangeTime = receivedFromExchangeTime,
                            exchangeProvidedId = "1",
                            exchangeProvidedTime = receivedFromExchangeTime,
                            isSnapshot = true,
                            bids = listOf(TestPriceLevel("42", "42")),
                            asks = listOf(),
                        )
                    },
                initialSnapshot = true,
            )

            assertEquals(
                """{"market":"binance-BTCUSDT-future","time":"2025-02-14T01:23:34.123456789Z","coin_metrics_id":"1","asks":[],"bids":[{"price":"42","size":"42"}],"type":"snapshot","collect_time":"2025-02-14T01:23:34.123456789Z","cm_sequence_id":"0"}""",
                inWebSocketListener.takeNextMessageOrNull(200)?.replaceAfterLast(",", """"cm_sequence_id":"0"}"""),
            )
        }
    }

    private suspend fun sendInitialSpotMarketMessage(inWebSocketListener: TestWebSocketListener) {
        val market = MarketId(exchangeId = 4, "BTCUSDT", MarketKind.Spot)

        send(
            depth = Depth.Top(100),
            books =
                listOf(market).map {
                    TestBook(
                        it,
                        receivedFromExchangeTime = receivedFromExchangeTime,
                        exchangeProvidedId = "1",
                        exchangeProvidedTime = receivedFromExchangeTime,
                        isSnapshot = true,
                        bids = listOf(TestPriceLevel("42", "42")),
                        asks = listOf(),
                    )
                },
            initialSnapshot = true,
        )

        assertEquals(
            """{"market":"binance-btc-usdt-spot","time":"2025-02-14T01:23:34.123456789Z","coin_metrics_id":"1","asks":[],"bids":[{"price":"42","size":"42"}],"type":"snapshot","collect_time":"2025-02-14T01:23:34.123456789Z","cm_sequence_id":"1"}""".trimMargin(),
            inWebSocketListener.takeNextMessage().replaceAfterLast(",", """"cm_sequence_id":"1"}"""),
        )
    }

    private suspend fun sendNotYetNewFutureMarketMessage(inWebSocketListener: TestWebSocketListener) {
        val market = MarketId(exchangeId = 4, "TRUMPUSDT", MarketKind.Future)
        send(
            depth = Depth.Top(100),
            books =
                listOf(market).map {
                    TestBook(
                        it,
                        receivedFromExchangeTime = receivedFromExchangeTime,
                        exchangeProvidedId = "1",
                        exchangeProvidedTime = receivedFromExchangeTime,
                        isSnapshot = true,
                        bids = listOf(TestPriceLevel("42", "42")),
                        asks = listOf(),
                    )
                },
            initialSnapshot = true,
        )
        assertNull(inWebSocketListener.takeNextMessageOrNull(200))
    }

    private suspend fun sendNewFutureMarketMessage(inWebSocketListener: TestWebSocketListener) {
        val market = MarketId(exchangeId = 4, "TRUMPUSDT", MarketKind.Future)

        val db = DatabaseImpl(statisticsConfig.databases.books, NopDbMonitoring)
        db.update(
            """
            INSERT INTO ${statisticsConfig.databases.books.schema}.books_future
            (book_exchange_id,book_symbol,book_depth_limit,book_time,book_database_time,book_exchange_sequence_id,book_bids,book_asks)
            VALUES (4,'TRUMPUSDT',100,'2025-02-13 10:00:00'::timestamp,'2025-02-13 10:00:00'::timestamp,NULL,ARRAY[ROW(0.1,1.04,NULL),ROW(0.11,1.03,NULL)]::test.BOOKENTRY[],ARRAY[ROW(0.4,1.07,NULL),ROW(0.3,1.5,NULL)]::test.BOOKENTRY[]);
            """.trimIndent(),
        ) {
            it.execute()
        }

        /**
         * Interested markets are added to WebSocket connections asynchronously,
         * so we need to check messages in a loop.
         */
        awaitUntilAsserted {
            send(
                depth = Depth.Top(100),
                books =
                    listOf(market).map {
                        TestBook(
                            it,
                            receivedFromExchangeTime = receivedFromExchangeTime,
                            exchangeProvidedId = "1",
                            exchangeProvidedTime = receivedFromExchangeTime,
                            isSnapshot = true,
                            bids = listOf(TestPriceLevel("42", "42")),
                            asks = listOf(),
                        )
                    },
                initialSnapshot = true,
            )
            assertEquals(
                """{"market":"binance-TRUMPUSDT-future","time":"2025-02-14T01:23:34.123456789Z","coin_metrics_id":"1","asks":[],"bids":[{"price":"42","size":"42"}],"type":"snapshot","collect_time":"2025-02-14T01:23:34.123456789Z","cm_sequence_id":"2"}""".trimMargin(),
                inWebSocketListener.takeNextMessageOrNull(200)?.replaceAfterLast(",", """"cm_sequence_id":"2"}"""),
            )
        }
    }

    private suspend fun sendNotYetNewSpotMarketMessage(inWebSocketListener: TestWebSocketListener) {
        val market = MarketId(exchangeId = 4, "TRUMPUSDT", MarketKind.Spot)

        send(
            depth = Depth.Top(100),
            books =
                listOf(market).map {
                    TestBook(
                        it,
                        receivedFromExchangeTime = receivedFromExchangeTime,
                        exchangeProvidedId = "1",
                        exchangeProvidedTime = receivedFromExchangeTime,
                        isSnapshot = true,
                        bids = listOf(TestPriceLevel("42", "42")),
                        asks = listOf(),
                    )
                },
            initialSnapshot = true,
        )
        assertNull(inWebSocketListener.takeNextMessageOrNull(200))
    }

    private suspend fun sendNewSpotMarketMessage(inWebSocketListener: TestWebSocketListener) {
        val market = MarketId(exchangeId = 4, "TRUMPUSDT", MarketKind.Spot)

        val dbMetadata = DatabaseImpl(statisticsConfig.databases.tradesSpot, NopDbMonitoring)
        dbMetadata.update(
            """
            INSERT INTO ${statisticsConfig.databases.tradesSpot.schema}.spot_metadata
            (market_exchange_id, market_symbol, market_base_id, market_quote_id, market_base_name, market_quote_name, market_listing_date, market_end_date, market_is_current, market_database_time, market_status, market_amount_increment, market_amount_size_min, market_amount_size_max, market_price_increment, market_price_size_min, market_price_size_max, market_order_size_min, market_taker_fee, market_maker_fee, market_margin_trading_enabled, market_native_base_name, market_native_quote_name)
            VALUES (4, 'TRUMPUSDT', 4076, 100, 'trump', 'usdt', '2018-04-24 13:00:23', '3000-01-01 08:00:00', 'true', '2024-04-02 12:06:10', 'online', null, '0.0001', null, '0.1', null, null, null, '0.0', '0.0', null, 'trump_native', 'usdt_native');
            """.trimIndent(),
        ) {
            it.execute()
        }

        val db = DatabaseImpl(statisticsConfig.databases.books, NopDbMonitoring)
        db.update(
            """
            INSERT INTO ${statisticsConfig.databases.books.schema}.books_spot
            (book_exchange_id,book_symbol,book_depth_limit,book_time,book_database_time,book_exchange_sequence_id,book_bids,book_asks)
            VALUES (4,'TRUMPUSDT',100,'2025-02-13 10:00:00'::timestamp,'2025-02-13 10:00:00'::timestamp,2,ARRAY[ROW(0.1,1.041,NULL),ROW(0.11,1.035,NULL)]::test.BOOKENTRY[],ARRAY[ROW(0.4,1.077,NULL),ROW(0.3,1.51,NULL)]::test.BOOKENTRY[]);
            """.trimIndent(),
        ) {
            it.execute()
        }

        /**
         * Interested markets are added to WebSocket connections asynchronously,
         * so we need to check messages in a loop.
         */
        awaitUntilAsserted {
            send(
                depth = Depth.Top(100),
                books =
                    listOf(market).map {
                        TestBook(
                            it,
                            receivedFromExchangeTime = receivedFromExchangeTime,
                            exchangeProvidedId = "1",
                            exchangeProvidedTime = receivedFromExchangeTime,
                            isSnapshot = true,
                            bids = listOf(TestPriceLevel("42", "42")),
                            asks = listOf(),
                        )
                    },
                initialSnapshot = true,
            )
            assertEquals(
                """{"market":"binance-trump-usdt-spot","time":"2025-02-14T01:23:34.123456789Z","coin_metrics_id":"1","asks":[],"bids":[{"price":"42","size":"42"}],"type":"snapshot","collect_time":"2025-02-14T01:23:34.123456789Z","cm_sequence_id":"3"}""".trimMargin(),
                inWebSocketListener.takeNextMessageOrNull(200)?.replaceAfterLast(",", """"cm_sequence_id":"3"}"""),
            )
        }
    }

    private suspend fun sendNonPatternMarketMessage(inWebSocketListener: TestWebSocketListener) {
        val market = MarketId(exchangeId = 1, "ETHUSD", MarketKind.Future)

        send(
            depth = Depth.Top(100),
            books =
                listOf(market).map {
                    TestBook(
                        it,
                        receivedFromExchangeTime = receivedFromExchangeTime,
                        exchangeProvidedId = "1",
                        exchangeProvidedTime = receivedFromExchangeTime,
                        isSnapshot = true,
                        bids = listOf(TestPriceLevel("42", "42")),
                        asks = listOf(),
                    )
                },
            initialSnapshot = true,
        )

        assertEquals(
            """{"market":"coinbase-ETHUSD-future","time":"2025-02-14T01:23:34.123456789Z","coin_metrics_id":"1","asks":[],"bids":[{"price":"42","size":"42"}],"type":"snapshot","collect_time":"2025-02-14T01:23:34.123456789Z","cm_sequence_id":"4"}""".trimMargin(),
            inWebSocketListener.takeNextMessage().replaceAfterLast(",", """"cm_sequence_id":"4"}"""),
        )
    }
}
