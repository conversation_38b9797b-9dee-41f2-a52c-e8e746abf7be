package io.coinmetrics.api.endpoints.streaming.asset.principalprice

import io.coinmetrics.api.endpoints.streaming.asset.principalprice.WsPrincipalPriceTestUtils.createKafkaPrincipalPrice
import io.coinmetrics.api.helper.TEST_API_KEY
import io.coinmetrics.api.helper.TestWebSocketListener
import io.coinmetrics.api.utils.TimeUtils
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import java.time.Instant
import java.time.temporal.ChronoUnit

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class WsPrincipalPriceTest : WsPrincipalPriceBaseTest() {
    @Test
    fun `receive btc-usd 1s with non-hour timestamp`() =
        runBlocking {
            val time = Instant.now().truncatedTo(ChronoUnit.HOURS).plus(1, ChronoUnit.SECONDS)
            executeBaseTest(
                asset = "btc",
                quote = "usd",
                frequency = "1s",
                apiKey = TEST_API_KEY,
                time = time,
                expectedConnectionCount = 1,
                expectedMessages =
                    listOf(
                        """{"time":"${TimeUtils.dateTimeFormatter.format(
                            time.truncatedTo(ChronoUnit.MILLIS),
                        )}","asset":"btc","principal_market_price_usd":"1.01","cm_sequence_id":"0"}""",
                    ),
            )
        }

    @Test
    fun `receive bnb-usd 1s with hour timestamp`() =
        runBlocking {
            val time = Instant.now().truncatedTo(ChronoUnit.HOURS)
            executeBaseTest(
                asset = "bnb",
                quote = "usd",
                frequency = "1s",
                apiKey = TEST_API_KEY,
                time = time,
                expectedConnectionCount = 1,
                expectedMessages =
                    listOf(
                        """{"time":"${TimeUtils.dateTimeFormatter.format(
                            time.truncatedTo(ChronoUnit.MILLIS),
                        )}","asset":"bnb","principal_market_price_usd":"1.01","cm_sequence_id":"0"}""",
                    ),
            )
        }

    @Test
    fun `receive eth-usd 1h`() =
        runBlocking {
            val time = Instant.now().truncatedTo(ChronoUnit.HOURS)
            executeBaseTest(
                asset = "eth",
                quote = "usd",
                frequency = "1h",
                apiKey = TEST_API_KEY,
                time = time,
                expectedConnectionCount = 1,
                expectedMessages =
                    listOf(
                        """{"time":"${TimeUtils.dateTimeFormatter.format(
                            time.truncatedTo(ChronoUnit.MILLIS),
                        )}","asset":"eth","principal_market_price_usd":"1.01","cm_sequence_id":"0"}""",
                    ),
            )
        }

    @Test
    fun `test unsupported currencies`() =
        runBlocking {
            val expectedAssets = listOf("usdt", "usdc", "ton", "doge", "ada", "sol", "xrp", "trx", "avax")
            val inWebSocketListener = TestWebSocketListener()
            connectToWebSocket(
                "/v4/timeseries-stream/asset-metrics?assets=${expectedAssets.joinToString(",")}&metrics=principal_market_price_usd&frequency=1s&api_key=$TEST_API_KEY&backfill=none",
                inWebSocketListener,
            )

            val time = Instant.now()
            val topicName = testPrincipalPriceTopics.first()
            expectedAssets.forEachIndexed { index, asset ->
                producer.send(topicName, createKafkaPrincipalPrice(assetId = asset, time = time))
                val unsupportedAssetId: Int = (index + 1).toString().repeat(9).toInt()
                producer.send(topicName, createKafkaPrincipalPrice(assetId = unsupportedAssetId, time = time))
            }

            val expectedMessages =
                expectedAssets.mapIndexed { index, asset ->
                    """{"time":"${
                        TimeUtils.dateTimeFormatter.format(
                            time.truncatedTo(ChronoUnit.MILLIS),
                        )
                    }","asset":"$asset","principal_market_price_usd":"1.01","cm_sequence_id":"$index"}"""
                }
            for (i in expectedMessages.indices) {
                val message = inWebSocketListener.takeNextMessage()
                assertEquals(expectedMessages[i], message) {
                    "Index $i failed."
                }
            }
            // no more messages
            assertFalse(inWebSocketListener.hasNextMessage())
        }
}
