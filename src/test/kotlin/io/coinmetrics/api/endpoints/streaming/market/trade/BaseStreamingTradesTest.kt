package io.coinmetrics.api.endpoints.streaming.market.trade

import io.coinmetrics.api.helper.BaseTest
import io.coinmetrics.api.modules.ApiModule
import io.coinmetrics.api.modules.streamingtrades.StreamingTradesApiModule

abstract class BaseStreamingTradesTest : BaseTest() {
    protected val streamingTradesApiModule by lazy {
        StreamingTradesApiModule(
            common = commonModule,
            config = streamingTradesApiConfig(),
        )
    }

    override fun otherModules(): List<ApiModule> = listOf(streamingTradesApiModule)

    protected open fun streamingTradesApiConfig() =
        TestTradesUtils.createStreamingTradesApiConfig(
            commonConfig = commonModule.config,
            envVariablesResolver = envVariablesResolver,
        )
}
