package io.coinmetrics.api.endpoints.streaming.market.openinterest

import io.coinmetrics.api.helper.BaseTest
import io.coinmetrics.api.helper.Containers
import io.coinmetrics.api.helper.TestWebSocketListener
import io.coinmetrics.api.modules.main.MainApiConfig
import io.coinmetrics.api.resources.Resources
import io.coinmetrics.api.utils.TimeUtils
import io.coinmetrics.proto.MarketDataFeed
import io.coinmetrics.proto.MarketDataFeed.OpenInterestEntry.MarketTypes
import io.coinmetrics.queues.QueueProducer
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.AfterAll
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class WsOpenInterestTwoDataSourcesTest : BaseTest() {
    companion object {
        private const val EXCHANGE_ID = 4
        val kafkaTopicName = WsOpenInterestBaseTest.kafkaTopicName(EXCHANGE_ID)
    }

    private val mainProducer by lazy {
        QueueProducer<MarketDataFeed.OpenInterestEntry>(host = Containers.kafka1.value.host, port = Containers.kafka1.value.port)
    }
    private val companionProducer by lazy {
        QueueProducer<MarketDataFeed.OpenInterestEntry>(host = Containers.kafka2.value.host, port = Containers.kafka2.value.port)
    }

    override fun mainApiConfig(): MainApiConfig =
        super.mainApiConfig().copy(
            kafkaOpenInterestConfigsPerExchange =
                listOf(
                    Triple(
                        Resources.getExchangeById(EXCHANGE_ID).getOrNull()!!,
                        MainApiConfig.KafkaSourceConfig(
                            configName = MainApiConfig.OPEN_INTEREST_CONFIG_NAME,
                            env = "dev",
                            topicName = kafkaTopicName,
                            kafkaServers =
                                MainApiConfig
                                    .KafkaConfig(
                                        configName = "KAFKA_OPEN_INTEREST",
                                        env = "dev",
                                        serverUrl = "${Containers.kafka1.value.host}:${Containers.kafka1.value.port},${Containers.kafka2.value.host}:${Containers.kafka2.value.port}",
                                    ).toListOfAllKafkaServers(),
                            envVariablesResolver = envVariablesResolver,
                            mainDataSourceCheckForSilentIntervalMs = 0,
                        ),
                        100,
                    ),
                ),
        )

    @Test
    fun `should receive and process records from two sources`() =
        runBlocking {
            val inWebSocketListener = TestWebSocketListener()
            connectToWebSocket(
                "/v4/timeseries-stream/market-openinterest?markets=binance-BTCUSDT-future&api_key=x1&backfill=none",
                inWebSocketListener,
            )
            val openInterests =
                TestOpenInterestUtils.createOpenInterests(
                    marketType = MarketTypes.FUTURE,
                    symbol = "BTCUSDT",
                    numberOfOpenInterests = 10,
                )

            repeat(openInterests.size) {
                if (it % 2 == 0) {
                    mainProducer.sendBytes(kafkaTopicName, openInterests[it].toByteArray())
                } else {
                    companionProducer.sendBytes(kafkaTopicName, openInterests[it].toByteArray())
                }
            }

            repeat(openInterests.size) {
                val message = inWebSocketListener.takeNextMessage()
                val expectedMessages = getExpectedMessages(openInterests, it)
                Assertions.assertTrue(expectedMessages.contains(message)) {
                    """
                |Received unexpected message: $message.
                |Expected messages for $it iteration:"
                |${expectedMessages.joinToString(System.lineSeparator())}.
                """.trimMargin("|")
                }
            }

            // no more messages
            Assertions.assertFalse(inWebSocketListener.hasNextMessage())
        }

    @AfterAll
    fun tearDown() {
        mainProducer.close()
        companionProducer.close()
    }

    @BeforeEach
    fun setUp() =
        runBlocking {
            mainApiModule.openInterestKafkaDataProviders.forEach { provider ->
                provider.dataSources.forEach { it.consumer.nextMessageOffset() }
            }
        }

    private fun getFormattedTime(time: Long) = TimeUtils.dateTimeFormatter.format(TimeUtils.fromMicros(time))

    private fun getExpectedMessages(
        openInterests: Collection<MarketDataFeed.OpenInterestEntry>,
        iteration: Int,
    ) = openInterests.map { openInterestEntry ->
        val time = getFormattedTime(openInterestEntry.exchangeTime)
        """{"market":"binance-BTCUSDT-future","time":"$time","contract_count":"1.01","value_usd":"2.01","exchange_time":"$time","cm_sequence_id":"$iteration"}"""
    }
}
