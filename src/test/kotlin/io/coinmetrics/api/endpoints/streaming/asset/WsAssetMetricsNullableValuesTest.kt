package io.coinmetrics.api.endpoints.streaming.asset

import io.coinmetrics.api.helper.BaseTest
import io.coinmetrics.api.helper.POSTGRES_IMAGE
import io.coinmetrics.api.helper.TEST_API_KEY
import io.coinmetrics.api.helper.TestWebSocketListener
import io.coinmetrics.api.modules.common.CommonConfig
import io.coinmetrics.api.modules.main.MainApiConfig
import io.coinmetrics.api.statistics.metrics.AssetMetricStatistics
import io.coinmetrics.api.utils.modifyDatabases
import io.coinmetrics.databases.DatabaseImpl
import io.coinmetrics.databases.DbConfig
import io.coinmetrics.databases.NopDbMonitoring
import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.AfterAll
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.testcontainers.containers.PostgreSQLContainer
import org.testcontainers.junit.jupiter.Container
import org.testcontainers.junit.jupiter.Testcontainers

@Testcontainers
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class WsAssetMetricsNullableValuesTest : BaseTest() {
    companion object {
        @Container
        val postgres =
            PostgreSQLContainer<Nothing>(POSTGRES_IMAGE).apply {
                withInitScript("postgres/init_db_streaming.sql")
            }
    }

    override fun commonConfig(): CommonConfig = super.commonConfig().copy(disabledStatistics = setOf(AssetMetricStatistics.descriptor))

    override fun mainApiConfig(): MainApiConfig =
        super
            .mainApiConfig()
            .copy(realtimeMetricsUpdateFrequencyMs = 200)
            .modifyDatabases {
                copy(
                    network =
                        DbConfig(
                            appName = "API",
                            dbConfigName = "NETWORK",
                            env = "dev",
                            jdbcUrl = "jdbc:postgresql://${postgres.host}:${postgres.firstMappedPort}/${postgres.databaseName}?user=${postgres.username}&password=${postgres.password}",
                            envVariablesResolver = envVariablesResolver,
                        ),
                )
            }

    @AfterAll
    internal fun tearDown() {
        super.stopServer()
        postgres.close()
    }

    @Test
    fun `should normally process metrics with NULL values`() {
        val config = mainApiModule.config
        val db = DatabaseImpl(mainApiModule.config.databases.network, NopDbMonitoring)

        val inWebSocketListener =
            runBlocking {
                db.update(
                    // BlkHgt is mandatory here because it is required in query to get the chain tip
                    """
                    INSERT INTO ${mainApiModule.config.databases.network.schema}.statistics_realtime (block_hash,parent_block_hash,asset,metric,time,height,value,computed_at,computed_by)
                    VALUES 
                        ('b1','a0','btc','FeeMedNtv',to_timestamp(1563583375),1,NULL,to_timestamp(1563583375),'0a'),
                        ('b2','b1','btc','FeeMedNtv',to_timestamp(1563583375),2,NULL,to_timestamp(1563583375),'0a'),
                        ('b3','b2','btc','FeeMedNtv',to_timestamp(1563583375),3,NULL,to_timestamp(1563583375),'0a'),
                        ('b3','b2','btc','BlkHgt',to_timestamp(1563583375),3,3,to_timestamp(1563583375),'0a')
                    """.trimIndent(),
                ) {
                    it.execute()
                }

                val delayBetweenCallsMs = config.realtimeMetricsUpdateFrequencyMs * 2
                delay(delayBetweenCallsMs)

                val inWebSocketListener = TestWebSocketListener()
                connectToWebSocketAsync(
                    "/v4/timeseries-stream/asset-metrics?assets=btc&metrics=FeeMedNtv&api_key=$TEST_API_KEY",
                    inWebSocketListener,
                )
                inWebSocketListener
            }

        val message = inWebSocketListener.takeNextMessage()
        assertEquals(
            """{"time":"2019-07-20T00:42:55.000000000Z","asset":"btc","height":"3","hash":"b3","parent_hash":"b2","type":"new_block","FeeMedNtv":null,"cm_sequence_id":"0"}""",
            message,
        )
        // no more messages
        assertFalse(inWebSocketListener.hasNextMessage())
    }
}
