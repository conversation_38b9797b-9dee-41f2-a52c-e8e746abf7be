package io.coinmetrics.api.endpoints.streaming.asset.rates

import io.coinmetrics.api.endpoints.stream.asset.AssetRatesKafkaDataProvider
import io.coinmetrics.api.helper.BaseTest
import io.coinmetrics.api.helper.Containers
import io.coinmetrics.api.modules.main.MainApiConfig
import io.coinmetrics.queues.QueueProducer
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.AfterAll
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.TestInstance

const val ONE_SECOND_RATES_TOPIC_NAME = "realtime_all"
const val SUB_SECOND_RATES_TOPIC_NAME = "rates_all_200ms"

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
open class WsRatesBaseTest : BaseTest() {
    companion object {
        val testReferenceRateTopics =
            listOf(
                "1s" to ONE_SECOND_RATES_TOPIC_NAME,
                "200ms" to SUB_SECOND_RATES_TOPIC_NAME,
            )
    }

    protected val producer by lazy {
        QueueProducer<AssetRatesKafkaDataProvider.RateInput>(
            host = Containers.kafka1.value.host,
            port = Containers.kafka1.value.port,
        )
    }

    override fun mainApiConfig(): MainApiConfig =
        super.mainApiConfig().copy(
            ratesConfigs =
                testReferenceRateTopics.map { (frequency, topicName) ->
                    Pair(
                        frequency,
                        MainApiConfig.KafkaSourceConfig(
                            configName = MainApiConfig.RATES_CONFIG_NAME,
                            topicName = topicName,
                            env = "dev",
                            kafkaServers =
                                MainApiConfig
                                    .KafkaConfig(
                                        configName = "KAFKA_RATES",
                                        env = "dev",
                                        serverUrl = "${Containers.kafka1.value.host}:${Containers.kafka1.value.port}",
                                    ).toListOfAllKafkaServers(),
                        ),
                    )
                },
        )

    @BeforeEach
    fun setUp() =
        runBlocking {
            mainApiModule.assetRatesDataProviders.forEach { provider ->
                provider.dataSources.forEach { it.consumer.nextMessageOffset() }
            }
        }

    @AfterAll
    fun tearDown() {
        producer.close()
    }
}
