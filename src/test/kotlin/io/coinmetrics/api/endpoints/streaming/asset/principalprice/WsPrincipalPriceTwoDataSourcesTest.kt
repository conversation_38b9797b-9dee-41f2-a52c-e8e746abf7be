package io.coinmetrics.api.endpoints.streaming.asset.principalprice

import io.coinmetrics.api.endpoints.stream.asset.AssetPrincipalPriceKafkaDataProvider
import io.coinmetrics.api.endpoints.streaming.asset.principalprice.WsPrincipalPriceTestUtils.createKafkaPrincipalPrice
import io.coinmetrics.api.helper.Containers
import io.coinmetrics.api.helper.TEST_API_KEY
import io.coinmetrics.api.helper.TestWebSocketListener
import io.coinmetrics.api.modules.main.MainApiConfig
import io.coinmetrics.queues.QueueProducer
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.AfterAll
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import java.time.Instant

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class WsPrincipalPriceTwoDataSourcesTest : WsPrincipalPriceBaseTest() {
    private val mainProducer by lazy {
        QueueProducer<AssetPrincipalPriceKafkaDataProvider.PrincipalPriceInput>(
            host = Containers.kafka1.value.host,
            port = Containers.kafka1.value.port,
        )
    }
    private val companionProducer by lazy {
        QueueProducer<AssetPrincipalPriceKafkaDataProvider.PrincipalPriceInput>(
            host = Containers.kafka2.value.host,
            port = Containers.kafka2.value.port,
        )
    }

    private val topicName = testPrincipalPriceTopics.first()

    init {
        clock.instant = Instant.parse("2024-03-19T10:00:00.000Z")
    }

    override fun mainApiConfig(): MainApiConfig =
        super.mainApiConfig().copy(
            principalPriceConfigs =
                testPrincipalPriceTopics.map { topicName ->
                    MainApiConfig.KafkaSourceConfig(
                        configName = MainApiConfig.PRINCIPAL_PRICE_CONFIG_NAME,
                        topicName = topicName,
                        env = "dev",
                        kafkaServers =
                            MainApiConfig
                                .KafkaConfig(
                                    configName = "KAFKA_PRINCIPAL_PRICE",
                                    env = "dev",
                                    serverUrl = "${Containers.kafka1.value.host}:${Containers.kafka1.value.port},${Containers.kafka2.value.host}:${Containers.kafka2.value.port}",
                                ).toListOfAllKafkaServers(),
                        envVariablesResolver = envVariablesResolver,
                        mainDataSourceCheckForSilentIntervalMs = 0,
                    )
                },
        )

    @Test
    fun `should receive and process records from two sources`() =
        runBlocking {
            val inWebSocketListener = TestWebSocketListener()
            connectToWebSocket(
                "/v4/timeseries-stream/asset-metrics?assets=btc&metrics=principal_market_price_usd&frequency=1s&backfill=none&api_key=$TEST_API_KEY",
                inWebSocketListener,
            )

            // 1 by 1
            oneByOne(inWebSocketListener)

            // 1 by 1 with gaps
            oneByOneWithGaps(inWebSocketListener)

            // 2 by 2
            twoByTwo(inWebSocketListener)

            // 3 by 3 with gaps
            threeByThreeWithGaps(inWebSocketListener)

            // ignore stale prices during RT Principal Price restart
            ignoreStaleRatesDuringRtRatesRestart(inWebSocketListener)

            // no more messages
            Assertions.assertFalse(inWebSocketListener.hasNextMessage())
        }

    private suspend fun oneByOne(inWebSocketListener: TestWebSocketListener) {
        // A simple test to show that we receive messages from both sources when sending messages 1 by 1
        log.info("starting one by one part")
        (1..8).forEach {
            clock.instant = clock.instant.plusSeconds(1)
            val originalRate =
                createKafkaPrincipalPrice(
                    time = clock.instant,
                )
            val producer =
                if (it % 2 == 0) {
                    log.info("[$it] sending to main: $originalRate")
                    mainProducer
                } else {
                    log.info("[$it] sending to companion: $originalRate")
                    companionProducer
                }
            producer.send(topicName, originalRate)
            assertRate(inWebSocketListener, originalRate, it)
        }
        log.info("ending one by one part")
    }

    private suspend fun oneByOneWithGaps(inWebSocketListener: TestWebSocketListener) {
        // A simple test with gaps to show that we don't wait before accepting the value with gaps
        log.info("starting one by one with gaps part")
        (1..4).forEach {
            clock.instant = clock.instant.plusSeconds(1)
            val originalRate =
                createKafkaPrincipalPrice(
                    time = clock.instant,
                )
            val producer =
                if (it % 2 == 0) {
                    log.info("[$it] sending to main: $originalRate")
                    mainProducer
                } else {
                    log.info("[$it] sending to companion: $originalRate")
                    companionProducer
                }
            producer.send(topicName, originalRate)
            assertRate(inWebSocketListener, originalRate, it)
            log.info("[$it] sleeping 1s...")
            clock.instant = clock.instant.plusSeconds(1)
        }
        log.info("ending one by one with gaps part")
    }

    private suspend fun twoByTwo(inWebSocketListener: TestWebSocketListener) {
        // A simple test to show that we read from both sources when sending messages 2 by 2
        log.info("starting two by two part")
        var counter = 0
        var count = 0
        repeat(8) {
            count++
            counter++
            clock.instant = clock.instant.plusSeconds(1)
            val originalRate =
                createKafkaPrincipalPrice(
                    time = clock.instant,
                )
            val producer =
                if (count <= 2) {
                    log.info("[$counter.$count] sending to main: $originalRate")
                    mainProducer
                } else {
                    log.info("[$counter.$count] sending to companion: $originalRate")
                    companionProducer
                }

            producer.send(topicName, originalRate)
            assertRate(inWebSocketListener, originalRate, "$counter.$count")

            if (count == 4) {
                count = 0
            }
        }
        log.info("ending two by two part")
    }

    private suspend fun threeByThreeWithGaps(inWebSocketListener: TestWebSocketListener) {
        // A simple test with gaps to show that we don't wait for the gaps to be filled in before accepting the subsequent value.
        log.info("starting three by three with gaps part")
        clock.instant = clock.instant.plusSeconds(1)

        val initRate = sendRate(0L, mainProducer)
        assertRate(inWebSocketListener, initRate, 0L)

        for (i in 1L..9L step 3L) {
            val overtakingRate = sendRate(i + 2, companionProducer)
            // lagging rate 1
            sendRate(i, mainProducer)
            // lagging rate 2
            sendRate(i + 1, mainProducer)
            assertRate(inWebSocketListener, overtakingRate, i + 2)
        }

        clock.instant = clock.instant.plusSeconds(9L)
        log.info("ending three by three with gaps part")
    }

    private suspend fun ignoreStaleRatesDuringRtRatesRestart(inWebSocketListener: TestWebSocketListener) {
        log.info("starting simulating rates restart")
        // 1. Everything is up and running
        // 2. Everything is up and running
        // 3. Restart real-time rates, rates are catching up
        // 4. Rates are catching up
        // 5. Everything is up and running
        // 6. Everything is up and running
        (1..6).forEach {
            clock.instant = clock.instant.plusSeconds(1)
            val latestRate =
                createKafkaPrincipalPrice(
                    time = clock.instant,
                )

            val maybeStaleRate =
                if (it == 3 || it == 4) {
                    // on step 3 and 4 we have stale rates
                    createKafkaPrincipalPrice(
                        time = clock.instant.minusSeconds(3),
                    )
                } else {
                    latestRate
                }

            log.info("[$it] sending to main: $maybeStaleRate")
            mainProducer.send(topicName, maybeStaleRate)

            log.info("[$it] sending to companion: $latestRate")
            companionProducer.send(topicName, latestRate)

            assertRate(inWebSocketListener, latestRate, it)
        }
        log.info("ending simulating rates restart")
    }

    private suspend fun sendRate(
        plusSeconds: Long,
        producer: QueueProducer<AssetPrincipalPriceKafkaDataProvider.PrincipalPriceInput>,
    ): AssetPrincipalPriceKafkaDataProvider.PrincipalPriceInput {
        val rate =
            createKafkaPrincipalPrice(
                time = clock.instant.plusSeconds(plusSeconds),
            )

        log.info("[$plusSeconds] sending to queue: $rate")
        producer.send(topicName, rate)

        return rate
    }

    private fun assertRate(
        inWebSocketListener: TestWebSocketListener,
        rate: AssetPrincipalPriceKafkaDataProvider.PrincipalPriceInput,
        logId: Any,
    ) {
        val message = inWebSocketListener.takeNextMessage(15000)
        log.info("[$logId] actual: $message, [$logId] expected: $rate")
        val time = Instant.parse(objectMapper.readTree(message)["time"].asText())
        Assertions.assertEquals(rate.millisSinceEpoch, time.toEpochMilli())
    }

    @AfterAll
    fun tearDownTwoDataSources() {
        runCatching { mainProducer.close() }
        runCatching { companionProducer.close() }
    }
}
