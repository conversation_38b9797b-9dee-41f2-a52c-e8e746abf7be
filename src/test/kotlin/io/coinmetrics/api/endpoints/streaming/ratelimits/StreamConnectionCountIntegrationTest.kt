package io.coinmetrics.api.endpoints.streaming.ratelimits

import io.coinmetrics.api.helper.TEST_API_KEY
import io.coinmetrics.api.modules.ApiModule
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.testcontainers.junit.jupiter.Testcontainers

@Testcontainers
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class StreamConnectionCountIntegrationTest : StreamConnectionCountBaseTest() {
    override fun otherModules(): List<ApiModule> = listOf(mainApiModule, streamingTradesApiModule)

    @Test
    fun `test basic error asset metrics`() {
        doTooManyRequestsErrorTest(
            "/v4/timeseries-stream/asset-metrics?assets=btc&metrics=BlkHgt,AdrActCnt,FlowInGEMNtv&api_key=$TEST_API_KEY&backfill=none",
        )
    }

    @Test
    fun `test basic error quotes`() {
        doTooManyRequestsErrorTest(
            "/v4/timeseries-stream/market-quotes?markets=binance-btc-usdt-spot,binance-BTCUSDT-future&api_key=$TEST_API_KEY&backfill=none",
        )
    }

    @Test
    fun `test basic error candles`() {
        doTooManyRequestsErrorTest("/v4/timeseries-stream/market-candles?markets=binance-btc-usdt-spot&api_key=$TEST_API_KEY&backfill=none")
    }

    @Test
    fun `test basic error index levels`() {
        doTooManyRequestsErrorTest("/v4/timeseries-stream/index-levels?indexes=CMBIBTC&api_key=$TEST_API_KEY&backfill=none")
    }

    @Test
    fun `test basic error trades`() {
        doTooManyRequestsErrorTest("/v4/timeseries-stream/market-trades?markets=bittrex-*&api_key=$TEST_API_KEY&backfill=none")
    }

    private fun doTooManyRequestsErrorTest(wsUri: String) {
        doTooManyRequestsErrorTest(wsUri, commonModule.config.streamingConfig.streamConnectionCountLimitDefault)
    }
}
