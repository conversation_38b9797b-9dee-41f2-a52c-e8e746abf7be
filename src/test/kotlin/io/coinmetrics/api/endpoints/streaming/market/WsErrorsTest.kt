package io.coinmetrics.api.endpoints.streaming.market

import io.coinmetrics.api.endpoints.streaming.market.trade.BaseStreamingTradesTest
import io.coinmetrics.api.helper.TestWebSocketListener
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance

/**
 * You can also test WebSocket errors via Google Chrome DevTools console.
 * Copy & paste the following two lines simultaneously.
 *
 * Locally:
 * w = new WebSocket("ws://localhost:8080/v4/timeseries-stream/market-trades?markets=coinbase-btc-usd-spot")
 * w.onmessage = m => console.log(m.data); w.onclose = () => console.log("closed")
 *
 * Staging:
 * w = new WebSocket("wss://staging-api.coinmetrics.io/v4/timeseries-stream/market-trades?markets=coinbase-btc-usd-spot")
 * w.onmessage = m => console.log(m.data); w.onclose = () => console.log("closed")
 */
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class WsErrorsTest : BaseStreamingTradesTest() {
    @Test
    fun `missing api_key`() {
        val listener = TestWebSocketListener()

        connectToWebSocket("/v4/timeseries-stream/market-trades?markets=binance-btc-usdt-spot", listener)

        val message = listener.takeNextMessage()
        assertEquals("""{"error":{"type":"unauthorized","message":"Requested resource requires authorization."}}""", message)
        assertFalse(listener.hasNextMessage())
        listener.waitForClose(10_000)
    }

    @Test
    fun `missing markets parameter`() {
        val listener = TestWebSocketListener()

        connectToWebSocket("/v4/timeseries-stream/market-trades?api_key=x1", listener)

        val message = listener.takeNextMessage()
        assertEquals("""{"error":{"type":"missing_parameter","message":"Missing parameter 'markets'."}}""", message)
        assertFalse(listener.hasNextMessage())
        listener.waitForClose(10_000)
    }
}
