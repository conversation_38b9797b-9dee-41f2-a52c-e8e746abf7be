package io.coinmetrics.api.endpoints.streaming.market.liquidation

import io.coinmetrics.api.helper.TestWebSocketListener
import io.coinmetrics.api.utils.TimeUtils
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Test
import java.time.Instant
import java.time.temporal.ChronoUnit

class WsLiquidationsTest : WsLiquidationsBaseTest() {
    @Test
    fun `basic receive`() =
        runBlocking {
            val inWebSocketListener = TestWebSocketListener()
            connectToWebSocket(
                "/v4/timeseries-stream/market-liquidations?markets=binance-BTCUSDT-future&api_key=x1&backfill=none",
                inWebSocketListener,
            )

            test(
                inWebSocketListener = inWebSocketListener,
                time = Instant.now().minusSeconds(1),
                liquidationId = 2,
                symbol = "BTCUSDT",
                isOrder = false,
                exchangeId = 4,
                expectedMessageWithPlaceholder = """{"market":"binance-BTCUSDT-future","time":"%time%","coin_metrics_id":"2","amount":"2.01","price":"1.01","type":"trade","side":"buy","cm_sequence_id":"0"}""",
            )

            test(
                inWebSocketListener = inWebSocketListener,
                time = Instant.now(),
                liquidationId = 3,
                symbol = "BTCUSDT",
                isOrder = true,
                exchangeId = 4,
                expectedMessageWithPlaceholder = """{"market":"binance-BTCUSDT-future","time":"%time%","coin_metrics_id":"3","amount":"2.01","price":"1.01","type":"order","side":"buy","cm_sequence_id":"1"}""",
            )
        }

    @Test
    fun `basic receive by pattern`() =
        runBlocking {
            val inWebSocketListener = TestWebSocketListener()

            connectToWebSocket("/v4/timeseries-stream/market-liquidations?markets=bittrex-*&api_key=x1&backfill=none", inWebSocketListener)

            test(
                inWebSocketListener = inWebSocketListener,
                time = Instant.now().minusSeconds(1),
                liquidationId = 2,
                symbol = "XRPUSD",
                isOrder = false,
                exchangeId = 33,
                expectedMessageWithPlaceholder = """{"market":"bittrex-XRPUSD-future","time":"%time%","coin_metrics_id":"2","amount":"2.01","price":"1.01","type":"trade","side":"buy","cm_sequence_id":"0"}""",
            )
        }

    private suspend fun test(
        inWebSocketListener: TestWebSocketListener,
        time: Instant,
        liquidationId: Int,
        symbol: String,
        isOrder: Boolean,
        exchangeId: Int,
        expectedMessageWithPlaceholder: String,
    ) {
        TestLiquidationsUtils
            .createLiquidations(
                startLiquidationId = liquidationId,
                symbol = symbol,
                exchangeTime = time,
                isOrder = isOrder,
            ).sendToKafka(exchangeId)

        val expectedMessage =
            expectedMessageWithPlaceholder.replace(
                "%time%",
                TimeUtils.dateTimeFormatter.format(time.truncatedTo(ChronoUnit.MICROS)),
            )

        val message = inWebSocketListener.takeNextMessage()
        assertEquals(expectedMessage, message)

        // no more messages
        assertFalse(inWebSocketListener.hasNextMessage())
    }

    @Test
    fun `should return empty response when requesting unsupported pattern market`() =
        runBlocking {
            val inWebSocketListener = TestWebSocketListener()
            connectToWebSocket(
                "/v4/timeseries-stream/market-liquidations?markets=zaif-*-option&api_key=x1&backfill=none",
                inWebSocketListener,
            )

            val message = inWebSocketListener.takeNextMessageOrNull(100)
            assertNull(message)
        }

    @Test
    fun `should return empty response when requesting unsupported pattern markets`() =
        runBlocking {
            val inWebSocketListener = TestWebSocketListener()
            connectToWebSocket(
                "/v4/timeseries-stream/market-liquidations?markets=zaif-*-option,simex-CGSEUR-*&api_key=x1&backfill=none",
                inWebSocketListener,
            )

            val message = inWebSocketListener.takeNextMessageOrNull(100)
            assertNull(message)
        }
}
