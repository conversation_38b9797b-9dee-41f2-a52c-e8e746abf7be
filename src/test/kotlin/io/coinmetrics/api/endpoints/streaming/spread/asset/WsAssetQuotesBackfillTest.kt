package io.coinmetrics.api.endpoints.streaming.spread.asset

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import io.coinmetrics.api.endpoints.stream.spread.SpreadQuotesKafkaDataProvider.AggregatedSpreadQuoteInput
import io.coinmetrics.api.helper.TEST_API_KEY
import io.coinmetrics.api.helper.TestWebSocketListener
import io.coinmetrics.api.utils.TimeUtils.dateTimeFormatter
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import java.time.temporal.ChronoUnit.MILLIS

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class WsAssetQuotesBackfillTest : WsAssetQuotesBaseTest() {
    private val mapper = jacksonObjectMapper()

    @Test
    fun `should return the latest quote from history when backfill parameter set to 'latest'`() =
        runBlocking {
            val uri = "/v4/timeseries-stream/asset-quotes?assets=btc&api_key=$TEST_API_KEY"
            val backgroundWsListener = TestWebSocketListener()
            connectToWebSocket("$uri&backfill=none", backgroundWsListener)

            val firstJsonMessage = """{"pair":{"base":0,"quote":3},"time":${timeProvider.now().toEpochMilli()},"ask_price":"24343.725954328216","ask_size":"2.96375165","bid_price":"24342.036360171896","bid_size":"12.00588437","mid_price":"24342.881157250056","spread":"0.0000694081421754166","base_asset":"btc","quote_asset":"usd"}"""
            val firstMessage = mapper.readValue(firstJsonMessage, AggregatedSpreadQuoteInput::class.java)

            val latestTime = timeProvider.now()
            val latestJsonMessage = """{"pair":{"base":0,"quote":3},"time":${latestTime.toEpochMilli()},"ask_price":"24343.09156697901","ask_size":"3.56146766","bid_price":"24342.139333941428","bid_size":"11.103545650000001","mid_price":"24342.615450460216","spread":"0.000039117942750207264","base_asset":"btc","quote_asset":"usd"}"""
            val latestMessage = mapper.readValue(latestJsonMessage, AggregatedSpreadQuoteInput::class.java)

            producer.send(ASSET_QUOTES_TOPIC_NAME, firstMessage)
            backgroundWsListener.takeNextMessage()
            producer.send(ASSET_QUOTES_TOPIC_NAME, latestMessage)
            backgroundWsListener.takeNextMessage()

            val actualListener = TestWebSocketListener()
            connectToWebSocket("$uri&backfill=latest", actualListener)

            with(latestMessage) {
                val expectedTime = dateTimeFormatter.format(latestTime.truncatedTo(MILLIS))
                assertEquals(
                    """{"pair":"btc-usd","time":"$expectedTime","ask_price":"$askPrice","ask_size":"$askSize","bid_price":"$bidPrice","bid_size":"$bidSize","mid_price":"$midPrice","spread":"$spread","cm_sequence_id":"0"}""",
                    actualListener.takeNextMessage(),
                )
            }

            // no more messages
            assertFalse(actualListener.hasNextMessage())
        }
}
