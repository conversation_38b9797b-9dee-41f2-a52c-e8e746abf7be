package io.coinmetrics.api.endpoints.streaming.market.orderbooks

import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.node.ObjectNode
import com.fasterxml.jackson.databind.node.TextNode
import io.coinmetrics.api.endpoints.stream.market.orderbooks.test.TestPosition
import io.coinmetrics.api.helper.BaseTest
import io.coinmetrics.api.helper.TEST_API_KEY
import io.coinmetrics.api.helper.TestWebSocketListener
import io.coinmetrics.api.modules.ApiModule
import io.coinmetrics.api.modules.streamingbooks.StreamingBooksApiConfig
import io.coinmetrics.api.modules.streamingbooks.StreamingBooksApiModule
import io.coinmetrics.bookstreams.Book
import io.coinmetrics.bookstreams.BookMessage
import io.coinmetrics.bookstreams.BookMessages
import io.coinmetrics.bookstreams.BookTopic
import io.coinmetrics.bookstreams.Depth
import kotlinx.coroutines.channels.Channel
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Assertions.assertTrue
import java.util.stream.Stream

abstract class BaseGetTimeseriesStreamMarketOrderbooksEndpointImplTest : BaseTest() {
    private val channel = Channel<BookMessages>(1000)

    inner class WsReceiver(
        private val notPretty: TestWebSocketListener,
        private val pretty: TestWebSocketListener,
    ) {
        private var nextCmSequenceId: Int = 0
        private val notPrettyCmIds = mutableSetOf<String>()
        private val prettyCmIds = mutableSetOf<String>()

        /**
         * Returns messages separated with `\n`.
         */
        fun receive(count: Int): String {
            class Context(
                val listener: TestWebSocketListener,
                val pretty: Boolean,
                var nextCmSequenceId: Int,
                val coinMetricsIds: MutableSet<String>,
            ) {
                val marketToMessages: MutableMap<String, MutableList<String>> = mutableMapOf()

                fun receiveOne(messageId: Int) {
                    while (true) {
                        val msgStr = listener.takeNextMessageOrNull(5000)
                        if (msgStr == null) {
                            log.warn(
                                "Still waiting for message $messageId/$count " +
                                    "(${if (pretty) "pretty" else "not pretty"})...",
                            )
                            continue
                        }
                        val msgTree = objectMapper.readTree(msgStr)

                        if (!pretty) {
                            log.info(
                                "Received message $messageId:\n" +
                                    objectMapper.writer().withDefaultPrettyPrinter().writeValueAsString(msgTree),
                            )
                        }

                        if (pretty) {
                            assertEquals(
                                msgStr,
                                objectMapper.writer().withDefaultPrettyPrinter().writeValueAsString(msgTree),
                            )
                        } else {
                            assertEquals(
                                msgStr,
                                objectMapper.writer().writeValueAsString(msgTree),
                            )
                        }

                        msgTree as ObjectNode

                        val cmSequenceId = (msgTree.get("cm_sequence_id") as TextNode).textValue().toInt()
                        assertEquals(nextCmSequenceId, cmSequenceId)
                        nextCmSequenceId += 1
                        msgTree.set<JsonNode>("cm_sequence_id", TextNode("<<VALID>>"))

                        val coinMetricsId = (msgTree.get("coin_metrics_id") as TextNode).textValue()
                        if (!coinMetricsId.startsWith("exchangeProvidedId:")) {
                            assertTrue(coinMetricsIds.add(coinMetricsId))
                            msgTree.set<JsonNode>("coin_metrics_id", TextNode("<<VALID>>"))
                        }

                        marketToMessages
                            .computeIfAbsent((msgTree.get("market") as TextNode).textValue()) {
                                mutableListOf()
                            }.add(objectMapper.writeValueAsString(msgTree))

                        break
                    }
                }
            }
            val notPrettyContext = Context(notPretty, pretty = false, nextCmSequenceId, notPrettyCmIds)
            val prettyContext = Context(pretty, pretty = true, nextCmSequenceId, prettyCmIds)

            repeat(count) {
                val messageId = it + 1
                notPrettyContext.receiveOne(messageId)
                prettyContext.receiveOne(messageId)

                assertEquals(notPrettyCmIds, prettyCmIds)
                nextCmSequenceId += 1
            }

            Stream
                .of(pretty, notPretty)
                .parallel()
                .forEach {
                    val message = it.takeNextMessageOrNull(200)
                    assertNull(message) {
                        "Unexpected message received:\n" +
                            objectMapper
                                .writer()
                                .withDefaultPrettyPrinter()
                                .writeValueAsString(objectMapper.readTree(message))
                    }
                }

            // Messages in both contexts are normalized and expected to be equal.
            val notPrettyMessages =
                notPrettyContext.marketToMessages.entries
                    .sortedBy { (market, _) -> market }
                    .map { (_, messages) -> messages }
                    .flatten()
            val prettyMessages =
                prettyContext.marketToMessages.entries
                    .sortedBy { (market, _) -> market }
                    .map { (_, messages) -> messages }
                    .flatten()
            assertEquals(notPrettyMessages, prettyMessages)

            return notPrettyMessages.joinToString("\n")
        }
    }

    protected val streamingBooksApiModule by lazy {
        StreamingBooksApiModule(
            common = commonModule,
            config = streamingBooksApiConfig(),
        )
    }

    override fun otherModules(): List<ApiModule> = listOf(mainApiModule, streamingBooksApiModule)

    override fun getAdditionalEnvVariables(): Map<String, String> =
        mapOf(
            "KAFKA_BOOKS_0" to "dummy:12345",
            "KAFKA_BOOKS_0_EXCHANGES" to "0, 1, 2, 4, 5, 6, 7, 9, 10, 20, 28, 31, 32, 33, 34, 35, 37, 38, 39, 40, 41, 42, 46",
        )

    protected open fun streamingBooksApiConfig() =
        StreamingBooksApiConfig(
            commonConfig = commonModule.config,
            testBookMessageChannel = channel,
        )

    fun createWsReceiver(
        markets: String,
        backfill: String = "none",
        depthLimit: String,
    ): WsReceiver {
        fun create(pretty: Boolean): TestWebSocketListener {
            val listener = TestWebSocketListener()
            val backfillParameter = if (backfill == "latest") "" else "&backfill=$backfill"
            val prettyParameter = if (pretty) "&pretty=true" else ""
            connectToWebSocket(
                "/v4/timeseries-stream/market-orderbooks?markets=$markets&depth_limit=$depthLimit&" +
                    "api_key=$TEST_API_KEY$backfillParameter$prettyParameter",
                listener,
            )
            return listener
        }

        return WsReceiver(create(pretty = false), create(pretty = true))
    }

    suspend fun send(
        depth: Depth,
        books: Iterable<Book>,
    ) = send(depth, initialSnapshot = false, books)

    suspend fun send(
        depth: Depth,
        vararg books: Book,
    ) = send(depth, books.toList())

    suspend fun send(
        depth: Depth,
        initialSnapshot: Boolean,
        books: Iterable<Book>,
    ) {
        val exchangeIdToBooks = books.groupBy { it.marketId.exchangeId }
        for ((exchangeId, exchangeBooks) in exchangeIdToBooks) {
            val topic = BookTopic(exchangeId, depth)
            val messages =
                exchangeBooks.map {
                    if (initialSnapshot) {
                        require(it.isSnapshot)
                        BookMessage.InitialSnapshot(it, topic, TestPosition())
                    } else {
                        BookMessage.Update(it, topic, TestPosition())
                    }
                }
            channel.send(BookMessages(topic, messages))
        }
    }
}
