package io.coinmetrics.api.endpoints.streaming.asset.rates

import io.coinmetrics.api.endpoints.stream.asset.AssetRatesKafkaDataProvider.RateInput
import io.coinmetrics.api.resources.Resources
import io.coinmetrics.proto.MarketDataFeed
import java.time.Instant

object WsRefRatesTestUtils {
    fun createReferenceRate(
        assetId: String = "btc",
        time: Instant = Instant.now(),
        price: Double = 1.01,
        quote: String = "usd",
    ): MarketDataFeed.RateEntry =
        MarketDataFeed.RateEntry
            .newBuilder()
            .setAsset(assetId)
            .setTimeMillis(time.toEpochMilli())
            .setPrice(price)
            .setQuote(quote)
            .build()

    fun createKafkaReferenceRate(
        assetId: String = "btc",
        time: Instant = Instant.now(),
        price: Double = 1.01,
        quote: String = "usd",
        createdAt: Long = System.currentTimeMillis(),
    ): RateInput =
        RateInput(
            base = Resources.getCurrencyInfo(assetId)!!.id,
            quote = Resources.getCurrencyInfo(quote)!!.id,
            millisSinceEpoch = time.toEpochMilli(),
            price = price,
            createdAt = createdAt,
        )

    fun createKafkaReferenceRate(
        assetId: Int,
        time: Instant = Instant.now(),
        price: Double = 1.01,
        quote: String = "usd",
        createdAt: Long = System.currentTimeMillis(),
    ): RateInput =
        RateInput(
            base = assetId,
            quote = Resources.getCurrencyInfo(quote)!!.id,
            millisSinceEpoch = time.toEpochMilli(),
            price = price,
            createdAt = createdAt,
        )
}
