package io.coinmetrics.api.endpoints.streaming.market.orderbooks

import io.coinmetrics.api.endpoints.stream.market.orderbooks.StreamingBook
import io.coinmetrics.api.endpoints.stream.market.orderbooks.test.TestBook
import io.coinmetrics.api.endpoints.stream.market.orderbooks.test.TestPriceLevel
import io.coinmetrics.bookstreams.MarketId
import io.coinmetrics.bookstreams.MarketKind
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import java.time.Instant
import java.util.UUID

class StreamingBookTest {
    @Test
    fun coinMetricsId() {
        assertEquals(
            "exchange SEquEnCE id 12345",
            StreamingBook.CoinMetricsId.ExchangeProvided("exchange SEquEnCE id 12345").toString(),
        )
        assertEquals(
            "AQIDBAUGBwgACQoLDA0ODxgnNkVUY3KB",
            StreamingBook.CoinMetricsId
                .Generated(
                    UUID(0x0102030405060708, 0x090a0b0c0d0e0f),
                    0x1827364554637281,
                ).toString(),
        )
        // Check `-` and `_` characters.
        assertEquals(
            "AAAAAAAAAP4AAAAAAAAA_gAAAAAAAAD-",
            StreamingBook.CoinMetricsId
                .Generated(
                    UUID(254, 254),
                    254,
                ).toString(),
        )
    }

    @Test
    fun toJson() {
        val book =
            TestBook(
                MarketId(123, "test symbol", MarketKind.Option),
                receivedFromExchangeTime = Instant.parse("2024-03-04T01:07:36.000010001Z"),
                exchangeProvidedId = "exchange sequence id",
                exchangeProvidedTime = Instant.parse("2024-03-04T01:07:36.102010301Z"),
                isSnapshot = true,
                bids =
                    listOf(
                        TestPriceLevel("23.1456", "3.2456"),
                    ),
                asks =
                    listOf(
                        TestPriceLevel("123.456", "123.456"),
                        TestPriceLevel("789.01", "1.45"),
                    ),
            )

        // CoinMetricsId.Generated
        var streamingBook =
            StreamingBook(
                market = "${book.marketId.exchangeId}|${book.marketId.symbol}|${book.marketId.kind}",
                StreamingBook.CoinMetricsId.Generated(UUID(12345, 6789), 42),
                book,
                initialSnapshot = false,
            )
        assertEquals(
            """{"market":"123|test symbol|Option","time":"2024-03-04T01:07:36.102010301Z","coin_metrics_id":"AAAAAAAAMDkAAAAAAAAahQAAAAAAAAAq","asks":[{"price":"123.456","size":"123.456"},{"price":"789.01","size":"1.45"}],"bids":[{"price":"23.1456","size":"3.2456"}],"type":"snapshot","collect_time":"2024-03-04T01:07:36.000010001Z","cm_sequence_id":"123456"}""",
            streamingBook.toJson("123456", pretty = false),
        )
        assertEquals(
            """{
            |  "market" : "123|test symbol|Option",
            |  "time" : "2024-03-04T01:07:36.102010301Z",
            |  "coin_metrics_id" : "AAAAAAAAMDkAAAAAAAAahQAAAAAAAAAq",
            |  "asks" : [ {
            |    "price" : "123.456",
            |    "size" : "123.456"
            |  }, {
            |    "price" : "789.01",
            |    "size" : "1.45"
            |  } ],
            |  "bids" : [ {
            |    "price" : "23.1456",
            |    "size" : "3.2456"
            |  } ],
            |  "type" : "snapshot",
            |  "collect_time" : "2024-03-04T01:07:36.000010001Z",
            |  "cm_sequence_id" : "123456"
            |}
            """.trimMargin(),
            streamingBook.toJson("123456", pretty = true),
        )

        // CoinMetricsId.ExchangeProvided
        streamingBook =
            StreamingBook(
                market = "${book.marketId.exchangeId}|${book.marketId.symbol}|${book.marketId.kind}",
                StreamingBook.CoinMetricsId.ExchangeProvided("exchange provided id"),
                book,
                initialSnapshot = false,
            )
        assertEquals(
            """{"market":"123|test symbol|Option","time":"2024-03-04T01:07:36.102010301Z","coin_metrics_id":"exchange provided id","asks":[{"price":"123.456","size":"123.456"},{"price":"789.01","size":"1.45"}],"bids":[{"price":"23.1456","size":"3.2456"}],"type":"snapshot","collect_time":"2024-03-04T01:07:36.000010001Z","cm_sequence_id":"123456"}""",
            streamingBook.toJson("123456", pretty = false),
        )
        assertEquals(
            """{
            |  "market" : "123|test symbol|Option",
            |  "time" : "2024-03-04T01:07:36.102010301Z",
            |  "coin_metrics_id" : "exchange provided id",
            |  "asks" : [ {
            |    "price" : "123.456",
            |    "size" : "123.456"
            |  }, {
            |    "price" : "789.01",
            |    "size" : "1.45"
            |  } ],
            |  "bids" : [ {
            |    "price" : "23.1456",
            |    "size" : "3.2456"
            |  } ],
            |  "type" : "snapshot",
            |  "collect_time" : "2024-03-04T01:07:36.000010001Z",
            |  "cm_sequence_id" : "123456"
            |}
            """.trimMargin(),
            streamingBook.toJson("123456", pretty = true),
        )
    }
}
