package io.coinmetrics.api.endpoints.streaming.market.trade

import io.coinmetrics.api.helper.TestWebSocketListener
import io.coinmetrics.api.utils.TimeUtils
import io.coinmetrics.api.utils.awaitUntilAsserted
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Test
import java.time.Instant
import java.time.temporal.ChronoUnit

class WsTradesOutOfOrderLatestBackfill : WsTradesBaseTest() {
    companion object {
        private const val EXCHANGE_ID = 4
    }

    @Test
    fun `out of order latest backfill`() =
        runBlocking {
            val backgroundListener = TestWebSocketListener() // needed to make sure the message is received by the server
            connectToWebSocket("/v4/timeseries-stream/market-trades?markets=binance-btc-usdt-spot&api_key=x1", backgroundListener)

            val time1 = Instant.now()
            TestTradesUtils.createTrades(exchangeTime = time1, scraperTime = time1, startTradeId = 2).sendToKafka(EXCHANGE_ID)
            backgroundListener.takeNextMessage()

            val time2 = time1.minusMillis(10)
            TestTradesUtils.createTrades(exchangeTime = time2, scraperTime = time2, startTradeId = 1).sendToKafka(EXCHANGE_ID)
            backgroundListener.takeNextMessage()

            // get the latest trade
            val inWebSocketListener = TestWebSocketListener()
            connectToWebSocket("/v4/timeseries-stream/market-trades?markets=binance-btc-usdt-spot&api_key=x1", inWebSocketListener)
            // test that message is received, and no timeout is reached

            awaitUntilAsserted {
                val trade = inWebSocketListener.takeNextMessage()
                val expectedTime = TimeUtils.dateTimeFormatter.format(time1.truncatedTo(ChronoUnit.MICROS))
                assertEquals(
                    """{"market":"binance-btc-usdt-spot","time":"$expectedTime","coin_metrics_id":"2","amount":"2.01","price":"1.01","collect_time":"$expectedTime","side":"buy","cm_sequence_id":"0"}""",
                    trade,
                )
            }
            assertFalse(inWebSocketListener.hasNextMessage())
        }
}
