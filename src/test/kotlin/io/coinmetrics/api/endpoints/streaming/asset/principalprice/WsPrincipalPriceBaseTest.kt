package io.coinmetrics.api.endpoints.streaming.asset.principalprice

import io.coinmetrics.api.endpoints.stream.asset.AssetPrincipalPriceKafkaDataProvider
import io.coinmetrics.api.endpoints.streaming.asset.principalprice.WsPrincipalPriceTestUtils.createKafkaPrincipalPrice
import io.coinmetrics.api.helper.BaseTest
import io.coinmetrics.api.helper.Containers
import io.coinmetrics.api.helper.TestWebSocketListener
import io.coinmetrics.api.modules.main.MainApiConfig
import io.coinmetrics.api.utils.awaitUntilAsserted
import io.coinmetrics.queues.QueueProducer
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.AfterAll
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.TestInstance
import java.time.Instant

const val PRINCIPAL_PRICE_TOPIC_NAME = "principal_price_realtime_all"

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
open class WsPrincipalPriceBaseTest : BaseTest() {
    companion object {
        val testPrincipalPriceTopics = listOf(PRINCIPAL_PRICE_TOPIC_NAME)
    }

    protected val producer by lazy {
        QueueProducer<AssetPrincipalPriceKafkaDataProvider.PrincipalPriceInput>(
            host = Containers.kafka1.value.host,
            port = Containers.kafka1.value.port,
        )
    }

    override fun mainApiConfig(): MainApiConfig =
        super.mainApiConfig().copy(
            principalPriceConfigs =
                testPrincipalPriceTopics.map { topicName ->
                    MainApiConfig.KafkaSourceConfig(
                        configName = MainApiConfig.PRINCIPAL_PRICE_CONFIG_NAME,
                        topicName = topicName,
                        env = "dev",
                        kafkaServers =
                            MainApiConfig
                                .KafkaConfig(
                                    configName = "KAFKA_PRINCIPAL_PRICE",
                                    env = "dev",
                                    serverUrl = "${Containers.kafka1.value.host}:${Containers.kafka1.value.port}",
                                ).toListOfAllKafkaServers(),
                    )
                },
        )

    protected suspend fun executeBaseTest(
        asset: String,
        quote: String,
        frequency: String,
        apiKey: String,
        time: Instant,
        expectedConnectionCount: Int,
        expectedMessages: List<String>,
    ) {
        val inWebSocketListener = TestWebSocketListener()
        val metricName = "principal_market_price_$quote"
        connectToWebSocket(
            "/v4/timeseries-stream/asset-metrics?assets=$asset&metrics=$metricName&frequency=$frequency&api_key=$apiKey&backfill=none",
            inWebSocketListener,
        )

        val clientConnections = mainApiModule.assetPrincipalPriceDataProviders.first().clientConnections
        val connections = clientConnections.connections

        awaitUntilAsserted {
            /**
             * We need to wait for connections
             * because sometimes we push messages to Kafka faster
             * than we add connections to the list
             */
            val connectionCount =
                connections.connections.count {
                    it.interestedAssets.contains(asset) && it.interestedMetrics.contains(metricName) && it.interestedFrequency == frequency
                }
            Assertions.assertEquals(expectedConnectionCount, connectionCount)
        }
        val principalPriceInput = createKafkaPrincipalPrice(assetId = asset, quote = quote, time = time)
        producer.send(testPrincipalPriceTopics.first(), principalPriceInput)

        for (i in expectedMessages.indices) {
            val message = inWebSocketListener.takeNextMessage()
            Assertions.assertEquals(expectedMessages[i], message) {
                "Index $i failed."
            }
        }
        // no more messages
        Assertions.assertFalse(inWebSocketListener.hasNextMessage())
    }

    @BeforeEach
    fun setUp() =
        runBlocking {
            mainApiModule.assetPrincipalPriceDataProviders.forEach { provider ->
                provider.dataSources.forEach { it.consumer.nextMessageOffset() }
            }
        }

    @AfterAll
    fun tearDown() {
        producer.close()
    }
}
