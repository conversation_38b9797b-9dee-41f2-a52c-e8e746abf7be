package io.coinmetrics.api.endpoints.streaming.market.trade

import io.coinmetrics.api.helper.TestWebSocketListener
import io.coinmetrics.api.utils.TimeUtils
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Test
import java.time.Instant
import java.time.temporal.ChronoUnit

class WsTradesDeribitTest : WsTradesBaseTest() {
    private val markPrice = "0.321"
    private val indexPrice = "0.123"
    private val ivTrade = "321.123"
    private val liquidation = "123.321"

    @Test
    fun `receive spot`() =
        runBlocking {
            val inWebSocketListener = TestWebSocketListener()
            connectToWebSocket(
                "/v4/timeseries-stream/market-trades?markets=deribit-btc-usdc-spot&api_key=x1&backfill=none",
                inWebSocketListener,
            )

            val time = Instant.now()
            val scraperTime = time.plusSeconds(1)

            // Case #1: with base/quote
            TestTradesUtils
                .createTrades(
                    baseId = 0,
                    quoteId = 912,
                    exchangeTime = time,
                    scraperTime = scraperTime,
                    markPrice = markPrice,
                    indexPrice = indexPrice,
                    ivTrade = ivTrade,
                    liquidation = liquidation,
                ).sendToKafka(37)

            val expectedMessage =
                """{"market":"deribit-btc-usdc-spot","time":"${TimeUtils.dateTimeFormatter.format(
                    time.truncatedTo(ChronoUnit.MICROS),
                )}","coin_metrics_id":"1","amount":"2.01","price":"1.01","collect_time":"${TimeUtils.dateTimeFormatter.format(
                    scraperTime.truncatedTo(ChronoUnit.MICROS),
                )}","side":"buy","mark_price":"0.321","index_price":"0.123","iv_trade":"321.123","liquidation":"123.321","cm_sequence_id":"0"}"""

            val message = inWebSocketListener.takeNextMessage()
            assertEquals(expectedMessage, message)

            // Case #2: with symbol
            val time2 = time.plusSeconds(5)
            val scraperTime2 = scraperTime.plusSeconds(5)
            TestTradesUtils
                .createTrades(
                    startTradeId = 2,
                    baseId = null,
                    quoteId = null,
                    symbol = "BTC_USDC",
                    exchangeTime = time2,
                    scraperTime = scraperTime2,
                    markPrice = markPrice,
                    indexPrice = indexPrice,
                    ivTrade = ivTrade,
                    liquidation = liquidation,
                ).sendToKafka(37)

            val expectedMessage2 =
                """{"market":"deribit-btc-usdc-spot","time":"${TimeUtils.dateTimeFormatter.format(
                    time2.truncatedTo(ChronoUnit.MICROS),
                )}","coin_metrics_id":"2","amount":"2.01","price":"1.01","collect_time":"${TimeUtils.dateTimeFormatter.format(
                    scraperTime2.truncatedTo(ChronoUnit.MICROS),
                )}","side":"buy","mark_price":"0.321","index_price":"0.123","iv_trade":"321.123","liquidation":"123.321","cm_sequence_id":"1"}"""

            val message2 = inWebSocketListener.takeNextMessage()
            assertEquals(expectedMessage2, message2)

            // no more messages
            assertFalse(inWebSocketListener.hasNextMessage())
        }

    @Test
    fun `receive futures`() =
        runBlocking {
            val inWebSocketListener = TestWebSocketListener()
            connectToWebSocket(
                "/v4/timeseries-stream/market-trades?markets=deribit-ETH-PERPETUAL-future&api_key=x1&backfill=none",
                inWebSocketListener,
            )

            val time = Instant.now()
            val scraperTime = time.plusSeconds(1)

            TestTradesUtils
                .createDerivativesTradesNew(
                    type = "future",
                    symbol = "ETH-PERPETUAL",
                    exchangeTime = time,
                    scraperTime = scraperTime,
                    markPrice = markPrice,
                    indexPrice = indexPrice,
                    ivTrade = ivTrade,
                    liquidation = liquidation,
                ).sendToKafka(37)

            val expectedMessages =
                listOf(
                    """{"market":"deribit-ETH-PERPETUAL-future","time":"${TimeUtils.dateTimeFormatter.format(
                        time.truncatedTo(ChronoUnit.MICROS),
                    )}","coin_metrics_id":"1","amount":"2.01","price":"1.01","collect_time":"${TimeUtils.dateTimeFormatter.format(
                        scraperTime.truncatedTo(ChronoUnit.MICROS),
                    )}","side":"buy","mark_price":"0.321","index_price":"0.123","iv_trade":"321.123","liquidation":"123.321","cm_sequence_id":"0"}""",
                )

            for (i in expectedMessages.indices) {
                val message = inWebSocketListener.takeNextMessage()
                assertEquals(expectedMessages[i], message) {
                    "Index $i failed."
                }
            }

            // no more messages
            assertFalse(inWebSocketListener.hasNextMessage())
        }

    @Test
    fun `receive option`() =
        runBlocking {
            val inWebSocketListener = TestWebSocketListener()
            connectToWebSocket(
                "/v4/timeseries-stream/market-trades?markets=deribit-BTC-21JUN24-70000-P-option&api_key=x1&backfill=none",
                inWebSocketListener,
            )

            val time = Instant.now()
            val scraperTime = time.plusSeconds(1)

            TestTradesUtils
                .createDerivativesTradesNew(
                    type = "option",
                    symbol = "BTC-21JUN24-70000-P",
                    exchangeTime = time,
                    scraperTime = scraperTime,
                    markPrice = markPrice,
                    indexPrice = indexPrice,
                    ivTrade = ivTrade,
                    liquidation = liquidation,
                ).sendToKafka(37)

            val expectedMessages =
                listOf(
                    """{"market":"deribit-BTC-21JUN24-70000-P-option","time":"${TimeUtils.dateTimeFormatter.format(
                        time.truncatedTo(ChronoUnit.MICROS),
                    )}","coin_metrics_id":"1","amount":"2.01","price":"1.01","collect_time":"${TimeUtils.dateTimeFormatter.format(
                        scraperTime.truncatedTo(ChronoUnit.MICROS),
                    )}","side":"buy","mark_price":"0.321","index_price":"0.123","iv_trade":"321.123","liquidation":"123.321","cm_sequence_id":"0"}""",
                )

            for (i in expectedMessages.indices) {
                val message = inWebSocketListener.takeNextMessage()
                assertEquals(expectedMessages[i], message) {
                    "Index $i failed."
                }
            }

            // no more messages
            assertFalse(inWebSocketListener.hasNextMessage())
        }
}
