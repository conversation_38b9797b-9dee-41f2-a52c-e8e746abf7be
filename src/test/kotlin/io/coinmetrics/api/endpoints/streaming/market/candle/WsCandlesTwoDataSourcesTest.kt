package io.coinmetrics.api.endpoints.streaming.market.candle

import com.fasterxml.jackson.databind.ObjectMapper
import io.coinmetrics.api.helper.CANDLES_TOPIC_NAME
import io.coinmetrics.api.helper.Containers
import io.coinmetrics.api.helper.TEST_API_KEY
import io.coinmetrics.api.helper.TestWebSocketListener
import io.coinmetrics.api.modules.main.MainApiConfig
import io.coinmetrics.proto.MarketDataFeed
import io.coinmetrics.queues.QueueProducer
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.AfterAll
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import java.time.Instant

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class WsCandlesTwoDataSourcesTest : WsCandlesBaseTest() {
    private val mainProducer by lazy {
        QueueProducer<MarketDataFeed.MarketCandle>(
            host = Containers.kafka1.value.host,
            port = Containers.kafka1.value.port,
        )
    }
    private val companionProducer by lazy {
        QueueProducer<MarketDataFeed.MarketCandle>(
            host = Containers.kafka2.value.host,
            port = Containers.kafka2.value.port,
        )
    }

    init {
        clock.instant = Instant.parse("2024-03-19T10:00:00.000Z")
    }

    override fun mainApiConfig(): MainApiConfig =
        super.mainApiConfig().let { config ->
            config.copy(
                candlesConfig =
                    config.candlesConfig.copy(
                        kafkaServers =
                            MainApiConfig
                                .KafkaConfig(
                                    configName = "KAFKA_CANDLES",
                                    env = "dev",
                                    serverUrl = "${Containers.kafka1.value.host}:${Containers.kafka1.value.port},${Containers.kafka2.value.host}:${Containers.kafka2.value.port}",
                                ).toListOfAllKafkaServers(),
                    ),
            )
        }

    @Test
    fun `should receive and process records from two sources`() =
        runBlocking {
            val inWebSocketListener = TestWebSocketListener()
            connectToWebSocket(
                "/v4/timeseries-stream/market-candles?markets=binance-btc-usdt-spot&api_key=$TEST_API_KEY&backfill=none",
                inWebSocketListener,
            )

            // 1 by 1
            oneByOne(inWebSocketListener)

            // 2 by 2
            twoByTwo(inWebSocketListener)

            // ignore stale candles during RT Candles restart
            ignoreStaleCandlesDuringRtCandlesRestart(inWebSocketListener)

            // no more messages
            Assertions.assertFalse(inWebSocketListener.hasNextMessage())
        }

    private suspend fun oneByOne(inWebSocketListener: TestWebSocketListener) {
        // A simple test to show that we switch between sources when sending messages 1 by 1
        log.info("starting one by one part")
        clock.instant = clock.instant.plusMillis(300)
        val objectMapper = ObjectMapper()
        (1..100).forEach {
            val originalCandle =
                createCandle(
                    time = clock.instant.minusMillis(300),
                    marketType = MarketDataFeed.MarketCandle.MarketTypes.SPOT,
                    baseId = 0,
                    quoteId = 100,
                )
            val producer =
                if (it % 2 == 0) {
                    log.info("[$it] sending to main")
                    mainProducer
                } else {
                    log.info("[$it] sending to companion")
                    companionProducer
                }
            producer.sendBytes(CANDLES_TOPIC_NAME, originalCandle.toByteArray())
            val message = inWebSocketListener.takeNextMessage()
            log.info("[$it] actual: $message, [$it] expected: $originalCandle")
            val time = Instant.parse(objectMapper.readTree(message)["time"].asText())
            assertEquals(originalCandle.startTime, time.toEpochMilli())
            log.info("[$it] sleeping 500 ms...")
            clock.instant = clock.instant.plusMillis(500)
        }
        log.info("ending one by one part")
    }

    private suspend fun twoByTwo(inWebSocketListener: TestWebSocketListener) {
        // A simple test to show that we switch between sources when sending messages 2 by 2
        log.info("starting two by two part")
        clock.instant = clock.instant.plusMillis(300)
        val objectMapper = ObjectMapper()
        var counter = 0
        var count = 0
        while (counter < 100) {
            count++
            counter++
            val originalCandle =
                createCandle(
                    time = clock.instant.minusMillis(300).plusMillis(count.toLong()),
                    marketType = MarketDataFeed.MarketCandle.MarketTypes.SPOT,
                    baseId = 0,
                    quoteId = 100,
                )
            // Making sure that big candles don't make switching
            val bigCandle =
                createCandle(
                    time = clock.instant.minusSeconds(5 * 60).plusMillis(count.toLong()),
                    marketType = MarketDataFeed.MarketCandle.MarketTypes.SPOT,
                    baseId = 0,
                    quoteId = 100,
                    intervalMinutes = 5,
                )
            val producer =
                if (count <= 2) {
                    log.info("[$counter.$count] sending to main")
                    mainProducer
                } else {
                    log.info("[$counter.$count] sending to companion")
                    companionProducer
                }

            producer.sendBytes(CANDLES_TOPIC_NAME, originalCandle.toByteArray())
            producer.sendBytes(CANDLES_TOPIC_NAME, bigCandle.toByteArray())

            val message = inWebSocketListener.takeNextMessage()
            log.info("[$counter.$count] actual:\n$message\n[$counter.$count] expected:\n$originalCandle")
            val time = Instant.parse(objectMapper.readTree(message)["time"].asText())
            assertEquals(originalCandle.startTime, time.toEpochMilli())

            if (count % 2 == 0) {
                log.info("[$counter.$count] sleeping 500 ms...")
                clock.instant = clock.instant.plusMillis(500)
            }
            if (count == 4) {
                count = 0
            }
        }
        log.info("ending two by two part")
    }

    private suspend fun ignoreStaleCandlesDuringRtCandlesRestart(inWebSocketListener: TestWebSocketListener) {
        log.info("starting simulating candles restart")
        clock.instant = clock.instant.plusMillis(300)
        val objectMapper = ObjectMapper()
        // 1. Everything is up and running
        // 2. Everything is up and running
        // 3. Restart real-time candles, spot and future candles are catching up
        // 4. Future candles get caught up faster
        // 5. Everything is up and running
        // 6. Everything is up and running
        (1..6).forEach {
            val spotCandle =
                createCandle(
                    time = clock.instant.minusMillis(300),
                    marketType = MarketDataFeed.MarketCandle.MarketTypes.SPOT,
                    baseId = 0,
                    quoteId = 100,
                )
            val futureCandle =
                createCandle(
                    time = clock.instant.minusMillis(300),
                    marketType = MarketDataFeed.MarketCandle.MarketTypes.FUTURE,
                    symbol = "BTCUSDT",
                )
            val latestCandles = listOf(spotCandle, futureCandle)

            val maybeStaleCandles =
                if (it == 3 || it == 4) {
                    // on step 3 we have both spot and future stale candles
                    val spotStaleCandle =
                        createCandle(
                            time = clock.instant.minusSeconds(2 * 60),
                            marketType = MarketDataFeed.MarketCandle.MarketTypes.SPOT,
                            baseId = 0,
                            quoteId = 100,
                        )
                    val futureMaybeStaleCandle =
                        if (it == 3) {
                            createCandle(
                                time = clock.instant.minusSeconds(2 * 60),
                                marketType = MarketDataFeed.MarketCandle.MarketTypes.FUTURE,
                                symbol = "BTCUSDT",
                            )
                        } else {
                            // on step 4 future candles get caught up but spot ones are still catching up
                            futureCandle
                        }

                    listOf(spotStaleCandle, futureMaybeStaleCandle)
                } else {
                    latestCandles
                }

            log.info("[$it] sending to main")
            maybeStaleCandles.forEach { maybeStaleCandle ->
                mainProducer.sendBytes(CANDLES_TOPIC_NAME, maybeStaleCandle.toByteArray())
            }

            log.info("[$it] sending to companion")
            latestCandles.forEach { latestCandle ->
                companionProducer.sendBytes(CANDLES_TOPIC_NAME, latestCandle.toByteArray())
            }

            val message = inWebSocketListener.takeNextMessage()
            log.info("[$it] actual: $message, [$it] expected: $latestCandles")
            val time = Instant.parse(objectMapper.readTree(message)["time"].asText())
            assertEquals(spotCandle.startTime, time.toEpochMilli())
            log.info("[$it] sleeping 500 ms...")
            clock.instant = clock.instant.plusMillis(500)
        }
        log.info("ending simulating candles restart")
    }

    @AfterAll
    override fun tearDown() {
        mainProducer.close()
        companionProducer.close()
    }
}
