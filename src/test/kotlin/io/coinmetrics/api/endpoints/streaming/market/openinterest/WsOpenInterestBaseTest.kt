package io.coinmetrics.api.endpoints.streaming.market.openinterest

import io.coinmetrics.api.helper.BaseTest
import io.coinmetrics.api.helper.Containers
import io.coinmetrics.api.modules.main.MainApiConfig
import io.coinmetrics.api.resources.Resources
import io.coinmetrics.proto.MarketDataFeed
import io.coinmetrics.queues.QueueProducer
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.AfterAll
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.TestInstance

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
open class WsOpenInterestBaseTest : BaseTest() {
    companion object {
        val supportedExchangeIds = listOf(1, 4, 10, 33, 37, 38)

        fun kafkaTopicName(exchangeId: Int): String = "open_interests_$exchangeId.proto"
    }

    protected val producer by lazy {
        QueueProducer<MarketDataFeed.OpenInterestEntry>(host = Containers.kafka1.value.host, port = Containers.kafka1.value.port)
    }

    protected suspend fun List<MarketDataFeed.OpenInterestEntry>.sendToKafka(exchangeId: Int) {
        forEach {
            if (exchangeId !in supportedExchangeIds) {
                error("Please add exchange $exchangeId to supportedExchangeIds for the test to pass.")
            }
            producer.sendBytes(kafkaTopicName(exchangeId), it.toByteArray())
        }
    }

    protected fun List<MarketDataFeed.OpenInterestEntry>.sendToKafkaAsync(exchangeId: Int): List<Deferred<Long>> =
        map {
            if (exchangeId !in supportedExchangeIds) {
                error("Please add exchange $exchangeId to supportedExchangeIds for the test to pass.")
            }
            producer.sendBytesAsync(kafkaTopicName(exchangeId), it.toByteArray())
        }

    override fun mainApiConfig(): MainApiConfig =
        super.mainApiConfig().copy(
            kafkaOpenInterestConfigsPerExchange =
                supportedExchangeIds.map { exchangeId ->
                    Triple(
                        Resources.getExchangeById(exchangeId).getOrNull()!!,
                        MainApiConfig.KafkaSourceConfig(
                            configName = MainApiConfig.OPEN_INTEREST_CONFIG_NAME,
                            env = "dev",
                            topicName = kafkaTopicName(exchangeId),
                            kafkaServers =
                                MainApiConfig
                                    .KafkaConfig(
                                        configName = "KAFKA_OPEN_INTEREST",
                                        env = "dev",
                                        serverUrl = "${Containers.kafka1.value.host}:${Containers.kafka1.value.port}",
                                    ).toListOfAllKafkaServers(),
                            envVariablesResolver = envVariablesResolver,
                        ),
                        100,
                    )
                },
        )

    @AfterAll
    fun tearDown() {
        producer.close()
    }

    @BeforeEach
    open fun setUp() =
        runBlocking {
            mainApiModule.openInterestKafkaDataProviders.forEach { provider ->
                provider.dataSources.filter { it.initialized }.forEach { it.consumer.nextMessageOffset() }
            }
        }
}
