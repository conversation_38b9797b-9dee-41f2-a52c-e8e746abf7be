package io.coinmetrics.api.endpoints.streaming.spread.asset

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import io.coinmetrics.api.endpoints.stream.spread.SpreadQuotesKafkaDataProvider.AggregatedSpreadQuoteInput
import io.coinmetrics.api.endpoints.stream.spread.SpreadQuotesKafkaDataProvider.Pair
import io.coinmetrics.api.helper.TEST_API_KEY
import io.coinmetrics.api.helper.TEST_API_KEY_2
import io.coinmetrics.api.helper.TestWebSocketListener
import io.coinmetrics.api.utils.TimeUtils.dateTimeFormatter
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import java.time.Instant

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class WsAssetQuotesTest : WsAssetQuotesBaseTest() {
    private val mapper = jacksonObjectMapper()

    @Test
    fun `test basic receive`() =
        runBlocking {
            val webSocketListener = TestWebSocketListener()
            connectToWebSocket("/v4/timeseries-stream/asset-quotes?assets=btc&api_key=$TEST_API_KEY&backfill=none", webSocketListener)
            val time = Instant.now()
            val jsonMessage = """{"pair":{"base":0,"quote":3},"time":${time.toEpochMilli()},"ask_price":"24343.725954328216","ask_size":"2.96375165","bid_price":"24342.036360171896","bid_size":"12.00588437","mid_price":"24342.881157250056","spread":"0.0000694081421754166","base_asset":"btc","quote_asset":"usd"}"""
            val message = mapper.readValue(jsonMessage, AggregatedSpreadQuoteInput::class.java)
            producer.send(ASSET_QUOTES_TOPIC_NAME, message)
            assertEquals(
                """{"pair":"btc-usd","time":"${dateTimeFormatter.format(
                    Instant.ofEpochMilli(message.time),
                )}","ask_price":"${message.askPrice}","ask_size":"${message.askSize}","bid_price":"${message.bidPrice}","bid_size":"${message.bidSize}","mid_price":"${message.midPrice}","spread":"${message.spread}","cm_sequence_id":"0"}""".trimIndent(),
                webSocketListener.takeNextMessage(),
            )

            // no more messages
            assertFalse(webSocketListener.hasNextMessage())
        }

    @ParameterizedTest
    @ValueSource(strings = ["pulti", "null", "foo"])
    fun `should return bad_parameter error when requested asset is not supported`(asset: String) =
        runBlocking {
            val webSocketListener = TestWebSocketListener()
            connectToWebSocket("/v4/timeseries-stream/asset-quotes?assets=$asset&api_key=$TEST_API_KEY&backfill=none", webSocketListener)

            assertEquals(
                """{"error":{"type":"bad_parameter","message":"Bad parameter 'assets'. Value '$asset' is not supported."}}""".trimIndent(),
                webSocketListener.takeNextMessage(),
            )

            // no more messages
            assertFalse(webSocketListener.hasNextMessage())
        }

    @Test
    fun `should return missing_parameter error when 'assets' parameter is missing`() =
        runBlocking {
            val webSocketListener = TestWebSocketListener()
            connectToWebSocket("/v4/timeseries-stream/asset-quotes?assets=&api_key=$TEST_API_KEY&backfill=none", webSocketListener)

            assertEquals(
                """{"error":{"type":"missing_parameter","message":"Missing parameter 'assets'."}}""".trimIndent(),
                webSocketListener.takeNextMessage(),
            )

            // no more messages
            assertFalse(webSocketListener.hasNextMessage())
        }

    @Test
    fun `should return result when requested aggregation method is supported`() =
        runBlocking {
            val webSocketListener = TestWebSocketListener()
            val uri = "/v4/timeseries-stream/asset-quotes?assets=btc&api_key=$TEST_API_KEY&aggregation_method=aggregated_spread&backfill=none"
            connectToWebSocket(uri, webSocketListener)

            val time = Instant.now()
            val jsonMessage = """{"pair":{"base":0,"quote":3},"time":${time.toEpochMilli()},"ask_price":"24343.725954328216","ask_size":"2.96375165","bid_price":"24342.036360171896","bid_size":"12.00588437","mid_price":"24342.881157250056","spread":"0.0000694081421754166","base_asset":"btc","quote_asset":"usd"}"""
            val message = mapper.readValue(jsonMessage, AggregatedSpreadQuoteInput::class.java)
            producer.send(ASSET_QUOTES_TOPIC_NAME, message)

            assertEquals(
                """{"pair":"btc-usd","time":"${dateTimeFormatter.format(
                    Instant.ofEpochMilli(message.time),
                )}","ask_price":"${message.askPrice}","ask_size":"${message.askSize}","bid_price":"${message.bidPrice}","bid_size":"${message.bidSize}","mid_price":"${message.midPrice}","spread":"${message.spread}","cm_sequence_id":"0"}""".trimIndent(),
                webSocketListener.takeNextMessage(),
            )

            // no more messages
            assertFalse(webSocketListener.hasNextMessage())
        }

    @Test
    fun `should return bad_parameter error when requested aggregation method is not supported`() =
        runBlocking {
            val webSocketListener = TestWebSocketListener()
            val uri = "/v4/timeseries-stream/asset-quotes?assets=btc&api_key=$TEST_API_KEY&aggregation_method=unknown&backfill=none"
            connectToWebSocket(uri, webSocketListener)

            assertEquals(
                """{"error":{"type":"bad_parameter","message":"Bad parameter 'aggregation_method'. Value 'unknown' is not supported."}}""".trimIndent(),
                webSocketListener.takeNextMessage(),
            )

            // no more messages
            assertFalse(webSocketListener.hasNextMessage())
        }

    @Test
    fun `should return forbidden error when key has no access to market data feed`() =
        runBlocking {
            val webSocketListener = TestWebSocketListener()
            val uri = "/v4/timeseries-stream/asset-quotes?assets=btc&api_key=$TEST_API_KEY_2&backfill=none"
            connectToWebSocket(uri, webSocketListener)

            assertEquals(
                """{"error":{"type":"forbidden","message":"Requested resource is not available with supplied credentials."}}""".trimIndent(),
                webSocketListener.takeNextMessage(),
            )

            // no more messages
            assertFalse(webSocketListener.hasNextMessage())
        }

    @Test
    fun `test json conversion`() {
        val jsonString = """{"pair":{"base":3154,"quote":0},"time":1677019214250,"ask_price":"0.0004547","ask_size":"54.346","bid_price":"0.0004539","bid_size":"34.051","mid_price":"0.0004543","spread":"0.0017609509134933294","base_asset":"multi","quote_asset":"btc"}"""
        val quote = mapper.readValue(jsonString, AggregatedSpreadQuoteInput::class.java)
        val expectedQuote =
            AggregatedSpreadQuoteInput(
                pair = Pair(3154, 0),
                time = 1677019214250,
                askPrice = "0.0004547",
                askSize = "54.346",
                bidPrice = "0.0004539",
                bidSize = "34.051",
                midPrice = "0.0004543",
                spread = "0.0017609509134933294",
                baseAsset = "multi",
                quoteAsset = "btc",
            )
        assertEquals(quote, expectedQuote) { "Asset quote mismatch" }
    }

    @ParameterizedTest
    @ValueSource(strings = ["BTC", "eTh"])
    fun `test asset name in upper case`(requestedAsset: String) =
        runBlocking {
            val webSocketListener = TestWebSocketListener()
            connectToWebSocket(
                "/v4/timeseries-stream/asset-quotes?assets=$requestedAsset&api_key=$TEST_API_KEY&backfill=none",
                webSocketListener,
            )
            val time = Instant.now()
            val actualAsset = requestedAsset.lowercase()

            /**
             * It doesn't matter what number you use as a base because it is disregarded in the SpreadQuotesKafkaDataProvider.
             */
            val jsonMessage = """{"pair":{"base":999,"quote":3},"time":${time.toEpochMilli()},"ask_price":"24343.725954328216","ask_size":"2.96375165","bid_price":"24342.036360171896","bid_size":"12.00588437","mid_price":"24342.881157250056","spread":"0.0000694081421754166","base_asset":"$actualAsset","quote_asset":"usd"}"""
            val message = mapper.readValue(jsonMessage, AggregatedSpreadQuoteInput::class.java)
            producer.send(ASSET_QUOTES_TOPIC_NAME, message)
            assertEquals(
                """{"pair":"$actualAsset-usd","time":"${dateTimeFormatter.format(
                    Instant.ofEpochMilli(message.time),
                )}","ask_price":"${message.askPrice}","ask_size":"${message.askSize}","bid_price":"${message.bidPrice}","bid_size":"${message.bidSize}","mid_price":"${message.midPrice}","spread":"${message.spread}","cm_sequence_id":"0"}""".trimIndent(),
                webSocketListener.takeNextMessage(),
            )

            // no more messages
            assertFalse(webSocketListener.hasNextMessage())
        }
}
