package io.coinmetrics.api.endpoints.streaming.asset.principalprice

import io.coinmetrics.api.endpoints.stream.asset.AssetPrincipalPriceKafkaDataProvider
import io.coinmetrics.api.resources.Resources
import java.time.Instant

object WsPrincipalPriceTestUtils {
    fun createKafkaPrincipalPrice(
        assetId: String = "btc",
        time: Instant,
        price: Double = 1.01,
        quote: String = "usd",
        createdAt: Long = System.currentTimeMillis(),
    ): AssetPrincipalPriceKafkaDataProvider.PrincipalPriceInput =
        AssetPrincipalPriceKafkaDataProvider.PrincipalPriceInput(
            base = Resources.getCurrencyInfo(assetId)!!.id,
            quote = Resources.getCurrencyInfo(quote)!!.id,
            millisSinceEpoch = time.toEpochMilli(),
            price = price,
            createdAt = createdAt,
        )

    fun createKafkaPrincipalPrice(
        assetId: Int,
        time: Instant,
        price: Double = 1.01,
        quote: String = "usd",
        createdAt: Long = System.currentTimeMillis(),
    ): AssetPrincipalPriceKafkaDataProvider.PrincipalPriceInput =
        AssetPrincipalPriceKafkaDataProvider.PrincipalPriceInput(
            base = assetId,
            quote = Resources.getCurrencyInfo(quote)!!.id,
            millisSinceEpoch = time.toEpochMilli(),
            price = price,
            createdAt = createdAt,
        )
}
