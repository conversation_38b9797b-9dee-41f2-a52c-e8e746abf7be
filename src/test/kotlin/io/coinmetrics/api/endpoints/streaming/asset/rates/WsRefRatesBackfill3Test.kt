package io.coinmetrics.api.endpoints.streaming.asset.rates

import io.coinmetrics.api.helper.TEST_API_KEY
import io.coinmetrics.api.helper.TestWebSocketListener
import io.coinmetrics.api.utils.TimeUtils
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Test
import java.time.Instant
import java.time.temporal.ChronoUnit

class WsRefRatesBackfill3Test : WsRatesBaseTest() {
    @Test
    fun `check ETH quoted reference rate support`() =
        runBlocking {
            val baseTime = Instant.now().plusMillis(1000)

            producer
                .sendAsync(
                    ONE_SECOND_RATES_TOPIC_NAME,
                    WsRefRatesTestUtils.createKafkaReferenceRate(
                        time = baseTime,
                        assetId = "atom",
                        quote = "eth",
                    ),
                ).await()
            producer
                .sendAsync(
                    ONE_SECOND_RATES_TOPIC_NAME,
                    WsRefRatesTestUtils.createKafkaReferenceRate(
                        time = baseTime.minusMillis(10),
                        price = 2.0,
                        assetId = "atom",
                        quote = "eth",
                    ),
                ).await()

            // get the latest
            val inWebSocketListener = TestWebSocketListener()
            connectToWebSocket(
                "/v4/timeseries-stream/asset-metrics?assets=atom&metrics=ReferenceRateETH&frequency=1s&api_key=$TEST_API_KEY",
                inWebSocketListener,
            )

            val expectedMessage =
                """{"time":"${TimeUtils.dateTimeFormatter.format(
                    baseTime.truncatedTo(ChronoUnit.MILLIS),
                )}","asset":"atom","ReferenceRateETH":"1.01","cm_sequence_id":"0"}"""

            val message = inWebSocketListener.takeNextMessage()
            assertEquals(expectedMessage, message)

            // no more messages
            assertFalse(inWebSocketListener.hasNextMessage())
        }
}
