package io.coinmetrics.api.endpoints.streaming.market.candle

import io.coinmetrics.api.helper.CANDLES_TOPIC_NAME
import io.coinmetrics.api.helper.TEST_API_KEY
import io.coinmetrics.api.helper.TestWebSocketListener
import io.coinmetrics.proto.MarketDataFeed.MarketCandle
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import java.time.Instant
import java.time.temporal.ChronoUnit

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class WsCandlesTest : WsCandlesBaseTest() {
    @Test
    fun `test basic receive spot`() =
        runBlocking {
            val inWebSocketListener = TestWebSocketListener()
            connectToWebSocket(
                "/v4/timeseries-stream/market-candles?markets=binance-btc-usdt-spot&api_key=$TEST_API_KEY&backfill=none",
                inWebSocketListener,
            )

            val time = Instant.now()

            producer.sendBytes(
                CANDLES_TOPIC_NAME,
                createCandle(
                    time = time,
                    marketType = MarketCandle.MarketTypes.SPOT,
                    baseId = 0,
                    quoteId = 100,
                ).toByteArray(),
            )

            assertEquals(
                """{"market":"binance-btc-usdt-spot","time":"${formatTime(
                    time.minusSeconds(60),
                )}","price_open":"8.58","price_close":"8.58","price_high":"8.6","price_low":"8.58","vwap":"8.58004639859120984437","volume":"501218.10483905","candle_usd_volume":"3305.4700119511577485413995302691925425974618058515","candle_trades_count":"11","cm_sequence_id":"0"}""",
                inWebSocketListener.takeNextMessage(),
            )

            // no more messages
            assertFalse(inWebSocketListener.hasNextMessage())
        }

    @Test
    fun `test basic receive future`() =
        runBlocking {
            val inWebSocketListener = TestWebSocketListener()
            connectToWebSocket(
                "/v4/timeseries-stream/market-candles?markets=binance-BTCUSDT-future&api_key=$TEST_API_KEY&backfill=none",
                inWebSocketListener,
            )

            val time = Instant.now()

            producer.sendBytes(
                CANDLES_TOPIC_NAME,
                createCandle(time = time, marketType = MarketCandle.MarketTypes.FUTURE, symbol = "BTCUSDT").toByteArray(),
            )

            assertEquals(
                """{"market":"binance-BTCUSDT-future","time":"${formatTime(
                    time.minusSeconds(60),
                )}","price_open":"8.58","price_close":"8.58","price_high":"8.6","price_low":"8.58","vwap":"8.58004639859120984437","volume":"501218.10483905","candle_usd_volume":"3305.4700119511577485413995302691925425974618058515","candle_trades_count":"11","cm_sequence_id":"0"}""",
                inWebSocketListener.takeNextMessage(),
            )

            // no more messages
            assertFalse(inWebSocketListener.hasNextMessage())
        }

    @Test
    fun `test not receive spot when base is -1`() =
        runBlocking {
            noSpotWhenBaseOrQuoteIsNegative(-1, 100)
        }

    @Test
    fun `test not receive spot when quote is -1`() =
        runBlocking {
            noSpotWhenBaseOrQuoteIsNegative(0, -1)
        }

    private suspend fun noSpotWhenBaseOrQuoteIsNegative(
        basedId: Int,
        quoteId: Int,
    ) {
        val inWebSocketListener = TestWebSocketListener()
        connectToWebSocket(
            "/v4/timeseries-stream/market-candles?markets=binance-btc-usdt-spot&api_key=$TEST_API_KEY&backfill=none",
            inWebSocketListener,
        )

        val time = Instant.now()

        producer.sendBytes(
            CANDLES_TOPIC_NAME,
            createCandle(
                time = time,
                marketType = MarketCandle.MarketTypes.SPOT,
                baseId = basedId,
                quoteId = quoteId,
            ).toByteArray(),
        )

        // no more messages
        assertFalse(inWebSocketListener.hasNextMessage())
    }

    @Test
    fun `test not receive future when symbol is empty`() =
        runBlocking {
            noFutureWhenSymbolIsInvalid("")
        }

    @Test
    fun `test not receive future when symbol is blank`() =
        runBlocking {
            noFutureWhenSymbolIsInvalid(" ")
        }

    private suspend fun noFutureWhenSymbolIsInvalid(symbol: String) {
        val inWebSocketListener = TestWebSocketListener()
        connectToWebSocket(
            "/v4/timeseries-stream/market-candles?markets=binance-BTCUSDT-future&api_key=$TEST_API_KEY&backfill=none",
            inWebSocketListener,
        )

        val time = Instant.now()

        producer.sendBytes(
            CANDLES_TOPIC_NAME,
            createCandle(time = time, marketType = MarketCandle.MarketTypes.FUTURE, symbol = symbol).toByteArray(),
        )

        // no more messages
        assertFalse(inWebSocketListener.hasNextMessage())
    }

    @Test
    fun `test order receive spot`() =
        runBlocking {
            val inWebSocketListener = TestWebSocketListener()
            connectToWebSocket(
                "/v4/timeseries-stream/market-candles?markets=binance-btc-usdt-spot&api_key=$TEST_API_KEY&backfill=none",
                inWebSocketListener,
            )

            val time = Instant.now().truncatedTo(ChronoUnit.MINUTES)

            producer.sendBytes(
                CANDLES_TOPIC_NAME,
                createCandle(
                    time = time,
                    marketType = MarketCandle.MarketTypes.SPOT,
                    baseId = 0,
                    quoteId = 100,
                ).toByteArray(),
            )

            assertEquals(
                """{"market":"binance-btc-usdt-spot","time":"${formatTime(
                    time.minusSeconds(60),
                )}","price_open":"8.58","price_close":"8.58","price_high":"8.6","price_low":"8.58","vwap":"8.58004639859120984437","volume":"501218.10483905","candle_usd_volume":"3305.4700119511577485413995302691925425974618058515","candle_trades_count":"11","cm_sequence_id":"0"}""",
                inWebSocketListener.takeNextMessage(),
            )

            // no more messages
            assertFalse(inWebSocketListener.hasNextMessage())

            val time2 = Instant.now().truncatedTo(ChronoUnit.MINUTES).minus(1, ChronoUnit.HOURS)

            producer.sendBytes(
                CANDLES_TOPIC_NAME,
                createCandle(
                    time = time2,
                    marketType = MarketCandle.MarketTypes.SPOT,
                    baseId = 0,
                    quoteId = 100,
                ).toByteArray(),
            )

            // no more messages
            assertFalse(inWebSocketListener.hasNextMessage())

            val inWebSocketLatestListener = TestWebSocketListener()
            connectToWebSocket(
                "/v4/timeseries-stream/market-candles?markets=binance-btc-usdt-spot&api_key=$TEST_API_KEY&backfill=latest",
                inWebSocketLatestListener,
            )

            assertEquals(
                """{"market":"binance-btc-usdt-spot","time":"${formatTime(
                    time.minusSeconds(60),
                )}","price_open":"8.58","price_close":"8.58","price_high":"8.6","price_low":"8.58","vwap":"8.58004639859120984437","volume":"501218.10483905","candle_usd_volume":"3305.4700119511577485413995302691925425974618058515","candle_trades_count":"11","cm_sequence_id":"0"}""",
                inWebSocketLatestListener.takeNextMessage(),
            )
        }

    @Test
    fun `should return empty response when requesting unsupported pattern market`() =
        runBlocking {
            val inWebSocketListener = TestWebSocketListener()
            connectToWebSocket(
                "/v4/timeseries-stream/market-candles?markets=zaif-*-option&api_key=x1&backfill=none",
                inWebSocketListener,
            )

            val message = inWebSocketListener.takeNextMessageOrNull(100)
            assertNull(message)
        }

    @Test
    fun `should return empty response when requesting unsupported pattern markets`() =
        runBlocking {
            val inWebSocketListener = TestWebSocketListener()
            connectToWebSocket(
                "/v4/timeseries-stream/market-candles?markets=zaif-*-option,simex-CGSEUR-*&api_key=x1&backfill=none",
                inWebSocketListener,
            )

            val message = inWebSocketListener.takeNextMessageOrNull(100)
            assertNull(message)
        }
}
