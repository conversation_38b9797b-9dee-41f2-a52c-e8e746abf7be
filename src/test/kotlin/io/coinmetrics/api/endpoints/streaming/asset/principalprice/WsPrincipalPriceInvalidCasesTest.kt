package io.coinmetrics.api.endpoints.streaming.asset.principalprice

import io.coinmetrics.api.helper.TEST_API_KEY
import io.coinmetrics.api.helper.TEST_API_KEY_2
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import java.time.Instant

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class WsPrincipalPriceInvalidCasesTest : WsPrincipalPriceBaseTest() {
    @Test
    fun `market not supported for btc-eur 1s`() =
        runBlocking {
            val time = Instant.now()
            executeBaseTest(
                asset = "btc",
                quote = "eur",
                frequency = "1s",
                apiKey = TEST_API_KEY,
                time = time,
                expectedConnectionCount = 0,
                expectedMessages =
                    listOf(
                        """{"error":{"type":"bad_parameter","message":"Bad parameter 'metrics'. Value 'principal_market_price_eur' is not supported."}}""",
                    ),
            )
        }

    @Test
    fun `no permissions for xrp-usd 1s`() =
        runBlocking {
            val time = Instant.now()
            executeBaseTest(
                asset = "xrp",
                quote = "usd",
                frequency = "1s",
                apiKey = TEST_API_KEY_2,
                time = time,
                expectedConnectionCount = 0,
                expectedMessages =
                    listOf(
                        """{"error":{"type":"forbidden","message":"Requested metrics are not available with supplied credentials: principal_market_price_usd."}}""",
                    ),
            )
        }

    @Test
    fun `frequency not supported for ltc-usd 15s`() =
        runBlocking {
            val time = Instant.now()
            executeBaseTest(
                asset = "ltc",
                quote = "usd",
                frequency = "15s",
                apiKey = TEST_API_KEY,
                time = time,
                expectedConnectionCount = 0,
                expectedMessages =
                    listOf(
                        """{"error":{"type":"bad_parameter","message":"Bad parameter 'frequency'. Value '15s' is not supported."}}""",
                    ),
            )
        }
}
