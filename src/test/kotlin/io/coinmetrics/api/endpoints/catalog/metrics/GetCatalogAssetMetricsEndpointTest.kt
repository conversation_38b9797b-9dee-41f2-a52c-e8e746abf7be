package io.coinmetrics.api.endpoints.catalog.metrics

import io.coinmetrics.api.helper.BaseTest
import io.coinmetrics.api.helper.COMMUNITY_KEY
import io.coinmetrics.api.helper.EMPTY_RESPONSE
import io.coinmetrics.api.helper.TEST_API_KEY
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class GetCatalogAssetMetricsEndpointTest : BaseTest() {
    @Test
    fun `should return the catalog of available asset metrics for given key`() {
        getResponse("/v4/catalog/asset-metrics?api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should return full catalog of asset metrics`() {
        getResponse("/v4/catalog-all/asset-metrics?api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should return all available reviewable asset metrics`() {
        val expectedResponse =
            """{"data":[{"metric":"FlowInGEMNtv","full_name":"Flow, in, to Gemini, native units","description":"The sum in native units sent to Gemini that interval.","product":"Network Data","category":"Exchange","subcategory":"Deposits","unit":"Native units","data_type":"decimal","type":"Sum","frequencies":[{"frequency":"1b","assets":["eth"]},{"frequency":"1d","assets":["eth"]}],"reviewable":true,"display_name":"Gemini Deposits (native units)"},{"metric":"FlowTfrInBFXCnt","full_name":"Flow, transfers, to Bitfinex, count","description":"The sum count of transfers to any address belonging to Bitfinex in that interval. If the sender address also belongs to Bitfinex, the transfer is not counted.","product":"Network Data","category":"Exchange","subcategory":"Deposits","unit":"Transfers","data_type":"decimal","type":"Sum","frequencies":[{"frequency":"1b","assets":["eth"]}],"reviewable":true,"display_name":"Bitfinex Deposit Cnt"},{"metric":"FlowTfrOutBTXCnt","full_name":"Flow, transfers, from Bittrex, count","description":"The sum count of transfers from any address belonging to Bittrex in that interval. If the recipient address also belongs to Bittrex, the transfer is not counted.","product":"Network Data","category":"Exchange","subcategory":"Withdrawals","unit":"Transfers","data_type":"decimal","type":"Sum","frequencies":[{"frequency":"1d","assets":["btc","eth"]}],"reviewable":true,"display_name":"Bittrex Withdrawal Cnt"},{"metric":"SplyExUSD","full_name":"Supply, held by exchanges, USD","description":"The sum USD value of all native units held in hot or cold exchange wallets that interval.","product":"Network Data","category":"Exchange","subcategory":"Exchange Supply","unit":"USD","data_type":"decimal","type":"Sum","frequencies":[{"frequency":"1d","assets":["btc","eth"]}],"reviewable":true,"display_name":"Exchange Supply (USD)"}]}""".trimMargin()
        assertResponse(
            expectedCode = 200,
            expectedResponse,
            pathAndQuery = "/v4/catalog/asset-metrics?api_key=$TEST_API_KEY&reviewable=true",
        )
    }

    @Test
    fun `should return all available non-reviewable asset metrics`() {
        getResponse("/v4/catalog/asset-metrics?api_key=$TEST_API_KEY&reviewable=false").assertResponse()
    }

    @Test
    fun `should return all asset metrics by metrics filter`() {
        val expectedResponse =
            """{"data":[{"metric":"AdrActCnt","full_name":"Addresses, active, count","description":"The sum count of unique addresses that were active in the network (either as a recipient or originator of a ledger change) that interval. All parties in a ledger change action (recipients and originators) are counted. Individual addresses are not double-counted if previously active.","product":"Network Data","category":"Addresses","subcategory":"Active","unit":"Addresses","data_type":"bigint","type":"Sum","frequencies":[{"frequency":"1d","assets":["ant"]}],"display_name":"Active Addr Cnt"},{"metric":"FlowInGEMNtv","full_name":"Flow, in, to Gemini, native units","description":"The sum in native units sent to Gemini that interval.","product":"Network Data","category":"Exchange","subcategory":"Deposits","unit":"Native units","data_type":"decimal","type":"Sum","frequencies":[{"frequency":"1b","assets":["eth"]},{"frequency":"1d","assets":["eth"]}],"reviewable":true,"display_name":"Gemini Deposits (native units)"}]}"""
        assertResponse(
            expectedCode = 200,
            expectedResponse,
            pathAndQuery = "/v4/catalog/asset-metrics?api_key=$TEST_API_KEY&metrics=FlowInGEMNtv,AdrActCnt",
        )
    }

    @Test
    fun `should return empty result when reviewable metric specified and reviewable=false`() {
        assertResponse(
            expectedCode = 200,
            EMPTY_RESPONSE,
            pathAndQuery = "/v4/catalog/asset-metrics?api_key=$TEST_API_KEY&metrics=FlowInGEMNtv&reviewable=false",
        )
    }

    @Test
    fun `should return 400 when invalid metric specified`() {
        val expectedResponse =
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'metrics'. Value 'SomeInvalidMetricName' is not supported."}}"""
        assertResponse(
            expectedCode = 400,
            expectedResponse,
            pathAndQuery = "/v4/catalog/asset-metrics?api_key=$TEST_API_KEY&metrics=SomeInvalidMetricName",
        )
    }

    @Test
    fun `should not return MinerEntity metric because it is internal`() {
        assertResponse(
            expectedCode = 200,
            EMPTY_RESPONSE,
            pathAndQuery = "/v4/catalog/asset-metrics?api_key=$TEST_API_KEY&metrics=MinerEntity",
        )
    }

    @Test
    fun `should return the catalog of available asset metrics for community key`() {
        val expectedResponse =
            """{"data":[{"metric":"AdrActCnt","full_name":"Addresses, active, count","description":"The sum count of unique addresses that were active in the network (either as a recipient or originator of a ledger change) that interval. All parties in a ledger change action (recipients and originators) are counted. Individual addresses are not double-counted if previously active.","product":"Network Data","category":"Addresses","subcategory":"Active","unit":"Addresses","data_type":"bigint","type":"Sum","frequencies":[{"frequency":"1d","assets":["ant"]}],"display_name":"Active Addr Cnt"},{"metric":"PriceUSD","full_name":"Price, USD","description":"The fixed closing price of the asset as of 00:00 UTC the following day (i.e., midnight UTC of the current day) denominated in USD. This price is generated by Coin Metrics' fixing/reference rate service. Real-time PriceUSD is the fixed closing price of the asset as of the timestamp set by the block's miner.","product":"Network Data","category":"Market","subcategory":"Price","unit":"USD","data_type":"decimal","type":"NA","frequencies":[{"frequency":"1d","assets":["btc"]}],"display_name":"USD Denominated Closing Price"},{"metric":"ReferenceRate","full_name":"Reference Rate, USD","description":"The price of an asset quoted in U.S. dollars using a framework to select high quality constituent markets and a methodology that is resistant manipulation.","product":"CM Prices","category":"Market","subcategory":"Price","unit":"USD","data_type":"decimal","type":"NA","frequencies":[{"frequency":"1s","assets":["btc","eth","mtl_metal"]},{"frequency":"1m","assets":["btc","eth"]},{"frequency":"1h","assets":["ada","atom","btc","eth"]},{"frequency":"1d","assets":["btc","eth"]},{"frequency":"1d-ny-close","assets":["ada","btc"]}]},{"metric":"ReferenceRateETH","full_name":"Reference Rate, ETH","description":"The price of an asset quoted in Etherium using a framework to select high quality constituent markets and a methodology that is resistant manipulation.","product":"CM Prices","category":"Market","subcategory":"Price","unit":"ETH","data_type":"decimal","type":"NA","frequencies":[{"frequency":"1h","assets":["atom"]}]},{"metric":"ReferenceRateEUR","full_name":"Reference Rate, EUR","description":"The price of an asset quoted in Euros using a framework to select high quality constituent markets and a methodology that is resistant manipulation.","product":"CM Prices","category":"Market","subcategory":"Price","unit":"EUR","data_type":"decimal","type":"NA","frequencies":[{"frequency":"1s","assets":["btc","eth"]},{"frequency":"1m","assets":["btc","eth"]},{"frequency":"1h","assets":["btc","eth"]},{"frequency":"1d","assets":["btc","eth"]},{"frequency":"1d-ny-close","assets":["btc"]}]},{"metric":"ReferenceRateUSD","full_name":"Reference Rate, USD","description":"The price of an asset quoted in U.S. dollars using a framework to select high quality constituent markets and a methodology that is resistant manipulation.","product":"CM Prices","category":"Market","subcategory":"Price","unit":"USD","data_type":"decimal","type":"NA","frequencies":[{"frequency":"1s","assets":["btc","eth","mtl_metal"]},{"frequency":"1m","assets":["btc","eth"]},{"frequency":"1h","assets":["ada","atom","btc","eth"]},{"frequency":"1d","assets":["btc","eth"]},{"frequency":"1d-ny-close","assets":["ada","btc"]}]},{"metric":"TxCnt","full_name":"Transactions, count","description":"The sum count of transactions that interval. Transactions represent a bundle of intended actions to alter the ledger initiated by a user (human or machine). Transactions are counted whether they execute or not and whether they result in the transfer of native units or not (a transaction can result in no, one, or many transfers). Changes to the ledger mandated by the protocol (and not by a user) or post-launch new issuance issued by a founder or controlling entity are not included here.","product":"Network Data","category":"Transactions","subcategory":"Transactions","unit":"Transactions","data_type":"bigint","type":"Sum","frequencies":[{"frequency":"1d","assets":["btc","eth"]}],"display_name":"Tx Cnt"},{"metric":"TxTfrValMedNtv","full_name":"Transactions, transfers, value, median, native units","description":"The median count of native units transferred per transfer (i.e., the median size of a transfer) between distinct addresses that interval.","product":"Network Data","category":"Transactions","subcategory":"Transfer Value","unit":"Native units","data_type":"decimal","type":"Median","frequencies":[{"frequency":"1d","assets":["ant"]}],"display_name":"Median Tx Size (native units)"},{"metric":"principal_market_price_usd","full_name":"Principal Market Price, USD","description":"The price of an asset quoted in U.S. dollars derived from the asset's principal market, the market with the most trading volume or activity.","product":"CM Prices","category":"Market","subcategory":"Price","unit":"USD","data_type":"decimal","type":"N/A","frequencies":[{"frequency":"1s","assets":["btc","eth"]},{"frequency":"1m","assets":["btc","eth"]},{"frequency":"1h","assets":["btc","eth"]},{"frequency":"1d","assets":["btc","eth"]},{"frequency":"1d-ny-close","assets":["btc","eth"]}],"display_name":"Principal Market Price"},{"metric":"principal_market_usd","full_name":"Principal Market, USD","description":"The asset's principal market, the market with the most trading volume or activity.","product":"CM Prices","category":"Market","subcategory":"Price","unit":"USD","data_type":"text","type":"N/A","frequencies":[{"frequency":"1s","assets":["btc","eth"]},{"frequency":"1m","assets":["btc","eth"]},{"frequency":"1h","assets":["btc","eth"]},{"frequency":"1d","assets":["btc","eth"]},{"frequency":"1d-ny-close","assets":["btc","eth"]}],"display_name":"Principal Market"}]}"""
        assertResponse(
            expectedCode = 200,
            expectedResponse,
            pathAndQuery = "/v4/catalog/asset-metrics?api_key=$COMMUNITY_KEY",
        )
    }

    @Test
    fun `should return full catalog of asset metrics for community key`() {
        getResponse("/v4/catalog-all/asset-metrics?api_key=$COMMUNITY_KEY").assertResponse()
    }

    @Test
    fun `should return empty reviewable asset metrics for community key`() {
        assertResponse(
            expectedCode = 200,
            EMPTY_RESPONSE,
            pathAndQuery = "/v4/catalog/asset-metrics?api_key=$COMMUNITY_KEY&reviewable=true",
        )
    }

    @Test
    fun `should return all available non-reviewable asset metrics for community key`() {
        val expectedResponse =
            """{"data":[{"metric":"AdrActCnt","full_name":"Addresses, active, count","description":"The sum count of unique addresses that were active in the network (either as a recipient or originator of a ledger change) that interval. All parties in a ledger change action (recipients and originators) are counted. Individual addresses are not double-counted if previously active.","product":"Network Data","category":"Addresses","subcategory":"Active","unit":"Addresses","data_type":"bigint","type":"Sum","frequencies":[{"frequency":"1d","assets":["ant"]}],"display_name":"Active Addr Cnt"},{"metric":"PriceUSD","full_name":"Price, USD","description":"The fixed closing price of the asset as of 00:00 UTC the following day (i.e., midnight UTC of the current day) denominated in USD. This price is generated by Coin Metrics' fixing/reference rate service. Real-time PriceUSD is the fixed closing price of the asset as of the timestamp set by the block's miner.","product":"Network Data","category":"Market","subcategory":"Price","unit":"USD","data_type":"decimal","type":"NA","frequencies":[{"frequency":"1d","assets":["btc"]}],"display_name":"USD Denominated Closing Price"},{"metric":"ReferenceRate","full_name":"Reference Rate, USD","description":"The price of an asset quoted in U.S. dollars using a framework to select high quality constituent markets and a methodology that is resistant manipulation.","product":"CM Prices","category":"Market","subcategory":"Price","unit":"USD","data_type":"decimal","type":"NA","frequencies":[{"frequency":"1s","assets":["btc","eth","mtl_metal"]},{"frequency":"1m","assets":["btc","eth"]},{"frequency":"1h","assets":["ada","atom","btc","eth"]},{"frequency":"1d","assets":["btc","eth"]},{"frequency":"1d-ny-close","assets":["ada","btc"]}]},{"metric":"ReferenceRateETH","full_name":"Reference Rate, ETH","description":"The price of an asset quoted in Etherium using a framework to select high quality constituent markets and a methodology that is resistant manipulation.","product":"CM Prices","category":"Market","subcategory":"Price","unit":"ETH","data_type":"decimal","type":"NA","frequencies":[{"frequency":"1h","assets":["atom"]}]},{"metric":"ReferenceRateEUR","full_name":"Reference Rate, EUR","description":"The price of an asset quoted in Euros using a framework to select high quality constituent markets and a methodology that is resistant manipulation.","product":"CM Prices","category":"Market","subcategory":"Price","unit":"EUR","data_type":"decimal","type":"NA","frequencies":[{"frequency":"1s","assets":["btc","eth"]},{"frequency":"1m","assets":["btc","eth"]},{"frequency":"1h","assets":["btc","eth"]},{"frequency":"1d","assets":["btc","eth"]},{"frequency":"1d-ny-close","assets":["btc"]}]},{"metric":"ReferenceRateUSD","full_name":"Reference Rate, USD","description":"The price of an asset quoted in U.S. dollars using a framework to select high quality constituent markets and a methodology that is resistant manipulation.","product":"CM Prices","category":"Market","subcategory":"Price","unit":"USD","data_type":"decimal","type":"NA","frequencies":[{"frequency":"1s","assets":["btc","eth","mtl_metal"]},{"frequency":"1m","assets":["btc","eth"]},{"frequency":"1h","assets":["ada","atom","btc","eth"]},{"frequency":"1d","assets":["btc","eth"]},{"frequency":"1d-ny-close","assets":["ada","btc"]}]},{"metric":"TxCnt","full_name":"Transactions, count","description":"The sum count of transactions that interval. Transactions represent a bundle of intended actions to alter the ledger initiated by a user (human or machine). Transactions are counted whether they execute or not and whether they result in the transfer of native units or not (a transaction can result in no, one, or many transfers). Changes to the ledger mandated by the protocol (and not by a user) or post-launch new issuance issued by a founder or controlling entity are not included here.","product":"Network Data","category":"Transactions","subcategory":"Transactions","unit":"Transactions","data_type":"bigint","type":"Sum","frequencies":[{"frequency":"1d","assets":["btc","eth"]}],"display_name":"Tx Cnt"},{"metric":"TxTfrValMedNtv","full_name":"Transactions, transfers, value, median, native units","description":"The median count of native units transferred per transfer (i.e., the median size of a transfer) between distinct addresses that interval.","product":"Network Data","category":"Transactions","subcategory":"Transfer Value","unit":"Native units","data_type":"decimal","type":"Median","frequencies":[{"frequency":"1d","assets":["ant"]}],"display_name":"Median Tx Size (native units)"},{"metric":"principal_market_price_usd","full_name":"Principal Market Price, USD","description":"The price of an asset quoted in U.S. dollars derived from the asset's principal market, the market with the most trading volume or activity.","product":"CM Prices","category":"Market","subcategory":"Price","unit":"USD","data_type":"decimal","type":"N/A","frequencies":[{"frequency":"1s","assets":["btc","eth"]},{"frequency":"1m","assets":["btc","eth"]},{"frequency":"1h","assets":["btc","eth"]},{"frequency":"1d","assets":["btc","eth"]},{"frequency":"1d-ny-close","assets":["btc","eth"]}],"display_name":"Principal Market Price"},{"metric":"principal_market_usd","full_name":"Principal Market, USD","description":"The asset's principal market, the market with the most trading volume or activity.","product":"CM Prices","category":"Market","subcategory":"Price","unit":"USD","data_type":"text","type":"N/A","frequencies":[{"frequency":"1s","assets":["btc","eth"]},{"frequency":"1m","assets":["btc","eth"]},{"frequency":"1h","assets":["btc","eth"]},{"frequency":"1d","assets":["btc","eth"]},{"frequency":"1d-ny-close","assets":["btc","eth"]}],"display_name":"Principal Market"}]}"""
        assertResponse(
            expectedCode = 200,
            expectedResponse,
            pathAndQuery = "/v4/catalog/asset-metrics?api_key=$COMMUNITY_KEY&reviewable=false",
        )
    }
}
