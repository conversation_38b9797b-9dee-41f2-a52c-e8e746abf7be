package io.coinmetrics.api.endpoints.catalog

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import io.coinmetrics.api.helper.BaseTest
import io.coinmetrics.api.helper.GOD_KEY
import io.coinmetrics.api.helper.TEST_API_KEY
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import java.io.BufferedReader
import java.io.InputStream
import java.io.InputStreamReader
import java.net.HttpURLConnection
import java.net.URI
import java.util.stream.Collectors

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class AssetsCatalogTest : BaseTest() {
    @Test
    fun `test asset_info with unsupported asset name`() {
        getResponse("/v4/catalog/assets?api_key=$TEST_API_KEY&assets=btc,edr_endorprotocol2").assertResponse()
    }

    @Test
    fun `asset exists for the market data only`() {
        getResponse("/v4/catalog/assets?api_key=$TEST_API_KEY&assets=btc,edr_endorprotocol").assertResponse()
    }

    @Test
    fun `asset exists for the market data only cme no access test`() {
        getResponse("/v4/catalog/assets?api_key=$GOD_KEY&assets=btc,edr_endorprotocol").assertResponse()
    }

    @Test
    fun `should return error when include and exclude filters are both specified`() {
        getResponse("/v4/catalog/assets?api_key=$GOD_KEY&assets=btc&include=metrics&exclude=markets").assertResponse()
    }

    @Test
    fun `should return error when illegal include filter specified`() {
        getResponse("/v4/catalog/assets?api_key=$GOD_KEY&assets=btc&include=metricss").assertResponse()
    }

    @Test
    fun `should return error when illegal exclude filter specified`() {
        getResponse("/v4/catalog/assets?api_key=$GOD_KEY&assets=btc&exclude=marketss").assertResponse()
    }

    @Test
    fun `should exclude markets from response when exclude=markets for btc asset`() {
        getResponse("/v4/catalog/assets?api_key=$GOD_KEY&assets=btc&exclude=markets").assertResponse()
    }

    @Test
    fun `should include only metrics in response when include=metrics for btc asset`() {
        getResponse("/v4/catalog/assets?api_key=$GOD_KEY&assets=btc&include=metrics").assertResponse()
    }

    @Test
    fun `asset is not allowed for demo key in market data resource`() {
        getResponse("/v4/catalog/assets?api_key=NYk69ScVxQFbX3UWnHMt&assets=edr_endorprotocol").assertResponse()
    }

    @Test
    fun `asset is not supported as quote but supported as base`() {
        getResponse("/v4/catalog/assets?api_key=$TEST_API_KEY&assets=est").assertResponse()
    }

    @Test
    fun `asset is not supported as quote but supported as base, catalog all`() {
        val response = getResponse("/v4/catalog-all/assets?api_key=$TEST_API_KEY&assets=cvc")
        assertEquals(200, response.status) {
            "Body: ${response.body}"
        }
        val node = jacksonObjectMapper().readTree(response.body)
        assertTrue(node.path("data").size() > 0)
    }

    @Test
    fun `test catalog asset btc`() {
        getResponse("/v4/catalog/assets?assets=btc&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `test catalog-all asset btc`() {
        getResponse("/v4/catalog-all/assets?assets=btc&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `test catalog asset mtl_metal`() {
        getResponse("/v4/catalog-all/assets?assets=mtl_metal&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `test catalog asset btc,mtl_metal`() {
        getResponse("/v4/catalog-all/assets?assets=btc,mtl_metal&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `test multi asset info`() {
        getResponse("/v4/catalog/assets?assets=btc,eth&api_key=$TEST_API_KEY").assertResponse("1")
        getResponse("/v4/catalog/assets?assets=BTC,eth&api_key=$TEST_API_KEY").assertResponse("2")
        getResponse("/v4/catalog/assets?assets=BTC,ETH&api_key=$TEST_API_KEY").assertResponse("3")
    }

    @Test
    fun `test full asset info`() {
        getResponse("/v4/catalog/assets?api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should return only usdc asset when it's specified in assets filter`() {
        getResponse("/v4/catalog/assets?api_key=$TEST_API_KEY&assets=usdc").assertResponse()
    }

    @Test
    fun `community api`() {
        // we can't change Host header using java 11 http client
        // the issue is solved in java 12

        // but for now we use ancient http client and hacks as workaround
        System.setProperty("sun.net.http.allowRestrictedHeaders", "true")

        val url = URI("http://127.0.0.1:${server.actualPort()}/v4/catalog/assets?assets=ant").toURL()
        val conn: HttpURLConnection = url.openConnection() as HttpURLConnection
        conn.requestMethod = "GET"
        conn.setRequestProperty("Host", "community-api-stg.coinmetrics.io")

        val response = readInputStream(conn.inputStream)
        assertEquals(200, conn.responseCode) { "Response body: $response" }

        val expectedResponse =
            """{"data":[{"asset":"ant","full_name":"Aragon","metrics":[{"metric":"AdrActCnt","frequencies":[{"frequency":"1d","min_time":"2020-04-15T00:00:00.000000000Z","max_time":"2020-04-15T00:00:00.000000000Z","community":true}]},{"metric":"TxTfrValMedNtv","frequencies":[{"frequency":"1d","min_time":"2020-04-15T00:00:00.000000000Z","max_time":"2020-04-15T00:00:00.000000000Z","community":true}]}]}]}"""
        assertEquals(expectedResponse, response)
    }

    @Test
    fun `test many assets`() {
        getResponse("/v4/catalog/assets?api_key=$TEST_API_KEY&assets=1inch,aave,ada,agi,algo,alice,ankr,ape,api3,atom,audio,avax,axs,band,bat,bnb,btc,busd,cake,celo,celr,chz,coti,crv,dash,doge,dot,dydx,enj,eth,fil,ftm,gala,glmr,grt,hbar,imx,iost,kava,knc,ksm,ldo,link,lrc,ltc,mana,mask,matic,mkr,near,nu,ocean,qnt,rndr,rune,sand,shib,skl,snx,sol,storj,stx,trx,uma,uni,waves,woo,xlm,xrp,xtz,zil&exclude=exchanges,markets,metrics").assertResponse()
    }

    private fun readInputStream(inputStream: InputStream): String = BufferedReader(InputStreamReader(inputStream)).lines().collect(Collectors.joining("\n"))
}
