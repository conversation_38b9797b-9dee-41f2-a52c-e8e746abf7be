package io.coinmetrics.api.endpoints.catalog.market

import io.coinmetrics.api.helper.BaseTest
import io.coinmetrics.api.helper.COMMUNITY_KEY
import io.coinmetrics.api.helper.POSTGRES_IMAGE
import io.coinmetrics.api.modules.common.CommonConfig
import io.coinmetrics.api.modules.main.MainApiConfig
import io.coinmetrics.api.utils.TimeUtils
import io.coinmetrics.api.utils.modifyDatabases
import io.coinmetrics.databases.DbConfig
import org.junit.jupiter.api.AfterAll
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.testcontainers.containers.PostgreSQLContainer
import org.testcontainers.junit.jupiter.Testcontainers
import java.time.Duration
import java.time.Instant
import java.time.ZonedDateTime
import java.time.temporal.ChronoUnit.DAYS
import java.time.temporal.ChronoUnit.HOURS
import java.time.temporal.ChronoUnit.MINUTES

@Testcontainers
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class DynamicMarketCandlesCatalogTest : BaseTest() {
    companion object {
        private val postgres =
            PostgreSQLContainer<Nothing>(POSTGRES_IMAGE).apply {
                withInitScript("postgres/init_db_streaming.sql")
            }

        private val now: Instant = ZonedDateTime.now().toInstant()
        private val maxTime1m = now.truncatedTo(MINUTES)
        private val minTime1m = maxTime1m.minus(1L, MINUTES)
        private val maxTime5m = Instant.ofEpochSecond(now.epochSecond.div(300).times(300))
        private val minTime5m = maxTime5m.minus(5L, MINUTES)
        private val maxTime10m = Instant.ofEpochSecond(now.epochSecond.div(600).times(600))
        private val minTime10m = maxTime10m.minus(10L, MINUTES)
        private val maxTime15m = Instant.ofEpochSecond(now.epochSecond.div(900).times(900))
        private val minTime15m = maxTime15m.minus(15L, MINUTES)
        private val maxTime30m = Instant.ofEpochSecond(now.epochSecond.div(1800).times(1800))
        private val minTime30m = maxTime15m.minus(30L, MINUTES)
        private val maxTime1h = now.truncatedTo(HOURS)
        private val minTime1h = maxTime1h.minus(1L, HOURS)
        private val maxTime1d: Instant = now.minus(1L, DAYS).truncatedTo(DAYS)
        private val minTime1d = maxTime1d.minus(3440L, DAYS)

        init {
            postgres.start()
            val schema = postgres.databaseName
            val connection = postgres.createConnection("")
            val testData =
                """
                    SET TIME ZONE 'UTC';
                    INSERT INTO $schema.candles_market_spot_33_1m (candle_base_id, candle_quote_id, candle_start_time, candle_open_price, candle_close_price, candle_low_price, candle_high_price, candle_vwap, candle_volume, candle_trades_count, candle_usd_volume, candle_gap_length)
                    VALUES (0, 3, to_timestamp(${minTime1m.epochSecond}), 0.05808, 0.0808, 0.08308, 0.0808, 0.08308, 49, 123, 149, 0),
                           (0, 3, to_timestamp(${maxTime1m.epochSecond}), 0.05808, 0.0808, 0.08308, 0.0808, 0.08308, 49, 123, 149, 0);
                    
                    INSERT INTO $schema.candles_market_spot_33_5m (candle_base_id, candle_quote_id, candle_start_time, candle_open_price, candle_close_price, candle_low_price, candle_high_price, candle_vwap, candle_volume, candle_trades_count, candle_usd_volume, candle_gap_length)
                    VALUES (0, 3, to_timestamp(${minTime5m.epochSecond}), 0.05808, 0.0808, 0.08308, 0.0808, 0.08308, 49, 123, 149, null),
                           (0, 3, to_timestamp(${maxTime5m.epochSecond}), 0.0808, 0.0808, 0.0808, 0.0808, 0.0808, 49, 123, 149, null);
                            
                    INSERT INTO $schema.candles_market_spot_33_10m (candle_base_id, candle_quote_id, candle_start_time, candle_open_price, candle_close_price, candle_low_price, candle_high_price, candle_vwap, candle_volume, candle_trades_count, candle_usd_volume, candle_gap_length)
                    VALUES (0, 3, to_timestamp(${minTime10m.epochSecond}), 0.0808, 0.0808, 0.0808, 0.0808, 0.0808, 49, 123, 149, null),
                           (0, 3, to_timestamp(${maxTime10m.epochSecond}), 0.0808, 0.0808, 0.0808, 0.0808, 0.0808, 49, 123, 149, null);
                    
                    INSERT INTO $schema.candles_market_spot_33_15m (candle_base_id, candle_quote_id, candle_start_time, candle_open_price, candle_close_price, candle_low_price, candle_high_price, candle_vwap, candle_volume, candle_trades_count, candle_usd_volume, candle_gap_length)
                    VALUES (0, 3, to_timestamp(${minTime15m.epochSecond}), 0.0808, 0.0808, 0.0808, 0.0808, 0.0808, 49, 123, 149, null),
                           (0, 3, to_timestamp(${maxTime15m.epochSecond}), 0.0808, 0.0808, 0.0808, 0.0808, 0.0808, 49, 123, 149, null);
                    
                    INSERT INTO $schema.candles_market_spot_33_30m (candle_base_id, candle_quote_id, candle_start_time, candle_open_price, candle_close_price, candle_low_price, candle_high_price, candle_vwap, candle_volume, candle_trades_count, candle_usd_volume, candle_gap_length)
                    VALUES (0, 3, to_timestamp(${minTime30m.epochSecond}), 0.0808, 0.0808, 0.0808, 0.0808, 0.0808, 49, 123, 149, null),
                           (0, 3, to_timestamp(${maxTime30m.epochSecond}), 0.0808, 0.0808, 0.0808, 0.0808, 0.0808, 49, 123, 149, null);
                    
                    INSERT INTO $schema.candles_market_spot_33_1h (candle_base_id, candle_quote_id, candle_start_time, candle_open_price, candle_close_price, candle_low_price, candle_high_price, candle_vwap, candle_volume, candle_trades_count, candle_usd_volume, candle_gap_length)
                    VALUES (0, 3, to_timestamp(${minTime1h.epochSecond}), 0.0808, 0.0808, 0.0808, 0.0808, 0.0808, 49, 123, 149, null),
                           (0, 3, to_timestamp(${maxTime1h.epochSecond}), 0.0808, 0.0808, 0.0808, 0.0808, 0.0808, 49, 123, 149, null);
                    
                    INSERT INTO $schema.candles_market_spot_33_1d (candle_base_id, candle_quote_id, candle_start_time, candle_open_price, candle_close_price, candle_low_price, candle_high_price, candle_vwap, candle_volume, candle_trades_count, candle_usd_volume, candle_gap_length)
                    VALUES (0, 3, to_timestamp(${minTime1d.epochSecond}), 0.0909, 0.0808, 0.07723, 0.09307, 0.0865179442508711, 574, 123, 1574, null),
                           (0, 3, to_timestamp(${maxTime1d.minus(
                    6L,
                    DAYS,
                ).epochSecond}), 0.0909, 0.0808, 0.07723, 0.09307, 0.0865179442508711, 574, 123, 1574, null),
                           (0, 3, to_timestamp(${maxTime1d.minus(
                    5L,
                    DAYS,
                ).epochSecond}), 0.09029, 0.008, 0.0323, 0.0932, 0.08651794425, 574, 123, 1574, null),
                           (0, 3, to_timestamp(${maxTime1d.minus(
                    4L,
                    DAYS,
                ).epochSecond}), 0.09059, 0.4008, 0.023, 0.0932, 0.0861794425, 574, 123, 1574, null),
                           (0, 3, to_timestamp(${maxTime1d.minus(
                    3L,
                    DAYS,
                ).epochSecond}), 0.0909, 0.008, 0.023, 0.02932, 0.0865194425, 574, 123, 1574, null),
                           (0, 3, to_timestamp(${maxTime1d.minus(
                    2L,
                    DAYS,
                ).epochSecond}), 0.09039, 0.008, 0.023, 0.01932, 0.0865794425, 574, 123, 1574, null),
                           (0, 3, to_timestamp(${maxTime1d.minus(
                    1L,
                    DAYS,
                ).epochSecond}), 0.0909, 0.008, 0.023, 0.092, 0.086517425, 574, 123, 1574, null),
                           (0, 3, to_timestamp(${maxTime1d.epochSecond}), 0.0909, 0.008, 0.023, 0.09322, 0.08651794425, 574, 123, 1574, null);
                """.trimIndent()
            connection.prepareStatement(testData).execute()
        }
    }

    override fun commonConfig(): CommonConfig = super.commonConfig().copy(statisticsPollInterval = Duration.ofSeconds(1))

    override fun mainApiConfig(): MainApiConfig =
        super
            .mainApiConfig()
            .copy(realtimeMetricsUpdateFrequencyMs = 200)
            .modifyDatabases {
                copy(
                    candles =
                        DbConfig(
                            appName = "API",
                            dbConfigName = "CANDLES_MARKET",
                            env = "dev",
                            jdbcUrl = "jdbc:postgresql://${postgres.host}:${postgres.firstMappedPort}/${postgres.databaseName}?user=${postgres.username}&password=${postgres.password}",
                            envVariablesResolver = envVariablesResolver,
                        ),
                )
            }

    override fun statisticsConfig() =
        super.statisticsConfig().copy(
            assetMetricStatisticsRefreshIntervalSec = 1,
        )

    @AfterAll
    internal fun tearDown() {
        super.stopServer()
        postgres.close()
    }

    @Test
    fun `market candles catalog should have 1d statistics when requested by community clients`() {
        val expected1mMinTime = TimeUtils.dateTimeFormatter.format(minTime1m)
        val expected1mMaxTime = TimeUtils.dateTimeFormatter.format(maxTime1m)
        val expected5mMinTime = TimeUtils.dateTimeFormatter.format(minTime5m)
        val expected5mMaxTime = TimeUtils.dateTimeFormatter.format(maxTime5m)
        val expected10mMinTime = TimeUtils.dateTimeFormatter.format(minTime10m)
        val expected10mMaxTime = TimeUtils.dateTimeFormatter.format(maxTime10m)
        val expected15mMinTime = TimeUtils.dateTimeFormatter.format(minTime15m)
        val expected15mMaxTime = TimeUtils.dateTimeFormatter.format(maxTime15m)
        val expected30mMinTime = TimeUtils.dateTimeFormatter.format(minTime30m)
        val expected30mMaxTime = TimeUtils.dateTimeFormatter.format(maxTime30m)
        val expected1hMinTime = TimeUtils.dateTimeFormatter.format(minTime1h)
        val expected1hMaxTime = TimeUtils.dateTimeFormatter.format(maxTime1h)
        val expected1dMinTime = TimeUtils.dateTimeFormatter.format(minTime1d)
        val expected1dMaxTime = TimeUtils.dateTimeFormatter.format(maxTime1d)
        val expectedResponse =
            """{"data":[{"market":"bittrex-btc-usd-spot","frequencies":[{"frequency":"1m","min_time":"$expected1mMinTime","max_time":"$expected1mMaxTime"},{"frequency":"5m","min_time":"$expected5mMinTime","max_time":"$expected5mMaxTime"},{"frequency":"10m","min_time":"$expected10mMinTime","max_time":"$expected10mMaxTime"},{"frequency":"15m","min_time":"$expected15mMinTime","max_time":"$expected15mMaxTime"},{"frequency":"30m","min_time":"$expected30mMinTime","max_time":"$expected30mMaxTime"},{"frequency":"1h","min_time":"$expected1hMinTime","max_time":"$expected1hMaxTime"},{"frequency":"1d","min_time":"$expected1dMinTime","max_time":"$expected1dMaxTime"}]}]}"""

        assertResponse(200, expectedResponse, "/v4/catalog/market-candles?markets=bittrex-btc-usd-spot&api_key=$COMMUNITY_KEY")
    }
}
