package io.coinmetrics.api.endpoints.catalog

import io.coinmetrics.api.helper.BaseTest
import io.coinmetrics.api.helper.COMMUNITY_KEY
import io.coinmetrics.api.helper.TEST_API_KEY
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class AssetAlertsCatalogTest : BaseTest() {
    @Test
    fun `bad api_key`() {
        val expectedResponse =
            """{"error":{"type":"wrong_credentials","message":"Supplied credentials are not valid."}}"""
        assertResponse(
            401,
            expectedResponse,
            "/v4/catalog-all/asset-alerts?api_key=xxx",
        )
    }

    @Test
    fun `unsupported asset name`() {
        val expectedResponse =
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'assets'. Value 'xxx' is not supported."}}"""
        assertResponse(400, expectedResponse, "/v4/catalog/asset-alerts?assets=btc,xxx&api_key=$TEST_API_KEY")
    }

    @Test
    fun `unsupported asset alert name`() {
        val expectedResponse =
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'alerts'. Value 'xxx' is not supported."}}"""
        assertResponse(
            400,
            expectedResponse,
            "/v4/catalog/asset-alerts?assets=btc&alerts=xxx&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `all asset alerts for btc`() {
        val expectedResponse =
            """{"data":[{"asset":"btc","name":"mempool_vsize_hi","conditions":[{"description":"test","constituents":["'mempool_vsize'"],"threshold":"300"}]}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/catalog-all/asset-alerts?assets=btc&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `specific asset alerts for all assets`() {
        val expectedResponse =
            """{"data":[{"asset":"btc","name":"mempool_vsize_hi","conditions":[{"description":"test","constituents":["'mempool_vsize'"],"threshold":"300"}]},{"asset":"usdc","name":"admin_key_change_1b_hi","conditions":[{"description":"test","constituents":["'test'"],"threshold":"1"}]},{"asset":"usdt_eth","name":"admin_key_change_1b_hi","conditions":[{"description":"test","constituents":["'test'"],"threshold":"1"}]}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/catalog-all/asset-alerts?alerts=mempool_vsize_hi,admin_key_change_1b_hi&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `all asset alerts for btc and usdc`() {
        val expectedResponse =
            """{"data":[{"asset":"btc","name":"mempool_vsize_hi","conditions":[{"description":"test","constituents":["'mempool_vsize'"],"threshold":"300"}]},{"asset":"usdc","name":"admin_key_change_1b_hi","conditions":[{"description":"test","constituents":["'test'"],"threshold":"1"}]},{"asset":"usdc","name":"admin_key_change_inflation_event_120b_hi","conditions":[{"description":"test1","constituents":["'test1'"],"threshold":"1"},{"description":"test2","constituents":["'test1'","'test2'"]}]}]}"""
        assertResponse(200, expectedResponse, "/v4/catalog/asset-alerts?assets=btc,usdc&api_key=$TEST_API_KEY")
        assertResponse(200, expectedResponse, "/v4/catalog/asset-alerts?assets=BTC,usdc&api_key=$TEST_API_KEY")
        assertResponse(200, expectedResponse, "/v4/catalog/asset-alerts?assets=BTC,USDC&api_key=$TEST_API_KEY")
    }

    @Test
    fun `specific asset alerts for btc`() {
        val expectedResponse =
            """{"data":[{"asset":"btc","name":"mempool_vsize_hi","conditions":[{"description":"test","constituents":["'mempool_vsize'"],"threshold":"300"}]}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/catalog/asset-alerts?assets=btc&alerts=mempool_vsize_hi,mempool_count_5m_lo&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `specific asset alerts for btc, including unsupported`() {
        val expectedResponse =
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'assets'. All requested assets don't support '[admin_key_change_inflation_event_120b_hi]' alerts."}}"""
        assertResponse(
            400,
            expectedResponse,
            "/v4/catalog/asset-alerts?assets=btc&alerts=mempool_vsize_hi,mempool_count_5m_lo,admin_key_change_inflation_event_120b_hi&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `specific asset alerts for btc and usdc`() {
        val expectedResponse =
            """{"data":[{"asset":"btc","name":"mempool_vsize_hi","conditions":[{"description":"test","constituents":["'mempool_vsize'"],"threshold":"300"}]},{"asset":"usdc","name":"admin_key_change_inflation_event_120b_hi","conditions":[{"description":"test1","constituents":["'test1'"],"threshold":"1"},{"description":"test2","constituents":["'test1'","'test2'"]}]}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/catalog/asset-alerts?assets=btc,usdc&alerts=mempool_vsize_hi,mempool_count_5m_lo,admin_key_change_inflation_event_120b_hi&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `all asset alerts`() {
        val expectedResponse =
            """{"data":[{"asset":"btc","name":"mempool_vsize_hi","conditions":[{"description":"test","constituents":["'mempool_vsize'"],"threshold":"300"}]},{"asset":"usdc","name":"admin_key_change_1b_hi","conditions":[{"description":"test","constituents":["'test'"],"threshold":"1"}]},{"asset":"usdc","name":"admin_key_change_inflation_event_120b_hi","conditions":[{"description":"test1","constituents":["'test1'"],"threshold":"1"},{"description":"test2","constituents":["'test1'","'test2'"]}]},{"asset":"usdt_eth","name":"admin_key_change_1b_hi","conditions":[{"description":"test","constituents":["'test'"],"threshold":"1"}]}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/catalog/asset-alerts?api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `test empty alerts catalog response for community key`() {
        val expectedResponse =
            """{"data":[]}"""
        assertResponse(200, expectedResponse, "/v4/catalog/asset-alerts?api_key=$COMMUNITY_KEY")
    }
}
