package io.coinmetrics.api.endpoints.catalog.v2

import io.coinmetrics.api.helper.BaseTest
import io.coinmetrics.api.helper.EMPTY_RESPONSE
import io.coinmetrics.api.helper.ERIS_X_API_KEY
import io.coinmetrics.api.helper.ONLY_ERIS_X_API_KEY
import io.coinmetrics.api.helper.TEST_API_KEY
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class GetCatalogV2MarketTradesEndpointImplTest : BaseTest() {
    @Test
    fun `should return market trades in json format by default`() {
        getResponse("/v4/catalog-v2/market-trades?api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should return market trades in json format`() {
        getResponse("/v4/catalog-v2/market-trades?api_key=$TEST_API_KEY&format=json").assertResponse()
    }

    @Test
    fun `should return market trades filtered by base and quote`() {
        val expectedResponse =
            """{"data":[{"market":"binance-XBTUSD-future","min_time":"2019-03-15T09:51:28.851000000Z","max_time":"2019-03-15T10:36:46.428000000Z"},{"market":"binance.us-btc-usd-spot","min_time":"2019-03-15T10:36:46.428000000Z","max_time":"2019-05-15T10:00:00.000000000Z"},{"market":"bitmex-XBTUSD-future","min_time":"2019-03-15T09:51:28.851000000Z","max_time":"2019-03-20T00:00:00.000000000Z"},{"market":"bittrex-btc-usd-spot","min_time":"2019-03-15T09:51:28.851000000Z","max_time":"2019-03-15T10:36:46.428000000Z"},{"market":"cme-BTCQ1-future","min_time":"2020-03-15T10:36:46.428000000Z","max_time":"2021-03-15T10:36:46.428000000Z"},{"market":"deribit-BTC-29MAR21-54000-C-option","min_time":"2020-03-15T10:36:46.428000000Z","max_time":"2021-03-15T10:36:46.428000000Z"},{"market":"deribit-BTC-9APR21-50000-P-option","min_time":"2019-03-15T09:51:28.851000000Z","max_time":"2019-03-20T00:00:00.000000000Z"},{"market":"huobi-BTC-9APR21-50000-P4-option","min_time":"2019-03-15T10:36:31.852000000Z","max_time":"2019-03-15T10:36:31.852000000Z"},{"market":"huobi-XBTUSD4-future","min_time":"2019-03-15T10:36:31.852000000Z","max_time":"2019-03-15T10:36:31.852000000Z"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/catalog-v2/market-trades?api_key=$TEST_API_KEY&base=btc&quote=usd",
        )
    }

    @Test
    fun `should return 400 filtering market trades by unknown base`() {
        assertResponse(
            400,
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'base'. Value 'wth' is not supported."}}""",
            "/v4/catalog-v2/market-trades?api_key=$TEST_API_KEY&base=wth&quote=eth",
        )
    }

    @Test
    fun `should return empty list of market trades filtered by unsupported base`() {
        assertResponse(
            200,
            EMPTY_RESPONSE,
            "/v4/catalog-v2/market-trades?api_key=$TEST_API_KEY&base=swth&quote=eth",
        )
    }

    @Test
    fun `should return market trades filtered by symbol`() {
        val expectedResponse =
            """{"data":[{"market":"binance-XBTUSD-future","min_time":"2019-03-15T09:51:28.851000000Z","max_time":"2019-03-15T10:36:46.428000000Z"},{"market":"bitmex-XBTUSD-future","min_time":"2019-03-15T09:51:28.851000000Z","max_time":"2019-03-20T00:00:00.000000000Z"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/catalog-v2/market-trades?api_key=$TEST_API_KEY&symbol=XBTUSD",
        )
    }

    @Test
    fun `should return market trades filtered by exchange`() {
        val expectedResponse =
            """{"data":[{"market":"cme-BTCQ1-future","min_time":"2020-03-15T10:36:46.428000000Z","max_time":"2021-03-15T10:36:46.428000000Z"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/catalog-v2/market-trades?api_key=$TEST_API_KEY&exchange=cme",
        )
    }

    @Test
    fun `given unknown exchange when exchange parameter specified`() {
        getResponse("/v4/catalog-v2/market-trades?exchange=unknown_exchange&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should return market trades in json_stream format`() {
        getResponse("/v4/catalog-v2/market-trades?api_key=$TEST_API_KEY&format=json_stream").assertResponse()
    }

    @Test
    fun `should return market trades in json_stream format ignoring page_size`() {
        getResponse("/v4/catalog-v2/market-trades?api_key=$TEST_API_KEY&format=json_stream&page_size=2").assertResponse()
    }

    @Test
    fun `should return market trades in json_stream format ignoring paging_from`() {
        getResponse("/v4/catalog-v2/market-trades?api_key=$TEST_API_KEY&format=json_stream&paging_from=end").assertResponse()
    }

    @Test
    fun `should return market trades in json_stream format ignoring 'pretty' parameter`() {
        getResponse("/v4/catalog-v2/market-trades?api_key=$TEST_API_KEY&format=json_stream&pretty=true").assertResponse()
    }

    @Test
    fun `should return 400 when format is invalid`() {
        val expectedResponse =
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'format'. Must be one of the following: json, json_stream."}}"""
        assertResponse(
            400,
            expectedResponse,
            "/v4/catalog-v2/market-trades?api_key=$TEST_API_KEY&format=ldjson",
        )
    }

    @Test
    fun `should return spot market trades`() {
        getResponse("/v4/catalog-v2/market-trades?api_key=$TEST_API_KEY&type=spot").assertResponse()
    }

    @Test
    fun `should return future market trades`() {
        val expectedResponse =
            """{"data":[{"market":"binance-XBTUSD-future","min_time":"2019-03-15T09:51:28.851000000Z","max_time":"2019-03-15T10:36:46.428000000Z"},{"market":"bitmex-FILUSD-future","min_time":"2023-09-17T00:15:51.000000000Z","max_time":"2023-09-17T00:15:51.000000000Z"},{"market":"bitmex-XBTUSD-future","min_time":"2019-03-15T09:51:28.851000000Z","max_time":"2019-03-20T00:00:00.000000000Z"},{"market":"cme-BTCQ1-future","min_time":"2020-03-15T10:36:46.428000000Z","max_time":"2021-03-15T10:36:46.428000000Z"},{"market":"deribit-ETH-7JUN24-future","min_time":"2024-05-30T11:04:48.000000000Z","max_time":"2024-05-30T11:04:48.000000000Z"},{"market":"deribit-ETH-PERPETUAL-future","min_time":"2024-05-30T11:03:14.000000000Z","max_time":"2024-05-30T11:03:14.000000000Z"},{"market":"ftx-SOL-0925-future","min_time":"2019-03-15T10:36:46.428000000Z","max_time":"2019-03-15T10:36:46.428000000Z"},{"market":"huobi-XBTUSD4-future","min_time":"2019-03-15T10:36:31.852000000Z","max_time":"2019-03-15T10:36:31.852000000Z"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/catalog-v2/market-trades?api_key=$TEST_API_KEY&type=future",
        )
    }

    @Test
    fun `should return 400 when type is invalid`() {
        assertResponse(
            400,
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'type'. Value 'non-supported' is not supported. Supported values are 'spot', 'future', 'option'."}}""",
            "/v4/catalog-v2/market-trades?api_key=$TEST_API_KEY&type=non-supported",
        )
    }

    @Test
    fun `should return 400 when both markets and type specified`() {
        val expectedResponse =
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'markets'. Cannot combine filtering by 'markets' with other filtering options."}}"""
        assertResponse(
            400,
            expectedResponse,
            "/v4/catalog-v2/market-trades?api_key=$TEST_API_KEY&markets=bittrex-btc-usd-spot&type=future",
        )
    }

    @Test
    fun `should return 400 when asset specified and type is future`() {
        val expectedResponse =
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'type'. 'type' should be 'spot' when filtering by 'asset'."}}"""
        assertResponse(
            400,
            expectedResponse,
            "/v4/catalog-v2/market-trades?api_key=$TEST_API_KEY&asset=btc&type=future",
        )
    }

    @Test
    fun `should return 400 when both asset and quote specified`() {
        val expectedResponse =
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'asset'. Cannot combine filtering by 'asset' with other filtering options."}}"""
        assertResponse(
            400,
            expectedResponse,
            "/v4/catalog-v2/market-trades?api_key=$TEST_API_KEY&asset=btc&quote=eth",
        )
    }

    @Test
    fun `should return 400 when both symbol and quote specified`() {
        val expectedResponse =
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'symbol'. Cannot combine filtering by 'symbol' with other filtering options."}}"""
        assertResponse(
            400,
            expectedResponse,
            "/v4/catalog-v2/market-trades?api_key=$TEST_API_KEY&symbol=btc&quote=eth",
        )
    }

    @Test
    fun `should return 400 when symbol specified and type is spot`() {
        val expectedResponse =
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'type'. 'type' should not be 'spot' when filtering by 'symbol'."}}"""
        assertResponse(
            400,
            expectedResponse,
            "/v4/catalog-v2/market-trades?api_key=$TEST_API_KEY&symbol=btc&type=spot",
        )
    }

    @Test
    fun `should return all future market trades by base=btc`() {
        val expectedResponse =
            """{"data":[{"market":"binance-XBTUSD-future","min_time":"2019-03-15T09:51:28.851000000Z","max_time":"2019-03-15T10:36:46.428000000Z"},{"market":"bitmex-XBTUSD-future","min_time":"2019-03-15T09:51:28.851000000Z","max_time":"2019-03-20T00:00:00.000000000Z"},{"market":"cme-BTCQ1-future","min_time":"2020-03-15T10:36:46.428000000Z","max_time":"2021-03-15T10:36:46.428000000Z"},{"market":"huobi-XBTUSD4-future","min_time":"2019-03-15T10:36:31.852000000Z","max_time":"2019-03-15T10:36:31.852000000Z"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/catalog-v2/market-trades?api_key=$TEST_API_KEY&base=btc&type=future",
        )
    }

    @Test
    fun `should return the first page of market trades`() {
        val expectedResponse =
            """{"data":[{"market":"binance-XBTUSD-future","min_time":"2019-03-15T09:51:28.851000000Z","max_time":"2019-03-15T10:36:46.428000000Z"},{"market":"binance-btc-usdt-spot","min_time":"2019-03-15T09:51:28.851000000Z","max_time":"2019-03-15T10:36:46.428000000Z"},{"market":"binance-undef314159-usdt-spot","min_time":"2019-03-15T10:36:46.428000000Z","max_time":"2019-03-15T10:36:46.428000000Z"},{"market":"binance.us-btc-usd-spot","min_time":"2019-03-15T10:36:46.428000000Z","max_time":"2019-05-15T10:00:00.000000000Z"},{"market":"bitmex-FILUSD-future","min_time":"2023-09-17T00:15:51.000000000Z","max_time":"2023-09-17T00:15:51.000000000Z"}],"next_page_token":"Yml0bWV4LVhCVFVTRC1mdXR1cmU","next_page_url":"http://127.0.0.1:8080/v4/catalog-v2/market-trades?api_key=x1&page_size=5&next_page_token=Yml0bWV4LVhCVFVTRC1mdXR1cmU"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/catalog-v2/market-trades?api_key=$TEST_API_KEY&page_size=5",
        )
    }

    @Test
    fun `should return the second page of market trades`() {
        val expectedResponse =
            """{"data":[{"market":"bittrex-btc-usd-spot","min_time":"2019-03-15T09:51:28.851000000Z","max_time":"2019-03-15T10:36:46.428000000Z"},{"market":"bybit-BTC-9APR21-50000-C-option","min_time":"2024-02-27T21:33:47.010246000Z","max_time":"2024-02-27T21:33:47.010246000Z"},{"market":"cme-BTCQ1-future","min_time":"2020-03-15T10:36:46.428000000Z","max_time":"2021-03-15T10:36:46.428000000Z"},{"market":"curve_eth-1-cbeth-eth-spot","min_time":"2020-05-12T16:44:41.000000000Z","max_time":"2020-05-12T16:44:41.000000000Z"},{"market":"curve_eth-1-tusd_2_eth-dai-spot","min_time":"2020-05-12T16:44:41.000000000Z","max_time":"2020-05-12T16:44:41.000000000Z"}],"next_page_token":"Y3VydmVfZXRoLTItYWdldXJfZXRoLWV1cmNfZXRoLXNwb3Q","next_page_url":"http://127.0.0.1:8080/v4/catalog-v2/market-trades?api_key=x1&page_size=5&next_page_token=Y3VydmVfZXRoLTItYWdldXJfZXRoLWV1cmNfZXRoLXNwb3Q"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/catalog-v2/market-trades?api_key=$TEST_API_KEY&page_size=5&next_page_token=Yml0dHJleC1idGMtdXNkLXNwb3Q",
        )
    }

    @Test
    fun `should return the third page of market trades`() {
        val expectedResponse =
            """{"data":[{"market":"deribit-BTC-9APR21-50000-P-option","min_time":"2019-03-15T09:51:28.851000000Z","max_time":"2019-03-20T00:00:00.000000000Z"},{"market":"deribit-ETH-2APR21-1960-C-option","min_time":"2019-03-15T10:36:46.428000000Z","max_time":"2019-03-15T10:36:46.428000000Z"},{"market":"deribit-ETH-7JUN24-future","min_time":"2024-05-30T11:04:48.000000000Z","max_time":"2024-05-30T11:04:48.000000000Z"},{"market":"deribit-ETH-PERPETUAL-future","min_time":"2024-05-30T11:03:14.000000000Z","max_time":"2024-05-30T11:03:14.000000000Z"},{"market":"deribit-btc-usdc-spot","min_time":"2024-05-30T10:21:45.000000000Z","max_time":"2024-05-30T10:21:45.000000000Z"}],"next_page_token":"ZGVyaWJpdC1ldGgtdXNkYy1zcG90","next_page_url":"http://127.0.0.1:8080/v4/catalog-v2/market-trades?api_key=x1&page_size=5&next_page_token=ZGVyaWJpdC1ldGgtdXNkYy1zcG90"}""".trimMargin()
        assertResponse(
            200,
            expectedResponse,
            "/v4/catalog-v2/market-trades?api_key=$TEST_API_KEY&page_size=5&next_page_token=ZGVyaWJpdC1CVEMtOUFQUjIxLTUwMDAwLVAtb3B0aW9u",
        )
    }

    @Test
    fun `should return the fourth page of market trades`() {
        getResponse("/v4/catalog-v2/market-trades?api_key=$TEST_API_KEY&page_size=5&next_page_token=aHVvYmktYWRhLWJ0Yy1zcG90").assertResponse()
    }

    @Test
    fun `should return the last page of market trades`() {
        getResponse("/v4/catalog-v2/market-trades?api_key=$TEST_API_KEY&page_size=5&next_page_token=dW5pc3dhcF92M19ldGgtMy1nbG0td2V0aC1zcG90").assertResponse()
    }

    @Test
    fun `should return the first page of market trades filtering by exchange`() {
        val expectedResponse =
            """{"data":[{"market":"binance-XBTUSD-future","min_time":"2019-03-15T09:51:28.851000000Z","max_time":"2019-03-15T10:36:46.428000000Z"},{"market":"binance-btc-usdt-spot","min_time":"2019-03-15T09:51:28.851000000Z","max_time":"2019-03-15T10:36:46.428000000Z"}],"next_page_token":"YmluYW5jZS11bmRlZjMxNDE1OS11c2R0LXNwb3Q","next_page_url":"http://127.0.0.1:8080/v4/catalog-v2/market-trades?api_key=x1&page_size=2&exchange=binance&next_page_token=YmluYW5jZS11bmRlZjMxNDE1OS11c2R0LXNwb3Q"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/catalog-v2/market-trades?api_key=$TEST_API_KEY&page_size=2&exchange=binance",
        )
    }

    @Test
    fun `should return the second page of market trades filtering by exchange`() {
        val expectedResponse =
            """{"data":[{"market":"binance-undef314159-usdt-spot","min_time":"2019-03-15T10:36:46.428000000Z","max_time":"2019-03-15T10:36:46.428000000Z"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/catalog-v2/market-trades?api_key=$TEST_API_KEY&page_size=2&exchange=binance&next_page_token=YmluYW5jZS11bmRlZjMxNDE1OS11c2R0LXNwb3Q",
        )
    }

    @Test
    fun `should return the first page of market trades filtering by exchange and paging from end`() {
        val expectedResponse =
            """{"data":[{"market":"binance-btc-usdt-spot","min_time":"2019-03-15T09:51:28.851000000Z","max_time":"2019-03-15T10:36:46.428000000Z"},{"market":"binance-undef314159-usdt-spot","min_time":"2019-03-15T10:36:46.428000000Z","max_time":"2019-03-15T10:36:46.428000000Z"}],"next_page_token":"YmluYW5jZS1YQlRVU0QtZnV0dXJl","next_page_url":"http://127.0.0.1:8080/v4/catalog-v2/market-trades?api_key=x1&page_size=2&paging_from=end&exchange=binance&next_page_token=YmluYW5jZS1YQlRVU0QtZnV0dXJl"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/catalog-v2/market-trades?api_key=$TEST_API_KEY&page_size=2&paging_from=end&exchange=binance",
        )
    }

    @Test
    fun `should return the second page of market trades filtering by exchange and paging from end`() {
        val expectedResponse =
            """{"data":[{"market":"binance-XBTUSD-future","min_time":"2019-03-15T09:51:28.851000000Z","max_time":"2019-03-15T10:36:46.428000000Z"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/catalog-v2/market-trades?api_key=$TEST_API_KEY&page_size=2&paging_from=end&exchange=binance&next_page_token=YmluYW5jZS1YQlRVU0QtZnV0dXJl",
        )
    }

    @Test
    fun `given unauthorized key when ErisX market requested`() {
        getResponse("/v4/catalog-v2/market-trades?api_key=$TEST_API_KEY&markets=erisx-btc-usd-spot").assertResponse()
    }

    @Test
    fun `catalog-all-v2 should return data when ErisX market is specified with an unauthorized key`() {
        val expectedResponse = """{"data":[{"market":"erisx-btc-usd-spot","min_time":"2023-10-15T10:36:46.428000000Z","max_time":"2023-10-15T10:36:46.428000000Z"}]}"""
        assertResponse(
            expectedCode = 200,
            expectedResponse = expectedResponse,
            pathAndQuery = "/v4/catalog-all-v2/market-trades?api_key=$TEST_API_KEY&markets=erisx-btc-usd-spot",
        )
    }

    @Test
    fun `catalog-v2 and catalog-all-v2 should return only ErisX data when ErisX market is specified with the authorized key`() {
        val expectedResponse = """{"data":[{"market":"erisx-btc-usd-spot","min_time":"2023-10-15T10:36:46.428000000Z","max_time":"2023-10-15T10:36:46.428000000Z"}]}"""
        assertResponse(
            expectedCode = 200,
            expectedResponse = expectedResponse,
            pathAndQuery = "/v4/catalog-v2/market-trades?api_key=$ERIS_X_API_KEY&markets=erisx-btc-usd-spot",
        )
        assertResponse(
            expectedCode = 200,
            expectedResponse = expectedResponse,
            pathAndQuery = "/v4/catalog-all-v2/market-trades?api_key=$ERIS_X_API_KEY&markets=erisx-btc-usd-spot",
        )
    }

    @Test
    fun `given api key with erisx_market_data and market_data_feed_package when no specific markets requested`() {
        getResponse("/v4/catalog-v2/market-trades?api_key=$ERIS_X_API_KEY").assertResponse()
    }

    @Test
    fun `given api key authorized for ErisX only when no specific markets requested`() {
        getResponse("/v4/catalog-v2/market-trades?api_key=$ONLY_ERIS_X_API_KEY").assertResponse()
    }

    @Test
    fun `catalog-v2 should return all requested markets with one of markets is not ErisX and ErisX api key`() {
        assertResponse(
            expectedCode = 200,
            expectedResponse = """{"data":[{"market":"bittrex-btc-usd-spot","min_time":"2019-03-15T09:51:28.851000000Z","max_time":"2019-03-15T10:36:46.428000000Z"},{"market":"erisx-btc-usd-spot","min_time":"2023-10-15T10:36:46.428000000Z","max_time":"2023-10-15T10:36:46.428000000Z"}]}""",
            pathAndQuery = "/v4/catalog-v2/market-trades?markets=erisx-btc-usd-spot,bittrex-btc-usd-spot,erisx-btc-usd-spot,bittrex-btc-usd-spot,&api_key=$ERIS_X_API_KEY",
        )
    }

    @Test
    fun `catalog-v2 should return 403 with one of markets is not ErisX and only ErisX api key`() {
        getResponse("/v4/catalog-v2/market-trades?markets=bittrex-btc-usd-spot,erisx-btc-usd-spot&api_key=$ONLY_ERIS_X_API_KEY").assertResponse()
    }

    @Test
    fun `catalog-all-v2 should return all data with no parameters and ErisX api key`() {
        getResponse("/v4/catalog-all-v2/market-trades?api_key=$ERIS_X_API_KEY").assertResponse()
    }

    @Test
    fun `should return market trades in pretty format`() {
        val expectedResponse =
            """{
  "data" : [ {
  "market" : "binance-XBTUSD-future",
  "min_time" : "2019-03-15T09:51:28.851000000Z",
  "max_time" : "2019-03-15T10:36:46.428000000Z"
}, {
  "market" : "binance-btc-usdt-spot",
  "min_time" : "2019-03-15T09:51:28.851000000Z",
  "max_time" : "2019-03-15T10:36:46.428000000Z"
}, {
  "market" : "binance-undef314159-usdt-spot",
  "min_time" : "2019-03-15T10:36:46.428000000Z",
  "max_time" : "2019-03-15T10:36:46.428000000Z"
}, {
  "market" : "binance.us-btc-usd-spot",
  "min_time" : "2019-03-15T10:36:46.428000000Z",
  "max_time" : "2019-05-15T10:00:00.000000000Z"
}, {
  "market" : "bitmex-FILUSD-future",
  "min_time" : "2023-09-17T00:15:51.000000000Z",
  "max_time" : "2023-09-17T00:15:51.000000000Z"
}, {
  "market" : "bitmex-XBTUSD-future",
  "min_time" : "2019-03-15T09:51:28.851000000Z",
  "max_time" : "2019-03-20T00:00:00.000000000Z"
}, {
  "market" : "bittrex-btc-usd-spot",
  "min_time" : "2019-03-15T09:51:28.851000000Z",
  "max_time" : "2019-03-15T10:36:46.428000000Z"
}, {
  "market" : "bybit-BTC-9APR21-50000-C-option",
  "min_time" : "2024-02-27T21:33:47.010246000Z",
  "max_time" : "2024-02-27T21:33:47.010246000Z"
}, {
  "market" : "cme-BTCQ1-future",
  "min_time" : "2020-03-15T10:36:46.428000000Z",
  "max_time" : "2021-03-15T10:36:46.428000000Z"
}, {
  "market" : "curve_eth-1-cbeth-eth-spot",
  "min_time" : "2020-05-12T16:44:41.000000000Z",
  "max_time" : "2020-05-12T16:44:41.000000000Z"
}, {
  "market" : "curve_eth-1-tusd_2_eth-dai-spot",
  "min_time" : "2020-05-12T16:44:41.000000000Z",
  "max_time" : "2020-05-12T16:44:41.000000000Z"
}, {
  "market" : "curve_eth-2-ageur_eth-eurc_eth-spot",
  "min_time" : "2020-05-12T16:44:41.000000000Z",
  "max_time" : "2020-05-12T16:44:41.000000000Z"
}, {
  "market" : "deribit-BTC-21JUN24-70000-P-option",
  "min_time" : "2024-05-30T11:24:08.000000000Z",
  "max_time" : "2024-05-30T11:24:08.000000000Z"
}, {
  "market" : "deribit-BTC-29MAR21-54000-C-option",
  "min_time" : "2020-03-15T10:36:46.428000000Z",
  "max_time" : "2021-03-15T10:36:46.428000000Z"
}, {
  "market" : "deribit-BTC-31MAY24-67500-C-option",
  "min_time" : "2024-05-30T11:27:44.000000000Z",
  "max_time" : "2024-05-30T11:27:44.000000000Z"
}, {
  "market" : "deribit-BTC-9APR21-50000-C-option",
  "min_time" : "2024-02-27T21:33:47.010246000Z",
  "max_time" : "2024-02-27T21:33:47.010246000Z"
}, {
  "market" : "deribit-BTC-9APR21-50000-P-option",
  "min_time" : "2019-03-15T09:51:28.851000000Z",
  "max_time" : "2019-03-20T00:00:00.000000000Z"
}, {
  "market" : "deribit-ETH-2APR21-1960-C-option",
  "min_time" : "2019-03-15T10:36:46.428000000Z",
  "max_time" : "2019-03-15T10:36:46.428000000Z"
}, {
  "market" : "deribit-ETH-7JUN24-future",
  "min_time" : "2024-05-30T11:04:48.000000000Z",
  "max_time" : "2024-05-30T11:04:48.000000000Z"
}, {
  "market" : "deribit-ETH-PERPETUAL-future",
  "min_time" : "2024-05-30T11:03:14.000000000Z",
  "max_time" : "2024-05-30T11:03:14.000000000Z"
}, {
  "market" : "deribit-btc-usdc-spot",
  "min_time" : "2024-05-30T10:21:45.000000000Z",
  "max_time" : "2024-05-30T10:21:45.000000000Z"
}, {
  "market" : "deribit-eth-usdc-spot",
  "min_time" : "2024-05-30T10:18:47.000000000Z",
  "max_time" : "2024-05-30T10:18:47.000000000Z"
}, {
  "market" : "ftx-SOL-0925-future",
  "min_time" : "2019-03-15T10:36:46.428000000Z",
  "max_time" : "2019-03-15T10:36:46.428000000Z"
}, {
  "market" : "hitbtc-vanry-btc-spot",
  "min_time" : "2024-06-15T08:45:04.000000000Z",
  "max_time" : "2024-06-15T08:45:04.000000000Z"
}, {
  "market" : "huobi-BTC-9APR21-50000-C-option",
  "min_time" : "2024-02-27T21:33:47.010246000Z",
  "max_time" : "2024-02-27T21:33:47.010246000Z"
}, {
  "market" : "huobi-BTC-9APR21-50000-P4-option",
  "min_time" : "2019-03-15T10:36:31.852000000Z",
  "max_time" : "2019-03-15T10:36:31.852000000Z"
}, {
  "market" : "huobi-XBTUSD4-future",
  "min_time" : "2019-03-15T10:36:31.852000000Z",
  "max_time" : "2019-03-15T10:36:31.852000000Z"
}, {
  "market" : "huobi-ada-btc-spot",
  "min_time" : "2019-03-15T10:36:31.852000000Z",
  "max_time" : "2019-03-15T10:36:31.852000000Z"
}, {
  "market" : "sushiswap_v1_eth-sushi-usdt_eth-spot",
  "min_time" : "2020-09-04T10:44:02.000000000Z",
  "max_time" : "2020-09-05T09:20:32.000000000Z"
}, {
  "market" : "uniswap_v2_eth-usdc-weth-spot",
  "min_time" : "2020-05-05T21:09:32.000000000Z",
  "max_time" : "2020-05-12T16:44:41.000000000Z"
}, {
  "market" : "uniswap_v3_base-1-usdc_base.eth-cbbtc_base.eth-spot",
  "min_time" : "2020-09-04T10:44:02.000000000Z",
  "max_time" : "2020-09-04T15:53:03.000000000Z"
}, {
  "market" : "uniswap_v3_eth-2-link-weth-spot",
  "min_time" : "2021-05-05T20:31:28.000000000Z",
  "max_time" : "2021-05-05T20:46:44.000000000Z"
}, {
  "market" : "uniswap_v3_eth-3-glm-weth-spot",
  "min_time" : "2021-05-05T21:21:46.000000000Z",
  "max_time" : "2021-05-05T22:26:53.000000000Z"
} ]
}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/catalog-v2/market-trades?api_key=$TEST_API_KEY&pretty=true",
        )
    }
}
