package io.coinmetrics.api.endpoints.catalog.v2.farum

import io.coinmetrics.api.helper.BaseTest
import io.coinmetrics.api.helper.TEST_API_KEY
import io.coinmetrics.api.helper.TEST_API_KEY_2
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class GetCatalogV2MempoolFeeratesEndpointTest : BaseTest() {
    @Test
    fun `should return catalog`() {
        val expectedResponse =
            """{"data":[{"asset":"btc","min_time":"2021-06-09T00:00:00.000000000Z","max_time":"2021-06-09T00:02:00.000000000Z"}]}"""
        assertResponseWithContentType(
            200,
            expectedResponse,
            "/v4/catalog-v2/mempool-feerates?api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `should not return catalog for invalid key`() {
        val expectedResponse =
            """{"error":{"type":"forbidden","message":"Requested resource is not available with supplied credentials."}}"""
        assertResponseWithContentType(
            403,
            expectedResponse,
            "/v4/catalog-v2/mempool-feerates?api_key=$TEST_API_KEY_2",
        )
    }

    @Test
    fun `should return catalog-all`() {
        val expectedResponse =
            """{"data":[{"asset":"btc","min_time":"2021-06-09T00:00:00.000000000Z","max_time":"2021-06-09T00:02:00.000000000Z"}]}"""
        assertResponseWithContentType(
            200,
            expectedResponse,
            "/v4/catalog-all-v2/mempool-feerates?api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `should return catalog-all with no permissions key`() {
        val expectedResponse =
            """{"data":[{"asset":"btc","min_time":"2021-06-09T00:00:00.000000000Z","max_time":"2021-06-09T00:02:00.000000000Z"}]}"""
        assertResponseWithContentType(
            200,
            expectedResponse,
            "/v4/catalog-all-v2/mempool-feerates?api_key=$TEST_API_KEY_2",
        )
    }

    @Test
    fun `should return btc only catalog`() {
        val expectedResponse =
            """{"data":[{"asset":"btc","min_time":"2021-06-09T00:00:00.000000000Z","max_time":"2021-06-09T00:02:00.000000000Z"}]}"""
        assertResponseWithContentType(
            200,
            expectedResponse,
            "/v4/catalog-v2/mempool-feerates?assets=btc&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `should return empty catalog when eth requested`() {
        val expectedResponse =
            """{"data":[]}"""
        assertResponseWithContentType(
            200,
            expectedResponse,
            "/v4/catalog-v2/mempool-feerates?assets=eth&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `should return mempool-feerates assets in json_stream format`() {
        val expectedResponse =
            """{"asset":"btc","min_time":"2021-06-09T00:00:00.000000000Z","max_time":"2021-06-09T00:02:00.000000000Z"}"""
        assertResponseWithContentType(
            200,
            expectedResponse,
            "/v4/catalog-v2/mempool-feerates?api_key=$TEST_API_KEY&format=json_stream",
            "application/x-ndjson",
        )
    }
}
