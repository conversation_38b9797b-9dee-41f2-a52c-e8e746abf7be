package io.coinmetrics.api.endpoints.catalog.v2

import io.coinmetrics.api.helper.BaseTest
import io.coinmetrics.api.helper.TEST_API_KEY
import io.coinmetrics.api.helper.TEST_API_KEY_2
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class GetCatalogV2BlockchainTransactionsEndpointTest : BaseTest() {
    private val fullResponse = """{"data":[{"asset":"algo","min_time":"2019-06-23T02:10:10.000000000Z","max_time":"2019-06-23T02:10:14.000000000Z","experimental":true},{"asset":"btc","min_time":"2010-01-17T06:16:08.000000000Z","max_time":"2010-01-17T07:17:16.000000000Z"},{"asset":"doge","min_time":"2021-12-06T23:07:41.000000000Z","max_time":"2021-12-06T23:07:41.000000000Z"},{"asset":"ltc","min_time":"2021-12-09T00:50:44.000000000Z","max_time":"2021-12-09T00:50:57.000000000Z","experimental":true}]}"""
    private val pagingFromEndResponse = """{"data":[{"asset":"doge","min_time":"2021-12-06T23:07:41.000000000Z","max_time":"2021-12-06T23:07:41.000000000Z"},{"asset":"ltc","min_time":"2021-12-09T00:50:44.000000000Z","max_time":"2021-12-09T00:50:57.000000000Z","experimental":true}],"next_page_token":"YnRj","next_page_url":"http://127.0.0.1:8080/v4/catalog-v2/blockchain-v2/transactions?api_key=x1&paging_from=end&page_size=2&next_page_token=YnRj"}"""
    private val singleResponsePage1 = """{"data":[{"asset":"algo","min_time":"2019-06-23T02:10:10.000000000Z","max_time":"2019-06-23T02:10:14.000000000Z","experimental":true}],"next_page_token":"YnRj","next_page_url":"http://127.0.0.1:8080/v4/catalog-v2/blockchain-v2/transactions?api_key=x1&page_size=1&next_page_token=YnRj"}"""
    private val singleResponsePage2 = """{"data":[{"asset":"btc","min_time":"2010-01-17T06:16:08.000000000Z","max_time":"2010-01-17T07:17:16.000000000Z"}],"next_page_token":"ZG9nZQ","next_page_url":"http://127.0.0.1:8080/v4/catalog-v2/blockchain-v2/transactions?api_key=x1&page_size=1&next_page_token=ZG9nZQ"}"""
    private val emptyResponse = """{"data":[]}"""

    @Test
    fun `atlas catalog-v2 test for transactions endpoint`() {
        assertResponseWithContentType(
            200,
            fullResponse,
            "/v4/catalog-v2/blockchain-v2/transactions?api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `atlas catalog-v2 test for transactions endpoint with page size of 1`() {
        assertResponseWithContentType(
            200,
            singleResponsePage1,
            "/v4/catalog-v2/blockchain-v2/transactions?api_key=$TEST_API_KEY&page_size=1",
        )
    }

    @Test
    fun `atlas catalog-v2 test for transactions endpoint for page 2`() {
        assertResponseWithContentType(
            200,
            singleResponsePage2,
            "/v4/catalog-v2/blockchain-v2/transactions?api_key=$TEST_API_KEY&page_size=1&next_page_token=YnRj",
        )
    }

    @Test
    fun `atlas catalog-v2 test for transactions endpoint for page from end`() {
        assertResponseWithContentType(
            200,
            pagingFromEndResponse,
            "/v4/catalog-v2/blockchain-v2/transactions?api_key=$TEST_API_KEY&paging_from=end&page_size=2",
        )
    }

    @Test
    fun `atlas catalog-v2 test for transactions endpoint with valid key but invalid assets`() {
        assertResponseWithContentType(
            200,
            emptyResponse,
            "/v4/catalog-v2/blockchain-v2/transactions?api_key=$TEST_API_KEY&assets=xyzzy",
        )
    }

    @Test
    fun `atlas catalog-v2 test for transactions endpoint with no permission key`() {
        assertResponseWithContentType(
            200,
            emptyResponse,
            "/v4/catalog-v2/blockchain-v2/transactions?api_key=$TEST_API_KEY_2",
        )
    }

    @Test
    fun `atlas catalog-all-v2 test for transactions endpoint`() {
        assertResponseWithContentType(
            200,
            fullResponse,
            "/v4/catalog-all-v2/blockchain-v2/transactions?api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `atlas catalog-all-v2 test for accounts transactions with no permission key`() {
        assertResponseWithContentType(
            200,
            fullResponse,
            "/v4/catalog-all-v2/blockchain-v2/transactions?api_key=$TEST_API_KEY_2",
        )
    }
}
