package io.coinmetrics.api.endpoints.catalog.farum
//
import io.coinmetrics.api.helper.BaseTest
import io.coinmetrics.api.helper.TEST_API_KEY
import io.coinmetrics.api.helper.TEST_API_KEY_2
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class AssetChainsCatalogTest : BaseTest() {
    @Test
    fun `should return catalog`() {
        val expectedResponse =
            """{"data":[{"asset":"btc","min_time":"2021-01-01T08:00:00.000000000Z","max_time":"2021-01-04T08:00:00.000000000Z"},{"asset":"eth","min_time":"2022-02-16T09:10:20.000000000Z","max_time":"2023-03-31T12:54:11.000000000Z"}]}"""
        assertResponseWithContentType(
            200,
            expectedResponse,
            "/v4/catalog/asset-chains?api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `should not return catalog with no permissions key`() {
        val expectedResponse =
            """{"error":{"type":"forbidden","message":"Requested resource is not available with supplied credentials."}}"""
        assertResponseWithContentType(
            403,
            expectedResponse,
            "/v4/catalog/asset-chains?api_key=$TEST_API_KEY_2",
        )
    }

    @Test
    fun `should return catalog-all`() {
        val expectedResponse =
            """{"data":[{"asset":"btc","min_time":"2021-01-01T08:00:00.000000000Z","max_time":"2021-01-04T08:00:00.000000000Z"},{"asset":"eth","min_time":"2022-02-16T09:10:20.000000000Z","max_time":"2023-03-31T12:54:11.000000000Z"}]}"""
        assertResponseWithContentType(
            200,
            expectedResponse,
            "/v4/catalog-all/asset-chains?api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `should return catalog-all with no permissions key`() {
        val expectedResponse =
            """{"data":[{"asset":"btc","min_time":"2021-01-01T08:00:00.000000000Z","max_time":"2021-01-04T08:00:00.000000000Z"},{"asset":"eth","min_time":"2022-02-16T09:10:20.000000000Z","max_time":"2023-03-31T12:54:11.000000000Z"}]}"""
        assertResponseWithContentType(
            200,
            expectedResponse,
            "/v4/catalog-all/asset-chains?api_key=$TEST_API_KEY_2",
        )
    }

    @Test
    fun `should return eth only catalog`() {
        val expectedResponse =
            """{"data":[{"asset":"eth","min_time":"2022-02-16T09:10:20.000000000Z","max_time":"2023-03-31T12:54:11.000000000Z"}]}"""
        assertResponseWithContentType(
            200,
            expectedResponse,
            "/v4/catalog/asset-chains?assets=eth&api_key=$TEST_API_KEY",
        )
    }
}
