package io.coinmetrics.api.endpoints.catalog.v2

import io.coinmetrics.api.helper.BaseTest
import io.coinmetrics.api.helper.TEST_API_KEY
import io.coinmetrics.api.model.S3BooksMarketType
import io.coinmetrics.testing.autoexpect.AutoExpect
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(AutoExpect::class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
open class GetCatalogV2MarketQuotesEndpointImplTest : BaseTest() {
    override fun getAdditionalEnvVariables(): Map<String, String> =
        mapOf(
            "API_${S3BooksMarketType.SPOT_BOOKS}_TIERS" to "COLD:[..2023-08-09),HOT:[2023-08-09..]",
            "API_${S3BooksMarketType.SPOT_BOOKS}_TIER_COLD_TYPE" to "S3",
            "API_${S3BooksMarketType.FUTURES_BOOKS}_TIERS" to "COLD:[..2023-08-09),HOT:[2023-08-09..]",
            "API_${S3BooksMarketType.FUTURES_BOOKS}_TIER_COLD_TYPE" to "S3",
            "API_${S3BooksMarketType.OPTIONS_BOOKS}_TIERS" to "COLD:[..2023-08-09),HOT:[2023-08-09..]",
            "API_${S3BooksMarketType.OPTIONS_BOOKS}_TIER_COLD_TYPE" to "S3",
        )

    @Test
    fun `should return market quotes in json format by default`() {
        getResponse("/v4/catalog-v2/market-quotes?api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should return market quotes in json format`() {
        getResponse("/v4/catalog-v2/market-quotes?api_key=$TEST_API_KEY&format=json").assertResponse()
    }

    @Test
    fun `should return market quotes filtered by base and quote`() {
        getResponse("/v4/catalog-v2/market-quotes?api_key=$TEST_API_KEY&base=btc&quote=usd").assertResponse()
    }

    @Test
    fun `should return 400 filtering market quotes by unknown base`() {
        getResponse("/v4/catalog-v2/market-quotes?api_key=$TEST_API_KEY&base=wth&quote=eth").assertResponse()
    }

    @Test
    fun `should return empty list of market quotes filtered by unsupported base`() {
        getResponse("/v4/catalog-v2/market-quotes?api_key=$TEST_API_KEY&base=swth&quote=eth").assertResponse()
    }

    @Test
    fun `should return market quotes filtered by symbol`() {
        getResponse("/v4/catalog-v2/market-quotes?api_key=$TEST_API_KEY&symbol=XBTUSD").assertResponse()
    }

    @Test
    fun `should return market quotes filtered by exchange`() {
        getResponse("/v4/catalog-v2/market-quotes?api_key=$TEST_API_KEY&exchange=cme").assertResponse()
    }

    @Test
    fun `should return market quotes in json_stream format`() {
        getResponse("/v4/catalog-v2/market-quotes?api_key=$TEST_API_KEY&format=json_stream").assertResponse()
    }

    @Test
    fun `should return market quotes in json_stream format ignoring 'pretty' parameter`() {
        getResponse("/v4/catalog-v2/market-quotes?api_key=$TEST_API_KEY&format=json_stream&pretty=true").assertResponse()
    }

    @Test
    fun `should return 400 when format is invalid`() {
        getResponse("/v4/catalog-v2/market-quotes?api_key=$TEST_API_KEY&format=ldjson").assertResponse()
    }

    @Test
    fun `should return spot market quotes`() {
        getResponse("/v4/catalog-v2/market-quotes?api_key=$TEST_API_KEY&type=spot").assertResponse()
    }

    @Test
    fun `should return future market quotes`() {
        getResponse("/v4/catalog-v2/market-quotes?api_key=$TEST_API_KEY&type=future").assertResponse()
    }

    @Test
    fun `should return 400 when type is invalid`() {
        getResponse("/v4/catalog-v2/market-quotes?api_key=$TEST_API_KEY&type=non-supported").assertResponse()
    }

    @Test
    fun `should return 400 when both markets and type specified`() {
        getResponse("/v4/catalog-v2/market-quotes?api_key=$TEST_API_KEY&markets=bittrex-btc-usd-spot&type=future").assertResponse()
    }

    @Test
    fun `should return 400 when asset specified and type is future`() {
        getResponse("/v4/catalog-v2/market-quotes?api_key=$TEST_API_KEY&asset=btc&type=future").assertResponse()
    }

    @Test
    fun `should return 400 when both asset and quote specified`() {
        getResponse("/v4/catalog-v2/market-quotes?api_key=$TEST_API_KEY&asset=btc&quote=eth").assertResponse()
    }

    @Test
    fun `should return 400 when both symbol and quote specified`() {
        getResponse("/v4/catalog-v2/market-quotes?api_key=$TEST_API_KEY&symbol=btc&quote=eth").assertResponse()
    }

    @Test
    fun `should return 400 when symbol specified and type is spot`() {
        getResponse("/v4/catalog-v2/market-quotes?api_key=$TEST_API_KEY&symbol=btc&type=spot").assertResponse()
    }

    @Test
    fun `should return all future market quotes by base=btc`() {
        getResponse("/v4/catalog-v2/market-quotes?api_key=$TEST_API_KEY&base=btc&type=future").assertResponse()
    }

    @Test
    fun `should return the first page of market quotes`() {
        getResponse("/v4/catalog-v2/market-quotes?api_key=$TEST_API_KEY&page_size=20").assertResponse()
    }

    @Test
    fun `should return the last page of market quotes`() {
        getResponse("/v4/catalog-v2/market-quotes?api_key=$TEST_API_KEY&page_size=20&next_page_token=ZGVyaWJpdC1FVEgtMU9DVDIxLTMyMDAtUC1vcHRpb24").assertResponse()
    }

    @Test
    fun `should return market quotes in pretty format`() {
        getResponse("/v4/catalog-v2/market-quotes?api_key=$TEST_API_KEY&pretty=true").assertResponse()
    }
}
