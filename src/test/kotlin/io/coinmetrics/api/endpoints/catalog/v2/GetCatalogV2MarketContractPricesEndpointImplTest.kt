package io.coinmetrics.api.endpoints.catalog.v2

import io.coinmetrics.api.helper.BaseTest
import io.coinmetrics.api.helper.EMPTY_RESPONSE
import io.coinmetrics.api.helper.TEST_API_KEY
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class GetCatalogV2MarketContractPricesEndpointImplTest : BaseTest() {
    companion object {
        private const val DEFAULT_JSON_RESPONSE =
            """{"data":[{"market":"bittrex-XRP-10AUG23-8000-C-option","min_time":"2023-08-09T00:00:00.000000000Z","max_time":"2023-08-10T00:00:00.000000000Z"},{"market":"bybit-ETH-25MAR22-1202-S-option","min_time":"2021-09-01T13:24:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"bybit-ETH-30APR23-2010-C-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"bybit-ETH-30APR23-2010-P-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"bybit-ETH-30MAY23-2020-C-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"bybit-ETH-31MAY23-2020-P-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"deribit-BTC-10NOV23-future","min_time":"2023-11-09T14:03:00.000000000Z","max_time":"2023-11-09T14:03:00.000000000Z"},{"market":"deribit-BTC-15OCT21-60000-C-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2021-09-30T15:59:00.000000000Z"},{"market":"deribit-BTC-17NOV23-future","min_time":"2023-11-09T14:01:00.000000000Z","max_time":"2023-11-09T14:02:00.000000000Z"},{"market":"deribit-BTC-1OCT21-75000-C-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2021-09-30T15:59:00.000000000Z"},{"market":"deribit-BTC-24NOV23-future","min_time":"2023-09-29T16:19:00.000000000Z","max_time":"2023-09-29T16:19:00.000000000Z"},{"market":"deribit-BTC-26NOV21-60000-C-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2021-09-30T15:59:00.000000000Z"},{"market":"deribit-BTC-8OCT21-50000-C-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2021-09-30T15:59:00.000000000Z"},{"market":"deribit-ETH-1OCT21-2850-P-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2023-01-14T08:05:00.000000000Z"},{"market":"deribit-ETH-1OCT21-3200-P-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2021-09-30T15:59:00.000000000Z"},{"market":"deribit-ETH-24JUN22-1000-C-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2021-09-30T15:59:00.000000000Z"},{"market":"deribit-ETH-25MAR22-1202-S-option","min_time":"2021-09-01T13:24:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"deribit-ETH-28JAN24-future","min_time":"2024-01-25T15:15:00.000000000Z","max_time":"2024-01-25T15:15:00.000000000Z"},{"market":"deribit-ETH-29OCT21-2000-P-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2021-09-30T15:59:00.000000000Z"},{"market":"deribit-ETH-2OCT21-3250-P-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2021-09-30T15:59:00.000000000Z"},{"market":"deribit-ETH-30APR23-2010-C-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"deribit-ETH-30APR23-2010-P-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"deribit-ETH-30MAY23-2020-C-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"deribit-ETH-31DEC21-4000-C-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2021-09-30T15:59:00.000000000Z"},{"market":"deribit-ETH-31MAY23-2020-P-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"deribit-XRP-31JAN24-future","min_time":"2024-01-30T13:00:00.000000000Z","max_time":"2024-01-30T13:00:00.000000000Z"},{"market":"deribit-XRP-31SEP23-2023-P-option","min_time":"2023-09-01T00:00:00.000000000Z","max_time":"2023-09-05T15:00:00.000000000Z"},{"market":"huobi-ETH-25MAR22-1202-S-option","min_time":"2021-09-01T13:24:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"huobi-ETH-30APR23-2010-C-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"huobi-ETH-30APR23-2010-P-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"huobi-ETH-30MAY23-2020-C-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"huobi-ETH-31MAY23-2020-P-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"huobi-XRP-31AUG23-2023-P-option","min_time":"2023-08-02T00:00:00.000000000Z","max_time":"2023-08-04T11:00:00.000000000Z"}]}"""

        private const val DEFAULT_NDJSON_RESPONSE =
            """
            {"market":"bittrex-XRP-10AUG23-8000-C-option","min_time":"2023-08-09T00:00:00.000000000Z","max_time":"2023-08-10T00:00:00.000000000Z"}
            {"market":"bybit-ETH-25MAR22-1202-S-option","min_time":"2021-09-01T13:24:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"}
            {"market":"bybit-ETH-30APR23-2010-C-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"}
            {"market":"bybit-ETH-30APR23-2010-P-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"}
            {"market":"bybit-ETH-30MAY23-2020-C-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"}
            {"market":"bybit-ETH-31MAY23-2020-P-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"}
            {"market":"deribit-BTC-10NOV23-future","min_time":"2023-11-09T14:03:00.000000000Z","max_time":"2023-11-09T14:03:00.000000000Z"}
            {"market":"deribit-BTC-15OCT21-60000-C-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2021-09-30T15:59:00.000000000Z"}
            {"market":"deribit-BTC-17NOV23-future","min_time":"2023-11-09T14:01:00.000000000Z","max_time":"2023-11-09T14:02:00.000000000Z"}
            {"market":"deribit-BTC-1OCT21-75000-C-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2021-09-30T15:59:00.000000000Z"}
            {"market":"deribit-BTC-24NOV23-future","min_time":"2023-09-29T16:19:00.000000000Z","max_time":"2023-09-29T16:19:00.000000000Z"}
            {"market":"deribit-BTC-26NOV21-60000-C-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2021-09-30T15:59:00.000000000Z"}
            {"market":"deribit-BTC-8OCT21-50000-C-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2021-09-30T15:59:00.000000000Z"}
            {"market":"deribit-ETH-1OCT21-2850-P-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2023-01-14T08:05:00.000000000Z"}
            {"market":"deribit-ETH-1OCT21-3200-P-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2021-09-30T15:59:00.000000000Z"}
            {"market":"deribit-ETH-24JUN22-1000-C-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2021-09-30T15:59:00.000000000Z"}
            {"market":"deribit-ETH-25MAR22-1202-S-option","min_time":"2021-09-01T13:24:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"}
            {"market":"deribit-ETH-28JAN24-future","min_time":"2024-01-25T15:15:00.000000000Z","max_time":"2024-01-25T15:15:00.000000000Z"}
            {"market":"deribit-ETH-29OCT21-2000-P-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2021-09-30T15:59:00.000000000Z"}
            {"market":"deribit-ETH-2OCT21-3250-P-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2021-09-30T15:59:00.000000000Z"}
            {"market":"deribit-ETH-30APR23-2010-C-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"}
            {"market":"deribit-ETH-30APR23-2010-P-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"}
            {"market":"deribit-ETH-30MAY23-2020-C-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"}
            {"market":"deribit-ETH-31DEC21-4000-C-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2021-09-30T15:59:00.000000000Z"}
            {"market":"deribit-ETH-31MAY23-2020-P-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"}
            {"market":"deribit-XRP-31JAN24-future","min_time":"2024-01-30T13:00:00.000000000Z","max_time":"2024-01-30T13:00:00.000000000Z"}
            {"market":"deribit-XRP-31SEP23-2023-P-option","min_time":"2023-09-01T00:00:00.000000000Z","max_time":"2023-09-05T15:00:00.000000000Z"}
            {"market":"huobi-ETH-25MAR22-1202-S-option","min_time":"2021-09-01T13:24:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"}
            {"market":"huobi-ETH-30APR23-2010-C-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"}
            {"market":"huobi-ETH-30APR23-2010-P-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"}
            {"market":"huobi-ETH-30MAY23-2020-C-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"}
            {"market":"huobi-ETH-31MAY23-2020-P-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"}
            {"market":"huobi-XRP-31AUG23-2023-P-option","min_time":"2023-08-02T00:00:00.000000000Z","max_time":"2023-08-04T11:00:00.000000000Z"}
    """
    }

    @Test
    fun `should return market contract prices in json format by default`() {
        assertResponseWithContentType(
            200,
            DEFAULT_JSON_RESPONSE,
            "/v4/catalog/market-contract-prices?api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `should return market contract prices in json format`() {
        assertResponseWithContentType(
            200,
            DEFAULT_JSON_RESPONSE,
            "/v4/catalog/market-contract-prices?api_key=$TEST_API_KEY&format=json",
        )
    }

    @Test
    fun `should return all supported market contract prices`() {
        assertResponse(
            expectedCode = 200,
            expectedResponse = DEFAULT_JSON_RESPONSE,
            pathAndQuery = "/v4/catalog-all/market-contract-prices?api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `should return market contract prices filtered by exchange`() {
        assertResponse(
            expectedCode = 200,
            expectedResponse = """{"data":[{"market":"deribit-BTC-10NOV23-future","min_time":"2023-11-09T14:03:00.000000000Z","max_time":"2023-11-09T14:03:00.000000000Z"},{"market":"deribit-BTC-15OCT21-60000-C-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2021-09-30T15:59:00.000000000Z"},{"market":"deribit-BTC-17NOV23-future","min_time":"2023-11-09T14:01:00.000000000Z","max_time":"2023-11-09T14:02:00.000000000Z"},{"market":"deribit-BTC-1OCT21-75000-C-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2021-09-30T15:59:00.000000000Z"},{"market":"deribit-BTC-24NOV23-future","min_time":"2023-09-29T16:19:00.000000000Z","max_time":"2023-09-29T16:19:00.000000000Z"},{"market":"deribit-BTC-26NOV21-60000-C-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2021-09-30T15:59:00.000000000Z"},{"market":"deribit-BTC-8OCT21-50000-C-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2021-09-30T15:59:00.000000000Z"},{"market":"deribit-ETH-1OCT21-2850-P-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2023-01-14T08:05:00.000000000Z"},{"market":"deribit-ETH-1OCT21-3200-P-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2021-09-30T15:59:00.000000000Z"},{"market":"deribit-ETH-24JUN22-1000-C-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2021-09-30T15:59:00.000000000Z"},{"market":"deribit-ETH-25MAR22-1202-S-option","min_time":"2021-09-01T13:24:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"deribit-ETH-28JAN24-future","min_time":"2024-01-25T15:15:00.000000000Z","max_time":"2024-01-25T15:15:00.000000000Z"},{"market":"deribit-ETH-29OCT21-2000-P-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2021-09-30T15:59:00.000000000Z"},{"market":"deribit-ETH-2OCT21-3250-P-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2021-09-30T15:59:00.000000000Z"},{"market":"deribit-ETH-30APR23-2010-C-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"deribit-ETH-30APR23-2010-P-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"deribit-ETH-30MAY23-2020-C-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"deribit-ETH-31DEC21-4000-C-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2021-09-30T15:59:00.000000000Z"},{"market":"deribit-ETH-31MAY23-2020-P-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"deribit-XRP-31JAN24-future","min_time":"2024-01-30T13:00:00.000000000Z","max_time":"2024-01-30T13:00:00.000000000Z"},{"market":"deribit-XRP-31SEP23-2023-P-option","min_time":"2023-09-01T00:00:00.000000000Z","max_time":"2023-09-05T15:00:00.000000000Z"}]}""",
            pathAndQuery = "/v4/catalog/market-contract-prices?api_key=$TEST_API_KEY&exchange=deribit",
        )
    }

    @Test
    fun `should return market contract prices filtered by type`() {
        assertResponseWithContentType(
            200,
            """{"data":[{"market":"bittrex-XRP-10AUG23-8000-C-option","min_time":"2023-08-09T00:00:00.000000000Z","max_time":"2023-08-10T00:00:00.000000000Z"},{"market":"bybit-ETH-25MAR22-1202-S-option","min_time":"2021-09-01T13:24:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"bybit-ETH-30APR23-2010-C-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"bybit-ETH-30APR23-2010-P-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"bybit-ETH-30MAY23-2020-C-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"bybit-ETH-31MAY23-2020-P-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"deribit-BTC-15OCT21-60000-C-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2021-09-30T15:59:00.000000000Z"},{"market":"deribit-BTC-1OCT21-75000-C-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2021-09-30T15:59:00.000000000Z"},{"market":"deribit-BTC-26NOV21-60000-C-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2021-09-30T15:59:00.000000000Z"},{"market":"deribit-BTC-8OCT21-50000-C-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2021-09-30T15:59:00.000000000Z"},{"market":"deribit-ETH-1OCT21-2850-P-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2023-01-14T08:05:00.000000000Z"},{"market":"deribit-ETH-1OCT21-3200-P-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2021-09-30T15:59:00.000000000Z"},{"market":"deribit-ETH-24JUN22-1000-C-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2021-09-30T15:59:00.000000000Z"},{"market":"deribit-ETH-25MAR22-1202-S-option","min_time":"2021-09-01T13:24:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"deribit-ETH-29OCT21-2000-P-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2021-09-30T15:59:00.000000000Z"},{"market":"deribit-ETH-2OCT21-3250-P-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2021-09-30T15:59:00.000000000Z"},{"market":"deribit-ETH-30APR23-2010-C-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"deribit-ETH-30APR23-2010-P-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"deribit-ETH-30MAY23-2020-C-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"deribit-ETH-31DEC21-4000-C-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2021-09-30T15:59:00.000000000Z"},{"market":"deribit-ETH-31MAY23-2020-P-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"deribit-XRP-31SEP23-2023-P-option","min_time":"2023-09-01T00:00:00.000000000Z","max_time":"2023-09-05T15:00:00.000000000Z"},{"market":"huobi-ETH-25MAR22-1202-S-option","min_time":"2021-09-01T13:24:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"huobi-ETH-30APR23-2010-C-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"huobi-ETH-30APR23-2010-P-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"huobi-ETH-30MAY23-2020-C-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"huobi-ETH-31MAY23-2020-P-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"huobi-XRP-31AUG23-2023-P-option","min_time":"2023-08-02T00:00:00.000000000Z","max_time":"2023-08-04T11:00:00.000000000Z"}]}""",
            "/v4/catalog/market-contract-prices?api_key=$TEST_API_KEY&type=option",
        )
    }

    @Test
    fun `should return market contract prices filtered by type=future`() {
        assertResponse(
            expectedCode = 200,
            expectedResponse = """{"data":[{"market":"deribit-BTC-10NOV23-future","min_time":"2023-11-09T14:03:00.000000000Z","max_time":"2023-11-09T14:03:00.000000000Z"},{"market":"deribit-BTC-17NOV23-future","min_time":"2023-11-09T14:01:00.000000000Z","max_time":"2023-11-09T14:02:00.000000000Z"},{"market":"deribit-BTC-24NOV23-future","min_time":"2023-09-29T16:19:00.000000000Z","max_time":"2023-09-29T16:19:00.000000000Z"},{"market":"deribit-ETH-28JAN24-future","min_time":"2024-01-25T15:15:00.000000000Z","max_time":"2024-01-25T15:15:00.000000000Z"},{"market":"deribit-XRP-31JAN24-future","min_time":"2024-01-30T13:00:00.000000000Z","max_time":"2024-01-30T13:00:00.000000000Z"}]}""",
            pathAndQuery = "/v4/catalog/market-contract-prices?api_key=$TEST_API_KEY&type=future",
        )
    }

    @Test
    fun `should return 400 when type is invalid`() {
        assertResponseWithContentType(
            400,
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'type'. Value 'non-supported' is not supported. Supported values are 'spot', 'future', 'option'."}}""",
            "/v4/catalog/market-contract-prices?api_key=$TEST_API_KEY&type=non-supported",
        )
    }

    @Test
    fun `should return 400 when both markets and type specified`() {
        assertResponseWithContentType(
            400,
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'markets'. Cannot combine filtering by 'markets' with other filtering options."}}""",
            "/v4/catalog/market-contract-prices?api_key=$TEST_API_KEY&markets=bittrex-btc-usd-spot&type=option",
        )
    }

    @Test
    fun `should return 400 when asset specified and type is future`() {
        assertResponseWithContentType(
            400,
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'type'. 'type' should be 'spot' when filtering by 'asset'."}}""",
            "/v4/catalog/market-contract-prices?api_key=$TEST_API_KEY&asset=btc&type=option",
        )
    }

    @Test
    fun `should return market contract prices when filtered by symbol`() {
        assertResponseWithContentType(
            200,
            """{"data":[{"market":"deribit-BTC-15OCT21-60000-C-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2021-09-30T15:59:00.000000000Z"}]}""",
            "/v4/catalog/market-contract-prices?api_key=$TEST_API_KEY&symbol=BTC-15OCT21-60000-C",
        )
    }

    @Test
    fun `should return market contract prices filtered by eth base and usd quote`() {
        assertResponse(
            expectedCode = 200,
            expectedResponse = """{"data":[{"market":"bybit-ETH-30APR23-2010-C-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"bybit-ETH-30APR23-2010-P-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"bybit-ETH-31MAY23-2020-P-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"deribit-ETH-1OCT21-2850-P-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2023-01-14T08:05:00.000000000Z"},{"market":"deribit-ETH-1OCT21-3200-P-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2021-09-30T15:59:00.000000000Z"},{"market":"deribit-ETH-24JUN22-1000-C-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2021-09-30T15:59:00.000000000Z"},{"market":"deribit-ETH-28JAN24-future","min_time":"2024-01-25T15:15:00.000000000Z","max_time":"2024-01-25T15:15:00.000000000Z"},{"market":"deribit-ETH-29OCT21-2000-P-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2021-09-30T15:59:00.000000000Z"},{"market":"deribit-ETH-2OCT21-3250-P-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2021-09-30T15:59:00.000000000Z"},{"market":"deribit-ETH-30APR23-2010-C-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"deribit-ETH-30APR23-2010-P-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"deribit-ETH-31DEC21-4000-C-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2021-09-30T15:59:00.000000000Z"},{"market":"deribit-ETH-31MAY23-2020-P-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"huobi-ETH-30APR23-2010-C-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"huobi-ETH-30APR23-2010-P-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"huobi-ETH-31MAY23-2020-P-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"}]}""",
            pathAndQuery = "/v4/catalog/market-contract-prices?api_key=$TEST_API_KEY&base=eth&quote=usd",
        )
    }

    @Test
    fun `should return 400 filtering market contract prices by unknown base`() {
        assertResponse(
            400,
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'base'. Value 'wth' is not supported."}}""",
            "/v4/catalog/market-contract-prices?api_key=$TEST_API_KEY&base=wth&quote=eth",
        )
    }

    @Test
    fun `should return empty list of market contract prices filtered by unsupported base`() {
        assertResponse(
            200,
            EMPTY_RESPONSE,
            "/v4/catalog/market-contract-prices?api_key=$TEST_API_KEY&base=swth&quote=eth",
        )
    }

    @Test
    fun `should return market contract prices in json_stream format`() {
        assertResponseWithContentType(
            expectedCode = 200,
            expectedResponse = DEFAULT_NDJSON_RESPONSE.trimIndent(),
            pathAndQuery = "/v4/catalog/market-contract-prices?api_key=$TEST_API_KEY&format=json_stream",
            contentType = "application/x-ndjson",
        )
    }

    @Test
    fun `should return market contract prices in json_stream format ignoring 'pretty' parameter`() {
        assertResponseWithContentType(
            expectedCode = 200,
            expectedResponse = DEFAULT_NDJSON_RESPONSE.trimIndent(),
            pathAndQuery = "/v4/catalog/market-contract-prices?api_key=$TEST_API_KEY&format=json_stream&pretty=true",
            contentType = "application/x-ndjson",
        )
    }

    @Test
    fun `should return 400 when format is invalid`() {
        assertResponseWithContentType(
            400,
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'format'. Must be one of the following: json, json_stream."}}""",
            "/v4/catalog/market-contract-prices?api_key=$TEST_API_KEY&format=ldjson",
        )
    }

    @ParameterizedTest(name = "should return 400 when limit is {0}")
    @ValueSource(strings = ["-10", "invalid"])
    fun `should return 400 when limit is invalid`(limit: String) {
        assertResponse(
            400,
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'limit'. Must be positive number or `none`."}}""",
            "/v4/catalog/market-contract-prices?api_key=$TEST_API_KEY&limit=$limit",
        )
    }

    @Test
    fun `should return 400 when both asset and quote specified`() {
        val expectedResponse =
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'asset'. Cannot combine filtering by 'asset' with other filtering options."}}"""
        assertResponse(
            400,
            expectedResponse,
            "/v4/catalog/market-contract-prices?api_key=$TEST_API_KEY&asset=btc&quote=usd",
        )
    }

    @Test
    fun `should return 400 when both symbol and quote specified`() {
        val expectedResponse =
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'symbol'. Cannot combine filtering by 'symbol' with other filtering options."}}"""
        assertResponse(
            400,
            expectedResponse,
            "/v4/catalog/market-contract-prices?api_key=$TEST_API_KEY&symbol=BTC-15OCT21-60000-C&quote=usd",
        )
    }

    @Test
    fun `should return 400 when symbol specified and type is spot`() {
        val expectedResponse =
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'type'. 'type' should not be 'spot' when filtering by 'symbol'."}}"""
        assertResponse(
            400,
            expectedResponse,
            "/v4/catalog/market-contract-prices?api_key=$TEST_API_KEY&symbol=btc&type=spot",
        )
    }

    @Test
    fun `should return all option market contract prices by eth base`() {
        assertResponse(
            200,
            """{"data":[{"market":"bybit-ETH-30APR23-2010-C-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"bybit-ETH-30APR23-2010-P-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"bybit-ETH-31MAY23-2020-P-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"deribit-ETH-1OCT21-2850-P-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2023-01-14T08:05:00.000000000Z"},{"market":"deribit-ETH-1OCT21-3200-P-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2021-09-30T15:59:00.000000000Z"},{"market":"deribit-ETH-24JUN22-1000-C-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2021-09-30T15:59:00.000000000Z"},{"market":"deribit-ETH-29OCT21-2000-P-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2021-09-30T15:59:00.000000000Z"},{"market":"deribit-ETH-2OCT21-3250-P-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2021-09-30T15:59:00.000000000Z"},{"market":"deribit-ETH-30APR23-2010-C-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"deribit-ETH-30APR23-2010-P-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"deribit-ETH-31DEC21-4000-C-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2021-09-30T15:59:00.000000000Z"},{"market":"deribit-ETH-31MAY23-2020-P-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"huobi-ETH-30APR23-2010-C-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"huobi-ETH-30APR23-2010-P-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"huobi-ETH-31MAY23-2020-P-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"}]}""",
            "/v4/catalog/market-contract-prices?api_key=$TEST_API_KEY&base=eth&type=option",
        )
    }

    @Test
    fun `should return the first page of market contract prices`() {
        val expectedResponse = """{"data":[{"market":"bittrex-XRP-10AUG23-8000-C-option","min_time":"2023-08-09T00:00:00.000000000Z","max_time":"2023-08-10T00:00:00.000000000Z"},{"market":"bybit-ETH-25MAR22-1202-S-option","min_time":"2021-09-01T13:24:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"bybit-ETH-30APR23-2010-C-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"bybit-ETH-30APR23-2010-P-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"bybit-ETH-30MAY23-2020-C-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"bybit-ETH-31MAY23-2020-P-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"deribit-BTC-10NOV23-future","min_time":"2023-11-09T14:03:00.000000000Z","max_time":"2023-11-09T14:03:00.000000000Z"},{"market":"deribit-BTC-15OCT21-60000-C-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2021-09-30T15:59:00.000000000Z"},{"market":"deribit-BTC-17NOV23-future","min_time":"2023-11-09T14:01:00.000000000Z","max_time":"2023-11-09T14:02:00.000000000Z"},{"market":"deribit-BTC-1OCT21-75000-C-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2021-09-30T15:59:00.000000000Z"}],"next_page_token":"ZGVyaWJpdC1CVEMtMjROT1YyMy1mdXR1cmU","next_page_url":"http://127.0.0.1:8080/v4/catalog-v2/market-contract-prices?api_key=x1&page_size=10&next_page_token=ZGVyaWJpdC1CVEMtMjROT1YyMy1mdXR1cmU"}"""
        assertResponse(
            expectedCode = 200,
            expectedResponse = expectedResponse,
            pathAndQuery = "/v4/catalog-v2/market-contract-prices?api_key=$TEST_API_KEY&page_size=10",
        )
    }

    @Test
    fun `should return the second page of market contract prices`() {
        val expectedResponse =
            """{"data":[{"market":"deribit-BTC-24NOV23-future","min_time":"2023-09-29T16:19:00.000000000Z","max_time":"2023-09-29T16:19:00.000000000Z"},{"market":"deribit-BTC-26NOV21-60000-C-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2021-09-30T15:59:00.000000000Z"},{"market":"deribit-BTC-8OCT21-50000-C-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2021-09-30T15:59:00.000000000Z"},{"market":"deribit-ETH-1OCT21-2850-P-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2023-01-14T08:05:00.000000000Z"},{"market":"deribit-ETH-1OCT21-3200-P-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2021-09-30T15:59:00.000000000Z"},{"market":"deribit-ETH-24JUN22-1000-C-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2021-09-30T15:59:00.000000000Z"},{"market":"deribit-ETH-25MAR22-1202-S-option","min_time":"2021-09-01T13:24:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"deribit-ETH-28JAN24-future","min_time":"2024-01-25T15:15:00.000000000Z","max_time":"2024-01-25T15:15:00.000000000Z"},{"market":"deribit-ETH-29OCT21-2000-P-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2021-09-30T15:59:00.000000000Z"},{"market":"deribit-ETH-2OCT21-3250-P-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2021-09-30T15:59:00.000000000Z"}],"next_page_token":"ZGVyaWJpdC1FVEgtMzBBUFIyMy0yMDEwLUMtb3B0aW9u","next_page_url":"http://127.0.0.1:8080/v4/catalog-v2/market-contract-prices?api_key=x1&page_size=10&next_page_token=ZGVyaWJpdC1FVEgtMzBBUFIyMy0yMDEwLUMtb3B0aW9u"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/catalog-v2/market-contract-prices?api_key=$TEST_API_KEY&page_size=10&next_page_token=ZGVyaWJpdC1CVEMtMjROT1YyMy1mdXR1cmU",
        )
    }

    @Test
    fun `should return the third page of market contract prices`() {
        val expectedResponse = """{"data":[{"market":"deribit-ETH-30APR23-2010-C-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"deribit-ETH-30APR23-2010-P-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"deribit-ETH-30MAY23-2020-C-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"deribit-ETH-31DEC21-4000-C-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2021-09-30T15:59:00.000000000Z"},{"market":"deribit-ETH-31MAY23-2020-P-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"deribit-XRP-31JAN24-future","min_time":"2024-01-30T13:00:00.000000000Z","max_time":"2024-01-30T13:00:00.000000000Z"},{"market":"deribit-XRP-31SEP23-2023-P-option","min_time":"2023-09-01T00:00:00.000000000Z","max_time":"2023-09-05T15:00:00.000000000Z"},{"market":"huobi-ETH-25MAR22-1202-S-option","min_time":"2021-09-01T13:24:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"huobi-ETH-30APR23-2010-C-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"huobi-ETH-30APR23-2010-P-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"}],"next_page_token":"aHVvYmktRVRILTMwTUFZMjMtMjAyMC1DLW9wdGlvbg","next_page_url":"http://127.0.0.1:8080/v4/catalog-v2/market-contract-prices?api_key=x1&page_size=10&next_page_token=aHVvYmktRVRILTMwTUFZMjMtMjAyMC1DLW9wdGlvbg"}"""
        assertResponse(
            expectedCode = 200,
            expectedResponse = expectedResponse,
            pathAndQuery = "/v4/catalog-v2/market-contract-prices?api_key=$TEST_API_KEY&page_size=10&next_page_token=ZGVyaWJpdC1FVEgtMzBBUFIyMy0yMDEwLUMtb3B0aW9u",
        )
    }

    @Test
    fun `should return the last page of market contract prices`() {
        val expectedResponse = """{"data":[{"market":"huobi-ETH-30MAY23-2020-C-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"huobi-ETH-31MAY23-2020-P-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"huobi-XRP-31AUG23-2023-P-option","min_time":"2023-08-02T00:00:00.000000000Z","max_time":"2023-08-04T11:00:00.000000000Z"}]}"""
        assertResponse(
            expectedCode = 200,
            expectedResponse = expectedResponse,
            pathAndQuery = "/v4/catalog-v2/market-contract-prices?api_key=$TEST_API_KEY&page_size=10&next_page_token=aHVvYmktRVRILTMwTUFZMjMtMjAyMC1DLW9wdGlvbg",
        )
    }

    @Test
    fun `should return the first page of market contract prices paging from end`() {
        val expectedResponse = """{"data":[{"market":"deribit-ETH-1OCT21-2850-P-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2023-01-14T08:05:00.000000000Z"},{"market":"deribit-ETH-1OCT21-3200-P-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2021-09-30T15:59:00.000000000Z"},{"market":"deribit-ETH-24JUN22-1000-C-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2021-09-30T15:59:00.000000000Z"},{"market":"deribit-ETH-25MAR22-1202-S-option","min_time":"2021-09-01T13:24:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"deribit-ETH-28JAN24-future","min_time":"2024-01-25T15:15:00.000000000Z","max_time":"2024-01-25T15:15:00.000000000Z"},{"market":"deribit-ETH-29OCT21-2000-P-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2021-09-30T15:59:00.000000000Z"},{"market":"deribit-ETH-2OCT21-3250-P-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2021-09-30T15:59:00.000000000Z"},{"market":"deribit-ETH-30APR23-2010-C-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"deribit-ETH-30APR23-2010-P-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"deribit-ETH-30MAY23-2020-C-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"deribit-ETH-31DEC21-4000-C-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2021-09-30T15:59:00.000000000Z"},{"market":"deribit-ETH-31MAY23-2020-P-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"deribit-XRP-31JAN24-future","min_time":"2024-01-30T13:00:00.000000000Z","max_time":"2024-01-30T13:00:00.000000000Z"},{"market":"deribit-XRP-31SEP23-2023-P-option","min_time":"2023-09-01T00:00:00.000000000Z","max_time":"2023-09-05T15:00:00.000000000Z"},{"market":"huobi-ETH-25MAR22-1202-S-option","min_time":"2021-09-01T13:24:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"huobi-ETH-30APR23-2010-C-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"huobi-ETH-30APR23-2010-P-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"huobi-ETH-30MAY23-2020-C-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"huobi-ETH-31MAY23-2020-P-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"huobi-XRP-31AUG23-2023-P-option","min_time":"2023-08-02T00:00:00.000000000Z","max_time":"2023-08-04T11:00:00.000000000Z"}],"next_page_token":"ZGVyaWJpdC1CVEMtOE9DVDIxLTUwMDAwLUMtb3B0aW9u","next_page_url":"http://127.0.0.1:8080/v4/catalog-v2/market-contract-prices?api_key=x1&page_size=20&paging_from=end&next_page_token=ZGVyaWJpdC1CVEMtOE9DVDIxLTUwMDAwLUMtb3B0aW9u"}"""
        assertResponse(
            expectedCode = 200,
            expectedResponse = expectedResponse,
            pathAndQuery = "/v4/catalog-v2/market-contract-prices?api_key=$TEST_API_KEY&page_size=20&paging_from=end",
        )
    }

    @Test
    fun `should return the last page of market contract prices paging from end`() {
        val expectedResponse = """{"data":[{"market":"bittrex-XRP-10AUG23-8000-C-option","min_time":"2023-08-09T00:00:00.000000000Z","max_time":"2023-08-10T00:00:00.000000000Z"},{"market":"bybit-ETH-25MAR22-1202-S-option","min_time":"2021-09-01T13:24:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"bybit-ETH-30APR23-2010-C-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"bybit-ETH-30APR23-2010-P-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"bybit-ETH-30MAY23-2020-C-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"bybit-ETH-31MAY23-2020-P-option","min_time":"2023-05-02T13:26:00.000000000Z","max_time":"2023-05-02T13:26:00.000000000Z"},{"market":"deribit-BTC-10NOV23-future","min_time":"2023-11-09T14:03:00.000000000Z","max_time":"2023-11-09T14:03:00.000000000Z"},{"market":"deribit-BTC-15OCT21-60000-C-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2021-09-30T15:59:00.000000000Z"},{"market":"deribit-BTC-17NOV23-future","min_time":"2023-11-09T14:01:00.000000000Z","max_time":"2023-11-09T14:02:00.000000000Z"},{"market":"deribit-BTC-1OCT21-75000-C-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2021-09-30T15:59:00.000000000Z"},{"market":"deribit-BTC-24NOV23-future","min_time":"2023-09-29T16:19:00.000000000Z","max_time":"2023-09-29T16:19:00.000000000Z"},{"market":"deribit-BTC-26NOV21-60000-C-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2021-09-30T15:59:00.000000000Z"},{"market":"deribit-BTC-8OCT21-50000-C-option","min_time":"2021-09-30T15:59:00.000000000Z","max_time":"2021-09-30T15:59:00.000000000Z"}]}"""
        assertResponse(
            expectedCode = 200,
            expectedResponse = expectedResponse,
            pathAndQuery = "/v4/catalog-v2/market-contract-prices?api_key=$TEST_API_KEY&page_size=20&paging_from=end&next_page_token=ZGVyaWJpdC1CVEMtOE9DVDIxLTUwMDAwLUMtb3B0aW9u",
        )
    }

    @Test
    fun `should return market contract prices in pretty format`() {
        val expectedResponse =
            """{
  "data" : [ {
  "market" : "bittrex-XRP-10AUG23-8000-C-option",
  "min_time" : "2023-08-09T00:00:00.000000000Z",
  "max_time" : "2023-08-10T00:00:00.000000000Z"
}, {
  "market" : "bybit-ETH-25MAR22-1202-S-option",
  "min_time" : "2021-09-01T13:24:00.000000000Z",
  "max_time" : "2023-05-02T13:26:00.000000000Z"
}, {
  "market" : "bybit-ETH-30APR23-2010-C-option",
  "min_time" : "2023-05-02T13:26:00.000000000Z",
  "max_time" : "2023-05-02T13:26:00.000000000Z"
}, {
  "market" : "bybit-ETH-30APR23-2010-P-option",
  "min_time" : "2023-05-02T13:26:00.000000000Z",
  "max_time" : "2023-05-02T13:26:00.000000000Z"
}, {
  "market" : "bybit-ETH-30MAY23-2020-C-option",
  "min_time" : "2023-05-02T13:26:00.000000000Z",
  "max_time" : "2023-05-02T13:26:00.000000000Z"
}, {
  "market" : "bybit-ETH-31MAY23-2020-P-option",
  "min_time" : "2023-05-02T13:26:00.000000000Z",
  "max_time" : "2023-05-02T13:26:00.000000000Z"
}, {
  "market" : "deribit-BTC-10NOV23-future",
  "min_time" : "2023-11-09T14:03:00.000000000Z",
  "max_time" : "2023-11-09T14:03:00.000000000Z"
}, {
  "market" : "deribit-BTC-15OCT21-60000-C-option",
  "min_time" : "2021-09-30T15:59:00.000000000Z",
  "max_time" : "2021-09-30T15:59:00.000000000Z"
}, {
  "market" : "deribit-BTC-17NOV23-future",
  "min_time" : "2023-11-09T14:01:00.000000000Z",
  "max_time" : "2023-11-09T14:02:00.000000000Z"
}, {
  "market" : "deribit-BTC-1OCT21-75000-C-option",
  "min_time" : "2021-09-30T15:59:00.000000000Z",
  "max_time" : "2021-09-30T15:59:00.000000000Z"
}, {
  "market" : "deribit-BTC-24NOV23-future",
  "min_time" : "2023-09-29T16:19:00.000000000Z",
  "max_time" : "2023-09-29T16:19:00.000000000Z"
}, {
  "market" : "deribit-BTC-26NOV21-60000-C-option",
  "min_time" : "2021-09-30T15:59:00.000000000Z",
  "max_time" : "2021-09-30T15:59:00.000000000Z"
}, {
  "market" : "deribit-BTC-8OCT21-50000-C-option",
  "min_time" : "2021-09-30T15:59:00.000000000Z",
  "max_time" : "2021-09-30T15:59:00.000000000Z"
}, {
  "market" : "deribit-ETH-1OCT21-2850-P-option",
  "min_time" : "2021-09-30T15:59:00.000000000Z",
  "max_time" : "2023-01-14T08:05:00.000000000Z"
}, {
  "market" : "deribit-ETH-1OCT21-3200-P-option",
  "min_time" : "2021-09-30T15:59:00.000000000Z",
  "max_time" : "2021-09-30T15:59:00.000000000Z"
}, {
  "market" : "deribit-ETH-24JUN22-1000-C-option",
  "min_time" : "2021-09-30T15:59:00.000000000Z",
  "max_time" : "2021-09-30T15:59:00.000000000Z"
}, {
  "market" : "deribit-ETH-25MAR22-1202-S-option",
  "min_time" : "2021-09-01T13:24:00.000000000Z",
  "max_time" : "2023-05-02T13:26:00.000000000Z"
}, {
  "market" : "deribit-ETH-28JAN24-future",
  "min_time" : "2024-01-25T15:15:00.000000000Z",
  "max_time" : "2024-01-25T15:15:00.000000000Z"
}, {
  "market" : "deribit-ETH-29OCT21-2000-P-option",
  "min_time" : "2021-09-30T15:59:00.000000000Z",
  "max_time" : "2021-09-30T15:59:00.000000000Z"
}, {
  "market" : "deribit-ETH-2OCT21-3250-P-option",
  "min_time" : "2021-09-30T15:59:00.000000000Z",
  "max_time" : "2021-09-30T15:59:00.000000000Z"
}, {
  "market" : "deribit-ETH-30APR23-2010-C-option",
  "min_time" : "2023-05-02T13:26:00.000000000Z",
  "max_time" : "2023-05-02T13:26:00.000000000Z"
}, {
  "market" : "deribit-ETH-30APR23-2010-P-option",
  "min_time" : "2023-05-02T13:26:00.000000000Z",
  "max_time" : "2023-05-02T13:26:00.000000000Z"
}, {
  "market" : "deribit-ETH-30MAY23-2020-C-option",
  "min_time" : "2023-05-02T13:26:00.000000000Z",
  "max_time" : "2023-05-02T13:26:00.000000000Z"
}, {
  "market" : "deribit-ETH-31DEC21-4000-C-option",
  "min_time" : "2021-09-30T15:59:00.000000000Z",
  "max_time" : "2021-09-30T15:59:00.000000000Z"
}, {
  "market" : "deribit-ETH-31MAY23-2020-P-option",
  "min_time" : "2023-05-02T13:26:00.000000000Z",
  "max_time" : "2023-05-02T13:26:00.000000000Z"
}, {
  "market" : "deribit-XRP-31JAN24-future",
  "min_time" : "2024-01-30T13:00:00.000000000Z",
  "max_time" : "2024-01-30T13:00:00.000000000Z"
}, {
  "market" : "deribit-XRP-31SEP23-2023-P-option",
  "min_time" : "2023-09-01T00:00:00.000000000Z",
  "max_time" : "2023-09-05T15:00:00.000000000Z"
}, {
  "market" : "huobi-ETH-25MAR22-1202-S-option",
  "min_time" : "2021-09-01T13:24:00.000000000Z",
  "max_time" : "2023-05-02T13:26:00.000000000Z"
}, {
  "market" : "huobi-ETH-30APR23-2010-C-option",
  "min_time" : "2023-05-02T13:26:00.000000000Z",
  "max_time" : "2023-05-02T13:26:00.000000000Z"
}, {
  "market" : "huobi-ETH-30APR23-2010-P-option",
  "min_time" : "2023-05-02T13:26:00.000000000Z",
  "max_time" : "2023-05-02T13:26:00.000000000Z"
}, {
  "market" : "huobi-ETH-30MAY23-2020-C-option",
  "min_time" : "2023-05-02T13:26:00.000000000Z",
  "max_time" : "2023-05-02T13:26:00.000000000Z"
}, {
  "market" : "huobi-ETH-31MAY23-2020-P-option",
  "min_time" : "2023-05-02T13:26:00.000000000Z",
  "max_time" : "2023-05-02T13:26:00.000000000Z"
}, {
  "market" : "huobi-XRP-31AUG23-2023-P-option",
  "min_time" : "2023-08-02T00:00:00.000000000Z",
  "max_time" : "2023-08-04T11:00:00.000000000Z"
} ]
}"""
        assertResponse(
            expectedCode = 200,
            expectedResponse = expectedResponse,
            pathAndQuery = "/v4/catalog/market-contract-prices?api_key=$TEST_API_KEY&pretty=true",
        )
    }
}
