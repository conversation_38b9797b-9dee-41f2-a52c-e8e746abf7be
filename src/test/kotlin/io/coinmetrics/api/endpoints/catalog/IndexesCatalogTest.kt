package io.coinmetrics.api.endpoints.catalog

import io.coinmetrics.api.helper.BaseTest
import io.coinmetrics.api.helper.COMMUNITY_INDEXES_KEY
import io.coinmetrics.api.helper.COMMUNITY_INDEXES_KEY_2
import io.coinmetrics.api.helper.TEST_API_KEY
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class IndexesCatalogTest : BaseTest() {
    @Test
    fun `test indexes discovery`() {
        val expectedResponse =
            """{"data":[{"index":"CMBI10M","full_name":"CMBI 10 Momentum Index","description":"A multi asset index measuring the performance of the momentum factor for CMBI10 Index.","frequencies":[{"frequency":"15s","min_time":"2020-05-19T16:00:00.000000000Z","max_time":"2020-05-20T16:00:00.000000000Z"},{"frequency":"1h","min_time":"2019-11-21T21:00:00.000000000Z","max_time":"2020-05-07T20:00:00.000000000Z"},{"frequency":"1d-ny-close","min_time":"2019-11-21T21:00:00.000000000Z","max_time":"2020-05-07T20:00:00.000000000Z"}],"type":"thematic"},{"index":"CMBIALGO","full_name":"CMBI Algorand Index","description":"A single asset index measuring the performance an investor would expect from purchasing and holding Algorand.","frequencies":[{"frequency":"1s","min_time":"2022-03-15T04:14:32.000000000Z","max_time":"2022-03-15T04:14:41.000000000Z"}],"type":"single_asset"},{"index":"CMBIBTC","full_name":"CMBI Bitcoin Index","description":"A single asset index measuring the performance an investor would expect from purchasing and holding Bitcoin.","frequencies":[{"frequency":"1s","min_time":"2022-03-15T04:14:32.000000000Z","max_time":"2023-08-22T13:02:41.000000000Z"},{"frequency":"15s","min_time":"2017-11-01T19:59:59.999900000Z","max_time":"2019-11-17T15:15:30.000000000Z"},{"frequency":"1h","min_time":"2019-11-17T13:00:00.000000000Z","max_time":"2023-08-22T18:00:00.000000000Z"},{"frequency":"1d-ny-close","min_time":"2019-11-17T21:00:00.000000000Z","max_time":"2019-11-17T21:00:00.000000000Z"}],"type":"single_asset"},{"index":"CMBIBTCT","full_name":"CMBI Bitcoin Total Return Index","description":"A single asset index measuring the performance an investor would expect from purchasing and holding Bitcoin as well as liquidating legitimate forked assets.","frequencies":[{"frequency":"1s","min_time":"2022-03-15T04:14:32.000000000Z","max_time":"2023-08-22T13:02:41.000000000Z"},{"frequency":"15s","min_time":"2017-11-01T19:59:59.999900000Z","max_time":"2019-11-17T15:15:30.000000000Z"},{"frequency":"1h","min_time":"2019-11-17T13:00:00.000000000Z","max_time":"2023-08-22T18:00:00.000000000Z"},{"frequency":"1d-ny-close","min_time":"2019-11-17T21:00:00.000000000Z","max_time":"2019-11-17T21:00:00.000000000Z"}],"type":"single_asset"},{"index":"CMBIETH","full_name":"CMBI Ethereum Index","description":"A single asset index measuring the performance an investor would expect from purchasing and holding Ethereum.","frequencies":[{"frequency":"15s","min_time":"2019-11-17T13:21:00.000000000Z","max_time":"2019-11-18T15:15:30.000000000Z"},{"frequency":"1h","min_time":"2019-11-17T13:00:00.000000000Z","max_time":"2020-05-07T20:00:00.000000000Z"},{"frequency":"1d","min_time":"2019-11-19T00:00:00.000000000Z","max_time":"2019-11-19T00:00:00.000000000Z"},{"frequency":"1d-ny-close","min_time":"2019-11-19T21:00:00.000000000Z","max_time":"2020-05-07T20:00:00.000000000Z"}],"type":"single_asset"},{"index":"CMBIETHT","full_name":"CMBI Ethereum Total Return Index","description":"A single asset index measuring the performance an investor would expect from purchasing and holding Ethereum as well as liquidating legitimate forked assets.","frequencies":[{"frequency":"15s","min_time":"2019-11-17T13:21:00.000000000Z","max_time":"2019-11-18T15:15:30.000000000Z"},{"frequency":"1h","min_time":"2019-11-17T13:00:00.000000000Z","max_time":"2020-05-07T20:00:00.000000000Z"},{"frequency":"1d","min_time":"2019-11-19T00:00:00.000000000Z","max_time":"2019-11-19T00:00:00.000000000Z"},{"frequency":"1d-ny-close","min_time":"2019-11-19T21:00:00.000000000Z","max_time":"2020-05-07T20:00:00.000000000Z"}],"type":"single_asset"},{"index":"CMBIHASH","full_name":"CMBI Bitcoin Hash Rate Index","description":"Measure of the amount of hash rate being contributed to mining Bitcoin blocks.","frequencies":[{"frequency":"15s","min_time":"2019-11-18T15:00:00.000000000Z","max_time":"2019-11-18T16:00:00.000000000Z"},{"frequency":"1h","min_time":"2019-11-18T15:00:00.000000000Z","max_time":"2019-11-18T16:00:00.000000000Z"}],"type":"thematic"},{"index":"CMBIWORK","full_name":"CMBI Bitcoin Observed Work Index","description":"Measure of the amount of mining activity being conducted on the Bitcoin Network throughout a 24 hour period.","frequencies":[{"frequency":"15s","min_time":"2019-11-18T17:00:00.000000000Z","max_time":"2020-05-20T16:00:00.000000000Z"},{"frequency":"1h","min_time":"2019-11-18T17:00:00.000000000Z","max_time":"2020-05-20T16:00:00.000000000Z"},{"frequency":"1d-ny-midday","min_time":"2019-11-18T17:00:00.000000000Z","max_time":"2020-05-20T16:00:00.000000000Z"}],"type":"thematic"},{"index":"FIDBEIP","full_name":"Fidelity Bitcoin and Ethereum Price Index","description":"Price Return is designed to reflect the performance of Bitcoin and Ethereum in U.S. dollars.","frequencies":[{"frequency":"15s","min_time":"2018-03-16T19:00:00.000000000Z","max_time":"2020-05-07T20:00:00.000000000Z"},{"frequency":"1d-ny-close","min_time":"2020-05-07T20:00:00.000000000Z","max_time":"2020-05-07T20:00:00.000000000Z"}],"type":"multi_asset"},{"index":"FIDBEIT","full_name":"Fidelity Bitcoin and Ethereum Total Return Index","description":"Total Return is designed to reflect the performance of Bitcoin and Ethereum, including the liquidation value of significant forks, in U.S. dollars.","frequencies":[{"frequency":"15s","min_time":"2018-03-16T19:00:00.000000000Z","max_time":"2020-05-07T20:00:00.000000000Z"},{"frequency":"1d-ny-close","min_time":"2020-05-07T20:00:00.000000000Z","max_time":"2020-05-07T20:00:00.000000000Z"}],"type":"multi_asset"},{"index":"FIDBTCP","full_name":"Fidelity Bitcoin Price Index","description":"Price Return is designed to reflect the performance of Bitcoin in U.S. dollars.","frequencies":[{"frequency":"15s","min_time":"2018-03-16T19:59:59.999990000Z","max_time":"2020-05-07T20:00:00.000000000Z"},{"frequency":"1d-ny-close","min_time":"2019-11-18T21:00:00.000000000Z","max_time":"2020-05-07T20:00:00.000000000Z"}],"type":"single_asset"},{"index":"FIDBTCT","full_name":"Fidelity Bitcoin Total Return Index","description":"Total Return is designed to reflect the performance of Bitcoin, including the liquidation value of significant forks, in U.S. dollars.","frequencies":[{"frequency":"15s","min_time":"2018-03-16T19:59:59.999990000Z","max_time":"2020-05-07T20:00:00.000000000Z"},{"frequency":"1d-ny-close","min_time":"2019-11-18T21:00:00.000000000Z","max_time":"2020-05-07T20:00:00.000000000Z"}],"type":"single_asset"},{"index":"FIDETHP","full_name":"Fidelity Ethereum Price Index","description":"Price Return is designed to reflect the performance of Ethereum in U.S. dollars.","frequencies":[{"frequency":"15s","min_time":"2018-03-16T19:59:59.999990000Z","max_time":"2020-05-07T20:00:00.000000000Z"},{"frequency":"1d-ny-close","min_time":"2019-11-18T21:00:00.000000000Z","max_time":"2020-05-07T20:00:00.000000000Z"}],"type":"single_asset"},{"index":"FIDETHT","full_name":"Fidelity Ethereum Total Return Index","description":"Total Return is designed to reflect the performance of Ethereum, including the liquidation value of significant forks, in U.S. dollars.","frequencies":[{"frequency":"15s","min_time":"2018-03-16T19:59:59.999990000Z","max_time":"2020-05-07T20:00:00.000000000Z"},{"frequency":"1d-ny-close","min_time":"2019-11-18T21:00:00.000000000Z","max_time":"2020-05-07T20:00:00.000000000Z"}],"type":"single_asset"},{"index":"RTREE","full_name":"RWM WisdomTree Crypto Index","description":"The RWM WisdomTree Crypto Index is designed to track the performance of a diversified basket of crypto assets representing the broad crypto asset market.","frequencies":[{"frequency":"15s","min_time":"2020-05-19T16:00:00.000000000Z","max_time":"2020-05-20T16:00:00.000000000Z"},{"frequency":"1h","min_time":"2020-05-19T16:00:00.000000000Z","max_time":"2020-05-20T16:00:00.000000000Z"}]},{"index":"RTREET","full_name":"RWM WisdomTree Crypto Total Return Index","description":"The RWM WisdomTree Crypto Total Return Index is designed to track the performance of a diversified basket of crypto assets representing the broad crypto asset market, including the liquidation value of significant forks.","frequencies":[{"frequency":"15s","min_time":"2020-05-19T16:00:00.000000000Z","max_time":"2020-05-20T16:00:00.000000000Z"},{"frequency":"1h","min_time":"2020-05-19T16:00:00.000000000Z","max_time":"2020-05-20T16:00:00.000000000Z"}]}]}"""
        assertResponse(200, expectedResponse, "/v4/catalog/indexes?api_key=$TEST_API_KEY")
    }

    @Test
    fun `test indexes info`() {
        val expectedResponse =
            """{"data":[{"index":"CMBIETH","full_name":"CMBI Ethereum Index","description":"A single asset index measuring the performance an investor would expect from purchasing and holding Ethereum.","frequencies":[{"frequency":"15s","min_time":"2019-11-17T13:21:00.000000000Z","max_time":"2019-11-18T15:15:30.000000000Z"},{"frequency":"1h","min_time":"2019-11-17T13:00:00.000000000Z","max_time":"2020-05-07T20:00:00.000000000Z"},{"frequency":"1d","min_time":"2019-11-19T00:00:00.000000000Z","max_time":"2019-11-19T00:00:00.000000000Z"},{"frequency":"1d-ny-close","min_time":"2019-11-19T21:00:00.000000000Z","max_time":"2020-05-07T20:00:00.000000000Z"}],"type":"single_asset"}]}"""
        assertResponse(200, expectedResponse, "/v4/catalog/indexes?api_key=$TEST_API_KEY&indexes=CMBIETH")
    }

    @Test
    fun `should return 403 when index data is not available for provided api key`() {
        getResponse("/v4/catalog/indexes?api_key=$COMMUNITY_INDEXES_KEY&indexes=CMBIAUE").assertResponse()
    }

    @Test
    fun `test multi indexes info`() {
        val expectedResponse =
            """{"data":[{"index":"FIDBTCP","full_name":"Fidelity Bitcoin Price Index","description":"Price Return is designed to reflect the performance of Bitcoin in U.S. dollars.","frequencies":[{"frequency":"15s","min_time":"2018-03-16T19:59:59.999990000Z","max_time":"2020-05-07T20:00:00.000000000Z"},{"frequency":"1d-ny-close","min_time":"2019-11-18T21:00:00.000000000Z","max_time":"2020-05-07T20:00:00.000000000Z"}],"type":"single_asset"}]}"""
        assertResponse(200, expectedResponse, "/v4/catalog/indexes?api_key=$TEST_API_KEY&indexes=CMBI10,FIDBTCP")
        assertResponse(200, expectedResponse, "/v4/catalog/indexes?api_key=$TEST_API_KEY&indexes=cmbi10,FIDBTCP")
    }

    @Test
    fun `test FIDBEIP indexes info`() {
        val expectedResponse =
            """{"data":[{"index":"FIDBEIP","full_name":"Fidelity Bitcoin and Ethereum Price Index","description":"Price Return is designed to reflect the performance of Bitcoin and Ethereum in U.S. dollars.","frequencies":[{"frequency":"15s","min_time":"2018-03-16T19:00:00.000000000Z","max_time":"2020-05-07T20:00:00.000000000Z"},{"frequency":"1d-ny-close","min_time":"2020-05-07T20:00:00.000000000Z","max_time":"2020-05-07T20:00:00.000000000Z"}],"type":"multi_asset"}]}"""
        assertResponse(200, expectedResponse, "/v4/catalog/indexes?api_key=$TEST_API_KEY&indexes=FIDBEIP")
    }

    @Test
    fun `should return 1s frequency for CMBIBTCT index`() {
        val expectedResponse =
            """{"data":[{"index":"CMBIBTCT","full_name":"CMBI Bitcoin Total Return Index","description":"A single asset index measuring the performance an investor would expect from purchasing and holding Bitcoin as well as liquidating legitimate forked assets.","frequencies":[{"frequency":"1s","min_time":"2022-03-15T04:14:32.000000000Z","max_time":"2023-08-22T13:02:41.000000000Z"},{"frequency":"15s","min_time":"2017-11-01T19:59:59.999900000Z","max_time":"2019-11-17T15:15:30.000000000Z"},{"frequency":"1h","min_time":"2019-11-17T13:00:00.000000000Z","max_time":"2023-08-22T18:00:00.000000000Z"},{"frequency":"1d-ny-close","min_time":"2019-11-17T21:00:00.000000000Z","max_time":"2019-11-17T21:00:00.000000000Z"}],"type":"single_asset"}]}"""
        assertResponse(200, expectedResponse, "/v4/catalog/indexes?api_key=$TEST_API_KEY&indexes=CMBIBTCT")
    }

    @Test
    fun `should return CMBIBTCT index with restricted time range for community key`() {
        val expectedResponse =
            """{"data":[{"index":"CMBIBTCT","full_name":"CMBI Bitcoin Total Return Index","description":"A single asset index measuring the performance an investor would expect from purchasing and holding Bitcoin as well as liquidating legitimate forked assets.","frequencies":[{"frequency":"1s","min_time":"2023-08-22T12:02:41.000000000Z","max_time":"2023-08-22T13:02:41.000000000Z"},{"frequency":"15s","min_time":"2019-11-17T14:15:30.000000000Z","max_time":"2019-11-17T15:15:30.000000000Z"},{"frequency":"1h","min_time":"2023-08-21T18:00:00.000000000Z","max_time":"2023-08-22T18:00:00.000000000Z"},{"frequency":"1d-ny-close","min_time":"2019-10-18T21:00:00.000000000Z","max_time":"2019-11-17T21:00:00.000000000Z"}],"type":"single_asset"}]}"""
        assertResponse(200, expectedResponse, "/v4/catalog/indexes?api_key=$COMMUNITY_INDEXES_KEY&indexes=CMBIBTCT")
    }

    @Test
    fun `should return CMBIBTCT index with no time restrictions in catalog-all for community key`() {
        val expectedResponse =
            """{"data":[{"index":"CMBIBTCT","full_name":"CMBI Bitcoin Total Return Index","description":"A single asset index measuring the performance an investor would expect from purchasing and holding Bitcoin as well as liquidating legitimate forked assets.","frequencies":[{"frequency":"1s","min_time":"2022-03-15T04:14:32.000000000Z","max_time":"2023-08-22T13:02:41.000000000Z"},{"frequency":"15s","min_time":"2017-11-01T19:59:59.999900000Z","max_time":"2019-11-17T15:15:30.000000000Z"},{"frequency":"1h","min_time":"2019-11-17T13:00:00.000000000Z","max_time":"2023-08-22T18:00:00.000000000Z"},{"frequency":"1d-ny-close","min_time":"2019-11-17T21:00:00.000000000Z","max_time":"2019-11-17T21:00:00.000000000Z"}],"type":"single_asset"}]}"""
        assertResponse(200, expectedResponse, "/v4/catalog-all/indexes?api_key=$COMMUNITY_INDEXES_KEY&indexes=CMBIBTCT")
    }

    @Test
    fun `should return CMBIBTC index with no time restrictions as this key has custom widening permissions`() {
        val expectedResponse =
            """{"data":[{"index":"CMBIBTC","full_name":"CMBI Bitcoin Index","description":"A single asset index measuring the performance an investor would expect from purchasing and holding Bitcoin.","frequencies":[{"frequency":"1s","min_time":"2022-03-15T04:14:32.000000000Z","max_time":"2023-08-22T13:02:41.000000000Z"},{"frequency":"15s","min_time":"2017-11-01T19:59:59.999900000Z","max_time":"2019-11-17T15:15:30.000000000Z"},{"frequency":"1h","min_time":"2019-11-17T13:00:00.000000000Z","max_time":"2023-08-22T18:00:00.000000000Z"},{"frequency":"1d-ny-close","min_time":"2019-11-17T21:00:00.000000000Z","max_time":"2019-11-17T21:00:00.000000000Z"}],"type":"single_asset"}]}"""
        assertResponse(200, expectedResponse, "/v4/catalog/indexes?api_key=$COMMUNITY_INDEXES_KEY_2&indexes=CMBIBTC")
    }

    @Test
    fun `should return CMBIBTCT index WITH restricted time range for the key that has custom widening permissions but NOT for the requested index`() {
        val expectedResponse =
            """{"data":[{"index":"CMBIBTCT","full_name":"CMBI Bitcoin Total Return Index","description":"A single asset index measuring the performance an investor would expect from purchasing and holding Bitcoin as well as liquidating legitimate forked assets.","frequencies":[{"frequency":"1s","min_time":"2023-08-22T12:02:41.000000000Z","max_time":"2023-08-22T13:02:41.000000000Z"},{"frequency":"15s","min_time":"2019-11-17T14:15:30.000000000Z","max_time":"2019-11-17T15:15:30.000000000Z"},{"frequency":"1h","min_time":"2023-08-21T18:00:00.000000000Z","max_time":"2023-08-22T18:00:00.000000000Z"},{"frequency":"1d-ny-close","min_time":"2019-10-18T21:00:00.000000000Z","max_time":"2019-11-17T21:00:00.000000000Z"}],"type":"single_asset"}]}"""
        assertResponse(200, expectedResponse, "/v4/catalog/indexes?api_key=$COMMUNITY_INDEXES_KEY_2&indexes=CMBIBTCT")
    }

    @Test
    fun `test multi indexes info FIDETH`() {
        val expectedResponse =
            """{"data":[{"index":"FIDETHP","full_name":"Fidelity Ethereum Price Index","description":"Price Return is designed to reflect the performance of Ethereum in U.S. dollars.","frequencies":[{"frequency":"15s","min_time":"2018-03-16T19:59:59.999990000Z","max_time":"2020-05-07T20:00:00.000000000Z"},{"frequency":"1d-ny-close","min_time":"2019-11-18T21:00:00.000000000Z","max_time":"2020-05-07T20:00:00.000000000Z"}],"type":"single_asset"},{"index":"FIDETHT","full_name":"Fidelity Ethereum Total Return Index","description":"Total Return is designed to reflect the performance of Ethereum, including the liquidation value of significant forks, in U.S. dollars.","frequencies":[{"frequency":"15s","min_time":"2018-03-16T19:59:59.999990000Z","max_time":"2020-05-07T20:00:00.000000000Z"},{"frequency":"1d-ny-close","min_time":"2019-11-18T21:00:00.000000000Z","max_time":"2020-05-07T20:00:00.000000000Z"}],"type":"single_asset"}]}"""
        assertResponse(200, expectedResponse, "/v4/catalog/indexes?api_key=iUOMzzMKwiDnAkFjuStJ&indexes=FIDETHT,FIDETHP")
        assertResponse(200, expectedResponse, "/v4/catalog/indexes?api_key=iUOMzzMKwiDnAkFjuStJ&indexes=fidetht,FIDETHP")
    }

    @Test
    fun `test invalid indexes info`() {
        val expectedResponse =
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'indexes'. Value 'CMBIETH2' is not supported."}}"""
        assertResponse(400, expectedResponse, "/v4/catalog/indexes?api_key=$TEST_API_KEY&indexes=CMBIETH2")
        assertResponse(400, expectedResponse, "/v4/catalog/indexes?api_key=$TEST_API_KEY&indexes=cmbieth2")
    }

    @Test
    fun `should return 403 for hidden index in AMS API`() {
        getResponse("/v4/catalog/indexes?api_key=$TEST_API_KEY&indexes=CMBIBTCV").assertResponse()
    }

    @Test
    fun `should return 400 for unknown and hidden indexes in AMS API`() {
        val expectedResponse = """{"error":{"type":"bad_parameter","message":"Bad parameter 'indexes'. Value 'UNKNOWNIDX' is not supported."}}"""
        assertResponse(400, expectedResponse, "/v4/catalog/indexes?api_key=$TEST_API_KEY&indexes=UNKNOWNIDX,CMBIBTCV")
    }

    @Test
    fun `should return 400 for hidden and unknown indexes in AMS API`() {
        val expectedResponse = """{"error":{"type":"bad_parameter","message":"Bad parameter 'indexes'. Value 'UNKNOWNIDX' is not supported."}}"""
        assertResponse(400, expectedResponse, "/v4/catalog/indexes?api_key=$TEST_API_KEY&indexes=CMBIBTCV,UNKNOWNIDX")
    }

    @Test
    fun `should return 403 when one of the indexes is hidden in AMS API`() {
        getResponse("/v4/catalog/indexes?api_key=$TEST_API_KEY&indexes=CMBI10,FIDBTCP,CMBIBTCV").assertResponse("1")
        getResponse("/v4/catalog/indexes?api_key=$TEST_API_KEY&indexes=cmbi10,FIDBTCP,CMBIBTCV").assertResponse("2")
    }

    @Test
    fun `catalog-all should return 403 for hidden index in AMS API`() {
        getResponse("/v4/catalog-all/indexes?api_key=$TEST_API_KEY&indexes=CMBIBTCV").assertResponse()
    }

    @Test
    fun `catalog-all should return 400 for unknown and hidden indexes in AMS API`() {
        val expectedResponse = """{"error":{"type":"bad_parameter","message":"Bad parameter 'indexes'. Value 'UNKNOWNIDX' is not supported."}}"""
        assertResponse(400, expectedResponse, "/v4/catalog-all/indexes?api_key=$TEST_API_KEY&indexes=UNKNOWNIDX,CMBIBTCV")
    }

    @Test
    fun `catalog-all should return 400 for hidden and unknown indexes in AMS API`() {
        val expectedResponse = """{"error":{"type":"bad_parameter","message":"Bad parameter 'indexes'. Value 'UNKNOWNIDX' is not supported."}}"""
        assertResponse(400, expectedResponse, "/v4/catalog-all/indexes?api_key=$TEST_API_KEY&indexes=CMBIBTCV,UNKNOWNIDX")
    }

    @Test
    fun `catalog-all should return 403 when one of the indexes is hidden in AMS API`() {
        getResponse("/v4/catalog-all/indexes?api_key=$TEST_API_KEY&indexes=CMBI10,FIDBTCP,CMBIBTC,CMBIBTCV").assertResponse("1")
        getResponse("/v4/catalog-all/indexes?api_key=$TEST_API_KEY&indexes=cmbi10,FIDBTCP,CMBIBTC,CMBIBTCV").assertResponse("2")
    }
}
