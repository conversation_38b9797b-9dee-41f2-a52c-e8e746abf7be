package io.coinmetrics.api.endpoints.catalog.farum

import io.coinmetrics.api.helper.BaseTest
import io.coinmetrics.api.helper.TEST_API_KEY
import io.coinmetrics.api.helper.TEST_API_KEY_2
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class MiningPoolTipsSummaryCatalogTest : BaseTest() {
    @Test
    fun `should return catalog`() {
        val expectedResponse =
            """{"data":[{"asset":"btc","min_time":"2021-03-29T08:00:00.000000000Z","max_time":"2021-03-29T08:01:00.000000000Z"}]}"""
        assertResponseWithContentType(
            200,
            expectedResponse,
            "/v4/catalog/mining-pool-tips-summary?api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `should not return catalog with no permissions key`() {
        val expectedResponse =
            """{"error":{"type":"forbidden","message":"Requested resource is not available with supplied credentials."}}"""
        assertResponseWithContentType(
            403,
            expectedResponse,
            "/v4/catalog/mining-pool-tips-summary?api_key=$TEST_API_KEY_2",
        )
    }

    @Test
    fun `should return catalog-all`() {
        val expectedResponse =
            """{"data":[{"asset":"btc","min_time":"2021-03-29T08:00:00.000000000Z","max_time":"2021-03-29T08:01:00.000000000Z"}]}"""
        assertResponseWithContentType(
            200,
            expectedResponse,
            "/v4/catalog-all/mining-pool-tips-summary?api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `should return catalog-all with no permissions key`() {
        val expectedResponse =
            """{"data":[{"asset":"btc","min_time":"2021-03-29T08:00:00.000000000Z","max_time":"2021-03-29T08:01:00.000000000Z"}]}"""
        assertResponseWithContentType(
            200,
            expectedResponse,
            "/v4/catalog-all/mining-pool-tips-summary?api_key=$TEST_API_KEY_2",
        )
    }

    @Test
    fun `should return btc only catalog`() {
        val expectedResponse =
            """{"data":[{"asset":"btc","min_time":"2021-03-29T08:00:00.000000000Z","max_time":"2021-03-29T08:01:00.000000000Z"}]}"""
        assertResponseWithContentType(
            200,
            expectedResponse,
            "/v4/catalog/mining-pool-tips-summary?assets=btc&api_key=$TEST_API_KEY",
        )
    }
}
