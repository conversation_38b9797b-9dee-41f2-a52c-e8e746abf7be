package io.coinmetrics.api.endpoints.catalog.pair

import io.coinmetrics.api.helper.BaseTest
import io.coinmetrics.api.helper.COMMUNITY_KEY
import io.coinmetrics.api.helper.EMPTY_RESPONSE
import io.coinmetrics.api.helper.TEST_API_KEY
import io.coinmetrics.api.helper.TEST_API_KEY_2
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class PairCandlesCatalogTest : BaseTest() {
    @Test
    fun `should return pair candles statistics`() {
        val expectedResponse =
            """{"data":[{"pair":"btc-eur","frequencies":[{"frequency":"1m","min_time":"2011-08-28T18:26:00.000000000Z","max_time":"2022-06-29T00:34:00.000000000Z"},{"frequency":"5m","min_time":"2011-08-28T18:25:00.000000000Z","max_time":"2022-06-29T00:30:00.000000000Z"},{"frequency":"10m","min_time":"2011-08-28T18:20:00.000000000Z","max_time":"2022-06-29T00:20:00.000000000Z"}]},{"pair":"btc-usd","frequencies":[{"frequency":"1m","min_time":"2010-07-18T03:43:00.000000000Z","max_time":"2022-06-29T00:16:00.000000000Z"},{"frequency":"5m","min_time":"2010-07-18T03:40:00.000000000Z","max_time":"2022-06-29T00:10:00.000000000Z"},{"frequency":"10m","min_time":"2010-07-18T03:40:00.000000000Z","max_time":"2022-06-29T00:00:00.000000000Z"},{"frequency":"15m","min_time":"2010-07-18T03:30:00.000000000Z","max_time":"2022-06-29T00:00:00.000000000Z"},{"frequency":"30m","min_time":"2010-07-18T03:30:00.000000000Z","max_time":"2022-06-28T23:30:00.000000000Z"},{"frequency":"1h","min_time":"2010-07-18T03:00:00.000000000Z","max_time":"2022-06-28T23:00:00.000000000Z"},{"frequency":"4h","min_time":"2010-07-18T00:00:00.000000000Z","max_time":"2022-06-28T20:00:00.000000000Z"},{"frequency":"1d","min_time":"2010-07-18T00:00:00.000000000Z","max_time":"2022-06-29T00:00:00.000000000Z"}]},{"pair":"eth-eur","frequencies":[{"frequency":"1m","min_time":"2023-09-17T23:54:00.000000000Z","max_time":"2023-09-17T23:59:00.000000000Z"},{"frequency":"5m","min_time":"2023-09-17T23:30:00.000000000Z","max_time":"2023-09-17T23:55:00.000000000Z"},{"frequency":"10m","min_time":"2023-09-17T23:00:00.000000000Z","max_time":"2023-09-17T23:50:00.000000000Z"},{"frequency":"15m","min_time":"2023-09-17T22:30:00.000000000Z","max_time":"2023-09-17T23:45:00.000000000Z"},{"frequency":"30m","min_time":"2023-09-17T21:00:00.000000000Z","max_time":"2023-09-17T23:30:00.000000000Z"},{"frequency":"1h","min_time":"2023-09-14T20:00:00.000000000Z","max_time":"2023-09-17T19:00:00.000000000Z"},{"frequency":"4h","min_time":"2023-09-17T00:00:00.000000000Z","max_time":"2023-09-17T20:00:00.000000000Z"},{"frequency":"1d","min_time":"2023-09-15T00:00:00.000000000Z","max_time":"2023-09-17T00:00:00.000000000Z"}]},{"pair":"eth-usd","frequencies":[{"frequency":"1m","min_time":"2022-06-29T00:22:00.000000000Z","max_time":"2022-06-29T00:22:00.000000000Z"},{"frequency":"5m","min_time":"2022-06-29T00:15:00.000000000Z","max_time":"2022-06-29T00:15:00.000000000Z"},{"frequency":"10m","min_time":"2022-06-29T00:10:00.000000000Z","max_time":"2022-06-29T00:10:00.000000000Z"}]},{"pair":"sol-usd","frequencies":[{"frequency":"1m","min_time":"2022-05-29T00:22:00.000000000Z","max_time":"2022-05-29T00:22:00.000000000Z"},{"frequency":"5m","min_time":"2022-05-29T00:15:00.000000000Z","max_time":"2022-05-29T00:15:00.000000000Z"},{"frequency":"10m","min_time":"2022-05-29T00:10:00.000000000Z","max_time":"2022-05-29T00:10:00.000000000Z"}]}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/catalog/pair-candles?api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `should return empty pair candles statistics for community key`() {
        assertResponse(
            200,
            EMPTY_RESPONSE,
            "/v4/catalog/pair-candles?api_key=$COMMUNITY_KEY",
        )
    }

    @Test
    fun `should return pair candles statistics for specific pair`() {
        val expectedResponse =
            """{"data":[{"pair":"btc-eur","frequencies":[{"frequency":"1m","min_time":"2011-08-28T18:26:00.000000000Z","max_time":"2022-06-29T00:34:00.000000000Z"},{"frequency":"5m","min_time":"2011-08-28T18:25:00.000000000Z","max_time":"2022-06-29T00:30:00.000000000Z"},{"frequency":"10m","min_time":"2011-08-28T18:20:00.000000000Z","max_time":"2022-06-29T00:20:00.000000000Z"}]}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/catalog/pair-candles?pairs=btc-eur&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `should return pair candles statistics for specific pairs list`() {
        val expectedResponse =
            """{"data":[{"pair":"btc-eur","frequencies":[{"frequency":"1m","min_time":"2011-08-28T18:26:00.000000000Z","max_time":"2022-06-29T00:34:00.000000000Z"},{"frequency":"5m","min_time":"2011-08-28T18:25:00.000000000Z","max_time":"2022-06-29T00:30:00.000000000Z"},{"frequency":"10m","min_time":"2011-08-28T18:20:00.000000000Z","max_time":"2022-06-29T00:20:00.000000000Z"}]},{"pair":"eth-usd","frequencies":[{"frequency":"1m","min_time":"2022-06-29T00:22:00.000000000Z","max_time":"2022-06-29T00:22:00.000000000Z"},{"frequency":"5m","min_time":"2022-06-29T00:15:00.000000000Z","max_time":"2022-06-29T00:15:00.000000000Z"},{"frequency":"10m","min_time":"2022-06-29T00:10:00.000000000Z","max_time":"2022-06-29T00:10:00.000000000Z"}]}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/catalog/pair-candles?pairs=btc-eur,eth-usd&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `should return full catalog of pair candles statistics`() {
        val expectedResponse =
            """{"data":[{"pair":"btc-eur","frequencies":[{"frequency":"1m","min_time":"2011-08-28T18:26:00.000000000Z","max_time":"2022-06-29T00:34:00.000000000Z"},{"frequency":"5m","min_time":"2011-08-28T18:25:00.000000000Z","max_time":"2022-06-29T00:30:00.000000000Z"},{"frequency":"10m","min_time":"2011-08-28T18:20:00.000000000Z","max_time":"2022-06-29T00:20:00.000000000Z"}]},{"pair":"btc-usd","frequencies":[{"frequency":"1m","min_time":"2010-07-18T03:43:00.000000000Z","max_time":"2022-06-29T00:16:00.000000000Z"},{"frequency":"5m","min_time":"2010-07-18T03:40:00.000000000Z","max_time":"2022-06-29T00:10:00.000000000Z"},{"frequency":"10m","min_time":"2010-07-18T03:40:00.000000000Z","max_time":"2022-06-29T00:00:00.000000000Z"},{"frequency":"15m","min_time":"2010-07-18T03:30:00.000000000Z","max_time":"2022-06-29T00:00:00.000000000Z"},{"frequency":"30m","min_time":"2010-07-18T03:30:00.000000000Z","max_time":"2022-06-28T23:30:00.000000000Z"},{"frequency":"1h","min_time":"2010-07-18T03:00:00.000000000Z","max_time":"2022-06-28T23:00:00.000000000Z"},{"frequency":"4h","min_time":"2010-07-18T00:00:00.000000000Z","max_time":"2022-06-28T20:00:00.000000000Z"},{"frequency":"1d","min_time":"2010-07-18T00:00:00.000000000Z","max_time":"2022-06-29T00:00:00.000000000Z"}]},{"pair":"eth-eur","frequencies":[{"frequency":"1m","min_time":"2023-09-17T23:54:00.000000000Z","max_time":"2023-09-17T23:59:00.000000000Z"},{"frequency":"5m","min_time":"2023-09-17T23:30:00.000000000Z","max_time":"2023-09-17T23:55:00.000000000Z"},{"frequency":"10m","min_time":"2023-09-17T23:00:00.000000000Z","max_time":"2023-09-17T23:50:00.000000000Z"},{"frequency":"15m","min_time":"2023-09-17T22:30:00.000000000Z","max_time":"2023-09-17T23:45:00.000000000Z"},{"frequency":"30m","min_time":"2023-09-17T21:00:00.000000000Z","max_time":"2023-09-17T23:30:00.000000000Z"},{"frequency":"1h","min_time":"2023-09-14T20:00:00.000000000Z","max_time":"2023-09-17T19:00:00.000000000Z"},{"frequency":"4h","min_time":"2023-09-17T00:00:00.000000000Z","max_time":"2023-09-17T20:00:00.000000000Z"},{"frequency":"1d","min_time":"2023-09-15T00:00:00.000000000Z","max_time":"2023-09-17T00:00:00.000000000Z"}]},{"pair":"eth-usd","frequencies":[{"frequency":"1m","min_time":"2022-06-29T00:22:00.000000000Z","max_time":"2022-06-29T00:22:00.000000000Z"},{"frequency":"5m","min_time":"2022-06-29T00:15:00.000000000Z","max_time":"2022-06-29T00:15:00.000000000Z"},{"frequency":"10m","min_time":"2022-06-29T00:10:00.000000000Z","max_time":"2022-06-29T00:10:00.000000000Z"}]},{"pair":"sol-usd","frequencies":[{"frequency":"1m","min_time":"2022-05-29T00:22:00.000000000Z","max_time":"2022-05-29T00:22:00.000000000Z"},{"frequency":"5m","min_time":"2022-05-29T00:15:00.000000000Z","max_time":"2022-05-29T00:15:00.000000000Z"},{"frequency":"10m","min_time":"2022-05-29T00:10:00.000000000Z","max_time":"2022-05-29T00:10:00.000000000Z"}]}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/catalog-all/pair-candles?api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `should return full catalog of pair candles statistics for a community key`() {
        val expectedResponse =
            """{"data":[{"pair":"btc-eur","frequencies":[{"frequency":"1m","min_time":"2011-08-28T18:26:00.000000000Z","max_time":"2022-06-29T00:34:00.000000000Z"},{"frequency":"5m","min_time":"2011-08-28T18:25:00.000000000Z","max_time":"2022-06-29T00:30:00.000000000Z"},{"frequency":"10m","min_time":"2011-08-28T18:20:00.000000000Z","max_time":"2022-06-29T00:20:00.000000000Z"}]},{"pair":"btc-usd","frequencies":[{"frequency":"1m","min_time":"2010-07-18T03:43:00.000000000Z","max_time":"2022-06-29T00:16:00.000000000Z"},{"frequency":"5m","min_time":"2010-07-18T03:40:00.000000000Z","max_time":"2022-06-29T00:10:00.000000000Z"},{"frequency":"10m","min_time":"2010-07-18T03:40:00.000000000Z","max_time":"2022-06-29T00:00:00.000000000Z"},{"frequency":"15m","min_time":"2010-07-18T03:30:00.000000000Z","max_time":"2022-06-29T00:00:00.000000000Z"},{"frequency":"30m","min_time":"2010-07-18T03:30:00.000000000Z","max_time":"2022-06-28T23:30:00.000000000Z"},{"frequency":"1h","min_time":"2010-07-18T03:00:00.000000000Z","max_time":"2022-06-28T23:00:00.000000000Z"},{"frequency":"4h","min_time":"2010-07-18T00:00:00.000000000Z","max_time":"2022-06-28T20:00:00.000000000Z"},{"frequency":"1d","min_time":"2010-07-18T00:00:00.000000000Z","max_time":"2022-06-29T00:00:00.000000000Z"}]},{"pair":"eth-eur","frequencies":[{"frequency":"1m","min_time":"2023-09-17T23:54:00.000000000Z","max_time":"2023-09-17T23:59:00.000000000Z"},{"frequency":"5m","min_time":"2023-09-17T23:30:00.000000000Z","max_time":"2023-09-17T23:55:00.000000000Z"},{"frequency":"10m","min_time":"2023-09-17T23:00:00.000000000Z","max_time":"2023-09-17T23:50:00.000000000Z"},{"frequency":"15m","min_time":"2023-09-17T22:30:00.000000000Z","max_time":"2023-09-17T23:45:00.000000000Z"},{"frequency":"30m","min_time":"2023-09-17T21:00:00.000000000Z","max_time":"2023-09-17T23:30:00.000000000Z"},{"frequency":"1h","min_time":"2023-09-14T20:00:00.000000000Z","max_time":"2023-09-17T19:00:00.000000000Z"},{"frequency":"4h","min_time":"2023-09-17T00:00:00.000000000Z","max_time":"2023-09-17T20:00:00.000000000Z"},{"frequency":"1d","min_time":"2023-09-15T00:00:00.000000000Z","max_time":"2023-09-17T00:00:00.000000000Z"}]},{"pair":"eth-usd","frequencies":[{"frequency":"1m","min_time":"2022-06-29T00:22:00.000000000Z","max_time":"2022-06-29T00:22:00.000000000Z"},{"frequency":"5m","min_time":"2022-06-29T00:15:00.000000000Z","max_time":"2022-06-29T00:15:00.000000000Z"},{"frequency":"10m","min_time":"2022-06-29T00:10:00.000000000Z","max_time":"2022-06-29T00:10:00.000000000Z"}]},{"pair":"sol-usd","frequencies":[{"frequency":"1m","min_time":"2022-05-29T00:22:00.000000000Z","max_time":"2022-05-29T00:22:00.000000000Z"},{"frequency":"5m","min_time":"2022-05-29T00:15:00.000000000Z","max_time":"2022-05-29T00:15:00.000000000Z"},{"frequency":"10m","min_time":"2022-05-29T00:10:00.000000000Z","max_time":"2022-05-29T00:10:00.000000000Z"}]}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/catalog-all/pair-candles?api_key=$COMMUNITY_KEY",
        )
    }

    @Test
    fun `should return 403 when pair candles is not supported for the key`() {
        getResponse("/v4/catalog/pair-candles?api_key=$TEST_API_KEY_2").assertResponse()
    }

    @Test
    fun `should return 400 error when statistic for an unknown pair is requested`() {
        val expectedResponse =
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'pairs'. Value 'btc-eth' is not supported."}}"""
        assertResponse(
            400,
            expectedResponse,
            "/v4/catalog/pair-candles?pairs=btc-eth&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `should return 400 error when statistic for an unknown pair is requested together with valid one`() {
        val expectedResponse =
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'pairs'. Value 'btc-eth' is not supported."}}"""
        assertResponse(
            400,
            expectedResponse,
            "/v4/catalog/pair-candles?pairs=btc-usd,btc-eth&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `should return 401 error when api key is not specified`() {
        val expectedResponse =
            """{"error":{"type":"unauthorized","message":"Requested resource requires authorization."}}"""
        assertResponse(
            401,
            expectedResponse,
            "/v4/catalog/pair-candles",
        )
    }

    @Test
    fun `should return 401 error when api key is not specified for full catalog`() {
        val expectedResponse =
            """{"error":{"type":"unauthorized","message":"Requested resource requires authorization."}}"""
        assertResponse(
            401,
            expectedResponse,
            "/v4/catalog-all/pair-candles",
        )
    }

    @Test
    fun `should return 401 error when api key is unknown`() {
        val expectedResponse =
            """{"error":{"type":"wrong_credentials","message":"Supplied credentials are not valid."}}"""
        assertResponse(
            401,
            expectedResponse,
            "/v4/catalog/pair-candles?api_key=unknown",
        )
    }
}
