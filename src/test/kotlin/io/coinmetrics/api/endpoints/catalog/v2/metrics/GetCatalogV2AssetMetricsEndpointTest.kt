package io.coinmetrics.api.endpoints.catalog.v2.metrics

import io.coinmetrics.api.helper.BaseTest
import io.coinmetrics.api.helper.COMMUNITY_KEY
import io.coinmetrics.api.helper.EMPTY_RESPONSE
import io.coinmetrics.api.helper.TEST_API_KEY
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class GetCatalogV2AssetMetricsEndpointTest : BaseTest() {
    @Test
    fun `should return 400 when asset is unknown`() {
        val expectedResponse =
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'assets'. Value 'unknownasset' is not supported."}}"""
        assertResponse(
            400,
            expectedResponse,
            "/v4/catalog-v2/asset-metrics?api_key=$TEST_API_KEY&assets=btc,unknownasset",
        )
    }

    @Test
    fun `should return 400 when metric is unknown`() {
        val expectedResponse =
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'metrics'. Value 'unknownmetric' is not supported."}}"""
        assertResponse(
            400,
            expectedResponse,
            "/v4/catalog-v2/asset-metrics?api_key=$TEST_API_KEY&assets=btc&metrics=unknownmetric",
        )
    }

    @Test
    fun `should return all asset-metrics`() {
        getResponse("/v4/catalog-v2/asset-metrics?api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should return all asset-metrics in json_stream`() {
        getResponse("/v4/catalog-v2/asset-metrics?api_key=$TEST_API_KEY&format=json_stream").assertResponse()
    }

    @Test
    fun `should ignore page size when format=json_stream`() {
        getResponse("/v4/catalog-v2/asset-metrics?api_key=$TEST_API_KEY&format=json_stream&page_size=2").assertResponse()
    }

    @Test
    fun `should return asset-metrics filtered by assets`() {
        assertResponse(
            200,
            """{"data":[{"asset":"btc","metrics":[{"metric":"FlowTfrOutBTXCnt","frequencies":[{"frequency":"1d","min_time":"2020-04-08T00:00:00.000000000Z","max_time":"2020-04-11T00:00:00.000000000Z"}]},{"metric":"PriceUSD","frequencies":[{"frequency":"1b","min_time":"2020-04-16T15:08:13.000000000Z","max_time":"2020-04-16T15:08:13.000000000Z","min_height":"1","max_height":"3","min_hash":"a1","max_hash":"b4"},{"frequency":"1d","min_time":"2020-04-11T00:00:00.000000000Z","max_time":"2020-04-15T00:00:00.000000000Z","community":true}]},{"metric":"ReferenceRate","frequencies":[{"frequency":"1s","min_time":"2016-06-13T23:23:54.000000000Z","max_time":"2020-01-07T20:15:00.000000000Z","community":true},{"frequency":"1m","min_time":"2016-06-13T23:24:00.000000000Z","max_time":"2020-01-07T20:15:00.000000000Z","community":true},{"frequency":"1h","min_time":"2019-01-23T21:00:00.000000000Z","max_time":"2019-07-14T01:00:00.000000000Z","community":true},{"frequency":"1d","min_time":"2019-06-24T00:00:00.000000000Z","max_time":"2019-06-25T00:00:00.000000000Z","community":true},{"frequency":"1d-ny-close","min_time":"2019-01-23T21:00:00.000000000Z","max_time":"2019-05-31T20:00:00.000000000Z","community":true}]},{"metric":"ReferenceRateEUR","frequencies":[{"frequency":"1s","min_time":"2016-06-13T23:23:54.000000000Z","max_time":"2020-01-07T20:15:00.000000000Z","community":true},{"frequency":"1m","min_time":"2016-06-13T23:24:00.000000000Z","max_time":"2020-01-07T20:15:00.000000000Z","community":true},{"frequency":"1h","min_time":"2019-01-23T21:00:00.000000000Z","max_time":"2019-07-14T01:00:00.000000000Z","community":true},{"frequency":"1d","min_time":"2019-06-24T00:00:00.000000000Z","max_time":"2019-06-25T00:00:00.000000000Z","community":true},{"frequency":"1d-ny-close","min_time":"2019-01-23T21:00:00.000000000Z","max_time":"2019-05-31T20:00:00.000000000Z","community":true}]},{"metric":"ReferenceRateUSD","frequencies":[{"frequency":"1s","min_time":"2016-06-13T23:23:54.000000000Z","max_time":"2020-01-07T20:15:00.000000000Z","community":true},{"frequency":"1m","min_time":"2016-06-13T23:24:00.000000000Z","max_time":"2020-01-07T20:15:00.000000000Z","community":true},{"frequency":"1h","min_time":"2019-01-23T21:00:00.000000000Z","max_time":"2019-07-14T01:00:00.000000000Z","community":true},{"frequency":"1d","min_time":"2019-06-24T00:00:00.000000000Z","max_time":"2019-06-25T00:00:00.000000000Z","community":true},{"frequency":"1d-ny-close","min_time":"2019-01-23T21:00:00.000000000Z","max_time":"2019-05-31T20:00:00.000000000Z","community":true}]},{"metric":"SplyExUSD","frequencies":[{"frequency":"1d","min_time":"2020-04-08T00:00:00.000000000Z","max_time":"2020-04-11T00:00:00.000000000Z"}]},{"metric":"TxCnt","frequencies":[{"frequency":"1b","min_time":"2020-04-16T15:08:13.000000000Z","max_time":"2020-04-16T15:08:13.000000000Z","min_height":"1","max_height":"3","min_hash":"a1","max_hash":"b4"},{"frequency":"1d","min_time":"2020-04-11T00:00:00.000000000Z","max_time":"2020-04-15T00:00:00.000000000Z","community":true}]},{"metric":"block_count_at_tip","frequencies":[{"frequency":"1b","min_time":"2009-01-03T18:15:05.000000000Z","max_time":"2022-12-28T08:16:11.618750000Z","min_height":"1","max_height":"769215","min_hash":"000000000019d6689c085ae165831e934ff763ae46a2a6c172b3f1b60a8ce26f","max_hash":"00000000000000000005f11253bdaab14b69109cf5aee0cb7986495e0edcd1ca"}]},{"metric":"block_count_empty_6b","frequencies":[{"frequency":"1b","min_time":"2021-03-09T13:09:03.000000000Z","max_time":"2021-03-09T13:19:45.000000000Z","min_height":"673863","max_height":"673864","min_hash":"0000000000000000000985863cfae2ba05dee4756d75abc88413b922fce42feb","max_hash":"000000000000000000018bcd97d245f054da64df5e3d204182d2c9d032032366"}]},{"metric":"block_difficulty","frequencies":[{"frequency":"1b","min_time":"2022-12-28T07:29:48.000000000Z","max_time":"2022-12-28T08:18:53.000000000Z","min_height":"769214","max_height":"769216","min_hash":"0000000000000000000420500b6ca7b3ff888ef659b0c7311122b1b078136b27","max_hash":"000000000000000000058c05e97952f12b0307fc4ec8ed2a584284270bb1fd58"}]},{"metric":"block_feerate_min","frequencies":[{"frequency":"1b","min_time":"2009-01-03T18:15:07.000000000Z","max_time":"2009-01-03T18:15:07.000000000Z","min_height":"1","max_height":"6","min_hash":"00000000839a8e6886ab5951d76f411475428afc90947ee320161bbf18eb6048","max_hash":"000000003031a0e73735690c5a1ff2a4be82553b2a12b776fbd3a215dc8f778d"}]},{"metric":"block_fees","frequencies":[{"frequency":"1b","min_time":"2009-01-03T18:15:07.000000000Z","max_time":"2022-12-28T08:18:53.000000000Z","min_height":"6","max_height":"769216","min_hash":"000000003031a0e73735690c5a1ff2a4be82553b2a12b776fbd3a215dc8f778d","max_hash":"000000000000000000058c05e97952f12b0307fc4ec8ed2a584284270bb1fd58"}]},{"metric":"confirmation_suggestion_min","frequencies":[{"frequency":"1m","min_time":"2009-01-03T18:16:00.000000000Z","max_time":"2009-01-03T18:17:00.000000000Z"}]},{"metric":"futures_aggregate_funding_rate_all_margin_1d_period","frequencies":[{"frequency":"1h","min_time":"2023-09-14T00:00:00.000000000Z","max_time":"2023-09-14T00:00:00.000000000Z"},{"frequency":"1d","min_time":"2023-09-14T00:00:00.000000000Z","max_time":"2023-09-14T00:00:00.000000000Z"}]},{"metric":"futures_aggregate_funding_rate_all_margin_1y_period","frequencies":[{"frequency":"1h","min_time":"2023-09-14T00:00:00.000000000Z","max_time":"2023-09-14T00:00:00.000000000Z"},{"frequency":"1d","min_time":"2023-09-14T00:00:00.000000000Z","max_time":"2023-09-14T00:00:00.000000000Z"}]},{"metric":"futures_aggregate_funding_rate_all_margin_30d_period","frequencies":[{"frequency":"1h","min_time":"2023-09-14T00:00:00.000000000Z","max_time":"2023-09-14T00:00:00.000000000Z"},{"frequency":"1d","min_time":"2023-09-14T00:00:00.000000000Z","max_time":"2023-09-14T00:00:00.000000000Z"}]},{"metric":"futures_aggregate_funding_rate_all_margin_8h_period","frequencies":[{"frequency":"1h","min_time":"2023-09-14T00:00:00.000000000Z","max_time":"2023-09-14T00:00:00.000000000Z"},{"frequency":"1d","min_time":"2023-09-14T00:00:00.000000000Z","max_time":"2023-09-14T00:00:00.000000000Z"}]},{"metric":"futures_aggregate_funding_rate_coin_margin_1d_period","frequencies":[{"frequency":"1h","min_time":"2023-09-14T00:00:00.000000000Z","max_time":"2023-09-14T00:00:00.000000000Z"},{"frequency":"1d","min_time":"2023-09-14T00:00:00.000000000Z","max_time":"2023-09-14T00:00:00.000000000Z"}]},{"metric":"futures_aggregate_funding_rate_coin_margin_1y_period","frequencies":[{"frequency":"1h","min_time":"2023-09-14T00:00:00.000000000Z","max_time":"2023-09-14T00:00:00.000000000Z"},{"frequency":"1d","min_time":"2023-09-14T00:00:00.000000000Z","max_time":"2023-09-14T00:00:00.000000000Z"}]},{"metric":"futures_aggregate_funding_rate_coin_margin_30d_period","frequencies":[{"frequency":"1h","min_time":"2023-09-14T00:00:00.000000000Z","max_time":"2023-09-14T00:00:00.000000000Z"},{"frequency":"1d","min_time":"2023-09-14T00:00:00.000000000Z","max_time":"2023-09-14T00:00:00.000000000Z"}]},{"metric":"futures_aggregate_funding_rate_coin_margin_8h_period","frequencies":[{"frequency":"1h","min_time":"2023-09-14T00:00:00.000000000Z","max_time":"2023-09-14T00:00:00.000000000Z"},{"frequency":"1d","min_time":"2023-09-14T00:00:00.000000000Z","max_time":"2023-09-14T00:00:00.000000000Z"}]},{"metric":"futures_aggregate_funding_rate_usd_margin_1d_period","frequencies":[{"frequency":"1h","min_time":"2023-09-14T00:00:00.000000000Z","max_time":"2023-09-14T00:00:00.000000000Z"},{"frequency":"1d","min_time":"2023-09-14T00:00:00.000000000Z","max_time":"2023-09-14T00:00:00.000000000Z"}]},{"metric":"futures_aggregate_funding_rate_usd_margin_1y_period","frequencies":[{"frequency":"1h","min_time":"2023-09-14T00:00:00.000000000Z","max_time":"2023-09-14T00:00:00.000000000Z"},{"frequency":"1d","min_time":"2023-09-14T00:00:00.000000000Z","max_time":"2023-09-14T00:00:00.000000000Z"}]},{"metric":"futures_aggregate_funding_rate_usd_margin_30d_period","frequencies":[{"frequency":"1h","min_time":"2023-09-14T00:00:00.000000000Z","max_time":"2023-09-14T00:00:00.000000000Z"},{"frequency":"1d","min_time":"2023-09-14T00:00:00.000000000Z","max_time":"2023-09-14T00:00:00.000000000Z"}]},{"metric":"futures_aggregate_funding_rate_usd_margin_8h_period","frequencies":[{"frequency":"1h","min_time":"2023-09-12T00:00:00.000000000Z","max_time":"2023-09-14T07:00:00.000000000Z"},{"frequency":"1d","min_time":"2023-09-12T00:00:00.000000000Z","max_time":"2023-09-14T07:00:00.000000000Z"}]},{"metric":"futures_cumulative_funding_rate_all_margin_1d","frequencies":[{"frequency":"1h","min_time":"2023-09-14T00:00:00.000000000Z","max_time":"2023-09-14T00:00:00.000000000Z"},{"frequency":"1d","min_time":"2023-09-14T00:00:00.000000000Z","max_time":"2023-09-14T00:00:00.000000000Z"}]},{"metric":"futures_cumulative_funding_rate_all_margin_30d","frequencies":[{"frequency":"1h","min_time":"2023-09-14T00:00:00.000000000Z","max_time":"2023-09-14T00:00:00.000000000Z"},{"frequency":"1d","min_time":"2023-09-14T00:00:00.000000000Z","max_time":"2023-09-14T00:00:00.000000000Z"}]},{"metric":"futures_cumulative_funding_rate_all_margin_7d","frequencies":[{"frequency":"1h","min_time":"2023-09-14T00:00:00.000000000Z","max_time":"2023-09-14T00:00:00.000000000Z"},{"frequency":"1d","min_time":"2023-09-14T00:00:00.000000000Z","max_time":"2023-09-14T00:00:00.000000000Z"}]},{"metric":"futures_cumulative_funding_rate_coin_margin_1d","frequencies":[{"frequency":"1h","min_time":"2023-09-14T00:00:00.000000000Z","max_time":"2023-09-14T00:00:00.000000000Z"},{"frequency":"1d","min_time":"2023-09-14T00:00:00.000000000Z","max_time":"2023-09-14T00:00:00.000000000Z"}]},{"metric":"futures_cumulative_funding_rate_coin_margin_30d","frequencies":[{"frequency":"1h","min_time":"2023-09-14T00:00:00.000000000Z","max_time":"2023-09-14T00:00:00.000000000Z"},{"frequency":"1d","min_time":"2023-09-14T00:00:00.000000000Z","max_time":"2023-09-14T00:00:00.000000000Z"}]},{"metric":"futures_cumulative_funding_rate_coin_margin_7d","frequencies":[{"frequency":"1h","min_time":"2023-09-14T00:00:00.000000000Z","max_time":"2023-09-14T00:00:00.000000000Z"},{"frequency":"1d","min_time":"2023-09-14T00:00:00.000000000Z","max_time":"2023-09-14T00:00:00.000000000Z"}]},{"metric":"futures_cumulative_funding_rate_usd_margin_1d","frequencies":[{"frequency":"1h","min_time":"2023-09-12T00:00:00.000000000Z","max_time":"2023-09-14T07:00:00.000000000Z"},{"frequency":"1d","min_time":"2023-09-12T00:00:00.000000000Z","max_time":"2023-09-14T07:00:00.000000000Z"}]},{"metric":"futures_cumulative_funding_rate_usd_margin_30d","frequencies":[{"frequency":"1h","min_time":"2023-09-14T00:00:00.000000000Z","max_time":"2023-09-14T00:00:00.000000000Z"},{"frequency":"1d","min_time":"2023-09-14T00:00:00.000000000Z","max_time":"2023-09-14T00:00:00.000000000Z"}]},{"metric":"futures_cumulative_funding_rate_usd_margin_7d","frequencies":[{"frequency":"1h","min_time":"2023-09-14T00:00:00.000000000Z","max_time":"2023-09-14T00:00:00.000000000Z"},{"frequency":"1d","min_time":"2023-09-14T00:00:00.000000000Z","max_time":"2023-09-14T00:00:00.000000000Z"}]},{"metric":"mempool_count","frequencies":[{"frequency":"1m","min_time":"2009-01-03T18:15:00.000000000Z","max_time":"2009-01-03T18:16:00.000000000Z"}]},{"metric":"mempool_fee","frequencies":[{"frequency":"1m","min_time":"2009-01-03T18:15:00.000000000Z","max_time":"2009-01-03T18:16:00.000000000Z"}]},{"metric":"mempool_next_block_inclusion_approx_feerate_min","frequencies":[{"frequency":"1m","min_time":"2009-01-03T18:16:00.000000000Z","max_time":"2009-01-03T18:16:00.000000000Z"}]},{"metric":"open_interest_reported_future_usd","frequencies":[{"frequency":"1h","min_time":"2020-12-25T05:00:00.000000000Z","max_time":"2020-12-27T00:00:00.000000000Z"},{"frequency":"1d","min_time":"2020-12-25T05:00:00.000000000Z","max_time":"2020-12-27T00:00:00.000000000Z"}]},{"metric":"principal_market_price_usd","frequencies":[{"frequency":"1s","min_time":"2022-10-09T17:58:00.000000000Z","max_time":"2023-09-04T17:00:00.000000000Z","community":true},{"frequency":"1m","min_time":"2022-10-09T17:58:00.000000000Z","max_time":"2023-09-04T17:00:00.000000000Z","community":true},{"frequency":"1h","min_time":"2022-10-08T23:00:00.000000000Z","max_time":"2023-09-04T22:00:00.000000000Z","community":true},{"frequency":"1d","min_time":"2022-10-09T00:00:00.000000000Z","max_time":"2023-09-03T00:00:00.000000000Z","community":true},{"frequency":"1d-ny-close","min_time":"2022-10-09T12:00:00.000000000Z","max_time":"2022-10-09T12:00:00.000000000Z","community":true}]},{"metric":"principal_market_usd","frequencies":[{"frequency":"1s","min_time":"2022-10-09T17:58:00.000000000Z","max_time":"2023-09-04T17:00:00.000000000Z","community":true},{"frequency":"1m","min_time":"2022-10-09T17:58:00.000000000Z","max_time":"2023-09-04T17:00:00.000000000Z","community":true},{"frequency":"1h","min_time":"2022-10-08T23:00:00.000000000Z","max_time":"2023-09-04T22:00:00.000000000Z","community":true},{"frequency":"1d","min_time":"2022-10-09T00:00:00.000000000Z","max_time":"2023-09-03T00:00:00.000000000Z","community":true},{"frequency":"1d-ny-close","min_time":"2022-10-09T12:00:00.000000000Z","max_time":"2022-10-09T12:00:00.000000000Z","community":true}]},{"metric":"volatility_realized_usd_rolling_24h","frequencies":[{"frequency":"10m","min_time":"2022-11-28T22:20:00.000000000Z","max_time":"2023-08-25T01:00:00.000000000Z"},{"frequency":"1h","min_time":"2022-11-28T22:20:00.000000000Z","max_time":"2023-08-25T01:00:00.000000000Z"},{"frequency":"1d","min_time":"2022-11-28T22:20:00.000000000Z","max_time":"2023-08-25T01:00:00.000000000Z"}]},{"metric":"volatility_realized_usd_rolling_30d","frequencies":[{"frequency":"10m","min_time":"2022-11-28T22:20:00.000000000Z","max_time":"2023-08-25T01:00:00.000000000Z"},{"frequency":"1h","min_time":"2022-11-28T22:20:00.000000000Z","max_time":"2023-08-25T01:00:00.000000000Z"},{"frequency":"1d","min_time":"2022-11-28T22:20:00.000000000Z","max_time":"2023-08-25T01:00:00.000000000Z"}]},{"metric":"volatility_realized_usd_rolling_7d","frequencies":[{"frequency":"10m","min_time":"2022-11-28T22:20:00.000000000Z","max_time":"2023-08-25T01:00:00.000000000Z"},{"frequency":"1h","min_time":"2022-11-28T22:20:00.000000000Z","max_time":"2023-08-25T01:00:00.000000000Z"},{"frequency":"1d","min_time":"2022-11-28T22:20:00.000000000Z","max_time":"2023-08-25T01:00:00.000000000Z"}]},{"metric":"volume_trusted_spot_usd_1d","frequencies":[{"frequency":"1d","min_time":"2020-12-25T00:00:00.000000000Z","max_time":"2020-12-29T00:00:00.000000000Z"}]},{"metric":"volume_trusted_spot_usd_1h","frequencies":[{"frequency":"1h","min_time":"2020-12-25T01:00:00.000000000Z","max_time":"2020-12-25T05:00:00.000000000Z"}]}]},{"asset":"ocean","metrics":[{"metric":"CapMrktEstUSD","frequencies":[{"frequency":"1d","min_time":"2019-07-17T00:00:00.000000000Z","max_time":"2019-07-26T00:00:00.000000000Z"}]}]}]}""",
            "/v4/catalog-v2/asset-metrics?api_key=$TEST_API_KEY&assets=btc,ocean",
        )
    }

    @Test
    fun `should return asset-metrics filtered by the eth asset`() {
        getResponse("/v4/catalog-v2/asset-metrics?api_key=$TEST_API_KEY&assets=eth").assertResponse()
    }

    @Test
    fun `should return asset-metrics filtered by metrics`() {
        assertResponse(
            200,
            """{"data":[{"asset":"btc","metrics":[{"metric":"TxCnt","frequencies":[{"frequency":"1b","min_time":"2020-04-16T15:08:13.000000000Z","max_time":"2020-04-16T15:08:13.000000000Z","min_height":"1","max_height":"3","min_hash":"a1","max_hash":"b4"},{"frequency":"1d","min_time":"2020-04-11T00:00:00.000000000Z","max_time":"2020-04-15T00:00:00.000000000Z","community":true}]}]},{"asset":"eth","metrics":[{"metric":"TxCnt","frequencies":[{"frequency":"1b","min_time":"2020-04-12T12:08:13.000000000Z","max_time":"2020-04-15T15:08:13.000000000Z","min_height":"1","max_height":"4","min_hash":"a1","max_hash":"a4"},{"frequency":"1d","min_time":"2020-04-08T00:00:00.000000000Z","max_time":"2020-04-11T00:00:00.000000000Z","community":true}]}]},{"asset":"sol","metrics":[{"metric":"TxCnt","frequencies":[{"frequency":"1b","min_time":"2022-04-04T08:20:00.000000000Z","max_time":"2022-04-04T08:20:01.000000000Z","min_height":"116119303","max_height":"116119305","min_hash":"8XEvUB6gqKyvLATsWwfuNtEpALoMc9cn5LDkfUMAg4tM","max_hash":"********************************************","experimental":true}]}]}]}""",
            "/v4/catalog-v2/asset-metrics?api_key=$TEST_API_KEY&metrics=TxCnt",
        )
    }

    @Test
    fun `should return asset-metrics filtered by assets and metrics`() {
        assertResponse(
            200,
            """{"data":[{"asset":"btc","metrics":[{"metric":"TxCnt","frequencies":[{"frequency":"1b","min_time":"2020-04-16T15:08:13.000000000Z","max_time":"2020-04-16T15:08:13.000000000Z","min_height":"1","max_height":"3","min_hash":"a1","max_hash":"b4"},{"frequency":"1d","min_time":"2020-04-11T00:00:00.000000000Z","max_time":"2020-04-15T00:00:00.000000000Z","community":true}]}]},{"asset":"eth","metrics":[{"metric":"TxCnt","frequencies":[{"frequency":"1b","min_time":"2020-04-12T12:08:13.000000000Z","max_time":"2020-04-15T15:08:13.000000000Z","min_height":"1","max_height":"4","min_hash":"a1","max_hash":"a4"},{"frequency":"1d","min_time":"2020-04-08T00:00:00.000000000Z","max_time":"2020-04-11T00:00:00.000000000Z","community":true}]}]}]}""",
            "/v4/catalog-v2/asset-metrics?api_key=$TEST_API_KEY&assets=btc,eth&metrics=TxCnt",
        )
    }

    @Test
    fun `should return empty result when no asset-metrics found filtering by both assets and metrics`() {
        assertResponse(
            200,
            EMPTY_RESPONSE,
            "/v4/catalog-v2/asset-metrics?api_key=$TEST_API_KEY&assets=mtl_metal&metrics=TxCnt",
        )
    }

    @Test
    fun `should return all reviewable metrics`() {
        assertResponse(
            200,
            """{"data":[{"asset":"btc","metrics":[{"metric":"FlowTfrOutBTXCnt","frequencies":[{"frequency":"1d","min_time":"2020-04-08T00:00:00.000000000Z","max_time":"2020-04-11T00:00:00.000000000Z"}]},{"metric":"SplyExUSD","frequencies":[{"frequency":"1d","min_time":"2020-04-08T00:00:00.000000000Z","max_time":"2020-04-11T00:00:00.000000000Z"}]}]},{"asset":"eth","metrics":[{"metric":"FlowInGEMNtv","frequencies":[{"frequency":"1b","min_time":"2020-04-10T19:43:29.000000000Z","max_time":"2020-04-10T19:43:45.000000000Z","min_height":"9846470","max_height":"9846471","min_hash":"e2ae25ebc779861756935e7d91a4349b6082c649e8fd98d7984af4d02b2e3618","max_hash":"35316482a9b9caf7914f24306c89c60623d4d94768752671e152fc7bf05a8c2b"},{"frequency":"1d","min_time":"2020-04-11T00:00:00.000000000Z","max_time":"2020-04-11T00:00:00.000000000Z"}]},{"metric":"FlowTfrInBFXCnt","frequencies":[{"frequency":"1b","min_time":"2020-04-10T19:43:45.000000000Z","max_time":"2020-04-10T19:43:45.000000000Z","min_height":"9846471","max_height":"9846471","min_hash":"35316482a9b9caf7914f24306c89c60623d4d94768752671e152fc7bf05a8c2b","max_hash":"35316482a9b9caf7914f24306c89c60623d4d94768752671e152fc7bf05a8c2b"}]},{"metric":"FlowTfrOutBTXCnt","frequencies":[{"frequency":"1d","min_time":"2020-04-08T00:00:00.000000000Z","max_time":"2020-04-11T00:00:00.000000000Z"}]},{"metric":"SplyExUSD","frequencies":[{"frequency":"1d","min_time":"2020-04-08T00:00:00.000000000Z","max_time":"2020-04-11T00:00:00.000000000Z"}]}]}]}""",
            "/v4/catalog-v2/asset-metrics?api_key=$TEST_API_KEY&reviewable=true",
        )
    }

    @Test
    fun `should return all reviewable metrics for btc asset`() {
        assertResponse(
            200,
            """{"data":[{"asset":"btc","metrics":[{"metric":"FlowTfrOutBTXCnt","frequencies":[{"frequency":"1d","min_time":"2020-04-08T00:00:00.000000000Z","max_time":"2020-04-11T00:00:00.000000000Z"}]},{"metric":"SplyExUSD","frequencies":[{"frequency":"1d","min_time":"2020-04-08T00:00:00.000000000Z","max_time":"2020-04-11T00:00:00.000000000Z"}]}]}]}""",
            "/v4/catalog-v2/asset-metrics?api_key=$TEST_API_KEY&assets=btc&reviewable=true",
        )
    }

    @Test
    fun `should return empty result when non-reviewable metric specified and reviewable=true`() {
        assertResponse(
            200,
            EMPTY_RESPONSE,
            "/v4/catalog-v2/asset-metrics?api_key=$TEST_API_KEY&metrics=AdrActCnt&reviewable=true",
        )
    }

    @Test
    fun `should return all non-reviewable metrics`() {
        getResponse("/v4/catalog-v2/asset-metrics?api_key=$TEST_API_KEY&reviewable=false").assertResponse()
    }

    @Test
    fun `should return the first page of asset-metrics`() {
        assertResponse(
            200,
            """{"data":[{"asset":"ada","metrics":[{"metric":"ReferenceRate","frequencies":[{"frequency":"1h","min_time":"2024-03-28T18:00:00.000000000Z","max_time":"2024-03-28T21:00:00.000000000Z","community":true},{"frequency":"1d-ny-close","min_time":"2024-03-28T20:00:00.000000000Z","max_time":"2024-03-28T20:00:00.000000000Z","community":true}]},{"metric":"ReferenceRateUSD","frequencies":[{"frequency":"1h","min_time":"2024-03-28T18:00:00.000000000Z","max_time":"2024-03-28T21:00:00.000000000Z","community":true},{"frequency":"1d-ny-close","min_time":"2024-03-28T20:00:00.000000000Z","max_time":"2024-03-28T20:00:00.000000000Z","community":true}]}]},{"asset":"ant","metrics":[{"metric":"AdrActCnt","frequencies":[{"frequency":"1d","min_time":"2020-04-15T00:00:00.000000000Z","max_time":"2020-04-15T00:00:00.000000000Z","community":true}]},{"metric":"TxTfrValMedNtv","frequencies":[{"frequency":"1d","min_time":"2020-04-15T00:00:00.000000000Z","max_time":"2020-04-15T00:00:00.000000000Z","community":true}]}]},{"asset":"atom","metrics":[{"metric":"ReferenceRate","frequencies":[{"frequency":"1h","min_time":"2021-11-04T11:00:00.000000000Z","max_time":"2021-11-04T12:00:00.000000000Z","community":true}]},{"metric":"ReferenceRateETH","frequencies":[{"frequency":"1h","min_time":"2021-11-04T11:00:00.000000000Z","max_time":"2021-11-04T12:00:00.000000000Z","community":true}]},{"metric":"ReferenceRateUSD","frequencies":[{"frequency":"1h","min_time":"2021-11-04T11:00:00.000000000Z","max_time":"2021-11-04T12:00:00.000000000Z","community":true}]}]}],"next_page_token":"YnRj","next_page_url":"http://127.0.0.1:8080/v4/catalog-v2/asset-metrics?api_key=x1&page_size=3&next_page_token=YnRj"}""",
            "/v4/catalog-v2/asset-metrics?api_key=$TEST_API_KEY&page_size=3",
        )
    }

    @Test
    fun `should return the second page of asset-metrics`() {
        getResponse("/v4/catalog-v2/asset-metrics?api_key=$TEST_API_KEY&page_size=3&next_page_token=YnRj").assertResponse()
    }

    @Test
    fun `should return the last page of asset-metrics`() {
        assertResponse(
            200,
            """{"data":[{"asset":"ocean","metrics":[{"metric":"CapMrktEstUSD","frequencies":[{"frequency":"1d","min_time":"2019-07-17T00:00:00.000000000Z","max_time":"2019-07-26T00:00:00.000000000Z"}]}]},{"asset":"sol","metrics":[{"metric":"BlkHgt","frequencies":[{"frequency":"1b","min_time":"2022-04-04T08:20:00.000000000Z","max_time":"2022-04-04T08:20:01.000000000Z","min_height":"116119303","max_height":"116119305","min_hash":"8XEvUB6gqKyvLATsWwfuNtEpALoMc9cn5LDkfUMAg4tM","max_hash":"********************************************","experimental":true}]},{"metric":"PriceUSD","frequencies":[{"frequency":"1b","min_time":"2022-04-04T08:19:59.000000000Z","max_time":"2022-04-04T08:20:01.000000000Z","min_height":"116119302","max_height":"116119305","min_hash":"9w4UzaPVUVwhLbhR7zAeyNMTDqfEauJzApgicsgkNgB6","max_hash":"********************************************"}]},{"metric":"TxCnt","frequencies":[{"frequency":"1b","min_time":"2022-04-04T08:20:00.000000000Z","max_time":"2022-04-04T08:20:01.000000000Z","min_height":"116119303","max_height":"116119305","min_hash":"8XEvUB6gqKyvLATsWwfuNtEpALoMc9cn5LDkfUMAg4tM","max_hash":"********************************************","experimental":true}]}]}]}""",
            "/v4/catalog-v2/asset-metrics?api_key=$TEST_API_KEY&page_size=3&next_page_token=b2NlYW4",
        )
    }

    @Test
    fun `should return the first page of asset-metrics paging from end`() {
        assertResponse(
            200,
            """{"data":[{"asset":"mtl_metal","metrics":[{"metric":"ReferenceRate","frequencies":[{"frequency":"1s","min_time":"2016-06-13T23:23:54.000000000Z","max_time":"2016-06-13T23:23:54.000000000Z","community":true}]},{"metric":"ReferenceRateUSD","frequencies":[{"frequency":"1s","min_time":"2016-06-13T23:23:54.000000000Z","max_time":"2016-06-13T23:23:54.000000000Z","community":true}]}]},{"asset":"ocean","metrics":[{"metric":"CapMrktEstUSD","frequencies":[{"frequency":"1d","min_time":"2019-07-17T00:00:00.000000000Z","max_time":"2019-07-26T00:00:00.000000000Z"}]}]},{"asset":"sol","metrics":[{"metric":"BlkHgt","frequencies":[{"frequency":"1b","min_time":"2022-04-04T08:20:00.000000000Z","max_time":"2022-04-04T08:20:01.000000000Z","min_height":"116119303","max_height":"116119305","min_hash":"8XEvUB6gqKyvLATsWwfuNtEpALoMc9cn5LDkfUMAg4tM","max_hash":"********************************************","experimental":true}]},{"metric":"PriceUSD","frequencies":[{"frequency":"1b","min_time":"2022-04-04T08:19:59.000000000Z","max_time":"2022-04-04T08:20:01.000000000Z","min_height":"116119302","max_height":"116119305","min_hash":"9w4UzaPVUVwhLbhR7zAeyNMTDqfEauJzApgicsgkNgB6","max_hash":"********************************************"}]},{"metric":"TxCnt","frequencies":[{"frequency":"1b","min_time":"2022-04-04T08:20:00.000000000Z","max_time":"2022-04-04T08:20:01.000000000Z","min_height":"116119303","max_height":"116119305","min_hash":"8XEvUB6gqKyvLATsWwfuNtEpALoMc9cn5LDkfUMAg4tM","max_hash":"********************************************","experimental":true}]}]}],"next_page_token":"ZXRo","next_page_url":"http://127.0.0.1:8080/v4/catalog-v2/asset-metrics?api_key=x1&page_size=3&paging_from=end&next_page_token=ZXRo"}""",
            "/v4/catalog-v2/asset-metrics?api_key=$TEST_API_KEY&page_size=3&paging_from=end",
        )
    }

    @Test
    fun `should return the second page of asset-metrics paging from end`() {
        getResponse("/v4/catalog-v2/asset-metrics?api_key=$TEST_API_KEY&page_size=3&paging_from=end&next_page_token=ZXRo").assertResponse()
    }

    @Test
    fun `should return the last page of asset-metrics paging from end`() {
        assertResponse(
            200,
            """{"data":[{"asset":"ada","metrics":[{"metric":"ReferenceRate","frequencies":[{"frequency":"1h","min_time":"2024-03-28T18:00:00.000000000Z","max_time":"2024-03-28T21:00:00.000000000Z","community":true},{"frequency":"1d-ny-close","min_time":"2024-03-28T20:00:00.000000000Z","max_time":"2024-03-28T20:00:00.000000000Z","community":true}]},{"metric":"ReferenceRateUSD","frequencies":[{"frequency":"1h","min_time":"2024-03-28T18:00:00.000000000Z","max_time":"2024-03-28T21:00:00.000000000Z","community":true},{"frequency":"1d-ny-close","min_time":"2024-03-28T20:00:00.000000000Z","max_time":"2024-03-28T20:00:00.000000000Z","community":true}]}]},{"asset":"ant","metrics":[{"metric":"AdrActCnt","frequencies":[{"frequency":"1d","min_time":"2020-04-15T00:00:00.000000000Z","max_time":"2020-04-15T00:00:00.000000000Z","community":true}]},{"metric":"TxTfrValMedNtv","frequencies":[{"frequency":"1d","min_time":"2020-04-15T00:00:00.000000000Z","max_time":"2020-04-15T00:00:00.000000000Z","community":true}]}]}]}""",
            "/v4/catalog-v2/asset-metrics?api_key=$TEST_API_KEY&page_size=3&paging_from=end&next_page_token=YW50",
        )
    }

    @Test
    fun `should return the first page of asset-metrics filtering by metrics`() {
        assertResponse(
            200,
            """{"data":[{"asset":"ada","metrics":[{"metric":"ReferenceRate","frequencies":[{"frequency":"1h","min_time":"2024-03-28T18:00:00.000000000Z","max_time":"2024-03-28T21:00:00.000000000Z","community":true},{"frequency":"1d-ny-close","min_time":"2024-03-28T20:00:00.000000000Z","max_time":"2024-03-28T20:00:00.000000000Z","community":true}]}]},{"asset":"atom","metrics":[{"metric":"ReferenceRate","frequencies":[{"frequency":"1h","min_time":"2021-11-04T11:00:00.000000000Z","max_time":"2021-11-04T12:00:00.000000000Z","community":true}]}]},{"asset":"btc","metrics":[{"metric":"ReferenceRate","frequencies":[{"frequency":"1s","min_time":"2016-06-13T23:23:54.000000000Z","max_time":"2020-01-07T20:15:00.000000000Z","community":true},{"frequency":"1m","min_time":"2016-06-13T23:24:00.000000000Z","max_time":"2020-01-07T20:15:00.000000000Z","community":true},{"frequency":"1h","min_time":"2019-01-23T21:00:00.000000000Z","max_time":"2019-07-14T01:00:00.000000000Z","community":true},{"frequency":"1d","min_time":"2019-06-24T00:00:00.000000000Z","max_time":"2019-06-25T00:00:00.000000000Z","community":true},{"frequency":"1d-ny-close","min_time":"2019-01-23T21:00:00.000000000Z","max_time":"2019-05-31T20:00:00.000000000Z","community":true}]}]}],"next_page_token":"ZXRo","next_page_url":"http://127.0.0.1:8080/v4/catalog-v2/asset-metrics?api_key=x1&metrics=ReferenceRate&page_size=3&next_page_token=ZXRo"}""",
            "/v4/catalog-v2/asset-metrics?api_key=$TEST_API_KEY&metrics=ReferenceRate&page_size=3",
        )
    }

    @Test
    fun `should return the last page of asset-metrics filtering by metrics`() {
        getResponse("/v4/catalog-v2/asset-metrics?api_key=$TEST_API_KEY&metrics=ReferenceRate&page_size=3&next_page_token=ZXRo").assertResponse()
    }

    @Test
    fun `should return all asset-metrics for community key`() {
        assertResponse(
            200,
            """{"data":[{"asset":"ant","metrics":[{"metric":"AdrActCnt","frequencies":[{"frequency":"1d","min_time":"2020-04-15T00:00:00.000000000Z","max_time":"2020-04-15T00:00:00.000000000Z","community":true}]},{"metric":"TxTfrValMedNtv","frequencies":[{"frequency":"1d","min_time":"2020-04-15T00:00:00.000000000Z","max_time":"2020-04-15T00:00:00.000000000Z","community":true}]}]},{"asset":"btc","metrics":[{"metric":"PriceUSD","frequencies":[{"frequency":"1d","min_time":"2020-04-11T00:00:00.000000000Z","max_time":"2020-04-15T00:00:00.000000000Z","community":true}]},{"metric":"TxCnt","frequencies":[{"frequency":"1d","min_time":"2020-04-11T00:00:00.000000000Z","max_time":"2020-04-15T00:00:00.000000000Z","community":true}]}]},{"asset":"eth","metrics":[{"metric":"TxCnt","frequencies":[{"frequency":"1d","min_time":"2020-04-08T00:00:00.000000000Z","max_time":"2020-04-11T00:00:00.000000000Z","community":true}]}]}]}""",
            "/v4/catalog-v2/asset-metrics?api_key=$COMMUNITY_KEY",
        )
    }

    @Test
    fun `should return all asset-metrics in json_stream for community key`() {
        assertResponseWithContentType(
            200,
            """
            {"asset":"ant","metrics":[{"metric":"AdrActCnt","frequencies":[{"frequency":"1d","min_time":"2020-04-15T00:00:00.000000000Z","max_time":"2020-04-15T00:00:00.000000000Z","community":true}]},{"metric":"TxTfrValMedNtv","frequencies":[{"frequency":"1d","min_time":"2020-04-15T00:00:00.000000000Z","max_time":"2020-04-15T00:00:00.000000000Z","community":true}]}]}
            {"asset":"btc","metrics":[{"metric":"PriceUSD","frequencies":[{"frequency":"1d","min_time":"2020-04-11T00:00:00.000000000Z","max_time":"2020-04-15T00:00:00.000000000Z","community":true}]},{"metric":"TxCnt","frequencies":[{"frequency":"1d","min_time":"2020-04-11T00:00:00.000000000Z","max_time":"2020-04-15T00:00:00.000000000Z","community":true}]}]}
            {"asset":"eth","metrics":[{"metric":"TxCnt","frequencies":[{"frequency":"1d","min_time":"2020-04-08T00:00:00.000000000Z","max_time":"2020-04-11T00:00:00.000000000Z","community":true}]}]}
            """.trimIndent(),
            "/v4/catalog-v2/asset-metrics?api_key=$COMMUNITY_KEY&format=json_stream",
            contentType = "application/x-ndjson",
        )
    }

    @Test
    fun `should return empty reviewable metrics for community key`() {
        assertResponse(
            200,
            EMPTY_RESPONSE,
            "/v4/catalog-v2/asset-metrics?api_key=$COMMUNITY_KEY&reviewable=true",
        )
    }

    @Test
    fun `should return all non-reviewable metrics for community key`() {
        assertResponse(
            200,
            """{"data":[{"asset":"ant","metrics":[{"metric":"AdrActCnt","frequencies":[{"frequency":"1d","min_time":"2020-04-15T00:00:00.000000000Z","max_time":"2020-04-15T00:00:00.000000000Z","community":true}]},{"metric":"TxTfrValMedNtv","frequencies":[{"frequency":"1d","min_time":"2020-04-15T00:00:00.000000000Z","max_time":"2020-04-15T00:00:00.000000000Z","community":true}]}]},{"asset":"btc","metrics":[{"metric":"PriceUSD","frequencies":[{"frequency":"1d","min_time":"2020-04-11T00:00:00.000000000Z","max_time":"2020-04-15T00:00:00.000000000Z","community":true}]},{"metric":"TxCnt","frequencies":[{"frequency":"1d","min_time":"2020-04-11T00:00:00.000000000Z","max_time":"2020-04-15T00:00:00.000000000Z","community":true}]}]},{"asset":"eth","metrics":[{"metric":"TxCnt","frequencies":[{"frequency":"1d","min_time":"2020-04-08T00:00:00.000000000Z","max_time":"2020-04-11T00:00:00.000000000Z","community":true}]}]}]}""",
            "/v4/catalog-v2/asset-metrics?api_key=$COMMUNITY_KEY&reviewable=false",
        )
    }
}
