package io.coinmetrics.api.endpoints.catalog.v2.metrics

import io.coinmetrics.api.helper.BaseTest
import io.coinmetrics.api.helper.COMMUNITY_KEY
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import java.time.Instant

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class GetCatalogV2AssetMetricsEndpointPrincipalPriceCommunityTest : BaseTest() {
    init {
        clock.instant = Instant.parse("2023-09-04T17:00:01.000Z")
    }

    @Test
    fun `should return principal prices`() {
        assertResponse(
            200,
            """{"data":[{"asset":"btc","metrics":[{"metric":"principal_market_price_usd","frequencies":[{"frequency":"1s","min_time":"2023-08-28T16:00:02.000000000Z","max_time":"2023-09-04T17:00:00.000000000Z","community":true},{"frequency":"1m","min_time":"2023-08-28T16:01:00.000000000Z","max_time":"2023-09-04T17:00:00.000000000Z","community":true},{"frequency":"1h","min_time":"2023-08-28T17:00:00.000000000Z","max_time":"2023-09-04T22:00:00.000000000Z","community":true},{"frequency":"1d","min_time":"2023-08-28T17:00:00.000000000Z","max_time":"2023-09-03T00:00:00.000000000Z","community":true}]}]}]}""",
            "/v4/catalog-v2/asset-metrics?api_key=$COMMUNITY_KEY&metrics=principal_market_price_usd",
        )
    }

    @Test
    fun `should return all principal prices`() {
        assertResponse(
            200,
            """{"data":[{"asset":"btc","metrics":[{"metric":"principal_market_price_usd","frequencies":[{"frequency":"1s","min_time":"2022-10-09T17:58:00.000000000Z","max_time":"2023-09-04T17:00:00.000000000Z","community":true},{"frequency":"1m","min_time":"2022-10-09T17:58:00.000000000Z","max_time":"2023-09-04T17:00:00.000000000Z","community":true},{"frequency":"1h","min_time":"2022-10-08T23:00:00.000000000Z","max_time":"2023-09-04T22:00:00.000000000Z","community":true},{"frequency":"1d","min_time":"2022-10-09T00:00:00.000000000Z","max_time":"2023-09-03T00:00:00.000000000Z","community":true},{"frequency":"1d-ny-close","min_time":"2022-10-09T12:00:00.000000000Z","max_time":"2022-10-09T12:00:00.000000000Z","community":true}]}]},{"asset":"eth","metrics":[{"metric":"principal_market_price_usd","frequencies":[{"frequency":"1s","min_time":"2022-10-09T17:58:00.000000000Z","max_time":"2022-10-09T17:59:49.000000000Z","community":true},{"frequency":"1m","min_time":"2022-10-09T17:58:00.000000000Z","max_time":"2022-10-09T17:59:00.000000000Z","community":true},{"frequency":"1h","min_time":"2022-10-08T23:00:00.000000000Z","max_time":"2022-10-09T12:00:00.000000000Z","community":true},{"frequency":"1d","min_time":"2022-10-09T00:00:00.000000000Z","max_time":"2022-10-09T00:00:00.000000000Z","community":true},{"frequency":"1d-ny-close","min_time":"2022-10-09T12:00:00.000000000Z","max_time":"2022-10-09T12:00:00.000000000Z","community":true}]}]}]}""",
            "/v4/catalog-all-v2/asset-metrics?api_key=$COMMUNITY_KEY&metrics=principal_market_price_usd",
        )
    }
}
