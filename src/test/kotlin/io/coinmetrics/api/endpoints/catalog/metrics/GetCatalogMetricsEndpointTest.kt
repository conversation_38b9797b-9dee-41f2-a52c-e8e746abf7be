package io.coinmetrics.api.endpoints.catalog.metrics

import io.coinmetrics.api.helper.BaseTest
import io.coinmetrics.api.helper.COMMUNITY_KEY
import io.coinmetrics.api.helper.TEST_API_KEY
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class GetCatalogMetricsEndpointTest : BaseTest() {
    @Test
    fun `test metrics discovery`() {
        getResponse("/v4/catalog/metrics?api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `test metric info`() {
        getResponse("/v4/catalog/metrics?api_key=$TEST_API_KEY&metrics=TxTfrValMedNtv").assertResponse()
    }

    @Test
    fun `test multi metric info`() {
        getResponse("/v4/catalog/metrics?api_key=$TEST_API_KEY&metrics=AdrActCnt,FlowInGEMNtv").assertResponse()
    }

    @Test
    fun `test reviewable metrics`() {
        getResponse("/v4/catalog/metrics?api_key=$TEST_API_KEY&metrics=AdrActCnt,FlowInGEMNtv&reviewable=true").assertResponse()
    }

    @Test
    fun `non-reviewable`() {
        getResponse("/v4/catalog/metrics?api_key=$TEST_API_KEY&metrics=AdrActCnt,FlowInGEMNtv&reviewable=false").assertResponse()
    }

    @Test
    fun `test internal chain monitor metrics are not discoverable`() {
        getResponse("/v4/catalog/metrics?api_key=$TEST_API_KEY&metrics=sc_admin_key_change").assertResponse()
    }

    @Test
    fun `test chain monitor metrics discovery with community api key`() {
        getResponse("/v4/catalog/metrics?api_key=$COMMUNITY_KEY&metrics=mempool_count,mempool_fee,block_count_at_tip,sc_admin_key_change").assertResponse()
    }
}
