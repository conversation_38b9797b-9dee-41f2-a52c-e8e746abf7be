package io.coinmetrics.api.endpoints.catalog.market

import io.coinmetrics.api.helper.BaseTest
import io.coinmetrics.api.helper.EMPTY_RESPONSE
import io.coinmetrics.api.helper.TEST_API_KEY
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class MarketLiquidationsCatalogTest : BaseTest() {
    @Test
    fun `should return market liquidations in json format by default`() {
        val expectedResponse =
            """{"data":[{"market":"binance-XBTUSD-future","min_time":"2019-03-15T09:51:28.851000000Z","max_time":"2020-08-04T17:29:47.509000000Z"},{"market":"bitmex-FILUSD-future","min_time":"2023-09-17T10:36:40.247000000Z","max_time":"2023-09-17T10:36:40.247000000Z"},{"market":"bitmex-XBTUSD-future","min_time":"2019-03-15T09:51:28.851000000Z","max_time":"2020-08-04T17:29:47.509000000Z"},{"market":"ftx-SOL-0925-future","min_time":"2019-03-15T10:36:46.428000000Z","max_time":"2019-03-15T10:36:46.428000000Z"},{"market":"huobi-XBTUSD4-future","min_time":"2019-03-15T10:36:31.852000000Z","max_time":"2019-03-15T10:36:31.852000000Z"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/catalog/market-liquidations?api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `should return market liquidations in json format`() {
        val expectedResponse =
            """{"data":[{"market":"binance-XBTUSD-future","min_time":"2019-03-15T09:51:28.851000000Z","max_time":"2020-08-04T17:29:47.509000000Z"},{"market":"bitmex-FILUSD-future","min_time":"2023-09-17T10:36:40.247000000Z","max_time":"2023-09-17T10:36:40.247000000Z"},{"market":"bitmex-XBTUSD-future","min_time":"2019-03-15T09:51:28.851000000Z","max_time":"2020-08-04T17:29:47.509000000Z"},{"market":"ftx-SOL-0925-future","min_time":"2019-03-15T10:36:46.428000000Z","max_time":"2019-03-15T10:36:46.428000000Z"},{"market":"huobi-XBTUSD4-future","min_time":"2019-03-15T10:36:31.852000000Z","max_time":"2019-03-15T10:36:31.852000000Z"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/catalog/market-liquidations?api_key=$TEST_API_KEY&format=json",
        )
    }

    @Test
    fun `should return limited list of market liquidations`() {
        val expectedResponse =
            """{"data":[{"market":"binance-XBTUSD-future","min_time":"2019-03-15T09:51:28.851000000Z","max_time":"2020-08-04T17:29:47.509000000Z"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/catalog/market-liquidations?api_key=$TEST_API_KEY&limit=1",
        )
    }

    @Test
    fun `should return market liquidations filtered by base and quote`() {
        val expectedResponse =
            """{"data":[{"market":"binance-XBTUSD-future","min_time":"2019-03-15T09:51:28.851000000Z","max_time":"2020-08-04T17:29:47.509000000Z"},{"market":"bitmex-XBTUSD-future","min_time":"2019-03-15T09:51:28.851000000Z","max_time":"2020-08-04T17:29:47.509000000Z"},{"market":"huobi-XBTUSD4-future","min_time":"2019-03-15T10:36:31.852000000Z","max_time":"2019-03-15T10:36:31.852000000Z"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/catalog/market-liquidations?api_key=$TEST_API_KEY&base=btc&quote=usd",
        )
    }

    @Test
    fun `should return 400 filtering market liquidations by unknown base`() {
        assertResponse(
            400,
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'base'. Value 'wth' is not supported."}}""",
            "/v4/catalog/market-liquidations?api_key=$TEST_API_KEY&base=wth&quote=eth",
        )
    }

    @Test
    fun `should return empty list of market liquidations filtered by unsupported base`() {
        assertResponse(
            200,
            EMPTY_RESPONSE,
            "/v4/catalog/market-liquidations?api_key=$TEST_API_KEY&base=swth&quote=eth",
        )
    }

    @Test
    fun `should return market liquidations filtered by symbol`() {
        val expectedResponse =
            """{"data":[{"market":"binance-XBTUSD-future","min_time":"2019-03-15T09:51:28.851000000Z","max_time":"2020-08-04T17:29:47.509000000Z"},{"market":"bitmex-XBTUSD-future","min_time":"2019-03-15T09:51:28.851000000Z","max_time":"2020-08-04T17:29:47.509000000Z"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/catalog/market-liquidations?api_key=$TEST_API_KEY&symbol=XBTUSD",
        )
    }

    @Test
    fun `should return market liquidations filtered by exchange`() {
        val expectedResponse =
            """{"data":[{"market":"bitmex-FILUSD-future","min_time":"2023-09-17T10:36:40.247000000Z","max_time":"2023-09-17T10:36:40.247000000Z"},{"market":"bitmex-XBTUSD-future","min_time":"2019-03-15T09:51:28.851000000Z","max_time":"2020-08-04T17:29:47.509000000Z"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/catalog/market-liquidations?api_key=$TEST_API_KEY&exchange=bitmex",
        )
    }

    @Test
    fun `should return market liquidations in json_stream format`() {
        val expectedResponse =
            """{"market":"binance-XBTUSD-future","min_time":"2019-03-15T09:51:28.851000000Z","max_time":"2020-08-04T17:29:47.509000000Z"}
{"market":"bitmex-FILUSD-future","min_time":"2023-09-17T10:36:40.247000000Z","max_time":"2023-09-17T10:36:40.247000000Z"}
{"market":"bitmex-XBTUSD-future","min_time":"2019-03-15T09:51:28.851000000Z","max_time":"2020-08-04T17:29:47.509000000Z"}
{"market":"ftx-SOL-0925-future","min_time":"2019-03-15T10:36:46.428000000Z","max_time":"2019-03-15T10:36:46.428000000Z"}
{"market":"huobi-XBTUSD4-future","min_time":"2019-03-15T10:36:31.852000000Z","max_time":"2019-03-15T10:36:31.852000000Z"}"""
        assertResponseWithContentType(
            200,
            expectedResponse,
            "/v4/catalog/market-liquidations?api_key=$TEST_API_KEY&format=json_stream",
            contentType = "application/x-ndjson",
        )
    }

    @Test
    fun `should return market liquidations in json_stream format ignoring 'pretty' parameter`() {
        val expectedResponse =
            """{"market":"binance-XBTUSD-future","min_time":"2019-03-15T09:51:28.851000000Z","max_time":"2020-08-04T17:29:47.509000000Z"}
{"market":"bitmex-FILUSD-future","min_time":"2023-09-17T10:36:40.247000000Z","max_time":"2023-09-17T10:36:40.247000000Z"}
{"market":"bitmex-XBTUSD-future","min_time":"2019-03-15T09:51:28.851000000Z","max_time":"2020-08-04T17:29:47.509000000Z"}
{"market":"ftx-SOL-0925-future","min_time":"2019-03-15T10:36:46.428000000Z","max_time":"2019-03-15T10:36:46.428000000Z"}
{"market":"huobi-XBTUSD4-future","min_time":"2019-03-15T10:36:31.852000000Z","max_time":"2019-03-15T10:36:31.852000000Z"}"""
        assertResponseWithContentType(
            200,
            expectedResponse,
            "/v4/catalog/market-liquidations?api_key=$TEST_API_KEY&format=json_stream&pretty=true",
            contentType = "application/x-ndjson",
        )
    }

    @Test
    fun `should return 400 when format is invalid`() {
        val expectedResponse =
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'format'. Must be one of the following: json, json_stream."}}"""
        assertResponse(
            400,
            expectedResponse,
            "/v4/catalog/market-liquidations?api_key=$TEST_API_KEY&format=ldjson",
        )
    }

    @Test
    fun `should return 400 when limit is negative`() {
        val expectedResponse =
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'limit'. Must be positive number or `none`."}}"""
        assertResponse(
            400,
            expectedResponse,
            "/v4/catalog/market-liquidations?api_key=$TEST_API_KEY&limit=-10",
        )
    }

    @Test
    fun `should return 400 when limit is invalid`() {
        val expectedResponse =
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'limit'. Must be positive number or `none`."}}"""
        assertResponse(
            400,
            expectedResponse,
            "/v4/catalog/market-liquidations?api_key=$TEST_API_KEY&limit=something",
        )
    }

    @Test
    fun `should return future market liquidations`() {
        val expectedResponse =
            """{"data":[{"market":"binance-XBTUSD-future","min_time":"2019-03-15T09:51:28.851000000Z","max_time":"2020-08-04T17:29:47.509000000Z"},{"market":"bitmex-FILUSD-future","min_time":"2023-09-17T10:36:40.247000000Z","max_time":"2023-09-17T10:36:40.247000000Z"},{"market":"bitmex-XBTUSD-future","min_time":"2019-03-15T09:51:28.851000000Z","max_time":"2020-08-04T17:29:47.509000000Z"},{"market":"ftx-SOL-0925-future","min_time":"2019-03-15T10:36:46.428000000Z","max_time":"2019-03-15T10:36:46.428000000Z"},{"market":"huobi-XBTUSD4-future","min_time":"2019-03-15T10:36:31.852000000Z","max_time":"2019-03-15T10:36:31.852000000Z"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/catalog/market-liquidations?api_key=$TEST_API_KEY&type=future",
        )
    }

    @Test
    fun `should return 400 when type is invalid`() {
        assertResponse(
            400,
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'type'. Value 'non-supported' is not supported. Supported values are 'spot', 'future', 'option'."}}""",
            "/v4/catalog/market-liquidations?api_key=$TEST_API_KEY&type=non-supported",
        )
    }

    @Test
    fun `should return 400 when both markets and type specified`() {
        val expectedResponse =
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'markets'. Cannot combine filtering by 'markets' with other filtering options."}}"""
        assertResponse(
            400,
            expectedResponse,
            "/v4/catalog/market-liquidations?api_key=$TEST_API_KEY&markets=bittrex-btc-usd-spot&type=future",
        )
    }

    @Test
    fun `should return 400 when asset specified and type is future`() {
        val expectedResponse =
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'type'. 'type' should be 'spot' when filtering by 'asset'."}}"""
        assertResponse(
            400,
            expectedResponse,
            "/v4/catalog/market-liquidations?api_key=$TEST_API_KEY&asset=btc&type=future",
        )
    }

    @Test
    fun `should return 400 when both asset and quote specified`() {
        val expectedResponse =
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'asset'. Cannot combine filtering by 'asset' with other filtering options."}}"""
        assertResponse(
            400,
            expectedResponse,
            "/v4/catalog/market-liquidations?api_key=$TEST_API_KEY&asset=btc&quote=eth",
        )
    }

    @Test
    fun `should return 400 when both symbol and quote specified`() {
        val expectedResponse =
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'symbol'. Cannot combine filtering by 'symbol' with other filtering options."}}"""
        assertResponse(
            400,
            expectedResponse,
            "/v4/catalog/market-liquidations?api_key=$TEST_API_KEY&symbol=btc&quote=eth",
        )
    }

    @Test
    fun `should return 400 when symbol specified and type is spot`() {
        val expectedResponse =
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'type'. 'type' should not be 'spot' when filtering by 'symbol'."}}"""
        assertResponse(
            400,
            expectedResponse,
            "/v4/catalog/market-liquidations?api_key=$TEST_API_KEY&symbol=btc&type=spot",
        )
    }

    @Test
    fun `should return all future market liquidations by base=btc`() {
        val expectedResponse =
            """{"data":[{"market":"binance-XBTUSD-future","min_time":"2019-03-15T09:51:28.851000000Z","max_time":"2020-08-04T17:29:47.509000000Z"},{"market":"bitmex-XBTUSD-future","min_time":"2019-03-15T09:51:28.851000000Z","max_time":"2020-08-04T17:29:47.509000000Z"},{"market":"huobi-XBTUSD4-future","min_time":"2019-03-15T10:36:31.852000000Z","max_time":"2019-03-15T10:36:31.852000000Z"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/catalog/market-liquidations?api_key=$TEST_API_KEY&base=btc&type=future",
        )
    }

    @Test
    fun `should return market liquidations in pretty format`() {
        val expectedResponse =
            """{
  "data" : [ {
  "market" : "binance-XBTUSD-future",
  "min_time" : "2019-03-15T09:51:28.851000000Z",
  "max_time" : "2020-08-04T17:29:47.509000000Z"
}, {
  "market" : "bitmex-FILUSD-future",
  "min_time" : "2023-09-17T10:36:40.247000000Z",
  "max_time" : "2023-09-17T10:36:40.247000000Z"
}, {
  "market" : "bitmex-XBTUSD-future",
  "min_time" : "2019-03-15T09:51:28.851000000Z",
  "max_time" : "2020-08-04T17:29:47.509000000Z"
}, {
  "market" : "ftx-SOL-0925-future",
  "min_time" : "2019-03-15T10:36:46.428000000Z",
  "max_time" : "2019-03-15T10:36:46.428000000Z"
}, {
  "market" : "huobi-XBTUSD4-future",
  "min_time" : "2019-03-15T10:36:31.852000000Z",
  "max_time" : "2019-03-15T10:36:31.852000000Z"
} ]
}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/catalog/market-liquidations?api_key=$TEST_API_KEY&pretty=true",
        )
    }
}
