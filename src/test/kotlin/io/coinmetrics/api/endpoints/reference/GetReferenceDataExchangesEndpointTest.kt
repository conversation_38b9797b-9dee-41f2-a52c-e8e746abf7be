package io.coinmetrics.api.endpoints.reference

import io.coinmetrics.api.helper.BaseTest
import io.coinmetrics.api.helper.TEST_API_KEY
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class GetReferenceDataExchangesEndpointTest : BaseTest() {
    @Test
    fun `should return 400 when invalid format specified`() {
        getResponse("/v4/reference-data/exchanges?api_key=$TEST_API_KEY&format=invalid_format").assertResponse()
    }

    @Test
    fun `should return 400 when invalid next_page_token specified`() {
        getResponse("/v4/reference-data/exchanges?api_key=$TEST_API_KEY&next_page_token=invalid%3Dformat").assertResponse()
    }

    @Test
    fun `should return 400 when unsupported exchanges requested`() {
        getResponse("/v4/reference-data/exchanges?exchanges=mtgox&api_key=$TEST_API_KEY&").assertResponse()
    }

    @Test
    fun `should return the first page of exchanges reference data`() {
        getResponse("/v4/reference-data/exchanges?api_key=$TEST_API_KEY&page_size=3").assertResponse()
    }

    @Test
    fun `should return the second page of exchanges reference data`() {
        getResponse("/v4/reference-data/exchanges?api_key=$TEST_API_KEY&page_size=3&next_page_token=YmluYW5jZS51cw").assertResponse()
    }

    @Test
    fun `should return the first page of exchanges reference data filtered by exchanges`() {
        getResponse("/v4/reference-data/exchanges?api_key=$TEST_API_KEY&page_size=2&exchanges=cme,mexc,binance,bibox,bittrex").assertResponse()
    }

    @Test
    fun `should return the second page of exchanges reference data filtered by exchanges`() {
        getResponse("/v4/reference-data/exchanges?api_key=$TEST_API_KEY&page_size=2&exchanges=cme,mexc,binance,bibox,bittrex&next_page_token=Yml0dHJleA").assertResponse()
    }

    @Test
    fun `should return the last page of exchanges reference data filtered by exchanges`() {
        getResponse("/v4/reference-data/exchanges?api_key=$TEST_API_KEY&page_size=2&exchanges=cme,mexc,binance,bibox,bittrex&next_page_token=bWV4Yw").assertResponse()
    }

    @Test
    fun `should return the first page of exchanges reference data filtered by exchanges paging from end`() {
        getResponse("/v4/reference-data/exchanges?api_key=$TEST_API_KEY&page_size=2&exchanges=cme,mexc,binance,bibox,bittrex&paging_from=end").assertResponse()
    }

    @Test
    fun `should return the second page of exchanges reference data filtered by exchanges paging from end`() {
        getResponse("/v4/reference-data/exchanges?api_key=$TEST_API_KEY&page_size=2&exchanges=cme,mexc,binance,bibox,bittrex&paging_from=end&next_page_token=Yml0dHJleA").assertResponse()
    }

    @Test
    fun `should return the last page of exchanges reference data filtered by exchanges paging from end`() {
        getResponse("/v4/reference-data/exchanges?api_key=$TEST_API_KEY&page_size=2&exchanges=cme,mexc,binance,bibox,bittrex&paging_from=end&next_page_token=Ymlib3g").assertResponse()
    }
}
