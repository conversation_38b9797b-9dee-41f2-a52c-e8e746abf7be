package io.coinmetrics.api.endpoints.reference

import io.coinmetrics.api.helper.BaseTest
import io.coinmetrics.api.helper.TEST_API_KEY
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class GetReferenceDataAssetsEndpointTest : BaseTest() {
    @Test
    fun `should return 400 when invalid format specified`() {
        assertResponse(
            expectedCode = 400,
            expectedResponse = """{"error":{"type":"bad_parameter","message":"Bad parameter 'format'. Must be one of the following: json, json_stream."}}""",
            pathAndQuery = "/v4/reference-data/assets?api_key=$TEST_API_KEY&format=invalid_format",
        )
    }

    @Test
    fun `should return 400 when invalid next_page_token specified`() {
        assertResponse(
            expectedCode = 400,
            expectedResponse = """{"error":{"type":"bad_parameter","message":"Bad parameter 'next_page_token'."}}""",
            pathAndQuery = "/v4/reference-data/assets?api_key=$TEST_API_KEY&next_page_token=invalid%3Dformat",
        )
    }

    @Test
    fun `should return 400 when unsupported assets requested`() {
        assertResponse(
            expectedCode = 400,
            expectedResponse = """{"error":{"type":"bad_parameter","message":"Bad parameter 'assets'. All requested assets are not supported."}}""",
            pathAndQuery = "/v4/reference-data/assets?assets=9inch&api_key=$TEST_API_KEY&",
        )
    }

    @Test
    fun `should return the first page of assets reference data`() {
        assertResponse(
            expectedCode = 200,
            expectedResponse = """{"data":[{"asset":"00","full_name":"00 Token"},{"asset":"0x0","full_name":"0x0.ai: AI Smart Contract"},{"asset":"1000sats","full_name":"1000SATS (Ordinals)"}],"next_page_token":"MTAweA","next_page_url":"http://127.0.0.1:8080/v4/reference-data/assets?api_key=x1&page_size=3&next_page_token=MTAweA"}""",
            pathAndQuery = "/v4/reference-data/assets?api_key=$TEST_API_KEY&page_size=3",
        )
    }

    @Test
    fun `should return the second page of assets reference data`() {
        assertResponse(
            expectedCode = 200,
            expectedResponse = """{"data":[{"asset":"18c","full_name":"Block 18"},{"asset":"1art","full_name":"ArtWallet"},{"asset":"1box","full_name":"1BOX"}],"next_page_token":"MWNhdA","next_page_url":"http://127.0.0.1:8080/v4/reference-data/assets?api_key=x1&page_size=3&next_page_token=MWNhdA"}""",
            pathAndQuery = "/v4/reference-data/assets?api_key=$TEST_API_KEY&page_size=3&next_page_token=MThj",
        )
    }

    @Test
    fun `should return the first page of assets reference data filtered by assets`() {
        getResponse("/v4/reference-data/assets?api_key=$TEST_API_KEY&page_size=2&assets=btc,eth,etc,1inch,ada").assertResponse()
    }

    @Test
    fun `should return the second page of assets reference data filtered by assets`() {
        assertResponse(
            expectedCode = 200,
            expectedResponse = """{"data":[{"asset":"btc","full_name":"Bitcoin"},{"asset":"etc","full_name":"Ethereum Classic"}],"next_page_token":"ZXRo","next_page_url":"http://127.0.0.1:8080/v4/reference-data/assets?api_key=x1&page_size=2&assets=btc,eth,etc,1inch,ada&next_page_token=ZXRo"}""",
            pathAndQuery = "/v4/reference-data/assets?api_key=$TEST_API_KEY&page_size=2&assets=btc,eth,etc,1inch,ada&next_page_token=YnRj",
        )
    }

    @Test
    fun `should return the last page of assets reference data filtered by assets`() {
        assertResponse(
            expectedCode = 200,
            expectedResponse = """{"data":[{"asset":"eth","full_name":"Ethereum"}]}""",
            pathAndQuery = "/v4/reference-data/assets?api_key=$TEST_API_KEY&page_size=2&assets=btc,eth,etc,1inch,ada&next_page_token=ZXRo",
        )
    }

    @Test
    fun `should return the first page of assets reference data filtered by assets paging from end`() {
        assertResponse(
            expectedCode = 200,
            expectedResponse = """{"data":[{"asset":"etc","full_name":"Ethereum Classic"},{"asset":"eth","full_name":"Ethereum"}],"next_page_token":"YnRj","next_page_url":"http://127.0.0.1:8080/v4/reference-data/assets?api_key=x1&page_size=2&assets=btc,eth,etc,1inch,ada&paging_from=end&next_page_token=YnRj"}""",
            pathAndQuery = "/v4/reference-data/assets?api_key=$TEST_API_KEY&page_size=2&assets=btc,eth,etc,1inch,ada&paging_from=end",
        )
    }

    @Test
    fun `should return the second page of assets reference data filtered by assets paging from end`() {
        assertResponse(
            expectedCode = 200,
            expectedResponse = """{"data":[{"asset":"ada","full_name":"Cardano"},{"asset":"btc","full_name":"Bitcoin"}],"next_page_token":"MWluY2g","next_page_url":"http://127.0.0.1:8080/v4/reference-data/assets?api_key=x1&page_size=2&assets=btc,eth,etc,1inch,ada&paging_from=end&next_page_token=MWluY2g"}""",
            pathAndQuery = "/v4/reference-data/assets?api_key=$TEST_API_KEY&page_size=2&assets=btc,eth,etc,1inch,ada&paging_from=end&next_page_token=YnRj",
        )
    }

    @Test
    fun `should return the last page of assets reference data filtered by assets paging from end`() {
        assertResponse(
            expectedCode = 200,
            expectedResponse = """{"data":[{"asset":"1inch","full_name":"1inch"}]}""",
            pathAndQuery = "/v4/reference-data/assets?api_key=$TEST_API_KEY&page_size=2&assets=btc,eth,etc,1inch,ada&paging_from=end&next_page_token=MWluY2g",
        )
    }

    @Test
    fun `should return caching headers`() {
        assertStatusCodeAndHeaders(
            pathAndQuery = "/v4/reference-data/assets?api_key=$TEST_API_KEY&page_size=2&assets=btc,eth,etc,1inch,ada&paging_from=end&next_page_token=MWluY2g",
            expectedCode = 200,
            expectedHeaders =
                listOf(
                    "Cache-Control" to "no-cache",
                    "Expires" to "Thu, 01 Jan 1970 00:00:00 GMT",
                ),
        )
    }
}
