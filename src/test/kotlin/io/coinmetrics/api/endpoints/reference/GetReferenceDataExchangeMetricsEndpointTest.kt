package io.coinmetrics.api.endpoints.reference

import io.coinmetrics.api.helper.BaseTest
import io.coinmetrics.api.helper.TEST_API_KEY
import io.coinmetrics.testing.autoexpect.AutoExpect
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(AutoExpect::class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class GetReferenceDataExchangeMetricsEndpointTest : BaseTest() {
    @Test
    fun `should return exchange metric data when supported metric requested`() {
        getResponse("/v4/reference-data/exchange-metrics?metrics=open_interest_reported_future_usd&api_key=$TEST_API_KEY&").assertResponse()
    }

    @Test
    fun `should return 400 when unsupported metric requested`() {
        getResponse("/v4/reference-data/exchange-metrics?metrics=liquidity_depth_0_1_percent_bid_volume_usd&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should return all exchange metrics in json_stream format`() {
        getResponse("/v4/reference-data/exchange-metrics?format=json_stream&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should return all exchange metrics in json_stream format ignoring page_size and paging_from`() {
        getResponse("/v4/reference-data/exchange-metrics?format=json_stream&api_key=$TEST_API_KEY&page_size=1&paging_from=end").assertResponse()
    }
}
