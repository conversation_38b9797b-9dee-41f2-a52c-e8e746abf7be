package io.coinmetrics.api.endpoints.reference

import io.coinmetrics.api.helper.BaseTest
import io.coinmetrics.api.helper.TEST_API_KEY
import io.coinmetrics.testing.autoexpect.AutoExpect
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(AutoExpect::class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class GetReferenceDataAssetMetricsEndpointTest : BaseTest() {
    @Test
    fun `should return 400 when invalid format specified`() {
        getResponse("/v4/reference-data/asset-metrics?api_key=$TEST_API_KEY&format=invalid_format").assertResponse()
    }

    @Test
    fun `should return 400 when invalid next_page_token specified`() {
        getResponse("/v4/reference-data/asset-metrics?api_key=$TEST_API_KEY&next_page_token=invalid%3Dformat").assertResponse()
    }

    @Test
    fun `should return 400 when specified metric is not asset metric`() {
        getResponse("/v4/reference-data/asset-metrics?metrics=AdrAccCnt&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should return all asset metrics reference data`() {
        getResponse("/v4/reference-data/asset-metrics?api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should return all asset metrics reference data paging from end`() {
        getResponse("/v4/reference-data/asset-metrics?api_key=$TEST_API_KEY&paging_from=end").assertResponse()
    }

    @Test
    fun `should return all asset metrics reference data filtered by metrics`() {
        getResponse("/v4/reference-data/asset-metrics?api_key=$TEST_API_KEY&metrics=volume_trusted_spot_usd_1h,AdrActCnt").assertResponse()
    }

    @Test
    fun `should return all REVIEWABLE asset metrics reference data`() {
        getResponse("/v4/reference-data/asset-metrics?api_key=$TEST_API_KEY&reviewable=true").assertResponse()
    }

    @Test
    fun `should return all NON-REVIEWABLE asset metrics reference data`() {
        getResponse("/v4/reference-data/asset-metrics?api_key=$TEST_API_KEY&reviewable=false").assertResponse()
    }

    @Test
    fun `should return the first page of asset metrics reference data`() {
        getResponse("/v4/reference-data/asset-metrics?api_key=$TEST_API_KEY&page_size=30").assertResponse()
    }

    @Test
    fun `should return the second page of asset metrics reference data`() {
        getResponse("/v4/reference-data/asset-metrics?api_key=$TEST_API_KEY&page_size=30&next_page_token=Rmxvd091dEV4SW5jbFVTRA").assertResponse()
    }

    @Test
    fun `should return the last page of asset metrics reference data`() {
        getResponse("/v4/reference-data/asset-metrics?api_key=$TEST_API_KEY&page_size=30&next_page_token=UmVmZXJlbmNlUmF0ZUVVUg").assertResponse()
    }

    @Test
    fun `should return the first page of asset metrics reference data paging from end`() {
        getResponse("/v4/reference-data/asset-metrics?api_key=$TEST_API_KEY&page_size=30&paging_from=end").assertResponse()
    }

    @Test
    fun `should return the second page of asset metrics reference data data paging from end`() {
        getResponse("/v4/reference-data/asset-metrics?api_key=$TEST_API_KEY&page_size=30&paging_from=end&next_page_token=Rmxvd1Rmck91dEtSS0NudA").assertResponse()
    }

    @Test
    fun `should return the last page of asset metrics reference data data paging from end`() {
        getResponse("/v4/reference-data/asset-metrics?api_key=$TEST_API_KEY&page_size=30&paging_from=end&next_page_token=Rmxvd091dEJOQk50dg").assertResponse()
    }

    @Test
    fun `should return the first page of REVIEWABLE asset metrics reference data`() {
        getResponse("/v4/reference-data/asset-metrics?api_key=$TEST_API_KEY&reviewable=true&page_size=20").assertResponse()
    }

    @Test
    fun `should return the second page of REVIEWABLE asset metrics reference data`() {
        getResponse("/v4/reference-data/asset-metrics?api_key=$TEST_API_KEY&reviewable=true&page_size=20&next_page_token=Rmxvd091dEJOQk50dg").assertResponse()
    }

    @Test
    fun `should return the last page of REVIEWABLE asset metrics reference data`() {
        getResponse("/v4/reference-data/asset-metrics?api_key=$TEST_API_KEY&reviewable=true&page_size=20&next_page_token=Rmxvd1RmckluQlNQQ250").assertResponse()
    }

    @Test
    fun `should return all asset metrics reference data in json_stream format`() {
        getResponse("/v4/reference-data/asset-metrics?api_key=$TEST_API_KEY&format=json_stream").assertResponse()
    }

    @Test
    fun `should return all asset metrics reference data in json_stream format ignoring page_size and paging_from`() {
        getResponse("/v4/reference-data/asset-metrics?api_key=$TEST_API_KEY&format=json_stream&page_size=2&paging_from=end").assertResponse()
    }
}
