package io.coinmetrics.api.endpoints

import com.fasterxml.jackson.module.kotlin.readValue
import io.coinmetrics.api.helper.BaseTest
import io.coinmetrics.api.helper.TEST_API_KEY
import io.coinmetrics.api.helper.TemporalTestEnv
import io.coinmetrics.api.helper.TestResponse
import io.coinmetrics.api.models.CreatedJobDetailsResponse
import io.coinmetrics.api.models.JobDetails
import io.coinmetrics.api.models.JobStatus
import io.coinmetrics.api.models.JobsDetailsResponse
import io.coinmetrics.api.modules.main.MainApiConfig
import io.coinmetrics.api.modules.main.MainApiModule
import io.coinmetrics.api.modules.main.TemporalConfig
import io.coinmetrics.api.service.job.JobService
import io.coinmetrics.api.service.job.TemporalClients
import io.coinmetrics.api.utils.awaitUntilAsserted
import io.coinmetrics.jobs.networkdata.ApiNdJobs
import io.prometheus.metrics.model.registry.PrometheusRegistry
import io.temporal.worker.WorkerFactory
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.AfterAll
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotEquals
import org.junit.jupiter.api.BeforeAll
import java.time.Duration
import java.time.Instant
import java.util.UUID
import java.util.concurrent.Callable
import java.util.concurrent.Executors
import java.util.concurrent.ThreadLocalRandom

open class BaseJobTest : BaseTest() {
    private var workerFactory: WorkerFactory? = null
    protected lateinit var jobService: JobService

    override fun mainApiConfig(): MainApiConfig =
        super.mainApiConfig().copy(
            temporalConfig =
                TemporalConfig(
                    serviceTarget = TemporalTestEnv.instance.value.serviceTarget,
                    namespace = namespace,
                    expirationPeriod = Duration.ofMinutes(5),
                    threadCount = 16,
                ),
        )

    /**
     * Each test should have its own namespace.
     * Namespaces list is located at resources/temporal/create-namespaces.sh file.
     */
    protected open val namespace = this::class.simpleName!!

    protected open fun workflowImplementationTypes(): List<Class<*>> = emptyList()

    protected open fun activityImplementations(): List<Any> = emptyList()

    @BeforeAll
    fun setUp() {
        TemporalTestEnv.instance.value.createNamespace(namespace)

        val mainApiModule =
            server.modules
                .filterIsInstance<MainApiModule>()
                .first()

        val workflowImplementationTypes = workflowImplementationTypes()
        if (workflowImplementationTypes.isNotEmpty()) {
            val (client) = TemporalClients.createClientAndService(mainApiModule.config.temporalConfig!!, PrometheusRegistry())

            val workerFactory = WorkerFactory.newInstance(client)
            val worker = workerFactory.newWorker(ApiNdJobs.getTaskQueue(TEST_API_KEY))

            log.info("Registering workflow impls: ${workflowImplementationTypes.map { it.simpleName }}")
            worker.registerWorkflowImplementationTypes(*workflowImplementationTypes.toTypedArray())

            val activityImplementations = activityImplementations()
            if (activityImplementations.isNotEmpty()) {
                log.info("Registering activity impls: ${activityImplementations.map { it.javaClass.simpleName }}")
                worker.registerActivitiesImplementations(*activityImplementations.toTypedArray())
            }

            this.workerFactory = workerFactory
            workerFactory.start()
        }

        jobService = mainApiModule.jobService!!
    }

    @AfterAll
    fun shutDown() {
        workerFactory?.shutdownNow()
    }

    protected fun getJobById(
        apiKey: String,
        jobId: String,
        receivedTime: Instant? = null,
    ): JobDetails {
        val getJobByIdResponse =
            getResponse(
                "/v4/jobs?ids=$jobId&api_key=$apiKey",
                *(receivedTime?.let { arrayOf("X-Test-Received-Timestamp", it.toString()) } ?: arrayOf()),
            )
        getJobByIdResponse.assertStatusCodeAndHeaders(expectedCode = 200)
        val jobsDetailsResponse = getJobByIdResponse.body.toJobsDetailsResponse()
        assertEquals(1, jobsDetailsResponse.data.size)
        val jobDetails = jobsDetailsResponse.data.first()
        assertEquals(jobId, jobDetails.id)
        return jobDetails
    }

    protected fun String.toCreatedJobDetailsResponse(): CreatedJobDetailsResponse = objectMapper.readValue<CreatedJobDetailsResponse>(this)

    protected fun generateRandomString(): String = UUID.randomUUID().toString()

    private fun String.toJobsDetailsResponse(): JobsDetailsResponse = objectMapper.readValue<JobsDetailsResponse>(this)

    protected fun runDeduplicationTest(executeRequest: () -> TestResponse) {
        // should return the same result many times
        val parallelism = 16
        Executors.newFixedThreadPool(parallelism).use { executor ->
            var prevResponse: CreatedJobDetailsResponse? = null
            repeat(2) {
                val responses =
                    List(parallelism) {
                        executor.submit(
                            Callable<String> {
                                Thread.sleep(Duration.ofMillis(ThreadLocalRandom.current().nextLong(10)))
                                executeRequest()
                                    .also {
                                        assertEquals(200, it.status)
                                    }.body
                            },
                        )
                    }.map {
                        it.get()!!
                    }
                assertEquals(1, responses.toSet().size)
                val response = responses.first().toCreatedJobDetailsResponse()
                if (prevResponse != null) {
                    assertNotEquals(prevResponse, response)
                }
                prevResponse = response

                // Terminate the job.
                runBlocking {
                    jobService.terminateJob(response.jobId)
                    awaitUntilAsserted {
                        assertEquals(JobStatus.FAILED, getJobById(TEST_API_KEY, response.jobId).status)
                    }
                }
            }
        }
    }
}
