package io.coinmetrics.api.endpoints.taxonomy

import io.coinmetrics.api.helper.EMPTY_RESPONSE
import io.coinmetrics.api.helper.INVALID_NEXT_PAGE_TOKEN_RESPONSE
import io.coinmetrics.api.helper.TEST_API_KEY
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
internal class GetTaxonomyAssetsEndpointTest : BaseTaxonomyApiTest() {
    @Test
    fun `should return 400 when invalid version specified`() {
        whenRequesting(path = "/v4/taxonomy/assets?version=invalid&api_key=$TEST_API_KEY").expect(
            statusCode = 400,
            payload = """{"error":{"type":"bad_parameter","message":"Bad parameter 'version'. Invalid format of taxonomy version specified."}}""",
        )
    }

    @Test
    fun `should return 400 when start_time is invalid`() {
        whenRequesting(path = "/v4/taxonomy/assets?classification_start_time=invalid&api_key=$TEST_API_KEY").expect(
            statusCode = 400,
            payload = """{"error":{"type":"bad_parameter","message":"Bad parameter 'start_time'. datetime string 'invalid' has incorrect format."}}""",
        )
    }

    @Test
    fun `should return 400 when end_time is invalid`() {
        whenRequesting(path = "/v4/taxonomy/assets?classification_end_time=invalid&api_key=$TEST_API_KEY").expect(
            statusCode = 400,
            payload = """{"error":{"type":"bad_parameter","message":"Bad parameter 'end_time'. datetime string 'invalid' has incorrect format."}}""",
        )
    }

    @Test
    fun `should return 400 when invalid next_page_token specified`() {
        whenRequesting(path = "/v4/taxonomy/assets?next_page_token=invalid&api_key=$TEST_API_KEY").expect(
            statusCode = 400,
            payload = INVALID_NEXT_PAGE_TOKEN_RESPONSE,
        )
    }

    /* ----------------------------- Test scenario 1: ETH -----------------------------------
        ETH was classified in version 1.0.
        Then re-classified in version 3.0.
        Then deleted in version 4.0.
     */

    @Test
    fun `should return eth for versions 1 & 2`() {
        // should be the same result because asset changed in version 3.0
        listOf("1.0", "2.0").forEach { version ->
            whenRequesting(path = "/v4/taxonomy/assets?assets=eth&version=$version&api_key=$TEST_API_KEY").expect(
                statusCode = 200,
                payload = """{"data":[{"asset":"eth","full_name":"Ethereum","taxonomy_version":"1.0","updated_at_taxonomy_version":"1.0","classification_start_time":"2022-11-03","class_id":"20","class":"Blockchain Infrastructure","sector_id":"2010","sector":"Smart Contract Platforms","subsector_id":"201010","subsector":"Smart Contract Platforms","classification_end_time":"2022-11-05"}]}""",
            )
        }
    }

    @Test
    fun `should return eth for version 3 & latest`() {
        // should return the same result because version 3.0 is the latest for ETH
        listOf("3.0", "latest").forEach { version ->
            whenRequesting(path = "/v4/taxonomy/assets?assets=eth&version=$version&api_key=$TEST_API_KEY").expect(
                statusCode = 200,
                payload = """{"data":[{"asset":"eth","full_name":"Ethereum","taxonomy_version":"3.0","updated_at_taxonomy_version":"3.0","classification_start_time":"2022-11-05","class_id":"20","class":"Blockchain Infrastructure","sector_id":"2020","sector":"Blockchain Utilities","subsector_id":"202010","subsector":"Network Scaling","classification_end_time":"2022-11-06"}]}""",
            )
        }
    }

    @Test
    fun `should return all versions of eth`() {
        whenRequesting(path = "/v4/taxonomy/assets?assets=eth&version=*&api_key=$TEST_API_KEY").expect(
            statusCode = 200,
            payload = """{"data":[{"asset":"eth","full_name":"Ethereum","taxonomy_version":"1.0","updated_at_taxonomy_version":"1.0","classification_start_time":"2022-11-03","class_id":"20","class":"Blockchain Infrastructure","sector_id":"2010","sector":"Smart Contract Platforms","subsector_id":"201010","subsector":"Smart Contract Platforms","classification_end_time":"2022-11-05"},{"asset":"eth","full_name":"Ethereum","taxonomy_version":"3.0","updated_at_taxonomy_version":"3.0","classification_start_time":"2022-11-05","class_id":"20","class":"Blockchain Infrastructure","sector_id":"2020","sector":"Blockchain Utilities","subsector_id":"202010","subsector":"Network Scaling","classification_end_time":"2022-11-06"}]}""",
        )
    }

    @Test
    fun `should return empty result for non-existed version`() {
        whenRequesting(path = "/v4/taxonomy/assets?assets=eth&version=5.0&api_key=$TEST_API_KEY").expect(
            statusCode = 200,
            payload = EMPTY_RESPONSE,
        )
    }

    /* ----------------------------- Test scenario 2: BTC -----------------------------------
        BTC was classified in version 1.0.
        Then re-classified in version 2.0.
     */

    @Test
    fun `should return btc for version 1`() {
        whenRequesting(path = "/v4/taxonomy/assets?assets=btc&version=1.0&api_key=$TEST_API_KEY").expect(
            statusCode = 200,
            payload = """{"data":[{"asset":"btc","full_name":"Bitcoin","taxonomy_version":"1.0","updated_at_taxonomy_version":"1.0","classification_start_time":"2022-11-03","class_id":"10","class":"Digital Currencies","sector_id":"1010","sector":"Value Transfer Coins","subsector_id":"101010","subsector":"Value Transfer Coins","classification_end_time":"2022-11-04"}]}""",
        )
    }

    @Test
    fun `should return btc for version 2-4 and latest`() {
        // should be the same result for the following versions because asset wasn't changed after version 2.0
        listOf("2.0", "3.0", "4.0", "latest").forEach { version ->
            whenRequesting(path = "/v4/taxonomy/assets?assets=btc&version=$version&api_key=$TEST_API_KEY").expect(
                statusCode = 200,
                payload = """{"data":[{"asset":"btc","full_name":"Bitcoin","taxonomy_version":"2.0","updated_at_taxonomy_version":"2.0","classification_start_time":"2022-11-04","class_id":"10","class":"Digital Currencies","sector_id":"1020","sector":"Specialized Coins","subsector_id":"102020","subsector":"Privacy Coins"}]}""",
            )
        }
    }

    @Test
    fun `should return all btc versions`() {
        whenRequesting(path = "/v4/taxonomy/assets?assets=btc&version=*&api_key=$TEST_API_KEY").expect(
            statusCode = 200,
            payload = """{"data":[{"asset":"btc","full_name":"Bitcoin","taxonomy_version":"1.0","updated_at_taxonomy_version":"1.0","classification_start_time":"2022-11-03","class_id":"10","class":"Digital Currencies","sector_id":"1010","sector":"Value Transfer Coins","subsector_id":"101010","subsector":"Value Transfer Coins","classification_end_time":"2022-11-04"},{"asset":"btc","full_name":"Bitcoin","taxonomy_version":"2.0","updated_at_taxonomy_version":"2.0","classification_start_time":"2022-11-04","class_id":"10","class":"Digital Currencies","sector_id":"1020","sector":"Specialized Coins","subsector_id":"102020","subsector":"Privacy Coins"}]}""",
        )
    }

    /* ----------------------------- Test scenario 3: DOGE -----------------------------------
        DOGE was classified in version 2.0.
        Never deleted or re-classified.
     */

    @Test
    fun `should return empty result for version 1 for doge`() {
        whenRequesting(path = "/v4/taxonomy/assets?assets=doge&version=1.0&api_key=$TEST_API_KEY").expect(
            statusCode = 200,
            payload = EMPTY_RESPONSE,
        )
    }

    @Test
    fun `should return doge for versions 2-4, latest and all`() {
        // should be the same result for the following versions because asset wasn't changed after version 2.0
        listOf("2.0", "3.0", "4.0", "latest", "*").forEach { version ->
            whenRequesting(path = "/v4/taxonomy/assets?assets=doge&version=$version&api_key=$TEST_API_KEY").expect(
                statusCode = 200,
                payload = """{"data":[{"asset":"doge","full_name":"Dogecoin","taxonomy_version":"2.0","updated_at_taxonomy_version":"2.0","classification_start_time":"2022-11-04","class_id":"10","class":"Digital Currencies","sector_id":"1020","sector":"Specialized Coins","subsector_id":"102010","subsector":"Meme Coins"}]}""",
            )
        }
    }

    /* ----------------------------- Test scenario 4: WBTC -----------------------------------
        WBTC was classified in version 1.0.
        Then re-classified in version 2.0 (only subsector name changed, IDs remained the same).
     */

    @Test
    fun `should return wbtc for version 1`() {
        whenRequesting(path = "/v4/taxonomy/assets?assets=wbtc&version=1.0&api_key=$TEST_API_KEY").expect(
            statusCode = 200,
            payload = """{"data":[{"asset":"wbtc","full_name":"Wrapped Bitcoin","taxonomy_version":"1.0","updated_at_taxonomy_version":"1.0","classification_start_time":"2022-11-03","class_id":"40","class":"On-Chain Derivatives","sector_id":"4020","sector":"Tokenized Assets","subsector_id":"402010","subsector":"Asset-Backed Tokens","classification_end_time":"2022-11-04"}]}""",
        )
    }

    @Test
    fun `should return wbtc for versions 2-4 and latest`() {
        // should be the same result for the following versions because asset wasn't changed after version 2.0
        listOf("2.0", "3.0", "4.0", "latest").forEach { version ->
            whenRequesting(path = "/v4/taxonomy/assets?assets=wbtc&version=$version&api_key=$TEST_API_KEY").expect(
                statusCode = 200,
                payload = """{"data":[{"asset":"wbtc","full_name":"Wrapped Bitcoin","taxonomy_version":"2.0","updated_at_taxonomy_version":"2.0","classification_start_time":"2022-11-04","class_id":"40","class":"On-Chain Derivatives","sector_id":"4020","sector":"Tokenized Assets","subsector_id":"402010","subsector":"Asset-Backed Tokens 2"}]}""",
            )
        }
    }

    @Test
    fun `should return wbtc for all versions`() {
        whenRequesting(path = "/v4/taxonomy/assets?assets=wbtc&version=*&api_key=$TEST_API_KEY").expect(
            statusCode = 200,
            payload = """{"data":[{"asset":"wbtc","full_name":"Wrapped Bitcoin","taxonomy_version":"1.0","updated_at_taxonomy_version":"1.0","classification_start_time":"2022-11-03","class_id":"40","class":"On-Chain Derivatives","sector_id":"4020","sector":"Tokenized Assets","subsector_id":"402010","subsector":"Asset-Backed Tokens","classification_end_time":"2022-11-04"},{"asset":"wbtc","full_name":"Wrapped Bitcoin","taxonomy_version":"2.0","updated_at_taxonomy_version":"2.0","classification_start_time":"2022-11-04","class_id":"40","class":"On-Chain Derivatives","sector_id":"4020","sector":"Tokenized Assets","subsector_id":"402010","subsector":"Asset-Backed Tokens 2"}]}""",
        )
    }

    // ----------------------------- Filtering -----------------------------------

    @Test
    fun `should filter by version only`() {
        whenRequesting(path = "/v4/taxonomy/assets?version=4.0&api_key=$TEST_API_KEY").expect(
            statusCode = 200,
            payload = """{"data":[{"asset":"1inch","full_name":"1inch","taxonomy_version":"1.0","updated_at_taxonomy_version":"1.0","classification_start_time":"2022-11-03","class_id":"30","class":"Digital Asset Applications","sector_id":"3010","sector":"Decentralized Finance","subsector_id":"301010","subsector":"Decentralized Exchanges"},{"asset":"ada","full_name":"Cardano","taxonomy_version":"1.0","updated_at_taxonomy_version":"1.0","classification_start_time":"2022-11-03","class_id":"20","class":"Blockchain Infrastructure","sector_id":"2010","sector":"Smart Contract Platforms","subsector_id":"201010","subsector":"Smart Contract Platforms"},{"asset":"atom","full_name":"Cosmos","taxonomy_version":"1.0","updated_at_taxonomy_version":"1.0","classification_start_time":"2022-11-03","class_id":"20","class":"Blockchain Infrastructure","sector_id":"2020","sector":"Blockchain Utilities","subsector_id":"202030","subsector":"Blockchain Networks"},{"asset":"btc","full_name":"Bitcoin","taxonomy_version":"2.0","updated_at_taxonomy_version":"2.0","classification_start_time":"2022-11-04","class_id":"10","class":"Digital Currencies","sector_id":"1020","sector":"Specialized Coins","subsector_id":"102020","subsector":"Privacy Coins"},{"asset":"busd","full_name":"Binance USD","taxonomy_version":"1.0","updated_at_taxonomy_version":"1.0","classification_start_time":"2022-11-03","class_id":"40","class":"On-Chain Derivatives","sector_id":"4010","sector":"Stablecoins","subsector_id":"401010","subsector":"Fiat-backed Stablecoins"},{"asset":"dai","full_name":"Dai","taxonomy_version":"1.0","updated_at_taxonomy_version":"1.0","classification_start_time":"2022-11-03","class_id":"40","class":"On-Chain Derivatives","sector_id":"4010","sector":"Stablecoins","subsector_id":"401020","subsector":"Crypto-backed Stablecoins"},{"asset":"doge","full_name":"Dogecoin","taxonomy_version":"2.0","updated_at_taxonomy_version":"2.0","classification_start_time":"2022-11-04","class_id":"10","class":"Digital Currencies","sector_id":"1020","sector":"Specialized Coins","subsector_id":"102010","subsector":"Meme Coins"},{"asset":"enj","full_name":"Enjin Coin","taxonomy_version":"1.0","updated_at_taxonomy_version":"1.0","classification_start_time":"2022-11-03","class_id":"30","class":"Digital Asset Applications","sector_id":"3050","sector":"Metaverse","subsector_id":"305030","subsector":"NFT Ecosystems"},{"asset":"ens","full_name":"Ethereum Name Service","taxonomy_version":"1.0","updated_at_taxonomy_version":"1.0","classification_start_time":"2022-11-03","class_id":"20","class":"Blockchain Infrastructure","sector_id":"2030","sector":"Application Utilities","subsector_id":"203020","subsector":"Digital Identity"},{"asset":"eos","full_name":"EOS","taxonomy_version":"1.0","updated_at_taxonomy_version":"1.0","classification_start_time":"2022-11-03","class_id":"20","class":"Blockchain Infrastructure","sector_id":"2010","sector":"Smart Contract Platforms","subsector_id":"201010","subsector":"Smart Contract Platforms"},{"asset":"ern","full_name":"Ethernity","taxonomy_version":"1.0","updated_at_taxonomy_version":"1.0","classification_start_time":"2022-11-03","class_id":"30","class":"Digital Asset Applications","sector_id":"3050","sector":"Metaverse","subsector_id":"305030","subsector":"NFT Ecosystems"},{"asset":"eth","full_name":"Ethereum","taxonomy_version":"3.0","updated_at_taxonomy_version":"3.0","classification_start_time":"2022-11-05","class_id":"20","class":"Blockchain Infrastructure","sector_id":"2020","sector":"Blockchain Utilities","subsector_id":"202010","subsector":"Network Scaling","classification_end_time":"2022-11-06"},{"asset":"farm","full_name":"Harvest Finance","taxonomy_version":"1.0","updated_at_taxonomy_version":"1.0","classification_start_time":"2022-11-03","class_id":"30","class":"Digital Asset Applications","sector_id":"3010","sector":"Decentralized Finance","subsector_id":"301060","subsector":"Asset Management"},{"asset":"wbtc","full_name":"Wrapped Bitcoin","taxonomy_version":"2.0","updated_at_taxonomy_version":"2.0","classification_start_time":"2022-11-04","class_id":"40","class":"On-Chain Derivatives","sector_id":"4020","sector":"Tokenized Assets","subsector_id":"402010","subsector":"Asset-Backed Tokens 2"}]}""",
        )
    }

    @Test
    fun `should filter by class_id`() {
        whenRequesting(path = "/v4/taxonomy/assets?class_ids=10&api_key=$TEST_API_KEY").expect(
            statusCode = 200,
            payload = """{"data":[{"asset":"btc","full_name":"Bitcoin","taxonomy_version":"2.0","updated_at_taxonomy_version":"2.0","classification_start_time":"2022-11-04","class_id":"10","class":"Digital Currencies","sector_id":"1020","sector":"Specialized Coins","subsector_id":"102020","subsector":"Privacy Coins"},{"asset":"doge","full_name":"Dogecoin","taxonomy_version":"2.0","updated_at_taxonomy_version":"2.0","classification_start_time":"2022-11-04","class_id":"10","class":"Digital Currencies","sector_id":"1020","sector":"Specialized Coins","subsector_id":"102010","subsector":"Meme Coins"}]}""",
        )
    }

    @Test
    fun `should filter by class_ids=30`() {
        whenRequesting(path = "/v4/taxonomy/assets?class_ids=30&api_key=$TEST_API_KEY").expect(
            statusCode = 200,
            payload = """{"data":[{"asset":"1inch","full_name":"1inch","taxonomy_version":"1.0","updated_at_taxonomy_version":"1.0","classification_start_time":"2022-11-03","class_id":"30","class":"Digital Asset Applications","sector_id":"3010","sector":"Decentralized Finance","subsector_id":"301010","subsector":"Decentralized Exchanges"},{"asset":"alice","full_name":"My Neighbor Alice","taxonomy_version":"2.0","updated_at_taxonomy_version":"2.0","classification_start_time":"2022-11-04","class_id":"30","class":"Digital Asset Applications","sector_id":"3050","sector":"Metaverse","subsector_id":"305010","subsector":"Virtual Worlds","classification_end_time":"2022-11-05"},{"asset":"enj","full_name":"Enjin Coin","taxonomy_version":"1.0","updated_at_taxonomy_version":"1.0","classification_start_time":"2022-11-03","class_id":"30","class":"Digital Asset Applications","sector_id":"3050","sector":"Metaverse","subsector_id":"305030","subsector":"NFT Ecosystems"},{"asset":"ern","full_name":"Ethernity","taxonomy_version":"1.0","updated_at_taxonomy_version":"1.0","classification_start_time":"2022-11-03","class_id":"30","class":"Digital Asset Applications","sector_id":"3050","sector":"Metaverse","subsector_id":"305030","subsector":"NFT Ecosystems"},{"asset":"farm","full_name":"Harvest Finance","taxonomy_version":"1.0","updated_at_taxonomy_version":"1.0","classification_start_time":"2022-11-03","class_id":"30","class":"Digital Asset Applications","sector_id":"3010","sector":"Decentralized Finance","subsector_id":"301060","subsector":"Asset Management"}]}""",
        )
    }

    @Test
    fun `should filter by class_ids`() {
        whenRequesting(path = "/v4/taxonomy/assets?class_ids=10,40&api_key=$TEST_API_KEY").expect(
            statusCode = 200,
            payload = """{"data":[{"asset":"btc","full_name":"Bitcoin","taxonomy_version":"2.0","updated_at_taxonomy_version":"2.0","classification_start_time":"2022-11-04","class_id":"10","class":"Digital Currencies","sector_id":"1020","sector":"Specialized Coins","subsector_id":"102020","subsector":"Privacy Coins"},{"asset":"busd","full_name":"Binance USD","taxonomy_version":"1.0","updated_at_taxonomy_version":"1.0","classification_start_time":"2022-11-03","class_id":"40","class":"On-Chain Derivatives","sector_id":"4010","sector":"Stablecoins","subsector_id":"401010","subsector":"Fiat-backed Stablecoins"},{"asset":"dai","full_name":"Dai","taxonomy_version":"1.0","updated_at_taxonomy_version":"1.0","classification_start_time":"2022-11-03","class_id":"40","class":"On-Chain Derivatives","sector_id":"4010","sector":"Stablecoins","subsector_id":"401020","subsector":"Crypto-backed Stablecoins"},{"asset":"doge","full_name":"Dogecoin","taxonomy_version":"2.0","updated_at_taxonomy_version":"2.0","classification_start_time":"2022-11-04","class_id":"10","class":"Digital Currencies","sector_id":"1020","sector":"Specialized Coins","subsector_id":"102010","subsector":"Meme Coins"},{"asset":"wbtc","full_name":"Wrapped Bitcoin","taxonomy_version":"2.0","updated_at_taxonomy_version":"2.0","classification_start_time":"2022-11-04","class_id":"40","class":"On-Chain Derivatives","sector_id":"4020","sector":"Tokenized Assets","subsector_id":"402010","subsector":"Asset-Backed Tokens 2"}]}""",
        )
    }

    @Test
    fun `should filter by sector_id`() {
        whenRequesting(path = "/v4/taxonomy/assets?sector_ids=4010&api_key=$TEST_API_KEY").expect(
            statusCode = 200,
            payload = """{"data":[{"asset":"busd","full_name":"Binance USD","taxonomy_version":"1.0","updated_at_taxonomy_version":"1.0","classification_start_time":"2022-11-03","class_id":"40","class":"On-Chain Derivatives","sector_id":"4010","sector":"Stablecoins","subsector_id":"401010","subsector":"Fiat-backed Stablecoins"},{"asset":"dai","full_name":"Dai","taxonomy_version":"1.0","updated_at_taxonomy_version":"1.0","classification_start_time":"2022-11-03","class_id":"40","class":"On-Chain Derivatives","sector_id":"4010","sector":"Stablecoins","subsector_id":"401020","subsector":"Crypto-backed Stablecoins"}]}""",
        )
    }

    @Test
    fun `should filter by sector_ids`() {
        whenRequesting(path = "/v4/taxonomy/assets?sector_ids=4010,2030&api_key=$TEST_API_KEY").expect(
            statusCode = 200,
            payload = """{"data":[{"asset":"busd","full_name":"Binance USD","taxonomy_version":"1.0","updated_at_taxonomy_version":"1.0","classification_start_time":"2022-11-03","class_id":"40","class":"On-Chain Derivatives","sector_id":"4010","sector":"Stablecoins","subsector_id":"401010","subsector":"Fiat-backed Stablecoins"},{"asset":"dai","full_name":"Dai","taxonomy_version":"1.0","updated_at_taxonomy_version":"1.0","classification_start_time":"2022-11-03","class_id":"40","class":"On-Chain Derivatives","sector_id":"4010","sector":"Stablecoins","subsector_id":"401020","subsector":"Crypto-backed Stablecoins"},{"asset":"ens","full_name":"Ethereum Name Service","taxonomy_version":"1.0","updated_at_taxonomy_version":"1.0","classification_start_time":"2022-11-03","class_id":"20","class":"Blockchain Infrastructure","sector_id":"2030","sector":"Application Utilities","subsector_id":"203020","subsector":"Digital Identity"}]}""",
        )
    }

    @Test
    fun `should filter by subsector_id`() {
        whenRequesting(path = "/v4/taxonomy/assets?subsector_ids=305030&api_key=$TEST_API_KEY").expect(
            statusCode = 200,
            payload = """{"data":[{"asset":"enj","full_name":"Enjin Coin","taxonomy_version":"1.0","updated_at_taxonomy_version":"1.0","classification_start_time":"2022-11-03","class_id":"30","class":"Digital Asset Applications","sector_id":"3050","sector":"Metaverse","subsector_id":"305030","subsector":"NFT Ecosystems"},{"asset":"ern","full_name":"Ethernity","taxonomy_version":"1.0","updated_at_taxonomy_version":"1.0","classification_start_time":"2022-11-03","class_id":"30","class":"Digital Asset Applications","sector_id":"3050","sector":"Metaverse","subsector_id":"305030","subsector":"NFT Ecosystems"}]}""",
        )
    }

    @Test
    fun `should filter by subsector_ids`() {
        whenRequesting(path = "/v4/taxonomy/assets?subsector_ids=202010,301060,202030&api_key=$TEST_API_KEY").expect(
            statusCode = 200,
            payload = """{"data":[{"asset":"atom","full_name":"Cosmos","taxonomy_version":"1.0","updated_at_taxonomy_version":"1.0","classification_start_time":"2022-11-03","class_id":"20","class":"Blockchain Infrastructure","sector_id":"2020","sector":"Blockchain Utilities","subsector_id":"202030","subsector":"Blockchain Networks"},{"asset":"eth","full_name":"Ethereum","taxonomy_version":"3.0","updated_at_taxonomy_version":"3.0","classification_start_time":"2022-11-05","class_id":"20","class":"Blockchain Infrastructure","sector_id":"2020","sector":"Blockchain Utilities","subsector_id":"202010","subsector":"Network Scaling","classification_end_time":"2022-11-06"},{"asset":"farm","full_name":"Harvest Finance","taxonomy_version":"1.0","updated_at_taxonomy_version":"1.0","classification_start_time":"2022-11-03","class_id":"30","class":"Digital Asset Applications","sector_id":"3010","sector":"Decentralized Finance","subsector_id":"301060","subsector":"Asset Management"}]}""",
        )
    }

    @Test
    fun `should filter by classification_start_time`() {
        whenRequesting(path = "/v4/taxonomy/assets?classification_start_time=2022-11-04&api_key=$TEST_API_KEY").expect(
            statusCode = 200,
            payload = """{"data":[{"asset":"alice","full_name":"My Neighbor Alice","taxonomy_version":"2.0","updated_at_taxonomy_version":"2.0","classification_start_time":"2022-11-04","class_id":"30","class":"Digital Asset Applications","sector_id":"3050","sector":"Metaverse","subsector_id":"305010","subsector":"Virtual Worlds","classification_end_time":"2022-11-05"},{"asset":"btc","full_name":"Bitcoin","taxonomy_version":"2.0","updated_at_taxonomy_version":"2.0","classification_start_time":"2022-11-04","class_id":"10","class":"Digital Currencies","sector_id":"1020","sector":"Specialized Coins","subsector_id":"102020","subsector":"Privacy Coins"},{"asset":"doge","full_name":"Dogecoin","taxonomy_version":"2.0","updated_at_taxonomy_version":"2.0","classification_start_time":"2022-11-04","class_id":"10","class":"Digital Currencies","sector_id":"1020","sector":"Specialized Coins","subsector_id":"102010","subsector":"Meme Coins"},{"asset":"eth","full_name":"Ethereum","taxonomy_version":"3.0","updated_at_taxonomy_version":"3.0","classification_start_time":"2022-11-05","class_id":"20","class":"Blockchain Infrastructure","sector_id":"2020","sector":"Blockchain Utilities","subsector_id":"202010","subsector":"Network Scaling","classification_end_time":"2022-11-06"},{"asset":"wbtc","full_name":"Wrapped Bitcoin","taxonomy_version":"2.0","updated_at_taxonomy_version":"2.0","classification_start_time":"2022-11-04","class_id":"40","class":"On-Chain Derivatives","sector_id":"4020","sector":"Tokenized Assets","subsector_id":"402010","subsector":"Asset-Backed Tokens 2"}]}""",
        )
    }

    @Test
    fun `should filter by classification_end_time`() {
        whenRequesting(path = "/v4/taxonomy/assets?classification_end_time=2022-11-05&api_key=$TEST_API_KEY").expect(
            statusCode = 200,
            payload = """{"data":[{"asset":"alice","full_name":"My Neighbor Alice","taxonomy_version":"2.0","updated_at_taxonomy_version":"2.0","classification_start_time":"2022-11-04","class_id":"30","class":"Digital Asset Applications","sector_id":"3050","sector":"Metaverse","subsector_id":"305010","subsector":"Virtual Worlds","classification_end_time":"2022-11-05"},{"asset":"btc","full_name":"Bitcoin","taxonomy_version":"1.0","updated_at_taxonomy_version":"1.0","classification_start_time":"2022-11-03","class_id":"10","class":"Digital Currencies","sector_id":"1010","sector":"Value Transfer Coins","subsector_id":"101010","subsector":"Value Transfer Coins","classification_end_time":"2022-11-04"},{"asset":"eth","full_name":"Ethereum","taxonomy_version":"1.0","updated_at_taxonomy_version":"1.0","classification_start_time":"2022-11-03","class_id":"20","class":"Blockchain Infrastructure","sector_id":"2010","sector":"Smart Contract Platforms","subsector_id":"201010","subsector":"Smart Contract Platforms","classification_end_time":"2022-11-05"},{"asset":"wbtc","full_name":"Wrapped Bitcoin","taxonomy_version":"1.0","updated_at_taxonomy_version":"1.0","classification_start_time":"2022-11-03","class_id":"40","class":"On-Chain Derivatives","sector_id":"4020","sector":"Tokenized Assets","subsector_id":"402010","subsector":"Asset-Backed Tokens","classification_end_time":"2022-11-04"}]}""",
        )
    }

    @Test
    fun `should filter by classification_start_time and classification_end_time`() {
        whenRequesting(
            path = "/v4/taxonomy/assets?classification_start_time=2022-11-04&classification_end_time=2022-11-06&api_key=$TEST_API_KEY",
        ).expect(
            statusCode = 200,
            payload = """{"data":[{"asset":"alice","full_name":"My Neighbor Alice","taxonomy_version":"2.0","updated_at_taxonomy_version":"2.0","classification_start_time":"2022-11-04","class_id":"30","class":"Digital Asset Applications","sector_id":"3050","sector":"Metaverse","subsector_id":"305010","subsector":"Virtual Worlds","classification_end_time":"2022-11-05"},{"asset":"eth","full_name":"Ethereum","taxonomy_version":"3.0","updated_at_taxonomy_version":"3.0","classification_start_time":"2022-11-05","class_id":"20","class":"Blockchain Infrastructure","sector_id":"2020","sector":"Blockchain Utilities","subsector_id":"202010","subsector":"Network Scaling","classification_end_time":"2022-11-06"}]}""",
        )
    }

    // ----------------------------- Ordering & Pagination -----------------------------------

    @Test
    fun `should return 1st page filtering by class_id`() {
        whenRequesting(path = "/v4/taxonomy/assets?class_ids=20&page_size=2&api_key=$TEST_API_KEY").expect(
            statusCode = 200,
            payload = """{"data":[{"asset":"ada","full_name":"Cardano","taxonomy_version":"1.0","updated_at_taxonomy_version":"1.0","classification_start_time":"2022-11-03","class_id":"20","class":"Blockchain Infrastructure","sector_id":"2010","sector":"Smart Contract Platforms","subsector_id":"201010","subsector":"Smart Contract Platforms"},{"asset":"atom","full_name":"Cosmos","taxonomy_version":"1.0","updated_at_taxonomy_version":"1.0","classification_start_time":"2022-11-03","class_id":"20","class":"Blockchain Infrastructure","sector_id":"2020","sector":"Blockchain Utilities","subsector_id":"202030","subsector":"Blockchain Networks"}],"next_page_token":"ZW5zfDE2Njc0MzM2MDAwMDA","next_page_url":"http://127.0.0.1:8080/v4/taxonomy/assets?class_ids=20&page_size=2&api_key=x1&next_page_token=ZW5zfDE2Njc0MzM2MDAwMDA"}""",
        )
    }

    @Test
    fun `should return 2nd page filtering by class_id`() {
        whenRequesting(
            path = "/v4/taxonomy/assets?class_ids=20&page_size=2&next_page_token=ZW5zfDE2Njc0MzM2MDAwMDA&api_key=$TEST_API_KEY",
        ).expect(
            statusCode = 200,
            payload = """{"data":[{"asset":"ens","full_name":"Ethereum Name Service","taxonomy_version":"1.0","updated_at_taxonomy_version":"1.0","classification_start_time":"2022-11-03","class_id":"20","class":"Blockchain Infrastructure","sector_id":"2030","sector":"Application Utilities","subsector_id":"203020","subsector":"Digital Identity"},{"asset":"eos","full_name":"EOS","taxonomy_version":"1.0","updated_at_taxonomy_version":"1.0","classification_start_time":"2022-11-03","class_id":"20","class":"Blockchain Infrastructure","sector_id":"2010","sector":"Smart Contract Platforms","subsector_id":"201010","subsector":"Smart Contract Platforms"}],"next_page_token":"ZXRofDE2Njc2MDY0MDAwMDA","next_page_url":"http://127.0.0.1:8080/v4/taxonomy/assets?class_ids=20&page_size=2&next_page_token=ZXRofDE2Njc2MDY0MDAwMDA&api_key=x1"}""",
        )
    }

    @Test
    fun `should return 3rd page filtering by class_id`() {
        whenRequesting(
            path = "/v4/taxonomy/assets?class_ids=20&page_size=2&next_page_token=ZXRofDE2Njc2MDY0MDAwMDA&api_key=$TEST_API_KEY",
        ).expect(
            statusCode = 200,
            payload = """{"data":[{"asset":"eth","full_name":"Ethereum","taxonomy_version":"3.0","updated_at_taxonomy_version":"3.0","classification_start_time":"2022-11-05","class_id":"20","class":"Blockchain Infrastructure","sector_id":"2020","sector":"Blockchain Utilities","subsector_id":"202010","subsector":"Network Scaling","classification_end_time":"2022-11-06"}]}""",
        )
    }

    @Test
    fun `should return 1st page filtering by class_ids, all versions, paging from end`() {
        whenRequesting(path = "/v4/taxonomy/assets?class_ids=30&page_size=2&paging_from=end&api_key=$TEST_API_KEY").expect(
            statusCode = 200,
            payload = """{"data":[{"asset":"ern","full_name":"Ethernity","taxonomy_version":"1.0","updated_at_taxonomy_version":"1.0","classification_start_time":"2022-11-03","class_id":"30","class":"Digital Asset Applications","sector_id":"3050","sector":"Metaverse","subsector_id":"305030","subsector":"NFT Ecosystems"},{"asset":"farm","full_name":"Harvest Finance","taxonomy_version":"1.0","updated_at_taxonomy_version":"1.0","classification_start_time":"2022-11-03","class_id":"30","class":"Digital Asset Applications","sector_id":"3010","sector":"Decentralized Finance","subsector_id":"301060","subsector":"Asset Management"}],"next_page_token":"ZW5qfDE2Njc0MzM2MDAwMDA","next_page_url":"http://127.0.0.1:8080/v4/taxonomy/assets?class_ids=30&page_size=2&paging_from=end&api_key=x1&next_page_token=ZW5qfDE2Njc0MzM2MDAwMDA"}""",
        )
    }

    @Test
    fun `should return 2nd page filtering by class_ids, all versions, paging from end`() {
        whenRequesting(
            path = "/v4/taxonomy/assets?class_ids=30&page_size=2&paging_from=end&next_page_token=ZW5qfDE2Njc0MzM2MDAwMDA&api_key=$TEST_API_KEY",
        ).expect(
            statusCode = 200,
            payload = """{"data":[{"asset":"alice","full_name":"My Neighbor Alice","taxonomy_version":"2.0","updated_at_taxonomy_version":"2.0","classification_start_time":"2022-11-04","class_id":"30","class":"Digital Asset Applications","sector_id":"3050","sector":"Metaverse","subsector_id":"305010","subsector":"Virtual Worlds","classification_end_time":"2022-11-05"},{"asset":"enj","full_name":"Enjin Coin","taxonomy_version":"1.0","updated_at_taxonomy_version":"1.0","classification_start_time":"2022-11-03","class_id":"30","class":"Digital Asset Applications","sector_id":"3050","sector":"Metaverse","subsector_id":"305030","subsector":"NFT Ecosystems"}],"next_page_token":"MWluY2h8MTY2NzQzMzYwMDAwMA","next_page_url":"http://127.0.0.1:8080/v4/taxonomy/assets?class_ids=30&page_size=2&paging_from=end&next_page_token=MWluY2h8MTY2NzQzMzYwMDAwMA&api_key=x1"}""",
        )
    }

    @Test
    fun `should return 3rd page filtering by class_ids, all versions, paging from end`() {
        whenRequesting(
            path = "/v4/taxonomy/assets?class_ids=30&page_size=2&paging_from=end&next_page_token=MWluY2h8MTY2NzQzMzYwMDAwMA&api_key=$TEST_API_KEY",
        ).expect(
            statusCode = 200,
            payload = """{"data":[{"asset":"1inch","full_name":"1inch","taxonomy_version":"1.0","updated_at_taxonomy_version":"1.0","classification_start_time":"2022-11-03","class_id":"30","class":"Digital Asset Applications","sector_id":"3010","sector":"Decentralized Finance","subsector_id":"301010","subsector":"Decentralized Exchanges"}]}""",
        )
    }
}
