package io.coinmetrics.api.endpoints.blockchain

import io.coinmetrics.api.helper.BaseTest
import io.coinmetrics.api.helper.COMMUNITY_KEY
import io.coinmetrics.api.helper.EMPTY_RESPONSE
import io.coinmetrics.api.helper.TEST_API_KEY
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class BlockchainV2AccountsTest : BaseTest() {
    @Test
    fun `should return 403 when trying to access accounts with forbidden key`() {
        val expectedResponse =
            """{"error":{"type":"forbidden","message":"Requested resource is not available with supplied credentials."}}"""
        assertResponse(
            403,
            expectedResponse,
            "/v4/blockchain-v2/btc/accounts?api_key=x2",
        )
    }

    @Test
    fun `should return 400 trying to get accounts for non-existing asset`() {
        val expectedResponse =
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'asset'. Value 'btcxxx' is not supported."}}"""
        assertResponse(
            400,
            expectedResponse,
            "/v4/blockchain-v2/btcxxx/accounts?api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `should return 400 when asset name contains comma`() {
        val expectedResponse =
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'asset'. Value 'btc,eth' is not supported."}}"""
        assertResponse(
            400,
            expectedResponse,
            "/v4/blockchain-v2/btc,eth/accounts?api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `should return 400 trying to get accounts for non-existing in our db asset`() {
        val expectedResponse =
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'asset'. Value 'bch' is not supported."}}"""
        assertResponse(
            expectedCode = 400,
            expectedResponse = expectedResponse,
            pathAndQuery = "/v4/blockchain-v2/bch/accounts?api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `should return empty list of all accounts as ETH table exists but is empty`() {
        assertResponse(
            expectedCode = 200,
            expectedResponse = EMPTY_RESPONSE,
            pathAndQuery = "/v4/blockchain-v2/eth/accounts?api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `should return list of all accounts for btc asset`() {
        val expectedResponse =
            """{"data":[{"account":"**********************************","type":"UTXO","creation_height":"299087","creation_block_hash":"************00002a3db51bc2352319be56536fd0607df1d1d3ab2b8cf73893","creation_time":"2014-05-04T14:51:57.000000000Z","creation_chain_sequence_number":"****************","balance":"0.********","n_debits":"0","n_credits":"12","last_chain_sequence_number":"****************","last_credit_height":"312745"},{"account":"**********************************","type":"UTXO","creation_height":"462031","creation_block_hash":"************00000156101007ce892cec617a735267065923e6fafcf5a099fa","creation_time":"2017-04-15T17:49:40.000000000Z","creation_chain_sequence_number":"****************","balance":"0","n_debits":"9","n_credits":"9","last_chain_sequence_number":"****************","last_debit_height":"463017","last_credit_height":"463010"},{"account":"**********************************","type":"UTXO","creation_height":"470920","creation_block_hash":"************0000002c7505ef2272e0677fa53d68d633f8e076ed42dd3380e6","creation_time":"2017-06-12T06:50:36.000000000Z","creation_chain_sequence_number":"****************","balance":"0.0000583","n_debits":"3","n_credits":"4","last_chain_sequence_number":"****************","last_debit_height":"474496","last_credit_height":"478464"},{"account":"**********************************","type":"UTXO","creation_height":"480777","creation_block_hash":"************0000000e7d28b7c2a37e42278e5d34c8a64a05d227e3608feac8","creation_time":"2017-08-16T12:34:59.000000000Z","creation_chain_sequence_number":"****************","balance":"0","n_debits":"6","n_credits":"6","last_chain_sequence_number":"****************","last_debit_height":"513907","last_credit_height":"513892"},{"account":"**********************************","type":"UTXO","creation_height":"485861","creation_block_hash":"************00000033350448762f4d658a0e7afc6a2d21c833487c42819487","creation_time":"2017-09-18T06:31:16.000000000Z","creation_chain_sequence_number":"****************","balance":"0","n_debits":"10","n_credits":"10","last_chain_sequence_number":"****************","last_debit_height":"500530","last_credit_height":"500523"},{"account":"**********************************","type":"UTXO","creation_height":"525056","creation_block_hash":"************00000000e955552cb92c12035d22ca5389bdcd9352fa51bf47e2","creation_time":"2018-05-30T04:19:03.000000000Z","creation_chain_sequence_number":"****************","balance":"0","n_debits":"4","n_credits":"4","last_chain_sequence_number":"****************","last_debit_height":"529782","last_credit_height":"529778"},{"account":"**********************************","type":"UTXO","creation_height":"645045","creation_block_hash":"************00000000cda09abdd9c8847f6d9a4b218cab2d3401b87768c73b","creation_time":"2020-08-23T19:46:02.000000000Z","creation_chain_sequence_number":"****************","balance":"0","n_debits":"6","n_credits":"6","last_chain_sequence_number":"****************","last_debit_height":"649240","last_credit_height":"649073"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain-v2/btc/accounts?api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `should return list of accounts filtered by accounts list for btc asset`() {
        val expectedResponse =
            """{"data":[{"account":"**********************************","type":"UTXO","creation_height":"299087","creation_block_hash":"************00002a3db51bc2352319be56536fd0607df1d1d3ab2b8cf73893","creation_time":"2014-05-04T14:51:57.000000000Z","creation_chain_sequence_number":"****************","balance":"0.********","n_debits":"0","n_credits":"12","last_chain_sequence_number":"****************","last_credit_height":"312745"},{"account":"**********************************","type":"UTXO","creation_height":"462031","creation_block_hash":"************00000156101007ce892cec617a735267065923e6fafcf5a099fa","creation_time":"2017-04-15T17:49:40.000000000Z","creation_chain_sequence_number":"****************","balance":"0","n_debits":"9","n_credits":"9","last_chain_sequence_number":"****************","last_debit_height":"463017","last_credit_height":"463010"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain-v2/btc/accounts?api_key=$TEST_API_KEY&accounts=**********************************,**********************************",
        )
    }

    @Test
    fun `should return empty list of accounts when filtered by unknown accounts for btc asset`() {
        assertResponse(
            expectedCode = 200,
            expectedResponse = EMPTY_RESPONSE,
            pathAndQuery = "/v4/blockchain-v2/btc/accounts?api_key=$TEST_API_KEY&accounts=123,456",
        )
    }

    @Test
    fun `should return 400 when all time filters specified`() {
        val expectedResponse =
            """{"error":{"type":"bad_parameters","message":"Filters '*_time', '*_height' and '*_chain_sequence_number' are mutually exclusive and can't be specified in the same request."}}"""
        assertResponse(
            400,
            expectedResponse,
            "/v4/blockchain-v2/btc/accounts?api_key=$TEST_API_KEY&start_time=2006-01-20&start_height=462031&start_chain_sequence_number=****************",
        )
    }

    @Test
    fun `should return 400 when height and chain sequence number filters specified`() {
        val expectedResponse =
            """{"error":{"type":"bad_parameters","message":"Filters '*_height' and '*_chain_sequence_number' are mutually exclusive and can't be specified in the same request."}}"""
        assertResponse(
            400,
            expectedResponse,
            "/v4/blockchain-v2/btc/accounts?api_key=$TEST_API_KEY&start_height=462031&start_chain_sequence_number=****************",
        )
    }

    @Test
    fun `should return 400 when time and chain sequence number filters specified`() {
        val expectedResponse =
            """{"error":{"type":"bad_parameters","message":"Filters '*_time' and '*_chain_sequence_number' are mutually exclusive and can't be specified in the same request."}}"""
        assertResponse(
            400,
            expectedResponse,
            "/v4/blockchain-v2/btc/accounts?api_key=$TEST_API_KEY&start_time=2006-01-20&start_chain_sequence_number=****************",
        )
    }

    @Test
    fun `should return list of accounts filtered by start_height for btc asset`() {
        val expectedResponse =
            """{"data":[{"account":"**********************************","type":"UTXO","creation_height":"480777","creation_block_hash":"************0000000e7d28b7c2a37e42278e5d34c8a64a05d227e3608feac8","creation_time":"2017-08-16T12:34:59.000000000Z","creation_chain_sequence_number":"****************","balance":"0","n_debits":"6","n_credits":"6","last_chain_sequence_number":"****************","last_debit_height":"513907","last_credit_height":"513892"},{"account":"**********************************","type":"UTXO","creation_height":"485861","creation_block_hash":"************00000033350448762f4d658a0e7afc6a2d21c833487c42819487","creation_time":"2017-09-18T06:31:16.000000000Z","creation_chain_sequence_number":"****************","balance":"0","n_debits":"10","n_credits":"10","last_chain_sequence_number":"****************","last_debit_height":"500530","last_credit_height":"500523"},{"account":"**********************************","type":"UTXO","creation_height":"525056","creation_block_hash":"************00000000e955552cb92c12035d22ca5389bdcd9352fa51bf47e2","creation_time":"2018-05-30T04:19:03.000000000Z","creation_chain_sequence_number":"****************","balance":"0","n_debits":"4","n_credits":"4","last_chain_sequence_number":"****************","last_debit_height":"529782","last_credit_height":"529778"},{"account":"**********************************","type":"UTXO","creation_height":"645045","creation_block_hash":"************00000000cda09abdd9c8847f6d9a4b218cab2d3401b87768c73b","creation_time":"2020-08-23T19:46:02.000000000Z","creation_chain_sequence_number":"****************","balance":"0","n_debits":"6","n_credits":"6","last_chain_sequence_number":"****************","last_debit_height":"649240","last_credit_height":"649073"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain-v2/btc/accounts?api_key=$TEST_API_KEY&start_height=480777",
        )
    }

    @Test
    fun `should return list of accounts filtered by start_height and accounts for btc asset`() {
        val expectedResponse =
            """{"data":[{"account":"**********************************","type":"UTXO","creation_height":"480777","creation_block_hash":"************0000000e7d28b7c2a37e42278e5d34c8a64a05d227e3608feac8","creation_time":"2017-08-16T12:34:59.000000000Z","creation_chain_sequence_number":"****************","balance":"0","n_debits":"6","n_credits":"6","last_chain_sequence_number":"****************","last_debit_height":"513907","last_credit_height":"513892"},{"account":"**********************************","type":"UTXO","creation_height":"485861","creation_block_hash":"************00000033350448762f4d658a0e7afc6a2d21c833487c42819487","creation_time":"2017-09-18T06:31:16.000000000Z","creation_chain_sequence_number":"****************","balance":"0","n_debits":"10","n_credits":"10","last_chain_sequence_number":"****************","last_debit_height":"500530","last_credit_height":"500523"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain-v2/btc/accounts?api_key=$TEST_API_KEY&start_height=480777&accounts=**********************************,**********************************",
        )
    }

    @Test
    fun `should return list of accounts filtered by start_height and end_height for btc asset`() {
        val expectedResponse =
            """{"data":[{"account":"**********************************","type":"UTXO","creation_height":"480777","creation_block_hash":"************0000000e7d28b7c2a37e42278e5d34c8a64a05d227e3608feac8","creation_time":"2017-08-16T12:34:59.000000000Z","creation_chain_sequence_number":"****************","balance":"0","n_debits":"6","n_credits":"6","last_chain_sequence_number":"****************","last_debit_height":"513907","last_credit_height":"513892"},{"account":"**********************************","type":"UTXO","creation_height":"485861","creation_block_hash":"************00000033350448762f4d658a0e7afc6a2d21c833487c42819487","creation_time":"2017-09-18T06:31:16.000000000Z","creation_chain_sequence_number":"****************","balance":"0","n_debits":"10","n_credits":"10","last_chain_sequence_number":"****************","last_debit_height":"500530","last_credit_height":"500523"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain-v2/btc/accounts?api_key=$TEST_API_KEY&start_height=480777&end_height=485862",
        )
    }

    @Test
    fun `should return empty list of accounts when there is no data in specified heights range`() {
        assertResponse(
            200,
            EMPTY_RESPONSE,
            "/v4/blockchain-v2/btc/accounts?api_key=$TEST_API_KEY&start_height=1&end_height=2",
        )
    }

    @Test
    fun `should return 400 when start_height is greater than end_height`() {
        val expectedResponse =
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'start_height'. Start height is less than end height."}}"""
        assertResponse(
            400,
            expectedResponse,
            "/v4/blockchain-v2/btc/accounts?api_key=$TEST_API_KEY&start_height=485862&end_height=480777",
        )
    }

    @Test
    fun `CORNER CASE should return last account filtered by start_height with start_inclusive=true`() {
        val expectedResponse =
            """{"data":[{"account":"**********************************","type":"UTXO","creation_height":"645045","creation_block_hash":"************00000000cda09abdd9c8847f6d9a4b218cab2d3401b87768c73b","creation_time":"2020-08-23T19:46:02.000000000Z","creation_chain_sequence_number":"****************","balance":"0","n_debits":"6","n_credits":"6","last_chain_sequence_number":"****************","last_debit_height":"649240","last_credit_height":"649073"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain-v2/btc/accounts?api_key=$TEST_API_KEY&start_height=645045&start_inclusive=true",
        )
    }

    @Test
    fun `CORNER CASE should not return last account filtered by start_height with start_inclusive=false`() {
        assertResponse(
            200,
            EMPTY_RESPONSE,
            "/v4/blockchain-v2/btc/accounts?api_key=$TEST_API_KEY&start_height=645045&start_inclusive=false",
        )
    }

    @Test
    fun `CORNER CASE should return first account filtered by end_height with end_inclusive=true`() {
        val expectedResponse =
            """{"data":[{"account":"**********************************","type":"UTXO","creation_height":"299087","creation_block_hash":"************00002a3db51bc2352319be56536fd0607df1d1d3ab2b8cf73893","creation_time":"2014-05-04T14:51:57.000000000Z","creation_chain_sequence_number":"****************","balance":"0.********","n_debits":"0","n_credits":"12","last_chain_sequence_number":"****************","last_credit_height":"312745"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain-v2/btc/accounts?api_key=$TEST_API_KEY&end_height=299087&end_inclusive=true",
        )
    }

    @Test
    fun `CORNER CASE should not return first account filtered by end_height with end_inclusive=false`() {
        assertResponse(
            200,
            EMPTY_RESPONSE,
            "/v4/blockchain-v2/btc/accounts?api_key=$TEST_API_KEY&end_height=299087&end_inclusive=false",
        )
    }

    @Test
    fun `should return first accounts page filtered by start_height with page_size=3 and paging_from=start`() {
        val expectedResponse =
            """{"data":[{"account":"**********************************","type":"UTXO","creation_height":"462031","creation_block_hash":"************00000156101007ce892cec617a735267065923e6fafcf5a099fa","creation_time":"2017-04-15T17:49:40.000000000Z","creation_chain_sequence_number":"****************","balance":"0","n_debits":"9","n_credits":"9","last_chain_sequence_number":"****************","last_debit_height":"463017","last_credit_height":"463010"},{"account":"**********************************","type":"UTXO","creation_height":"470920","creation_block_hash":"************0000002c7505ef2272e0677fa53d68d633f8e076ed42dd3380e6","creation_time":"2017-06-12T06:50:36.000000000Z","creation_chain_sequence_number":"****************","balance":"0.0000583","n_debits":"3","n_credits":"4","last_chain_sequence_number":"****************","last_debit_height":"474496","last_credit_height":"478464"},{"account":"**********************************","type":"UTXO","creation_height":"480777","creation_block_hash":"************0000000e7d28b7c2a37e42278e5d34c8a64a05d227e3608feac8","creation_time":"2017-08-16T12:34:59.000000000Z","creation_chain_sequence_number":"****************","balance":"0","n_debits":"6","n_credits":"6","last_chain_sequence_number":"****************","last_debit_height":"513907","last_credit_height":"513892"}],"next_page_token":"********************************************************************","next_page_url":"http://127.0.0.1:8080/v4/blockchain-v2/btc/accounts?api_key=x1&start_height=462031&page_size=3&paging_from=start&next_page_token=********************************************************************"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain-v2/btc/accounts?api_key=$TEST_API_KEY&start_height=462031&page_size=3&paging_from=start",
        )
    }

    @Test
    fun `should return second accounts page filtered by start_height with page_size=3 and paging_from=start`() {
        val expectedResponse =
            """{"data":[{"account":"**********************************","type":"UTXO","creation_height":"485861","creation_block_hash":"************00000033350448762f4d658a0e7afc6a2d21c833487c42819487","creation_time":"2017-09-18T06:31:16.000000000Z","creation_chain_sequence_number":"****************","balance":"0","n_debits":"10","n_credits":"10","last_chain_sequence_number":"****************","last_debit_height":"500530","last_credit_height":"500523"},{"account":"**********************************","type":"UTXO","creation_height":"525056","creation_block_hash":"************00000000e955552cb92c12035d22ca5389bdcd9352fa51bf47e2","creation_time":"2018-05-30T04:19:03.000000000Z","creation_chain_sequence_number":"****************","balance":"0","n_debits":"4","n_credits":"4","last_chain_sequence_number":"****************","last_debit_height":"529782","last_credit_height":"529778"},{"account":"**********************************","type":"UTXO","creation_height":"645045","creation_block_hash":"************00000000cda09abdd9c8847f6d9a4b218cab2d3401b87768c73b","creation_time":"2020-08-23T19:46:02.000000000Z","creation_chain_sequence_number":"****************","balance":"0","n_debits":"6","n_credits":"6","last_chain_sequence_number":"****************","last_debit_height":"649240","last_credit_height":"649073"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain-v2/btc/accounts?api_key=$TEST_API_KEY&start_height=462031&page_size=3&paging_from=start&next_page_token=********************************************************************",
        )
    }

    @Test
    fun `should return list of accounts filtered by start_chain_sequence_number for btc asset`() {
        val expectedResponse =
            """{"data":[{"account":"**********************************","type":"UTXO","creation_height":"485861","creation_block_hash":"************00000033350448762f4d658a0e7afc6a2d21c833487c42819487","creation_time":"2017-09-18T06:31:16.000000000Z","creation_chain_sequence_number":"****************","balance":"0","n_debits":"10","n_credits":"10","last_chain_sequence_number":"****************","last_debit_height":"500530","last_credit_height":"500523"},{"account":"**********************************","type":"UTXO","creation_height":"525056","creation_block_hash":"************00000000e955552cb92c12035d22ca5389bdcd9352fa51bf47e2","creation_time":"2018-05-30T04:19:03.000000000Z","creation_chain_sequence_number":"****************","balance":"0","n_debits":"4","n_credits":"4","last_chain_sequence_number":"****************","last_debit_height":"529782","last_credit_height":"529778"},{"account":"**********************************","type":"UTXO","creation_height":"645045","creation_block_hash":"************00000000cda09abdd9c8847f6d9a4b218cab2d3401b87768c73b","creation_time":"2020-08-23T19:46:02.000000000Z","creation_chain_sequence_number":"****************","balance":"0","n_debits":"6","n_credits":"6","last_chain_sequence_number":"****************","last_debit_height":"649240","last_credit_height":"649073"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain-v2/btc/accounts?api_key=$TEST_API_KEY&start_chain_sequence_number=****************",
        )
    }

    @Test
    fun `should return list of accounts filtered by start_chain_sequence_number and end_chain_sequence_number for btc asset`() {
        val expectedResponse =
            """{"data":[{"account":"**********************************","type":"UTXO","creation_height":"470920","creation_block_hash":"************0000002c7505ef2272e0677fa53d68d633f8e076ed42dd3380e6","creation_time":"2017-06-12T06:50:36.000000000Z","creation_chain_sequence_number":"****************","balance":"0.0000583","n_debits":"3","n_credits":"4","last_chain_sequence_number":"****************","last_debit_height":"474496","last_credit_height":"478464"},{"account":"**********************************","type":"UTXO","creation_height":"480777","creation_block_hash":"************0000000e7d28b7c2a37e42278e5d34c8a64a05d227e3608feac8","creation_time":"2017-08-16T12:34:59.000000000Z","creation_chain_sequence_number":"****************","balance":"0","n_debits":"6","n_credits":"6","last_chain_sequence_number":"****************","last_debit_height":"513907","last_credit_height":"513892"},{"account":"**********************************","type":"UTXO","creation_height":"485861","creation_block_hash":"************00000033350448762f4d658a0e7afc6a2d21c833487c42819487","creation_time":"2017-09-18T06:31:16.000000000Z","creation_chain_sequence_number":"****************","balance":"0","n_debits":"10","n_credits":"10","last_chain_sequence_number":"****************","last_debit_height":"500530","last_credit_height":"500523"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain-v2/btc/accounts?api_key=$TEST_API_KEY&start_chain_sequence_number=****************&end_chain_sequence_number=****************",
        )
    }

    @Test
    fun `should return 400 when start_chain_sequence_number is greater than end_chain_sequence_number`() {
        val expectedResponse =
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'start_chain_sequence_number'. Parameter 'start_chain_sequence_number' must be greater than 'end_chain_sequence_number'."}}"""
        assertResponse(
            400,
            expectedResponse,
            "/v4/blockchain-v2/btc/accounts?api_key=$TEST_API_KEY&start_chain_sequence_number=****************&end_chain_sequence_number=****************",
        )
    }

    @Test
    fun `CORNER CASE should return last account filtered by start_chain_sequence_number with start_inclusive=true`() {
        val expectedResponse =
            """{"data":[{"account":"**********************************","type":"UTXO","creation_height":"645045","creation_block_hash":"************00000000cda09abdd9c8847f6d9a4b218cab2d3401b87768c73b","creation_time":"2020-08-23T19:46:02.000000000Z","creation_chain_sequence_number":"****************","balance":"0","n_debits":"6","n_credits":"6","last_chain_sequence_number":"****************","last_debit_height":"649240","last_credit_height":"649073"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain-v2/btc/accounts?api_key=$TEST_API_KEY&start_chain_sequence_number=****************&start_inclusive=true",
        )
    }

    @Test
    fun `CORNER CASE should not return last account filtered by start_chain_sequence_number with start_inclusive=false`() {
        assertResponse(
            200,
            EMPTY_RESPONSE,
            "/v4/blockchain-v2/btc/accounts?api_key=$TEST_API_KEY&start_chain_sequence_number=****************&start_inclusive=false",
        )
    }

    @Test
    fun `CORNER CASE should return first account filtered by end_chain_sequence_number with end_inclusive=true`() {
        val expectedResponse =
            """{"data":[{"account":"**********************************","type":"UTXO","creation_height":"299087","creation_block_hash":"************00002a3db51bc2352319be56536fd0607df1d1d3ab2b8cf73893","creation_time":"2014-05-04T14:51:57.000000000Z","creation_chain_sequence_number":"****************","balance":"0.********","n_debits":"0","n_credits":"12","last_chain_sequence_number":"****************","last_credit_height":"312745"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain-v2/btc/accounts?api_key=$TEST_API_KEY&end_chain_sequence_number=****************&end_inclusive=true",
        )
    }

    @Test
    fun `CORNER CASE should not return first account filtered by end_chain_sequence_number with end_inclusive=false`() {
        assertResponse(
            200,
            EMPTY_RESPONSE,
            "/v4/blockchain-v2/btc/accounts?api_key=$TEST_API_KEY&end_chain_sequence_number=****************&end_inclusive=false",
        )
    }

    @Test
    fun `should return first accounts page filtered by start_chain_sequence_number with page_size=2 and paging_from=start`() {
        val expectedResponse =
            """{"data":[{"account":"**********************************","type":"UTXO","creation_height":"480777","creation_block_hash":"************0000000e7d28b7c2a37e42278e5d34c8a64a05d227e3608feac8","creation_time":"2017-08-16T12:34:59.000000000Z","creation_chain_sequence_number":"****************","balance":"0","n_debits":"6","n_credits":"6","last_chain_sequence_number":"****************","last_debit_height":"513907","last_credit_height":"513892"},{"account":"**********************************","type":"UTXO","creation_height":"485861","creation_block_hash":"************00000033350448762f4d658a0e7afc6a2d21c833487c42819487","creation_time":"2017-09-18T06:31:16.000000000Z","creation_chain_sequence_number":"****************","balance":"0","n_debits":"10","n_credits":"10","last_chain_sequence_number":"****************","last_debit_height":"500530","last_credit_height":"500523"}],"next_page_token":"********************************************************************","next_page_url":"http://127.0.0.1:8080/v4/blockchain-v2/btc/accounts?api_key=x1&start_chain_sequence_number=****************&page_size=2&paging_from=start&next_page_token=********************************************************************"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain-v2/btc/accounts?api_key=$TEST_API_KEY&start_chain_sequence_number=****************&page_size=2&paging_from=start",
        )
    }

    @Test
    fun `should return second accounts page filtered by start_height with page_size=2 and paging_from=start`() {
        val expectedResponse =
            """{"data":[{"account":"**********************************","type":"UTXO","creation_height":"525056","creation_block_hash":"************00000000e955552cb92c12035d22ca5389bdcd9352fa51bf47e2","creation_time":"2018-05-30T04:19:03.000000000Z","creation_chain_sequence_number":"****************","balance":"0","n_debits":"4","n_credits":"4","last_chain_sequence_number":"****************","last_debit_height":"529782","last_credit_height":"529778"},{"account":"**********************************","type":"UTXO","creation_height":"645045","creation_block_hash":"************00000000cda09abdd9c8847f6d9a4b218cab2d3401b87768c73b","creation_time":"2020-08-23T19:46:02.000000000Z","creation_chain_sequence_number":"****************","balance":"0","n_debits":"6","n_credits":"6","last_chain_sequence_number":"****************","last_debit_height":"649240","last_credit_height":"649073"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain-v2/btc/accounts?api_key=$TEST_API_KEY&start_chain_sequence_number=****************&page_size=2&paging_from=start&next_page_token=********************************************************************",
        )
    }

    @Test
    fun `should return list of accounts filtered by start_time for btc asset`() {
        val expectedResponse =
            """{"data":[{"account":"**********************************","type":"UTXO","creation_height":"485861","creation_block_hash":"************00000033350448762f4d658a0e7afc6a2d21c833487c42819487","creation_time":"2017-09-18T06:31:16.000000000Z","creation_chain_sequence_number":"****************","balance":"0","n_debits":"10","n_credits":"10","last_chain_sequence_number":"****************","last_debit_height":"500530","last_credit_height":"500523"},{"account":"**********************************","type":"UTXO","creation_height":"525056","creation_block_hash":"************00000000e955552cb92c12035d22ca5389bdcd9352fa51bf47e2","creation_time":"2018-05-30T04:19:03.000000000Z","creation_chain_sequence_number":"****************","balance":"0","n_debits":"4","n_credits":"4","last_chain_sequence_number":"****************","last_debit_height":"529782","last_credit_height":"529778"},{"account":"**********************************","type":"UTXO","creation_height":"645045","creation_block_hash":"************00000000cda09abdd9c8847f6d9a4b218cab2d3401b87768c73b","creation_time":"2020-08-23T19:46:02.000000000Z","creation_chain_sequence_number":"****************","balance":"0","n_debits":"6","n_credits":"6","last_chain_sequence_number":"****************","last_debit_height":"649240","last_credit_height":"649073"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain-v2/btc/accounts?api_key=$TEST_API_KEY&start_time=2017-09-18T06:31:16Z",
        )
    }

    @Test
    fun `should return list of accounts filtered by start_time and end_time for btc asset`() {
        val expectedResponse =
            """{"data":[{"account":"**********************************","type":"UTXO","creation_height":"485861","creation_block_hash":"************00000033350448762f4d658a0e7afc6a2d21c833487c42819487","creation_time":"2017-09-18T06:31:16.000000000Z","creation_chain_sequence_number":"****************","balance":"0","n_debits":"10","n_credits":"10","last_chain_sequence_number":"****************","last_debit_height":"500530","last_credit_height":"500523"},{"account":"**********************************","type":"UTXO","creation_height":"525056","creation_block_hash":"************00000000e955552cb92c12035d22ca5389bdcd9352fa51bf47e2","creation_time":"2018-05-30T04:19:03.000000000Z","creation_chain_sequence_number":"****************","balance":"0","n_debits":"4","n_credits":"4","last_chain_sequence_number":"****************","last_debit_height":"529782","last_credit_height":"529778"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain-v2/btc/accounts?api_key=$TEST_API_KEY&start_time=2017-09-18T06:31:16Z&end_time=2018-05-30T06:31:16Z",
        )
    }

    @Test
    fun `should return empty list of accounts when there is no data in time range`() {
        assertResponse(
            200,
            EMPTY_RESPONSE,
            "/v4/blockchain-v2/btc/accounts?api_key=$TEST_API_KEY&start_time=2020-09-18T06:31:16Z&end_time=2021-05-30T06:31:16Z",
        )
    }

    @Test
    fun `should return 400 when start_time is greater than end_time`() {
        val expectedResponse =
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'start_time'. Start time '2018-05-30T06:31:16Z' is later than the end time '2017-09-18T06:31:16Z'."}}"""
        assertResponse(
            400,
            expectedResponse,
            "/v4/blockchain-v2/btc/accounts?api_key=$TEST_API_KEY&end_time=2017-09-18T06:31:16Z&start_time=2018-05-30T06:31:16Z",
        )
    }

    @Test
    fun `CORNER CASE should return last account filtered by start_time with start_inclusive=true`() {
        val expectedResponse =
            """{"data":[{"account":"**********************************","type":"UTXO","creation_height":"645045","creation_block_hash":"************00000000cda09abdd9c8847f6d9a4b218cab2d3401b87768c73b","creation_time":"2020-08-23T19:46:02.000000000Z","creation_chain_sequence_number":"****************","balance":"0","n_debits":"6","n_credits":"6","last_chain_sequence_number":"****************","last_debit_height":"649240","last_credit_height":"649073"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain-v2/btc/accounts?api_key=$TEST_API_KEY&start_time=2020-08-23T19:46:02Z&start_inclusive=true",
        )
    }

    @Test
    fun `CORNER CASE should not return last account filtered by start_time with start_inclusive=false`() {
        assertResponse(
            200,
            EMPTY_RESPONSE,
            "/v4/blockchain-v2/btc/accounts?api_key=$TEST_API_KEY&start_time=2020-08-23T19:46:02Z&start_inclusive=false",
        )
    }

    @Test
    fun `CORNER CASE should return first account filtered by end_time with end_inclusive=true`() {
        val expectedResponse =
            """{"data":[{"account":"**********************************","type":"UTXO","creation_height":"299087","creation_block_hash":"************00002a3db51bc2352319be56536fd0607df1d1d3ab2b8cf73893","creation_time":"2014-05-04T14:51:57.000000000Z","creation_chain_sequence_number":"****************","balance":"0.********","n_debits":"0","n_credits":"12","last_chain_sequence_number":"****************","last_credit_height":"312745"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain-v2/btc/accounts?api_key=$TEST_API_KEY&end_time=2014-05-04T14:51:57Z&end_inclusive=true",
        )
    }

    @Test
    fun `CORNER CASE should not return first account filtered by end_time with end_inclusive=false`() {
        assertResponse(
            200,
            EMPTY_RESPONSE,
            "/v4/blockchain-v2/btc/accounts?api_key=$TEST_API_KEY&end_time=2014-05-04T14:51:57Z&end_inclusive=false",
        )
    }

    @Test
    fun `should return first accounts page filtered by start_time with page_size=2 and paging_from=start`() {
        val expectedResponse =
            """{"data":[{"account":"**********************************","type":"UTXO","creation_height":"480777","creation_block_hash":"************0000000e7d28b7c2a37e42278e5d34c8a64a05d227e3608feac8","creation_time":"2017-08-16T12:34:59.000000000Z","creation_chain_sequence_number":"****************","balance":"0","n_debits":"6","n_credits":"6","last_chain_sequence_number":"****************","last_debit_height":"513907","last_credit_height":"513892"},{"account":"**********************************","type":"UTXO","creation_height":"485861","creation_block_hash":"************00000033350448762f4d658a0e7afc6a2d21c833487c42819487","creation_time":"2017-09-18T06:31:16.000000000Z","creation_chain_sequence_number":"****************","balance":"0","n_debits":"10","n_credits":"10","last_chain_sequence_number":"****************","last_debit_height":"500530","last_credit_height":"500523"}],"next_page_token":"********************************************************************","next_page_url":"http://127.0.0.1:8080/v4/blockchain-v2/btc/accounts?api_key=x1&start_time=2017-08-16T12%3A34%3A59Z&page_size=2&paging_from=start&next_page_token=********************************************************************"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain-v2/btc/accounts?api_key=$TEST_API_KEY&start_time=2017-08-16T12:34:59Z&page_size=2&paging_from=start",
        )
    }

    @Test
    fun `should return second accounts page filtered by start_time with page_size=2 and paging_from=start`() {
        val expectedResponse =
            """{"data":[{"account":"**********************************","type":"UTXO","creation_height":"525056","creation_block_hash":"************00000000e955552cb92c12035d22ca5389bdcd9352fa51bf47e2","creation_time":"2018-05-30T04:19:03.000000000Z","creation_chain_sequence_number":"****************","balance":"0","n_debits":"4","n_credits":"4","last_chain_sequence_number":"****************","last_debit_height":"529782","last_credit_height":"529778"},{"account":"**********************************","type":"UTXO","creation_height":"645045","creation_block_hash":"************00000000cda09abdd9c8847f6d9a4b218cab2d3401b87768c73b","creation_time":"2020-08-23T19:46:02.000000000Z","creation_chain_sequence_number":"****************","balance":"0","n_debits":"6","n_credits":"6","last_chain_sequence_number":"****************","last_debit_height":"649240","last_credit_height":"649073"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain-v2/btc/accounts?api_key=$TEST_API_KEY&start_time=2017-08-16T12%3A34%3A59Z&page_size=2&paging_from=start&next_page_token=********************************************************************",
        )
    }

    @Test
    fun `should return all accounts for etc asset`() {
        val expectedResponse =
            """{"data":[{"account":"68700fff2b339c7501cdab79100f9d2909a841d7","type":"ACCOUNT","creation_height":"********","creation_block_hash":"b73fbe2f76d359be0507b72e04a354944ca889d15735af2ccd07849da6610712","creation_time":"2021-11-17T08:13:31.000000000Z","creation_chain_sequence_number":"*****************","balance":"0.031342593498321799","n_debits":"0","n_credits":"1","last_chain_sequence_number":"*****************","last_credit_height":"********"},{"account":"68700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d7","type":"ACCOUNT","creation_height":"********","creation_block_hash":"b73fbe2f76d359be0507b72e04a354944ca889d15735af2ccd07849da6610712","creation_time":"2021-11-17T08:13:31.000000000Z","creation_chain_sequence_number":"*****************","balance":"0.031342593498321799","n_debits":"0","n_credits":"1","last_chain_sequence_number":"*****************","last_credit_height":"********"},{"account":"1f6cc7f4ae7df8af24c06bbf7b22759ae66fa360","type":"ACCOUNT","creation_height":"********","creation_block_hash":"fc629648d5fa937fec544b57e1f914c8ce1c8cd207675bd54b4890eec3e66f06","creation_time":"2021-11-17T08:14:12.000000000Z","creation_chain_sequence_number":"*****************","balance":"0.********","n_debits":"0","n_credits":"1","last_chain_sequence_number":"*****************","last_credit_height":"********"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain-v2/etc/accounts?api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `should return account filtering by hexed account for etc asset`() {
        val expectedResponse =
            """{"data":[{"account":"68700fff2b339c7501cdab79100f9d2909a841d7","type":"ACCOUNT","creation_height":"********","creation_block_hash":"b73fbe2f76d359be0507b72e04a354944ca889d15735af2ccd07849da6610712","creation_time":"2021-11-17T08:13:31.000000000Z","creation_chain_sequence_number":"*****************","balance":"0.031342593498321799","n_debits":"0","n_credits":"1","last_chain_sequence_number":"*****************","last_credit_height":"********"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain-v2/etc/accounts?api_key=$TEST_API_KEY&accounts=68700fff2b339c7501cdab79100f9d2909a841d7",
        )
    }

    @Test
    fun `should truncate account if its size more than 1K for etc asset`() {
        val expectedResponse =
            """{"data":[{"account":"68700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d7","type":"ACCOUNT","creation_height":"********","creation_block_hash":"b73fbe2f76d359be0507b72e04a354944ca889d15735af2ccd07849da6610712","creation_time":"2021-11-17T08:13:31.000000000Z","creation_chain_sequence_number":"*****************","balance":"0.031342593498321799","n_debits":"0","n_credits":"1","last_chain_sequence_number":"*****************","last_credit_height":"********"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain-v2/etc/accounts?api_key=$TEST_API_KEY&accounts=68700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d768700fff2b339c7501cdab79100f9d2909a841d7",
        )
    }

    @Test
    fun `should return 403 when trying to access pro only accounts with community key`() {
        val expectedResponse =
            """{"error":{"type":"forbidden","message":"Requested resource is not available with supplied credentials."}}"""
        assertResponse(
            403,
            expectedResponse,
            "/v4/blockchain-v2/loom/accounts?api_key=$COMMUNITY_KEY",
        )
    }

    @Test
    fun `should return error 403 when filter by more than one account for community key`() {
        val account1 = "001"
        val account2 = "002"

        val expectedResponse =
            """{"error":{"type":"forbidden","message":"Filter 'accounts' with multiple elements is not allowed for Community API."}}"""

        assertResponse(
            403,
            expectedResponse,
            "/v4/blockchain-v2/btc/accounts?api_key=$COMMUNITY_KEY&accounts=$account1,$account2",
        )
    }

    @Test
    fun `should ignore start_height filters for btc asset for community key`() {
        val expectedResponse =
            """{"data":[{"account":"**********************************","type":"UTXO","creation_height":"299087","creation_block_hash":"************00002a3db51bc2352319be56536fd0607df1d1d3ab2b8cf73893","creation_time":"2014-05-04T14:51:57.000000000Z","creation_chain_sequence_number":"****************","balance":"0.********","n_debits":"0","n_credits":"12","last_chain_sequence_number":"****************","last_credit_height":"312745"},{"account":"**********************************","type":"UTXO","creation_height":"462031","creation_block_hash":"************00000156101007ce892cec617a735267065923e6fafcf5a099fa","creation_time":"2017-04-15T17:49:40.000000000Z","creation_chain_sequence_number":"****************","balance":"0","n_debits":"9","n_credits":"9","last_chain_sequence_number":"****************","last_debit_height":"463017","last_credit_height":"463010"},{"account":"**********************************","type":"UTXO","creation_height":"470920","creation_block_hash":"************0000002c7505ef2272e0677fa53d68d633f8e076ed42dd3380e6","creation_time":"2017-06-12T06:50:36.000000000Z","creation_chain_sequence_number":"****************","balance":"0.0000583","n_debits":"3","n_credits":"4","last_chain_sequence_number":"****************","last_debit_height":"474496","last_credit_height":"478464"},{"account":"**********************************","type":"UTXO","creation_height":"480777","creation_block_hash":"************0000000e7d28b7c2a37e42278e5d34c8a64a05d227e3608feac8","creation_time":"2017-08-16T12:34:59.000000000Z","creation_chain_sequence_number":"****************","balance":"0","n_debits":"6","n_credits":"6","last_chain_sequence_number":"****************","last_debit_height":"513907","last_credit_height":"513892"},{"account":"**********************************","type":"UTXO","creation_height":"485861","creation_block_hash":"************00000033350448762f4d658a0e7afc6a2d21c833487c42819487","creation_time":"2017-09-18T06:31:16.000000000Z","creation_chain_sequence_number":"****************","balance":"0","n_debits":"10","n_credits":"10","last_chain_sequence_number":"****************","last_debit_height":"500530","last_credit_height":"500523"},{"account":"**********************************","type":"UTXO","creation_height":"525056","creation_block_hash":"************00000000e955552cb92c12035d22ca5389bdcd9352fa51bf47e2","creation_time":"2018-05-30T04:19:03.000000000Z","creation_chain_sequence_number":"****************","balance":"0","n_debits":"4","n_credits":"4","last_chain_sequence_number":"****************","last_debit_height":"529782","last_credit_height":"529778"},{"account":"**********************************","type":"UTXO","creation_height":"645045","creation_block_hash":"************00000000cda09abdd9c8847f6d9a4b218cab2d3401b87768c73b","creation_time":"2020-08-23T19:46:02.000000000Z","creation_chain_sequence_number":"****************","balance":"0","n_debits":"6","n_credits":"6","last_chain_sequence_number":"****************","last_debit_height":"649240","last_credit_height":"649073"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain-v2/btc/accounts?api_key=$COMMUNITY_KEY&start_height=645046",
        )
    }

    @Test
    fun `should ignore end_height filters for btc asset for community key`() {
        val expectedResponse =
            """{"data":[{"account":"**********************************","type":"UTXO","creation_height":"299087","creation_block_hash":"************00002a3db51bc2352319be56536fd0607df1d1d3ab2b8cf73893","creation_time":"2014-05-04T14:51:57.000000000Z","creation_chain_sequence_number":"****************","balance":"0.********","n_debits":"0","n_credits":"12","last_chain_sequence_number":"****************","last_credit_height":"312745"},{"account":"**********************************","type":"UTXO","creation_height":"462031","creation_block_hash":"************00000156101007ce892cec617a735267065923e6fafcf5a099fa","creation_time":"2017-04-15T17:49:40.000000000Z","creation_chain_sequence_number":"****************","balance":"0","n_debits":"9","n_credits":"9","last_chain_sequence_number":"****************","last_debit_height":"463017","last_credit_height":"463010"},{"account":"**********************************","type":"UTXO","creation_height":"470920","creation_block_hash":"************0000002c7505ef2272e0677fa53d68d633f8e076ed42dd3380e6","creation_time":"2017-06-12T06:50:36.000000000Z","creation_chain_sequence_number":"****************","balance":"0.0000583","n_debits":"3","n_credits":"4","last_chain_sequence_number":"****************","last_debit_height":"474496","last_credit_height":"478464"},{"account":"**********************************","type":"UTXO","creation_height":"480777","creation_block_hash":"************0000000e7d28b7c2a37e42278e5d34c8a64a05d227e3608feac8","creation_time":"2017-08-16T12:34:59.000000000Z","creation_chain_sequence_number":"****************","balance":"0","n_debits":"6","n_credits":"6","last_chain_sequence_number":"****************","last_debit_height":"513907","last_credit_height":"513892"},{"account":"**********************************","type":"UTXO","creation_height":"485861","creation_block_hash":"************00000033350448762f4d658a0e7afc6a2d21c833487c42819487","creation_time":"2017-09-18T06:31:16.000000000Z","creation_chain_sequence_number":"****************","balance":"0","n_debits":"10","n_credits":"10","last_chain_sequence_number":"****************","last_debit_height":"500530","last_credit_height":"500523"},{"account":"**********************************","type":"UTXO","creation_height":"525056","creation_block_hash":"************00000000e955552cb92c12035d22ca5389bdcd9352fa51bf47e2","creation_time":"2018-05-30T04:19:03.000000000Z","creation_chain_sequence_number":"****************","balance":"0","n_debits":"4","n_credits":"4","last_chain_sequence_number":"****************","last_debit_height":"529782","last_credit_height":"529778"},{"account":"**********************************","type":"UTXO","creation_height":"645045","creation_block_hash":"************00000000cda09abdd9c8847f6d9a4b218cab2d3401b87768c73b","creation_time":"2020-08-23T19:46:02.000000000Z","creation_chain_sequence_number":"****************","balance":"0","n_debits":"6","n_credits":"6","last_chain_sequence_number":"****************","last_debit_height":"649240","last_credit_height":"649073"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain-v2/btc/accounts?api_key=$COMMUNITY_KEY&end_height=645040",
        )
    }

    @Test
    fun `should ignore filters by chain start_chain_sequence_number for btc asset for community key`() {
        val expectedResponse =
            """{"data":[{"account":"**********************************","type":"UTXO","creation_height":"299087","creation_block_hash":"************00002a3db51bc2352319be56536fd0607df1d1d3ab2b8cf73893","creation_time":"2014-05-04T14:51:57.000000000Z","creation_chain_sequence_number":"****************","balance":"0.********","n_debits":"0","n_credits":"12","last_chain_sequence_number":"****************","last_credit_height":"312745"},{"account":"**********************************","type":"UTXO","creation_height":"462031","creation_block_hash":"************00000156101007ce892cec617a735267065923e6fafcf5a099fa","creation_time":"2017-04-15T17:49:40.000000000Z","creation_chain_sequence_number":"****************","balance":"0","n_debits":"9","n_credits":"9","last_chain_sequence_number":"****************","last_debit_height":"463017","last_credit_height":"463010"},{"account":"**********************************","type":"UTXO","creation_height":"470920","creation_block_hash":"************0000002c7505ef2272e0677fa53d68d633f8e076ed42dd3380e6","creation_time":"2017-06-12T06:50:36.000000000Z","creation_chain_sequence_number":"****************","balance":"0.0000583","n_debits":"3","n_credits":"4","last_chain_sequence_number":"****************","last_debit_height":"474496","last_credit_height":"478464"},{"account":"**********************************","type":"UTXO","creation_height":"480777","creation_block_hash":"************0000000e7d28b7c2a37e42278e5d34c8a64a05d227e3608feac8","creation_time":"2017-08-16T12:34:59.000000000Z","creation_chain_sequence_number":"****************","balance":"0","n_debits":"6","n_credits":"6","last_chain_sequence_number":"****************","last_debit_height":"513907","last_credit_height":"513892"},{"account":"**********************************","type":"UTXO","creation_height":"485861","creation_block_hash":"************00000033350448762f4d658a0e7afc6a2d21c833487c42819487","creation_time":"2017-09-18T06:31:16.000000000Z","creation_chain_sequence_number":"****************","balance":"0","n_debits":"10","n_credits":"10","last_chain_sequence_number":"****************","last_debit_height":"500530","last_credit_height":"500523"},{"account":"**********************************","type":"UTXO","creation_height":"525056","creation_block_hash":"************00000000e955552cb92c12035d22ca5389bdcd9352fa51bf47e2","creation_time":"2018-05-30T04:19:03.000000000Z","creation_chain_sequence_number":"****************","balance":"0","n_debits":"4","n_credits":"4","last_chain_sequence_number":"****************","last_debit_height":"529782","last_credit_height":"529778"},{"account":"**********************************","type":"UTXO","creation_height":"645045","creation_block_hash":"************00000000cda09abdd9c8847f6d9a4b218cab2d3401b87768c73b","creation_time":"2020-08-23T19:46:02.000000000Z","creation_chain_sequence_number":"****************","balance":"0","n_debits":"6","n_credits":"6","last_chain_sequence_number":"****************","last_debit_height":"649240","last_credit_height":"649073"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain-v2/btc/accounts?api_key=$COMMUNITY_KEY&start_chain_sequence_number=****************",
        )
    }

    @Test
    fun `should ignore filters by chain end_chain_sequence_number for btc asset for community key`() {
        val expectedResponse =
            """{"data":[{"account":"**********************************","type":"UTXO","creation_height":"299087","creation_block_hash":"************00002a3db51bc2352319be56536fd0607df1d1d3ab2b8cf73893","creation_time":"2014-05-04T14:51:57.000000000Z","creation_chain_sequence_number":"****************","balance":"0.********","n_debits":"0","n_credits":"12","last_chain_sequence_number":"****************","last_credit_height":"312745"},{"account":"**********************************","type":"UTXO","creation_height":"462031","creation_block_hash":"************00000156101007ce892cec617a735267065923e6fafcf5a099fa","creation_time":"2017-04-15T17:49:40.000000000Z","creation_chain_sequence_number":"****************","balance":"0","n_debits":"9","n_credits":"9","last_chain_sequence_number":"****************","last_debit_height":"463017","last_credit_height":"463010"},{"account":"**********************************","type":"UTXO","creation_height":"470920","creation_block_hash":"************0000002c7505ef2272e0677fa53d68d633f8e076ed42dd3380e6","creation_time":"2017-06-12T06:50:36.000000000Z","creation_chain_sequence_number":"****************","balance":"0.0000583","n_debits":"3","n_credits":"4","last_chain_sequence_number":"****************","last_debit_height":"474496","last_credit_height":"478464"},{"account":"**********************************","type":"UTXO","creation_height":"480777","creation_block_hash":"************0000000e7d28b7c2a37e42278e5d34c8a64a05d227e3608feac8","creation_time":"2017-08-16T12:34:59.000000000Z","creation_chain_sequence_number":"****************","balance":"0","n_debits":"6","n_credits":"6","last_chain_sequence_number":"****************","last_debit_height":"513907","last_credit_height":"513892"},{"account":"**********************************","type":"UTXO","creation_height":"485861","creation_block_hash":"************00000033350448762f4d658a0e7afc6a2d21c833487c42819487","creation_time":"2017-09-18T06:31:16.000000000Z","creation_chain_sequence_number":"****************","balance":"0","n_debits":"10","n_credits":"10","last_chain_sequence_number":"****************","last_debit_height":"500530","last_credit_height":"500523"},{"account":"**********************************","type":"UTXO","creation_height":"525056","creation_block_hash":"************00000000e955552cb92c12035d22ca5389bdcd9352fa51bf47e2","creation_time":"2018-05-30T04:19:03.000000000Z","creation_chain_sequence_number":"****************","balance":"0","n_debits":"4","n_credits":"4","last_chain_sequence_number":"****************","last_debit_height":"529782","last_credit_height":"529778"},{"account":"**********************************","type":"UTXO","creation_height":"645045","creation_block_hash":"************00000000cda09abdd9c8847f6d9a4b218cab2d3401b87768c73b","creation_time":"2020-08-23T19:46:02.000000000Z","creation_chain_sequence_number":"****************","balance":"0","n_debits":"6","n_credits":"6","last_chain_sequence_number":"****************","last_debit_height":"649240","last_credit_height":"649073"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain-v2/btc/accounts?api_key=$COMMUNITY_KEY&end_chain_sequence_number=****************",
        )
    }

    @Test
    fun `should return first accounts page with page_size=2 and paging_from=start for community key`() {
        val expectedResponse =
            """{"data":[{"account":"**********************************","type":"UTXO","creation_height":"299087","creation_block_hash":"************00002a3db51bc2352319be56536fd0607df1d1d3ab2b8cf73893","creation_time":"2014-05-04T14:51:57.000000000Z","creation_chain_sequence_number":"****************","balance":"0.********","n_debits":"0","n_credits":"12","last_chain_sequence_number":"****************","last_credit_height":"312745"},{"account":"**********************************","type":"UTXO","creation_height":"462031","creation_block_hash":"************00000156101007ce892cec617a735267065923e6fafcf5a099fa","creation_time":"2017-04-15T17:49:40.000000000Z","creation_chain_sequence_number":"****************","balance":"0","n_debits":"9","n_credits":"9","last_chain_sequence_number":"****************","last_debit_height":"463017","last_credit_height":"463010"}],"next_page_token":"********************************************************************","next_page_url":"http://127.0.0.1:8080/v4/blockchain-v2/btc/accounts?api_key=T1z1tZcIY2tnX4jcUhve&page_size=2&paging_from=start&next_page_token=********************************************************************"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain-v2/btc/accounts?api_key=$COMMUNITY_KEY&page_size=2&paging_from=start",
        )
    }

    @Test
    fun `should return second accounts page with page_size=2 and paging_from=start for community key`() {
        val expectedResponse =
            """{"data":[{"account":"**********************************","type":"UTXO","creation_height":"470920","creation_block_hash":"************0000002c7505ef2272e0677fa53d68d633f8e076ed42dd3380e6","creation_time":"2017-06-12T06:50:36.000000000Z","creation_chain_sequence_number":"****************","balance":"0.0000583","n_debits":"3","n_credits":"4","last_chain_sequence_number":"****************","last_debit_height":"474496","last_credit_height":"478464"},{"account":"**********************************","type":"UTXO","creation_height":"480777","creation_block_hash":"************0000000e7d28b7c2a37e42278e5d34c8a64a05d227e3608feac8","creation_time":"2017-08-16T12:34:59.000000000Z","creation_chain_sequence_number":"****************","balance":"0","n_debits":"6","n_credits":"6","last_chain_sequence_number":"****************","last_debit_height":"513907","last_credit_height":"513892"}],"next_page_token":"********************************************************************","next_page_url":"http://127.0.0.1:8080/v4/blockchain-v2/btc/accounts?api_key=T1z1tZcIY2tnX4jcUhve&page_size=2&paging_from=start&next_page_token=********************************************************************"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain-v2/btc/accounts?api_key=$COMMUNITY_KEY&page_size=2&paging_from=start&next_page_token=********************************************************************",
        )
    }
}
