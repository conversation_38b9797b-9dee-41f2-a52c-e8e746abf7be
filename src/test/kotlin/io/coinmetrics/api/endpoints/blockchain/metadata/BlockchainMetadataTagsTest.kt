package io.coinmetrics.api.endpoints.blockchain.metadata

import io.coinmetrics.api.helper.BaseTest
import io.coinmetrics.api.helper.COMMUNITY_KEY
import io.coinmetrics.api.helper.TEST_API_KEY
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.testcontainers.junit.jupiter.Testcontainers

@Testcontainers
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class BlockchainMetadataTagsTest : BaseTest() {
    override fun getAdditionalEnvVariables(): Map<String, String> = mapOf("API_ADDRESS_TAGGING_ENABLED" to "true")

    @Test
    fun `should return client-accessible tags only when requested with an API key having tagging package included`() {
        val expectedResponse =
            """{"data":[{"tag":"ADDRESS_IS_CUSTODIAN","type":"ENTITY","description":"Tag the ADDRESS as CUSTO<PERSON><PERSON>."},{"tag":"ADDRESS_IS_DEPOSIT_WALLET","type":"ENTITY","description":"Tag the ADDRESS as DEPOSIT wallet."},{"tag":"ADDRESS_IS_EXCHANGE","type":"ENTITY","description":"Tag the ADDRESS as EXCHANGE."},{"tag":"ADDRESS_IS_GAMBLING","type":"ENTITY","description":"Tag the ADDRESS as GAMBLING."},{"tag":"ADDRESS_IS_HISTORIC","type":"ENTITY","description":"Tag the ADDRESS as HISTORIC."},{"tag":"ADDRESS_IS_ICO","type":"ENTITY","description":"Tag the ADDRESS as ICO."},{"tag":"ADDRESS_IS_MINING","type":"ENTITY","description":"Tag the ADDRESS as a Miner"},{"tag":"ADDRESS_IS_MINING_POOL","type":"ENTITY","description":"Tag the ADDRESS as a MINING_POOL."},{"tag":"ADDRESS_IS_TREASURY","type":"ENTITY","description":"Tag the ADDRESS as TREASURY wallet."},{"tag":"ADDRESS_IS_CONTRACT","type":"ENTITY","description":"ADDRESS is Smart Contract. Fields timestamp_start/timestamp_end, block_hash/height_start/end can contain information when contract was created and destructed."},{"tag":"ADDRESS_IS_FUND","type":"ENTITY","description":"Tag the ADDRESS as FUND"}]}"""
        assertResponse(200, expectedResponse, "/v4/blockchain-metadata/tags?api_key=x3")
    }

    @Test
    fun `should return list of tags when no parameters specified`() {
        val expectedResponse =
            """{"data":[{"tag":"ADDRESS_IS_COLD_WALLET","type":"ENTITY","description":"Tag the ADDRESS as COLD wallet."},{"tag":"ADDRESS_IS_CUSTODIAN","type":"ENTITY","description":"Tag the ADDRESS as CUSTODIAN."},{"tag":"ADDRESS_IS_DEPOSIT_WALLET","type":"ENTITY","description":"Tag the ADDRESS as DEPOSIT wallet."},{"tag":"ADDRESS_IS_EXCHANGE","type":"ENTITY","description":"Tag the ADDRESS as EXCHANGE."},{"tag":"ADDRESS_IS_GAMBLING","type":"ENTITY","description":"Tag the ADDRESS as GAMBLING."},{"tag":"ADDRESS_IS_HISTORIC","type":"ENTITY","description":"Tag the ADDRESS as HISTORIC."},{"tag":"ADDRESS_IS_ICO","type":"ENTITY","description":"Tag the ADDRESS as ICO."},{"tag":"ADDRESS_IS_MINING","type":"ENTITY","description":"Tag the ADDRESS as a Miner"},{"tag":"ADDRESS_IS_MINING_POOL","type":"ENTITY","description":"Tag the ADDRESS as a MINING_POOL."},{"tag":"ADDRESS_IS_RESTRICTED","type":"ENTITY","description":"Tag ADDRESS as \"restricted\". The reason is located on the tag_metadata field."},{"tag":"ADDRESS_IS_TREASURY","type":"ENTITY","description":"Tag the ADDRESS as TREASURY wallet."},{"tag":"ADDRESS_IS_CONTRACT","type":"ENTITY","description":"ADDRESS is Smart Contract. Fields timestamp_start/timestamp_end, block_hash/height_start/end can contain information when contract was created and destructed."},{"tag":"ADDRESS_IS_FUND","type":"ENTITY","description":"Tag the ADDRESS as FUND"},{"tag":"ADDRESS_IS_SWAPPER","type":"ENTITY","description":"ADDRESS is a Token Swapper"},{"tag":"ENTITY_IDENTITY","type":"ENTITY_ASSOCIATION","description":"Source and destination entity are the same."},{"tag":"ENTITY_OWNERSHIP","type":"ENTITY_ASSOCIATION","description":"Associates pairs of Entities where the source entity is owned by the destination entity."},{"tag":"LOCATION_IDENTITY","type":"LOCATION_ASSOCIATION","description":"Source Location and Destination Location are the same."},{"tag":"LOCATION_PARENT","type":"LOCATION_ASSOCIATION","description":"Source Location is parent of  Destination Location."}]}"""
        assertResponse(200, expectedResponse, "/v4/blockchain-metadata/tags?api_key=$TEST_API_KEY")
    }

    @Test
    fun `should return 400 when type parameter is empty for list of tags`() {
        val expectedResponse =
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'type'."}}"""
        assertResponse(400, expectedResponse, "/v4/blockchain-metadata/tags?type=&api_key=$TEST_API_KEY")
    }

    @Test
    fun `should return 400 when type parameter is not supported for list of tags`() {
        val typeParameter = "FOO"
        val expectedResponse =
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'type'. Value '$typeParameter' is not supported."}}"""
        assertResponse(400, expectedResponse, "/v4/blockchain-metadata/tags?type=$typeParameter&api_key=$TEST_API_KEY")
    }

    @Test
    fun `should return list of entity tags when type ENTITY is specified`() {
        val expectedResponse =
            """{"data":[{"tag":"ADDRESS_IS_COLD_WALLET","type":"ENTITY","description":"Tag the ADDRESS as COLD wallet."},{"tag":"ADDRESS_IS_CUSTODIAN","type":"ENTITY","description":"Tag the ADDRESS as CUSTODIAN."},{"tag":"ADDRESS_IS_DEPOSIT_WALLET","type":"ENTITY","description":"Tag the ADDRESS as DEPOSIT wallet."},{"tag":"ADDRESS_IS_EXCHANGE","type":"ENTITY","description":"Tag the ADDRESS as EXCHANGE."},{"tag":"ADDRESS_IS_GAMBLING","type":"ENTITY","description":"Tag the ADDRESS as GAMBLING."},{"tag":"ADDRESS_IS_HISTORIC","type":"ENTITY","description":"Tag the ADDRESS as HISTORIC."},{"tag":"ADDRESS_IS_ICO","type":"ENTITY","description":"Tag the ADDRESS as ICO."},{"tag":"ADDRESS_IS_MINING","type":"ENTITY","description":"Tag the ADDRESS as a Miner"},{"tag":"ADDRESS_IS_MINING_POOL","type":"ENTITY","description":"Tag the ADDRESS as a MINING_POOL."},{"tag":"ADDRESS_IS_RESTRICTED","type":"ENTITY","description":"Tag ADDRESS as \"restricted\". The reason is located on the tag_metadata field."},{"tag":"ADDRESS_IS_TREASURY","type":"ENTITY","description":"Tag the ADDRESS as TREASURY wallet."},{"tag":"ADDRESS_IS_CONTRACT","type":"ENTITY","description":"ADDRESS is Smart Contract. Fields timestamp_start/timestamp_end, block_hash/height_start/end can contain information when contract was created and destructed."},{"tag":"ADDRESS_IS_FUND","type":"ENTITY","description":"Tag the ADDRESS as FUND"},{"tag":"ADDRESS_IS_SWAPPER","type":"ENTITY","description":"ADDRESS is a Token Swapper"}]}"""
        assertResponse(200, expectedResponse, "/v4/blockchain-metadata/tags?type=ENTITY&api_key=$TEST_API_KEY")
    }

    @Test
    fun `should return list of entity association tags when type ENTITY_ASSOCIATION is specified`() {
        val expectedResponse =
            """{"data":[{"tag":"ENTITY_IDENTITY","type":"ENTITY_ASSOCIATION","description":"Source and destination entity are the same."},{"tag":"ENTITY_OWNERSHIP","type":"ENTITY_ASSOCIATION","description":"Associates pairs of Entities where the source entity is owned by the destination entity."}]}"""
        assertResponse(200, expectedResponse, "/v4/blockchain-metadata/tags?type=ENTITY_ASSOCIATION&api_key=$TEST_API_KEY")
    }

    @Test
    fun `should return list of location association tags when type LOCATION_ASSOCIATION is specified`() {
        val expectedResponse =
            """{"data":[{"tag":"LOCATION_IDENTITY","type":"LOCATION_ASSOCIATION","description":"Source Location and Destination Location are the same."},{"tag":"LOCATION_PARENT","type":"LOCATION_ASSOCIATION","description":"Source Location is parent of  Destination Location."}]}"""
        assertResponse(200, expectedResponse, "/v4/blockchain-metadata/tags?type=LOCATION_ASSOCIATION&api_key=$TEST_API_KEY")
    }

    @Test
    fun `should return 401 when key was not specified`() {
        val expectedResponse =
            """{"error":{"type":"unauthorized","message":"Requested resource requires authorization."}}"""
        assertResponse(401, expectedResponse, "/v4/blockchain-metadata/tags")
    }

    @Test
    fun `should return 401 when non existing key was specified`() {
        val expectedResponse =
            """{"error":{"type":"wrong_credentials","message":"Supplied credentials are not valid."}}"""
        assertResponse(401, expectedResponse, "/v4/blockchain-metadata/tags?api_key=foo_bar")
    }

    @Test
    fun `should return 403 when key has no access to tagging data`() {
        val expectedResponse =
            """{"error":{"type":"forbidden","message":"Requested resource is not available with supplied credentials."}}"""
        assertResponse(403, expectedResponse, "/v4/blockchain-metadata/tags?api_key=$COMMUNITY_KEY")
    }

    @Test
    fun `should return first page of tags when page size specified`() {
        val expectedResponse =
            """{"data":[{"tag":"ADDRESS_IS_COLD_WALLET","type":"ENTITY","description":"Tag the ADDRESS as COLD wallet."},{"tag":"ADDRESS_IS_CUSTODIAN","type":"ENTITY","description":"Tag the ADDRESS as CUSTODIAN."},{"tag":"ADDRESS_IS_DEPOSIT_WALLET","type":"ENTITY","description":"Tag the ADDRESS as DEPOSIT wallet."},{"tag":"ADDRESS_IS_EXCHANGE","type":"ENTITY","description":"Tag the ADDRESS as EXCHANGE."},{"tag":"ADDRESS_IS_GAMBLING","type":"ENTITY","description":"Tag the ADDRESS as GAMBLING."}],"next_page_token":"NQ","next_page_url":"http://127.0.0.1:8080/v4/blockchain-metadata/tags?api_key=$TEST_API_KEY&page_size=5&next_page_token=NQ"}"""
        assertResponse(200, expectedResponse, "/v4/blockchain-metadata/tags?api_key=$TEST_API_KEY&page_size=5")
    }

    @Test
    fun `should return second page of tags when page size specified`() {
        val expectedResponse =
            """{"data":[{"tag":"ADDRESS_IS_HISTORIC","type":"ENTITY","description":"Tag the ADDRESS as HISTORIC."},{"tag":"ADDRESS_IS_ICO","type":"ENTITY","description":"Tag the ADDRESS as ICO."},{"tag":"ADDRESS_IS_MINING","type":"ENTITY","description":"Tag the ADDRESS as a Miner"},{"tag":"ADDRESS_IS_MINING_POOL","type":"ENTITY","description":"Tag the ADDRESS as a MINING_POOL."},{"tag":"ADDRESS_IS_RESTRICTED","type":"ENTITY","description":"Tag ADDRESS as \"restricted\". The reason is located on the tag_metadata field."}],"next_page_token":"MTA","next_page_url":"http://127.0.0.1:8080/v4/blockchain-metadata/tags?api_key=$TEST_API_KEY&page_size=5&next_page_token=MTA"}"""
        assertResponse(200, expectedResponse, "/v4/blockchain-metadata/tags?api_key=$TEST_API_KEY&page_size=5&next_page_token=NQ")
    }

    @Test
    fun `should return last page of tags when page size specified`() {
        val expectedResponse =
            """{"data":[{"tag":"ENTITY_OWNERSHIP","type":"ENTITY_ASSOCIATION","description":"Associates pairs of Entities where the source entity is owned by the destination entity."},{"tag":"LOCATION_IDENTITY","type":"LOCATION_ASSOCIATION","description":"Source Location and Destination Location are the same."},{"tag":"LOCATION_PARENT","type":"LOCATION_ASSOCIATION","description":"Source Location is parent of  Destination Location."}]}"""
        assertResponse(200, expectedResponse, "/v4/blockchain-metadata/tags?api_key=$TEST_API_KEY&page_size=15&next_page_token=MTU")
    }

    @Test
    fun `should return 400 when next page token is invalid`() {
        val expectedResponse =
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'next_page_token'."}}"""
        assertResponse(400, expectedResponse, "/v4/blockchain-metadata/tags?api_key=$TEST_API_KEY&next_page_token=FOO")
    }
}
