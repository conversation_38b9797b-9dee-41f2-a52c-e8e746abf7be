package io.coinmetrics.api.endpoints.blockchain.list.balances

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.endpoints.GetBlockchainV2ListOfBalanceUpdatesRequest
import io.coinmetrics.api.models.PagingFrom
import io.coinmetrics.api.utils.QueryTextBuilder
import io.coinmetrics.api.utils.RangeQuery
import io.coinmetrics.httpserver.HttpRequest
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import java.math.BigInteger
import kotlin.coroutines.EmptyCoroutineContext

internal class BalanceUpdatesQueryBuilderTest {
    companion object {
        private val tableNamesProvider = { "public.{asset}_udm_v2_balance_updates" to "public.{asset}_udm_v2_stale_blocks" }
        private val keyNames = arrayOf("bu.chain_sequence_number", "bu.block_hash")
    }

    @Test
    fun `should use default query builder when no parameters specified`() {
        val request = getRequest(asset = "btc")
        assertEquals(
            """
            SELECT
                bu.*,
                NULL AS stale_block_height
            FROM public.{asset}_udm_v2_balance_updates bu
            WHERE TRUE AND bu.chain_sequence_number >= **************** AND bu.chain_sequence_number <= ****************    AND bu.block_hash NOT IN (SELECT block_hash FROM public.{asset}_udm_v2_stale_blocks)
            ORDER BY bu.chain_sequence_number DESC, bu.block_hash DESC
            LIMIT 100;
            """.trimIndent(),
            buildQuery(request),
        )
    }

    @Test
    fun `should use default query builder when only accounts specified`() {
        val request =
            getRequest(
                asset = "btc",
                accounts = listOf("**********************************", "**********************************"),
            )
        assertEquals(
            """
            SELECT 
                bu.*, 
                NULL AS stale_block_height 
            FROM public.{asset}_udm_v2_balance_updates bu, (
                WITH a(account) AS ( VALUES ('\x333834504c346435645a6753667163737474435136747a4c5247544e534d746f557a'),('\x31446469435367384a64334167705337646e5166636136317843643233706547377a') )
                SELECT bu.*
                FROM a
                    JOIN LATERAL ( 
                        SELECT chain_sequence_number, block_hash 
                        FROM public.{asset}_udm_v2_balance_updates bu
                        WHERE bu.account = a.account::bytea AND bu.chain_sequence_number >= **************** AND bu.chain_sequence_number <= ****************   AND bu.block_hash NOT IN (SELECT block_hash FROM public.{asset}_udm_v2_stale_blocks)
                        ORDER BY bu.chain_sequence_number DESC, bu.block_hash DESC
                        LIMIT 100
                    ) bu ON true
                WHERE TRUE AND bu.chain_sequence_number >= **************** AND bu.chain_sequence_number <= ****************
                ORDER BY bu.chain_sequence_number DESC, bu.block_hash DESC
                LIMIT 100
            ) AS seq_block_ordered 
            WHERE bu.chain_sequence_number = seq_block_ordered.chain_sequence_number AND bu.block_hash = seq_block_ordered.block_hash 
            ORDER BY bu.chain_sequence_number DESC, bu.block_hash DESC
            LIMIT 100;
            """.trimIndent(),
            buildQuery(request),
        )
    }

    @Test
    fun `should use specific query builder when both accounts and limit_per_account specified`() {
        val request =
            getRequest(
                asset = "btc",
                accounts = listOf("**********************************", "**********************************"),
                limitPerAccount = 2,
            )
        assertEquals(
            """
            WITH a(account) AS (VALUES ('\x333834504c346435645a6753667163737474435136747a4c5247544e534d746f557a'),('\x31446469435367384a64334167705337646e5166636136317843643233706547377a'))
            SELECT bu.*
            FROM a
            JOIN LATERAL ( 
                SELECT 
                    bu.*, 
                    NULL AS stale_block_height
                FROM public.{asset}_udm_v2_balance_updates bu
                WHERE TRUE
                     AND bu.chain_sequence_number > **************** AND bu.chain_sequence_number < ****************  
                    AND bu.account = a.account::bytea
                    AND bu.block_hash NOT IN (SELECT block_hash FROM public.{asset}_udm_v2_stale_blocks)
                ORDER BY bu.chain_sequence_number DESC, bu.block_hash DESC
                LIMIT 2
                 ) bu ON true
            WHERE TRUE AND bu.chain_sequence_number >= **************** AND bu.chain_sequence_number <= ****************
            ORDER BY bu.chain_sequence_number DESC, bu.block_hash DESC
            LIMIT 100;
            """.trimIndent(),
            buildQuery(request),
        )
    }

    @Test
    fun `should fail to build query because accounts are invalid`() {
        val request =
            getRequest(
                asset = "etc",
                accounts = listOf("abc", "def"),
                limitPerAccount = 2,
            )

        when (val result = tryToBuildQuery(request)) {
            is FunctionResult.Success -> throw AssertionError("Should fail to build query.")
            is FunctionResult.Failure ->
                assertEquals(
                    "Bad parameter 'accounts'. Invalid account specified: 'abc'.",
                    result.value.message,
                )
        }
    }

    private fun tryToBuildQuery(request: GetBlockchainV2ListOfBalanceUpdatesRequest): FunctionResult<ApiError, QueryTextBuilder> = balanceUpdatesQueryBuilder(request, rangeQuery(), keyNames, tableNamesProvider)

    private fun buildQuery(request: GetBlockchainV2ListOfBalanceUpdatesRequest): String =
        balanceUpdatesQueryBuilder(request, rangeQuery(), keyNames, tableNamesProvider).getOrElse {
            throw AssertionError("Failed to build query: $it.")
        }("AND bu.chain_sequence_number >= **************** AND bu.chain_sequence_number <= ****************", 100)

    private fun getRequest(
        asset: String,
        accounts: List<String>? = null,
        limitPerAccount: Int? = null,
        subAccounts: List<String>? = null,
        txids: List<String>? = null,
        blockHashes: List<String>? = null,
    ) = GetBlockchainV2ListOfBalanceUpdatesRequest(
        httpRequest = dummyHttpRequest(),
        apiKey = "",
        asset = asset,
        accounts = accounts,
        limitPerAccount = limitPerAccount,
        subAccounts = subAccounts,
        txids = txids,
        blockHashes = blockHashes,
        startTime = null,
        endTime = null,
        startHeight = null,
        endHeight = null,
        startChainSequenceNumber = null,
        endChainSequenceNumber = null,
        nextPageToken = null,
    )

    private fun dummyHttpRequest() =
        HttpRequest(
            method = "GET",
            path = "/",
            headers = emptyMap(),
            queryParameters = emptyMap(),
            timeSec = 0,
            coroutineContext = EmptyCoroutineContext,
        )

    private fun rangeQuery(): RangeQuery.BigIntegerAndStringRangeQuery =
        RangeQuery.BigIntegerAndStringRangeQuery(
            startKey1 = BigInteger("****************"),
            endKey1 = BigInteger("****************"),
            startKey2 = null,
            endKey2 = null,
            startInclusive = false,
            endInclusive = false,
            pagingFrom = PagingFrom.END,
        )
}
