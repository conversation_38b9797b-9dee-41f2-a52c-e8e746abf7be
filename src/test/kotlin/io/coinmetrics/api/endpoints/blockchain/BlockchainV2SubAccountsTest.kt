package io.coinmetrics.api.endpoints.blockchain

import io.coinmetrics.api.helper.BaseTest
import io.coinmetrics.api.helper.COMMUNITY_KEY
import io.coinmetrics.api.helper.EMPTY_RESPONSE
import io.coinmetrics.api.helper.TEST_API_KEY
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class BlockchainV2SubAccountsTest : BaseTest() {
    @Test
    fun `should return 403 when trying to access sub-accounts with forbidden key`() {
        val expectedResponse =
            """{"error":{"type":"forbidden","message":"Requested resource is not available with supplied credentials."}}"""
        assertResponse(
            403,
            expectedResponse,
            "/v4/blockchain-v2/btc/sub-accounts?api_key=x2",
        )
    }

    @Test
    fun `should return 400 trying to get sub-accounts for non-existing asset`() {
        val expectedResponse =
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'asset'. Value 'btcxxx' is not supported."}}"""
        assertResponse(
            400,
            expectedResponse,
            "/v4/blockchain-v2/btcxxx/sub-accounts?api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `should return 400 trying to get sub-accounts for non-existing in our db asset`() {
        val expectedResponse =
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'asset'. Value 'bch' is not supported."}}"""
        assertResponse(
            expectedCode = 400,
            expectedResponse = expectedResponse,
            pathAndQuery = "/v4/blockchain-v2/bch/sub-accounts?api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `should return empty list of all sub-accounts as ETH table exists but is empty`() {
        assertResponse(
            expectedCode = 200,
            expectedResponse = EMPTY_RESPONSE,
            pathAndQuery = "/v4/blockchain-v2/eth/sub-accounts?api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `should return list of all sub-accounts for btc asset`() {
        val expectedResponse =
            """{"data":[{"sub_account":"303d3687eea9fd7ff9e89891c4795a76a849b111e73fbbdd0014d40ac0d8e75c-0","account":"**********************************","type":"UTXO","creation_height":"92066","creation_time":"2010-11-15T21:36:19.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0005636a05402caa5e3e22850face70e999c987751cc29f258207b1b"},{"sub_account":"3da8739218013c3271079ed9030266807359c5cb4d85d37fc459d34d61667179-0","account":"**********************************","type":"UTXO","creation_height":"92066","creation_time":"2010-11-15T21:36:19.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0005636a05402caa5e3e22850face70e999c987751cc29f258207b1b"},{"sub_account":"b4d1e49d6e18f3b811a79d58c1c3af234e53c8721568cc2a865cec2d6b13a2e8-0","account":"**********************************","type":"UTXO","creation_height":"92067","creation_time":"2010-11-15T22:06:47.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0006f9b809365a295225ec109b1e76fcb16f040bed626211ce9ce7c8"},{"sub_account":"b5cb830ba0c5ea696709121530357a2ab0154bbb95add2393f34eb662eb828df-0","account":"**********************************","type":"UTXO","creation_height":"92068","creation_time":"2010-11-15T22:22:30.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********000c10491f16d464f138e9ca3fd335ac97e0e7e1bae94cd1eb9b6e66"},{"sub_account":"112a7bd80b1c24127713c89b3d2da59342ff7d8eed140b30a27c9fcdf416c0c2-1","account":"*********************************","type":"UTXO","creation_height":"92069","creation_time":"2010-11-15T22:26:23.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********000c890177eb8e97152b433882c0dbae361e6615f369e9f4cf060974"},{"sub_account":"f57cd9ca26f16c0ab628d281f3c56c6c95e12977f3e41304ccae98f5f414a4d5-0","account":"**********************************","type":"UTXO","creation_height":"92070","creation_time":"2010-11-15T22:28:35.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0005592cb78aa7384677543971f05844e07f1d04bac5594d93ab7405"},{"sub_account":"445bce50671a88ae98a7770120bfbde1aa53e3eb007ea96bb860d0b501117a16-0","account":"1N6PPxZ1wQVTeG4tHJqb3C5LjW5i5sSxGE","type":"UTXO","creation_height":"92071","creation_time":"2010-11-15T22:31:48.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0008d2fd5fc7ad76a7b59ad85f093154d7df1eb036706f0866aa7fe9"},{"sub_account":"9663bdb2cb0afb5386527332931bb472d5926e011eb4bcad2d67e1360eeb8bb9-0","account":"1Etz42nbWwDZ4yavdFsrEtZu5MzU8TGTcc","type":"UTXO","creation_height":"92071","creation_time":"2010-11-15T22:31:48.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0008d2fd5fc7ad76a7b59ad85f093154d7df1eb036706f0866aa7fe9"},{"sub_account":"9beaef247e1adfc9e877431390cee1b0ce428c6c7424396b3e893abbd9a61a9b-0","account":"1BPEotGorsfkkeHRaFQHnP7pC7AUKPJLh7","type":"UTXO","creation_height":"92071","creation_time":"2010-11-15T22:31:48.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0008d2fd5fc7ad76a7b59ad85f093154d7df1eb036706f0866aa7fe9"},{"sub_account":"c1c10c6ac940ad8e2bc01568e5e7b9cd4f8deb96e1e9058580f875b84ba14107-0","account":"**********************************","type":"UTXO","creation_height":"92071","creation_time":"2010-11-15T22:31:48.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0008d2fd5fc7ad76a7b59ad85f093154d7df1eb036706f0866aa7fe9"},{"sub_account":"c52de017cf8cd8c28704764cf3831092e719f181394d4820afe1de208c73ec50-0","account":"**********************************","type":"UTXO","creation_height":"92071","creation_time":"2010-11-15T22:31:48.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0008d2fd5fc7ad76a7b59ad85f093154d7df1eb036706f0866aa7fe9"},{"sub_account":"dc34d86e6727db756f40d35558d3750767677ca75ab4554b761b3b6536b9091f-0","account":"1AF21NWaF75wRMT8T27qyXuABinxC2qJsV","type":"UTXO","creation_height":"92072","creation_time":"2010-11-15T22:41:10.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********000812b75d5b4dcbd89a07788ade7cb9687457dac997d0669b4f8af6"},{"sub_account":"0c99b60b80405841ef91ab74223bb517e633f50fd9d6b6110d45c6b55ddf4e91-0","account":"**********************************","type":"UTXO","creation_height":"92073","creation_time":"2010-11-15T23:09:06.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0005b3fb679b2758c2cdd3311c0f483c639812039c8075acbf3b59ae"},{"sub_account":"7efa275918efe617f642c3f31beef234b8e876c233250c46a103befe2a26049f-0","account":"**********************************","type":"UTXO","creation_height":"92073","creation_time":"2010-11-15T23:09:06.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0005b3fb679b2758c2cdd3311c0f483c639812039c8075acbf3b59ae"},{"sub_account":"7efa275918efe617f642c3f31beef234b8e876c233250c46a103befe2a26049f-1","account":"**********************************","type":"UTXO","creation_height":"92073","creation_time":"2010-11-15T23:09:06.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0005b3fb679b2758c2cdd3311c0f483c639812039c8075acbf3b59ae"},{"sub_account":"e6311fc17b4bdc29f40a35dc20e4dfdcd5dec24df6c70bbd9470b2b80eb26863-0","account":"**********************************","type":"UTXO","creation_height":"92074","creation_time":"2010-11-15T23:22:22.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0005707b17cf760e1035d9e19a3001ab0fe4dbbdc206bd48b692813c"},{"sub_account":"ba2031ac0a1d21a167638fe4b91a1bce4492c3a22ced4c7afd3795f19e629064-0","account":"**********************************","type":"UTXO","creation_height":"92075","creation_time":"2010-11-15T23:24:07.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0006b584590d83cb9a991f3bd5d8511b4abdbfea05e65590cbe23093"},{"sub_account":"e49703b2797bf72dc3fc0aae0c7c40fbca267a9bc6669e086162d5ec76cca4d0-0","account":"**********************************","type":"UTXO","creation_height":"92075","creation_time":"2010-11-15T23:24:07.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0006b584590d83cb9a991f3bd5d8511b4abdbfea05e65590cbe23093"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain-v2/btc/sub-accounts?api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `should return empty list of sub-accounts filtered by non-existing accounts for btc asset`() {
        assertResponse(
            200,
            EMPTY_RESPONSE,
            "/v4/blockchain-v2/btc/sub-accounts?api_key=$TEST_API_KEY&accounts=**********************************,**********************************",
        )
    }

    @Test
    fun `should return list of sub-accounts filtered by accounts list for btc asset`() {
        val expectedResponse =
            """{"data":[{"sub_account":"303d3687eea9fd7ff9e89891c4795a76a849b111e73fbbdd0014d40ac0d8e75c-0","account":"**********************************","type":"UTXO","creation_height":"92066","creation_time":"2010-11-15T21:36:19.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0005636a05402caa5e3e22850face70e999c987751cc29f258207b1b"},{"sub_account":"3da8739218013c3271079ed9030266807359c5cb4d85d37fc459d34d61667179-0","account":"**********************************","type":"UTXO","creation_height":"92066","creation_time":"2010-11-15T21:36:19.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0005636a05402caa5e3e22850face70e999c987751cc29f258207b1b"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain-v2/btc/sub-accounts?api_key=$TEST_API_KEY&accounts=**********************************",
        )
    }

    @Test
    fun `should return empty list of sub-accounts when filtered by invalid accounts for btc asset`() {
        assertResponse(
            expectedCode = 200,
            expectedResponse = EMPTY_RESPONSE,
            pathAndQuery = "/v4/blockchain-v2/btc/sub-accounts?api_key=$TEST_API_KEY&accounts=123,456",
        )
    }

    @Test
    fun `should return 400 when all time filters specified`() {
        val expectedResponse =
            """{"error":{"type":"bad_parameters","message":"Filters '*_time', '*_height' and '*_chain_sequence_number' are mutually exclusive and can't be specified in the same request."}}"""
        assertResponse(
            400,
            expectedResponse,
            "/v4/blockchain-v2/btc/sub-accounts?api_key=$TEST_API_KEY&start_time=2006-01-20&start_height=462031&start_chain_sequence_number=****************",
        )
    }

    @Test
    fun `should return 400 when height and chain sequence number filters specified`() {
        val expectedResponse =
            """{"error":{"type":"bad_parameters","message":"Filters '*_height' and '*_chain_sequence_number' are mutually exclusive and can't be specified in the same request."}}"""
        assertResponse(
            400,
            expectedResponse,
            "/v4/blockchain-v2/btc/sub-accounts?api_key=$TEST_API_KEY&start_height=462031&start_chain_sequence_number=****************",
        )
    }

    @Test
    fun `should return 400 when time and chain sequence number filters specified`() {
        val expectedResponse =
            """{"error":{"type":"bad_parameters","message":"Filters '*_time' and '*_chain_sequence_number' are mutually exclusive and can't be specified in the same request."}}"""
        assertResponse(
            400,
            expectedResponse,
            "/v4/blockchain-v2/btc/sub-accounts?api_key=$TEST_API_KEY&start_time=2006-01-20&start_chain_sequence_number=****************",
        )
    }

    @Test
    fun `should return list of sub-accounts filtered by start_height for btc asset`() {
        val expectedResponse =
            """{"data":[{"sub_account":"e6311fc17b4bdc29f40a35dc20e4dfdcd5dec24df6c70bbd9470b2b80eb26863-0","account":"**********************************","type":"UTXO","creation_height":"92074","creation_time":"2010-11-15T23:22:22.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0005707b17cf760e1035d9e19a3001ab0fe4dbbdc206bd48b692813c"},{"sub_account":"ba2031ac0a1d21a167638fe4b91a1bce4492c3a22ced4c7afd3795f19e629064-0","account":"**********************************","type":"UTXO","creation_height":"92075","creation_time":"2010-11-15T23:24:07.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0006b584590d83cb9a991f3bd5d8511b4abdbfea05e65590cbe23093"},{"sub_account":"e49703b2797bf72dc3fc0aae0c7c40fbca267a9bc6669e086162d5ec76cca4d0-0","account":"**********************************","type":"UTXO","creation_height":"92075","creation_time":"2010-11-15T23:24:07.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0006b584590d83cb9a991f3bd5d8511b4abdbfea05e65590cbe23093"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain-v2/btc/sub-accounts?api_key=$TEST_API_KEY&start_height=92074",
        )
    }

    @Test
    fun `should return list of sub-accounts filtered by start_height and accounts for btc asset`() {
        val expectedResponse =
            """{"data":[{"sub_account":"e6311fc17b4bdc29f40a35dc20e4dfdcd5dec24df6c70bbd9470b2b80eb26863-0","account":"**********************************","type":"UTXO","creation_height":"92074","creation_time":"2010-11-15T23:22:22.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0005707b17cf760e1035d9e19a3001ab0fe4dbbdc206bd48b692813c"},{"sub_account":"ba2031ac0a1d21a167638fe4b91a1bce4492c3a22ced4c7afd3795f19e629064-0","account":"**********************************","type":"UTXO","creation_height":"92075","creation_time":"2010-11-15T23:24:07.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0006b584590d83cb9a991f3bd5d8511b4abdbfea05e65590cbe23093"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain-v2/btc/sub-accounts?api_key=$TEST_API_KEY&start_height=92074&accounts=**********************************,**********************************",
        )
    }

    @Test
    fun `should return list of sub-accounts filtered by start_height and end_height for btc asset`() {
        val expectedResponse =
            """{"data":[{"sub_account":"0c99b60b80405841ef91ab74223bb517e633f50fd9d6b6110d45c6b55ddf4e91-0","account":"**********************************","type":"UTXO","creation_height":"92073","creation_time":"2010-11-15T23:09:06.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0005b3fb679b2758c2cdd3311c0f483c639812039c8075acbf3b59ae"},{"sub_account":"7efa275918efe617f642c3f31beef234b8e876c233250c46a103befe2a26049f-0","account":"**********************************","type":"UTXO","creation_height":"92073","creation_time":"2010-11-15T23:09:06.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0005b3fb679b2758c2cdd3311c0f483c639812039c8075acbf3b59ae"},{"sub_account":"7efa275918efe617f642c3f31beef234b8e876c233250c46a103befe2a26049f-1","account":"**********************************","type":"UTXO","creation_height":"92073","creation_time":"2010-11-15T23:09:06.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0005b3fb679b2758c2cdd3311c0f483c639812039c8075acbf3b59ae"},{"sub_account":"e6311fc17b4bdc29f40a35dc20e4dfdcd5dec24df6c70bbd9470b2b80eb26863-0","account":"**********************************","type":"UTXO","creation_height":"92074","creation_time":"2010-11-15T23:22:22.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0005707b17cf760e1035d9e19a3001ab0fe4dbbdc206bd48b692813c"},{"sub_account":"ba2031ac0a1d21a167638fe4b91a1bce4492c3a22ced4c7afd3795f19e629064-0","account":"**********************************","type":"UTXO","creation_height":"92075","creation_time":"2010-11-15T23:24:07.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0006b584590d83cb9a991f3bd5d8511b4abdbfea05e65590cbe23093"},{"sub_account":"e49703b2797bf72dc3fc0aae0c7c40fbca267a9bc6669e086162d5ec76cca4d0-0","account":"**********************************","type":"UTXO","creation_height":"92075","creation_time":"2010-11-15T23:24:07.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0006b584590d83cb9a991f3bd5d8511b4abdbfea05e65590cbe23093"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain-v2/btc/sub-accounts?api_key=$TEST_API_KEY&start_height=92073&end_height=92075",
        )
    }

    @Test
    fun `should return empty list of sub-accounts when there is no data in specified heights range`() {
        assertResponse(
            200,
            EMPTY_RESPONSE,
            "/v4/blockchain-v2/btc/sub-accounts?api_key=$TEST_API_KEY&start_height=1&end_height=2",
        )
    }

    @Test
    fun `should return 400 when start_height is greater than end_height`() {
        val expectedResponse =
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'start_height'. Start height is less than end height."}}"""
        assertResponse(
            400,
            expectedResponse,
            "/v4/blockchain-v2/btc/sub-accounts?api_key=$TEST_API_KEY&start_height=485862&end_height=480777",
        )
    }

    @Test
    fun `CORNER CASE should return last 2 sub-accounts filtered by start_height with start_inclusive=true`() {
        val expectedResponse =
            """{"data":[{"sub_account":"ba2031ac0a1d21a167638fe4b91a1bce4492c3a22ced4c7afd3795f19e629064-0","account":"**********************************","type":"UTXO","creation_height":"92075","creation_time":"2010-11-15T23:24:07.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0006b584590d83cb9a991f3bd5d8511b4abdbfea05e65590cbe23093"},{"sub_account":"e49703b2797bf72dc3fc0aae0c7c40fbca267a9bc6669e086162d5ec76cca4d0-0","account":"**********************************","type":"UTXO","creation_height":"92075","creation_time":"2010-11-15T23:24:07.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0006b584590d83cb9a991f3bd5d8511b4abdbfea05e65590cbe23093"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain-v2/btc/sub-accounts?api_key=$TEST_API_KEY&start_height=92075&start_inclusive=true",
        )
    }

    @Test
    fun `CORNER CASE should not return last sub-account filtered by start_height with start_inclusive=false`() {
        assertResponse(
            200,
            EMPTY_RESPONSE,
            "/v4/blockchain-v2/btc/sub-accounts?api_key=$TEST_API_KEY&start_height=92075&start_inclusive=false",
        )
    }

    @Test
    fun `CORNER CASE should return first 2 sub-accounts filtered by end_height with end_inclusive=true`() {
        val expectedResponse =
            """{"data":[{"sub_account":"303d3687eea9fd7ff9e89891c4795a76a849b111e73fbbdd0014d40ac0d8e75c-0","account":"**********************************","type":"UTXO","creation_height":"92066","creation_time":"2010-11-15T21:36:19.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0005636a05402caa5e3e22850face70e999c987751cc29f258207b1b"},{"sub_account":"3da8739218013c3271079ed9030266807359c5cb4d85d37fc459d34d61667179-0","account":"**********************************","type":"UTXO","creation_height":"92066","creation_time":"2010-11-15T21:36:19.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0005636a05402caa5e3e22850face70e999c987751cc29f258207b1b"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain-v2/btc/sub-accounts?api_key=$TEST_API_KEY&end_height=92066&end_inclusive=true",
        )
    }

    @Test
    fun `CORNER CASE should not return first sub-account filtered by end_height with end_inclusive=false`() {
        assertResponse(
            200,
            EMPTY_RESPONSE,
            "/v4/blockchain-v2/btc/sub-accounts?api_key=$TEST_API_KEY&end_height=84263&end_inclusive=false",
        )
    }

    @Test
    fun `should return first sub-accounts page filtered by start_height with page_size=3 and paging_from=start`() {
        val expectedResponse =
            """{"data":[{"sub_account":"0c99b60b80405841ef91ab74223bb517e633f50fd9d6b6110d45c6b55ddf4e91-0","account":"**********************************","type":"UTXO","creation_height":"92073","creation_time":"2010-11-15T23:09:06.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0005b3fb679b2758c2cdd3311c0f483c639812039c8075acbf3b59ae"},{"sub_account":"7efa275918efe617f642c3f31beef234b8e876c233250c46a103befe2a26049f-0","account":"**********************************","type":"UTXO","creation_height":"92073","creation_time":"2010-11-15T23:09:06.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0005b3fb679b2758c2cdd3311c0f483c639812039c8075acbf3b59ae"},{"sub_account":"7efa275918efe617f642c3f31beef234b8e876c233250c46a103befe2a26049f-1","account":"**********************************","type":"UTXO","creation_height":"92073","creation_time":"2010-11-15T23:09:06.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0005b3fb679b2758c2cdd3311c0f483c639812039c8075acbf3b59ae"}],"next_page_token":"*******************************************************************","next_page_url":"http://127.0.0.1:8080/v4/blockchain-v2/btc/sub-accounts?api_key=x1&start_height=92073&page_size=3&paging_from=start&next_page_token=*******************************************************************"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain-v2/btc/sub-accounts?api_key=$TEST_API_KEY&start_height=92073&page_size=3&paging_from=start",
        )
    }

    @Test
    fun `should return second sub-accounts page filtered by start_height with page_size=3 and paging_from=start`() {
        val expectedResponse =
            """{"data":[{"sub_account":"e6311fc17b4bdc29f40a35dc20e4dfdcd5dec24df6c70bbd9470b2b80eb26863-0","account":"**********************************","type":"UTXO","creation_height":"92074","creation_time":"2010-11-15T23:22:22.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0005707b17cf760e1035d9e19a3001ab0fe4dbbdc206bd48b692813c"},{"sub_account":"ba2031ac0a1d21a167638fe4b91a1bce4492c3a22ced4c7afd3795f19e629064-0","account":"**********************************","type":"UTXO","creation_height":"92075","creation_time":"2010-11-15T23:24:07.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0006b584590d83cb9a991f3bd5d8511b4abdbfea05e65590cbe23093"},{"sub_account":"e49703b2797bf72dc3fc0aae0c7c40fbca267a9bc6669e086162d5ec76cca4d0-0","account":"**********************************","type":"UTXO","creation_height":"92075","creation_time":"2010-11-15T23:24:07.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0006b584590d83cb9a991f3bd5d8511b4abdbfea05e65590cbe23093"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain-v2/btc/sub-accounts?api_key=$TEST_API_KEY&start_height=92073&page_size=3&paging_from=start&next_page_token=*******************************************************************",
        )
    }

    @Test
    fun `should return list of sub-accounts filtered by start_chain_sequence_number for btc asset`() {
        val expectedResponse =
            """{"data":[{"sub_account":"e6311fc17b4bdc29f40a35dc20e4dfdcd5dec24df6c70bbd9470b2b80eb26863-0","account":"**********************************","type":"UTXO","creation_height":"92074","creation_time":"2010-11-15T23:22:22.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0005707b17cf760e1035d9e19a3001ab0fe4dbbdc206bd48b692813c"},{"sub_account":"ba2031ac0a1d21a167638fe4b91a1bce4492c3a22ced4c7afd3795f19e629064-0","account":"**********************************","type":"UTXO","creation_height":"92075","creation_time":"2010-11-15T23:24:07.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0006b584590d83cb9a991f3bd5d8511b4abdbfea05e65590cbe23093"},{"sub_account":"e49703b2797bf72dc3fc0aae0c7c40fbca267a9bc6669e086162d5ec76cca4d0-0","account":"**********************************","type":"UTXO","creation_height":"92075","creation_time":"2010-11-15T23:24:07.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0006b584590d83cb9a991f3bd5d8511b4abdbfea05e65590cbe23093"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain-v2/btc/sub-accounts?api_key=$TEST_API_KEY&start_chain_sequence_number=***************",
        )
    }

    @Test
    fun `should return list of sub-accounts filtered by start_chain_sequence_number and end_chain_sequence_number for btc asset`() {
        val expectedResponse =
            """{"data":[{"sub_account":"7efa275918efe617f642c3f31beef234b8e876c233250c46a103befe2a26049f-0","account":"**********************************","type":"UTXO","creation_height":"92073","creation_time":"2010-11-15T23:09:06.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0005b3fb679b2758c2cdd3311c0f483c639812039c8075acbf3b59ae"},{"sub_account":"7efa275918efe617f642c3f31beef234b8e876c233250c46a103befe2a26049f-1","account":"**********************************","type":"UTXO","creation_height":"92073","creation_time":"2010-11-15T23:09:06.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0005b3fb679b2758c2cdd3311c0f483c639812039c8075acbf3b59ae"},{"sub_account":"e6311fc17b4bdc29f40a35dc20e4dfdcd5dec24df6c70bbd9470b2b80eb26863-0","account":"**********************************","type":"UTXO","creation_height":"92074","creation_time":"2010-11-15T23:22:22.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0005707b17cf760e1035d9e19a3001ab0fe4dbbdc206bd48b692813c"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain-v2/btc/sub-accounts?api_key=$TEST_API_KEY&start_chain_sequence_number=***************&end_chain_sequence_number=***************",
        )
    }

    @Test
    fun `should return 400 when start_chain_sequence_number is greater than end_chain_sequence_number`() {
        val expectedResponse =
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'start_chain_sequence_number'. Parameter 'start_chain_sequence_number' must be greater than 'end_chain_sequence_number'."}}"""
        assertResponse(
            400,
            expectedResponse,
            "/v4/blockchain-v2/btc/sub-accounts?api_key=$TEST_API_KEY&start_chain_sequence_number=****************&end_chain_sequence_number=****************",
        )
    }

    @Test
    fun `CORNER CASE should return last sub-account filtered by start_chain_sequence_number with start_inclusive=true`() {
        val expectedResponse =
            """{"data":[{"sub_account":"e49703b2797bf72dc3fc0aae0c7c40fbca267a9bc6669e086162d5ec76cca4d0-0","account":"**********************************","type":"UTXO","creation_height":"92075","creation_time":"2010-11-15T23:24:07.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0006b584590d83cb9a991f3bd5d8511b4abdbfea05e65590cbe23093"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain-v2/btc/sub-accounts?api_key=$TEST_API_KEY&start_chain_sequence_number=***************&start_inclusive=true",
        )
    }

    @Test
    fun `CORNER CASE should not return last sub-account filtered by start_chain_sequence_number with start_inclusive=false`() {
        assertResponse(
            200,
            EMPTY_RESPONSE,
            "/v4/blockchain-v2/btc/sub-accounts?api_key=$TEST_API_KEY&start_chain_sequence_number=***************&start_inclusive=false",
        )
    }

    @Test
    fun `CORNER CASE should return first sub-account filtered by end_chain_sequence_number with end_inclusive=true`() {
        val expectedResponse =
            """{"data":[{"sub_account":"303d3687eea9fd7ff9e89891c4795a76a849b111e73fbbdd0014d40ac0d8e75c-0","account":"**********************************","type":"UTXO","creation_height":"92066","creation_time":"2010-11-15T21:36:19.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0005636a05402caa5e3e22850face70e999c987751cc29f258207b1b"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain-v2/btc/sub-accounts?api_key=$TEST_API_KEY&end_chain_sequence_number=***************&end_inclusive=true",
        )
    }

    @Test
    fun `CORNER CASE should not return first sub-account filtered by end_chain_sequence_number with end_inclusive=false`() {
        assertResponse(
            200,
            EMPTY_RESPONSE,
            "/v4/blockchain-v2/btc/sub-accounts?api_key=$TEST_API_KEY&end_chain_sequence_number=***************&end_inclusive=false",
        )
    }

    @Test
    fun `should return first sub-accounts page filtered by start_chain_sequence_number with page_size=2 and paging_from=start`() {
        val expectedResponse =
            """{"data":[{"sub_account":"3da8739218013c3271079ed9030266807359c5cb4d85d37fc459d34d61667179-0","account":"**********************************","type":"UTXO","creation_height":"92066","creation_time":"2010-11-15T21:36:19.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0005636a05402caa5e3e22850face70e999c987751cc29f258207b1b"},{"sub_account":"b4d1e49d6e18f3b811a79d58c1c3af234e53c8721568cc2a865cec2d6b13a2e8-0","account":"**********************************","type":"UTXO","creation_height":"92067","creation_time":"2010-11-15T22:06:47.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0006f9b809365a295225ec109b1e76fcb16f040bed626211ce9ce7c8"}],"next_page_token":"*******************************************************************","next_page_url":"http://127.0.0.1:8080/v4/blockchain-v2/btc/sub-accounts?api_key=x1&start_chain_sequence_number=***************&page_size=2&paging_from=start&next_page_token=*******************************************************************"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain-v2/btc/sub-accounts?api_key=$TEST_API_KEY&start_chain_sequence_number=***************&page_size=2&paging_from=start",
        )
    }

    @Test
    fun `should return second sub-accounts page filtered by start_height with page_size=2 and paging_from=start`() {
        val expectedResponse =
            """{"data":[{"sub_account":"b5cb830ba0c5ea696709121530357a2ab0154bbb95add2393f34eb662eb828df-0","account":"**********************************","type":"UTXO","creation_height":"92068","creation_time":"2010-11-15T22:22:30.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********000c10491f16d464f138e9ca3fd335ac97e0e7e1bae94cd1eb9b6e66"},{"sub_account":"112a7bd80b1c24127713c89b3d2da59342ff7d8eed140b30a27c9fcdf416c0c2-1","account":"*********************************","type":"UTXO","creation_height":"92069","creation_time":"2010-11-15T22:26:23.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********000c890177eb8e97152b433882c0dbae361e6615f369e9f4cf060974"}],"next_page_token":"******************************************************************","next_page_url":"http://127.0.0.1:8080/v4/blockchain-v2/btc/sub-accounts?api_key=x1&start_chain_sequence_number=***************&page_size=2&paging_from=start&next_page_token=******************************************************************"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain-v2/btc/sub-accounts?api_key=$TEST_API_KEY&start_chain_sequence_number=***************&page_size=2&paging_from=start&next_page_token=*******************************************************************",
        )
    }

    @Test
    fun `should return list of sub-accounts filtered by start_time for btc asset`() {
        val expectedResponse =
            """{"data":[{"sub_account":"e6311fc17b4bdc29f40a35dc20e4dfdcd5dec24df6c70bbd9470b2b80eb26863-0","account":"**********************************","type":"UTXO","creation_height":"92074","creation_time":"2010-11-15T23:22:22.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0005707b17cf760e1035d9e19a3001ab0fe4dbbdc206bd48b692813c"},{"sub_account":"ba2031ac0a1d21a167638fe4b91a1bce4492c3a22ced4c7afd3795f19e629064-0","account":"**********************************","type":"UTXO","creation_height":"92075","creation_time":"2010-11-15T23:24:07.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0006b584590d83cb9a991f3bd5d8511b4abdbfea05e65590cbe23093"},{"sub_account":"e49703b2797bf72dc3fc0aae0c7c40fbca267a9bc6669e086162d5ec76cca4d0-0","account":"**********************************","type":"UTXO","creation_height":"92075","creation_time":"2010-11-15T23:24:07.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0006b584590d83cb9a991f3bd5d8511b4abdbfea05e65590cbe23093"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain-v2/btc/sub-accounts?api_key=$TEST_API_KEY&start_time=2010-11-15T23:22:22Z",
        )
    }

    @Test
    fun `should return list of sub-accounts filtered by start_time and end_time for btc asset`() {
        val expectedResponse =
            """{"data":[{"sub_account":"b5cb830ba0c5ea696709121530357a2ab0154bbb95add2393f34eb662eb828df-0","account":"**********************************","type":"UTXO","creation_height":"92068","creation_time":"2010-11-15T22:22:30.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********000c10491f16d464f138e9ca3fd335ac97e0e7e1bae94cd1eb9b6e66"},{"sub_account":"112a7bd80b1c24127713c89b3d2da59342ff7d8eed140b30a27c9fcdf416c0c2-1","account":"*********************************","type":"UTXO","creation_height":"92069","creation_time":"2010-11-15T22:26:23.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********000c890177eb8e97152b433882c0dbae361e6615f369e9f4cf060974"},{"sub_account":"f57cd9ca26f16c0ab628d281f3c56c6c95e12977f3e41304ccae98f5f414a4d5-0","account":"**********************************","type":"UTXO","creation_height":"92070","creation_time":"2010-11-15T22:28:35.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0005592cb78aa7384677543971f05844e07f1d04bac5594d93ab7405"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain-v2/btc/sub-accounts?api_key=$TEST_API_KEY&start_time=2010-11-15T22:22:30Z&end_time=2010-11-15T22:28:35Z",
        )
    }

    @Test
    fun `should return empty list of sub-accounts when there is no data in time range`() {
        assertResponse(
            200,
            EMPTY_RESPONSE,
            "/v4/blockchain-v2/btc/sub-accounts?api_key=$TEST_API_KEY&start_time=2020-09-18T06:31:16Z&end_time=2021-05-30T06:31:16Z",
        )
    }

    @Test
    fun `should return 400 when start_time is greater than end_time`() {
        val expectedResponse =
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'start_time'. Start time '2018-05-30T06:31:16Z' is later than the end time '2017-09-18T06:31:16Z'."}}"""
        assertResponse(
            400,
            expectedResponse,
            "/v4/blockchain-v2/btc/sub-accounts?api_key=$TEST_API_KEY&end_time=2017-09-18T06:31:16Z&start_time=2018-05-30T06:31:16Z",
        )
    }

    @Test
    fun `CORNER CASE should return last sub-accounts filtered by start_time with start_inclusive=true`() {
        val expectedResponse =
            """{"data":[{"sub_account":"ba2031ac0a1d21a167638fe4b91a1bce4492c3a22ced4c7afd3795f19e629064-0","account":"**********************************","type":"UTXO","creation_height":"92075","creation_time":"2010-11-15T23:24:07.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0006b584590d83cb9a991f3bd5d8511b4abdbfea05e65590cbe23093"},{"sub_account":"e49703b2797bf72dc3fc0aae0c7c40fbca267a9bc6669e086162d5ec76cca4d0-0","account":"**********************************","type":"UTXO","creation_height":"92075","creation_time":"2010-11-15T23:24:07.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0006b584590d83cb9a991f3bd5d8511b4abdbfea05e65590cbe23093"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain-v2/btc/sub-accounts?api_key=$TEST_API_KEY&start_time=2010-11-15T23:24:07Z&start_inclusive=true",
        )
    }

    @Test
    fun `CORNER CASE should not return last sub-account filtered by start_time with start_inclusive=false`() {
        assertResponse(
            200,
            EMPTY_RESPONSE,
            "/v4/blockchain-v2/btc/sub-accounts?api_key=$TEST_API_KEY&start_time=2020-08-23T19:46:02Z&start_inclusive=false",
        )
    }

    @Test
    fun `CORNER CASE should return first sub-account filtered by end_time with end_inclusive=true`() {
        val expectedResponse =
            """{"data":[{"sub_account":"303d3687eea9fd7ff9e89891c4795a76a849b111e73fbbdd0014d40ac0d8e75c-0","account":"**********************************","type":"UTXO","creation_height":"92066","creation_time":"2010-11-15T21:36:19.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0005636a05402caa5e3e22850face70e999c987751cc29f258207b1b"},{"sub_account":"3da8739218013c3271079ed9030266807359c5cb4d85d37fc459d34d61667179-0","account":"**********************************","type":"UTXO","creation_height":"92066","creation_time":"2010-11-15T21:36:19.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0005636a05402caa5e3e22850face70e999c987751cc29f258207b1b"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain-v2/btc/sub-accounts?api_key=$TEST_API_KEY&end_time=2010-11-15T21:36:19Z&end_inclusive=true",
        )
    }

    @Test
    fun `CORNER CASE should not return first sub-accounts filtered by end_time with end_inclusive=false`() {
        assertResponse(
            200,
            EMPTY_RESPONSE,
            "/v4/blockchain-v2/btc/sub-accounts?api_key=$TEST_API_KEY&end_time=2010-11-15T21:36:19Z&end_inclusive=false",
        )
    }

    @Test
    fun `should return first sub-accounts page filtered by start_time with page_size=2 and paging_from=start`() {
        val expectedResponse =
            """{"data":[{"sub_account":"b4d1e49d6e18f3b811a79d58c1c3af234e53c8721568cc2a865cec2d6b13a2e8-0","account":"**********************************","type":"UTXO","creation_height":"92067","creation_time":"2010-11-15T22:06:47.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0006f9b809365a295225ec109b1e76fcb16f040bed626211ce9ce7c8"},{"sub_account":"b5cb830ba0c5ea696709121530357a2ab0154bbb95add2393f34eb662eb828df-0","account":"**********************************","type":"UTXO","creation_height":"92068","creation_time":"2010-11-15T22:22:30.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********000c10491f16d464f138e9ca3fd335ac97e0e7e1bae94cd1eb9b6e66"}],"next_page_token":"*******************************************************************","next_page_url":"http://127.0.0.1:8080/v4/blockchain-v2/btc/sub-accounts?api_key=x1&start_time=2010-11-15T22%3A06%3A47Z&page_size=2&paging_from=start&next_page_token=*******************************************************************"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain-v2/btc/sub-accounts?api_key=$TEST_API_KEY&start_time=2010-11-15T22:06:47Z&page_size=2&paging_from=start",
        )
    }

    @Test
    fun `should return second sub-accounts page filtered by start_time with page_size=2 and paging_from=start`() {
        val expectedResponse =
            """{"data":[{"sub_account":"112a7bd80b1c24127713c89b3d2da59342ff7d8eed140b30a27c9fcdf416c0c2-1","account":"*********************************","type":"UTXO","creation_height":"92069","creation_time":"2010-11-15T22:26:23.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********000c890177eb8e97152b433882c0dbae361e6615f369e9f4cf060974"},{"sub_account":"f57cd9ca26f16c0ab628d281f3c56c6c95e12977f3e41304ccae98f5f414a4d5-0","account":"**********************************","type":"UTXO","creation_height":"92070","creation_time":"2010-11-15T22:28:35.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0005592cb78aa7384677543971f05844e07f1d04bac5594d93ab7405"}],"next_page_token":"*******************************************************************","next_page_url":"http://127.0.0.1:8080/v4/blockchain-v2/btc/sub-accounts?api_key=x1&start_time=2010-11-15T22%3A06%3A47Z&page_size=2&paging_from=start&next_page_token=*******************************************************************"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain-v2/btc/sub-accounts?api_key=$TEST_API_KEY&start_time=2010-11-15T22%3A06%3A47Z&page_size=2&paging_from=start&next_page_token=*******************************************************************",
        )
    }

    @Test
    fun `should return all sub-accounts for etc asset`() {
        val expectedResponse =
            """{"data":[{"sub_account":"PRESALE","account":"ISSUANCE","type":"VIRTUAL","creation_height":"0","creation_time":"1970-01-01T00:00:00.********0Z","creation_chain_sequence_number":"8893","creation_block_hash":"d4e56740f876aef8c010b86a40d5f56745a118d0906a34e69aec8c0db1cb8fa3"},{"sub_account":"BLOCK_REWARD","account":"ISSUANCE","type":"VIRTUAL","creation_height":"1","creation_time":"2015-07-30T15:26:28.********0Z","creation_chain_sequence_number":"**********","creation_block_hash":"88e96d4537bea4d9c05d12549907b32561d3bf31f45aae734cdc119f13406cb6","balance":"-********.74948"},{"sub_account":"UNCLE_BLOCK_REWARD","account":"ISSUANCE","type":"VIRTUAL","creation_height":"3","creation_time":"2015-07-30T15:27:28.********0Z","creation_chain_sequence_number":"***********","creation_block_hash":"3d6122660cc824376f11ee842f83addc3525e2dd6756b9bcf0affa6aa88cf741"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain-v2/etc/sub-accounts?api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `should return sub-account filtering by account for etc asset`() {
        val expectedResponse =
            """{"data":[{"sub_account":"PRESALE","account":"ISSUANCE","type":"VIRTUAL","creation_height":"0","creation_time":"1970-01-01T00:00:00.********0Z","creation_chain_sequence_number":"8893","creation_block_hash":"d4e56740f876aef8c010b86a40d5f56745a118d0906a34e69aec8c0db1cb8fa3"},{"sub_account":"BLOCK_REWARD","account":"ISSUANCE","type":"VIRTUAL","creation_height":"1","creation_time":"2015-07-30T15:26:28.********0Z","creation_chain_sequence_number":"**********","creation_block_hash":"88e96d4537bea4d9c05d12549907b32561d3bf31f45aae734cdc119f13406cb6","balance":"-********.74948"},{"sub_account":"UNCLE_BLOCK_REWARD","account":"ISSUANCE","type":"VIRTUAL","creation_height":"3","creation_time":"2015-07-30T15:27:28.********0Z","creation_chain_sequence_number":"***********","creation_block_hash":"3d6122660cc824376f11ee842f83addc3525e2dd6756b9bcf0affa6aa88cf741"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain-v2/etc/sub-accounts?api_key=$TEST_API_KEY&accounts=ISSUANCE",
        )
    }

    @Test
    fun `should return 403 when trying to access pro only accounts with community key`() {
        val expectedResponse =
            """{"error":{"type":"forbidden","message":"Requested resource is not available with supplied credentials."}}"""
        assertResponse(
            403,
            expectedResponse,
            "/v4/blockchain-v2/loom/sub-accounts?api_key=$COMMUNITY_KEY",
        )
    }

    @Test
    fun `should return error 403 when filter by more than one account for community key`() {
        val account1 = "001"
        val account2 = "002"

        val expectedResponse =
            """{"error":{"type":"forbidden","message":"Filter 'accounts' with multiple elements is not allowed for Community API."}}"""

        assertResponse(
            403,
            expectedResponse,
            "/v4/blockchain-v2/btc/sub-accounts?api_key=$COMMUNITY_KEY&accounts=$account1,$account2",
        )
    }

    @Test
    fun `should ignore filters by time range for btc asset for community key`() {
        val expectedResponse =
            """{"data":[{"sub_account":"303d3687eea9fd7ff9e89891c4795a76a849b111e73fbbdd0014d40ac0d8e75c-0","account":"**********************************","type":"UTXO","creation_height":"92066","creation_time":"2010-11-15T21:36:19.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0005636a05402caa5e3e22850face70e999c987751cc29f258207b1b"},{"sub_account":"3da8739218013c3271079ed9030266807359c5cb4d85d37fc459d34d61667179-0","account":"**********************************","type":"UTXO","creation_height":"92066","creation_time":"2010-11-15T21:36:19.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0005636a05402caa5e3e22850face70e999c987751cc29f258207b1b"},{"sub_account":"b4d1e49d6e18f3b811a79d58c1c3af234e53c8721568cc2a865cec2d6b13a2e8-0","account":"**********************************","type":"UTXO","creation_height":"92067","creation_time":"2010-11-15T22:06:47.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0006f9b809365a295225ec109b1e76fcb16f040bed626211ce9ce7c8"},{"sub_account":"b5cb830ba0c5ea696709121530357a2ab0154bbb95add2393f34eb662eb828df-0","account":"**********************************","type":"UTXO","creation_height":"92068","creation_time":"2010-11-15T22:22:30.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********000c10491f16d464f138e9ca3fd335ac97e0e7e1bae94cd1eb9b6e66"},{"sub_account":"112a7bd80b1c24127713c89b3d2da59342ff7d8eed140b30a27c9fcdf416c0c2-1","account":"*********************************","type":"UTXO","creation_height":"92069","creation_time":"2010-11-15T22:26:23.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********000c890177eb8e97152b433882c0dbae361e6615f369e9f4cf060974"},{"sub_account":"f57cd9ca26f16c0ab628d281f3c56c6c95e12977f3e41304ccae98f5f414a4d5-0","account":"**********************************","type":"UTXO","creation_height":"92070","creation_time":"2010-11-15T22:28:35.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0005592cb78aa7384677543971f05844e07f1d04bac5594d93ab7405"},{"sub_account":"445bce50671a88ae98a7770120bfbde1aa53e3eb007ea96bb860d0b501117a16-0","account":"1N6PPxZ1wQVTeG4tHJqb3C5LjW5i5sSxGE","type":"UTXO","creation_height":"92071","creation_time":"2010-11-15T22:31:48.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0008d2fd5fc7ad76a7b59ad85f093154d7df1eb036706f0866aa7fe9"},{"sub_account":"9663bdb2cb0afb5386527332931bb472d5926e011eb4bcad2d67e1360eeb8bb9-0","account":"1Etz42nbWwDZ4yavdFsrEtZu5MzU8TGTcc","type":"UTXO","creation_height":"92071","creation_time":"2010-11-15T22:31:48.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0008d2fd5fc7ad76a7b59ad85f093154d7df1eb036706f0866aa7fe9"},{"sub_account":"9beaef247e1adfc9e877431390cee1b0ce428c6c7424396b3e893abbd9a61a9b-0","account":"1BPEotGorsfkkeHRaFQHnP7pC7AUKPJLh7","type":"UTXO","creation_height":"92071","creation_time":"2010-11-15T22:31:48.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0008d2fd5fc7ad76a7b59ad85f093154d7df1eb036706f0866aa7fe9"},{"sub_account":"c1c10c6ac940ad8e2bc01568e5e7b9cd4f8deb96e1e9058580f875b84ba14107-0","account":"**********************************","type":"UTXO","creation_height":"92071","creation_time":"2010-11-15T22:31:48.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0008d2fd5fc7ad76a7b59ad85f093154d7df1eb036706f0866aa7fe9"},{"sub_account":"c52de017cf8cd8c28704764cf3831092e719f181394d4820afe1de208c73ec50-0","account":"**********************************","type":"UTXO","creation_height":"92071","creation_time":"2010-11-15T22:31:48.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0008d2fd5fc7ad76a7b59ad85f093154d7df1eb036706f0866aa7fe9"},{"sub_account":"dc34d86e6727db756f40d35558d3750767677ca75ab4554b761b3b6536b9091f-0","account":"1AF21NWaF75wRMT8T27qyXuABinxC2qJsV","type":"UTXO","creation_height":"92072","creation_time":"2010-11-15T22:41:10.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********000812b75d5b4dcbd89a07788ade7cb9687457dac997d0669b4f8af6"},{"sub_account":"0c99b60b80405841ef91ab74223bb517e633f50fd9d6b6110d45c6b55ddf4e91-0","account":"**********************************","type":"UTXO","creation_height":"92073","creation_time":"2010-11-15T23:09:06.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0005b3fb679b2758c2cdd3311c0f483c639812039c8075acbf3b59ae"},{"sub_account":"7efa275918efe617f642c3f31beef234b8e876c233250c46a103befe2a26049f-0","account":"**********************************","type":"UTXO","creation_height":"92073","creation_time":"2010-11-15T23:09:06.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0005b3fb679b2758c2cdd3311c0f483c639812039c8075acbf3b59ae"},{"sub_account":"7efa275918efe617f642c3f31beef234b8e876c233250c46a103befe2a26049f-1","account":"**********************************","type":"UTXO","creation_height":"92073","creation_time":"2010-11-15T23:09:06.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0005b3fb679b2758c2cdd3311c0f483c639812039c8075acbf3b59ae"},{"sub_account":"e6311fc17b4bdc29f40a35dc20e4dfdcd5dec24df6c70bbd9470b2b80eb26863-0","account":"**********************************","type":"UTXO","creation_height":"92074","creation_time":"2010-11-15T23:22:22.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0005707b17cf760e1035d9e19a3001ab0fe4dbbdc206bd48b692813c"},{"sub_account":"ba2031ac0a1d21a167638fe4b91a1bce4492c3a22ced4c7afd3795f19e629064-0","account":"**********************************","type":"UTXO","creation_height":"92075","creation_time":"2010-11-15T23:24:07.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0006b584590d83cb9a991f3bd5d8511b4abdbfea05e65590cbe23093"},{"sub_account":"e49703b2797bf72dc3fc0aae0c7c40fbca267a9bc6669e086162d5ec76cca4d0-0","account":"**********************************","type":"UTXO","creation_height":"92075","creation_time":"2010-11-15T23:24:07.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0006b584590d83cb9a991f3bd5d8511b4abdbfea05e65590cbe23093"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain-v2/btc/sub-accounts?api_key=$COMMUNITY_KEY&start_time=2010-11-15T22:31:48Z&end_time=2010-11-15T22:41:10Z",
        )
    }

    @Test
    fun `should ignore filters by height range for btc asset for community key`() {
        val expectedResponse =
            """{"data":[{"sub_account":"303d3687eea9fd7ff9e89891c4795a76a849b111e73fbbdd0014d40ac0d8e75c-0","account":"**********************************","type":"UTXO","creation_height":"92066","creation_time":"2010-11-15T21:36:19.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0005636a05402caa5e3e22850face70e999c987751cc29f258207b1b"},{"sub_account":"3da8739218013c3271079ed9030266807359c5cb4d85d37fc459d34d61667179-0","account":"**********************************","type":"UTXO","creation_height":"92066","creation_time":"2010-11-15T21:36:19.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0005636a05402caa5e3e22850face70e999c987751cc29f258207b1b"},{"sub_account":"b4d1e49d6e18f3b811a79d58c1c3af234e53c8721568cc2a865cec2d6b13a2e8-0","account":"**********************************","type":"UTXO","creation_height":"92067","creation_time":"2010-11-15T22:06:47.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0006f9b809365a295225ec109b1e76fcb16f040bed626211ce9ce7c8"},{"sub_account":"b5cb830ba0c5ea696709121530357a2ab0154bbb95add2393f34eb662eb828df-0","account":"**********************************","type":"UTXO","creation_height":"92068","creation_time":"2010-11-15T22:22:30.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********000c10491f16d464f138e9ca3fd335ac97e0e7e1bae94cd1eb9b6e66"},{"sub_account":"112a7bd80b1c24127713c89b3d2da59342ff7d8eed140b30a27c9fcdf416c0c2-1","account":"*********************************","type":"UTXO","creation_height":"92069","creation_time":"2010-11-15T22:26:23.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********000c890177eb8e97152b433882c0dbae361e6615f369e9f4cf060974"},{"sub_account":"f57cd9ca26f16c0ab628d281f3c56c6c95e12977f3e41304ccae98f5f414a4d5-0","account":"**********************************","type":"UTXO","creation_height":"92070","creation_time":"2010-11-15T22:28:35.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0005592cb78aa7384677543971f05844e07f1d04bac5594d93ab7405"},{"sub_account":"445bce50671a88ae98a7770120bfbde1aa53e3eb007ea96bb860d0b501117a16-0","account":"1N6PPxZ1wQVTeG4tHJqb3C5LjW5i5sSxGE","type":"UTXO","creation_height":"92071","creation_time":"2010-11-15T22:31:48.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0008d2fd5fc7ad76a7b59ad85f093154d7df1eb036706f0866aa7fe9"},{"sub_account":"9663bdb2cb0afb5386527332931bb472d5926e011eb4bcad2d67e1360eeb8bb9-0","account":"1Etz42nbWwDZ4yavdFsrEtZu5MzU8TGTcc","type":"UTXO","creation_height":"92071","creation_time":"2010-11-15T22:31:48.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0008d2fd5fc7ad76a7b59ad85f093154d7df1eb036706f0866aa7fe9"},{"sub_account":"9beaef247e1adfc9e877431390cee1b0ce428c6c7424396b3e893abbd9a61a9b-0","account":"1BPEotGorsfkkeHRaFQHnP7pC7AUKPJLh7","type":"UTXO","creation_height":"92071","creation_time":"2010-11-15T22:31:48.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0008d2fd5fc7ad76a7b59ad85f093154d7df1eb036706f0866aa7fe9"},{"sub_account":"c1c10c6ac940ad8e2bc01568e5e7b9cd4f8deb96e1e9058580f875b84ba14107-0","account":"**********************************","type":"UTXO","creation_height":"92071","creation_time":"2010-11-15T22:31:48.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0008d2fd5fc7ad76a7b59ad85f093154d7df1eb036706f0866aa7fe9"},{"sub_account":"c52de017cf8cd8c28704764cf3831092e719f181394d4820afe1de208c73ec50-0","account":"**********************************","type":"UTXO","creation_height":"92071","creation_time":"2010-11-15T22:31:48.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0008d2fd5fc7ad76a7b59ad85f093154d7df1eb036706f0866aa7fe9"},{"sub_account":"dc34d86e6727db756f40d35558d3750767677ca75ab4554b761b3b6536b9091f-0","account":"1AF21NWaF75wRMT8T27qyXuABinxC2qJsV","type":"UTXO","creation_height":"92072","creation_time":"2010-11-15T22:41:10.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********000812b75d5b4dcbd89a07788ade7cb9687457dac997d0669b4f8af6"},{"sub_account":"0c99b60b80405841ef91ab74223bb517e633f50fd9d6b6110d45c6b55ddf4e91-0","account":"**********************************","type":"UTXO","creation_height":"92073","creation_time":"2010-11-15T23:09:06.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0005b3fb679b2758c2cdd3311c0f483c639812039c8075acbf3b59ae"},{"sub_account":"7efa275918efe617f642c3f31beef234b8e876c233250c46a103befe2a26049f-0","account":"**********************************","type":"UTXO","creation_height":"92073","creation_time":"2010-11-15T23:09:06.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0005b3fb679b2758c2cdd3311c0f483c639812039c8075acbf3b59ae"},{"sub_account":"7efa275918efe617f642c3f31beef234b8e876c233250c46a103befe2a26049f-1","account":"**********************************","type":"UTXO","creation_height":"92073","creation_time":"2010-11-15T23:09:06.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0005b3fb679b2758c2cdd3311c0f483c639812039c8075acbf3b59ae"},{"sub_account":"e6311fc17b4bdc29f40a35dc20e4dfdcd5dec24df6c70bbd9470b2b80eb26863-0","account":"**********************************","type":"UTXO","creation_height":"92074","creation_time":"2010-11-15T23:22:22.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0005707b17cf760e1035d9e19a3001ab0fe4dbbdc206bd48b692813c"},{"sub_account":"ba2031ac0a1d21a167638fe4b91a1bce4492c3a22ced4c7afd3795f19e629064-0","account":"**********************************","type":"UTXO","creation_height":"92075","creation_time":"2010-11-15T23:24:07.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0006b584590d83cb9a991f3bd5d8511b4abdbfea05e65590cbe23093"},{"sub_account":"e49703b2797bf72dc3fc0aae0c7c40fbca267a9bc6669e086162d5ec76cca4d0-0","account":"**********************************","type":"UTXO","creation_height":"92075","creation_time":"2010-11-15T23:24:07.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0006b584590d83cb9a991f3bd5d8511b4abdbfea05e65590cbe23093"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain-v2/btc/sub-accounts?api_key=$COMMUNITY_KEY&start_height=92069&end_height=92070",
        )
    }

    @Test
    fun `should ignore filters by chain sequence range for btc asset for community key`() {
        val expectedResponse =
            """{"data":[{"sub_account":"303d3687eea9fd7ff9e89891c4795a76a849b111e73fbbdd0014d40ac0d8e75c-0","account":"**********************************","type":"UTXO","creation_height":"92066","creation_time":"2010-11-15T21:36:19.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0005636a05402caa5e3e22850face70e999c987751cc29f258207b1b"},{"sub_account":"3da8739218013c3271079ed9030266807359c5cb4d85d37fc459d34d61667179-0","account":"**********************************","type":"UTXO","creation_height":"92066","creation_time":"2010-11-15T21:36:19.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0005636a05402caa5e3e22850face70e999c987751cc29f258207b1b"},{"sub_account":"b4d1e49d6e18f3b811a79d58c1c3af234e53c8721568cc2a865cec2d6b13a2e8-0","account":"**********************************","type":"UTXO","creation_height":"92067","creation_time":"2010-11-15T22:06:47.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0006f9b809365a295225ec109b1e76fcb16f040bed626211ce9ce7c8"},{"sub_account":"b5cb830ba0c5ea696709121530357a2ab0154bbb95add2393f34eb662eb828df-0","account":"**********************************","type":"UTXO","creation_height":"92068","creation_time":"2010-11-15T22:22:30.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********000c10491f16d464f138e9ca3fd335ac97e0e7e1bae94cd1eb9b6e66"},{"sub_account":"112a7bd80b1c24127713c89b3d2da59342ff7d8eed140b30a27c9fcdf416c0c2-1","account":"*********************************","type":"UTXO","creation_height":"92069","creation_time":"2010-11-15T22:26:23.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********000c890177eb8e97152b433882c0dbae361e6615f369e9f4cf060974"},{"sub_account":"f57cd9ca26f16c0ab628d281f3c56c6c95e12977f3e41304ccae98f5f414a4d5-0","account":"**********************************","type":"UTXO","creation_height":"92070","creation_time":"2010-11-15T22:28:35.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0005592cb78aa7384677543971f05844e07f1d04bac5594d93ab7405"},{"sub_account":"445bce50671a88ae98a7770120bfbde1aa53e3eb007ea96bb860d0b501117a16-0","account":"1N6PPxZ1wQVTeG4tHJqb3C5LjW5i5sSxGE","type":"UTXO","creation_height":"92071","creation_time":"2010-11-15T22:31:48.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0008d2fd5fc7ad76a7b59ad85f093154d7df1eb036706f0866aa7fe9"},{"sub_account":"9663bdb2cb0afb5386527332931bb472d5926e011eb4bcad2d67e1360eeb8bb9-0","account":"1Etz42nbWwDZ4yavdFsrEtZu5MzU8TGTcc","type":"UTXO","creation_height":"92071","creation_time":"2010-11-15T22:31:48.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0008d2fd5fc7ad76a7b59ad85f093154d7df1eb036706f0866aa7fe9"},{"sub_account":"9beaef247e1adfc9e877431390cee1b0ce428c6c7424396b3e893abbd9a61a9b-0","account":"1BPEotGorsfkkeHRaFQHnP7pC7AUKPJLh7","type":"UTXO","creation_height":"92071","creation_time":"2010-11-15T22:31:48.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0008d2fd5fc7ad76a7b59ad85f093154d7df1eb036706f0866aa7fe9"},{"sub_account":"c1c10c6ac940ad8e2bc01568e5e7b9cd4f8deb96e1e9058580f875b84ba14107-0","account":"**********************************","type":"UTXO","creation_height":"92071","creation_time":"2010-11-15T22:31:48.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0008d2fd5fc7ad76a7b59ad85f093154d7df1eb036706f0866aa7fe9"},{"sub_account":"c52de017cf8cd8c28704764cf3831092e719f181394d4820afe1de208c73ec50-0","account":"**********************************","type":"UTXO","creation_height":"92071","creation_time":"2010-11-15T22:31:48.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0008d2fd5fc7ad76a7b59ad85f093154d7df1eb036706f0866aa7fe9"},{"sub_account":"dc34d86e6727db756f40d35558d3750767677ca75ab4554b761b3b6536b9091f-0","account":"1AF21NWaF75wRMT8T27qyXuABinxC2qJsV","type":"UTXO","creation_height":"92072","creation_time":"2010-11-15T22:41:10.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********000812b75d5b4dcbd89a07788ade7cb9687457dac997d0669b4f8af6"},{"sub_account":"0c99b60b80405841ef91ab74223bb517e633f50fd9d6b6110d45c6b55ddf4e91-0","account":"**********************************","type":"UTXO","creation_height":"92073","creation_time":"2010-11-15T23:09:06.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0005b3fb679b2758c2cdd3311c0f483c639812039c8075acbf3b59ae"},{"sub_account":"7efa275918efe617f642c3f31beef234b8e876c233250c46a103befe2a26049f-0","account":"**********************************","type":"UTXO","creation_height":"92073","creation_time":"2010-11-15T23:09:06.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0005b3fb679b2758c2cdd3311c0f483c639812039c8075acbf3b59ae"},{"sub_account":"7efa275918efe617f642c3f31beef234b8e876c233250c46a103befe2a26049f-1","account":"**********************************","type":"UTXO","creation_height":"92073","creation_time":"2010-11-15T23:09:06.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0005b3fb679b2758c2cdd3311c0f483c639812039c8075acbf3b59ae"},{"sub_account":"e6311fc17b4bdc29f40a35dc20e4dfdcd5dec24df6c70bbd9470b2b80eb26863-0","account":"**********************************","type":"UTXO","creation_height":"92074","creation_time":"2010-11-15T23:22:22.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0005707b17cf760e1035d9e19a3001ab0fe4dbbdc206bd48b692813c"},{"sub_account":"ba2031ac0a1d21a167638fe4b91a1bce4492c3a22ced4c7afd3795f19e629064-0","account":"**********************************","type":"UTXO","creation_height":"92075","creation_time":"2010-11-15T23:24:07.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0006b584590d83cb9a991f3bd5d8511b4abdbfea05e65590cbe23093"},{"sub_account":"e49703b2797bf72dc3fc0aae0c7c40fbca267a9bc6669e086162d5ec76cca4d0-0","account":"**********************************","type":"UTXO","creation_height":"92075","creation_time":"2010-11-15T23:24:07.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0006b584590d83cb9a991f3bd5d8511b4abdbfea05e65590cbe23093"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain-v2/btc/sub-accounts?api_key=$COMMUNITY_KEY&start_chain_sequence_number=***************&end_chain_sequence_number=***************",
        )
    }

    @Test
    fun `should return first sub-accounts page without any filter for community key`() {
        val expectedResponse =
            """{"data":[{"sub_account":"303d3687eea9fd7ff9e89891c4795a76a849b111e73fbbdd0014d40ac0d8e75c-0","account":"**********************************","type":"UTXO","creation_height":"92066","creation_time":"2010-11-15T21:36:19.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0005636a05402caa5e3e22850face70e999c987751cc29f258207b1b"},{"sub_account":"3da8739218013c3271079ed9030266807359c5cb4d85d37fc459d34d61667179-0","account":"**********************************","type":"UTXO","creation_height":"92066","creation_time":"2010-11-15T21:36:19.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0005636a05402caa5e3e22850face70e999c987751cc29f258207b1b"}],"next_page_token":"*******************************************************************","next_page_url":"http://127.0.0.1:8080/v4/blockchain-v2/btc/sub-accounts?api_key=T1z1tZcIY2tnX4jcUhve&page_size=2&paging_from=start&next_page_token=*******************************************************************"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/blockchain-v2/btc/sub-accounts?api_key=$COMMUNITY_KEY&page_size=2&paging_from=start",
        )
    }

    @Test
    fun `should return second sub-accounts page without any filter for community key`() {
        val expectedResponse =
            """{"data":[{"sub_account":"b4d1e49d6e18f3b811a79d58c1c3af234e53c8721568cc2a865cec2d6b13a2e8-0","account":"**********************************","type":"UTXO","creation_height":"92067","creation_time":"2010-11-15T22:06:47.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********0006f9b809365a295225ec109b1e76fcb16f040bed626211ce9ce7c8"},{"sub_account":"b5cb830ba0c5ea696709121530357a2ab0154bbb95add2393f34eb662eb828df-0","account":"**********************************","type":"UTXO","creation_height":"92068","creation_time":"2010-11-15T22:22:30.********0Z","creation_chain_sequence_number":"***************","creation_block_hash":"********000c10491f16d464f138e9ca3fd335ac97e0e7e1bae94cd1eb9b6e66"}],"next_page_token":"*******************************************************************","next_page_url":"http://127.0.0.1:8080/v4/blockchain-v2/btc/sub-accounts?api_key=T1z1tZcIY2tnX4jcUhve&page_size=2&paging_from=start&next_page_token=*******************************************************************"}"""
        assertResponse(
            expectedCode = 200,
            expectedResponse = expectedResponse,
            pathAndQuery = "/v4/blockchain-v2/btc/sub-accounts?api_key=$COMMUNITY_KEY&page_size=2&paging_from=start&next_page_token=*******************************************************************",
        )
    }
}
