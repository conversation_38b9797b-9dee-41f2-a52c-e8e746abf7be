package io.coinmetrics.api.endpoints.timeseries.market

import io.coinmetrics.api.modules.main.MainApiConfig
import io.coinmetrics.api.statistics.app.StatisticsAppConfig
import org.junit.jupiter.api.TestInstance

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class QuotesLegacyTest : QuotesTest() {
    override fun mainApiConfig(): MainApiConfig =
        super.mainApiConfig().copy(
            useNewBooksTables = false,
        )

    override fun statisticsConfig(): StatisticsAppConfig =
        super.statisticsConfig().copy(
            useNewBooksTables = false,
        )
}
