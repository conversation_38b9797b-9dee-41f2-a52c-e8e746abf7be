package io.coinmetrics.api.endpoints.timeseries.market.datasources

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

class DepthLimiterTest {
    private val limiter = DepthLimiter()

    @Test
    fun `depth 1`() {
        val book =
            """
            {"market":"coinbase-btc-usd-spot","time":"2023-08-14T23:21:10.000000000Z","coin_metrics_id":"1692055270000000-0","asks":[{"price":"29407.22","size":"0.59653535"},{"price":"29407.23","size":"0.05322527"}],"bids":[{"price":"29407.21","size":"0.89255133"},{"price":"29406.96","size":"0.53194115"}],"database_time":"2023-08-14T23:21:11.651596000Z"}
            """.trimIndent()
        val expected =
            """
            {"market":"coinbase-btc-usd-spot","time":"2023-08-14T23:21:10.000000000Z","coin_metrics_id":"1692055270000000-0","asks":[{"price":"29407.22","size":"0.59653535"}],"bids":[{"price":"29407.21","size":"0.89255133"}],"database_time":"2023-08-14T23:21:11.651596000Z"}
            """.trimIndent()

        assertEquals(expected, book, 1)
    }

    @Test
    fun `depth 0`() {
        val book =
            """
            {"market":"coinbase-btc-usd-spot","time":"2023-08-14T23:21:10.000000000Z","coin_metrics_id":"1692055270000000-0","asks":[{"price":"29407.22","size":"0.59653535"},{"price":"29407.23","size":"0.05322527"}],"bids":[{"price":"29407.21","size":"0.89255133"},{"price":"29406.96","size":"0.53194115"}],"database_time":"2023-08-14T23:21:11.651596000Z"}
            """.trimIndent()
        val expected =
            """
            {"market":"coinbase-btc-usd-spot","time":"2023-08-14T23:21:10.000000000Z","coin_metrics_id":"1692055270000000-0","asks":[{"price":"29407.22","size":"0.59653535"}],"bids":[{"price":"29407.21","size":"0.89255133"}],"database_time":"2023-08-14T23:21:11.651596000Z"}
            """.trimIndent()

        assertThrows<IllegalArgumentException> {
            assertEquals(expected, book, 0)
        }
    }

    @Test
    fun `depth greater or equal than present`() {
        val book =
            """
            {"market":"coinbase-btc-usd-spot","time":"2023-08-14T23:21:10.000000000Z","coin_metrics_id":"1692055270000000-0","asks":[{"price":"29407.22","size":"0.59653535"},{"price":"29407.23","size":"0.05322527"}],"bids":[{"price":"29407.21","size":"0.89255133"},{"price":"29406.96","size":"0.53194115"}],"database_time":"2023-08-14T23:21:11.651596000Z"}
            """.trimIndent()

        assertEquals(book, book, 2)
        assertEquals(book, book, 3)
        assertEquals(book, book, 4)
        assertEquals(book, book, 100)
    }

    @Test
    fun `depth 2`() {
        val book =
            """
            {"market":"coinbase-btc-usd-spot","time":"2023-08-14T23:21:10.000000000Z","coin_metrics_id":"1692055270000000-0","asks":[{"price":"29407.22","size":"0.59653535"},{"price":"29407.23","size":"0.05322527"},{"price":"89407.23","size":"0.05322527"}],"bids":[{"price":"29407.21","size":"0.89255133"},{"price":"29406.96","size":"0.53194115"},{"price":"59406.96","size":"0.53194115"}],"database_time":"2023-08-14T23:21:11.651596000Z"}
            """.trimIndent()
        val expected =
            """
            {"market":"coinbase-btc-usd-spot","time":"2023-08-14T23:21:10.000000000Z","coin_metrics_id":"1692055270000000-0","asks":[{"price":"29407.22","size":"0.59653535"},{"price":"29407.23","size":"0.05322527"}],"bids":[{"price":"29407.21","size":"0.89255133"},{"price":"29406.96","size":"0.53194115"}],"database_time":"2023-08-14T23:21:11.651596000Z"}
            """.trimIndent()

        assertEquals(expected, book, 2)
    }

    @Test
    fun `depth 2, more asks`() {
        val book =
            """
            {"market":"coinbase-btc-usd-spot","time":"2023-08-14T23:21:10.000000000Z","coin_metrics_id":"1692055270000000-0","asks":[{"price":"29407.22","size":"0.59653535"},{"price":"29407.23","size":"0.05322527"},{"price":"89407.23","size":"0.05322527"}],"bids":[{"price":"29407.21","size":"0.89255133"},{"price":"29406.96","size":"0.53194115"}],"database_time":"2023-08-14T23:21:11.651596000Z"}
            """.trimIndent()
        val expected =
            """
            {"market":"coinbase-btc-usd-spot","time":"2023-08-14T23:21:10.000000000Z","coin_metrics_id":"1692055270000000-0","asks":[{"price":"29407.22","size":"0.59653535"},{"price":"29407.23","size":"0.05322527"}],"bids":[{"price":"29407.21","size":"0.89255133"},{"price":"29406.96","size":"0.53194115"}],"database_time":"2023-08-14T23:21:11.651596000Z"}
            """.trimIndent()

        assertEquals(expected, book, 2)
    }

    @Test
    fun `depth 2, more asks, few bids`() {
        val book =
            """
            {"market":"coinbase-btc-usd-spot","time":"2023-08-14T23:21:10.000000000Z","coin_metrics_id":"1692055270000000-0","asks":[{"price":"29407.22","size":"0.59653535"},{"price":"29407.23","size":"0.05322527"},{"price":"89407.23","size":"0.05322527"}],"bids":[{"price":"29407.21","size":"0.89255133"}],"database_time":"2023-08-14T23:21:11.651596000Z"}
            """.trimIndent()
        val expected =
            """
            {"market":"coinbase-btc-usd-spot","time":"2023-08-14T23:21:10.000000000Z","coin_metrics_id":"1692055270000000-0","asks":[{"price":"29407.22","size":"0.59653535"},{"price":"29407.23","size":"0.05322527"}],"bids":[{"price":"29407.21","size":"0.89255133"}],"database_time":"2023-08-14T23:21:11.651596000Z"}
            """.trimIndent()

        assertEquals(expected, book, 2)
    }

    @Test
    fun `depth 2, more bids`() {
        val book =
            """
            {"market":"coinbase-btc-usd-spot","time":"2023-08-14T23:21:10.000000000Z","coin_metrics_id":"1692055270000000-0","asks":[{"price":"29407.22","size":"0.59653535"},{"price":"29407.23","size":"0.05322527"}],"bids":[{"price":"29407.21","size":"0.89255133"},{"price":"29406.96","size":"0.53194115"},{"price":"59406.96","size":"0.53194115"}],"database_time":"2023-08-14T23:21:11.651596000Z"}
            """.trimIndent()
        val expected =
            """
            {"market":"coinbase-btc-usd-spot","time":"2023-08-14T23:21:10.000000000Z","coin_metrics_id":"1692055270000000-0","asks":[{"price":"29407.22","size":"0.59653535"},{"price":"29407.23","size":"0.05322527"}],"bids":[{"price":"29407.21","size":"0.89255133"},{"price":"29406.96","size":"0.53194115"}],"database_time":"2023-08-14T23:21:11.651596000Z"}
            """.trimIndent()

        assertEquals(expected, book, 2)
    }

    @Test
    fun `depth 2, more bids, few asks`() {
        val book =
            """
            {"market":"coinbase-btc-usd-spot","time":"2023-08-14T23:21:10.000000000Z","coin_metrics_id":"1692055270000000-0","asks":[{"price":"29407.22","size":"0.59653535"}],"bids":[{"price":"29407.21","size":"0.89255133"},{"price":"29406.96","size":"0.53194115"},{"price":"59406.96","size":"0.53194115"}],"database_time":"2023-08-14T23:21:11.651596000Z"}
            """.trimIndent()
        val expected =
            """
            {"market":"coinbase-btc-usd-spot","time":"2023-08-14T23:21:10.000000000Z","coin_metrics_id":"1692055270000000-0","asks":[{"price":"29407.22","size":"0.59653535"}],"bids":[{"price":"29407.21","size":"0.89255133"},{"price":"29406.96","size":"0.53194115"}],"database_time":"2023-08-14T23:21:11.651596000Z"}
            """.trimIndent()

        assertEquals(expected, book, 2)
    }

    private fun assertEquals(
        expectedBook: String,
        book: String,
        depthLimit: Int,
    ) {
        assertEquals(expectedBook, limiter.limit(book.encodeToByteArray(), depthLimit).decodeToString())
    }
}
