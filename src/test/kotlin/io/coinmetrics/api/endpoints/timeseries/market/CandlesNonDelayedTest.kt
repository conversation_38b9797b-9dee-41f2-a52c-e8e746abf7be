package io.coinmetrics.api.endpoints.timeseries.market

import io.coinmetrics.api.helper.BaseTest
import io.coinmetrics.api.helper.TEST_API_KEY
import io.coinmetrics.api.helper.TestClock
import io.coinmetrics.api.models.MarketCandlesResponse
import io.coinmetrics.api.modules.common.CommonConfig
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import java.time.Instant

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class CandlesNonDelayedTest : BaseTest() {
    private val testClock = TestClock().also { it.instant = Instant.parse("2024-10-18T12:15:01Z") }

    override fun commonConfig(): CommonConfig = super.commonConfig().copy(clock = testClock)

    @Test
    fun `instant and delayed candles with frequency of 1m and paging from start`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:20:00.000000000Z","price_open":"0.15897","price_close":"0.15898","price_high":"0.15901","price_low":"0.15897","vwap":"0.15900065419949308734","volume":"77188.84","candle_usd_volume":"12267.1084377712053879038564","candle_trades_count":"41"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:21:00.000000000Z","price_open":"0.15556","price_close":"0.16002","price_high":"0.16002","price_low":"0.15556","vwap":"0.15779","volume":"77000.02","candle_usd_volume":"12267.00001","candle_trades_count":"41"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:22:00.000000000Z","price_open":"0.15557","price_close":"0.16003","price_high":"0.16003","price_low":"0.15557","vwap":"0.1578","volume":"77000.03","candle_usd_volume":"12268.00001","candle_trades_count":"42"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:23:00.000000000Z","price_open":"0.15558","price_close":"0.16004","price_high":"0.16004","price_low":"0.15558","vwap":"0.15781","volume":"77000.04","candle_usd_volume":"12269.00001","candle_trades_count":"43"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:24:00.000000000Z","price_open":"0.15559","price_close":"0.16005","price_high":"0.16005","price_low":"0.15559","vwap":"0.15782","volume":"77000.05","candle_usd_volume":"12270.00001","candle_trades_count":"44"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:25:00.000000000Z","price_open":"0.1556","price_close":"0.16006","price_high":"0.16006","price_low":"0.1556","vwap":"0.15783","volume":"77000.06","candle_usd_volume":"12271.00001","candle_trades_count":"45"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:26:00.000000000Z","price_open":"0.15561","price_close":"0.16007","price_high":"0.16007","price_low":"0.15561","vwap":"0.15784","volume":"77000.07","candle_usd_volume":"12272.00001","candle_trades_count":"46"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:27:00.000000000Z","price_open":"0.15562","price_close":"0.16008","price_high":"0.16008","price_low":"0.15562","vwap":"0.15785","volume":"77000.08","candle_usd_volume":"12273.00001","candle_trades_count":"47"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:28:00.000000000Z","price_open":"0.15563","price_close":"0.16009","price_high":"0.16009","price_low":"0.15563","vwap":"0.15786","volume":"77000.09","candle_usd_volume":"12274.00001","candle_trades_count":"48"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:29:00.000000000Z","price_open":"0.15564","price_close":"0.1601","price_high":"0.1601","price_low":"0.15564","vwap":"0.15787","volume":"77000.1","candle_usd_volume":"12275.00001","candle_trades_count":"48"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:30:00.000000000Z","price_open":"0.15565","price_close":"0.16011","price_high":"0.16011","price_low":"0.15565","vwap":"0.15788","volume":"77000.11","candle_usd_volume":"12276.00001","candle_trades_count":"49"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:31:00.000000000Z","price_open":"0.15566","price_close":"0.16012","price_high":"0.16012","price_low":"0.15566","vwap":"0.15789","volume":"77000.12","candle_usd_volume":"12277.00001","candle_trades_count":"50"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:32:00.000000000Z","price_open":"0.15567","price_close":"0.16013","price_high":"0.16013","price_low":"0.15567","vwap":"0.1579","volume":"77000.13","candle_usd_volume":"12278.00001","candle_trades_count":"51"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:33:00.000000000Z","price_open":"0.15568","price_close":"0.16014","price_high":"0.16014","price_low":"0.15568","vwap":"0.15791","volume":"77000.14","candle_usd_volume":"12279.00001","candle_trades_count":"52"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:34:00.000000000Z","price_open":"0.15569","price_close":"0.16015","price_high":"0.16015","price_low":"0.15569","vwap":"0.15792","volume":"77000.15","candle_usd_volume":"12280.00001","candle_trades_count":"53"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:35:00.000000000Z","price_open":"0.1557","price_close":"0.16016","price_high":"0.16016","price_low":"0.1557","vwap":"0.15793","volume":"77000.16","candle_usd_volume":"12281.00001","candle_trades_count":"54"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:36:00.000000000Z","price_open":"0.15571","price_close":"0.16017","price_high":"0.16017","price_low":"0.15571","vwap":"0.15794","volume":"77000.17","candle_usd_volume":"12282.00001","candle_trades_count":"55"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:37:00.000000000Z","price_open":"0.15572","price_close":"0.16018","price_high":"0.16018","price_low":"0.15572","vwap":"0.15795","volume":"77000.18","candle_usd_volume":"12283.00001","candle_trades_count":"56"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-trx-usdt-spot&frequency=1m&paging_from=start&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed candles with frequency of 1m and paging from start and page size = 3`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:20:00.000000000Z","price_open":"0.15897","price_close":"0.15898","price_high":"0.15901","price_low":"0.15897","vwap":"0.15900065419949308734","volume":"77188.84","candle_usd_volume":"12267.1084377712053879038564","candle_trades_count":"41"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:21:00.000000000Z","price_open":"0.15556","price_close":"0.16002","price_high":"0.16002","price_low":"0.15556","vwap":"0.15779","volume":"77000.02","candle_usd_volume":"12267.00001","candle_trades_count":"41"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:22:00.000000000Z","price_open":"0.15557","price_close":"0.16003","price_high":"0.16003","price_low":"0.15557","vwap":"0.1578","volume":"77000.03","candle_usd_volume":"12268.00001","candle_trades_count":"42"}],"next_page_token":"0.MjAyNC0xMC0xOFQxMToyMjowMFo","next_page_url":"http://127.0.0.1:8080/v4/timeseries/market-candles?markets=bybit-trx-usdt-spot&frequency=1m&paging_from=start&page_size=3&api_key=x1&next_page_token=0.MjAyNC0xMC0xOFQxMToyMjowMFo"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-trx-usdt-spot&frequency=1m&paging_from=start&page_size=3&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed candles with frequency of 1m and paging from end and page size = 3`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:35:00.000000000Z","price_open":"0.1557","price_close":"0.16016","price_high":"0.16016","price_low":"0.1557","vwap":"0.15793","volume":"77000.16","candle_usd_volume":"12281.00001","candle_trades_count":"54"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:36:00.000000000Z","price_open":"0.15571","price_close":"0.16017","price_high":"0.16017","price_low":"0.15571","vwap":"0.15794","volume":"77000.17","candle_usd_volume":"12282.00001","candle_trades_count":"55"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:37:00.000000000Z","price_open":"0.15572","price_close":"0.16018","price_high":"0.16018","price_low":"0.15572","vwap":"0.15795","volume":"77000.18","candle_usd_volume":"12283.00001","candle_trades_count":"56"}],"next_page_token":"0.MjAyNC0xMC0xOFQxMTozNTowMFo","next_page_url":"http://127.0.0.1:8080/v4/timeseries/market-candles?markets=bybit-trx-usdt-spot&frequency=1m&paging_from=end&page_size=3&api_key=x1&next_page_token=0.MjAyNC0xMC0xOFQxMTozNTowMFo"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-trx-usdt-spot&frequency=1m&paging_from=end&page_size=3&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed candles with frequency of 1m and paging from start in a csv format`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:20:00.000000000Z","price_open":"0.15897","price_close":"0.15898","price_high":"0.15901","price_low":"0.15897","vwap":"0.15900065419949308734","volume":"77188.84","candle_usd_volume":"12267.1084377712053879038564","candle_trades_count":"41"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:21:00.000000000Z","price_open":"0.15556","price_close":"0.16002","price_high":"0.16002","price_low":"0.15556","vwap":"0.15779","volume":"77000.02","candle_usd_volume":"12267.00001","candle_trades_count":"41"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:22:00.000000000Z","price_open":"0.15557","price_close":"0.16003","price_high":"0.16003","price_low":"0.15557","vwap":"0.1578","volume":"77000.03","candle_usd_volume":"12268.00001","candle_trades_count":"42"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:23:00.000000000Z","price_open":"0.15558","price_close":"0.16004","price_high":"0.16004","price_low":"0.15558","vwap":"0.15781","volume":"77000.04","candle_usd_volume":"12269.00001","candle_trades_count":"43"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:24:00.000000000Z","price_open":"0.15559","price_close":"0.16005","price_high":"0.16005","price_low":"0.15559","vwap":"0.15782","volume":"77000.05","candle_usd_volume":"12270.00001","candle_trades_count":"44"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:25:00.000000000Z","price_open":"0.1556","price_close":"0.16006","price_high":"0.16006","price_low":"0.1556","vwap":"0.15783","volume":"77000.06","candle_usd_volume":"12271.00001","candle_trades_count":"45"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:26:00.000000000Z","price_open":"0.15561","price_close":"0.16007","price_high":"0.16007","price_low":"0.15561","vwap":"0.15784","volume":"77000.07","candle_usd_volume":"12272.00001","candle_trades_count":"46"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:27:00.000000000Z","price_open":"0.15562","price_close":"0.16008","price_high":"0.16008","price_low":"0.15562","vwap":"0.15785","volume":"77000.08","candle_usd_volume":"12273.00001","candle_trades_count":"47"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:28:00.000000000Z","price_open":"0.15563","price_close":"0.16009","price_high":"0.16009","price_low":"0.15563","vwap":"0.15786","volume":"77000.09","candle_usd_volume":"12274.00001","candle_trades_count":"48"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:29:00.000000000Z","price_open":"0.15564","price_close":"0.1601","price_high":"0.1601","price_low":"0.15564","vwap":"0.15787","volume":"77000.1","candle_usd_volume":"12275.00001","candle_trades_count":"48"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:30:00.000000000Z","price_open":"0.15565","price_close":"0.16011","price_high":"0.16011","price_low":"0.15565","vwap":"0.15788","volume":"77000.11","candle_usd_volume":"12276.00001","candle_trades_count":"49"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:31:00.000000000Z","price_open":"0.15566","price_close":"0.16012","price_high":"0.16012","price_low":"0.15566","vwap":"0.15789","volume":"77000.12","candle_usd_volume":"12277.00001","candle_trades_count":"50"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:32:00.000000000Z","price_open":"0.15567","price_close":"0.16013","price_high":"0.16013","price_low":"0.15567","vwap":"0.1579","volume":"77000.13","candle_usd_volume":"12278.00001","candle_trades_count":"51"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:33:00.000000000Z","price_open":"0.15568","price_close":"0.16014","price_high":"0.16014","price_low":"0.15568","vwap":"0.15791","volume":"77000.14","candle_usd_volume":"12279.00001","candle_trades_count":"52"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:34:00.000000000Z","price_open":"0.15569","price_close":"0.16015","price_high":"0.16015","price_low":"0.15569","vwap":"0.15792","volume":"77000.15","candle_usd_volume":"12280.00001","candle_trades_count":"53"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:35:00.000000000Z","price_open":"0.1557","price_close":"0.16016","price_high":"0.16016","price_low":"0.1557","vwap":"0.15793","volume":"77000.16","candle_usd_volume":"12281.00001","candle_trades_count":"54"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:36:00.000000000Z","price_open":"0.15571","price_close":"0.16017","price_high":"0.16017","price_low":"0.15571","vwap":"0.15794","volume":"77000.17","candle_usd_volume":"12282.00001","candle_trades_count":"55"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:37:00.000000000Z","price_open":"0.15572","price_close":"0.16018","price_high":"0.16018","price_low":"0.15572","vwap":"0.15795","volume":"77000.18","candle_usd_volume":"12283.00001","candle_trades_count":"56"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-trx-usdt-spot&frequency=1m&paging_from=start&api_key=$TEST_API_KEY",
        )

        val expectedResponseCsv =
            """
            market,time,price_open,price_close,price_high,price_low,vwap,volume,candle_usd_volume,candle_trades_count
            bybit-trx-usdt-spot,2024-10-18T11:20:00.000000000Z,0.15897,0.15898,0.15901,0.15897,0.15900065419949308734,77188.84,12267.1084377712053879038564,41
            bybit-trx-usdt-spot,2024-10-18T11:21:00.000000000Z,0.15556,0.16002,0.16002,0.15556,0.15779,77000.02,12267.00001,41
            bybit-trx-usdt-spot,2024-10-18T11:22:00.000000000Z,0.15557,0.16003,0.16003,0.15557,0.1578,77000.03,12268.00001,42
            bybit-trx-usdt-spot,2024-10-18T11:23:00.000000000Z,0.15558,0.16004,0.16004,0.15558,0.15781,77000.04,12269.00001,43
            bybit-trx-usdt-spot,2024-10-18T11:24:00.000000000Z,0.15559,0.16005,0.16005,0.15559,0.15782,77000.05,12270.00001,44
            bybit-trx-usdt-spot,2024-10-18T11:25:00.000000000Z,0.1556,0.16006,0.16006,0.1556,0.15783,77000.06,12271.00001,45
            bybit-trx-usdt-spot,2024-10-18T11:26:00.000000000Z,0.15561,0.16007,0.16007,0.15561,0.15784,77000.07,12272.00001,46
            bybit-trx-usdt-spot,2024-10-18T11:27:00.000000000Z,0.15562,0.16008,0.16008,0.15562,0.15785,77000.08,12273.00001,47
            bybit-trx-usdt-spot,2024-10-18T11:28:00.000000000Z,0.15563,0.16009,0.16009,0.15563,0.15786,77000.09,12274.00001,48
            bybit-trx-usdt-spot,2024-10-18T11:29:00.000000000Z,0.15564,0.1601,0.1601,0.15564,0.15787,77000.1,12275.00001,48
            bybit-trx-usdt-spot,2024-10-18T11:30:00.000000000Z,0.15565,0.16011,0.16011,0.15565,0.15788,77000.11,12276.00001,49
            bybit-trx-usdt-spot,2024-10-18T11:31:00.000000000Z,0.15566,0.16012,0.16012,0.15566,0.15789,77000.12,12277.00001,50
            bybit-trx-usdt-spot,2024-10-18T11:32:00.000000000Z,0.15567,0.16013,0.16013,0.15567,0.1579,77000.13,12278.00001,51
            bybit-trx-usdt-spot,2024-10-18T11:33:00.000000000Z,0.15568,0.16014,0.16014,0.15568,0.15791,77000.14,12279.00001,52
            bybit-trx-usdt-spot,2024-10-18T11:34:00.000000000Z,0.15569,0.16015,0.16015,0.15569,0.15792,77000.15,12280.00001,53
            bybit-trx-usdt-spot,2024-10-18T11:35:00.000000000Z,0.1557,0.16016,0.16016,0.1557,0.15793,77000.16,12281.00001,54
            bybit-trx-usdt-spot,2024-10-18T11:36:00.000000000Z,0.15571,0.16017,0.16017,0.15571,0.15794,77000.17,12282.00001,55
            bybit-trx-usdt-spot,2024-10-18T11:37:00.000000000Z,0.15572,0.16018,0.16018,0.15572,0.15795,77000.18,12283.00001,56

            """.trimIndent()
        assertResponseWithContentType(
            expectedCode = 200,
            expectedResponse = expectedResponseCsv,
            contentType = "text/csv",
            pathAndQuery = "/v4/timeseries/market-candles?markets=bybit-trx-usdt-spot&frequency=1m&paging_from=start&format=csv&api_key=$TEST_API_KEY",
        )

        assertEquals(jsonResponseToCsvResponse(expectedResponse), expectedResponseCsv)
    }

    @Test
    fun `instant and delayed candles with frequency of 1m and paging from start and start time including delayed candles`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:20:00.000000000Z","price_open":"0.15897","price_close":"0.15898","price_high":"0.15901","price_low":"0.15897","vwap":"0.15900065419949308734","volume":"77188.84","candle_usd_volume":"12267.1084377712053879038564","candle_trades_count":"41"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:21:00.000000000Z","price_open":"0.15556","price_close":"0.16002","price_high":"0.16002","price_low":"0.15556","vwap":"0.15779","volume":"77000.02","candle_usd_volume":"12267.00001","candle_trades_count":"41"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:22:00.000000000Z","price_open":"0.15557","price_close":"0.16003","price_high":"0.16003","price_low":"0.15557","vwap":"0.1578","volume":"77000.03","candle_usd_volume":"12268.00001","candle_trades_count":"42"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:23:00.000000000Z","price_open":"0.15558","price_close":"0.16004","price_high":"0.16004","price_low":"0.15558","vwap":"0.15781","volume":"77000.04","candle_usd_volume":"12269.00001","candle_trades_count":"43"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:24:00.000000000Z","price_open":"0.15559","price_close":"0.16005","price_high":"0.16005","price_low":"0.15559","vwap":"0.15782","volume":"77000.05","candle_usd_volume":"12270.00001","candle_trades_count":"44"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:25:00.000000000Z","price_open":"0.1556","price_close":"0.16006","price_high":"0.16006","price_low":"0.1556","vwap":"0.15783","volume":"77000.06","candle_usd_volume":"12271.00001","candle_trades_count":"45"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:26:00.000000000Z","price_open":"0.15561","price_close":"0.16007","price_high":"0.16007","price_low":"0.15561","vwap":"0.15784","volume":"77000.07","candle_usd_volume":"12272.00001","candle_trades_count":"46"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:27:00.000000000Z","price_open":"0.15562","price_close":"0.16008","price_high":"0.16008","price_low":"0.15562","vwap":"0.15785","volume":"77000.08","candle_usd_volume":"12273.00001","candle_trades_count":"47"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:28:00.000000000Z","price_open":"0.15563","price_close":"0.16009","price_high":"0.16009","price_low":"0.15563","vwap":"0.15786","volume":"77000.09","candle_usd_volume":"12274.00001","candle_trades_count":"48"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:29:00.000000000Z","price_open":"0.15564","price_close":"0.1601","price_high":"0.1601","price_low":"0.15564","vwap":"0.15787","volume":"77000.1","candle_usd_volume":"12275.00001","candle_trades_count":"48"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:30:00.000000000Z","price_open":"0.15565","price_close":"0.16011","price_high":"0.16011","price_low":"0.15565","vwap":"0.15788","volume":"77000.11","candle_usd_volume":"12276.00001","candle_trades_count":"49"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:31:00.000000000Z","price_open":"0.15566","price_close":"0.16012","price_high":"0.16012","price_low":"0.15566","vwap":"0.15789","volume":"77000.12","candle_usd_volume":"12277.00001","candle_trades_count":"50"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:32:00.000000000Z","price_open":"0.15567","price_close":"0.16013","price_high":"0.16013","price_low":"0.15567","vwap":"0.1579","volume":"77000.13","candle_usd_volume":"12278.00001","candle_trades_count":"51"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:33:00.000000000Z","price_open":"0.15568","price_close":"0.16014","price_high":"0.16014","price_low":"0.15568","vwap":"0.15791","volume":"77000.14","candle_usd_volume":"12279.00001","candle_trades_count":"52"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:34:00.000000000Z","price_open":"0.15569","price_close":"0.16015","price_high":"0.16015","price_low":"0.15569","vwap":"0.15792","volume":"77000.15","candle_usd_volume":"12280.00001","candle_trades_count":"53"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:35:00.000000000Z","price_open":"0.1557","price_close":"0.16016","price_high":"0.16016","price_low":"0.1557","vwap":"0.15793","volume":"77000.16","candle_usd_volume":"12281.00001","candle_trades_count":"54"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:36:00.000000000Z","price_open":"0.15571","price_close":"0.16017","price_high":"0.16017","price_low":"0.15571","vwap":"0.15794","volume":"77000.17","candle_usd_volume":"12282.00001","candle_trades_count":"55"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:37:00.000000000Z","price_open":"0.15572","price_close":"0.16018","price_high":"0.16018","price_low":"0.15572","vwap":"0.15795","volume":"77000.18","candle_usd_volume":"12283.00001","candle_trades_count":"56"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-trx-usdt-spot&frequency=1m&paging_from=start&start_time=2024-10-18T11:20:00&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed candles with frequency of 1m and paging from start and start time excluding delayed candles`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:30:00.000000000Z","price_open":"0.15565","price_close":"0.16011","price_high":"0.16011","price_low":"0.15565","vwap":"0.15788","volume":"77000.11","candle_usd_volume":"12276.00001","candle_trades_count":"49"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:31:00.000000000Z","price_open":"0.15566","price_close":"0.16012","price_high":"0.16012","price_low":"0.15566","vwap":"0.15789","volume":"77000.12","candle_usd_volume":"12277.00001","candle_trades_count":"50"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:32:00.000000000Z","price_open":"0.15567","price_close":"0.16013","price_high":"0.16013","price_low":"0.15567","vwap":"0.1579","volume":"77000.13","candle_usd_volume":"12278.00001","candle_trades_count":"51"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:33:00.000000000Z","price_open":"0.15568","price_close":"0.16014","price_high":"0.16014","price_low":"0.15568","vwap":"0.15791","volume":"77000.14","candle_usd_volume":"12279.00001","candle_trades_count":"52"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:34:00.000000000Z","price_open":"0.15569","price_close":"0.16015","price_high":"0.16015","price_low":"0.15569","vwap":"0.15792","volume":"77000.15","candle_usd_volume":"12280.00001","candle_trades_count":"53"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:35:00.000000000Z","price_open":"0.1557","price_close":"0.16016","price_high":"0.16016","price_low":"0.1557","vwap":"0.15793","volume":"77000.16","candle_usd_volume":"12281.00001","candle_trades_count":"54"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:36:00.000000000Z","price_open":"0.15571","price_close":"0.16017","price_high":"0.16017","price_low":"0.15571","vwap":"0.15794","volume":"77000.17","candle_usd_volume":"12282.00001","candle_trades_count":"55"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:37:00.000000000Z","price_open":"0.15572","price_close":"0.16018","price_high":"0.16018","price_low":"0.15572","vwap":"0.15795","volume":"77000.18","candle_usd_volume":"12283.00001","candle_trades_count":"56"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-trx-usdt-spot&frequency=1m&paging_from=start&start_time=2024-10-18T11:30:00&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed candles with frequency of 1m and paging from start and start time and end time including delayed candles`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:20:00.000000000Z","price_open":"0.15897","price_close":"0.15898","price_high":"0.15901","price_low":"0.15897","vwap":"0.15900065419949308734","volume":"77188.84","candle_usd_volume":"12267.1084377712053879038564","candle_trades_count":"41"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:21:00.000000000Z","price_open":"0.15556","price_close":"0.16002","price_high":"0.16002","price_low":"0.15556","vwap":"0.15779","volume":"77000.02","candle_usd_volume":"12267.00001","candle_trades_count":"41"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:22:00.000000000Z","price_open":"0.15557","price_close":"0.16003","price_high":"0.16003","price_low":"0.15557","vwap":"0.1578","volume":"77000.03","candle_usd_volume":"12268.00001","candle_trades_count":"42"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-trx-usdt-spot&frequency=1m&paging_from=start&start_time=2024-10-18T11:20:00&end_time=2024-10-18T11:22:00&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed candles with frequency of 1m and paging from start and start time and end time excluding delayed candles`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:30:00.000000000Z","price_open":"0.15565","price_close":"0.16011","price_high":"0.16011","price_low":"0.15565","vwap":"0.15788","volume":"77000.11","candle_usd_volume":"12276.00001","candle_trades_count":"49"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:31:00.000000000Z","price_open":"0.15566","price_close":"0.16012","price_high":"0.16012","price_low":"0.15566","vwap":"0.15789","volume":"77000.12","candle_usd_volume":"12277.00001","candle_trades_count":"50"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:32:00.000000000Z","price_open":"0.15567","price_close":"0.16013","price_high":"0.16013","price_low":"0.15567","vwap":"0.1579","volume":"77000.13","candle_usd_volume":"12278.00001","candle_trades_count":"51"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:33:00.000000000Z","price_open":"0.15568","price_close":"0.16014","price_high":"0.16014","price_low":"0.15568","vwap":"0.15791","volume":"77000.14","candle_usd_volume":"12279.00001","candle_trades_count":"52"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:34:00.000000000Z","price_open":"0.15569","price_close":"0.16015","price_high":"0.16015","price_low":"0.15569","vwap":"0.15792","volume":"77000.15","candle_usd_volume":"12280.00001","candle_trades_count":"53"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-trx-usdt-spot&frequency=1m&paging_from=start&start_time=2024-10-18T11:30:00&end_time=2024-10-18T11:34:00&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed candles with frequency of 1m and paging from start and end time including delayed candles`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:20:00.000000000Z","price_open":"0.15897","price_close":"0.15898","price_high":"0.15901","price_low":"0.15897","vwap":"0.15900065419949308734","volume":"77188.84","candle_usd_volume":"12267.1084377712053879038564","candle_trades_count":"41"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:21:00.000000000Z","price_open":"0.15556","price_close":"0.16002","price_high":"0.16002","price_low":"0.15556","vwap":"0.15779","volume":"77000.02","candle_usd_volume":"12267.00001","candle_trades_count":"41"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:22:00.000000000Z","price_open":"0.15557","price_close":"0.16003","price_high":"0.16003","price_low":"0.15557","vwap":"0.1578","volume":"77000.03","candle_usd_volume":"12268.00001","candle_trades_count":"42"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:23:00.000000000Z","price_open":"0.15558","price_close":"0.16004","price_high":"0.16004","price_low":"0.15558","vwap":"0.15781","volume":"77000.04","candle_usd_volume":"12269.00001","candle_trades_count":"43"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:24:00.000000000Z","price_open":"0.15559","price_close":"0.16005","price_high":"0.16005","price_low":"0.15559","vwap":"0.15782","volume":"77000.05","candle_usd_volume":"12270.00001","candle_trades_count":"44"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-trx-usdt-spot&frequency=1m&paging_from=start&end_time=2024-10-18T11:24:00&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed candles with frequency of 1m and paging from start and end time excluding instant candles`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:20:00.000000000Z","price_open":"0.15897","price_close":"0.15898","price_high":"0.15901","price_low":"0.15897","vwap":"0.15900065419949308734","volume":"77188.84","candle_usd_volume":"12267.1084377712053879038564","candle_trades_count":"41"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-trx-usdt-spot&frequency=1m&paging_from=start&end_time=2024-10-18T11:20:00&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed candles with frequency of 5m and paging from start and 1 instant value is missed intentionally`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:20:00.000000000Z","price_open":"0.15897","price_close":"0.1589","price_high":"0.15901","price_low":"0.15888","vwap":"0.1589704600455617","volume":"150376.41","candle_usd_volume":"23895.68890582412088074642033413356746325003","candle_trades_count":"134"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:30:00.000000000Z","price_open":"0.16","price_close":"0.1601","price_high":"0.1601","price_low":"0.16","vwap":"0.16005","volume":"150000.01","candle_usd_volume":"23895.00001","candle_trades_count":"135"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:35:00.000000000Z","price_open":"0.16002","price_close":"0.16008","price_high":"0.16008","price_low":"0.16002","vwap":"0.16006","volume":"150001.01","candle_usd_volume":"23896.00001","candle_trades_count":"136"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-trx-usdt-spot&frequency=5m&paging_from=start&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed candles with frequency of 5m and paging from start and 1 instant value is missed intentionally and page size = 2`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:20:00.000000000Z","price_open":"0.15897","price_close":"0.1589","price_high":"0.15901","price_low":"0.15888","vwap":"0.1589704600455617","volume":"150376.41","candle_usd_volume":"23895.68890582412088074642033413356746325003","candle_trades_count":"134"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:30:00.000000000Z","price_open":"0.16","price_close":"0.1601","price_high":"0.1601","price_low":"0.16","vwap":"0.16005","volume":"150000.01","candle_usd_volume":"23895.00001","candle_trades_count":"135"}],"next_page_token":"0.MjAyNC0xMC0xOFQxMTozMDowMFo","next_page_url":"http://127.0.0.1:8080/v4/timeseries/market-candles?markets=bybit-trx-usdt-spot&frequency=5m&paging_from=start&page_size=2&api_key=x1&next_page_token=0.MjAyNC0xMC0xOFQxMTozMDowMFo"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-trx-usdt-spot&frequency=5m&paging_from=start&page_size=2&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed candles with frequency of 5m and paging from end and 1 instant value is missed intentionally and page size = 2`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:30:00.000000000Z","price_open":"0.16","price_close":"0.1601","price_high":"0.1601","price_low":"0.16","vwap":"0.16005","volume":"150000.01","candle_usd_volume":"23895.00001","candle_trades_count":"135"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:35:00.000000000Z","price_open":"0.16002","price_close":"0.16008","price_high":"0.16008","price_low":"0.16002","vwap":"0.16006","volume":"150001.01","candle_usd_volume":"23896.00001","candle_trades_count":"136"}],"next_page_token":"0.MjAyNC0xMC0xOFQxMTozMDowMFo","next_page_url":"http://127.0.0.1:8080/v4/timeseries/market-candles?markets=bybit-trx-usdt-spot&frequency=5m&paging_from=end&page_size=2&api_key=x1&next_page_token=0.MjAyNC0xMC0xOFQxMTozMDowMFo"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-trx-usdt-spot&frequency=5m&paging_from=end&page_size=2&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed candles with frequency of 10m and paging from start`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:20:00.000000000Z","price_open":"0.15897","price_close":"0.15891","price_high":"0.15901","price_low":"0.15888","vwap":"0.158960288888022","volume":"186874.83","candle_usd_volume":"29694.05028649258992450344505400967153696509","candle_trades_count":"200"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:30:00.000000000Z","price_open":"0.15444","price_close":"0.16444","price_high":"0.16444","price_low":"0.15444","vwap":"0.15888","volume":"186874.01","candle_usd_volume":"29694.00001","candle_trades_count":"201"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-trx-usdt-spot&frequency=10m&paging_from=start&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed candles with frequency of 10m and paging from start and page size = 1`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:20:00.000000000Z","price_open":"0.15897","price_close":"0.15891","price_high":"0.15901","price_low":"0.15888","vwap":"0.158960288888022","volume":"186874.83","candle_usd_volume":"29694.05028649258992450344505400967153696509","candle_trades_count":"200"}],"next_page_token":"0.MjAyNC0xMC0xOFQxMToyMDowMFo","next_page_url":"http://127.0.0.1:8080/v4/timeseries/market-candles?markets=bybit-trx-usdt-spot&frequency=10m&paging_from=start&page_size=1&api_key=x1&next_page_token=0.MjAyNC0xMC0xOFQxMToyMDowMFo"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-trx-usdt-spot&frequency=10m&paging_from=start&page_size=1&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed candles with frequency of 10m and paging from end and page size = 1`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:30:00.000000000Z","price_open":"0.15444","price_close":"0.16444","price_high":"0.16444","price_low":"0.15444","vwap":"0.15888","volume":"186874.01","candle_usd_volume":"29694.00001","candle_trades_count":"201"}],"next_page_token":"0.MjAyNC0xMC0xOFQxMTozMDowMFo","next_page_url":"http://127.0.0.1:8080/v4/timeseries/market-candles?markets=bybit-trx-usdt-spot&frequency=10m&paging_from=end&page_size=1&api_key=x1&next_page_token=0.MjAyNC0xMC0xOFQxMTozMDowMFo"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-trx-usdt-spot&frequency=10m&paging_from=end&page_size=1&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed candles with frequency of 15m and paging from start`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:15:00.000000000Z","price_open":"0.15903","price_close":"0.15891","price_high":"0.15903","price_low":"0.15888","vwap":"0.1589748654124442","volume":"400331.96","candle_usd_volume":"63618.77307723008592494781199918967153696509","candle_trades_count":"335"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:30:00.000000000Z","price_open":"0.15555","price_close":"0.15666","price_high":"0.15666","price_low":"0.15555","vwap":"0.156","volume":"400331.01","candle_usd_volume":"63618.00001","candle_trades_count":"336"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-trx-usdt-spot&frequency=15m&paging_from=start&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed candles with frequency of 15m and paging from start and page size = 1`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:15:00.000000000Z","price_open":"0.15903","price_close":"0.15891","price_high":"0.15903","price_low":"0.15888","vwap":"0.1589748654124442","volume":"400331.96","candle_usd_volume":"63618.77307723008592494781199918967153696509","candle_trades_count":"335"}],"next_page_token":"0.MjAyNC0xMC0xOFQxMToxNTowMFo","next_page_url":"http://127.0.0.1:8080/v4/timeseries/market-candles?markets=bybit-trx-usdt-spot&frequency=15m&paging_from=start&page_size=1&api_key=x1&next_page_token=0.MjAyNC0xMC0xOFQxMToxNTowMFo"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-trx-usdt-spot&frequency=15m&paging_from=start&page_size=1&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed candles with frequency of 15m and paging from end and page size = 1`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:30:00.000000000Z","price_open":"0.15555","price_close":"0.15666","price_high":"0.15666","price_low":"0.15555","vwap":"0.156","volume":"400331.01","candle_usd_volume":"63618.00001","candle_trades_count":"336"}],"next_page_token":"0.MjAyNC0xMC0xOFQxMTozMDowMFo","next_page_url":"http://127.0.0.1:8080/v4/timeseries/market-candles?markets=bybit-trx-usdt-spot&frequency=15m&paging_from=end&page_size=1&api_key=x1&next_page_token=0.MjAyNC0xMC0xOFQxMTozMDowMFo"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-trx-usdt-spot&frequency=15m&paging_from=end&page_size=1&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed candles with frequency of 30m and paging from start`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:00:00.000000000Z","price_open":"0.15908","price_close":"0.15891","price_high":"0.15914","price_low":"0.15888","vwap":"0.1590188641784494","volume":"717288.38","candle_usd_volume":"114021.61717095928122785887028239535379449553","candle_trades_count":"670"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:30:00.000000000Z","price_open":"0.15901","price_close":"0.15991","price_high":"0.15991","price_low":"0.15901","vwap":"0.15951","volume":"717288.01","candle_usd_volume":"114021.00001","candle_trades_count":"671"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-trx-usdt-spot&frequency=30m&paging_from=start&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed candles with frequency of 30m and paging from start and page size = 1`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:00:00.000000000Z","price_open":"0.15908","price_close":"0.15891","price_high":"0.15914","price_low":"0.15888","vwap":"0.1590188641784494","volume":"717288.38","candle_usd_volume":"114021.61717095928122785887028239535379449553","candle_trades_count":"670"}],"next_page_token":"0.MjAyNC0xMC0xOFQxMTowMDowMFo","next_page_url":"http://127.0.0.1:8080/v4/timeseries/market-candles?markets=bybit-trx-usdt-spot&frequency=30m&paging_from=start&page_size=1&api_key=x1&next_page_token=0.MjAyNC0xMC0xOFQxMTowMDowMFo"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-trx-usdt-spot&frequency=30m&paging_from=start&page_size=1&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed candles with frequency of 30m and paging from end and page size = 1`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:30:00.000000000Z","price_open":"0.15901","price_close":"0.15991","price_high":"0.15991","price_low":"0.15901","vwap":"0.15951","volume":"717288.01","candle_usd_volume":"114021.00001","candle_trades_count":"671"}],"next_page_token":"0.MjAyNC0xMC0xOFQxMTozMDowMFo","next_page_url":"http://127.0.0.1:8080/v4/timeseries/market-candles?markets=bybit-trx-usdt-spot&frequency=30m&paging_from=end&page_size=1&api_key=x1&next_page_token=0.MjAyNC0xMC0xOFQxMTozMDowMFo"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-trx-usdt-spot&frequency=30m&paging_from=end&page_size=1&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed candles with frequency of 1h and paging from start`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-trx-usdt-spot","time":"2024-10-16T13:00:00.000000000Z","price_open":"0.15408","price_close":"0.15491","price_high":"0.15414","price_low":"0.15488","vwap":"0.1540188641784494","volume":"714288.38","candle_usd_volume":"114021.61717095928122785887028239535379449553","candle_trades_count":"645"},{"market":"bybit-trx-usdt-spot","time":"2024-10-17T11:00:00.000000000Z","price_open":"0.15508","price_close":"0.15591","price_high":"0.15514","price_low":"0.15588","vwap":"0.1550188641784494","volume":"715288.38","candle_usd_volume":"115021.61717095928122785887028239535379449553","candle_trades_count":"675"},{"market":"bybit-trx-usdt-spot","time":"2024-10-17T12:00:00.000000000Z","price_open":"0.15608","price_close":"0.15691","price_high":"0.15614","price_low":"0.15688","vwap":"0.1560188641784494","volume":"716288.38","candle_usd_volume":"116021.61717095928122785887028239535379449553","candle_trades_count":"676"},{"market":"bybit-trx-usdt-spot","time":"2024-10-17T13:00:00.000000000Z","price_open":"0.16608","price_close":"0.16691","price_high":"0.16614","price_low":"0.16688","vwap":"0.1660188641784494","volume":"766288.38","candle_usd_volume":"116026.61717095928122785887028239535379449553","candle_trades_count":"679"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T10:00:00.000000000Z","price_open":"0.1795","price_close":"0.1795","price_high":"0.1795","price_low":"0.1795","vwap":"0.1755","volume":"1783996.01","candle_usd_volume":"220277.00001","candle_trades_count":"1753"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:00:00.000000000Z","price_open":"0.15908","price_close":"0.1589","price_high":"0.15914","price_low":"0.15884","vwap":"0.1589882063083388","volume":"1385996.91","candle_usd_volume":"220279.73634820367613065647384011961914315841","candle_trades_count":"1258"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T12:00:00.000000000Z","price_open":"0.1595","price_close":"0.1695","price_high":"0.1695","price_low":"0.1595","vwap":"0.1655","volume":"1385996.01","candle_usd_volume":"220279.00001","candle_trades_count":"1259"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T13:00:00.000000000Z","price_open":"0.1395","price_close":"0.1395","price_high":"0.1395","price_low":"0.1395","vwap":"0.1355","volume":"1383996.01","candle_usd_volume":"220273.00001","candle_trades_count":"1253"},{"market":"bybit-trx-usdt-spot","time":"2024-10-19T10:00:00.000000000Z","price_open":"0.1295","price_close":"0.1295","price_high":"0.1295","price_low":"0.1295","vwap":"0.1255","volume":"1284996.01","candle_usd_volume":"220272.00001","candle_trades_count":"1252"},{"market":"bybit-trx-usdt-spot","time":"2024-10-19T11:00:00.000000000Z","price_open":"0.1695","price_close":"0.1695","price_high":"0.1695","price_low":"0.1695","vwap":"0.1655","volume":"1684996.01","candle_usd_volume":"220276.00001","candle_trades_count":"1256"},{"market":"bybit-trx-usdt-spot","time":"2024-10-19T12:00:00.000000000Z","price_open":"0.1795","price_close":"0.1795","price_high":"0.1795","price_low":"0.1795","vwap":"0.1755","volume":"1784996.01","candle_usd_volume":"220277.00001","candle_trades_count":"1257"},{"market":"bybit-trx-usdt-spot","time":"2024-10-20T10:00:00.000000000Z","price_open":"0.2295","price_close":"0.2295","price_high":"0.2295","price_low":"0.2295","vwap":"0.2255","volume":"2284996.01","candle_usd_volume":"420272.00001","candle_trades_count":"2252"},{"market":"bybit-trx-usdt-spot","time":"2024-10-20T11:00:00.000000000Z","price_open":"0.2695","price_close":"0.2695","price_high":"0.2695","price_low":"0.2695","vwap":"0.2655","volume":"2684996.01","candle_usd_volume":"420276.00001","candle_trades_count":"2256"},{"market":"bybit-trx-usdt-spot","time":"2024-10-20T12:00:00.000000000Z","price_open":"0.2795","price_close":"0.2795","price_high":"0.2795","price_low":"0.2795","vwap":"0.2755","volume":"2784996.01","candle_usd_volume":"420277.00001","candle_trades_count":"2257"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-trx-usdt-spot&frequency=1h&paging_from=start&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed candles with frequency of 1h and paging from start and page size = 1`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-trx-usdt-spot","time":"2024-10-16T13:00:00.000000000Z","price_open":"0.15408","price_close":"0.15491","price_high":"0.15414","price_low":"0.15488","vwap":"0.1540188641784494","volume":"714288.38","candle_usd_volume":"114021.61717095928122785887028239535379449553","candle_trades_count":"645"}],"next_page_token":"0.MjAyNC0xMC0xNlQxMzowMDowMFo","next_page_url":"http://127.0.0.1:8080/v4/timeseries/market-candles?markets=bybit-trx-usdt-spot&frequency=1h&paging_from=start&page_size=1&api_key=x1&next_page_token=0.MjAyNC0xMC0xNlQxMzowMDowMFo"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-trx-usdt-spot&frequency=1h&paging_from=start&page_size=1&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed candles with frequency of 1h and paging from end and page size = 1`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-trx-usdt-spot","time":"2024-10-20T12:00:00.000000000Z","price_open":"0.2795","price_close":"0.2795","price_high":"0.2795","price_low":"0.2795","vwap":"0.2755","volume":"2784996.01","candle_usd_volume":"420277.00001","candle_trades_count":"2257"}],"next_page_token":"0.MjAyNC0xMC0yMFQxMjowMDowMFo","next_page_url":"http://127.0.0.1:8080/v4/timeseries/market-candles?markets=bybit-trx-usdt-spot&frequency=1h&paging_from=end&page_size=1&api_key=x1&next_page_token=0.MjAyNC0xMC0yMFQxMjowMDowMFo"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-trx-usdt-spot&frequency=1h&paging_from=end&page_size=1&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed candles with frequency of 4h and paging from start`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-trx-usdt-spot","time":"2024-10-18T08:00:00.000000000Z","price_open":"0.1591","price_close":"0.1589","price_high":"0.15921","price_low":"0.15884","vwap":"0.159053781252249","volume":"5612564.49","candle_usd_volume":"892352.18298533114606336454322945869720670277","candle_trades_count":"4706"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T12:00:00.000000000Z","price_open":"0.16222","price_close":"0.16333","price_high":"0.16333","price_low":"0.16222","vwap":"0.16666","volume":"5612564.01","candle_usd_volume":"892352.00001","candle_trades_count":"4707"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-trx-usdt-spot&frequency=4h&paging_from=start&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed candles with frequency of 4h and paging from start and page size = 1`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-trx-usdt-spot","time":"2024-10-18T08:00:00.000000000Z","price_open":"0.1591","price_close":"0.1589","price_high":"0.15921","price_low":"0.15884","vwap":"0.159053781252249","volume":"5612564.49","candle_usd_volume":"892352.18298533114606336454322945869720670277","candle_trades_count":"4706"}],"next_page_token":"0.MjAyNC0xMC0xOFQwODowMDowMFo","next_page_url":"http://127.0.0.1:8080/v4/timeseries/market-candles?markets=bybit-trx-usdt-spot&frequency=4h&paging_from=start&page_size=1&api_key=x1&next_page_token=0.MjAyNC0xMC0xOFQwODowMDowMFo"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-trx-usdt-spot&frequency=4h&paging_from=start&page_size=1&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed candles with frequency of 4h and paging from end and page size = 1`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-trx-usdt-spot","time":"2024-10-18T12:00:00.000000000Z","price_open":"0.16222","price_close":"0.16333","price_high":"0.16333","price_low":"0.16222","vwap":"0.16666","volume":"5612564.01","candle_usd_volume":"892352.00001","candle_trades_count":"4707"}],"next_page_token":"0.MjAyNC0xMC0xOFQxMjowMDowMFo","next_page_url":"http://127.0.0.1:8080/v4/timeseries/market-candles?markets=bybit-trx-usdt-spot&frequency=4h&paging_from=end&page_size=1&api_key=x1&next_page_token=0.MjAyNC0xMC0xOFQxMjowMDowMFo"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-trx-usdt-spot&frequency=4h&paging_from=end&page_size=1&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed candles with frequency of 1d and paging from start`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-trx-usdt-spot","time":"2024-10-17T00:00:00.000000000Z","price_open":"0.1601","price_close":"0.15938","price_high":"0.16056","price_low":"0.1591","vwap":"0.159938246016863","volume":"38099958.37","candle_usd_volume":"6091654.72791880882081629919919093990618258893","candle_trades_count":"27656"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T00:00:00.000000000Z","price_open":"0.161","price_close":"0.162","price_high":"0.162","price_low":"0.161","vwap":"0.1615","volume":"38099958.01","candle_usd_volume":"6091654.00001","candle_trades_count":"27657"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-trx-usdt-spot&frequency=1d&paging_from=start&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed candles with frequency of 1d and paging from start and page size = 1`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-trx-usdt-spot","time":"2024-10-17T00:00:00.000000000Z","price_open":"0.1601","price_close":"0.15938","price_high":"0.16056","price_low":"0.1591","vwap":"0.159938246016863","volume":"38099958.37","candle_usd_volume":"6091654.72791880882081629919919093990618258893","candle_trades_count":"27656"}],"next_page_token":"0.MjAyNC0xMC0xN1QwMDowMDowMFo","next_page_url":"http://127.0.0.1:8080/v4/timeseries/market-candles?markets=bybit-trx-usdt-spot&frequency=1d&paging_from=start&page_size=1&api_key=x1&next_page_token=0.MjAyNC0xMC0xN1QwMDowMDowMFo"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-trx-usdt-spot&frequency=1d&paging_from=start&page_size=1&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed candles with frequency of 1d and paging from end and page size = 1`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-trx-usdt-spot","time":"2024-10-18T00:00:00.000000000Z","price_open":"0.161","price_close":"0.162","price_high":"0.162","price_low":"0.161","vwap":"0.1615","volume":"38099958.01","candle_usd_volume":"6091654.00001","candle_trades_count":"27657"}],"next_page_token":"0.MjAyNC0xMC0xOFQwMDowMDowMFo","next_page_url":"http://127.0.0.1:8080/v4/timeseries/market-candles?markets=bybit-trx-usdt-spot&frequency=1d&paging_from=end&page_size=1&api_key=x1&next_page_token=0.MjAyNC0xMC0xOFQwMDowMDowMFo"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-trx-usdt-spot&frequency=1d&paging_from=end&page_size=1&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed candles with frequency of 1d-1100 and paging from start`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-trx-usdt-spot","time":"2024-10-17T11:00:00.000000000Z","price_open":"0.15508","price_close":"0.1795","price_high":"0.1795","price_low":"0.15588","vwap":"0.16649182203031079462","volume":"3981861.15","candle_usd_volume":"567346.85152287784368357661084718606138348659","candle_trades_count":"3783"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:00:00.000000000Z","price_open":"0.15908","price_close":"0.1295","price_high":"0.1695","price_low":"0.1295","vwap":"0.14676349406230852093","volume":"5440984.94","candle_usd_volume":"881103.73637820367613065647384011961914315841","candle_trades_count":"5022"},{"market":"bybit-trx-usdt-spot","time":"2024-10-19T11:00:00.000000000Z","price_open":"0.1695","price_close":"0.2295","price_high":"0.2295","price_low":"0.1695","vwap":"0.19242442102264459445","volume":"5754988.03","candle_usd_volume":"860825.00003","candle_trades_count":"4765"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-trx-usdt-spot&frequency=1d-11:00&paging_from=start&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed candles with frequency of 1d-1100 and paging from end`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-trx-usdt-spot","time":"2024-10-17T11:00:00.000000000Z","price_open":"0.15508","price_close":"0.1795","price_high":"0.1795","price_low":"0.15588","vwap":"0.16649182203031079462","volume":"3981861.15","candle_usd_volume":"567346.85152287784368357661084718606138348659","candle_trades_count":"3783"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:00:00.000000000Z","price_open":"0.15908","price_close":"0.1295","price_high":"0.1695","price_low":"0.1295","vwap":"0.14676349406230852093","volume":"5440984.94","candle_usd_volume":"881103.73637820367613065647384011961914315841","candle_trades_count":"5022"},{"market":"bybit-trx-usdt-spot","time":"2024-10-19T11:00:00.000000000Z","price_open":"0.1695","price_close":"0.2295","price_high":"0.2295","price_low":"0.1695","vwap":"0.19242442102264459445","volume":"5754988.03","candle_usd_volume":"860825.00003","candle_trades_count":"4765"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-trx-usdt-spot&frequency=1d-11:00&paging_from=end&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed candles with frequency of 1d-1200 and paging from start`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-trx-usdt-spot","time":"2024-10-17T12:00:00.000000000Z","price_open":"0.15608","price_close":"0.1589","price_high":"0.1795","price_low":"0.15688","vwap":"0.16602035896372983656","volume":"4652569.68","candle_usd_volume":"672604.97070012223858637421440491032673214947","candle_trades_count":"4366"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T12:00:00.000000000Z","price_open":"0.1595","price_close":"0.1695","price_high":"0.1695","price_low":"0.1295","vwap":"0.14931185033747933557","volume":"5739984.04","candle_usd_volume":"881100.00004","candle_trades_count":"5020"},{"market":"bybit-trx-usdt-spot","time":"2024-10-19T12:00:00.000000000Z","price_open":"0.1795","price_close":"0.2695","price_high":"0.2695","price_low":"0.1795","vwap":"0.22818690926162899507","volume":"6754988.03","candle_usd_volume":"1060825.00003","candle_trades_count":"5765"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-trx-usdt-spot&frequency=1d-12:00&paging_from=start&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed candles with frequency of 1d-1200 and paging from end`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-trx-usdt-spot","time":"2024-10-17T12:00:00.000000000Z","price_open":"0.15608","price_close":"0.1589","price_high":"0.1795","price_low":"0.15688","vwap":"0.16602035896372983656","volume":"4652569.68","candle_usd_volume":"672604.97070012223858637421440491032673214947","candle_trades_count":"4366"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T12:00:00.000000000Z","price_open":"0.1595","price_close":"0.1695","price_high":"0.1695","price_low":"0.1295","vwap":"0.14931185033747933557","volume":"5739984.04","candle_usd_volume":"881100.00004","candle_trades_count":"5020"},{"market":"bybit-trx-usdt-spot","time":"2024-10-19T12:00:00.000000000Z","price_open":"0.1795","price_close":"0.2695","price_high":"0.2695","price_low":"0.1795","vwap":"0.22818690926162899507","volume":"6754988.03","candle_usd_volume":"1060825.00003","candle_trades_count":"5765"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-trx-usdt-spot&frequency=1d-12:00&paging_from=end&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed candles with frequency of 1d-1300 and paging from start`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-trx-usdt-spot","time":"2024-10-16T13:00:00.000000000Z","price_open":"0.15408","price_close":"0.15691","price_high":"0.15614","price_low":"0.15488","vwap":"0.15501979620346938798","volume":"2145865.14","candle_usd_volume":"345064.85151287784368357661084718606138348659","candle_trades_count":"1996"},{"market":"bybit-trx-usdt-spot","time":"2024-10-17T13:00:00.000000000Z","price_open":"0.16608","price_close":"0.1695","price_high":"0.1795","price_low":"0.15884","vwap":"0.16723088195502990536","volume":"5322277.31","candle_usd_volume":"776862.35353916295735851534412251497293765394","candle_trades_count":"4949"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T13:00:00.000000000Z","price_open":"0.1395","price_close":"0.1795","price_high":"0.1795","price_low":"0.1295","vwap":"0.15327163124861292195","volume":"6138984.04","candle_usd_volume":"881098.00004","candle_trades_count":"5018"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-trx-usdt-spot&frequency=1d-13:00&paging_from=start&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed candles with frequency of 1d-1300 and paging from end`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-trx-usdt-spot","time":"2024-10-16T13:00:00.000000000Z","price_open":"0.15408","price_close":"0.15691","price_high":"0.15614","price_low":"0.15488","vwap":"0.15501979620346938798","volume":"2145865.14","candle_usd_volume":"345064.85151287784368357661084718606138348659","candle_trades_count":"1996"},{"market":"bybit-trx-usdt-spot","time":"2024-10-17T13:00:00.000000000Z","price_open":"0.16608","price_close":"0.1695","price_high":"0.1795","price_low":"0.15884","vwap":"0.16723088195502990536","volume":"5322277.31","candle_usd_volume":"776862.35353916295735851534412251497293765394","candle_trades_count":"4949"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T13:00:00.000000000Z","price_open":"0.1395","price_close":"0.1795","price_high":"0.1795","price_low":"0.1295","vwap":"0.15327163124861292195","volume":"6138984.04","candle_usd_volume":"881098.00004","candle_trades_count":"5018"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-trx-usdt-spot&frequency=1d-13:00&paging_from=end&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed candles with frequency of 1d-0900 and New York time zone and paging from start`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-trx-usdt-spot","time":"2024-10-16T13:00:00.000000000Z","price_open":"0.15408","price_close":"0.15691","price_high":"0.15614","price_low":"0.15488","vwap":"0.15501979620346938798","volume":"2145865.14","candle_usd_volume":"345064.85151287784368357661084718606138348659","candle_trades_count":"1996"},{"market":"bybit-trx-usdt-spot","time":"2024-10-17T13:00:00.000000000Z","price_open":"0.16608","price_close":"0.1695","price_high":"0.1795","price_low":"0.15884","vwap":"0.16723088195502990536","volume":"5322277.31","candle_usd_volume":"776862.35353916295735851534412251497293765394","candle_trades_count":"4949"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T13:00:00.000000000Z","price_open":"0.1395","price_close":"0.1795","price_high":"0.1795","price_low":"0.1295","vwap":"0.15327163124861292195","volume":"6138984.04","candle_usd_volume":"881098.00004","candle_trades_count":"5018"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-trx-usdt-spot&frequency=1d-09:00&timezone=America/New_York&paging_from=start&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed candles with frequency of 1d-0900 and New York time zone and paging from end`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-trx-usdt-spot","time":"2024-10-16T13:00:00.000000000Z","price_open":"0.15408","price_close":"0.15691","price_high":"0.15614","price_low":"0.15488","vwap":"0.15501979620346938798","volume":"2145865.14","candle_usd_volume":"345064.85151287784368357661084718606138348659","candle_trades_count":"1996"},{"market":"bybit-trx-usdt-spot","time":"2024-10-17T13:00:00.000000000Z","price_open":"0.16608","price_close":"0.1695","price_high":"0.1795","price_low":"0.15884","vwap":"0.16723088195502990536","volume":"5322277.31","candle_usd_volume":"776862.35353916295735851534412251497293765394","candle_trades_count":"4949"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T13:00:00.000000000Z","price_open":"0.1395","price_close":"0.1795","price_high":"0.1795","price_low":"0.1295","vwap":"0.15327163124861292195","volume":"6138984.04","candle_usd_volume":"881098.00004","candle_trades_count":"5018"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-trx-usdt-spot&frequency=1d-09:00&timezone=America/New_York&paging_from=end&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed candles with frequency of 1d-0900 and New York time zone and paging from start and page size = 1`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-trx-usdt-spot","time":"2024-10-16T13:00:00.000000000Z","price_open":"0.15408","price_close":"0.15691","price_high":"0.15614","price_low":"0.15488","vwap":"0.15501979620346938798","volume":"2145865.14","candle_usd_volume":"345064.85151287784368357661084718606138348659","candle_trades_count":"1996"}],"next_page_token":"0.MjAyNC0xMC0xNlQxMzowMDowMFo","next_page_url":"http://127.0.0.1:8080/v4/timeseries/market-candles?markets=bybit-trx-usdt-spot&frequency=1d-09%3A00&timezone=America%2FNew_York&paging_from=start&page_size=1&api_key=x1&next_page_token=0.MjAyNC0xMC0xNlQxMzowMDowMFo"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-trx-usdt-spot&frequency=1d-09:00&timezone=America/New_York&paging_from=start&page_size=1&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed candles with frequency of 1d-0900 and New York time zone and paging from end and page size = 1`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-trx-usdt-spot","time":"2024-10-18T13:00:00.000000000Z","price_open":"0.1395","price_close":"0.1795","price_high":"0.1795","price_low":"0.1295","vwap":"0.15327163124861292195","volume":"6138984.04","candle_usd_volume":"881098.00004","candle_trades_count":"5018"}],"next_page_token":"0.MjAyNC0xMC0xOFQxMzowMDowMFo","next_page_url":"http://127.0.0.1:8080/v4/timeseries/market-candles?markets=bybit-trx-usdt-spot&frequency=1d-09%3A00&timezone=America%2FNew_York&paging_from=end&page_size=1&api_key=x1&next_page_token=0.MjAyNC0xMC0xOFQxMzowMDowMFo"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-trx-usdt-spot&frequency=1d-09:00&timezone=America/New_York&paging_from=end&page_size=1&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed candles with frequency of 1d-0900 and New York time zone and paging from start and start time and end time`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-trx-usdt-spot","time":"2024-10-17T13:00:00.000000000Z","price_open":"0.16608","price_close":"0.1695","price_high":"0.1795","price_low":"0.15884","vwap":"0.16723088195502990536","volume":"5322277.31","candle_usd_volume":"776862.35353916295735851534412251497293765394","candle_trades_count":"4949"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-trx-usdt-spot&frequency=1d-09:00&timezone=America/New_York&start_time=2024-10-17T09:00:00&end_time=2024-10-17T09:00:00&paging_from=start&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed candles with frequency of 1d-0900 and New York time zone and paging from end and start time and end time`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-trx-usdt-spot","time":"2024-10-18T13:00:00.000000000Z","price_open":"0.1395","price_close":"0.1795","price_high":"0.1795","price_low":"0.1295","vwap":"0.15327163124861292195","volume":"6138984.04","candle_usd_volume":"881098.00004","candle_trades_count":"5018"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-trx-usdt-spot&frequency=1d-09:00&timezone=America/New_York&start_time=2024-10-18T09:00:00&end_time=2024-10-18T09:00:00&paging_from=start&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `400 when instant candles exist but delayed candles do not exist`() {
        val expectedResponse = """{"error":{"type":"bad_parameter","message":"Bad parameter 'markets'. Market 'coinbase-zeta-usd-spot' is not supported."}}"""
        assertResponse(
            400,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=coinbase-zeta-usd-spot&frequency=1m&paging_from=start&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `only instant candles when start time within last 20 minutes`() {
        val expectedResponse = """{"data":[{"market":"kraken-pol-usd-spot","time":"2024-10-18T11:55:00.000000000Z","price_open":"0.40373","price_close":"0.40373","price_high":"0.40373","price_low":"0.40373","vwap":"0.40373","volume":"8.81329","candle_usd_volume":"4.3731305717","candle_trades_count":"2"},{"market":"kraken-pol-usd-spot","time":"2024-10-18T12:14:00.000000000Z","price_open":"0.40373","price_close":"0.40373","price_high":"0.40373","price_low":"0.40373","vwap":"0.40373","volume":"8.81329","candle_usd_volume":"4.3731305717","candle_trades_count":"2"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=kraken-pol-usd-spot&frequency=1m&paging_from=start&start_time=2024-10-18T11:55:00&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed candles when candle_end_time within last 21 candle_end_time minutes`() {
        val expectedResponse = """{"data":[{"market":"kraken-pol-usd-spot","time":"2024-10-18T11:54:00.000000000Z","price_open":"0.30373","price_close":"0.30373","price_high":"0.30373","price_low":"0.30373","vwap":"0.30373","volume":"7.81329","candle_usd_volume":"2.3731305717","candle_trades_count":"1"},{"market":"kraken-pol-usd-spot","time":"2024-10-18T11:55:00.000000000Z","price_open":"0.40373","price_close":"0.40373","price_high":"0.40373","price_low":"0.40373","vwap":"0.40373","volume":"8.81329","candle_usd_volume":"4.3731305717","candle_trades_count":"2"},{"market":"kraken-pol-usd-spot","time":"2024-10-18T12:14:00.000000000Z","price_open":"0.40373","price_close":"0.40373","price_high":"0.40373","price_low":"0.40373","vwap":"0.40373","volume":"8.81329","candle_usd_volume":"4.3731305717","candle_trades_count":"2"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=kraken-pol-usd-spot&frequency=1m&paging_from=start&start_time=2024-10-18T11:54:00&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant candles when candle_end_time within last 20 candle_end_time minutes and frequency 1m`() {
        val expectedResponse = """{"data":[{"market":"kraken-pol-usd-spot","time":"2024-10-18T11:55:00.000000000Z","price_open":"0.40373","price_close":"0.40373","price_high":"0.40373","price_low":"0.40373","vwap":"0.40373","volume":"8.81329","candle_usd_volume":"4.3731305717","candle_trades_count":"2"},{"market":"kraken-pol-usd-spot","time":"2024-10-18T12:14:00.000000000Z","price_open":"0.40373","price_close":"0.40373","price_high":"0.40373","price_low":"0.40373","vwap":"0.40373","volume":"8.81329","candle_usd_volume":"4.3731305717","candle_trades_count":"2"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=kraken-pol-usd-spot&frequency=1m&paging_from=start&start_time=2024-10-18T11:55:00&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant candles when candle_end_time within last 20 candle_end_time minutes and frequency 5m`() {
        val expectedResponse = """{"data":[{"market":"kraken-pol-usd-spot","time":"2024-10-18T11:55:00.000000000Z","price_open":"0.40375","price_close":"0.40375","price_high":"0.40375","price_low":"0.40375","vwap":"0.40375","volume":"8.81325","candle_usd_volume":"4.3731305715","candle_trades_count":"5"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=kraken-pol-usd-spot&frequency=5m&paging_from=start&start_time=2024-10-18T11:51:00&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant candles when candle_end_time within last 20 candle_end_time minutes and frequency 10m`() {
        val expectedResponse = """{"data":[{"market":"kraken-pol-usd-spot","time":"2024-10-18T11:50:00.000000000Z","price_open":"0.40385","price_close":"0.40385","price_high":"0.40385","price_low":"0.40385","vwap":"0.40385","volume":"8.81385","candle_usd_volume":"4.3731305785","candle_trades_count":"8"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=kraken-pol-usd-spot&frequency=10m&paging_from=start&start_time=2024-10-18T11:46:00&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant candles when candle_end_time within last 20 candle_end_time minutes and frequency 15m`() {
        val expectedResponse = """{"data":[{"market":"kraken-pol-usd-spot","time":"2024-10-18T11:45:00.000000000Z","price_open":"0.40585","price_close":"0.40585","price_high":"0.40585","price_low":"0.40585","vwap":"0.40585","volume":"8.81585","candle_usd_volume":"4.3731505785","candle_trades_count":"8"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=kraken-pol-usd-spot&frequency=15m&paging_from=start&start_time=2024-10-18T11:41:00&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant candles when candle_end_time within last 20 candle_end_time minutes and frequency 30m`() {
        val expectedResponse = """{"data":[{"market":"kraken-pol-usd-spot","time":"2024-10-18T11:30:00.000000000Z","price_open":"0.41585","price_close":"0.41585","price_high":"0.41585","price_low":"0.41585","vwap":"0.41585","volume":"8.82585","candle_usd_volume":"4.3931505785","candle_trades_count":"9"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=kraken-pol-usd-spot&frequency=30m&paging_from=start&start_time=2024-10-18T11:26:00&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant candles when candle_end_time within last 20 candle_end_time minutes and frequency 1h`() {
        val expectedResponse = """{"data":[{"market":"kraken-pol-usd-spot","time":"2024-10-18T11:00:00.000000000Z","price_open":"0.43585","price_close":"0.43585","price_high":"0.43585","price_low":"0.43585","vwap":"0.43585","volume":"8.83585","candle_usd_volume":"4.4931505785","candle_trades_count":"10"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=kraken-pol-usd-spot&frequency=1h&paging_from=start&start_time=2024-10-18T10:56:00&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant candles when candle_end_time within last 20 candle_end_time minutes and frequency 4h`() {
        val expectedResponse = """{"data":[{"market":"kraken-pol-usd-spot","time":"2024-10-18T08:00:00.000000000Z","price_open":"0.45585","price_close":"0.45585","price_high":"0.45585","price_low":"0.45585","vwap":"0.45585","volume":"8.85585","candle_usd_volume":"4.5931505785","candle_trades_count":"11"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=kraken-pol-usd-spot&frequency=4h&paging_from=start&start_time=2024-10-18T07:56:00&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant candles when candle_end_time within last 20 candle_end_time minutes and frequency 1d`() {
        val expectedResponse = """{"data":[{"market":"kraken-pol-usd-spot","time":"2024-10-18T00:00:00.000000000Z","price_open":"0.46585","price_close":"0.46585","price_high":"0.46585","price_low":"0.46585","vwap":"0.46585","volume":"8.86585","candle_usd_volume":"4.6931505785","candle_trades_count":"13"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=kraken-pol-usd-spot&frequency=1d&paging_from=start&start_time=2024-10-17T11:56:00&api_key=$TEST_API_KEY",
        )
    }

    // FUTURES

    @Test
    fun `instant and delayed future candles with frequency of 1m and paging from start`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:20:00.000000000Z","price_open":"0.15897","price_close":"0.15898","price_high":"0.15901","price_low":"0.15897","vwap":"0.15900065419949308734","volume":"77188.84","candle_usd_volume":"12267.1084377712053879038564","candle_trades_count":"41"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:21:00.000000000Z","price_open":"0.15556","price_close":"0.16002","price_high":"0.16002","price_low":"0.15556","vwap":"0.15779","volume":"77000.02","candle_usd_volume":"12267.00001","candle_trades_count":"41"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:22:00.000000000Z","price_open":"0.15557","price_close":"0.16003","price_high":"0.16003","price_low":"0.15557","vwap":"0.1578","volume":"77000.03","candle_usd_volume":"12268.00001","candle_trades_count":"42"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:23:00.000000000Z","price_open":"0.15558","price_close":"0.16004","price_high":"0.16004","price_low":"0.15558","vwap":"0.15781","volume":"77000.04","candle_usd_volume":"12269.00001","candle_trades_count":"43"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:24:00.000000000Z","price_open":"0.15559","price_close":"0.16005","price_high":"0.16005","price_low":"0.15559","vwap":"0.15782","volume":"77000.05","candle_usd_volume":"12270.00001","candle_trades_count":"44"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:25:00.000000000Z","price_open":"0.1556","price_close":"0.16006","price_high":"0.16006","price_low":"0.1556","vwap":"0.15783","volume":"77000.06","candle_usd_volume":"12271.00001","candle_trades_count":"45"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:26:00.000000000Z","price_open":"0.15561","price_close":"0.16007","price_high":"0.16007","price_low":"0.15561","vwap":"0.15784","volume":"77000.07","candle_usd_volume":"12272.00001","candle_trades_count":"46"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:27:00.000000000Z","price_open":"0.15562","price_close":"0.16008","price_high":"0.16008","price_low":"0.15562","vwap":"0.15785","volume":"77000.08","candle_usd_volume":"12273.00001","candle_trades_count":"47"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:28:00.000000000Z","price_open":"0.15563","price_close":"0.16009","price_high":"0.16009","price_low":"0.15563","vwap":"0.15786","volume":"77000.09","candle_usd_volume":"12274.00001","candle_trades_count":"48"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:29:00.000000000Z","price_open":"0.15564","price_close":"0.1601","price_high":"0.1601","price_low":"0.15564","vwap":"0.15787","volume":"77000.1","candle_usd_volume":"12275.00001","candle_trades_count":"48"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:30:00.000000000Z","price_open":"0.15565","price_close":"0.16011","price_high":"0.16011","price_low":"0.15565","vwap":"0.15788","volume":"77000.11","candle_usd_volume":"12276.00001","candle_trades_count":"49"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:31:00.000000000Z","price_open":"0.15566","price_close":"0.16012","price_high":"0.16012","price_low":"0.15566","vwap":"0.15789","volume":"77000.12","candle_usd_volume":"12277.00001","candle_trades_count":"50"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:32:00.000000000Z","price_open":"0.15567","price_close":"0.16013","price_high":"0.16013","price_low":"0.15567","vwap":"0.1579","volume":"77000.13","candle_usd_volume":"12278.00001","candle_trades_count":"51"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:33:00.000000000Z","price_open":"0.15568","price_close":"0.16014","price_high":"0.16014","price_low":"0.15568","vwap":"0.15791","volume":"77000.14","candle_usd_volume":"12279.00001","candle_trades_count":"52"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:34:00.000000000Z","price_open":"0.15569","price_close":"0.16015","price_high":"0.16015","price_low":"0.15569","vwap":"0.15792","volume":"77000.15","candle_usd_volume":"12280.00001","candle_trades_count":"53"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:35:00.000000000Z","price_open":"0.1557","price_close":"0.16016","price_high":"0.16016","price_low":"0.1557","vwap":"0.15793","volume":"77000.16","candle_usd_volume":"12281.00001","candle_trades_count":"54"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:36:00.000000000Z","price_open":"0.15571","price_close":"0.16017","price_high":"0.16017","price_low":"0.15571","vwap":"0.15794","volume":"77000.17","candle_usd_volume":"12282.00001","candle_trades_count":"55"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:37:00.000000000Z","price_open":"0.15572","price_close":"0.16018","price_high":"0.16018","price_low":"0.15572","vwap":"0.15795","volume":"77000.18","candle_usd_volume":"12283.00001","candle_trades_count":"56"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-TRXUSDT-future&frequency=1m&paging_from=start&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed future candles with frequency of 1m and paging from start and page size = 3`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:20:00.000000000Z","price_open":"0.15897","price_close":"0.15898","price_high":"0.15901","price_low":"0.15897","vwap":"0.15900065419949308734","volume":"77188.84","candle_usd_volume":"12267.1084377712053879038564","candle_trades_count":"41"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:21:00.000000000Z","price_open":"0.15556","price_close":"0.16002","price_high":"0.16002","price_low":"0.15556","vwap":"0.15779","volume":"77000.02","candle_usd_volume":"12267.00001","candle_trades_count":"41"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:22:00.000000000Z","price_open":"0.15557","price_close":"0.16003","price_high":"0.16003","price_low":"0.15557","vwap":"0.1578","volume":"77000.03","candle_usd_volume":"12268.00001","candle_trades_count":"42"}],"next_page_token":"0.MjAyNC0xMC0xOFQxMToyMjowMFo","next_page_url":"http://127.0.0.1:8080/v4/timeseries/market-candles?markets=bybit-TRXUSDT-future&frequency=1m&paging_from=start&page_size=3&api_key=x1&next_page_token=0.MjAyNC0xMC0xOFQxMToyMjowMFo"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-TRXUSDT-future&frequency=1m&paging_from=start&page_size=3&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed future candles with frequency of 1m and paging from end and page size = 3`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:35:00.000000000Z","price_open":"0.1557","price_close":"0.16016","price_high":"0.16016","price_low":"0.1557","vwap":"0.15793","volume":"77000.16","candle_usd_volume":"12281.00001","candle_trades_count":"54"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:36:00.000000000Z","price_open":"0.15571","price_close":"0.16017","price_high":"0.16017","price_low":"0.15571","vwap":"0.15794","volume":"77000.17","candle_usd_volume":"12282.00001","candle_trades_count":"55"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:37:00.000000000Z","price_open":"0.15572","price_close":"0.16018","price_high":"0.16018","price_low":"0.15572","vwap":"0.15795","volume":"77000.18","candle_usd_volume":"12283.00001","candle_trades_count":"56"}],"next_page_token":"0.MjAyNC0xMC0xOFQxMTozNTowMFo","next_page_url":"http://127.0.0.1:8080/v4/timeseries/market-candles?markets=bybit-TRXUSDT-future&frequency=1m&paging_from=end&page_size=3&api_key=x1&next_page_token=0.MjAyNC0xMC0xOFQxMTozNTowMFo"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-TRXUSDT-future&frequency=1m&paging_from=end&page_size=3&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed future candles with frequency of 1m and paging from start in a csv format`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:20:00.000000000Z","price_open":"0.15897","price_close":"0.15898","price_high":"0.15901","price_low":"0.15897","vwap":"0.15900065419949308734","volume":"77188.84","candle_usd_volume":"12267.1084377712053879038564","candle_trades_count":"41"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:21:00.000000000Z","price_open":"0.15556","price_close":"0.16002","price_high":"0.16002","price_low":"0.15556","vwap":"0.15779","volume":"77000.02","candle_usd_volume":"12267.00001","candle_trades_count":"41"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:22:00.000000000Z","price_open":"0.15557","price_close":"0.16003","price_high":"0.16003","price_low":"0.15557","vwap":"0.1578","volume":"77000.03","candle_usd_volume":"12268.00001","candle_trades_count":"42"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:23:00.000000000Z","price_open":"0.15558","price_close":"0.16004","price_high":"0.16004","price_low":"0.15558","vwap":"0.15781","volume":"77000.04","candle_usd_volume":"12269.00001","candle_trades_count":"43"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:24:00.000000000Z","price_open":"0.15559","price_close":"0.16005","price_high":"0.16005","price_low":"0.15559","vwap":"0.15782","volume":"77000.05","candle_usd_volume":"12270.00001","candle_trades_count":"44"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:25:00.000000000Z","price_open":"0.1556","price_close":"0.16006","price_high":"0.16006","price_low":"0.1556","vwap":"0.15783","volume":"77000.06","candle_usd_volume":"12271.00001","candle_trades_count":"45"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:26:00.000000000Z","price_open":"0.15561","price_close":"0.16007","price_high":"0.16007","price_low":"0.15561","vwap":"0.15784","volume":"77000.07","candle_usd_volume":"12272.00001","candle_trades_count":"46"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:27:00.000000000Z","price_open":"0.15562","price_close":"0.16008","price_high":"0.16008","price_low":"0.15562","vwap":"0.15785","volume":"77000.08","candle_usd_volume":"12273.00001","candle_trades_count":"47"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:28:00.000000000Z","price_open":"0.15563","price_close":"0.16009","price_high":"0.16009","price_low":"0.15563","vwap":"0.15786","volume":"77000.09","candle_usd_volume":"12274.00001","candle_trades_count":"48"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:29:00.000000000Z","price_open":"0.15564","price_close":"0.1601","price_high":"0.1601","price_low":"0.15564","vwap":"0.15787","volume":"77000.1","candle_usd_volume":"12275.00001","candle_trades_count":"48"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:30:00.000000000Z","price_open":"0.15565","price_close":"0.16011","price_high":"0.16011","price_low":"0.15565","vwap":"0.15788","volume":"77000.11","candle_usd_volume":"12276.00001","candle_trades_count":"49"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:31:00.000000000Z","price_open":"0.15566","price_close":"0.16012","price_high":"0.16012","price_low":"0.15566","vwap":"0.15789","volume":"77000.12","candle_usd_volume":"12277.00001","candle_trades_count":"50"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:32:00.000000000Z","price_open":"0.15567","price_close":"0.16013","price_high":"0.16013","price_low":"0.15567","vwap":"0.1579","volume":"77000.13","candle_usd_volume":"12278.00001","candle_trades_count":"51"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:33:00.000000000Z","price_open":"0.15568","price_close":"0.16014","price_high":"0.16014","price_low":"0.15568","vwap":"0.15791","volume":"77000.14","candle_usd_volume":"12279.00001","candle_trades_count":"52"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:34:00.000000000Z","price_open":"0.15569","price_close":"0.16015","price_high":"0.16015","price_low":"0.15569","vwap":"0.15792","volume":"77000.15","candle_usd_volume":"12280.00001","candle_trades_count":"53"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:35:00.000000000Z","price_open":"0.1557","price_close":"0.16016","price_high":"0.16016","price_low":"0.1557","vwap":"0.15793","volume":"77000.16","candle_usd_volume":"12281.00001","candle_trades_count":"54"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:36:00.000000000Z","price_open":"0.15571","price_close":"0.16017","price_high":"0.16017","price_low":"0.15571","vwap":"0.15794","volume":"77000.17","candle_usd_volume":"12282.00001","candle_trades_count":"55"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:37:00.000000000Z","price_open":"0.15572","price_close":"0.16018","price_high":"0.16018","price_low":"0.15572","vwap":"0.15795","volume":"77000.18","candle_usd_volume":"12283.00001","candle_trades_count":"56"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-TRXUSDT-future&frequency=1m&paging_from=start&api_key=$TEST_API_KEY",
        )

        val expectedResponseCsv =
            """
            market,time,price_open,price_close,price_high,price_low,vwap,volume,candle_usd_volume,candle_trades_count
            bybit-TRXUSDT-future,2024-10-18T11:20:00.000000000Z,0.15897,0.15898,0.15901,0.15897,0.15900065419949308734,77188.84,12267.1084377712053879038564,41
            bybit-TRXUSDT-future,2024-10-18T11:21:00.000000000Z,0.15556,0.16002,0.16002,0.15556,0.15779,77000.02,12267.00001,41
            bybit-TRXUSDT-future,2024-10-18T11:22:00.000000000Z,0.15557,0.16003,0.16003,0.15557,0.1578,77000.03,12268.00001,42
            bybit-TRXUSDT-future,2024-10-18T11:23:00.000000000Z,0.15558,0.16004,0.16004,0.15558,0.15781,77000.04,12269.00001,43
            bybit-TRXUSDT-future,2024-10-18T11:24:00.000000000Z,0.15559,0.16005,0.16005,0.15559,0.15782,77000.05,12270.00001,44
            bybit-TRXUSDT-future,2024-10-18T11:25:00.000000000Z,0.1556,0.16006,0.16006,0.1556,0.15783,77000.06,12271.00001,45
            bybit-TRXUSDT-future,2024-10-18T11:26:00.000000000Z,0.15561,0.16007,0.16007,0.15561,0.15784,77000.07,12272.00001,46
            bybit-TRXUSDT-future,2024-10-18T11:27:00.000000000Z,0.15562,0.16008,0.16008,0.15562,0.15785,77000.08,12273.00001,47
            bybit-TRXUSDT-future,2024-10-18T11:28:00.000000000Z,0.15563,0.16009,0.16009,0.15563,0.15786,77000.09,12274.00001,48
            bybit-TRXUSDT-future,2024-10-18T11:29:00.000000000Z,0.15564,0.1601,0.1601,0.15564,0.15787,77000.1,12275.00001,48
            bybit-TRXUSDT-future,2024-10-18T11:30:00.000000000Z,0.15565,0.16011,0.16011,0.15565,0.15788,77000.11,12276.00001,49
            bybit-TRXUSDT-future,2024-10-18T11:31:00.000000000Z,0.15566,0.16012,0.16012,0.15566,0.15789,77000.12,12277.00001,50
            bybit-TRXUSDT-future,2024-10-18T11:32:00.000000000Z,0.15567,0.16013,0.16013,0.15567,0.1579,77000.13,12278.00001,51
            bybit-TRXUSDT-future,2024-10-18T11:33:00.000000000Z,0.15568,0.16014,0.16014,0.15568,0.15791,77000.14,12279.00001,52
            bybit-TRXUSDT-future,2024-10-18T11:34:00.000000000Z,0.15569,0.16015,0.16015,0.15569,0.15792,77000.15,12280.00001,53
            bybit-TRXUSDT-future,2024-10-18T11:35:00.000000000Z,0.1557,0.16016,0.16016,0.1557,0.15793,77000.16,12281.00001,54
            bybit-TRXUSDT-future,2024-10-18T11:36:00.000000000Z,0.15571,0.16017,0.16017,0.15571,0.15794,77000.17,12282.00001,55
            bybit-TRXUSDT-future,2024-10-18T11:37:00.000000000Z,0.15572,0.16018,0.16018,0.15572,0.15795,77000.18,12283.00001,56

            """.trimIndent()
        assertResponseWithContentType(
            expectedCode = 200,
            expectedResponse = expectedResponseCsv,
            contentType = "text/csv",
            pathAndQuery = "/v4/timeseries/market-candles?markets=bybit-TRXUSDT-future&frequency=1m&paging_from=start&format=csv&api_key=$TEST_API_KEY",
        )

        assertEquals(jsonResponseToCsvResponse(expectedResponse), expectedResponseCsv)
    }

    @Test
    fun `instant and delayed future candles with frequency of 1m and paging from start and start time including delayed candles`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:20:00.000000000Z","price_open":"0.15897","price_close":"0.15898","price_high":"0.15901","price_low":"0.15897","vwap":"0.15900065419949308734","volume":"77188.84","candle_usd_volume":"12267.1084377712053879038564","candle_trades_count":"41"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:21:00.000000000Z","price_open":"0.15556","price_close":"0.16002","price_high":"0.16002","price_low":"0.15556","vwap":"0.15779","volume":"77000.02","candle_usd_volume":"12267.00001","candle_trades_count":"41"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:22:00.000000000Z","price_open":"0.15557","price_close":"0.16003","price_high":"0.16003","price_low":"0.15557","vwap":"0.1578","volume":"77000.03","candle_usd_volume":"12268.00001","candle_trades_count":"42"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:23:00.000000000Z","price_open":"0.15558","price_close":"0.16004","price_high":"0.16004","price_low":"0.15558","vwap":"0.15781","volume":"77000.04","candle_usd_volume":"12269.00001","candle_trades_count":"43"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:24:00.000000000Z","price_open":"0.15559","price_close":"0.16005","price_high":"0.16005","price_low":"0.15559","vwap":"0.15782","volume":"77000.05","candle_usd_volume":"12270.00001","candle_trades_count":"44"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:25:00.000000000Z","price_open":"0.1556","price_close":"0.16006","price_high":"0.16006","price_low":"0.1556","vwap":"0.15783","volume":"77000.06","candle_usd_volume":"12271.00001","candle_trades_count":"45"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:26:00.000000000Z","price_open":"0.15561","price_close":"0.16007","price_high":"0.16007","price_low":"0.15561","vwap":"0.15784","volume":"77000.07","candle_usd_volume":"12272.00001","candle_trades_count":"46"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:27:00.000000000Z","price_open":"0.15562","price_close":"0.16008","price_high":"0.16008","price_low":"0.15562","vwap":"0.15785","volume":"77000.08","candle_usd_volume":"12273.00001","candle_trades_count":"47"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:28:00.000000000Z","price_open":"0.15563","price_close":"0.16009","price_high":"0.16009","price_low":"0.15563","vwap":"0.15786","volume":"77000.09","candle_usd_volume":"12274.00001","candle_trades_count":"48"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:29:00.000000000Z","price_open":"0.15564","price_close":"0.1601","price_high":"0.1601","price_low":"0.15564","vwap":"0.15787","volume":"77000.1","candle_usd_volume":"12275.00001","candle_trades_count":"48"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:30:00.000000000Z","price_open":"0.15565","price_close":"0.16011","price_high":"0.16011","price_low":"0.15565","vwap":"0.15788","volume":"77000.11","candle_usd_volume":"12276.00001","candle_trades_count":"49"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:31:00.000000000Z","price_open":"0.15566","price_close":"0.16012","price_high":"0.16012","price_low":"0.15566","vwap":"0.15789","volume":"77000.12","candle_usd_volume":"12277.00001","candle_trades_count":"50"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:32:00.000000000Z","price_open":"0.15567","price_close":"0.16013","price_high":"0.16013","price_low":"0.15567","vwap":"0.1579","volume":"77000.13","candle_usd_volume":"12278.00001","candle_trades_count":"51"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:33:00.000000000Z","price_open":"0.15568","price_close":"0.16014","price_high":"0.16014","price_low":"0.15568","vwap":"0.15791","volume":"77000.14","candle_usd_volume":"12279.00001","candle_trades_count":"52"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:34:00.000000000Z","price_open":"0.15569","price_close":"0.16015","price_high":"0.16015","price_low":"0.15569","vwap":"0.15792","volume":"77000.15","candle_usd_volume":"12280.00001","candle_trades_count":"53"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:35:00.000000000Z","price_open":"0.1557","price_close":"0.16016","price_high":"0.16016","price_low":"0.1557","vwap":"0.15793","volume":"77000.16","candle_usd_volume":"12281.00001","candle_trades_count":"54"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:36:00.000000000Z","price_open":"0.15571","price_close":"0.16017","price_high":"0.16017","price_low":"0.15571","vwap":"0.15794","volume":"77000.17","candle_usd_volume":"12282.00001","candle_trades_count":"55"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:37:00.000000000Z","price_open":"0.15572","price_close":"0.16018","price_high":"0.16018","price_low":"0.15572","vwap":"0.15795","volume":"77000.18","candle_usd_volume":"12283.00001","candle_trades_count":"56"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-TRXUSDT-future&frequency=1m&paging_from=start&start_time=2024-10-18T11:20:00&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed future candles with frequency of 1m and paging from start and start time excluding delayed candles`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:30:00.000000000Z","price_open":"0.15565","price_close":"0.16011","price_high":"0.16011","price_low":"0.15565","vwap":"0.15788","volume":"77000.11","candle_usd_volume":"12276.00001","candle_trades_count":"49"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:31:00.000000000Z","price_open":"0.15566","price_close":"0.16012","price_high":"0.16012","price_low":"0.15566","vwap":"0.15789","volume":"77000.12","candle_usd_volume":"12277.00001","candle_trades_count":"50"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:32:00.000000000Z","price_open":"0.15567","price_close":"0.16013","price_high":"0.16013","price_low":"0.15567","vwap":"0.1579","volume":"77000.13","candle_usd_volume":"12278.00001","candle_trades_count":"51"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:33:00.000000000Z","price_open":"0.15568","price_close":"0.16014","price_high":"0.16014","price_low":"0.15568","vwap":"0.15791","volume":"77000.14","candle_usd_volume":"12279.00001","candle_trades_count":"52"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:34:00.000000000Z","price_open":"0.15569","price_close":"0.16015","price_high":"0.16015","price_low":"0.15569","vwap":"0.15792","volume":"77000.15","candle_usd_volume":"12280.00001","candle_trades_count":"53"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:35:00.000000000Z","price_open":"0.1557","price_close":"0.16016","price_high":"0.16016","price_low":"0.1557","vwap":"0.15793","volume":"77000.16","candle_usd_volume":"12281.00001","candle_trades_count":"54"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:36:00.000000000Z","price_open":"0.15571","price_close":"0.16017","price_high":"0.16017","price_low":"0.15571","vwap":"0.15794","volume":"77000.17","candle_usd_volume":"12282.00001","candle_trades_count":"55"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:37:00.000000000Z","price_open":"0.15572","price_close":"0.16018","price_high":"0.16018","price_low":"0.15572","vwap":"0.15795","volume":"77000.18","candle_usd_volume":"12283.00001","candle_trades_count":"56"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-TRXUSDT-future&frequency=1m&paging_from=start&start_time=2024-10-18T11:30:00&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed future candles with frequency of 1m and paging from start and start time and end time including delayed candles`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:20:00.000000000Z","price_open":"0.15897","price_close":"0.15898","price_high":"0.15901","price_low":"0.15897","vwap":"0.15900065419949308734","volume":"77188.84","candle_usd_volume":"12267.1084377712053879038564","candle_trades_count":"41"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:21:00.000000000Z","price_open":"0.15556","price_close":"0.16002","price_high":"0.16002","price_low":"0.15556","vwap":"0.15779","volume":"77000.02","candle_usd_volume":"12267.00001","candle_trades_count":"41"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:22:00.000000000Z","price_open":"0.15557","price_close":"0.16003","price_high":"0.16003","price_low":"0.15557","vwap":"0.1578","volume":"77000.03","candle_usd_volume":"12268.00001","candle_trades_count":"42"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-TRXUSDT-future&frequency=1m&paging_from=start&start_time=2024-10-18T11:20:00&end_time=2024-10-18T11:22:00&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed future candles with frequency of 1m and paging from start and start time and end time excluding delayed candles`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:30:00.000000000Z","price_open":"0.15565","price_close":"0.16011","price_high":"0.16011","price_low":"0.15565","vwap":"0.15788","volume":"77000.11","candle_usd_volume":"12276.00001","candle_trades_count":"49"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:31:00.000000000Z","price_open":"0.15566","price_close":"0.16012","price_high":"0.16012","price_low":"0.15566","vwap":"0.15789","volume":"77000.12","candle_usd_volume":"12277.00001","candle_trades_count":"50"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:32:00.000000000Z","price_open":"0.15567","price_close":"0.16013","price_high":"0.16013","price_low":"0.15567","vwap":"0.1579","volume":"77000.13","candle_usd_volume":"12278.00001","candle_trades_count":"51"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:33:00.000000000Z","price_open":"0.15568","price_close":"0.16014","price_high":"0.16014","price_low":"0.15568","vwap":"0.15791","volume":"77000.14","candle_usd_volume":"12279.00001","candle_trades_count":"52"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:34:00.000000000Z","price_open":"0.15569","price_close":"0.16015","price_high":"0.16015","price_low":"0.15569","vwap":"0.15792","volume":"77000.15","candle_usd_volume":"12280.00001","candle_trades_count":"53"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-TRXUSDT-future&frequency=1m&paging_from=start&start_time=2024-10-18T11:30:00&end_time=2024-10-18T11:34:00&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed future candles with frequency of 1m and paging from start and end time including delayed candles`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:20:00.000000000Z","price_open":"0.15897","price_close":"0.15898","price_high":"0.15901","price_low":"0.15897","vwap":"0.15900065419949308734","volume":"77188.84","candle_usd_volume":"12267.1084377712053879038564","candle_trades_count":"41"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:21:00.000000000Z","price_open":"0.15556","price_close":"0.16002","price_high":"0.16002","price_low":"0.15556","vwap":"0.15779","volume":"77000.02","candle_usd_volume":"12267.00001","candle_trades_count":"41"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:22:00.000000000Z","price_open":"0.15557","price_close":"0.16003","price_high":"0.16003","price_low":"0.15557","vwap":"0.1578","volume":"77000.03","candle_usd_volume":"12268.00001","candle_trades_count":"42"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:23:00.000000000Z","price_open":"0.15558","price_close":"0.16004","price_high":"0.16004","price_low":"0.15558","vwap":"0.15781","volume":"77000.04","candle_usd_volume":"12269.00001","candle_trades_count":"43"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:24:00.000000000Z","price_open":"0.15559","price_close":"0.16005","price_high":"0.16005","price_low":"0.15559","vwap":"0.15782","volume":"77000.05","candle_usd_volume":"12270.00001","candle_trades_count":"44"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-TRXUSDT-future&frequency=1m&paging_from=start&end_time=2024-10-18T11:24:00&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed future candles with frequency of 1m and paging from start and end time excluding instant candles`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:20:00.000000000Z","price_open":"0.15897","price_close":"0.15898","price_high":"0.15901","price_low":"0.15897","vwap":"0.15900065419949308734","volume":"77188.84","candle_usd_volume":"12267.1084377712053879038564","candle_trades_count":"41"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-TRXUSDT-future&frequency=1m&paging_from=start&end_time=2024-10-18T11:20:00&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed future candles with frequency of 5m and paging from start and 1 instant value is missed intentionally`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:20:00.000000000Z","price_open":"0.15897","price_close":"0.1589","price_high":"0.15901","price_low":"0.15888","vwap":"0.1589704600455617","volume":"150376.41","candle_usd_volume":"23895.68890582412088074642033413356746325003","candle_trades_count":"134"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:30:00.000000000Z","price_open":"0.16","price_close":"0.1601","price_high":"0.1601","price_low":"0.16","vwap":"0.16005","volume":"150000.01","candle_usd_volume":"23895.00001","candle_trades_count":"135"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:35:00.000000000Z","price_open":"0.16002","price_close":"0.16008","price_high":"0.16008","price_low":"0.16002","vwap":"0.16006","volume":"150001.01","candle_usd_volume":"23896.00001","candle_trades_count":"136"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-TRXUSDT-future&frequency=5m&paging_from=start&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed future candles with frequency of 5m and paging from start and 1 instant value is missed intentionally and page size = 2`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:20:00.000000000Z","price_open":"0.15897","price_close":"0.1589","price_high":"0.15901","price_low":"0.15888","vwap":"0.1589704600455617","volume":"150376.41","candle_usd_volume":"23895.68890582412088074642033413356746325003","candle_trades_count":"134"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:30:00.000000000Z","price_open":"0.16","price_close":"0.1601","price_high":"0.1601","price_low":"0.16","vwap":"0.16005","volume":"150000.01","candle_usd_volume":"23895.00001","candle_trades_count":"135"}],"next_page_token":"0.MjAyNC0xMC0xOFQxMTozMDowMFo","next_page_url":"http://127.0.0.1:8080/v4/timeseries/market-candles?markets=bybit-TRXUSDT-future&frequency=5m&paging_from=start&page_size=2&api_key=x1&next_page_token=0.MjAyNC0xMC0xOFQxMTozMDowMFo"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-TRXUSDT-future&frequency=5m&paging_from=start&page_size=2&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed future candles with frequency of 5m and paging from end and 1 instant value is missed intentionally and page size = 2`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:30:00.000000000Z","price_open":"0.16","price_close":"0.1601","price_high":"0.1601","price_low":"0.16","vwap":"0.16005","volume":"150000.01","candle_usd_volume":"23895.00001","candle_trades_count":"135"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:35:00.000000000Z","price_open":"0.16002","price_close":"0.16008","price_high":"0.16008","price_low":"0.16002","vwap":"0.16006","volume":"150001.01","candle_usd_volume":"23896.00001","candle_trades_count":"136"}],"next_page_token":"0.MjAyNC0xMC0xOFQxMTozMDowMFo","next_page_url":"http://127.0.0.1:8080/v4/timeseries/market-candles?markets=bybit-TRXUSDT-future&frequency=5m&paging_from=end&page_size=2&api_key=x1&next_page_token=0.MjAyNC0xMC0xOFQxMTozMDowMFo"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-TRXUSDT-future&frequency=5m&paging_from=end&page_size=2&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed future candles with frequency of 10m and paging from start`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:20:00.000000000Z","price_open":"0.15897","price_close":"0.15891","price_high":"0.15901","price_low":"0.15888","vwap":"0.158960288888022","volume":"186874.83","candle_usd_volume":"29694.05028649258992450344505400967153696509","candle_trades_count":"200"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:30:00.000000000Z","price_open":"0.15444","price_close":"0.16444","price_high":"0.16444","price_low":"0.15444","vwap":"0.15888","volume":"186874.01","candle_usd_volume":"29694.00001","candle_trades_count":"201"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-TRXUSDT-future&frequency=10m&paging_from=start&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed future candles with frequency of 10m and paging from start and page size = 1`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:20:00.000000000Z","price_open":"0.15897","price_close":"0.15891","price_high":"0.15901","price_low":"0.15888","vwap":"0.158960288888022","volume":"186874.83","candle_usd_volume":"29694.05028649258992450344505400967153696509","candle_trades_count":"200"}],"next_page_token":"0.MjAyNC0xMC0xOFQxMToyMDowMFo","next_page_url":"http://127.0.0.1:8080/v4/timeseries/market-candles?markets=bybit-TRXUSDT-future&frequency=10m&paging_from=start&page_size=1&api_key=x1&next_page_token=0.MjAyNC0xMC0xOFQxMToyMDowMFo"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-TRXUSDT-future&frequency=10m&paging_from=start&page_size=1&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed future candles with frequency of 10m and paging from end and page size = 1`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:30:00.000000000Z","price_open":"0.15444","price_close":"0.16444","price_high":"0.16444","price_low":"0.15444","vwap":"0.15888","volume":"186874.01","candle_usd_volume":"29694.00001","candle_trades_count":"201"}],"next_page_token":"0.MjAyNC0xMC0xOFQxMTozMDowMFo","next_page_url":"http://127.0.0.1:8080/v4/timeseries/market-candles?markets=bybit-TRXUSDT-future&frequency=10m&paging_from=end&page_size=1&api_key=x1&next_page_token=0.MjAyNC0xMC0xOFQxMTozMDowMFo"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-TRXUSDT-future&frequency=10m&paging_from=end&page_size=1&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed future candles with frequency of 15m and paging from start`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:15:00.000000000Z","price_open":"0.15903","price_close":"0.15891","price_high":"0.15903","price_low":"0.15888","vwap":"0.1589748654124442","volume":"400331.96","candle_usd_volume":"63618.77307723008592494781199918967153696509","candle_trades_count":"335"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:30:00.000000000Z","price_open":"0.15555","price_close":"0.15666","price_high":"0.15666","price_low":"0.15555","vwap":"0.156","volume":"400331.01","candle_usd_volume":"63618.00001","candle_trades_count":"336"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-TRXUSDT-future&frequency=15m&paging_from=start&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed future candles with frequency of 15m and paging from start and page size = 1`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:15:00.000000000Z","price_open":"0.15903","price_close":"0.15891","price_high":"0.15903","price_low":"0.15888","vwap":"0.1589748654124442","volume":"400331.96","candle_usd_volume":"63618.77307723008592494781199918967153696509","candle_trades_count":"335"}],"next_page_token":"0.MjAyNC0xMC0xOFQxMToxNTowMFo","next_page_url":"http://127.0.0.1:8080/v4/timeseries/market-candles?markets=bybit-TRXUSDT-future&frequency=15m&paging_from=start&page_size=1&api_key=x1&next_page_token=0.MjAyNC0xMC0xOFQxMToxNTowMFo"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-TRXUSDT-future&frequency=15m&paging_from=start&page_size=1&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed future candles with frequency of 15m and paging from end and page size = 1`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:30:00.000000000Z","price_open":"0.15555","price_close":"0.15666","price_high":"0.15666","price_low":"0.15555","vwap":"0.156","volume":"400331.01","candle_usd_volume":"63618.00001","candle_trades_count":"336"}],"next_page_token":"0.MjAyNC0xMC0xOFQxMTozMDowMFo","next_page_url":"http://127.0.0.1:8080/v4/timeseries/market-candles?markets=bybit-TRXUSDT-future&frequency=15m&paging_from=end&page_size=1&api_key=x1&next_page_token=0.MjAyNC0xMC0xOFQxMTozMDowMFo"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-TRXUSDT-future&frequency=15m&paging_from=end&page_size=1&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed future candles with frequency of 30m and paging from start`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:00:00.000000000Z","price_open":"0.15908","price_close":"0.15891","price_high":"0.15914","price_low":"0.15888","vwap":"0.1590188641784494","volume":"717288.38","candle_usd_volume":"114021.61717095928122785887028239535379449553","candle_trades_count":"670"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:30:00.000000000Z","price_open":"0.15901","price_close":"0.15991","price_high":"0.15991","price_low":"0.15901","vwap":"0.15951","volume":"717288.01","candle_usd_volume":"114021.00001","candle_trades_count":"671"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-TRXUSDT-future&frequency=30m&paging_from=start&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed future candles with frequency of 30m and paging from start and page size = 1`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:00:00.000000000Z","price_open":"0.15908","price_close":"0.15891","price_high":"0.15914","price_low":"0.15888","vwap":"0.1590188641784494","volume":"717288.38","candle_usd_volume":"114021.61717095928122785887028239535379449553","candle_trades_count":"670"}],"next_page_token":"0.MjAyNC0xMC0xOFQxMTowMDowMFo","next_page_url":"http://127.0.0.1:8080/v4/timeseries/market-candles?markets=bybit-TRXUSDT-future&frequency=30m&paging_from=start&page_size=1&api_key=x1&next_page_token=0.MjAyNC0xMC0xOFQxMTowMDowMFo"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-TRXUSDT-future&frequency=30m&paging_from=start&page_size=1&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed future candles with frequency of 30m and paging from end and page size = 1`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:30:00.000000000Z","price_open":"0.15901","price_close":"0.15991","price_high":"0.15991","price_low":"0.15901","vwap":"0.15951","volume":"717288.01","candle_usd_volume":"114021.00001","candle_trades_count":"671"}],"next_page_token":"0.MjAyNC0xMC0xOFQxMTozMDowMFo","next_page_url":"http://127.0.0.1:8080/v4/timeseries/market-candles?markets=bybit-TRXUSDT-future&frequency=30m&paging_from=end&page_size=1&api_key=x1&next_page_token=0.MjAyNC0xMC0xOFQxMTozMDowMFo"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-TRXUSDT-future&frequency=30m&paging_from=end&page_size=1&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed future candles with frequency of 1h and paging from start`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-TRXUSDT-future","time":"2024-10-17T13:00:00.000000000Z","price_open":"0.16608","price_close":"0.16691","price_high":"0.16614","price_low":"0.16688","vwap":"0.1660188641784494","volume":"766288.38","candle_usd_volume":"116026.61717095928122785887028239535379449553","candle_trades_count":"679"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:00:00.000000000Z","price_open":"0.15908","price_close":"0.1589","price_high":"0.15914","price_low":"0.15884","vwap":"0.1589882063083388","volume":"1385996.91","candle_usd_volume":"220279.73634820367613065647384011961914315841","candle_trades_count":"1258"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T12:00:00.000000000Z","price_open":"0.1595","price_close":"0.1695","price_high":"0.1695","price_low":"0.1595","vwap":"0.1655","volume":"1385996.01","candle_usd_volume":"220279.00001","candle_trades_count":"1259"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-TRXUSDT-future&frequency=1h&paging_from=start&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed future candles with frequency of 1h and paging from start and page size = 1`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-TRXUSDT-future","time":"2024-10-17T13:00:00.000000000Z","price_open":"0.16608","price_close":"0.16691","price_high":"0.16614","price_low":"0.16688","vwap":"0.1660188641784494","volume":"766288.38","candle_usd_volume":"116026.61717095928122785887028239535379449553","candle_trades_count":"679"}],"next_page_token":"0.MjAyNC0xMC0xN1QxMzowMDowMFo","next_page_url":"http://127.0.0.1:8080/v4/timeseries/market-candles?markets=bybit-TRXUSDT-future&frequency=1h&paging_from=start&page_size=1&api_key=x1&next_page_token=0.MjAyNC0xMC0xN1QxMzowMDowMFo"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-TRXUSDT-future&frequency=1h&paging_from=start&page_size=1&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed future candles with frequency of 1h and paging from end and page size = 1`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-TRXUSDT-future","time":"2024-10-18T12:00:00.000000000Z","price_open":"0.1595","price_close":"0.1695","price_high":"0.1695","price_low":"0.1595","vwap":"0.1655","volume":"1385996.01","candle_usd_volume":"220279.00001","candle_trades_count":"1259"}],"next_page_token":"0.MjAyNC0xMC0xOFQxMjowMDowMFo","next_page_url":"http://127.0.0.1:8080/v4/timeseries/market-candles?markets=bybit-TRXUSDT-future&frequency=1h&paging_from=end&page_size=1&api_key=x1&next_page_token=0.MjAyNC0xMC0xOFQxMjowMDowMFo"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-TRXUSDT-future&frequency=1h&paging_from=end&page_size=1&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed future candles with frequency of 4h and paging from start`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-TRXUSDT-future","time":"2024-10-18T08:00:00.000000000Z","price_open":"0.1591","price_close":"0.1589","price_high":"0.15921","price_low":"0.15884","vwap":"0.159053781252249","volume":"5612564.49","candle_usd_volume":"892352.18298533114606336454322945869720670277","candle_trades_count":"4706"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T12:00:00.000000000Z","price_open":"0.16222","price_close":"0.16333","price_high":"0.16333","price_low":"0.16222","vwap":"0.16666","volume":"5612564.01","candle_usd_volume":"892352.00001","candle_trades_count":"4707"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-TRXUSDT-future&frequency=4h&paging_from=start&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed future candles with frequency of 4h and paging from start and page size = 1`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-TRXUSDT-future","time":"2024-10-18T08:00:00.000000000Z","price_open":"0.1591","price_close":"0.1589","price_high":"0.15921","price_low":"0.15884","vwap":"0.159053781252249","volume":"5612564.49","candle_usd_volume":"892352.18298533114606336454322945869720670277","candle_trades_count":"4706"}],"next_page_token":"0.MjAyNC0xMC0xOFQwODowMDowMFo","next_page_url":"http://127.0.0.1:8080/v4/timeseries/market-candles?markets=bybit-TRXUSDT-future&frequency=4h&paging_from=start&page_size=1&api_key=x1&next_page_token=0.MjAyNC0xMC0xOFQwODowMDowMFo"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-TRXUSDT-future&frequency=4h&paging_from=start&page_size=1&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed future candles with frequency of 4h and paging from end and page size = 1`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-TRXUSDT-future","time":"2024-10-18T12:00:00.000000000Z","price_open":"0.16222","price_close":"0.16333","price_high":"0.16333","price_low":"0.16222","vwap":"0.16666","volume":"5612564.01","candle_usd_volume":"892352.00001","candle_trades_count":"4707"}],"next_page_token":"0.MjAyNC0xMC0xOFQxMjowMDowMFo","next_page_url":"http://127.0.0.1:8080/v4/timeseries/market-candles?markets=bybit-TRXUSDT-future&frequency=4h&paging_from=end&page_size=1&api_key=x1&next_page_token=0.MjAyNC0xMC0xOFQxMjowMDowMFo"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-TRXUSDT-future&frequency=4h&paging_from=end&page_size=1&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed future candles with frequency of 1d and paging from start`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-TRXUSDT-future","time":"2024-10-17T00:00:00.000000000Z","price_open":"0.1601","price_close":"0.15938","price_high":"0.16056","price_low":"0.1591","vwap":"0.159938246016863","volume":"38099958.37","candle_usd_volume":"6091654.72791880882081629919919093990618258893","candle_trades_count":"27656"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T00:00:00.000000000Z","price_open":"0.161","price_close":"0.162","price_high":"0.162","price_low":"0.161","vwap":"0.1615","volume":"38099958.01","candle_usd_volume":"6091654.00001","candle_trades_count":"27657"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-TRXUSDT-future&frequency=1d&paging_from=start&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed future candles with frequency of 1d and paging from start and page size = 1`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-TRXUSDT-future","time":"2024-10-17T00:00:00.000000000Z","price_open":"0.1601","price_close":"0.15938","price_high":"0.16056","price_low":"0.1591","vwap":"0.159938246016863","volume":"38099958.37","candle_usd_volume":"6091654.72791880882081629919919093990618258893","candle_trades_count":"27656"}],"next_page_token":"0.MjAyNC0xMC0xN1QwMDowMDowMFo","next_page_url":"http://127.0.0.1:8080/v4/timeseries/market-candles?markets=bybit-TRXUSDT-future&frequency=1d&paging_from=start&page_size=1&api_key=x1&next_page_token=0.MjAyNC0xMC0xN1QwMDowMDowMFo"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-TRXUSDT-future&frequency=1d&paging_from=start&page_size=1&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed future candles with frequency of 1d and paging from end and page size = 1`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-TRXUSDT-future","time":"2024-10-18T00:00:00.000000000Z","price_open":"0.161","price_close":"0.162","price_high":"0.162","price_low":"0.161","vwap":"0.1615","volume":"38099958.01","candle_usd_volume":"6091654.00001","candle_trades_count":"27657"}],"next_page_token":"0.MjAyNC0xMC0xOFQwMDowMDowMFo","next_page_url":"http://127.0.0.1:8080/v4/timeseries/market-candles?markets=bybit-TRXUSDT-future&frequency=1d&paging_from=end&page_size=1&api_key=x1&next_page_token=0.MjAyNC0xMC0xOFQwMDowMDowMFo"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-TRXUSDT-future&frequency=1d&paging_from=end&page_size=1&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed future candles with frequency of 1d-1300 and paging from start`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-TRXUSDT-future","time":"2024-10-17T13:00:00.000000000Z","price_open":"0.16608","price_close":"0.1695","price_high":"0.1695","price_low":"0.15884","vwap":"0.16306160530694495818","volume":"3538281.3","candle_usd_volume":"556585.35352916295735851534412251497293765394","candle_trades_count":"3196"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-TRXUSDT-future&frequency=1d-13:00&paging_from=start&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed future candles with frequency of 1d-1300 and paging from end`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-TRXUSDT-future","time":"2024-10-17T13:00:00.000000000Z","price_open":"0.16608","price_close":"0.1695","price_high":"0.1695","price_low":"0.15884","vwap":"0.16306160530694495818","volume":"3538281.3","candle_usd_volume":"556585.35352916295735851534412251497293765394","candle_trades_count":"3196"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-TRXUSDT-future&frequency=1d-13:00&paging_from=end&api_key=$TEST_API_KEY",
        )
    }

    // SPOT AND FUTURES

    @Test
    fun `instant and delayed spot and future candles with frequency of 1m and paging from start`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:20:00.000000000Z","price_open":"0.15897","price_close":"0.15898","price_high":"0.15901","price_low":"0.15897","vwap":"0.15900065419949308734","volume":"77188.84","candle_usd_volume":"12267.1084377712053879038564","candle_trades_count":"41"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:21:00.000000000Z","price_open":"0.15556","price_close":"0.16002","price_high":"0.16002","price_low":"0.15556","vwap":"0.15779","volume":"77000.02","candle_usd_volume":"12267.00001","candle_trades_count":"41"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:22:00.000000000Z","price_open":"0.15557","price_close":"0.16003","price_high":"0.16003","price_low":"0.15557","vwap":"0.1578","volume":"77000.03","candle_usd_volume":"12268.00001","candle_trades_count":"42"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:23:00.000000000Z","price_open":"0.15558","price_close":"0.16004","price_high":"0.16004","price_low":"0.15558","vwap":"0.15781","volume":"77000.04","candle_usd_volume":"12269.00001","candle_trades_count":"43"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:24:00.000000000Z","price_open":"0.15559","price_close":"0.16005","price_high":"0.16005","price_low":"0.15559","vwap":"0.15782","volume":"77000.05","candle_usd_volume":"12270.00001","candle_trades_count":"44"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:25:00.000000000Z","price_open":"0.1556","price_close":"0.16006","price_high":"0.16006","price_low":"0.1556","vwap":"0.15783","volume":"77000.06","candle_usd_volume":"12271.00001","candle_trades_count":"45"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:26:00.000000000Z","price_open":"0.15561","price_close":"0.16007","price_high":"0.16007","price_low":"0.15561","vwap":"0.15784","volume":"77000.07","candle_usd_volume":"12272.00001","candle_trades_count":"46"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:27:00.000000000Z","price_open":"0.15562","price_close":"0.16008","price_high":"0.16008","price_low":"0.15562","vwap":"0.15785","volume":"77000.08","candle_usd_volume":"12273.00001","candle_trades_count":"47"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:28:00.000000000Z","price_open":"0.15563","price_close":"0.16009","price_high":"0.16009","price_low":"0.15563","vwap":"0.15786","volume":"77000.09","candle_usd_volume":"12274.00001","candle_trades_count":"48"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:29:00.000000000Z","price_open":"0.15564","price_close":"0.1601","price_high":"0.1601","price_low":"0.15564","vwap":"0.15787","volume":"77000.1","candle_usd_volume":"12275.00001","candle_trades_count":"48"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:30:00.000000000Z","price_open":"0.15565","price_close":"0.16011","price_high":"0.16011","price_low":"0.15565","vwap":"0.15788","volume":"77000.11","candle_usd_volume":"12276.00001","candle_trades_count":"49"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:31:00.000000000Z","price_open":"0.15566","price_close":"0.16012","price_high":"0.16012","price_low":"0.15566","vwap":"0.15789","volume":"77000.12","candle_usd_volume":"12277.00001","candle_trades_count":"50"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:32:00.000000000Z","price_open":"0.15567","price_close":"0.16013","price_high":"0.16013","price_low":"0.15567","vwap":"0.1579","volume":"77000.13","candle_usd_volume":"12278.00001","candle_trades_count":"51"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:33:00.000000000Z","price_open":"0.15568","price_close":"0.16014","price_high":"0.16014","price_low":"0.15568","vwap":"0.15791","volume":"77000.14","candle_usd_volume":"12279.00001","candle_trades_count":"52"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:34:00.000000000Z","price_open":"0.15569","price_close":"0.16015","price_high":"0.16015","price_low":"0.15569","vwap":"0.15792","volume":"77000.15","candle_usd_volume":"12280.00001","candle_trades_count":"53"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:35:00.000000000Z","price_open":"0.1557","price_close":"0.16016","price_high":"0.16016","price_low":"0.1557","vwap":"0.15793","volume":"77000.16","candle_usd_volume":"12281.00001","candle_trades_count":"54"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:36:00.000000000Z","price_open":"0.15571","price_close":"0.16017","price_high":"0.16017","price_low":"0.15571","vwap":"0.15794","volume":"77000.17","candle_usd_volume":"12282.00001","candle_trades_count":"55"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:37:00.000000000Z","price_open":"0.15572","price_close":"0.16018","price_high":"0.16018","price_low":"0.15572","vwap":"0.15795","volume":"77000.18","candle_usd_volume":"12283.00001","candle_trades_count":"56"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:20:00.000000000Z","price_open":"0.15897","price_close":"0.15898","price_high":"0.15901","price_low":"0.15897","vwap":"0.15900065419949308734","volume":"77188.84","candle_usd_volume":"12267.1084377712053879038564","candle_trades_count":"41"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:21:00.000000000Z","price_open":"0.15556","price_close":"0.16002","price_high":"0.16002","price_low":"0.15556","vwap":"0.15779","volume":"77000.02","candle_usd_volume":"12267.00001","candle_trades_count":"41"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:22:00.000000000Z","price_open":"0.15557","price_close":"0.16003","price_high":"0.16003","price_low":"0.15557","vwap":"0.1578","volume":"77000.03","candle_usd_volume":"12268.00001","candle_trades_count":"42"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:23:00.000000000Z","price_open":"0.15558","price_close":"0.16004","price_high":"0.16004","price_low":"0.15558","vwap":"0.15781","volume":"77000.04","candle_usd_volume":"12269.00001","candle_trades_count":"43"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:24:00.000000000Z","price_open":"0.15559","price_close":"0.16005","price_high":"0.16005","price_low":"0.15559","vwap":"0.15782","volume":"77000.05","candle_usd_volume":"12270.00001","candle_trades_count":"44"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:25:00.000000000Z","price_open":"0.1556","price_close":"0.16006","price_high":"0.16006","price_low":"0.1556","vwap":"0.15783","volume":"77000.06","candle_usd_volume":"12271.00001","candle_trades_count":"45"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:26:00.000000000Z","price_open":"0.15561","price_close":"0.16007","price_high":"0.16007","price_low":"0.15561","vwap":"0.15784","volume":"77000.07","candle_usd_volume":"12272.00001","candle_trades_count":"46"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:27:00.000000000Z","price_open":"0.15562","price_close":"0.16008","price_high":"0.16008","price_low":"0.15562","vwap":"0.15785","volume":"77000.08","candle_usd_volume":"12273.00001","candle_trades_count":"47"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:28:00.000000000Z","price_open":"0.15563","price_close":"0.16009","price_high":"0.16009","price_low":"0.15563","vwap":"0.15786","volume":"77000.09","candle_usd_volume":"12274.00001","candle_trades_count":"48"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:29:00.000000000Z","price_open":"0.15564","price_close":"0.1601","price_high":"0.1601","price_low":"0.15564","vwap":"0.15787","volume":"77000.1","candle_usd_volume":"12275.00001","candle_trades_count":"48"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:30:00.000000000Z","price_open":"0.15565","price_close":"0.16011","price_high":"0.16011","price_low":"0.15565","vwap":"0.15788","volume":"77000.11","candle_usd_volume":"12276.00001","candle_trades_count":"49"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:31:00.000000000Z","price_open":"0.15566","price_close":"0.16012","price_high":"0.16012","price_low":"0.15566","vwap":"0.15789","volume":"77000.12","candle_usd_volume":"12277.00001","candle_trades_count":"50"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:32:00.000000000Z","price_open":"0.15567","price_close":"0.16013","price_high":"0.16013","price_low":"0.15567","vwap":"0.1579","volume":"77000.13","candle_usd_volume":"12278.00001","candle_trades_count":"51"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:33:00.000000000Z","price_open":"0.15568","price_close":"0.16014","price_high":"0.16014","price_low":"0.15568","vwap":"0.15791","volume":"77000.14","candle_usd_volume":"12279.00001","candle_trades_count":"52"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:34:00.000000000Z","price_open":"0.15569","price_close":"0.16015","price_high":"0.16015","price_low":"0.15569","vwap":"0.15792","volume":"77000.15","candle_usd_volume":"12280.00001","candle_trades_count":"53"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:35:00.000000000Z","price_open":"0.1557","price_close":"0.16016","price_high":"0.16016","price_low":"0.1557","vwap":"0.15793","volume":"77000.16","candle_usd_volume":"12281.00001","candle_trades_count":"54"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:36:00.000000000Z","price_open":"0.15571","price_close":"0.16017","price_high":"0.16017","price_low":"0.15571","vwap":"0.15794","volume":"77000.17","candle_usd_volume":"12282.00001","candle_trades_count":"55"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:37:00.000000000Z","price_open":"0.15572","price_close":"0.16018","price_high":"0.16018","price_low":"0.15572","vwap":"0.15795","volume":"77000.18","candle_usd_volume":"12283.00001","candle_trades_count":"56"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-*&frequency=1m&paging_from=start&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed spot and future candles with frequency of 1m and paging from start and page size = 25`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:20:00.000000000Z","price_open":"0.15897","price_close":"0.15898","price_high":"0.15901","price_low":"0.15897","vwap":"0.15900065419949308734","volume":"77188.84","candle_usd_volume":"12267.1084377712053879038564","candle_trades_count":"41"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:21:00.000000000Z","price_open":"0.15556","price_close":"0.16002","price_high":"0.16002","price_low":"0.15556","vwap":"0.15779","volume":"77000.02","candle_usd_volume":"12267.00001","candle_trades_count":"41"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:22:00.000000000Z","price_open":"0.15557","price_close":"0.16003","price_high":"0.16003","price_low":"0.15557","vwap":"0.1578","volume":"77000.03","candle_usd_volume":"12268.00001","candle_trades_count":"42"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:23:00.000000000Z","price_open":"0.15558","price_close":"0.16004","price_high":"0.16004","price_low":"0.15558","vwap":"0.15781","volume":"77000.04","candle_usd_volume":"12269.00001","candle_trades_count":"43"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:24:00.000000000Z","price_open":"0.15559","price_close":"0.16005","price_high":"0.16005","price_low":"0.15559","vwap":"0.15782","volume":"77000.05","candle_usd_volume":"12270.00001","candle_trades_count":"44"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:25:00.000000000Z","price_open":"0.1556","price_close":"0.16006","price_high":"0.16006","price_low":"0.1556","vwap":"0.15783","volume":"77000.06","candle_usd_volume":"12271.00001","candle_trades_count":"45"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:26:00.000000000Z","price_open":"0.15561","price_close":"0.16007","price_high":"0.16007","price_low":"0.15561","vwap":"0.15784","volume":"77000.07","candle_usd_volume":"12272.00001","candle_trades_count":"46"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:27:00.000000000Z","price_open":"0.15562","price_close":"0.16008","price_high":"0.16008","price_low":"0.15562","vwap":"0.15785","volume":"77000.08","candle_usd_volume":"12273.00001","candle_trades_count":"47"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:28:00.000000000Z","price_open":"0.15563","price_close":"0.16009","price_high":"0.16009","price_low":"0.15563","vwap":"0.15786","volume":"77000.09","candle_usd_volume":"12274.00001","candle_trades_count":"48"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:29:00.000000000Z","price_open":"0.15564","price_close":"0.1601","price_high":"0.1601","price_low":"0.15564","vwap":"0.15787","volume":"77000.1","candle_usd_volume":"12275.00001","candle_trades_count":"48"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:30:00.000000000Z","price_open":"0.15565","price_close":"0.16011","price_high":"0.16011","price_low":"0.15565","vwap":"0.15788","volume":"77000.11","candle_usd_volume":"12276.00001","candle_trades_count":"49"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:31:00.000000000Z","price_open":"0.15566","price_close":"0.16012","price_high":"0.16012","price_low":"0.15566","vwap":"0.15789","volume":"77000.12","candle_usd_volume":"12277.00001","candle_trades_count":"50"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:32:00.000000000Z","price_open":"0.15567","price_close":"0.16013","price_high":"0.16013","price_low":"0.15567","vwap":"0.1579","volume":"77000.13","candle_usd_volume":"12278.00001","candle_trades_count":"51"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:33:00.000000000Z","price_open":"0.15568","price_close":"0.16014","price_high":"0.16014","price_low":"0.15568","vwap":"0.15791","volume":"77000.14","candle_usd_volume":"12279.00001","candle_trades_count":"52"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:34:00.000000000Z","price_open":"0.15569","price_close":"0.16015","price_high":"0.16015","price_low":"0.15569","vwap":"0.15792","volume":"77000.15","candle_usd_volume":"12280.00001","candle_trades_count":"53"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:35:00.000000000Z","price_open":"0.1557","price_close":"0.16016","price_high":"0.16016","price_low":"0.1557","vwap":"0.15793","volume":"77000.16","candle_usd_volume":"12281.00001","candle_trades_count":"54"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:36:00.000000000Z","price_open":"0.15571","price_close":"0.16017","price_high":"0.16017","price_low":"0.15571","vwap":"0.15794","volume":"77000.17","candle_usd_volume":"12282.00001","candle_trades_count":"55"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:37:00.000000000Z","price_open":"0.15572","price_close":"0.16018","price_high":"0.16018","price_low":"0.15572","vwap":"0.15795","volume":"77000.18","candle_usd_volume":"12283.00001","candle_trades_count":"56"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:20:00.000000000Z","price_open":"0.15897","price_close":"0.15898","price_high":"0.15901","price_low":"0.15897","vwap":"0.15900065419949308734","volume":"77188.84","candle_usd_volume":"12267.1084377712053879038564","candle_trades_count":"41"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:21:00.000000000Z","price_open":"0.15556","price_close":"0.16002","price_high":"0.16002","price_low":"0.15556","vwap":"0.15779","volume":"77000.02","candle_usd_volume":"12267.00001","candle_trades_count":"41"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:22:00.000000000Z","price_open":"0.15557","price_close":"0.16003","price_high":"0.16003","price_low":"0.15557","vwap":"0.1578","volume":"77000.03","candle_usd_volume":"12268.00001","candle_trades_count":"42"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:23:00.000000000Z","price_open":"0.15558","price_close":"0.16004","price_high":"0.16004","price_low":"0.15558","vwap":"0.15781","volume":"77000.04","candle_usd_volume":"12269.00001","candle_trades_count":"43"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:24:00.000000000Z","price_open":"0.15559","price_close":"0.16005","price_high":"0.16005","price_low":"0.15559","vwap":"0.15782","volume":"77000.05","candle_usd_volume":"12270.00001","candle_trades_count":"44"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:25:00.000000000Z","price_open":"0.1556","price_close":"0.16006","price_high":"0.16006","price_low":"0.1556","vwap":"0.15783","volume":"77000.06","candle_usd_volume":"12271.00001","candle_trades_count":"45"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:26:00.000000000Z","price_open":"0.15561","price_close":"0.16007","price_high":"0.16007","price_low":"0.15561","vwap":"0.15784","volume":"77000.07","candle_usd_volume":"12272.00001","candle_trades_count":"46"}],"next_page_token":"YnliaXQtdHJ4LXVzZHQtc3BvdA.MjAyNC0xMC0xOFQxMToyNjowMFo","next_page_url":"http://127.0.0.1:8080/v4/timeseries/market-candles?markets=bybit-*&frequency=1m&paging_from=start&page_size=25&api_key=x1&next_page_token=YnliaXQtdHJ4LXVzZHQtc3BvdA.MjAyNC0xMC0xOFQxMToyNjowMFo"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-*&frequency=1m&paging_from=start&page_size=25&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed spot and future candles with frequency of 1m and paging from end and page size = 25`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:31:00.000000000Z","price_open":"0.15566","price_close":"0.16012","price_high":"0.16012","price_low":"0.15566","vwap":"0.15789","volume":"77000.12","candle_usd_volume":"12277.00001","candle_trades_count":"50"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:32:00.000000000Z","price_open":"0.15567","price_close":"0.16013","price_high":"0.16013","price_low":"0.15567","vwap":"0.1579","volume":"77000.13","candle_usd_volume":"12278.00001","candle_trades_count":"51"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:33:00.000000000Z","price_open":"0.15568","price_close":"0.16014","price_high":"0.16014","price_low":"0.15568","vwap":"0.15791","volume":"77000.14","candle_usd_volume":"12279.00001","candle_trades_count":"52"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:34:00.000000000Z","price_open":"0.15569","price_close":"0.16015","price_high":"0.16015","price_low":"0.15569","vwap":"0.15792","volume":"77000.15","candle_usd_volume":"12280.00001","candle_trades_count":"53"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:35:00.000000000Z","price_open":"0.1557","price_close":"0.16016","price_high":"0.16016","price_low":"0.1557","vwap":"0.15793","volume":"77000.16","candle_usd_volume":"12281.00001","candle_trades_count":"54"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:36:00.000000000Z","price_open":"0.15571","price_close":"0.16017","price_high":"0.16017","price_low":"0.15571","vwap":"0.15794","volume":"77000.17","candle_usd_volume":"12282.00001","candle_trades_count":"55"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:37:00.000000000Z","price_open":"0.15572","price_close":"0.16018","price_high":"0.16018","price_low":"0.15572","vwap":"0.15795","volume":"77000.18","candle_usd_volume":"12283.00001","candle_trades_count":"56"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:20:00.000000000Z","price_open":"0.15897","price_close":"0.15898","price_high":"0.15901","price_low":"0.15897","vwap":"0.15900065419949308734","volume":"77188.84","candle_usd_volume":"12267.1084377712053879038564","candle_trades_count":"41"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:21:00.000000000Z","price_open":"0.15556","price_close":"0.16002","price_high":"0.16002","price_low":"0.15556","vwap":"0.15779","volume":"77000.02","candle_usd_volume":"12267.00001","candle_trades_count":"41"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:22:00.000000000Z","price_open":"0.15557","price_close":"0.16003","price_high":"0.16003","price_low":"0.15557","vwap":"0.1578","volume":"77000.03","candle_usd_volume":"12268.00001","candle_trades_count":"42"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:23:00.000000000Z","price_open":"0.15558","price_close":"0.16004","price_high":"0.16004","price_low":"0.15558","vwap":"0.15781","volume":"77000.04","candle_usd_volume":"12269.00001","candle_trades_count":"43"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:24:00.000000000Z","price_open":"0.15559","price_close":"0.16005","price_high":"0.16005","price_low":"0.15559","vwap":"0.15782","volume":"77000.05","candle_usd_volume":"12270.00001","candle_trades_count":"44"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:25:00.000000000Z","price_open":"0.1556","price_close":"0.16006","price_high":"0.16006","price_low":"0.1556","vwap":"0.15783","volume":"77000.06","candle_usd_volume":"12271.00001","candle_trades_count":"45"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:26:00.000000000Z","price_open":"0.15561","price_close":"0.16007","price_high":"0.16007","price_low":"0.15561","vwap":"0.15784","volume":"77000.07","candle_usd_volume":"12272.00001","candle_trades_count":"46"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:27:00.000000000Z","price_open":"0.15562","price_close":"0.16008","price_high":"0.16008","price_low":"0.15562","vwap":"0.15785","volume":"77000.08","candle_usd_volume":"12273.00001","candle_trades_count":"47"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:28:00.000000000Z","price_open":"0.15563","price_close":"0.16009","price_high":"0.16009","price_low":"0.15563","vwap":"0.15786","volume":"77000.09","candle_usd_volume":"12274.00001","candle_trades_count":"48"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:29:00.000000000Z","price_open":"0.15564","price_close":"0.1601","price_high":"0.1601","price_low":"0.15564","vwap":"0.15787","volume":"77000.1","candle_usd_volume":"12275.00001","candle_trades_count":"48"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:30:00.000000000Z","price_open":"0.15565","price_close":"0.16011","price_high":"0.16011","price_low":"0.15565","vwap":"0.15788","volume":"77000.11","candle_usd_volume":"12276.00001","candle_trades_count":"49"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:31:00.000000000Z","price_open":"0.15566","price_close":"0.16012","price_high":"0.16012","price_low":"0.15566","vwap":"0.15789","volume":"77000.12","candle_usd_volume":"12277.00001","candle_trades_count":"50"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:32:00.000000000Z","price_open":"0.15567","price_close":"0.16013","price_high":"0.16013","price_low":"0.15567","vwap":"0.1579","volume":"77000.13","candle_usd_volume":"12278.00001","candle_trades_count":"51"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:33:00.000000000Z","price_open":"0.15568","price_close":"0.16014","price_high":"0.16014","price_low":"0.15568","vwap":"0.15791","volume":"77000.14","candle_usd_volume":"12279.00001","candle_trades_count":"52"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:34:00.000000000Z","price_open":"0.15569","price_close":"0.16015","price_high":"0.16015","price_low":"0.15569","vwap":"0.15792","volume":"77000.15","candle_usd_volume":"12280.00001","candle_trades_count":"53"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:35:00.000000000Z","price_open":"0.1557","price_close":"0.16016","price_high":"0.16016","price_low":"0.1557","vwap":"0.15793","volume":"77000.16","candle_usd_volume":"12281.00001","candle_trades_count":"54"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:36:00.000000000Z","price_open":"0.15571","price_close":"0.16017","price_high":"0.16017","price_low":"0.15571","vwap":"0.15794","volume":"77000.17","candle_usd_volume":"12282.00001","candle_trades_count":"55"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:37:00.000000000Z","price_open":"0.15572","price_close":"0.16018","price_high":"0.16018","price_low":"0.15572","vwap":"0.15795","volume":"77000.18","candle_usd_volume":"12283.00001","candle_trades_count":"56"}],"next_page_token":"YnliaXQtVFJYVVNEVC1mdXR1cmU.MjAyNC0xMC0xOFQxMTozMTowMFo","next_page_url":"http://127.0.0.1:8080/v4/timeseries/market-candles?markets=bybit-*&frequency=1m&paging_from=end&page_size=25&api_key=x1&next_page_token=YnliaXQtVFJYVVNEVC1mdXR1cmU.MjAyNC0xMC0xOFQxMTozMTowMFo"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-*&frequency=1m&paging_from=end&page_size=25&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed spot and future candles with frequency of 1m and paging from start in a csv format`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:20:00.000000000Z","price_open":"0.15897","price_close":"0.15898","price_high":"0.15901","price_low":"0.15897","vwap":"0.15900065419949308734","volume":"77188.84","candle_usd_volume":"12267.1084377712053879038564","candle_trades_count":"41"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:21:00.000000000Z","price_open":"0.15556","price_close":"0.16002","price_high":"0.16002","price_low":"0.15556","vwap":"0.15779","volume":"77000.02","candle_usd_volume":"12267.00001","candle_trades_count":"41"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:22:00.000000000Z","price_open":"0.15557","price_close":"0.16003","price_high":"0.16003","price_low":"0.15557","vwap":"0.1578","volume":"77000.03","candle_usd_volume":"12268.00001","candle_trades_count":"42"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:23:00.000000000Z","price_open":"0.15558","price_close":"0.16004","price_high":"0.16004","price_low":"0.15558","vwap":"0.15781","volume":"77000.04","candle_usd_volume":"12269.00001","candle_trades_count":"43"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:24:00.000000000Z","price_open":"0.15559","price_close":"0.16005","price_high":"0.16005","price_low":"0.15559","vwap":"0.15782","volume":"77000.05","candle_usd_volume":"12270.00001","candle_trades_count":"44"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:25:00.000000000Z","price_open":"0.1556","price_close":"0.16006","price_high":"0.16006","price_low":"0.1556","vwap":"0.15783","volume":"77000.06","candle_usd_volume":"12271.00001","candle_trades_count":"45"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:26:00.000000000Z","price_open":"0.15561","price_close":"0.16007","price_high":"0.16007","price_low":"0.15561","vwap":"0.15784","volume":"77000.07","candle_usd_volume":"12272.00001","candle_trades_count":"46"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:27:00.000000000Z","price_open":"0.15562","price_close":"0.16008","price_high":"0.16008","price_low":"0.15562","vwap":"0.15785","volume":"77000.08","candle_usd_volume":"12273.00001","candle_trades_count":"47"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:28:00.000000000Z","price_open":"0.15563","price_close":"0.16009","price_high":"0.16009","price_low":"0.15563","vwap":"0.15786","volume":"77000.09","candle_usd_volume":"12274.00001","candle_trades_count":"48"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:29:00.000000000Z","price_open":"0.15564","price_close":"0.1601","price_high":"0.1601","price_low":"0.15564","vwap":"0.15787","volume":"77000.1","candle_usd_volume":"12275.00001","candle_trades_count":"48"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:30:00.000000000Z","price_open":"0.15565","price_close":"0.16011","price_high":"0.16011","price_low":"0.15565","vwap":"0.15788","volume":"77000.11","candle_usd_volume":"12276.00001","candle_trades_count":"49"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:31:00.000000000Z","price_open":"0.15566","price_close":"0.16012","price_high":"0.16012","price_low":"0.15566","vwap":"0.15789","volume":"77000.12","candle_usd_volume":"12277.00001","candle_trades_count":"50"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:32:00.000000000Z","price_open":"0.15567","price_close":"0.16013","price_high":"0.16013","price_low":"0.15567","vwap":"0.1579","volume":"77000.13","candle_usd_volume":"12278.00001","candle_trades_count":"51"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:33:00.000000000Z","price_open":"0.15568","price_close":"0.16014","price_high":"0.16014","price_low":"0.15568","vwap":"0.15791","volume":"77000.14","candle_usd_volume":"12279.00001","candle_trades_count":"52"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:34:00.000000000Z","price_open":"0.15569","price_close":"0.16015","price_high":"0.16015","price_low":"0.15569","vwap":"0.15792","volume":"77000.15","candle_usd_volume":"12280.00001","candle_trades_count":"53"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:35:00.000000000Z","price_open":"0.1557","price_close":"0.16016","price_high":"0.16016","price_low":"0.1557","vwap":"0.15793","volume":"77000.16","candle_usd_volume":"12281.00001","candle_trades_count":"54"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:36:00.000000000Z","price_open":"0.15571","price_close":"0.16017","price_high":"0.16017","price_low":"0.15571","vwap":"0.15794","volume":"77000.17","candle_usd_volume":"12282.00001","candle_trades_count":"55"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:37:00.000000000Z","price_open":"0.15572","price_close":"0.16018","price_high":"0.16018","price_low":"0.15572","vwap":"0.15795","volume":"77000.18","candle_usd_volume":"12283.00001","candle_trades_count":"56"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:20:00.000000000Z","price_open":"0.15897","price_close":"0.15898","price_high":"0.15901","price_low":"0.15897","vwap":"0.15900065419949308734","volume":"77188.84","candle_usd_volume":"12267.1084377712053879038564","candle_trades_count":"41"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:21:00.000000000Z","price_open":"0.15556","price_close":"0.16002","price_high":"0.16002","price_low":"0.15556","vwap":"0.15779","volume":"77000.02","candle_usd_volume":"12267.00001","candle_trades_count":"41"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:22:00.000000000Z","price_open":"0.15557","price_close":"0.16003","price_high":"0.16003","price_low":"0.15557","vwap":"0.1578","volume":"77000.03","candle_usd_volume":"12268.00001","candle_trades_count":"42"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:23:00.000000000Z","price_open":"0.15558","price_close":"0.16004","price_high":"0.16004","price_low":"0.15558","vwap":"0.15781","volume":"77000.04","candle_usd_volume":"12269.00001","candle_trades_count":"43"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:24:00.000000000Z","price_open":"0.15559","price_close":"0.16005","price_high":"0.16005","price_low":"0.15559","vwap":"0.15782","volume":"77000.05","candle_usd_volume":"12270.00001","candle_trades_count":"44"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:25:00.000000000Z","price_open":"0.1556","price_close":"0.16006","price_high":"0.16006","price_low":"0.1556","vwap":"0.15783","volume":"77000.06","candle_usd_volume":"12271.00001","candle_trades_count":"45"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:26:00.000000000Z","price_open":"0.15561","price_close":"0.16007","price_high":"0.16007","price_low":"0.15561","vwap":"0.15784","volume":"77000.07","candle_usd_volume":"12272.00001","candle_trades_count":"46"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:27:00.000000000Z","price_open":"0.15562","price_close":"0.16008","price_high":"0.16008","price_low":"0.15562","vwap":"0.15785","volume":"77000.08","candle_usd_volume":"12273.00001","candle_trades_count":"47"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:28:00.000000000Z","price_open":"0.15563","price_close":"0.16009","price_high":"0.16009","price_low":"0.15563","vwap":"0.15786","volume":"77000.09","candle_usd_volume":"12274.00001","candle_trades_count":"48"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:29:00.000000000Z","price_open":"0.15564","price_close":"0.1601","price_high":"0.1601","price_low":"0.15564","vwap":"0.15787","volume":"77000.1","candle_usd_volume":"12275.00001","candle_trades_count":"48"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:30:00.000000000Z","price_open":"0.15565","price_close":"0.16011","price_high":"0.16011","price_low":"0.15565","vwap":"0.15788","volume":"77000.11","candle_usd_volume":"12276.00001","candle_trades_count":"49"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:31:00.000000000Z","price_open":"0.15566","price_close":"0.16012","price_high":"0.16012","price_low":"0.15566","vwap":"0.15789","volume":"77000.12","candle_usd_volume":"12277.00001","candle_trades_count":"50"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:32:00.000000000Z","price_open":"0.15567","price_close":"0.16013","price_high":"0.16013","price_low":"0.15567","vwap":"0.1579","volume":"77000.13","candle_usd_volume":"12278.00001","candle_trades_count":"51"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:33:00.000000000Z","price_open":"0.15568","price_close":"0.16014","price_high":"0.16014","price_low":"0.15568","vwap":"0.15791","volume":"77000.14","candle_usd_volume":"12279.00001","candle_trades_count":"52"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:34:00.000000000Z","price_open":"0.15569","price_close":"0.16015","price_high":"0.16015","price_low":"0.15569","vwap":"0.15792","volume":"77000.15","candle_usd_volume":"12280.00001","candle_trades_count":"53"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:35:00.000000000Z","price_open":"0.1557","price_close":"0.16016","price_high":"0.16016","price_low":"0.1557","vwap":"0.15793","volume":"77000.16","candle_usd_volume":"12281.00001","candle_trades_count":"54"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:36:00.000000000Z","price_open":"0.15571","price_close":"0.16017","price_high":"0.16017","price_low":"0.15571","vwap":"0.15794","volume":"77000.17","candle_usd_volume":"12282.00001","candle_trades_count":"55"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:37:00.000000000Z","price_open":"0.15572","price_close":"0.16018","price_high":"0.16018","price_low":"0.15572","vwap":"0.15795","volume":"77000.18","candle_usd_volume":"12283.00001","candle_trades_count":"56"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-*&frequency=1m&paging_from=start&api_key=$TEST_API_KEY",
        )

        val expectedResponseCsv =
            """
            market,time,price_open,price_close,price_high,price_low,vwap,volume,candle_usd_volume,candle_trades_count
            bybit-TRXUSDT-future,2024-10-18T11:20:00.000000000Z,0.15897,0.15898,0.15901,0.15897,0.15900065419949308734,77188.84,12267.1084377712053879038564,41
            bybit-TRXUSDT-future,2024-10-18T11:21:00.000000000Z,0.15556,0.16002,0.16002,0.15556,0.15779,77000.02,12267.00001,41
            bybit-TRXUSDT-future,2024-10-18T11:22:00.000000000Z,0.15557,0.16003,0.16003,0.15557,0.1578,77000.03,12268.00001,42
            bybit-TRXUSDT-future,2024-10-18T11:23:00.000000000Z,0.15558,0.16004,0.16004,0.15558,0.15781,77000.04,12269.00001,43
            bybit-TRXUSDT-future,2024-10-18T11:24:00.000000000Z,0.15559,0.16005,0.16005,0.15559,0.15782,77000.05,12270.00001,44
            bybit-TRXUSDT-future,2024-10-18T11:25:00.000000000Z,0.1556,0.16006,0.16006,0.1556,0.15783,77000.06,12271.00001,45
            bybit-TRXUSDT-future,2024-10-18T11:26:00.000000000Z,0.15561,0.16007,0.16007,0.15561,0.15784,77000.07,12272.00001,46
            bybit-TRXUSDT-future,2024-10-18T11:27:00.000000000Z,0.15562,0.16008,0.16008,0.15562,0.15785,77000.08,12273.00001,47
            bybit-TRXUSDT-future,2024-10-18T11:28:00.000000000Z,0.15563,0.16009,0.16009,0.15563,0.15786,77000.09,12274.00001,48
            bybit-TRXUSDT-future,2024-10-18T11:29:00.000000000Z,0.15564,0.1601,0.1601,0.15564,0.15787,77000.1,12275.00001,48
            bybit-TRXUSDT-future,2024-10-18T11:30:00.000000000Z,0.15565,0.16011,0.16011,0.15565,0.15788,77000.11,12276.00001,49
            bybit-TRXUSDT-future,2024-10-18T11:31:00.000000000Z,0.15566,0.16012,0.16012,0.15566,0.15789,77000.12,12277.00001,50
            bybit-TRXUSDT-future,2024-10-18T11:32:00.000000000Z,0.15567,0.16013,0.16013,0.15567,0.1579,77000.13,12278.00001,51
            bybit-TRXUSDT-future,2024-10-18T11:33:00.000000000Z,0.15568,0.16014,0.16014,0.15568,0.15791,77000.14,12279.00001,52
            bybit-TRXUSDT-future,2024-10-18T11:34:00.000000000Z,0.15569,0.16015,0.16015,0.15569,0.15792,77000.15,12280.00001,53
            bybit-TRXUSDT-future,2024-10-18T11:35:00.000000000Z,0.1557,0.16016,0.16016,0.1557,0.15793,77000.16,12281.00001,54
            bybit-TRXUSDT-future,2024-10-18T11:36:00.000000000Z,0.15571,0.16017,0.16017,0.15571,0.15794,77000.17,12282.00001,55
            bybit-TRXUSDT-future,2024-10-18T11:37:00.000000000Z,0.15572,0.16018,0.16018,0.15572,0.15795,77000.18,12283.00001,56
            bybit-trx-usdt-spot,2024-10-18T11:20:00.000000000Z,0.15897,0.15898,0.15901,0.15897,0.15900065419949308734,77188.84,12267.1084377712053879038564,41
            bybit-trx-usdt-spot,2024-10-18T11:21:00.000000000Z,0.15556,0.16002,0.16002,0.15556,0.15779,77000.02,12267.00001,41
            bybit-trx-usdt-spot,2024-10-18T11:22:00.000000000Z,0.15557,0.16003,0.16003,0.15557,0.1578,77000.03,12268.00001,42
            bybit-trx-usdt-spot,2024-10-18T11:23:00.000000000Z,0.15558,0.16004,0.16004,0.15558,0.15781,77000.04,12269.00001,43
            bybit-trx-usdt-spot,2024-10-18T11:24:00.000000000Z,0.15559,0.16005,0.16005,0.15559,0.15782,77000.05,12270.00001,44
            bybit-trx-usdt-spot,2024-10-18T11:25:00.000000000Z,0.1556,0.16006,0.16006,0.1556,0.15783,77000.06,12271.00001,45
            bybit-trx-usdt-spot,2024-10-18T11:26:00.000000000Z,0.15561,0.16007,0.16007,0.15561,0.15784,77000.07,12272.00001,46
            bybit-trx-usdt-spot,2024-10-18T11:27:00.000000000Z,0.15562,0.16008,0.16008,0.15562,0.15785,77000.08,12273.00001,47
            bybit-trx-usdt-spot,2024-10-18T11:28:00.000000000Z,0.15563,0.16009,0.16009,0.15563,0.15786,77000.09,12274.00001,48
            bybit-trx-usdt-spot,2024-10-18T11:29:00.000000000Z,0.15564,0.1601,0.1601,0.15564,0.15787,77000.1,12275.00001,48
            bybit-trx-usdt-spot,2024-10-18T11:30:00.000000000Z,0.15565,0.16011,0.16011,0.15565,0.15788,77000.11,12276.00001,49
            bybit-trx-usdt-spot,2024-10-18T11:31:00.000000000Z,0.15566,0.16012,0.16012,0.15566,0.15789,77000.12,12277.00001,50
            bybit-trx-usdt-spot,2024-10-18T11:32:00.000000000Z,0.15567,0.16013,0.16013,0.15567,0.1579,77000.13,12278.00001,51
            bybit-trx-usdt-spot,2024-10-18T11:33:00.000000000Z,0.15568,0.16014,0.16014,0.15568,0.15791,77000.14,12279.00001,52
            bybit-trx-usdt-spot,2024-10-18T11:34:00.000000000Z,0.15569,0.16015,0.16015,0.15569,0.15792,77000.15,12280.00001,53
            bybit-trx-usdt-spot,2024-10-18T11:35:00.000000000Z,0.1557,0.16016,0.16016,0.1557,0.15793,77000.16,12281.00001,54
            bybit-trx-usdt-spot,2024-10-18T11:36:00.000000000Z,0.15571,0.16017,0.16017,0.15571,0.15794,77000.17,12282.00001,55
            bybit-trx-usdt-spot,2024-10-18T11:37:00.000000000Z,0.15572,0.16018,0.16018,0.15572,0.15795,77000.18,12283.00001,56

            """.trimIndent()
        assertResponseWithContentType(
            expectedCode = 200,
            expectedResponse = expectedResponseCsv,
            contentType = "text/csv",
            pathAndQuery = "/v4/timeseries/market-candles?markets=bybit-*&frequency=1m&paging_from=start&format=csv&api_key=$TEST_API_KEY",
        )

        assertEquals(jsonResponseToCsvResponse(expectedResponse), expectedResponseCsv)
    }

    @Test
    fun `instant and delayed spot and future candles with frequency of 1m and paging from start and start time including delayed candles`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:20:00.000000000Z","price_open":"0.15897","price_close":"0.15898","price_high":"0.15901","price_low":"0.15897","vwap":"0.15900065419949308734","volume":"77188.84","candle_usd_volume":"12267.1084377712053879038564","candle_trades_count":"41"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:21:00.000000000Z","price_open":"0.15556","price_close":"0.16002","price_high":"0.16002","price_low":"0.15556","vwap":"0.15779","volume":"77000.02","candle_usd_volume":"12267.00001","candle_trades_count":"41"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:22:00.000000000Z","price_open":"0.15557","price_close":"0.16003","price_high":"0.16003","price_low":"0.15557","vwap":"0.1578","volume":"77000.03","candle_usd_volume":"12268.00001","candle_trades_count":"42"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:23:00.000000000Z","price_open":"0.15558","price_close":"0.16004","price_high":"0.16004","price_low":"0.15558","vwap":"0.15781","volume":"77000.04","candle_usd_volume":"12269.00001","candle_trades_count":"43"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:24:00.000000000Z","price_open":"0.15559","price_close":"0.16005","price_high":"0.16005","price_low":"0.15559","vwap":"0.15782","volume":"77000.05","candle_usd_volume":"12270.00001","candle_trades_count":"44"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:25:00.000000000Z","price_open":"0.1556","price_close":"0.16006","price_high":"0.16006","price_low":"0.1556","vwap":"0.15783","volume":"77000.06","candle_usd_volume":"12271.00001","candle_trades_count":"45"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:26:00.000000000Z","price_open":"0.15561","price_close":"0.16007","price_high":"0.16007","price_low":"0.15561","vwap":"0.15784","volume":"77000.07","candle_usd_volume":"12272.00001","candle_trades_count":"46"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:27:00.000000000Z","price_open":"0.15562","price_close":"0.16008","price_high":"0.16008","price_low":"0.15562","vwap":"0.15785","volume":"77000.08","candle_usd_volume":"12273.00001","candle_trades_count":"47"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:28:00.000000000Z","price_open":"0.15563","price_close":"0.16009","price_high":"0.16009","price_low":"0.15563","vwap":"0.15786","volume":"77000.09","candle_usd_volume":"12274.00001","candle_trades_count":"48"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:29:00.000000000Z","price_open":"0.15564","price_close":"0.1601","price_high":"0.1601","price_low":"0.15564","vwap":"0.15787","volume":"77000.1","candle_usd_volume":"12275.00001","candle_trades_count":"48"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:30:00.000000000Z","price_open":"0.15565","price_close":"0.16011","price_high":"0.16011","price_low":"0.15565","vwap":"0.15788","volume":"77000.11","candle_usd_volume":"12276.00001","candle_trades_count":"49"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:31:00.000000000Z","price_open":"0.15566","price_close":"0.16012","price_high":"0.16012","price_low":"0.15566","vwap":"0.15789","volume":"77000.12","candle_usd_volume":"12277.00001","candle_trades_count":"50"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:32:00.000000000Z","price_open":"0.15567","price_close":"0.16013","price_high":"0.16013","price_low":"0.15567","vwap":"0.1579","volume":"77000.13","candle_usd_volume":"12278.00001","candle_trades_count":"51"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:33:00.000000000Z","price_open":"0.15568","price_close":"0.16014","price_high":"0.16014","price_low":"0.15568","vwap":"0.15791","volume":"77000.14","candle_usd_volume":"12279.00001","candle_trades_count":"52"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:34:00.000000000Z","price_open":"0.15569","price_close":"0.16015","price_high":"0.16015","price_low":"0.15569","vwap":"0.15792","volume":"77000.15","candle_usd_volume":"12280.00001","candle_trades_count":"53"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:35:00.000000000Z","price_open":"0.1557","price_close":"0.16016","price_high":"0.16016","price_low":"0.1557","vwap":"0.15793","volume":"77000.16","candle_usd_volume":"12281.00001","candle_trades_count":"54"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:36:00.000000000Z","price_open":"0.15571","price_close":"0.16017","price_high":"0.16017","price_low":"0.15571","vwap":"0.15794","volume":"77000.17","candle_usd_volume":"12282.00001","candle_trades_count":"55"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:37:00.000000000Z","price_open":"0.15572","price_close":"0.16018","price_high":"0.16018","price_low":"0.15572","vwap":"0.15795","volume":"77000.18","candle_usd_volume":"12283.00001","candle_trades_count":"56"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:20:00.000000000Z","price_open":"0.15897","price_close":"0.15898","price_high":"0.15901","price_low":"0.15897","vwap":"0.15900065419949308734","volume":"77188.84","candle_usd_volume":"12267.1084377712053879038564","candle_trades_count":"41"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:21:00.000000000Z","price_open":"0.15556","price_close":"0.16002","price_high":"0.16002","price_low":"0.15556","vwap":"0.15779","volume":"77000.02","candle_usd_volume":"12267.00001","candle_trades_count":"41"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:22:00.000000000Z","price_open":"0.15557","price_close":"0.16003","price_high":"0.16003","price_low":"0.15557","vwap":"0.1578","volume":"77000.03","candle_usd_volume":"12268.00001","candle_trades_count":"42"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:23:00.000000000Z","price_open":"0.15558","price_close":"0.16004","price_high":"0.16004","price_low":"0.15558","vwap":"0.15781","volume":"77000.04","candle_usd_volume":"12269.00001","candle_trades_count":"43"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:24:00.000000000Z","price_open":"0.15559","price_close":"0.16005","price_high":"0.16005","price_low":"0.15559","vwap":"0.15782","volume":"77000.05","candle_usd_volume":"12270.00001","candle_trades_count":"44"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:25:00.000000000Z","price_open":"0.1556","price_close":"0.16006","price_high":"0.16006","price_low":"0.1556","vwap":"0.15783","volume":"77000.06","candle_usd_volume":"12271.00001","candle_trades_count":"45"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:26:00.000000000Z","price_open":"0.15561","price_close":"0.16007","price_high":"0.16007","price_low":"0.15561","vwap":"0.15784","volume":"77000.07","candle_usd_volume":"12272.00001","candle_trades_count":"46"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:27:00.000000000Z","price_open":"0.15562","price_close":"0.16008","price_high":"0.16008","price_low":"0.15562","vwap":"0.15785","volume":"77000.08","candle_usd_volume":"12273.00001","candle_trades_count":"47"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:28:00.000000000Z","price_open":"0.15563","price_close":"0.16009","price_high":"0.16009","price_low":"0.15563","vwap":"0.15786","volume":"77000.09","candle_usd_volume":"12274.00001","candle_trades_count":"48"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:29:00.000000000Z","price_open":"0.15564","price_close":"0.1601","price_high":"0.1601","price_low":"0.15564","vwap":"0.15787","volume":"77000.1","candle_usd_volume":"12275.00001","candle_trades_count":"48"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:30:00.000000000Z","price_open":"0.15565","price_close":"0.16011","price_high":"0.16011","price_low":"0.15565","vwap":"0.15788","volume":"77000.11","candle_usd_volume":"12276.00001","candle_trades_count":"49"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:31:00.000000000Z","price_open":"0.15566","price_close":"0.16012","price_high":"0.16012","price_low":"0.15566","vwap":"0.15789","volume":"77000.12","candle_usd_volume":"12277.00001","candle_trades_count":"50"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:32:00.000000000Z","price_open":"0.15567","price_close":"0.16013","price_high":"0.16013","price_low":"0.15567","vwap":"0.1579","volume":"77000.13","candle_usd_volume":"12278.00001","candle_trades_count":"51"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:33:00.000000000Z","price_open":"0.15568","price_close":"0.16014","price_high":"0.16014","price_low":"0.15568","vwap":"0.15791","volume":"77000.14","candle_usd_volume":"12279.00001","candle_trades_count":"52"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:34:00.000000000Z","price_open":"0.15569","price_close":"0.16015","price_high":"0.16015","price_low":"0.15569","vwap":"0.15792","volume":"77000.15","candle_usd_volume":"12280.00001","candle_trades_count":"53"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:35:00.000000000Z","price_open":"0.1557","price_close":"0.16016","price_high":"0.16016","price_low":"0.1557","vwap":"0.15793","volume":"77000.16","candle_usd_volume":"12281.00001","candle_trades_count":"54"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:36:00.000000000Z","price_open":"0.15571","price_close":"0.16017","price_high":"0.16017","price_low":"0.15571","vwap":"0.15794","volume":"77000.17","candle_usd_volume":"12282.00001","candle_trades_count":"55"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:37:00.000000000Z","price_open":"0.15572","price_close":"0.16018","price_high":"0.16018","price_low":"0.15572","vwap":"0.15795","volume":"77000.18","candle_usd_volume":"12283.00001","candle_trades_count":"56"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-*&frequency=1m&paging_from=start&start_time=2024-10-18T11:20:00&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed spot and future candles with frequency of 1m and paging from start and start time excluding delayed candles`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:30:00.000000000Z","price_open":"0.15565","price_close":"0.16011","price_high":"0.16011","price_low":"0.15565","vwap":"0.15788","volume":"77000.11","candle_usd_volume":"12276.00001","candle_trades_count":"49"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:31:00.000000000Z","price_open":"0.15566","price_close":"0.16012","price_high":"0.16012","price_low":"0.15566","vwap":"0.15789","volume":"77000.12","candle_usd_volume":"12277.00001","candle_trades_count":"50"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:32:00.000000000Z","price_open":"0.15567","price_close":"0.16013","price_high":"0.16013","price_low":"0.15567","vwap":"0.1579","volume":"77000.13","candle_usd_volume":"12278.00001","candle_trades_count":"51"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:33:00.000000000Z","price_open":"0.15568","price_close":"0.16014","price_high":"0.16014","price_low":"0.15568","vwap":"0.15791","volume":"77000.14","candle_usd_volume":"12279.00001","candle_trades_count":"52"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:34:00.000000000Z","price_open":"0.15569","price_close":"0.16015","price_high":"0.16015","price_low":"0.15569","vwap":"0.15792","volume":"77000.15","candle_usd_volume":"12280.00001","candle_trades_count":"53"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:35:00.000000000Z","price_open":"0.1557","price_close":"0.16016","price_high":"0.16016","price_low":"0.1557","vwap":"0.15793","volume":"77000.16","candle_usd_volume":"12281.00001","candle_trades_count":"54"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:36:00.000000000Z","price_open":"0.15571","price_close":"0.16017","price_high":"0.16017","price_low":"0.15571","vwap":"0.15794","volume":"77000.17","candle_usd_volume":"12282.00001","candle_trades_count":"55"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:37:00.000000000Z","price_open":"0.15572","price_close":"0.16018","price_high":"0.16018","price_low":"0.15572","vwap":"0.15795","volume":"77000.18","candle_usd_volume":"12283.00001","candle_trades_count":"56"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:30:00.000000000Z","price_open":"0.15565","price_close":"0.16011","price_high":"0.16011","price_low":"0.15565","vwap":"0.15788","volume":"77000.11","candle_usd_volume":"12276.00001","candle_trades_count":"49"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:31:00.000000000Z","price_open":"0.15566","price_close":"0.16012","price_high":"0.16012","price_low":"0.15566","vwap":"0.15789","volume":"77000.12","candle_usd_volume":"12277.00001","candle_trades_count":"50"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:32:00.000000000Z","price_open":"0.15567","price_close":"0.16013","price_high":"0.16013","price_low":"0.15567","vwap":"0.1579","volume":"77000.13","candle_usd_volume":"12278.00001","candle_trades_count":"51"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:33:00.000000000Z","price_open":"0.15568","price_close":"0.16014","price_high":"0.16014","price_low":"0.15568","vwap":"0.15791","volume":"77000.14","candle_usd_volume":"12279.00001","candle_trades_count":"52"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:34:00.000000000Z","price_open":"0.15569","price_close":"0.16015","price_high":"0.16015","price_low":"0.15569","vwap":"0.15792","volume":"77000.15","candle_usd_volume":"12280.00001","candle_trades_count":"53"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:35:00.000000000Z","price_open":"0.1557","price_close":"0.16016","price_high":"0.16016","price_low":"0.1557","vwap":"0.15793","volume":"77000.16","candle_usd_volume":"12281.00001","candle_trades_count":"54"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:36:00.000000000Z","price_open":"0.15571","price_close":"0.16017","price_high":"0.16017","price_low":"0.15571","vwap":"0.15794","volume":"77000.17","candle_usd_volume":"12282.00001","candle_trades_count":"55"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:37:00.000000000Z","price_open":"0.15572","price_close":"0.16018","price_high":"0.16018","price_low":"0.15572","vwap":"0.15795","volume":"77000.18","candle_usd_volume":"12283.00001","candle_trades_count":"56"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-*&frequency=1m&paging_from=start&start_time=2024-10-18T11:30:00&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed spot and future candles with frequency of 1m and paging from start and start time and end time including delayed candles`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:20:00.000000000Z","price_open":"0.15897","price_close":"0.15898","price_high":"0.15901","price_low":"0.15897","vwap":"0.15900065419949308734","volume":"77188.84","candle_usd_volume":"12267.1084377712053879038564","candle_trades_count":"41"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:21:00.000000000Z","price_open":"0.15556","price_close":"0.16002","price_high":"0.16002","price_low":"0.15556","vwap":"0.15779","volume":"77000.02","candle_usd_volume":"12267.00001","candle_trades_count":"41"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:22:00.000000000Z","price_open":"0.15557","price_close":"0.16003","price_high":"0.16003","price_low":"0.15557","vwap":"0.1578","volume":"77000.03","candle_usd_volume":"12268.00001","candle_trades_count":"42"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:20:00.000000000Z","price_open":"0.15897","price_close":"0.15898","price_high":"0.15901","price_low":"0.15897","vwap":"0.15900065419949308734","volume":"77188.84","candle_usd_volume":"12267.1084377712053879038564","candle_trades_count":"41"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:21:00.000000000Z","price_open":"0.15556","price_close":"0.16002","price_high":"0.16002","price_low":"0.15556","vwap":"0.15779","volume":"77000.02","candle_usd_volume":"12267.00001","candle_trades_count":"41"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:22:00.000000000Z","price_open":"0.15557","price_close":"0.16003","price_high":"0.16003","price_low":"0.15557","vwap":"0.1578","volume":"77000.03","candle_usd_volume":"12268.00001","candle_trades_count":"42"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-*&frequency=1m&paging_from=start&start_time=2024-10-18T11:20:00&end_time=2024-10-18T11:22:00&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed spot and future candles with frequency of 1m and paging from start and start time and end time excluding delayed candles`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:30:00.000000000Z","price_open":"0.15565","price_close":"0.16011","price_high":"0.16011","price_low":"0.15565","vwap":"0.15788","volume":"77000.11","candle_usd_volume":"12276.00001","candle_trades_count":"49"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:31:00.000000000Z","price_open":"0.15566","price_close":"0.16012","price_high":"0.16012","price_low":"0.15566","vwap":"0.15789","volume":"77000.12","candle_usd_volume":"12277.00001","candle_trades_count":"50"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:32:00.000000000Z","price_open":"0.15567","price_close":"0.16013","price_high":"0.16013","price_low":"0.15567","vwap":"0.1579","volume":"77000.13","candle_usd_volume":"12278.00001","candle_trades_count":"51"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:33:00.000000000Z","price_open":"0.15568","price_close":"0.16014","price_high":"0.16014","price_low":"0.15568","vwap":"0.15791","volume":"77000.14","candle_usd_volume":"12279.00001","candle_trades_count":"52"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:34:00.000000000Z","price_open":"0.15569","price_close":"0.16015","price_high":"0.16015","price_low":"0.15569","vwap":"0.15792","volume":"77000.15","candle_usd_volume":"12280.00001","candle_trades_count":"53"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:30:00.000000000Z","price_open":"0.15565","price_close":"0.16011","price_high":"0.16011","price_low":"0.15565","vwap":"0.15788","volume":"77000.11","candle_usd_volume":"12276.00001","candle_trades_count":"49"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:31:00.000000000Z","price_open":"0.15566","price_close":"0.16012","price_high":"0.16012","price_low":"0.15566","vwap":"0.15789","volume":"77000.12","candle_usd_volume":"12277.00001","candle_trades_count":"50"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:32:00.000000000Z","price_open":"0.15567","price_close":"0.16013","price_high":"0.16013","price_low":"0.15567","vwap":"0.1579","volume":"77000.13","candle_usd_volume":"12278.00001","candle_trades_count":"51"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:33:00.000000000Z","price_open":"0.15568","price_close":"0.16014","price_high":"0.16014","price_low":"0.15568","vwap":"0.15791","volume":"77000.14","candle_usd_volume":"12279.00001","candle_trades_count":"52"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:34:00.000000000Z","price_open":"0.15569","price_close":"0.16015","price_high":"0.16015","price_low":"0.15569","vwap":"0.15792","volume":"77000.15","candle_usd_volume":"12280.00001","candle_trades_count":"53"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-*&frequency=1m&paging_from=start&start_time=2024-10-18T11:30:00&end_time=2024-10-18T11:34:00&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed spot and future candles with frequency of 1m and paging from start and end time including delayed candles`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:20:00.000000000Z","price_open":"0.15897","price_close":"0.15898","price_high":"0.15901","price_low":"0.15897","vwap":"0.15900065419949308734","volume":"77188.84","candle_usd_volume":"12267.1084377712053879038564","candle_trades_count":"41"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:21:00.000000000Z","price_open":"0.15556","price_close":"0.16002","price_high":"0.16002","price_low":"0.15556","vwap":"0.15779","volume":"77000.02","candle_usd_volume":"12267.00001","candle_trades_count":"41"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:22:00.000000000Z","price_open":"0.15557","price_close":"0.16003","price_high":"0.16003","price_low":"0.15557","vwap":"0.1578","volume":"77000.03","candle_usd_volume":"12268.00001","candle_trades_count":"42"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:23:00.000000000Z","price_open":"0.15558","price_close":"0.16004","price_high":"0.16004","price_low":"0.15558","vwap":"0.15781","volume":"77000.04","candle_usd_volume":"12269.00001","candle_trades_count":"43"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:24:00.000000000Z","price_open":"0.15559","price_close":"0.16005","price_high":"0.16005","price_low":"0.15559","vwap":"0.15782","volume":"77000.05","candle_usd_volume":"12270.00001","candle_trades_count":"44"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:20:00.000000000Z","price_open":"0.15897","price_close":"0.15898","price_high":"0.15901","price_low":"0.15897","vwap":"0.15900065419949308734","volume":"77188.84","candle_usd_volume":"12267.1084377712053879038564","candle_trades_count":"41"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:21:00.000000000Z","price_open":"0.15556","price_close":"0.16002","price_high":"0.16002","price_low":"0.15556","vwap":"0.15779","volume":"77000.02","candle_usd_volume":"12267.00001","candle_trades_count":"41"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:22:00.000000000Z","price_open":"0.15557","price_close":"0.16003","price_high":"0.16003","price_low":"0.15557","vwap":"0.1578","volume":"77000.03","candle_usd_volume":"12268.00001","candle_trades_count":"42"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:23:00.000000000Z","price_open":"0.15558","price_close":"0.16004","price_high":"0.16004","price_low":"0.15558","vwap":"0.15781","volume":"77000.04","candle_usd_volume":"12269.00001","candle_trades_count":"43"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:24:00.000000000Z","price_open":"0.15559","price_close":"0.16005","price_high":"0.16005","price_low":"0.15559","vwap":"0.15782","volume":"77000.05","candle_usd_volume":"12270.00001","candle_trades_count":"44"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-*&frequency=1m&paging_from=start&end_time=2024-10-18T11:24:00&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed spot and future candles with frequency of 1m and paging from start and end time excluding instant candles`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:20:00.000000000Z","price_open":"0.15897","price_close":"0.15898","price_high":"0.15901","price_low":"0.15897","vwap":"0.15900065419949308734","volume":"77188.84","candle_usd_volume":"12267.1084377712053879038564","candle_trades_count":"41"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:20:00.000000000Z","price_open":"0.15897","price_close":"0.15898","price_high":"0.15901","price_low":"0.15897","vwap":"0.15900065419949308734","volume":"77188.84","candle_usd_volume":"12267.1084377712053879038564","candle_trades_count":"41"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-*&frequency=1m&paging_from=start&end_time=2024-10-18T11:20:00&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed spot and future candles with frequency of 5m and paging from start and 1 instant value is missed intentionally`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:20:00.000000000Z","price_open":"0.15897","price_close":"0.1589","price_high":"0.15901","price_low":"0.15888","vwap":"0.1589704600455617","volume":"150376.41","candle_usd_volume":"23895.68890582412088074642033413356746325003","candle_trades_count":"134"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:30:00.000000000Z","price_open":"0.16","price_close":"0.1601","price_high":"0.1601","price_low":"0.16","vwap":"0.16005","volume":"150000.01","candle_usd_volume":"23895.00001","candle_trades_count":"135"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:35:00.000000000Z","price_open":"0.16002","price_close":"0.16008","price_high":"0.16008","price_low":"0.16002","vwap":"0.16006","volume":"150001.01","candle_usd_volume":"23896.00001","candle_trades_count":"136"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:20:00.000000000Z","price_open":"0.15897","price_close":"0.1589","price_high":"0.15901","price_low":"0.15888","vwap":"0.1589704600455617","volume":"150376.41","candle_usd_volume":"23895.68890582412088074642033413356746325003","candle_trades_count":"134"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:30:00.000000000Z","price_open":"0.16","price_close":"0.1601","price_high":"0.1601","price_low":"0.16","vwap":"0.16005","volume":"150000.01","candle_usd_volume":"23895.00001","candle_trades_count":"135"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:35:00.000000000Z","price_open":"0.16002","price_close":"0.16008","price_high":"0.16008","price_low":"0.16002","vwap":"0.16006","volume":"150001.01","candle_usd_volume":"23896.00001","candle_trades_count":"136"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-*&frequency=5m&paging_from=start&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed spot and future candles with frequency of 5m and paging from start and 1 instant value is missed intentionally and page size = 4`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:20:00.000000000Z","price_open":"0.15897","price_close":"0.1589","price_high":"0.15901","price_low":"0.15888","vwap":"0.1589704600455617","volume":"150376.41","candle_usd_volume":"23895.68890582412088074642033413356746325003","candle_trades_count":"134"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:30:00.000000000Z","price_open":"0.16","price_close":"0.1601","price_high":"0.1601","price_low":"0.16","vwap":"0.16005","volume":"150000.01","candle_usd_volume":"23895.00001","candle_trades_count":"135"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:35:00.000000000Z","price_open":"0.16002","price_close":"0.16008","price_high":"0.16008","price_low":"0.16002","vwap":"0.16006","volume":"150001.01","candle_usd_volume":"23896.00001","candle_trades_count":"136"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:20:00.000000000Z","price_open":"0.15897","price_close":"0.1589","price_high":"0.15901","price_low":"0.15888","vwap":"0.1589704600455617","volume":"150376.41","candle_usd_volume":"23895.68890582412088074642033413356746325003","candle_trades_count":"134"}],"next_page_token":"YnliaXQtdHJ4LXVzZHQtc3BvdA.MjAyNC0xMC0xOFQxMToyMDowMFo","next_page_url":"http://127.0.0.1:8080/v4/timeseries/market-candles?markets=bybit-*&frequency=5m&paging_from=start&page_size=4&api_key=x1&next_page_token=YnliaXQtdHJ4LXVzZHQtc3BvdA.MjAyNC0xMC0xOFQxMToyMDowMFo"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-*&frequency=5m&paging_from=start&page_size=4&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed spot and future candles with frequency of 5m and paging from end and 1 instant value is missed intentionally and page size = 4`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:35:00.000000000Z","price_open":"0.16002","price_close":"0.16008","price_high":"0.16008","price_low":"0.16002","vwap":"0.16006","volume":"150001.01","candle_usd_volume":"23896.00001","candle_trades_count":"136"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:20:00.000000000Z","price_open":"0.15897","price_close":"0.1589","price_high":"0.15901","price_low":"0.15888","vwap":"0.1589704600455617","volume":"150376.41","candle_usd_volume":"23895.68890582412088074642033413356746325003","candle_trades_count":"134"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:30:00.000000000Z","price_open":"0.16","price_close":"0.1601","price_high":"0.1601","price_low":"0.16","vwap":"0.16005","volume":"150000.01","candle_usd_volume":"23895.00001","candle_trades_count":"135"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:35:00.000000000Z","price_open":"0.16002","price_close":"0.16008","price_high":"0.16008","price_low":"0.16002","vwap":"0.16006","volume":"150001.01","candle_usd_volume":"23896.00001","candle_trades_count":"136"}],"next_page_token":"YnliaXQtVFJYVVNEVC1mdXR1cmU.MjAyNC0xMC0xOFQxMTozNTowMFo","next_page_url":"http://127.0.0.1:8080/v4/timeseries/market-candles?markets=bybit-*&frequency=5m&paging_from=end&page_size=4&api_key=x1&next_page_token=YnliaXQtVFJYVVNEVC1mdXR1cmU.MjAyNC0xMC0xOFQxMTozNTowMFo"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-*&frequency=5m&paging_from=end&page_size=4&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed spot and future candles with frequency of 10m and paging from start`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:20:00.000000000Z","price_open":"0.15897","price_close":"0.15891","price_high":"0.15901","price_low":"0.15888","vwap":"0.158960288888022","volume":"186874.83","candle_usd_volume":"29694.05028649258992450344505400967153696509","candle_trades_count":"200"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:30:00.000000000Z","price_open":"0.15444","price_close":"0.16444","price_high":"0.16444","price_low":"0.15444","vwap":"0.15888","volume":"186874.01","candle_usd_volume":"29694.00001","candle_trades_count":"201"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:20:00.000000000Z","price_open":"0.15897","price_close":"0.15891","price_high":"0.15901","price_low":"0.15888","vwap":"0.158960288888022","volume":"186874.83","candle_usd_volume":"29694.05028649258992450344505400967153696509","candle_trades_count":"200"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:30:00.000000000Z","price_open":"0.15444","price_close":"0.16444","price_high":"0.16444","price_low":"0.15444","vwap":"0.15888","volume":"186874.01","candle_usd_volume":"29694.00001","candle_trades_count":"201"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-*&frequency=10m&paging_from=start&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed spot and future candles with frequency of 10m and paging from start and page size = 3`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:20:00.000000000Z","price_open":"0.15897","price_close":"0.15891","price_high":"0.15901","price_low":"0.15888","vwap":"0.158960288888022","volume":"186874.83","candle_usd_volume":"29694.05028649258992450344505400967153696509","candle_trades_count":"200"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:30:00.000000000Z","price_open":"0.15444","price_close":"0.16444","price_high":"0.16444","price_low":"0.15444","vwap":"0.15888","volume":"186874.01","candle_usd_volume":"29694.00001","candle_trades_count":"201"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:20:00.000000000Z","price_open":"0.15897","price_close":"0.15891","price_high":"0.15901","price_low":"0.15888","vwap":"0.158960288888022","volume":"186874.83","candle_usd_volume":"29694.05028649258992450344505400967153696509","candle_trades_count":"200"}],"next_page_token":"YnliaXQtdHJ4LXVzZHQtc3BvdA.MjAyNC0xMC0xOFQxMToyMDowMFo","next_page_url":"http://127.0.0.1:8080/v4/timeseries/market-candles?markets=bybit-*&frequency=10m&paging_from=start&page_size=3&api_key=x1&next_page_token=YnliaXQtdHJ4LXVzZHQtc3BvdA.MjAyNC0xMC0xOFQxMToyMDowMFo"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-*&frequency=10m&paging_from=start&page_size=3&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed spot and future candles with frequency of 10m and paging from end and page size = 3`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:30:00.000000000Z","price_open":"0.15444","price_close":"0.16444","price_high":"0.16444","price_low":"0.15444","vwap":"0.15888","volume":"186874.01","candle_usd_volume":"29694.00001","candle_trades_count":"201"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:20:00.000000000Z","price_open":"0.15897","price_close":"0.15891","price_high":"0.15901","price_low":"0.15888","vwap":"0.158960288888022","volume":"186874.83","candle_usd_volume":"29694.05028649258992450344505400967153696509","candle_trades_count":"200"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:30:00.000000000Z","price_open":"0.15444","price_close":"0.16444","price_high":"0.16444","price_low":"0.15444","vwap":"0.15888","volume":"186874.01","candle_usd_volume":"29694.00001","candle_trades_count":"201"}],"next_page_token":"YnliaXQtVFJYVVNEVC1mdXR1cmU.MjAyNC0xMC0xOFQxMTozMDowMFo","next_page_url":"http://127.0.0.1:8080/v4/timeseries/market-candles?markets=bybit-*&frequency=10m&paging_from=end&page_size=3&api_key=x1&next_page_token=YnliaXQtVFJYVVNEVC1mdXR1cmU.MjAyNC0xMC0xOFQxMTozMDowMFo"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-*&frequency=10m&paging_from=end&page_size=3&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed spot and future candles with frequency of 15m and paging from start`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:15:00.000000000Z","price_open":"0.15903","price_close":"0.15891","price_high":"0.15903","price_low":"0.15888","vwap":"0.1589748654124442","volume":"400331.96","candle_usd_volume":"63618.77307723008592494781199918967153696509","candle_trades_count":"335"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:30:00.000000000Z","price_open":"0.15555","price_close":"0.15666","price_high":"0.15666","price_low":"0.15555","vwap":"0.156","volume":"400331.01","candle_usd_volume":"63618.00001","candle_trades_count":"336"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:15:00.000000000Z","price_open":"0.15903","price_close":"0.15891","price_high":"0.15903","price_low":"0.15888","vwap":"0.1589748654124442","volume":"400331.96","candle_usd_volume":"63618.77307723008592494781199918967153696509","candle_trades_count":"335"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:30:00.000000000Z","price_open":"0.15555","price_close":"0.15666","price_high":"0.15666","price_low":"0.15555","vwap":"0.156","volume":"400331.01","candle_usd_volume":"63618.00001","candle_trades_count":"336"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-*&frequency=15m&paging_from=start&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed spot and future candles with frequency of 15m and paging from start and page size = 3`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:15:00.000000000Z","price_open":"0.15903","price_close":"0.15891","price_high":"0.15903","price_low":"0.15888","vwap":"0.1589748654124442","volume":"400331.96","candle_usd_volume":"63618.77307723008592494781199918967153696509","candle_trades_count":"335"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:30:00.000000000Z","price_open":"0.15555","price_close":"0.15666","price_high":"0.15666","price_low":"0.15555","vwap":"0.156","volume":"400331.01","candle_usd_volume":"63618.00001","candle_trades_count":"336"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:15:00.000000000Z","price_open":"0.15903","price_close":"0.15891","price_high":"0.15903","price_low":"0.15888","vwap":"0.1589748654124442","volume":"400331.96","candle_usd_volume":"63618.77307723008592494781199918967153696509","candle_trades_count":"335"}],"next_page_token":"YnliaXQtdHJ4LXVzZHQtc3BvdA.MjAyNC0xMC0xOFQxMToxNTowMFo","next_page_url":"http://127.0.0.1:8080/v4/timeseries/market-candles?markets=bybit-*&frequency=15m&paging_from=start&page_size=3&api_key=x1&next_page_token=YnliaXQtdHJ4LXVzZHQtc3BvdA.MjAyNC0xMC0xOFQxMToxNTowMFo"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-*&frequency=15m&paging_from=start&page_size=3&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed spot and future candles with frequency of 15m and paging from end and page size = 3`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:30:00.000000000Z","price_open":"0.15555","price_close":"0.15666","price_high":"0.15666","price_low":"0.15555","vwap":"0.156","volume":"400331.01","candle_usd_volume":"63618.00001","candle_trades_count":"336"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:15:00.000000000Z","price_open":"0.15903","price_close":"0.15891","price_high":"0.15903","price_low":"0.15888","vwap":"0.1589748654124442","volume":"400331.96","candle_usd_volume":"63618.77307723008592494781199918967153696509","candle_trades_count":"335"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:30:00.000000000Z","price_open":"0.15555","price_close":"0.15666","price_high":"0.15666","price_low":"0.15555","vwap":"0.156","volume":"400331.01","candle_usd_volume":"63618.00001","candle_trades_count":"336"}],"next_page_token":"YnliaXQtVFJYVVNEVC1mdXR1cmU.MjAyNC0xMC0xOFQxMTozMDowMFo","next_page_url":"http://127.0.0.1:8080/v4/timeseries/market-candles?markets=bybit-*&frequency=15m&paging_from=end&page_size=3&api_key=x1&next_page_token=YnliaXQtVFJYVVNEVC1mdXR1cmU.MjAyNC0xMC0xOFQxMTozMDowMFo"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-*&frequency=15m&paging_from=end&page_size=3&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed spot and future candles with frequency of 30m and paging from start`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:00:00.000000000Z","price_open":"0.15908","price_close":"0.15891","price_high":"0.15914","price_low":"0.15888","vwap":"0.1590188641784494","volume":"717288.38","candle_usd_volume":"114021.61717095928122785887028239535379449553","candle_trades_count":"670"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:30:00.000000000Z","price_open":"0.15901","price_close":"0.15991","price_high":"0.15991","price_low":"0.15901","vwap":"0.15951","volume":"717288.01","candle_usd_volume":"114021.00001","candle_trades_count":"671"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:00:00.000000000Z","price_open":"0.15908","price_close":"0.15891","price_high":"0.15914","price_low":"0.15888","vwap":"0.1590188641784494","volume":"717288.38","candle_usd_volume":"114021.61717095928122785887028239535379449553","candle_trades_count":"670"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:30:00.000000000Z","price_open":"0.15901","price_close":"0.15991","price_high":"0.15991","price_low":"0.15901","vwap":"0.15951","volume":"717288.01","candle_usd_volume":"114021.00001","candle_trades_count":"671"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-*&frequency=30m&paging_from=start&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed spot and future candles with frequency of 30m and paging from start and page size = 3`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:00:00.000000000Z","price_open":"0.15908","price_close":"0.15891","price_high":"0.15914","price_low":"0.15888","vwap":"0.1590188641784494","volume":"717288.38","candle_usd_volume":"114021.61717095928122785887028239535379449553","candle_trades_count":"670"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:30:00.000000000Z","price_open":"0.15901","price_close":"0.15991","price_high":"0.15991","price_low":"0.15901","vwap":"0.15951","volume":"717288.01","candle_usd_volume":"114021.00001","candle_trades_count":"671"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:00:00.000000000Z","price_open":"0.15908","price_close":"0.15891","price_high":"0.15914","price_low":"0.15888","vwap":"0.1590188641784494","volume":"717288.38","candle_usd_volume":"114021.61717095928122785887028239535379449553","candle_trades_count":"670"}],"next_page_token":"YnliaXQtdHJ4LXVzZHQtc3BvdA.MjAyNC0xMC0xOFQxMTowMDowMFo","next_page_url":"http://127.0.0.1:8080/v4/timeseries/market-candles?markets=bybit-*&frequency=30m&paging_from=start&page_size=3&api_key=x1&next_page_token=YnliaXQtdHJ4LXVzZHQtc3BvdA.MjAyNC0xMC0xOFQxMTowMDowMFo"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-*&frequency=30m&paging_from=start&page_size=3&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed spot and future candles with frequency of 30m and paging from end and page size = 3`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:30:00.000000000Z","price_open":"0.15901","price_close":"0.15991","price_high":"0.15991","price_low":"0.15901","vwap":"0.15951","volume":"717288.01","candle_usd_volume":"114021.00001","candle_trades_count":"671"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:00:00.000000000Z","price_open":"0.15908","price_close":"0.15891","price_high":"0.15914","price_low":"0.15888","vwap":"0.1590188641784494","volume":"717288.38","candle_usd_volume":"114021.61717095928122785887028239535379449553","candle_trades_count":"670"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:30:00.000000000Z","price_open":"0.15901","price_close":"0.15991","price_high":"0.15991","price_low":"0.15901","vwap":"0.15951","volume":"717288.01","candle_usd_volume":"114021.00001","candle_trades_count":"671"}],"next_page_token":"YnliaXQtVFJYVVNEVC1mdXR1cmU.MjAyNC0xMC0xOFQxMTozMDowMFo","next_page_url":"http://127.0.0.1:8080/v4/timeseries/market-candles?markets=bybit-*&frequency=30m&paging_from=end&page_size=3&api_key=x1&next_page_token=YnliaXQtVFJYVVNEVC1mdXR1cmU.MjAyNC0xMC0xOFQxMTozMDowMFo"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-*&frequency=30m&paging_from=end&page_size=3&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed spot and future candles with frequency of 1h and paging from start`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-TRXUSDT-future","time":"2024-10-17T13:00:00.000000000Z","price_open":"0.16608","price_close":"0.16691","price_high":"0.16614","price_low":"0.16688","vwap":"0.1660188641784494","volume":"766288.38","candle_usd_volume":"116026.61717095928122785887028239535379449553","candle_trades_count":"679"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:00:00.000000000Z","price_open":"0.15908","price_close":"0.1589","price_high":"0.15914","price_low":"0.15884","vwap":"0.1589882063083388","volume":"1385996.91","candle_usd_volume":"220279.73634820367613065647384011961914315841","candle_trades_count":"1258"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T12:00:00.000000000Z","price_open":"0.1595","price_close":"0.1695","price_high":"0.1695","price_low":"0.1595","vwap":"0.1655","volume":"1385996.01","candle_usd_volume":"220279.00001","candle_trades_count":"1259"},{"market":"bybit-trx-usdt-spot","time":"2024-10-16T13:00:00.000000000Z","price_open":"0.15408","price_close":"0.15491","price_high":"0.15414","price_low":"0.15488","vwap":"0.1540188641784494","volume":"714288.38","candle_usd_volume":"114021.61717095928122785887028239535379449553","candle_trades_count":"645"},{"market":"bybit-trx-usdt-spot","time":"2024-10-17T11:00:00.000000000Z","price_open":"0.15508","price_close":"0.15591","price_high":"0.15514","price_low":"0.15588","vwap":"0.1550188641784494","volume":"715288.38","candle_usd_volume":"115021.61717095928122785887028239535379449553","candle_trades_count":"675"},{"market":"bybit-trx-usdt-spot","time":"2024-10-17T12:00:00.000000000Z","price_open":"0.15608","price_close":"0.15691","price_high":"0.15614","price_low":"0.15688","vwap":"0.1560188641784494","volume":"716288.38","candle_usd_volume":"116021.61717095928122785887028239535379449553","candle_trades_count":"676"},{"market":"bybit-trx-usdt-spot","time":"2024-10-17T13:00:00.000000000Z","price_open":"0.16608","price_close":"0.16691","price_high":"0.16614","price_low":"0.16688","vwap":"0.1660188641784494","volume":"766288.38","candle_usd_volume":"116026.61717095928122785887028239535379449553","candle_trades_count":"679"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T10:00:00.000000000Z","price_open":"0.1795","price_close":"0.1795","price_high":"0.1795","price_low":"0.1795","vwap":"0.1755","volume":"1783996.01","candle_usd_volume":"220277.00001","candle_trades_count":"1753"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:00:00.000000000Z","price_open":"0.15908","price_close":"0.1589","price_high":"0.15914","price_low":"0.15884","vwap":"0.1589882063083388","volume":"1385996.91","candle_usd_volume":"220279.73634820367613065647384011961914315841","candle_trades_count":"1258"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T12:00:00.000000000Z","price_open":"0.1595","price_close":"0.1695","price_high":"0.1695","price_low":"0.1595","vwap":"0.1655","volume":"1385996.01","candle_usd_volume":"220279.00001","candle_trades_count":"1259"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T13:00:00.000000000Z","price_open":"0.1395","price_close":"0.1395","price_high":"0.1395","price_low":"0.1395","vwap":"0.1355","volume":"1383996.01","candle_usd_volume":"220273.00001","candle_trades_count":"1253"},{"market":"bybit-trx-usdt-spot","time":"2024-10-19T10:00:00.000000000Z","price_open":"0.1295","price_close":"0.1295","price_high":"0.1295","price_low":"0.1295","vwap":"0.1255","volume":"1284996.01","candle_usd_volume":"220272.00001","candle_trades_count":"1252"},{"market":"bybit-trx-usdt-spot","time":"2024-10-19T11:00:00.000000000Z","price_open":"0.1695","price_close":"0.1695","price_high":"0.1695","price_low":"0.1695","vwap":"0.1655","volume":"1684996.01","candle_usd_volume":"220276.00001","candle_trades_count":"1256"},{"market":"bybit-trx-usdt-spot","time":"2024-10-19T12:00:00.000000000Z","price_open":"0.1795","price_close":"0.1795","price_high":"0.1795","price_low":"0.1795","vwap":"0.1755","volume":"1784996.01","candle_usd_volume":"220277.00001","candle_trades_count":"1257"},{"market":"bybit-trx-usdt-spot","time":"2024-10-20T10:00:00.000000000Z","price_open":"0.2295","price_close":"0.2295","price_high":"0.2295","price_low":"0.2295","vwap":"0.2255","volume":"2284996.01","candle_usd_volume":"420272.00001","candle_trades_count":"2252"},{"market":"bybit-trx-usdt-spot","time":"2024-10-20T11:00:00.000000000Z","price_open":"0.2695","price_close":"0.2695","price_high":"0.2695","price_low":"0.2695","vwap":"0.2655","volume":"2684996.01","candle_usd_volume":"420276.00001","candle_trades_count":"2256"},{"market":"bybit-trx-usdt-spot","time":"2024-10-20T12:00:00.000000000Z","price_open":"0.2795","price_close":"0.2795","price_high":"0.2795","price_low":"0.2795","vwap":"0.2755","volume":"2784996.01","candle_usd_volume":"420277.00001","candle_trades_count":"2257"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-*&frequency=1h&paging_from=start&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed spot and future candles with frequency of 1h and paging from start and page size = 15`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-TRXUSDT-future","time":"2024-10-17T13:00:00.000000000Z","price_open":"0.16608","price_close":"0.16691","price_high":"0.16614","price_low":"0.16688","vwap":"0.1660188641784494","volume":"766288.38","candle_usd_volume":"116026.61717095928122785887028239535379449553","candle_trades_count":"679"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T11:00:00.000000000Z","price_open":"0.15908","price_close":"0.1589","price_high":"0.15914","price_low":"0.15884","vwap":"0.1589882063083388","volume":"1385996.91","candle_usd_volume":"220279.73634820367613065647384011961914315841","candle_trades_count":"1258"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T12:00:00.000000000Z","price_open":"0.1595","price_close":"0.1695","price_high":"0.1695","price_low":"0.1595","vwap":"0.1655","volume":"1385996.01","candle_usd_volume":"220279.00001","candle_trades_count":"1259"},{"market":"bybit-trx-usdt-spot","time":"2024-10-16T13:00:00.000000000Z","price_open":"0.15408","price_close":"0.15491","price_high":"0.15414","price_low":"0.15488","vwap":"0.1540188641784494","volume":"714288.38","candle_usd_volume":"114021.61717095928122785887028239535379449553","candle_trades_count":"645"},{"market":"bybit-trx-usdt-spot","time":"2024-10-17T11:00:00.000000000Z","price_open":"0.15508","price_close":"0.15591","price_high":"0.15514","price_low":"0.15588","vwap":"0.1550188641784494","volume":"715288.38","candle_usd_volume":"115021.61717095928122785887028239535379449553","candle_trades_count":"675"},{"market":"bybit-trx-usdt-spot","time":"2024-10-17T12:00:00.000000000Z","price_open":"0.15608","price_close":"0.15691","price_high":"0.15614","price_low":"0.15688","vwap":"0.1560188641784494","volume":"716288.38","candle_usd_volume":"116021.61717095928122785887028239535379449553","candle_trades_count":"676"},{"market":"bybit-trx-usdt-spot","time":"2024-10-17T13:00:00.000000000Z","price_open":"0.16608","price_close":"0.16691","price_high":"0.16614","price_low":"0.16688","vwap":"0.1660188641784494","volume":"766288.38","candle_usd_volume":"116026.61717095928122785887028239535379449553","candle_trades_count":"679"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T10:00:00.000000000Z","price_open":"0.1795","price_close":"0.1795","price_high":"0.1795","price_low":"0.1795","vwap":"0.1755","volume":"1783996.01","candle_usd_volume":"220277.00001","candle_trades_count":"1753"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:00:00.000000000Z","price_open":"0.15908","price_close":"0.1589","price_high":"0.15914","price_low":"0.15884","vwap":"0.1589882063083388","volume":"1385996.91","candle_usd_volume":"220279.73634820367613065647384011961914315841","candle_trades_count":"1258"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T12:00:00.000000000Z","price_open":"0.1595","price_close":"0.1695","price_high":"0.1695","price_low":"0.1595","vwap":"0.1655","volume":"1385996.01","candle_usd_volume":"220279.00001","candle_trades_count":"1259"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T13:00:00.000000000Z","price_open":"0.1395","price_close":"0.1395","price_high":"0.1395","price_low":"0.1395","vwap":"0.1355","volume":"1383996.01","candle_usd_volume":"220273.00001","candle_trades_count":"1253"},{"market":"bybit-trx-usdt-spot","time":"2024-10-19T10:00:00.000000000Z","price_open":"0.1295","price_close":"0.1295","price_high":"0.1295","price_low":"0.1295","vwap":"0.1255","volume":"1284996.01","candle_usd_volume":"220272.00001","candle_trades_count":"1252"},{"market":"bybit-trx-usdt-spot","time":"2024-10-19T11:00:00.000000000Z","price_open":"0.1695","price_close":"0.1695","price_high":"0.1695","price_low":"0.1695","vwap":"0.1655","volume":"1684996.01","candle_usd_volume":"220276.00001","candle_trades_count":"1256"},{"market":"bybit-trx-usdt-spot","time":"2024-10-19T12:00:00.000000000Z","price_open":"0.1795","price_close":"0.1795","price_high":"0.1795","price_low":"0.1795","vwap":"0.1755","volume":"1784996.01","candle_usd_volume":"220277.00001","candle_trades_count":"1257"},{"market":"bybit-trx-usdt-spot","time":"2024-10-20T10:00:00.000000000Z","price_open":"0.2295","price_close":"0.2295","price_high":"0.2295","price_low":"0.2295","vwap":"0.2255","volume":"2284996.01","candle_usd_volume":"420272.00001","candle_trades_count":"2252"}],"next_page_token":"YnliaXQtdHJ4LXVzZHQtc3BvdA.MjAyNC0xMC0yMFQxMDowMDowMFo","next_page_url":"http://127.0.0.1:8080/v4/timeseries/market-candles?markets=bybit-*&frequency=1h&paging_from=start&page_size=15&api_key=x1&next_page_token=YnliaXQtdHJ4LXVzZHQtc3BvdA.MjAyNC0xMC0yMFQxMDowMDowMFo"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-*&frequency=1h&paging_from=start&page_size=15&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed spot and future candles with frequency of 1h and paging from end and page size = 15`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-TRXUSDT-future","time":"2024-10-18T12:00:00.000000000Z","price_open":"0.1595","price_close":"0.1695","price_high":"0.1695","price_low":"0.1595","vwap":"0.1655","volume":"1385996.01","candle_usd_volume":"220279.00001","candle_trades_count":"1259"},{"market":"bybit-trx-usdt-spot","time":"2024-10-16T13:00:00.000000000Z","price_open":"0.15408","price_close":"0.15491","price_high":"0.15414","price_low":"0.15488","vwap":"0.1540188641784494","volume":"714288.38","candle_usd_volume":"114021.61717095928122785887028239535379449553","candle_trades_count":"645"},{"market":"bybit-trx-usdt-spot","time":"2024-10-17T11:00:00.000000000Z","price_open":"0.15508","price_close":"0.15591","price_high":"0.15514","price_low":"0.15588","vwap":"0.1550188641784494","volume":"715288.38","candle_usd_volume":"115021.61717095928122785887028239535379449553","candle_trades_count":"675"},{"market":"bybit-trx-usdt-spot","time":"2024-10-17T12:00:00.000000000Z","price_open":"0.15608","price_close":"0.15691","price_high":"0.15614","price_low":"0.15688","vwap":"0.1560188641784494","volume":"716288.38","candle_usd_volume":"116021.61717095928122785887028239535379449553","candle_trades_count":"676"},{"market":"bybit-trx-usdt-spot","time":"2024-10-17T13:00:00.000000000Z","price_open":"0.16608","price_close":"0.16691","price_high":"0.16614","price_low":"0.16688","vwap":"0.1660188641784494","volume":"766288.38","candle_usd_volume":"116026.61717095928122785887028239535379449553","candle_trades_count":"679"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T10:00:00.000000000Z","price_open":"0.1795","price_close":"0.1795","price_high":"0.1795","price_low":"0.1795","vwap":"0.1755","volume":"1783996.01","candle_usd_volume":"220277.00001","candle_trades_count":"1753"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T11:00:00.000000000Z","price_open":"0.15908","price_close":"0.1589","price_high":"0.15914","price_low":"0.15884","vwap":"0.1589882063083388","volume":"1385996.91","candle_usd_volume":"220279.73634820367613065647384011961914315841","candle_trades_count":"1258"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T12:00:00.000000000Z","price_open":"0.1595","price_close":"0.1695","price_high":"0.1695","price_low":"0.1595","vwap":"0.1655","volume":"1385996.01","candle_usd_volume":"220279.00001","candle_trades_count":"1259"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T13:00:00.000000000Z","price_open":"0.1395","price_close":"0.1395","price_high":"0.1395","price_low":"0.1395","vwap":"0.1355","volume":"1383996.01","candle_usd_volume":"220273.00001","candle_trades_count":"1253"},{"market":"bybit-trx-usdt-spot","time":"2024-10-19T10:00:00.000000000Z","price_open":"0.1295","price_close":"0.1295","price_high":"0.1295","price_low":"0.1295","vwap":"0.1255","volume":"1284996.01","candle_usd_volume":"220272.00001","candle_trades_count":"1252"},{"market":"bybit-trx-usdt-spot","time":"2024-10-19T11:00:00.000000000Z","price_open":"0.1695","price_close":"0.1695","price_high":"0.1695","price_low":"0.1695","vwap":"0.1655","volume":"1684996.01","candle_usd_volume":"220276.00001","candle_trades_count":"1256"},{"market":"bybit-trx-usdt-spot","time":"2024-10-19T12:00:00.000000000Z","price_open":"0.1795","price_close":"0.1795","price_high":"0.1795","price_low":"0.1795","vwap":"0.1755","volume":"1784996.01","candle_usd_volume":"220277.00001","candle_trades_count":"1257"},{"market":"bybit-trx-usdt-spot","time":"2024-10-20T10:00:00.000000000Z","price_open":"0.2295","price_close":"0.2295","price_high":"0.2295","price_low":"0.2295","vwap":"0.2255","volume":"2284996.01","candle_usd_volume":"420272.00001","candle_trades_count":"2252"},{"market":"bybit-trx-usdt-spot","time":"2024-10-20T11:00:00.000000000Z","price_open":"0.2695","price_close":"0.2695","price_high":"0.2695","price_low":"0.2695","vwap":"0.2655","volume":"2684996.01","candle_usd_volume":"420276.00001","candle_trades_count":"2256"},{"market":"bybit-trx-usdt-spot","time":"2024-10-20T12:00:00.000000000Z","price_open":"0.2795","price_close":"0.2795","price_high":"0.2795","price_low":"0.2795","vwap":"0.2755","volume":"2784996.01","candle_usd_volume":"420277.00001","candle_trades_count":"2257"}],"next_page_token":"YnliaXQtVFJYVVNEVC1mdXR1cmU.MjAyNC0xMC0xOFQxMjowMDowMFo","next_page_url":"http://127.0.0.1:8080/v4/timeseries/market-candles?markets=bybit-*&frequency=1h&paging_from=end&page_size=15&api_key=x1&next_page_token=YnliaXQtVFJYVVNEVC1mdXR1cmU.MjAyNC0xMC0xOFQxMjowMDowMFo"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-*&frequency=1h&paging_from=end&page_size=15&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed spot and future candles with frequency of 4h and paging from start`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-TRXUSDT-future","time":"2024-10-18T08:00:00.000000000Z","price_open":"0.1591","price_close":"0.1589","price_high":"0.15921","price_low":"0.15884","vwap":"0.159053781252249","volume":"5612564.49","candle_usd_volume":"892352.18298533114606336454322945869720670277","candle_trades_count":"4706"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T12:00:00.000000000Z","price_open":"0.16222","price_close":"0.16333","price_high":"0.16333","price_low":"0.16222","vwap":"0.16666","volume":"5612564.01","candle_usd_volume":"892352.00001","candle_trades_count":"4707"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T08:00:00.000000000Z","price_open":"0.1591","price_close":"0.1589","price_high":"0.15921","price_low":"0.15884","vwap":"0.159053781252249","volume":"5612564.49","candle_usd_volume":"892352.18298533114606336454322945869720670277","candle_trades_count":"4706"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T12:00:00.000000000Z","price_open":"0.16222","price_close":"0.16333","price_high":"0.16333","price_low":"0.16222","vwap":"0.16666","volume":"5612564.01","candle_usd_volume":"892352.00001","candle_trades_count":"4707"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-*&frequency=4h&paging_from=start&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed spot and future candles with frequency of 4h and paging from start and page size = 3`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-TRXUSDT-future","time":"2024-10-18T08:00:00.000000000Z","price_open":"0.1591","price_close":"0.1589","price_high":"0.15921","price_low":"0.15884","vwap":"0.159053781252249","volume":"5612564.49","candle_usd_volume":"892352.18298533114606336454322945869720670277","candle_trades_count":"4706"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T12:00:00.000000000Z","price_open":"0.16222","price_close":"0.16333","price_high":"0.16333","price_low":"0.16222","vwap":"0.16666","volume":"5612564.01","candle_usd_volume":"892352.00001","candle_trades_count":"4707"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T08:00:00.000000000Z","price_open":"0.1591","price_close":"0.1589","price_high":"0.15921","price_low":"0.15884","vwap":"0.159053781252249","volume":"5612564.49","candle_usd_volume":"892352.18298533114606336454322945869720670277","candle_trades_count":"4706"}],"next_page_token":"YnliaXQtdHJ4LXVzZHQtc3BvdA.MjAyNC0xMC0xOFQwODowMDowMFo","next_page_url":"http://127.0.0.1:8080/v4/timeseries/market-candles?markets=bybit-*&frequency=4h&paging_from=start&page_size=3&api_key=x1&next_page_token=YnliaXQtdHJ4LXVzZHQtc3BvdA.MjAyNC0xMC0xOFQwODowMDowMFo"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-*&frequency=4h&paging_from=start&page_size=3&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed spot and future candles with frequency of 4h and paging from end and page size = 3`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-TRXUSDT-future","time":"2024-10-18T12:00:00.000000000Z","price_open":"0.16222","price_close":"0.16333","price_high":"0.16333","price_low":"0.16222","vwap":"0.16666","volume":"5612564.01","candle_usd_volume":"892352.00001","candle_trades_count":"4707"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T08:00:00.000000000Z","price_open":"0.1591","price_close":"0.1589","price_high":"0.15921","price_low":"0.15884","vwap":"0.159053781252249","volume":"5612564.49","candle_usd_volume":"892352.18298533114606336454322945869720670277","candle_trades_count":"4706"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T12:00:00.000000000Z","price_open":"0.16222","price_close":"0.16333","price_high":"0.16333","price_low":"0.16222","vwap":"0.16666","volume":"5612564.01","candle_usd_volume":"892352.00001","candle_trades_count":"4707"}],"next_page_token":"YnliaXQtVFJYVVNEVC1mdXR1cmU.MjAyNC0xMC0xOFQxMjowMDowMFo","next_page_url":"http://127.0.0.1:8080/v4/timeseries/market-candles?markets=bybit-*&frequency=4h&paging_from=end&page_size=3&api_key=x1&next_page_token=YnliaXQtVFJYVVNEVC1mdXR1cmU.MjAyNC0xMC0xOFQxMjowMDowMFo"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-*&frequency=4h&paging_from=end&page_size=3&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed spot and future candles with frequency of 1d and paging from start`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-TRXUSDT-future","time":"2024-10-17T00:00:00.000000000Z","price_open":"0.1601","price_close":"0.15938","price_high":"0.16056","price_low":"0.1591","vwap":"0.159938246016863","volume":"38099958.37","candle_usd_volume":"6091654.72791880882081629919919093990618258893","candle_trades_count":"27656"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T00:00:00.000000000Z","price_open":"0.161","price_close":"0.162","price_high":"0.162","price_low":"0.161","vwap":"0.1615","volume":"38099958.01","candle_usd_volume":"6091654.00001","candle_trades_count":"27657"},{"market":"bybit-trx-usdt-spot","time":"2024-10-17T00:00:00.000000000Z","price_open":"0.1601","price_close":"0.15938","price_high":"0.16056","price_low":"0.1591","vwap":"0.159938246016863","volume":"38099958.37","candle_usd_volume":"6091654.72791880882081629919919093990618258893","candle_trades_count":"27656"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T00:00:00.000000000Z","price_open":"0.161","price_close":"0.162","price_high":"0.162","price_low":"0.161","vwap":"0.1615","volume":"38099958.01","candle_usd_volume":"6091654.00001","candle_trades_count":"27657"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-*&frequency=1d&paging_from=start&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed spot and future candles with frequency of 1d and paging from start and page size = 3`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-TRXUSDT-future","time":"2024-10-17T00:00:00.000000000Z","price_open":"0.1601","price_close":"0.15938","price_high":"0.16056","price_low":"0.1591","vwap":"0.159938246016863","volume":"38099958.37","candle_usd_volume":"6091654.72791880882081629919919093990618258893","candle_trades_count":"27656"},{"market":"bybit-TRXUSDT-future","time":"2024-10-18T00:00:00.000000000Z","price_open":"0.161","price_close":"0.162","price_high":"0.162","price_low":"0.161","vwap":"0.1615","volume":"38099958.01","candle_usd_volume":"6091654.00001","candle_trades_count":"27657"},{"market":"bybit-trx-usdt-spot","time":"2024-10-17T00:00:00.000000000Z","price_open":"0.1601","price_close":"0.15938","price_high":"0.16056","price_low":"0.1591","vwap":"0.159938246016863","volume":"38099958.37","candle_usd_volume":"6091654.72791880882081629919919093990618258893","candle_trades_count":"27656"}],"next_page_token":"YnliaXQtdHJ4LXVzZHQtc3BvdA.MjAyNC0xMC0xN1QwMDowMDowMFo","next_page_url":"http://127.0.0.1:8080/v4/timeseries/market-candles?markets=bybit-*&frequency=1d&paging_from=start&page_size=3&api_key=x1&next_page_token=YnliaXQtdHJ4LXVzZHQtc3BvdA.MjAyNC0xMC0xN1QwMDowMDowMFo"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-*&frequency=1d&paging_from=start&page_size=3&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `instant and delayed spot and future candles with frequency of 1d and paging from end and page size = 3`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-TRXUSDT-future","time":"2024-10-18T00:00:00.000000000Z","price_open":"0.161","price_close":"0.162","price_high":"0.162","price_low":"0.161","vwap":"0.1615","volume":"38099958.01","candle_usd_volume":"6091654.00001","candle_trades_count":"27657"},{"market":"bybit-trx-usdt-spot","time":"2024-10-17T00:00:00.000000000Z","price_open":"0.1601","price_close":"0.15938","price_high":"0.16056","price_low":"0.1591","vwap":"0.159938246016863","volume":"38099958.37","candle_usd_volume":"6091654.72791880882081629919919093990618258893","candle_trades_count":"27656"},{"market":"bybit-trx-usdt-spot","time":"2024-10-18T00:00:00.000000000Z","price_open":"0.161","price_close":"0.162","price_high":"0.162","price_low":"0.161","vwap":"0.1615","volume":"38099958.01","candle_usd_volume":"6091654.00001","candle_trades_count":"27657"}],"next_page_token":"YnliaXQtVFJYVVNEVC1mdXR1cmU.MjAyNC0xMC0xOFQwMDowMDowMFo","next_page_url":"http://127.0.0.1:8080/v4/timeseries/market-candles?markets=bybit-*&frequency=1d&paging_from=end&page_size=3&api_key=x1&next_page_token=YnliaXQtVFJYVVNEVC1mdXR1cmU.MjAyNC0xMC0xOFQwMDowMDowMFo"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=bybit-*&frequency=1d&paging_from=end&page_size=3&api_key=$TEST_API_KEY",
        )
    }

    // DEFI

    @Test
    fun `only delayed defi candles with frequency of 1d-0100 and paging from start`() {
        val expectedResponse =
            """{"data":[{"market":"uniswap_v3_eth-agg-usdc-weth-spot","time":"2024-10-17T01:00:00.000000000Z","price_open":"0.100589440482483","price_close":"0.000617592531233365","price_high":"0.10065099054524323001","price_low":"0.00058001527115586295","vwap":"0.0006129694246965269819049","volume":"1687869389.498295","candle_usd_volume":"1687843395.4819390874388976232267","candle_trades_count":"143840"}]}"""

        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-candles?markets=uniswap_v3_eth-agg-usdc-weth-spot&frequency=1d-01:00&paging_from=start&api_key=$TEST_API_KEY",
        )
    }

    private fun jsonResponseToCsvResponse(jsonResponse: String): String = jsonResponseToCsvResponse<MarketCandlesResponse>(jsonResponse) { it.data }
}
