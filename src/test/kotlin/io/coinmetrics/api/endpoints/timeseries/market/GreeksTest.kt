package io.coinmetrics.api.endpoints.timeseries.market

import io.coinmetrics.api.helper.EMPTY_CSV_RESPONSE
import io.coinmetrics.api.helper.TEST_API_KEY
import io.coinmetrics.api.models.MarketGreeksResponse
import io.coinmetrics.testing.autoexpect.AutoExpect
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.api.extension.ExtendWith
import org.testcontainers.junit.jupiter.Testcontainers

@ExtendWith(AutoExpect::class)
@Testcontainers
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class GreeksTest : OptionTickerBaseTest() {
    @Test
    fun `should return all items ordered by ticker_time when there is no start time, end time, and option metadata`() {
        test(
            "/v4/timeseries/market-greeks?markets=bybit-ETH-*-option&api_key=$TEST_API_KEY",
            200,
            """{"data":[{"market":"bybit-ETH-25MAR22-1202-S-option","time":"2021-09-01T13:24:00.000000000Z","database_time":"2021-09-01T13:24:41.266168000Z","vega":"2.47772","theta":"-0.70691","rho":"-1.31245","delta":"-0.04137","gamma":"0.00003","exchange_time":"2021-09-01T13:24:39.774000000Z"},{"market":"bybit-ETH-25MAR22-1202-S-option","time":"2021-09-01T13:25:00.000000000Z","database_time":"2021-09-01T13:25:41.266168000Z","vega":"0.56264","theta":"-15.07377","rho":"0.13455","delta":"0.85444","gamma":"0.00098","exchange_time":"2021-09-01T13:25:40.116000000Z"},{"market":"bybit-ETH-25MAR22-1202-S-option","time":"2021-09-01T13:26:00.000000000Z","database_time":"2021-09-01T13:25:41.266168000Z","vega":"0.56264","theta":"-15.07377","rho":"0.13455","delta":"0.85444","gamma":"0.00098","exchange_time":"2021-09-01T13:26:40.116000000Z"},{"market":"bybit-ETH-25MAR22-1202-S-option","time":"2022-09-01T13:26:00.000000000Z","database_time":"2021-09-01T13:25:41.266168000Z","vega":"0.56264","theta":"-15.07377","rho":"0.13455","delta":"0.85444","gamma":"0.00098","exchange_time":"2022-09-01T13:26:40.116000000Z"},{"market":"bybit-ETH-25MAR22-1202-S-option","time":"2023-05-02T13:26:00.000000000Z","database_time":"2023-05-02T13:25:41.266168000Z","vega":"0.56264","theta":"-15.07377","rho":"0.13455","delta":"0.85444","gamma":"0.00098","exchange_time":"2023-05-02T13:26:40.116000000Z"},{"market":"bybit-ETH-30APR23-2010-C-option","time":"2023-05-02T13:26:00.000000000Z","database_time":"2023-05-02T13:25:41.266168000Z","vega":"0.56264","theta":"-15.07377","rho":"0.13455","delta":"0.85444","gamma":"0.00098","exchange_time":"2023-05-02T13:26:40.116000000Z"},{"market":"bybit-ETH-30APR23-2010-P-option","time":"2023-05-02T13:26:00.000000000Z","database_time":"2023-05-02T13:25:41.266168000Z","vega":"0.56264","theta":"-15.07377","rho":"0.13455","delta":"0.85444","gamma":"0.00098","exchange_time":"2023-05-02T13:26:40.116000000Z"},{"market":"bybit-ETH-30MAY23-2020-C-option","time":"2023-05-02T13:26:00.000000000Z","database_time":"2023-05-02T13:25:41.266168000Z","vega":"0.56264","theta":"-15.07377","rho":"0.13455","delta":"0.85444","gamma":"0.00098","exchange_time":"2023-05-02T13:26:40.116000000Z"},{"market":"bybit-ETH-31MAY23-2020-P-option","time":"2023-05-02T13:26:00.000000000Z","database_time":"2023-05-02T13:25:41.266168000Z","vega":"0.56264","theta":"-15.07377","rho":"0.13455","delta":"0.85444","gamma":"0.00098","exchange_time":"2023-05-02T13:26:40.116000000Z"}]}""",
        )
    }

    @Test
    fun `should return all items ordered by ticker_time when there is no start time, end time, and option metadata in a csv format`() {
        val expectedResponse = """{"data":[{"market":"bybit-ETH-25MAR22-1202-S-option","time":"2021-09-01T13:24:00.000000000Z","database_time":"2021-09-01T13:24:41.266168000Z","vega":"2.47772","theta":"-0.70691","rho":"-1.31245","delta":"-0.04137","gamma":"0.00003","exchange_time":"2021-09-01T13:24:39.774000000Z"},{"market":"bybit-ETH-25MAR22-1202-S-option","time":"2021-09-01T13:25:00.000000000Z","database_time":"2021-09-01T13:25:41.266168000Z","vega":"0.56264","theta":"-15.07377","rho":"0.13455","delta":"0.85444","gamma":"0.00098","exchange_time":"2021-09-01T13:25:40.116000000Z"},{"market":"bybit-ETH-25MAR22-1202-S-option","time":"2021-09-01T13:26:00.000000000Z","database_time":"2021-09-01T13:25:41.266168000Z","vega":"0.56264","theta":"-15.07377","rho":"0.13455","delta":"0.85444","gamma":"0.00098","exchange_time":"2021-09-01T13:26:40.116000000Z"},{"market":"bybit-ETH-25MAR22-1202-S-option","time":"2022-09-01T13:26:00.000000000Z","database_time":"2021-09-01T13:25:41.266168000Z","vega":"0.56264","theta":"-15.07377","rho":"0.13455","delta":"0.85444","gamma":"0.00098","exchange_time":"2022-09-01T13:26:40.116000000Z"},{"market":"bybit-ETH-25MAR22-1202-S-option","time":"2023-05-02T13:26:00.000000000Z","database_time":"2023-05-02T13:25:41.266168000Z","vega":"0.56264","theta":"-15.07377","rho":"0.13455","delta":"0.85444","gamma":"0.00098","exchange_time":"2023-05-02T13:26:40.116000000Z"},{"market":"bybit-ETH-30APR23-2010-C-option","time":"2023-05-02T13:26:00.000000000Z","database_time":"2023-05-02T13:25:41.266168000Z","vega":"0.56264","theta":"-15.07377","rho":"0.13455","delta":"0.85444","gamma":"0.00098","exchange_time":"2023-05-02T13:26:40.116000000Z"},{"market":"bybit-ETH-30APR23-2010-P-option","time":"2023-05-02T13:26:00.000000000Z","database_time":"2023-05-02T13:25:41.266168000Z","vega":"0.56264","theta":"-15.07377","rho":"0.13455","delta":"0.85444","gamma":"0.00098","exchange_time":"2023-05-02T13:26:40.116000000Z"},{"market":"bybit-ETH-30MAY23-2020-C-option","time":"2023-05-02T13:26:00.000000000Z","database_time":"2023-05-02T13:25:41.266168000Z","vega":"0.56264","theta":"-15.07377","rho":"0.13455","delta":"0.85444","gamma":"0.00098","exchange_time":"2023-05-02T13:26:40.116000000Z"},{"market":"bybit-ETH-31MAY23-2020-P-option","time":"2023-05-02T13:26:00.000000000Z","database_time":"2023-05-02T13:25:41.266168000Z","vega":"0.56264","theta":"-15.07377","rho":"0.13455","delta":"0.85444","gamma":"0.00098","exchange_time":"2023-05-02T13:26:40.116000000Z"}]}"""
        test(
            pathAndQuery = "/v4/timeseries/market-greeks?markets=bybit-ETH-*-option&api_key=$TEST_API_KEY",
            expectedCode = 200,
            expectedResponse = expectedResponse,
        )

        val expectedResponseCsv =
            """
            market,time,database_time,vega,theta,rho,delta,gamma,exchange_time
            bybit-ETH-25MAR22-1202-S-option,2021-09-01T13:24:00.000000000Z,2021-09-01T13:24:41.266168000Z,2.47772,-0.70691,-1.31245,-0.04137,0.00003,2021-09-01T13:24:39.774000000Z
            bybit-ETH-25MAR22-1202-S-option,2021-09-01T13:25:00.000000000Z,2021-09-01T13:25:41.266168000Z,0.56264,-15.07377,0.13455,0.85444,0.00098,2021-09-01T13:25:40.116000000Z
            bybit-ETH-25MAR22-1202-S-option,2021-09-01T13:26:00.000000000Z,2021-09-01T13:25:41.266168000Z,0.56264,-15.07377,0.13455,0.85444,0.00098,2021-09-01T13:26:40.116000000Z
            bybit-ETH-25MAR22-1202-S-option,2022-09-01T13:26:00.000000000Z,2021-09-01T13:25:41.266168000Z,0.56264,-15.07377,0.13455,0.85444,0.00098,2022-09-01T13:26:40.116000000Z
            bybit-ETH-25MAR22-1202-S-option,2023-05-02T13:26:00.000000000Z,2023-05-02T13:25:41.266168000Z,0.56264,-15.07377,0.13455,0.85444,0.00098,2023-05-02T13:26:40.116000000Z
            bybit-ETH-30APR23-2010-C-option,2023-05-02T13:26:00.000000000Z,2023-05-02T13:25:41.266168000Z,0.56264,-15.07377,0.13455,0.85444,0.00098,2023-05-02T13:26:40.116000000Z
            bybit-ETH-30APR23-2010-P-option,2023-05-02T13:26:00.000000000Z,2023-05-02T13:25:41.266168000Z,0.56264,-15.07377,0.13455,0.85444,0.00098,2023-05-02T13:26:40.116000000Z
            bybit-ETH-30MAY23-2020-C-option,2023-05-02T13:26:00.000000000Z,2023-05-02T13:25:41.266168000Z,0.56264,-15.07377,0.13455,0.85444,0.00098,2023-05-02T13:26:40.116000000Z
            bybit-ETH-31MAY23-2020-P-option,2023-05-02T13:26:00.000000000Z,2023-05-02T13:25:41.266168000Z,0.56264,-15.07377,0.13455,0.85444,0.00098,2023-05-02T13:26:40.116000000Z

            """.trimIndent()
        assertResponseWithContentType(
            pathAndQuery = "/v4/timeseries/market-greeks?markets=bybit-ETH-*-option&format=csv&api_key=$TEST_API_KEY",
            expectedCode = 200,
            contentType = "text/csv",
            expectedResponse = expectedResponseCsv,
        )

        Assertions.assertEquals(jsonResponseToCsvResponse(expectedResponse), expectedResponseCsv)
    }

    @Test
    fun `should return items filtered by ticker_time and ordered by ticker_time when there is no option metadata`() {
        test(
            "/v4/timeseries/market-greeks?markets=bybit-ETH-*-option&api_key=$TEST_API_KEY&start_time=2021-09-01T13:24:00&end_time=2021-09-01T13:25:00",
            200,
            """{"data":[{"market":"bybit-ETH-25MAR22-1202-S-option","time":"2021-09-01T13:24:00.000000000Z","database_time":"2021-09-01T13:24:41.266168000Z","vega":"2.47772","theta":"-0.70691","rho":"-1.31245","delta":"-0.04137","gamma":"0.00003","exchange_time":"2021-09-01T13:24:39.774000000Z"},{"market":"bybit-ETH-25MAR22-1202-S-option","time":"2021-09-01T13:25:00.000000000Z","database_time":"2021-09-01T13:25:41.266168000Z","vega":"0.56264","theta":"-15.07377","rho":"0.13455","delta":"0.85444","gamma":"0.00098","exchange_time":"2021-09-01T13:25:40.116000000Z"}]}""",
        )
    }

    @Test
    fun `should return items filtered by ticker_time and option metadata and ordered by ticker_time`() {
        test(
            "/v4/timeseries/market-greeks?markets=bybit-ETH-*-option&api_key=$TEST_API_KEY&start_time=2023-05-01T13:24:00&end_time=2023-05-03T13:25:00",
            200,
            """{"data":[{"market":"bybit-ETH-25MAR22-1202-S-option","time":"2023-05-02T13:26:00.000000000Z","database_time":"2023-05-02T13:25:41.266168000Z","vega":"0.56264","theta":"-15.07377","rho":"0.13455","delta":"0.85444","gamma":"0.00098","exchange_time":"2023-05-02T13:26:40.116000000Z"},{"market":"bybit-ETH-30APR23-2010-C-option","time":"2023-05-02T13:26:00.000000000Z","database_time":"2023-05-02T13:25:41.266168000Z","vega":"0.56264","theta":"-15.07377","rho":"0.13455","delta":"0.85444","gamma":"0.00098","exchange_time":"2023-05-02T13:26:40.116000000Z"},{"market":"bybit-ETH-30APR23-2010-P-option","time":"2023-05-02T13:26:00.000000000Z","database_time":"2023-05-02T13:25:41.266168000Z","vega":"0.56264","theta":"-15.07377","rho":"0.13455","delta":"0.85444","gamma":"0.00098","exchange_time":"2023-05-02T13:26:40.116000000Z"},{"market":"bybit-ETH-30MAY23-2020-C-option","time":"2023-05-02T13:26:00.000000000Z","database_time":"2023-05-02T13:25:41.266168000Z","vega":"0.56264","theta":"-15.07377","rho":"0.13455","delta":"0.85444","gamma":"0.00098","exchange_time":"2023-05-02T13:26:40.116000000Z"},{"market":"bybit-ETH-31MAY23-2020-P-option","time":"2023-05-02T13:26:00.000000000Z","database_time":"2023-05-02T13:25:41.266168000Z","vega":"0.56264","theta":"-15.07377","rho":"0.13455","delta":"0.85444","gamma":"0.00098","exchange_time":"2023-05-02T13:26:40.116000000Z"}]}""",
        )
    }

    @Test
    fun `should return items filtered by ticker_time with start_time and option metadata and ordered by ticker_time`() {
        test(
            "/v4/timeseries/market-greeks?markets=bybit-ETH-*-option&api_key=$TEST_API_KEY&start_time=2023-05-01T13:24:00",
            200,
            """{"data":[{"market":"bybit-ETH-25MAR22-1202-S-option","time":"2023-05-02T13:26:00.000000000Z","database_time":"2023-05-02T13:25:41.266168000Z","vega":"0.56264","theta":"-15.07377","rho":"0.13455","delta":"0.85444","gamma":"0.00098","exchange_time":"2023-05-02T13:26:40.116000000Z"},{"market":"bybit-ETH-30APR23-2010-C-option","time":"2023-05-02T13:26:00.000000000Z","database_time":"2023-05-02T13:25:41.266168000Z","vega":"0.56264","theta":"-15.07377","rho":"0.13455","delta":"0.85444","gamma":"0.00098","exchange_time":"2023-05-02T13:26:40.116000000Z"},{"market":"bybit-ETH-30APR23-2010-P-option","time":"2023-05-02T13:26:00.000000000Z","database_time":"2023-05-02T13:25:41.266168000Z","vega":"0.56264","theta":"-15.07377","rho":"0.13455","delta":"0.85444","gamma":"0.00098","exchange_time":"2023-05-02T13:26:40.116000000Z"},{"market":"bybit-ETH-30MAY23-2020-C-option","time":"2023-05-02T13:26:00.000000000Z","database_time":"2023-05-02T13:25:41.266168000Z","vega":"0.56264","theta":"-15.07377","rho":"0.13455","delta":"0.85444","gamma":"0.00098","exchange_time":"2023-05-02T13:26:40.116000000Z"},{"market":"bybit-ETH-31MAY23-2020-P-option","time":"2023-05-02T13:26:00.000000000Z","database_time":"2023-05-02T13:25:41.266168000Z","vega":"0.56264","theta":"-15.07377","rho":"0.13455","delta":"0.85444","gamma":"0.00098","exchange_time":"2023-05-02T13:26:40.116000000Z"}]}""",
        )
    }

    @Test
    fun `should return items filtered by ticker_time with end_time and option metadata and ordered by ticker_time`() {
        test(
            "/v4/timeseries/market-greeks?markets=bybit-ETH-*-option&api_key=$TEST_API_KEY&end_time=2023-05-03T13:25:00",
            200,
            """{"data":[{"market":"bybit-ETH-25MAR22-1202-S-option","time":"2021-09-01T13:24:00.000000000Z","database_time":"2021-09-01T13:24:41.266168000Z","vega":"2.47772","theta":"-0.70691","rho":"-1.31245","delta":"-0.04137","gamma":"0.00003","exchange_time":"2021-09-01T13:24:39.774000000Z"},{"market":"bybit-ETH-25MAR22-1202-S-option","time":"2021-09-01T13:25:00.000000000Z","database_time":"2021-09-01T13:25:41.266168000Z","vega":"0.56264","theta":"-15.07377","rho":"0.13455","delta":"0.85444","gamma":"0.00098","exchange_time":"2021-09-01T13:25:40.116000000Z"},{"market":"bybit-ETH-25MAR22-1202-S-option","time":"2021-09-01T13:26:00.000000000Z","database_time":"2021-09-01T13:25:41.266168000Z","vega":"0.56264","theta":"-15.07377","rho":"0.13455","delta":"0.85444","gamma":"0.00098","exchange_time":"2021-09-01T13:26:40.116000000Z"},{"market":"bybit-ETH-25MAR22-1202-S-option","time":"2022-09-01T13:26:00.000000000Z","database_time":"2021-09-01T13:25:41.266168000Z","vega":"0.56264","theta":"-15.07377","rho":"0.13455","delta":"0.85444","gamma":"0.00098","exchange_time":"2022-09-01T13:26:40.116000000Z"},{"market":"bybit-ETH-25MAR22-1202-S-option","time":"2023-05-02T13:26:00.000000000Z","database_time":"2023-05-02T13:25:41.266168000Z","vega":"0.56264","theta":"-15.07377","rho":"0.13455","delta":"0.85444","gamma":"0.00098","exchange_time":"2023-05-02T13:26:40.116000000Z"},{"market":"bybit-ETH-30APR23-2010-C-option","time":"2023-05-02T13:26:00.000000000Z","database_time":"2023-05-02T13:25:41.266168000Z","vega":"0.56264","theta":"-15.07377","rho":"0.13455","delta":"0.85444","gamma":"0.00098","exchange_time":"2023-05-02T13:26:40.116000000Z"},{"market":"bybit-ETH-30APR23-2010-P-option","time":"2023-05-02T13:26:00.000000000Z","database_time":"2023-05-02T13:25:41.266168000Z","vega":"0.56264","theta":"-15.07377","rho":"0.13455","delta":"0.85444","gamma":"0.00098","exchange_time":"2023-05-02T13:26:40.116000000Z"},{"market":"bybit-ETH-30MAY23-2020-C-option","time":"2023-05-02T13:26:00.000000000Z","database_time":"2023-05-02T13:25:41.266168000Z","vega":"0.56264","theta":"-15.07377","rho":"0.13455","delta":"0.85444","gamma":"0.00098","exchange_time":"2023-05-02T13:26:40.116000000Z"},{"market":"bybit-ETH-31MAY23-2020-P-option","time":"2023-05-02T13:26:00.000000000Z","database_time":"2023-05-02T13:25:41.266168000Z","vega":"0.56264","theta":"-15.07377","rho":"0.13455","delta":"0.85444","gamma":"0.00098","exchange_time":"2023-05-02T13:26:40.116000000Z"}]}""",
        )
    }

    @Test
    fun `should return 400 when trying to get implied volatility for spot market`() {
        test(
            "/v4/timeseries/market-greeks?markets=bittrex-btc-usd-spot&api_key=$TEST_API_KEY",
            400,
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'markets'. Only option markets are supported."}}""",
        )
    }

    @Test
    fun `should return 400 when trying to get implied volatility for futures market`() {
        test(
            "/v4/timeseries/market-greeks?markets=bitmex-XBTUSD-future&api_key=$TEST_API_KEY",
            400,
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'markets'. Only option markets are supported."}}""",
        )
    }

    @Test
    fun `should return 400 when start_time is after end_time`() {
        test(
            "/v4/timeseries/market-greeks?markets=bybit-BTC-9APR21-50000-P-option&api_key=$TEST_API_KEY&start_time=2023-05-03T13:25:00&end_time=2023-05-01T13:24:00",
            400,
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'start_time'. Start time '2023-05-03T13:25:00' is later than the end time '2023-05-01T13:24:00'."}}""",
        )
    }

    @Test
    fun `should return 400 error when market is not supported`() {
        test(
            "/v4/timeseries/market-greeks?markets=bybit-ETH-2APR21-1966-C-option&api_key=$TEST_API_KEY",
            400,
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'markets'. Market 'bybit-ETH-2APR21-1966-C-option' is not supported."}}""",
        )
    }

    @Test
    fun `should return empty response when no option ticker data is available for market`() {
        test(
            "/v4/timeseries/market-greeks?markets=bybit-BTC-9APR21-50000-C-option&api_key=$TEST_API_KEY",
            200,
            """{"data":[]}""",
        )
    }

    @Test
    fun `should return empty response when no option ticker data is available for market in a csv response`() {
        assertResponseWithContentType(
            pathAndQuery = "/v4/timeseries/market-greeks?markets=bybit-BTC-9APR21-50000-C-option&format=csv&api_key=$TEST_API_KEY",
            expectedCode = 200,
            contentType = "text/csv",
            expectedResponse = EMPTY_CSV_RESPONSE,
        )
    }

    @Test
    fun `should return empty response when requesting unsupported pattern market`() {
        getResponse("/v4/timeseries/market-greeks?markets=zaif-*-option&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should return empty response when requesting unsupported pattern markets`() {
        getResponse("/v4/timeseries/market-greeks?markets=zaif-*-option,simex-CGSEUR-*&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should return 1m items`() {
        test(
            "/v4/timeseries/market-greeks?markets=deribit-XRP-*-option&granularity=1m&api_key=$TEST_API_KEY&start_time=2023-09-01T00:00:00&end_time=2023-09-05T00:00:00",
            200,
            """{"data":[{"market":"deribit-XRP-31SEP23-2023-P-option","time":"2023-09-01T00:00:00.000000000Z","database_time":"2023-09-01T00:00:00.000000000Z","vega":"0.56264","theta":"-15.07377","rho":"0.13455","delta":"0.85444","gamma":"0.00098","exchange_time":"2023-09-01T00:00:00.000000000Z"},{"market":"deribit-XRP-31SEP23-2023-P-option","time":"2023-09-01T23:59:00.000000000Z","database_time":"2023-09-01T23:59:00.000000000Z","vega":"0.56264","theta":"-15.07377","rho":"0.13455","delta":"0.85444","gamma":"0.00098","exchange_time":"2023-09-01T23:59:00.000000000Z"},{"market":"deribit-XRP-31SEP23-2023-P-option","time":"2023-09-02T00:01:00.000000000Z","database_time":"2023-09-02T00:01:00.000000000Z","vega":"0.56264","theta":"-15.07377","rho":"0.13455","delta":"0.85444","gamma":"0.00098","exchange_time":"2023-09-02T00:01:00.000000000Z"},{"market":"deribit-XRP-31SEP23-2023-P-option","time":"2023-09-02T01:00:00.000000000Z","database_time":"2023-09-02T01:00:00.000000000Z","vega":"0.56264","theta":"-15.07377","rho":"0.13455","delta":"0.85444","gamma":"0.00098","exchange_time":"2023-09-02T01:00:00.000000000Z"},{"market":"deribit-XRP-31SEP23-2023-P-option","time":"2023-09-02T02:00:00.000000000Z","database_time":"2023-09-02T02:00:00.000000000Z","vega":"0.56264","theta":"-15.07377","rho":"0.13455","delta":"0.85444","gamma":"0.00098","exchange_time":"2023-09-02T02:00:00.000000000Z"},{"market":"deribit-XRP-31SEP23-2023-P-option","time":"2023-09-03T00:00:00.000000000Z","database_time":"2023-09-03T00:00:00.000000000Z","vega":"0.56264","theta":"-15.07377","rho":"0.13455","delta":"0.85444","gamma":"0.00098","exchange_time":"2023-09-03T00:00:00.000000000Z"},{"market":"deribit-XRP-31SEP23-2023-P-option","time":"2023-09-04T00:00:00.000000000Z","database_time":"2023-09-04T00:00:00.000000000Z","vega":"0.56264","theta":"-15.07377","rho":"0.13455","delta":"0.85444","gamma":"0.00098","exchange_time":"2023-09-04T00:00:00.000000000Z"}]}""",
        )
    }

    @Test
    fun `should return 1h items`() {
        test(
            "/v4/timeseries/market-greeks?markets=deribit-XRP-*-option&granularity=1h&api_key=$TEST_API_KEY&start_time=2023-09-01T00:00:00&end_time=2023-09-05T00:00:00",
            200,
            """{"data":[{"market":"deribit-XRP-31SEP23-2023-P-option","time":"2023-09-01T00:00:00.000000000Z","database_time":"2023-09-01T00:00:00.000000000Z","vega":"0.56264","theta":"-15.07377","rho":"0.13455","delta":"0.85444","gamma":"0.00098","exchange_time":"2023-09-01T00:00:00.000000000Z"},{"market":"deribit-XRP-31SEP23-2023-P-option","time":"2023-09-01T23:59:00.000000000Z","database_time":"2023-09-01T23:59:00.000000000Z","vega":"0.56264","theta":"-15.07377","rho":"0.13455","delta":"0.85444","gamma":"0.00098","exchange_time":"2023-09-01T23:59:00.000000000Z"},{"market":"deribit-XRP-31SEP23-2023-P-option","time":"2023-09-02T01:00:00.000000000Z","database_time":"2023-09-02T01:00:00.000000000Z","vega":"0.56264","theta":"-15.07377","rho":"0.13455","delta":"0.85444","gamma":"0.00098","exchange_time":"2023-09-02T01:00:00.000000000Z"},{"market":"deribit-XRP-31SEP23-2023-P-option","time":"2023-09-02T02:00:00.000000000Z","database_time":"2023-09-02T02:00:00.000000000Z","vega":"0.56264","theta":"-15.07377","rho":"0.13455","delta":"0.85444","gamma":"0.00098","exchange_time":"2023-09-02T02:00:00.000000000Z"},{"market":"deribit-XRP-31SEP23-2023-P-option","time":"2023-09-03T00:00:00.000000000Z","database_time":"2023-09-03T00:00:00.000000000Z","vega":"0.56264","theta":"-15.07377","rho":"0.13455","delta":"0.85444","gamma":"0.00098","exchange_time":"2023-09-03T00:00:00.000000000Z"},{"market":"deribit-XRP-31SEP23-2023-P-option","time":"2023-09-04T00:00:00.000000000Z","database_time":"2023-09-04T00:00:00.000000000Z","vega":"0.56264","theta":"-15.07377","rho":"0.13455","delta":"0.85444","gamma":"0.00098","exchange_time":"2023-09-04T00:00:00.000000000Z"}]}""",
        )
    }

    @Test
    fun `should return 1d items`() {
        test(
            "/v4/timeseries/market-greeks?markets=deribit-XRP-*-option&granularity=1d&api_key=$TEST_API_KEY&start_time=2023-09-01T00:00:00&end_time=2023-09-05T00:00:00",
            200,
            """{"data":[{"market":"deribit-XRP-31SEP23-2023-P-option","time":"2023-09-01T00:00:00.000000000Z","database_time":"2023-09-01T00:00:00.000000000Z","vega":"0.56264","theta":"-15.07377","rho":"0.13455","delta":"0.85444","gamma":"0.00098","exchange_time":"2023-09-01T00:00:00.000000000Z"},{"market":"deribit-XRP-31SEP23-2023-P-option","time":"2023-09-01T23:59:00.000000000Z","database_time":"2023-09-01T23:59:00.000000000Z","vega":"0.56264","theta":"-15.07377","rho":"0.13455","delta":"0.85444","gamma":"0.00098","exchange_time":"2023-09-01T23:59:00.000000000Z"},{"market":"deribit-XRP-31SEP23-2023-P-option","time":"2023-09-03T00:00:00.000000000Z","database_time":"2023-09-03T00:00:00.000000000Z","vega":"0.56264","theta":"-15.07377","rho":"0.13455","delta":"0.85444","gamma":"0.00098","exchange_time":"2023-09-03T00:00:00.000000000Z"},{"market":"deribit-XRP-31SEP23-2023-P-option","time":"2023-09-04T00:00:00.000000000Z","database_time":"2023-09-04T00:00:00.000000000Z","vega":"0.56264","theta":"-15.07377","rho":"0.13455","delta":"0.85444","gamma":"0.00098","exchange_time":"2023-09-04T00:00:00.000000000Z"}]}""",
        )
    }

    @Test
    fun `should return 1h items with a different timezone`() {
        test(
            "/v4/timeseries/market-greeks?markets=deribit-XRP-*-option&granularity=1h&api_key=$TEST_API_KEY&start_time=2023-09-01T03:00:00&end_time=2023-09-02T04:00:00&timezone=GMT%2b03:00",
            200,
            """{"data":[{"market":"deribit-XRP-31SEP23-2023-P-option","time":"2023-09-01T00:00:00.000000000Z","database_time":"2023-09-01T00:00:00.000000000Z","vega":"0.56264","theta":"-15.07377","rho":"0.13455","delta":"0.85444","gamma":"0.00098","exchange_time":"2023-09-01T00:00:00.000000000Z"},{"market":"deribit-XRP-31SEP23-2023-P-option","time":"2023-09-01T23:59:00.000000000Z","database_time":"2023-09-01T23:59:00.000000000Z","vega":"0.56264","theta":"-15.07377","rho":"0.13455","delta":"0.85444","gamma":"0.00098","exchange_time":"2023-09-01T23:59:00.000000000Z"},{"market":"deribit-XRP-31SEP23-2023-P-option","time":"2023-09-02T01:00:00.000000000Z","database_time":"2023-09-02T01:00:00.000000000Z","vega":"0.56264","theta":"-15.07377","rho":"0.13455","delta":"0.85444","gamma":"0.00098","exchange_time":"2023-09-02T01:00:00.000000000Z"}]}""",
        )
    }

    @Test
    fun `should return 400 when multiple granularities requested`() {
        test(
            "/v4/timeseries/market-greeks?markets=deribit-XRP-*-option&granularity=1m,1h,1d&api_key=$TEST_API_KEY&start_time=2023-09-01T00:00:00&end_time=2023-09-05T00:00:00",
            400,
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'granularity'. Value '1m,1h,1d' is not supported. Supported values are 'raw', '1m', '1h', '1d'."}}""",
        )
    }

    @Test
    fun `should return 400 when invalid granularity requested`() {
        test(
            "/v4/timeseries/market-greeks?markets=deribit-XRP-*-option&granularity=1s&api_key=$TEST_API_KEY&start_time=2023-09-01T00:00:00&end_time=2023-09-05T00:00:00",
            400,
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'granularity'. Value '1s' is not supported. Supported values are 'raw', '1m', '1h', '1d'."}}""",
        )
    }

    private fun jsonResponseToCsvResponse(jsonResponse: String): String = jsonResponseToCsvResponse<MarketGreeksResponse>(jsonResponse) { it.data }
}
