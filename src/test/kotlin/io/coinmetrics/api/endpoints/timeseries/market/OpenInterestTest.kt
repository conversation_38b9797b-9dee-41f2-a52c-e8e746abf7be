package io.coinmetrics.api.endpoints.timeseries.market

import io.coinmetrics.api.helper.BaseTest
import io.coinmetrics.api.helper.EMPTY_CSV_RESPONSE
import io.coinmetrics.api.helper.TEST_API_KEY
import io.coinmetrics.api.models.MarketOpenInterestResponse
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class OpenInterestTest : BaseTest() {
    @Test
    fun `test openinterest error responses`() {
        listOf(
            "bittrex-invalid-asset-future",
            "nonexistingexhange-btc-usd-future",
            "nonexistingexhange-btcusd-future",
        ).forEach { marketId ->
            assertResponse(
                400,
                """{"error":{"type":"bad_parameter","message":"Bad parameter 'markets'. Market '$marketId' is not supported."}}""",
                "/v4/timeseries/market-openinterest?markets=$marketId&api_key=$TEST_API_KEY",
            )
        }

        listOf(
            "bittrex-btc-usd-test-spot",
            "bitmex-xbtusd-spot",
            "bittrex-btc-usd",
        ).forEach { marketId ->
            assertResponse(
                400,
                """{"error":{"type":"bad_parameter","message":"Bad parameter 'markets'. Incorrect market '$marketId'."}}""",
                "/v4/timeseries/market-openinterest?markets=$marketId&api_key=$TEST_API_KEY",
            )
        }

        listOf(
            "201912-23",
            "20191223T00:00:05.000Z",
            "2019-12-23T00:00:05000Z",
        ).forEach { start_time ->
            assertResponse(
                400,
                """{"error":{"type":"bad_parameter","message":"Bad parameter 'start_time'. Incorrect time format '$start_time'. Supported formats are 'yyyy-MM-dd', 'yyyyMMdd', 'yyyy-MM-ddTHH:mm:ss', 'yyyy-MM-ddTHHmmss', 'yyyy-MM-ddTHH:mm:ss.SSS', 'yyyy-MM-ddTHHmmss.SSS', 'yyyy-MM-ddTHH:mm:ss.SSSSSS', 'yyyy-MM-ddTHHmmss.SSSSSS', 'yyyy-MM-ddTHH:mm:ss.SSSSSSSSS', 'yyyy-MM-ddTHHmmss.SSSSSSSSS'."}}""".trimMargin(),
                "/v4/timeseries/market-openinterest?markets=bitmex-XBTUSD-future&api_key=$TEST_API_KEY&start_time=$start_time",
            )
        }
    }

    @Test
    fun `test openinterest for spot`() {
        val expectedResponse =
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'markets'. Only future and option markets are supported."}}"""
        assertResponse(
            400,
            expectedResponse,
            "/v4/timeseries/market-openinterest?markets=bittrex-btc-usd-spot&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `test openinterest with no parameters futures`() {
        val expectedResponse =
            """{"data":[{"market":"bitmex-XBTUSD-future","time":"2020-07-27T17:27:47.509000000Z","contract_count":"21242","value_usd":"2.24124","database_time":"2020-07-27T17:27:48.510000000Z","exchange_time":"2020-07-27T17:27:47.509000000Z"},{"market":"bitmex-XBTUSD-future","time":"2020-07-28T17:28:47.509000000Z","contract_count":"1","value_usd":"2124.24","database_time":"2020-07-27T17:27:48.519811000Z","exchange_time":"2020-07-28T17:28:47.509000000Z"},{"market":"bitmex-XBTUSD-future","time":"2020-07-29T00:00:01.000000000Z","contract_count":"2342","value_usd":"124.246","database_time":"2020-07-27T17:27:48.512000000Z","exchange_time":"2020-07-29T00:00:01.000000000Z"},{"market":"bitmex-XBTUSD-future","time":"2020-07-29T05:55:01.999000000Z","contract_count":"2342","value_usd":"124.246","database_time":"2020-07-27T17:27:48.513000000Z","exchange_time":"2020-07-29T05:55:01.999000000Z"},{"market":"bitmex-XBTUSD-future","time":"2020-07-29T10:34:01.353000000Z","contract_count":"2342","value_usd":"124.246","database_time":"2020-07-27T17:27:48.514000000Z","exchange_time":"2020-07-29T10:34:01.353000000Z"},{"market":"bitmex-XBTUSD-future","time":"2020-07-29T17:29:47.509000000Z","contract_count":"12","value_usd":"5124.246","database_time":"2020-07-27T17:27:48.515000000Z","exchange_time":"2020-07-29T17:29:47.509000000Z"},{"market":"bitmex-XBTUSD-future","time":"2020-08-01T17:29:47.509000000Z","contract_count":"1224","value_usd":"1124.246","database_time":"2020-07-27T17:27:48.516000000Z","exchange_time":"2020-08-01T17:29:47.509000000Z"},{"market":"bitmex-XBTUSD-future","time":"2020-08-02T17:29:47.509000000Z","contract_count":"856","value_usd":"2124.246","database_time":"2020-07-27T17:27:48.517000000Z","exchange_time":"2020-08-02T17:29:47.509000000Z"},{"market":"bitmex-XBTUSD-future","time":"2020-08-03T17:29:47.509000000Z","contract_count":"934","value_usd":"3124.246","database_time":"2020-07-27T17:27:48.518000000Z","exchange_time":"2020-08-03T17:29:47.509000000Z"},{"market":"bitmex-XBTUSD-future","time":"2020-08-04T17:29:47.509000000Z","contract_count":"9353","value_usd":"4124.246","database_time":"2020-07-27T17:27:48.519000000Z","exchange_time":"2020-08-04T17:29:47.509000000Z"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-openinterest?markets=bitmex-XBTUSD-future&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `test openinterest with no parameters futures in a csv format`() {
        val expectedResponse =
            """{"data":[{"market":"bitmex-XBTUSD-future","time":"2020-07-27T17:27:47.509000000Z","contract_count":"21242","value_usd":"2.24124","database_time":"2020-07-27T17:27:48.510000000Z","exchange_time":"2020-07-27T17:27:47.509000000Z"},{"market":"bitmex-XBTUSD-future","time":"2020-07-28T17:28:47.509000000Z","contract_count":"1","value_usd":"2124.24","database_time":"2020-07-27T17:27:48.519811000Z","exchange_time":"2020-07-28T17:28:47.509000000Z"},{"market":"bitmex-XBTUSD-future","time":"2020-07-29T00:00:01.000000000Z","contract_count":"2342","value_usd":"124.246","database_time":"2020-07-27T17:27:48.512000000Z","exchange_time":"2020-07-29T00:00:01.000000000Z"},{"market":"bitmex-XBTUSD-future","time":"2020-07-29T05:55:01.999000000Z","contract_count":"2342","value_usd":"124.246","database_time":"2020-07-27T17:27:48.513000000Z","exchange_time":"2020-07-29T05:55:01.999000000Z"},{"market":"bitmex-XBTUSD-future","time":"2020-07-29T10:34:01.353000000Z","contract_count":"2342","value_usd":"124.246","database_time":"2020-07-27T17:27:48.514000000Z","exchange_time":"2020-07-29T10:34:01.353000000Z"},{"market":"bitmex-XBTUSD-future","time":"2020-07-29T17:29:47.509000000Z","contract_count":"12","value_usd":"5124.246","database_time":"2020-07-27T17:27:48.515000000Z","exchange_time":"2020-07-29T17:29:47.509000000Z"},{"market":"bitmex-XBTUSD-future","time":"2020-08-01T17:29:47.509000000Z","contract_count":"1224","value_usd":"1124.246","database_time":"2020-07-27T17:27:48.516000000Z","exchange_time":"2020-08-01T17:29:47.509000000Z"},{"market":"bitmex-XBTUSD-future","time":"2020-08-02T17:29:47.509000000Z","contract_count":"856","value_usd":"2124.246","database_time":"2020-07-27T17:27:48.517000000Z","exchange_time":"2020-08-02T17:29:47.509000000Z"},{"market":"bitmex-XBTUSD-future","time":"2020-08-03T17:29:47.509000000Z","contract_count":"934","value_usd":"3124.246","database_time":"2020-07-27T17:27:48.518000000Z","exchange_time":"2020-08-03T17:29:47.509000000Z"},{"market":"bitmex-XBTUSD-future","time":"2020-08-04T17:29:47.509000000Z","contract_count":"9353","value_usd":"4124.246","database_time":"2020-07-27T17:27:48.519000000Z","exchange_time":"2020-08-04T17:29:47.509000000Z"}]}"""
        assertResponse(
            expectedCode = 200,
            expectedResponse = expectedResponse,
            pathAndQuery = "/v4/timeseries/market-openinterest?markets=bitmex-XBTUSD-future&api_key=$TEST_API_KEY",
        )

        val expectedResponseCsv =
            """
            market,time,contract_count,value_usd,database_time,exchange_time
            bitmex-XBTUSD-future,2020-07-27T17:27:47.509000000Z,21242,2.24124,2020-07-27T17:27:48.510000000Z,2020-07-27T17:27:47.509000000Z
            bitmex-XBTUSD-future,2020-07-28T17:28:47.509000000Z,1,2124.24,2020-07-27T17:27:48.519811000Z,2020-07-28T17:28:47.509000000Z
            bitmex-XBTUSD-future,2020-07-29T00:00:01.000000000Z,2342,124.246,2020-07-27T17:27:48.512000000Z,2020-07-29T00:00:01.000000000Z
            bitmex-XBTUSD-future,2020-07-29T05:55:01.999000000Z,2342,124.246,2020-07-27T17:27:48.513000000Z,2020-07-29T05:55:01.999000000Z
            bitmex-XBTUSD-future,2020-07-29T10:34:01.353000000Z,2342,124.246,2020-07-27T17:27:48.514000000Z,2020-07-29T10:34:01.353000000Z
            bitmex-XBTUSD-future,2020-07-29T17:29:47.509000000Z,12,5124.246,2020-07-27T17:27:48.515000000Z,2020-07-29T17:29:47.509000000Z
            bitmex-XBTUSD-future,2020-08-01T17:29:47.509000000Z,1224,1124.246,2020-07-27T17:27:48.516000000Z,2020-08-01T17:29:47.509000000Z
            bitmex-XBTUSD-future,2020-08-02T17:29:47.509000000Z,856,2124.246,2020-07-27T17:27:48.517000000Z,2020-08-02T17:29:47.509000000Z
            bitmex-XBTUSD-future,2020-08-03T17:29:47.509000000Z,934,3124.246,2020-07-27T17:27:48.518000000Z,2020-08-03T17:29:47.509000000Z
            bitmex-XBTUSD-future,2020-08-04T17:29:47.509000000Z,9353,4124.246,2020-07-27T17:27:48.519000000Z,2020-08-04T17:29:47.509000000Z

            """.trimIndent()
        assertResponseWithContentType(
            expectedCode = 200,
            expectedResponse = expectedResponseCsv,
            contentType = "text/csv",
            pathAndQuery = "/v4/timeseries/market-openinterest?markets=bitmex-XBTUSD-future&format=csv&api_key=$TEST_API_KEY",
        )

        Assertions.assertEquals(jsonResponseToCsvResponse(expectedResponse), expectedResponseCsv)
    }

    @Test
    fun `test openinterest with end_time and page_size`() {
        val expectedResponse =
            """{"data":[{"market":"bitmex-XBTUSD-future","time":"2020-07-27T17:27:47.509000000Z","contract_count":"21242","value_usd":"2.24124","database_time":"2020-07-27T17:27:48.510000000Z","exchange_time":"2020-07-27T17:27:47.509000000Z"},{"market":"bitmex-XBTUSD-future","time":"2020-07-28T17:28:47.509000000Z","contract_count":"1","value_usd":"2124.24","database_time":"2020-07-27T17:27:48.519811000Z","exchange_time":"2020-07-28T17:28:47.509000000Z"}],"next_page_token":"0.MjAyMC0wNy0yOFQxNzoyODo0Ny41MDla","next_page_url":"http://127.0.0.1:8080/v4/timeseries/market-openinterest?markets=bitmex-XBTUSD-future&api_key=x1&start_time=20180819&page_size=2&paging_from=start&next_page_token=0.MjAyMC0wNy0yOFQxNzoyODo0Ny41MDla"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-openinterest?markets=bitmex-XBTUSD-future&api_key=$TEST_API_KEY&start_time=20180819&page_size=2&paging_from=start",
        )
    }

    @Test
    fun `test openinterest with end_time and page_size in a csv format`() {
        val expectedResponse =
            """{"data":[{"market":"bitmex-XBTUSD-future","time":"2020-07-27T17:27:47.509000000Z","contract_count":"21242","value_usd":"2.24124","database_time":"2020-07-27T17:27:48.510000000Z","exchange_time":"2020-07-27T17:27:47.509000000Z"},{"market":"bitmex-XBTUSD-future","time":"2020-07-28T17:28:47.509000000Z","contract_count":"1","value_usd":"2124.24","database_time":"2020-07-27T17:27:48.519811000Z","exchange_time":"2020-07-28T17:28:47.509000000Z"}],"next_page_token":"0.MjAyMC0wNy0yOFQxNzoyODo0Ny41MDla","next_page_url":"http://127.0.0.1:8080/v4/timeseries/market-openinterest?markets=bitmex-XBTUSD-future&api_key=x1&start_time=20180819&page_size=2&paging_from=start&next_page_token=0.MjAyMC0wNy0yOFQxNzoyODo0Ny41MDla"}"""
        assertResponse(
            expectedCode = 200,
            expectedResponse = expectedResponse,
            pathAndQuery = "/v4/timeseries/market-openinterest?markets=bitmex-XBTUSD-future&api_key=$TEST_API_KEY&start_time=20180819&page_size=2&paging_from=start",
        )

        val expectedResponseCsv =
            """
            market,time,contract_count,value_usd,database_time,exchange_time
            bitmex-XBTUSD-future,2020-07-27T17:27:47.509000000Z,21242,2.24124,2020-07-27T17:27:48.510000000Z,2020-07-27T17:27:47.509000000Z
            bitmex-XBTUSD-future,2020-07-28T17:28:47.509000000Z,1,2124.24,2020-07-27T17:27:48.519811000Z,2020-07-28T17:28:47.509000000Z

            """.trimIndent()
        assertResponseWithContentType(
            expectedCode = 200,
            expectedResponse = expectedResponseCsv,
            contentType = "text/csv",
            pathAndQuery = "/v4/timeseries/market-openinterest?markets=bitmex-XBTUSD-future&format=csv&api_key=$TEST_API_KEY&start_time=20180819&page_size=2&paging_from=start",
        )

        Assertions.assertEquals(jsonResponseToCsvResponse(expectedResponse), expectedResponseCsv)
    }

    @Test
    fun `test option openinterest with end_time and page_size`() {
        val expectedResponse =
            """{"data":[{"market":"deribit-ETH-2APR21-1960-C-option","time":"2020-07-27T17:27:47.675661000Z","contract_count":"1155868","value_usd":"1558680","database_time":"2020-07-27T17:27:48.519813000Z","exchange_time":"2020-07-27T17:27:47.675661000Z"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-openinterest?markets=deribit-ETH-2APR21-1960-C-option&api_key=$TEST_API_KEY&start_time=20180819&page_size=2&paging_from=start",
        )
    }

    @Test
    fun `test openinterest with end_time, paging_from and page_size, empty`() {
        val expectedResponse =
            """{"data":[]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-openinterest?markets=bitmex-XBTUSD-future&api_key=$TEST_API_KEY&end_time=20180819&page_size=2",
        )
    }

    @Test
    fun `test openinterest with end_time, paging_from and page_size, empty in a csv format`() {
        assertResponseWithContentType(
            expectedCode = 200,
            expectedResponse = EMPTY_CSV_RESPONSE,
            contentType = "text/csv",
            pathAndQuery = "/v4/timeseries/market-openinterest?markets=bitmex-XBTUSD-future&format=csv&api_key=$TEST_API_KEY&end_time=20180819&page_size=2",
        )
    }

    @Test
    fun `test openinterest with end_time`() {
        val expectedResponse =
            """{"data":[{"market":"bitmex-XBTUSD-future","time":"2020-07-27T17:27:47.509000000Z","contract_count":"21242","value_usd":"2.24124","database_time":"2020-07-27T17:27:48.510000000Z","exchange_time":"2020-07-27T17:27:47.509000000Z"},{"market":"bitmex-XBTUSD-future","time":"2020-07-28T17:28:47.509000000Z","contract_count":"1","value_usd":"2124.24","database_time":"2020-07-27T17:27:48.519811000Z","exchange_time":"2020-07-28T17:28:47.509000000Z"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-openinterest?markets=bitmex-XBTUSD-future&api_key=$TEST_API_KEY&end_time=2020-07-28T17:28:48.000Z",
        )
    }

    @Test
    fun `test openinterest with end_time, paging_from and page_size`() {
        val expectedResponse =
            """{"data":[{"market":"bitmex-XBTUSD-future","time":"2020-08-02T17:29:47.509000000Z","contract_count":"856","value_usd":"2124.246","database_time":"2020-07-27T17:27:48.517000000Z","exchange_time":"2020-08-02T17:29:47.509000000Z"},{"market":"bitmex-XBTUSD-future","time":"2020-08-03T17:29:47.509000000Z","contract_count":"934","value_usd":"3124.246","database_time":"2020-07-27T17:27:48.518000000Z","exchange_time":"2020-08-03T17:29:47.509000000Z"},{"market":"bitmex-XBTUSD-future","time":"2020-08-04T17:29:47.509000000Z","contract_count":"9353","value_usd":"4124.246","database_time":"2020-07-27T17:27:48.519000000Z","exchange_time":"2020-08-04T17:29:47.509000000Z"}],"next_page_token":"0.MjAyMC0wOC0wMlQxNzoyOTo0Ny41MDla","next_page_url":"http://127.0.0.1:8080/v4/timeseries/market-openinterest?markets=bitmex-XBTUSD-future&api_key=x1&end_time=2020-08-29T00%3A00%3A05.000Z&page_size=3&next_page_token=0.MjAyMC0wOC0wMlQxNzoyOTo0Ny41MDla"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-openinterest?markets=bitmex-XBTUSD-future&api_key=$TEST_API_KEY&end_time=2020-08-29T00:00:05.000Z&page_size=3",
        )
    }

    @Test
    fun `test openinterest with cut end_time, paging_from and page_size`() {
        val expectedResponse =
            """{"data":[{"market":"bitmex-XBTUSD-future","time":"2020-07-28T17:28:47.509000000Z","contract_count":"1","value_usd":"2124.24","database_time":"2020-07-27T17:27:48.519811000Z","exchange_time":"2020-07-28T17:28:47.509000000Z"},{"market":"bitmex-XBTUSD-future","time":"2020-07-29T00:00:01.000000000Z","contract_count":"2342","value_usd":"124.246","database_time":"2020-07-27T17:27:48.512000000Z","exchange_time":"2020-07-29T00:00:01.000000000Z"},{"market":"bitmex-XBTUSD-future","time":"2020-07-29T05:55:01.999000000Z","contract_count":"2342","value_usd":"124.246","database_time":"2020-07-27T17:27:48.513000000Z","exchange_time":"2020-07-29T05:55:01.999000000Z"}],"next_page_token":"0.MjAyMC0wNy0yOFQxNzoyODo0Ny41MDla","next_page_url":"http://127.0.0.1:8080/v4/timeseries/market-openinterest?markets=bitmex-XBTUSD-future&api_key=x1&end_time=2020-07-29T09%3A51%3A28.851000Z&page_size=3&next_page_token=0.MjAyMC0wNy0yOFQxNzoyODo0Ny41MDla"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-openinterest?markets=bitmex-XBTUSD-future&api_key=$TEST_API_KEY&end_time=2020-07-29T09:51:28.851000Z&page_size=3",
        )
    }

    @Test
    fun `test openinterest with nanotime filtering`() {
        val expectedResponse =
            """{"data":[{"market":"bitmex-XBTUSD-future","time":"2020-07-29T05:55:01.999000000Z","contract_count":"2342","value_usd":"124.246","database_time":"2020-07-27T17:27:48.513000000Z","exchange_time":"2020-07-29T05:55:01.999000000Z"},{"market":"bitmex-XBTUSD-future","time":"2020-07-29T10:34:01.353000000Z","contract_count":"2342","value_usd":"124.246","database_time":"2020-07-27T17:27:48.514000000Z","exchange_time":"2020-07-29T10:34:01.353000000Z"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-openinterest?markets=bitmex-XBTUSD-future&api_key=$TEST_API_KEY&start_time=2020-07-29T00:00:01.000000001&end_time=2020-07-29T10:36:46.428000000&page_size=2",
        )
    }

    @Test
    fun `test openinterest with nanotime filtering 2`() {
        val expectedResponse =
            """{"data":[]}"""
        // trade with time '2020-07-29T10:36:46.428000000Z' should not be included in the response
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-openinterest?markets=bitmex-XBTUSD-future&api_key=$TEST_API_KEY&start_time=2020-07-29T10:36:46.428000001&end_time=2020-07-29T10:36:46.428000001&page_size=1",
        )
    }

    @Test
    fun `test openinterest with incorrect key`() {
        val expectedResponse =
            """{"error":{"type":"wrong_credentials","message":"Supplied credentials are not valid."}}"""
        assertResponse(
            401,
            expectedResponse,
            "/v4/timeseries/market-openinterest?markets=bitmex-XBTUSD-future&api_key=invalid_key&end_time=20191223&page_size=2",
        )
    }

    @Test
    fun `test openinterest with key without permissions`() {
        getResponse("/v4/timeseries/market-openinterest?markets=bitmex-XBTUSD-future&api_key=x2&end_time=20191223&page_size=2").assertResponse()
    }

    @Test
    fun `test openinterest with different timezones`() {
        val expectedResponse =
            """{"data":[{"market":"bitmex-XBTUSD-future","time":"2020-08-02T17:29:47.509000000Z","contract_count":"856","value_usd":"2124.246","database_time":"2020-07-27T17:27:48.517000000Z","exchange_time":"2020-08-02T17:29:47.509000000Z"},{"market":"bitmex-XBTUSD-future","time":"2020-08-03T17:29:47.509000000Z","contract_count":"934","value_usd":"3124.246","database_time":"2020-07-27T17:27:48.518000000Z","exchange_time":"2020-08-03T17:29:47.509000000Z"},{"market":"bitmex-XBTUSD-future","time":"2020-08-04T17:29:47.509000000Z","contract_count":"9353","value_usd":"4124.246","database_time":"2020-07-27T17:27:48.519000000Z","exchange_time":"2020-08-04T17:29:47.509000000Z"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-openinterest?markets=bitmex-XBTUSD-future&api_key=$TEST_API_KEY&start_time=2020-08-01T17:30:47.509&page_size=3&paging_from=start",
        )

        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-openinterest?markets=bitmex-XBTUSD-future&api_key=$TEST_API_KEY&start_time=2020-08-01T20:30:47.509&page_size=3&timezone=Europe/Istanbul&paging_from=start",
        )

        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-openinterest?markets=bitmex-XBTUSD-future&api_key=$TEST_API_KEY&start_time=2020-08-01T13:30:47.509&page_size=3&timezone=America/New_York&paging_from=start",
        )
    }

    @Test
    fun `test openinterest next page from end and page_size = 1`() {
        val expectedResponsePage1 =
            """{"data":[{"market":"bitmex-XBTUSD-future","time":"2020-08-04T17:29:47.509000000Z","contract_count":"9353","value_usd":"4124.246","database_time":"2020-07-27T17:27:48.519000000Z","exchange_time":"2020-08-04T17:29:47.509000000Z"}],"next_page_token":"0.MjAyMC0wOC0wNFQxNzoyOTo0Ny41MDla","next_page_url":"http://127.0.0.1:8080/v4/timeseries/market-openinterest?markets=bitmex-XBTUSD-future&api_key=x1&start_time=2020-07-29T10%3A36%3A46.275&page_size=1&next_page_token=0.MjAyMC0wOC0wNFQxNzoyOTo0Ny41MDla"}"""
        assertResponse(
            200,
            expectedResponsePage1,
            "/v4/timeseries/market-openinterest?markets=bitmex-XBTUSD-future&api_key=$TEST_API_KEY&start_time=2020-07-29T10:36:46.275&page_size=1",
        )

        val expectedResponsePage2 =
            """{"data":[{"market":"bitmex-XBTUSD-future","time":"2020-08-03T17:29:47.509000000Z","contract_count":"934","value_usd":"3124.246","database_time":"2020-07-27T17:27:48.518000000Z","exchange_time":"2020-08-03T17:29:47.509000000Z"}],"next_page_token":"0.MjAyMC0wOC0wM1QxNzoyOTo0Ny41MDla","next_page_url":"http://127.0.0.1:8080/v4/timeseries/market-openinterest?markets=bitmex-XBTUSD-future&api_key=x1&start_time=2020-07-29T10%3A36%3A46.275&page_size=1&next_page_token=0.MjAyMC0wOC0wM1QxNzoyOTo0Ny41MDla"}"""

        assertResponse(
            200,
            expectedResponsePage2,
            "/v4/timeseries/market-openinterest?markets=bitmex-XBTUSD-future&api_key=x1&start_time=2020-07-29T10%3A36%3A46.275&page_size=1&next_page_token=0.MjAyMC0wOC0wNFQxNzoyOTo0Ny41MDla",
        )
    }

    @Test
    fun `test openinterest next page from start and page_size = 2`() {
        val expectedResponsePage1 =
            """{"data":[{"market":"bitmex-XBTUSD-future","time":"2020-07-29T17:29:47.509000000Z","contract_count":"12","value_usd":"5124.246","database_time":"2020-07-27T17:27:48.515000000Z","exchange_time":"2020-07-29T17:29:47.509000000Z"},{"market":"bitmex-XBTUSD-future","time":"2020-08-01T17:29:47.509000000Z","contract_count":"1224","value_usd":"1124.246","database_time":"2020-07-27T17:27:48.516000000Z","exchange_time":"2020-08-01T17:29:47.509000000Z"}],"next_page_token":"0.MjAyMC0wOC0wMVQxNzoyOTo0Ny41MDla","next_page_url":"http://127.0.0.1:8080/v4/timeseries/market-openinterest?markets=bitmex-XBTUSD-future&api_key=x1&start_time=2020-07-29T10%3A36%3A40.247&page_size=2&paging_from=start&next_page_token=0.MjAyMC0wOC0wMVQxNzoyOTo0Ny41MDla"}"""
        assertResponse(
            200,
            expectedResponsePage1,
            "/v4/timeseries/market-openinterest?markets=bitmex-XBTUSD-future&api_key=$TEST_API_KEY&start_time=2020-07-29T10:36:40.247&page_size=2&paging_from=start",
        )

        val expectedResponsePage2 =
            """{"data":[{"market":"bitmex-XBTUSD-future","time":"2020-08-02T17:29:47.509000000Z","contract_count":"856","value_usd":"2124.246","database_time":"2020-07-27T17:27:48.517000000Z","exchange_time":"2020-08-02T17:29:47.509000000Z"},{"market":"bitmex-XBTUSD-future","time":"2020-08-03T17:29:47.509000000Z","contract_count":"934","value_usd":"3124.246","database_time":"2020-07-27T17:27:48.518000000Z","exchange_time":"2020-08-03T17:29:47.509000000Z"}],"next_page_token":"0.MjAyMC0wOC0wM1QxNzoyOTo0Ny41MDla","next_page_url":"http://127.0.0.1:8080/v4/timeseries/market-openinterest?markets=bitmex-XBTUSD-future&api_key=x1&start_time=2020-07-29T10%3A36%3A40.247&page_size=2&paging_from=start&next_page_token=0.MjAyMC0wOC0wM1QxNzoyOTo0Ny41MDla"}"""

        assertResponse(
            200,
            expectedResponsePage2,
            "/v4/timeseries/market-openinterest?markets=bitmex-XBTUSD-future&api_key=x1&start_time=2020-07-29T10%3A36%3A40.247&page_size=2&paging_from=start&next_page_token=0.MjAyMC0wOC0wMVQxNzoyOTo0Ny41MDla",
        )
    }

    @Test
    fun `test openinterest next page not present but number of elments on the page equal to page_size`() {
        val expectedResponse =
            """{"data":[{"market":"bitmex-XBTUSD-future","time":"2020-07-29T00:00:01.000000000Z","contract_count":"2342","value_usd":"124.246","database_time":"2020-07-27T17:27:48.512000000Z","exchange_time":"2020-07-29T00:00:01.000000000Z"},{"market":"bitmex-XBTUSD-future","time":"2020-07-29T05:55:01.999000000Z","contract_count":"2342","value_usd":"124.246","database_time":"2020-07-27T17:27:48.513000000Z","exchange_time":"2020-07-29T05:55:01.999000000Z"},{"market":"bitmex-XBTUSD-future","time":"2020-07-29T10:34:01.353000000Z","contract_count":"2342","value_usd":"124.246","database_time":"2020-07-27T17:27:48.514000000Z","exchange_time":"2020-07-29T10:34:01.353000000Z"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-openinterest?markets=bitmex-XBTUSD-future&api_key=$TEST_API_KEY&start_time=2020-07-29T00:00:00.247&end_time=2020-07-29T10:36:46.427&page_size=3",
        )
    }

    @Test
    fun `test openinterest invalid next page token`() {
        listOf(
            "MjAxOS0wMy0xNVQxMDozNjo0Ni4yNzVafDII",
            "invalid_token",
            "MjAxOS0wMy0xNVQxMDoZNjo0Ni4yNzVafDI",
            "1111111111111",
            "2",
        ).forEach { nextPageToken ->
            assertResponse(
                400,
                """{"error":{"type":"bad_parameter","message":"Bad parameter 'next_page_token'."}}""",
                "/v4/timeseries/market-openinterest?markets=bitmex-XBTUSD-future&api_key=$TEST_API_KEY&start_time=2020-07-29T10%3A36%3A40.247&page_size=2&next_page_token=$nextPageToken",
            )
        }
    }

    @Test
    fun `test openinterest invalid page size`() {
        assertResponse(
            400,
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'page_size'. Must be at least 1."}}""",
            "/v4/timeseries/market-openinterest?markets=bitmex-XBTUSD-future&api_key=$TEST_API_KEY&page_size=-1",
        )
        assertResponse(
            400,
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'page_size'. Must be at most 10000."}}""",
            "/v4/timeseries/market-openinterest?markets=bitmex-XBTUSD-future&api_key=$TEST_API_KEY&page_size=100000",
        )

        listOf(
            100_000_000_000,
            -100_000_000_000,
        ).forEach { pageSize ->
            assertResponse(
                400,
                """{"error":{"type":"bad_parameter","message":"Bad parameter 'page_size'."}}""",
                "/v4/timeseries/market-openinterest?markets=bitmex-XBTUSD-future&api_key=$TEST_API_KEY&page_size=$pageSize",
            )
        }
    }

    @Test
    fun `test openinterest invalid paging_from`() {
        listOf(
            "f",
            "forward",
            "endd",
            "startting",
            "backwards",
            "none",
        ).forEach { pagingFrom ->
            assertResponse(
                400,
                """{"error":{"type":"bad_parameter","message":"Bad parameter 'paging_from'. Value '$pagingFrom' is not supported. Supported values are 'start', 'end'."}}""",
                "/v4/timeseries/market-openinterest?markets=bitmex-XBTUSD-future&api_key=$TEST_API_KEY&paging_from=$pagingFrom",
            )
        }
    }

    @Test
    fun `test openinterest empty paging_from`() {
        getResponse("/v4/timeseries/market-openinterest?markets=bitmex-XBTUSD-future&api_key=$TEST_API_KEY&paging_from=").assertResponse()
    }

    @Test
    fun `should return empty response when requesting unsupported pattern market`() {
        getResponse("/v4/timeseries/market-openinterest?markets=zaif-*-option&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should return empty response when requesting unsupported pattern markets`() {
        getResponse("/v4/timeseries/market-openinterest?markets=zaif-*-option,simex-CGSEUR-*&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `test openinterest valid timestamp ceiling for end_time and flooring for start_time sub second`() {
        val expectedResponse =
            """{"data":[{"market":"bitmex-XBTUSD-future","time":"2020-07-29T10:34:01.353000000Z","contract_count":"2342","value_usd":"124.246","database_time":"2020-07-27T17:27:48.514000000Z","exchange_time":"2020-07-29T10:34:01.353000000Z"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-openinterest?markets=bitmex-XBTUSD-future&api_key=$TEST_API_KEY&start_time=2020-07-29T10:34:01&end_time=2020-07-29T10:34:01&page_size=2&paging_from=start",
        )
    }

    @Test
    fun `test openinterest valid timestamp ceiling for end_time and flooring for start_time sub day`() {
        val expectedResponse =
            """{"data":[{"market":"bitmex-XBTUSD-future","time":"2020-07-29T00:00:01.000000000Z","contract_count":"2342","value_usd":"124.246","database_time":"2020-07-27T17:27:48.512000000Z","exchange_time":"2020-07-29T00:00:01.000000000Z"},{"market":"bitmex-XBTUSD-future","time":"2020-07-29T05:55:01.999000000Z","contract_count":"2342","value_usd":"124.246","database_time":"2020-07-27T17:27:48.513000000Z","exchange_time":"2020-07-29T05:55:01.999000000Z"},{"market":"bitmex-XBTUSD-future","time":"2020-07-29T10:34:01.353000000Z","contract_count":"2342","value_usd":"124.246","database_time":"2020-07-27T17:27:48.514000000Z","exchange_time":"2020-07-29T10:34:01.353000000Z"},{"market":"bitmex-XBTUSD-future","time":"2020-07-29T17:29:47.509000000Z","contract_count":"12","value_usd":"5124.246","database_time":"2020-07-27T17:27:48.515000000Z","exchange_time":"2020-07-29T17:29:47.509000000Z"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-openinterest?markets=bitmex-XBTUSD-future&api_key=$TEST_API_KEY&start_time=2020-07-29&end_time=2020-07-29&page_size=5",
        )
    }

    @Test
    fun `test openinterest invalid start, end configuration`() {
        listOf(
            "2019-01-01" to "2018-12-31",
            "2019-01-01T01:01:01" to "2019-01-01T01:01:00",
            "2019-01-01T01:01:01.000002" to "2019-01-01T01:01:01.000001",
        ).forEach { (startTime, endTime) ->
            assertResponse(
                400,
                """{"error":{"type":"bad_parameter","message":"Bad parameter 'start_time'. Start time '$startTime' is later than the end time '$endTime'."}}""",
                "/v4/timeseries/market-openinterest?markets=bitmex-XBTUSD-future&api_key=$TEST_API_KEY&start_time=$startTime&end_time=$endTime",
            )
        }

        assertResponse(
            400,
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'start_time'. Start time '1970-01-01' is later than the end time '1969-12-31'."}}""",
            "/v4/timeseries/market-openinterest?markets=bitmex-XBTUSD-future&api_key=$TEST_API_KEY&end_time=1969-12-31",
        )
        assertResponse(
            400,
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'start_time'. Start time '2081-01-01' is later than the end time 'now'."}}""",
            "/v4/timeseries/market-openinterest?markets=bitmex-XBTUSD-future&api_key=$TEST_API_KEY&start_time=2081-01-01",
        )
    }

    @Test
    fun `test openinterest start inclusive, end exclusive`() {
        val expectedResponse =
            """{"data":[{"market":"bitmex-XBTUSD-future","time":"2020-07-29T00:00:01.000000000Z","contract_count":"2342","value_usd":"124.246","database_time":"2020-07-27T17:27:48.512000000Z","exchange_time":"2020-07-29T00:00:01.000000000Z"},{"market":"bitmex-XBTUSD-future","time":"2020-07-29T05:55:01.999000000Z","contract_count":"2342","value_usd":"124.246","database_time":"2020-07-27T17:27:48.513000000Z","exchange_time":"2020-07-29T05:55:01.999000000Z"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-openinterest?markets=bitmex-XBTUSD-future&api_key=$TEST_API_KEY&start_time=2020-07-29T00:00:01.000&end_time=2020-07-29T10:34:01.353&page_size=3&end_inclusive=false",
        )
    }

    @Test
    fun `test openinterest start exclusive, end inclusive`() {
        val expectedResponse =
            """{"data":[{"market":"bitmex-XBTUSD-future","time":"2020-07-29T05:55:01.999000000Z","contract_count":"2342","value_usd":"124.246","database_time":"2020-07-27T17:27:48.513000000Z","exchange_time":"2020-07-29T05:55:01.999000000Z"},{"market":"bitmex-XBTUSD-future","time":"2020-07-29T10:34:01.353000000Z","contract_count":"2342","value_usd":"124.246","database_time":"2020-07-27T17:27:48.514000000Z","exchange_time":"2020-07-29T10:34:01.353000000Z"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-openinterest?markets=bitmex-XBTUSD-future&api_key=$TEST_API_KEY&start_time=2020-07-29T00:00:01.000&end_time=2020-07-29T10:34:01.353&page_size=3&start_inclusive=false",
        )
    }

    @Test
    fun `test openinterest start exclusive, end exclusive`() {
        val expectedResponse =
            """{"data":[{"market":"bitmex-XBTUSD-future","time":"2020-07-29T05:55:01.999000000Z","contract_count":"2342","value_usd":"124.246","database_time":"2020-07-27T17:27:48.513000000Z","exchange_time":"2020-07-29T05:55:01.999000000Z"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-openinterest?markets=bitmex-XBTUSD-future&api_key=$TEST_API_KEY&start_time=2020-07-29T00:00:01.000&end_time=2020-07-29T10:34:01.353&page_size=3&start_inclusive=false&end_inclusive=false",
        )
    }

    @Test
    fun `invalid market id`() {
        val expectedResponse =
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'markets'. Market 'bitmex-XBTTTUSDDD-future' is not supported."}}"""
        assertResponse(
            400,
            expectedResponse,
            "/v4/timeseries/market-openinterest?markets=bitmex-XBTTTUSDDD-future&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `should return 1m futures`() {
        assertResponse(
            200,
            """{"data":[{"market":"binance-ETHUSD-future","time":"2023-09-01T23:59:00.000000000Z","contract_count":"9353","value_usd":"9124.246","database_time":"2023-09-01T23:59:00.000000000Z","exchange_time":"2023-09-01T23:59:00.000000000Z"},{"market":"binance-ETHUSD-future","time":"2023-09-02T00:00:00.000000000Z","contract_count":"9353","value_usd":"9124.246","database_time":"2023-09-02T00:00:00.000000000Z","exchange_time":"2023-09-02T00:00:00.000000000Z"},{"market":"binance-ETHUSD-future","time":"2023-09-02T00:01:00.000000000Z","contract_count":"9353","value_usd":"9124.246","database_time":"2023-09-02T00:01:00.000000000Z","exchange_time":"2023-09-02T00:01:00.000000000Z"},{"market":"binance-ETHUSD-future","time":"2023-09-02T00:02:00.000000000Z","contract_count":"9353","value_usd":"9124.246","database_time":"2023-09-02T00:02:00.000000000Z","exchange_time":"2023-09-02T00:02:00.000000000Z"},{"market":"binance-ETHUSD-future","time":"2023-09-02T01:00:00.000000000Z","contract_count":"9353","value_usd":"9124.246","database_time":"2023-09-02T01:00:00.000000000Z","exchange_time":"2023-09-02T01:00:00.000000000Z"},{"market":"binance-ETHUSD-future","time":"2023-09-02T23:00:00.000000000Z","contract_count":"9353","value_usd":"9124.246","database_time":"2023-09-02T23:00:00.000000000Z","exchange_time":"2023-09-02T23:00:00.000000000Z"},{"market":"binance-ETHUSD-future","time":"2023-09-03T00:00:00.000000000Z","contract_count":"9353","value_usd":"9124.246","database_time":"2023-09-03T00:00:00.000000000Z","exchange_time":"2023-09-03T00:00:00.000000000Z"},{"market":"binance-ETHUSD-future","time":"2023-09-03T10:00:00.000000000Z","contract_count":"9353","value_usd":"9124.246","database_time":"2023-09-03T10:00:00.000000000Z","exchange_time":"2023-09-03T10:00:00.000000000Z"}]}""",
            "/v4/timeseries/market-openinterest?markets=binance-ETHUSD-future&api_key=$TEST_API_KEY&start_time=2023-09-01T00:00:00&end_time=2023-09-04T00:00:00&granularity=1m",
        )
    }

    @Test
    fun `should return 1h futures`() {
        assertResponse(
            200,
            """{"data":[{"market":"binance-ETHUSD-future","time":"2023-09-02T00:00:00.000000000Z","contract_count":"9353","value_usd":"9124.246","database_time":"2023-09-02T00:00:00.000000000Z","exchange_time":"2023-09-02T00:00:00.000000000Z"},{"market":"binance-ETHUSD-future","time":"2023-09-02T01:00:00.000000000Z","contract_count":"9353","value_usd":"9124.246","database_time":"2023-09-02T01:00:00.000000000Z","exchange_time":"2023-09-02T01:00:00.000000000Z"},{"market":"binance-ETHUSD-future","time":"2023-09-02T23:00:00.000000000Z","contract_count":"9353","value_usd":"9124.246","database_time":"2023-09-02T23:00:00.000000000Z","exchange_time":"2023-09-02T23:00:00.000000000Z"},{"market":"binance-ETHUSD-future","time":"2023-09-03T00:00:00.000000000Z","contract_count":"9353","value_usd":"9124.246","database_time":"2023-09-03T00:00:00.000000000Z","exchange_time":"2023-09-03T00:00:00.000000000Z"},{"market":"binance-ETHUSD-future","time":"2023-09-03T10:00:00.000000000Z","contract_count":"9353","value_usd":"9124.246","database_time":"2023-09-03T10:00:00.000000000Z","exchange_time":"2023-09-03T10:00:00.000000000Z"}]}""",
            "/v4/timeseries/market-openinterest?markets=binance-ETHUSD-future&api_key=$TEST_API_KEY&start_time=2023-09-01T00:00:00&end_time=2023-09-04T00:00:00&granularity=1h",
        )
    }

    @Test
    fun `should return 1d futures`() {
        assertResponse(
            200,
            """{"data":[{"market":"binance-ETHUSD-future","time":"2023-09-02T00:00:00.000000000Z","contract_count":"9353","value_usd":"9124.246","database_time":"2023-09-02T00:00:00.000000000Z","exchange_time":"2023-09-02T00:00:00.000000000Z"},{"market":"binance-ETHUSD-future","time":"2023-09-03T00:00:00.000000000Z","contract_count":"9353","value_usd":"9124.246","database_time":"2023-09-03T00:00:00.000000000Z","exchange_time":"2023-09-03T00:00:00.000000000Z"},{"market":"binance-ETHUSD-future","time":"2023-09-03T10:00:00.000000000Z","contract_count":"9353","value_usd":"9124.246","database_time":"2023-09-03T10:00:00.000000000Z","exchange_time":"2023-09-03T10:00:00.000000000Z"}]}""",
            "/v4/timeseries/market-openinterest?markets=binance-ETHUSD-future&api_key=$TEST_API_KEY&start_time=2023-09-01T00:00:00&end_time=2023-09-04T00:00:00&granularity=1d",
        )
    }

    @Test
    fun `should return 1h futures with timezone`() {
        assertResponse(
            200,
            """{"data":[{"market":"binance-ETHUSD-future","time":"2023-09-03T10:00:00.000000000Z","contract_count":"9353","value_usd":"9124.246","database_time":"2023-09-03T10:00:00.000000000Z","exchange_time":"2023-09-03T10:00:00.000000000Z"}]}""",
            "/v4/timeseries/market-openinterest?markets=binance-ETHUSD-future&api_key=$TEST_API_KEY&start_time=2023-09-03T12:00:00&end_time=2023-09-03T14:00:00&granularity=1h&timezone=GMT%2b03:00",
        )
    }

    @Test
    fun `test futures with multiple frequencies`() {
        assertResponse(
            400,
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'granularity'. Value '1m,1h,1d' is not supported. Supported values are 'raw', '1m', '1h', '1d'."}}""",
            "/v4/timeseries/market-openinterest?markets=binance-ETHUSD-future&api_key=$TEST_API_KEY&start_time=2023-09-03T12:00:00&end_time=2023-09-03T14:00:00&granularity=1m,1h,1d",
        )
    }

    @Test
    fun `test futures with unsupported granularity`() {
        assertResponse(
            400,
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'granularity'. Value '10m' is not supported. Supported values are 'raw', '1m', '1h', '1d'."}}""",
            "/v4/timeseries/market-openinterest?markets=binance-ETHUSD-future&api_key=$TEST_API_KEY&start_time=2023-09-03T12:00:00&end_time=2023-09-03T14:00:00&granularity=10m",
        )
    }

    @Test
    fun `should return 1m options`() {
        assertResponse(
            200,
            """{"data":[{"market":"binance-ETH-01SEP23-3000-C-option","time":"2023-09-01T23:59:00.000000000Z","contract_count":"9353","value_usd":"9124.246","database_time":"2023-09-01T23:59:00.000000000Z","exchange_time":"2023-09-01T23:59:00.000000000Z"},{"market":"binance-ETH-01SEP23-3000-C-option","time":"2023-09-02T00:00:00.000000000Z","contract_count":"9353","value_usd":"9124.246","database_time":"2023-09-02T00:00:00.000000000Z","exchange_time":"2023-09-02T00:00:00.000000000Z"},{"market":"binance-ETH-01SEP23-3000-C-option","time":"2023-09-02T00:01:00.000000000Z","contract_count":"9353","value_usd":"9124.246","database_time":"2023-09-02T00:01:00.000000000Z","exchange_time":"2023-09-02T00:01:00.000000000Z"},{"market":"binance-ETH-01SEP23-3000-C-option","time":"2023-09-02T00:02:00.000000000Z","contract_count":"9353","value_usd":"9124.246","database_time":"2023-09-02T00:02:00.000000000Z","exchange_time":"2023-09-02T00:02:00.000000000Z"},{"market":"binance-ETH-01SEP23-3000-C-option","time":"2023-09-02T01:00:00.000000000Z","contract_count":"9353","value_usd":"9124.246","database_time":"2023-09-02T01:00:00.000000000Z","exchange_time":"2023-09-02T01:00:00.000000000Z"},{"market":"binance-ETH-01SEP23-3000-C-option","time":"2023-09-02T23:00:00.000000000Z","contract_count":"9353","value_usd":"9124.246","database_time":"2023-09-02T23:00:00.000000000Z","exchange_time":"2023-09-02T23:00:00.000000000Z"},{"market":"binance-ETH-01SEP23-3000-C-option","time":"2023-09-03T00:00:00.000000000Z","contract_count":"9353","value_usd":"9124.246","database_time":"2023-09-03T00:00:00.000000000Z","exchange_time":"2023-09-03T00:00:00.000000000Z"},{"market":"binance-ETH-01SEP23-3000-C-option","time":"2023-09-03T10:00:00.000000000Z","contract_count":"9353","value_usd":"9124.246","database_time":"2023-09-03T10:00:00.000000000Z","exchange_time":"2023-09-03T10:00:00.000000000Z"}]}""",
            "/v4/timeseries/market-openinterest?markets=binance-ETH-01SEP23-3000-C-option&api_key=$TEST_API_KEY&start_time=2023-09-01T00:00:00&end_time=2023-09-04T00:00:00&granularity=1m",
        )
    }

    @Test
    fun `should return 1h options`() {
        assertResponse(
            200,
            """{"data":[{"market":"binance-ETH-01SEP23-3000-C-option","time":"2023-09-02T00:00:00.000000000Z","contract_count":"9353","value_usd":"9124.246","database_time":"2023-09-02T00:00:00.000000000Z","exchange_time":"2023-09-02T00:00:00.000000000Z"},{"market":"binance-ETH-01SEP23-3000-C-option","time":"2023-09-02T01:00:00.000000000Z","contract_count":"9353","value_usd":"9124.246","database_time":"2023-09-02T01:00:00.000000000Z","exchange_time":"2023-09-02T01:00:00.000000000Z"},{"market":"binance-ETH-01SEP23-3000-C-option","time":"2023-09-02T23:00:00.000000000Z","contract_count":"9353","value_usd":"9124.246","database_time":"2023-09-02T23:00:00.000000000Z","exchange_time":"2023-09-02T23:00:00.000000000Z"},{"market":"binance-ETH-01SEP23-3000-C-option","time":"2023-09-03T00:00:00.000000000Z","contract_count":"9353","value_usd":"9124.246","database_time":"2023-09-03T00:00:00.000000000Z","exchange_time":"2023-09-03T00:00:00.000000000Z"},{"market":"binance-ETH-01SEP23-3000-C-option","time":"2023-09-03T10:00:00.000000000Z","contract_count":"9353","value_usd":"9124.246","database_time":"2023-09-03T10:00:00.000000000Z","exchange_time":"2023-09-03T10:00:00.000000000Z"}]}""",
            "/v4/timeseries/market-openinterest?markets=binance-ETH-01SEP23-3000-C-option&api_key=$TEST_API_KEY&start_time=2023-09-01T00:00:00&end_time=2023-09-04T00:00:00&granularity=1h",
        )
    }

    @Test
    fun `should return 1d options`() {
        assertResponse(
            200,
            """{"data":[{"market":"binance-ETH-01SEP23-3000-C-option","time":"2023-09-02T00:00:00.000000000Z","contract_count":"9353","value_usd":"9124.246","database_time":"2023-09-02T00:00:00.000000000Z","exchange_time":"2023-09-02T00:00:00.000000000Z"},{"market":"binance-ETH-01SEP23-3000-C-option","time":"2023-09-03T00:00:00.000000000Z","contract_count":"9353","value_usd":"9124.246","database_time":"2023-09-03T00:00:00.000000000Z","exchange_time":"2023-09-03T00:00:00.000000000Z"},{"market":"binance-ETH-01SEP23-3000-C-option","time":"2023-09-03T10:00:00.000000000Z","contract_count":"9353","value_usd":"9124.246","database_time":"2023-09-03T10:00:00.000000000Z","exchange_time":"2023-09-03T10:00:00.000000000Z"}]}""",
            "/v4/timeseries/market-openinterest?markets=binance-ETH-01SEP23-3000-C-option&api_key=$TEST_API_KEY&start_time=2023-09-01T00:00:00&end_time=2023-09-04T00:00:00&granularity=1d",
        )
    }

    @Test
    fun `should return 1h options with timezone`() {
        assertResponse(
            200,
            """{"data":[{"market":"binance-ETH-01SEP23-3000-C-option","time":"2023-09-03T10:00:00.000000000Z","contract_count":"9353","value_usd":"9124.246","database_time":"2023-09-03T10:00:00.000000000Z","exchange_time":"2023-09-03T10:00:00.000000000Z"}]}""",
            "/v4/timeseries/market-openinterest?markets=binance-ETH-01SEP23-3000-C-option&api_key=$TEST_API_KEY&start_time=2023-09-03T12:00:00&end_time=2023-09-03T14:00:00&granularity=1h&timezone=GMT%2b03:00",
        )
    }

    @Test
    fun `test options with multiple frequencies`() {
        assertResponse(
            400,
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'granularity'. Value '1m,1h,1d' is not supported. Supported values are 'raw', '1m', '1h', '1d'."}}""",
            "/v4/timeseries/market-openinterest?markets=binance-ETH-01SEP23-3000-C-option&api_key=$TEST_API_KEY&start_time=2023-09-03T12:00:00&end_time=2023-09-03T14:00:00&granularity=1m,1h,1d",
        )
    }

    @Test
    fun `test options with unsupported granularity`() {
        assertResponse(
            400,
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'granularity'. Value '10m' is not supported. Supported values are 'raw', '1m', '1h', '1d'."}}""",
            "/v4/timeseries/market-openinterest?markets=binance-ETH-01SEP23-3000-C-option&&api_key=$TEST_API_KEY&start_time=2023-09-03T12:00:00&end_time=2023-09-03T14:00:00&granularity=10m",
        )
    }

    private fun jsonResponseToCsvResponse(jsonResponse: String): String = jsonResponseToCsvResponse<MarketOpenInterestResponse>(jsonResponse) { it.data }
}
