package io.coinmetrics.api.endpoints.timeseries.market

import io.coinmetrics.api.helper.BaseTest
import io.coinmetrics.api.helper.TEST_API_KEY
import io.coinmetrics.api.model.S3BooksMarketType
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
open class QuotesTest : BaseTest() {
    override fun getAdditionalEnvVariables(): Map<String, String> =
        mapOf(
            "API_${S3BooksMarketType.SPOT_BOOKS}_TIERS" to "COLD:[..2023-08-09),HOT:[2023-08-09..]",
            "API_${S3BooksMarketType.SPOT_BOOKS}_TIER_COLD_TYPE" to "S3",
            "API_${S3BooksMarketType.FUTURES_BOOKS}_TIERS" to "COLD:[..2023-08-09),HOT:[2023-08-09..]",
            "API_${S3BooksMarketType.FUTURES_BOOKS}_TIER_COLD_TYPE" to "S3",
            "API_${S3BooksMarketType.OPTIONS_BOOKS}_TIERS" to "COLD:[..2023-08-09),HOT:[2023-08-09..]",
            "API_${S3BooksMarketType.OPTIONS_BOOKS}_TIER_COLD_TYPE" to "S3",
        )

    /**
     * Tests changes in "granularity" handling.
     */
    @Test
    fun `test quotes with granularity 1m`() {
        getResponse("/v4/timeseries/market-quotes?markets=bittrex-xrp-usd-spot&granularity=1m&start_time=2023-08-08T00:00:00Z&end_time=2023-08-11T00:00:00Z&api_key=$TEST_API_KEY").assertResponse()
    }

    /**
     * Tests changes in "granularity" handling.
     */
    @Test
    fun `test futures with granularity 1m`() {
        getResponse("/v4/timeseries/market-quotes?markets=bittrex-XRPUSD-future&granularity=1m&start_time=2023-08-08T00:00:00Z&end_time=2023-08-11T00:00:00Z&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `test futures with format=json_stream`() {
        getResponse("/v4/timeseries/market-quotes?markets=bittrex-XRPUSD-future&start_time=2023-08-08T00:00:00Z&end_time=2023-08-11T00:00:00Z&format=json_stream&api_key=$TEST_API_KEY").assertResponse("1")
        getResponse("/v4/timeseries/market-quotes?markets=bittrex-XRPUSD-future&start_time=2023-08-08T00:00:00Z&end_time=2023-08-11T00:00:00Z&format=json_stream&api_key=$TEST_API_KEY&pretty=true").assertResponse("2")
    }

    @Test
    fun `test quotes error responses`() {
        listOf(
            "bittrex-invalid-asset-spot",
            "nonexistingexhange-btc-usd-spot",
        ).forEach { marketId ->
            assertResponse(
                400,
                """{"error":{"type":"bad_parameter","message":"Bad parameter 'markets'. Market '$marketId' is not supported."}}""",
                "/v4/timeseries/market-quotes?markets=$marketId&api_key=$TEST_API_KEY",
            )
        }

        listOf(
            "bittrex-btc-usd-test-spot",
            "bitmex-xbtusd-spot",
            "bittrex-btc-usd",
        ).forEach { marketId ->
            assertResponse(
                400,
                """{"error":{"type":"bad_parameter","message":"Bad parameter 'markets'. Incorrect market '$marketId'."}}""",
                "/v4/timeseries/market-quotes?markets=$marketId&api_key=$TEST_API_KEY",
            )
        }

        listOf(
            "201912-23",
            "20191223T00:00:05.000Z",
            "2019-12-23T00:00:05000Z",
        ).forEach { end_time ->
            assertResponse(
                400,
                """{"error":{"type":"bad_parameter","message":"Bad parameter 'end_time'. Incorrect time format '$end_time'. Supported formats are 'yyyy-MM-dd', 'yyyyMMdd', 'yyyy-MM-ddTHH:mm:ss', 'yyyy-MM-ddTHHmmss', 'yyyy-MM-ddTHH:mm:ss.SSS', 'yyyy-MM-ddTHHmmss.SSS', 'yyyy-MM-ddTHH:mm:ss.SSSSSS', 'yyyy-MM-ddTHHmmss.SSSSSS', 'yyyy-MM-ddTHH:mm:ss.SSSSSSSSS', 'yyyy-MM-ddTHHmmss.SSSSSSSSS'."}}""".trimMargin(),
                "/v4/timeseries/market-quotes?markets=bittrex-btc-usd-spot&api_key=$TEST_API_KEY&end_time=$end_time",
            )
        }
    }

    @Test
    fun `test quotes with no parameters`() {
        getResponse("/v4/timeseries/market-quotes?markets=bittrex-btc-usd-spot&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `test quotes with start_time and page_size`() {
        getResponse("/v4/timeseries/market-quotes?markets=bittrex-btc-usd-spot&api_key=$TEST_API_KEY&start_time=20190819&page_size=2&paging_from=start").assertResponse()
    }

    @Test
    fun `test quotes with end_time and paging_from and page_size, empty`() {
        val expectedResponse =
            """{"data":[]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-quotes?markets=bittrex-btc-usd-spot&api_key=$TEST_API_KEY&end_time=20190818&page_size=2",
        )
    }

    @Test
    fun `test quotes with reference time and direction`() {
        getResponse("/v4/timeseries/market-quotes?markets=bittrex-btc-usd-spot&api_key=$TEST_API_KEY&end_time=2019-12-23T00:00:05.000Z").assertResponse()
    }

    @Test
    fun `test quotes for options`() {
        getResponse("/v4/timeseries/market-quotes?markets=deribit-ETH-1OCT21-2850-P-option&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `test quotes for futures`() {
        getResponse("/v4/timeseries/market-quotes?markets=bitmex-XBTUSD-future&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `test quotes with end_time and paging_from and page_size`() {
        getResponse("/v4/timeseries/market-quotes?markets=binance-btc-usdt-spot&api_key=$TEST_API_KEY&end_time=2019-12-23T00:00:05.000Z&page_size=3").assertResponse()
    }

    @Test
    fun `test quotes with cut end_time and paging_from and page_size`() {
        val expectedResponse =
            """{"data":[{"market":"binance-btc-usdt-spot","time":"2019-08-19T09:42:10.301782000Z","coin_metrics_id":"2","ask_price":"1.077","ask_size":"0.4","bid_price":"1.041","bid_size":"0.1"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-quotes?markets=binance-btc-usdt-spot&api_key=$TEST_API_KEY&end_time=2019-08-19T09:42:10.301782Z&page_size=3",
        )
    }

    @Test
    fun `test quotes with one sided and empty books`() {
        getResponse("/v4/timeseries/market-quotes?markets=bittrex-eth-usd-spot&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `test quotes with one sided and empty books include one sided false`() {
        getResponse("/v4/timeseries/market-quotes?markets=bittrex-eth-usd-spot&include_one_sided=false&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `test quotes with one sided and empty books include one sided true`() {
        getResponse("/v4/timeseries/market-quotes?markets=bittrex-eth-usd-spot&include_one_sided=true&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `test option quotes with one sided and empty books when include one sided is false`() {
        getResponse("/v4/timeseries/market-quotes?markets=deribit-ETH-1OCT21-2850-P-option&include_one_sided=false&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `test option quotes with one sided and empty books when include one sided is false and page size is limited`() {
        getResponse("/v4/timeseries/market-quotes?markets=deribit-ETH-1OCT21-2850-P-option&include_one_sided=false&page_size=2&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `test option quotes with one sided and empty books when include one sided is true`() {
        getResponse("/v4/timeseries/market-quotes?markets=deribit-ETH-1OCT21-2850-P-option&include_one_sided=true&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should return empty response when requesting unsupported pattern market`() {
        getResponse("/v4/timeseries/market-quotes?markets=zaif-*-option&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should return empty response when requesting unsupported pattern markets`() {
        getResponse("/v4/timeseries/market-quotes?markets=zaif-*-option,simex-CGSEUR-*&api_key=$TEST_API_KEY").assertResponse()
    }

    // TODO: Tests with inclusive/exclusive time boundaries
    // TODO: Tests with different timezones
    // TODO: Test for the next page
    // TODO: Test for the next page not present but elements returned withing the limit
    // TODO: test invalid next page token
    // TODO: Test for invalid page_size
    // TODO: Test for invalid paging_from
    // TODO: Test for invalid timestamp ceiling for end_time and flooring for start_time

    @Test
    fun `test quotes with incorrect key`() {
        val expectedResponse =
            """{"error":{"type":"wrong_credentials","message":"Supplied credentials are not valid."}}"""
        assertResponse(
            401,
            expectedResponse,
            "/v4/timeseries/market-quotes?markets=bittrex-btc-usd-spot&api_key=invalid_key&start_time=20191223&page_size=2",
        )
    }

    @Test
    fun `test quotes with key without permissions`() {
        getResponse("/v4/timeseries/market-quotes?markets=bittrex-btc-usd-spot&api_key=x2&start_time=20191223&page_size=2").assertResponse()
    }

    @Test
    fun `test quotes with default granularity`() {
        getResponse("/v4/timeseries/market-quotes?markets=bittrex-xrp-usd-spot&start_time=2023-08-08T00:00:00Z&end_time=2023-08-11T00:00:00Z&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `test quotes with granularity 10s results in unsupported error`() {
        assertResponse(
            400,
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'granularity'. Value '10s' is not supported. Supported values are 'raw', '1m', '1h', '1d'."}}""",
            "/v4/timeseries/market-quotes?markets=bittrex-xrp-usd-spot&granularity=10s&start_time=2023-08-08T00:00:00Z&end_time=2023-08-11T00:00:00Z&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `test quotes with granularity 1h`() {
        getResponse("/v4/timeseries/market-quotes?markets=bittrex-xrp-usd-spot&granularity=1h&start_time=2023-08-08T00:00:00Z&end_time=2023-08-11T00:00:00Z&api_key=$TEST_API_KEY&paging_from=start").assertResponse()
    }

    @Test
    fun `test quotes with granularity 1d`() {
        val expectedResponse =
            """{"data":[{"market":"bittrex-xrp-usd-spot","time":"2023-08-09T00:00:00.000000000Z","coin_metrics_id":"2","ask_price":"1986.705000007","ask_size":"1.51031355","bid_price":"1985.815","bid_size":"1.51006499"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-quotes?markets=bittrex-xrp-usd-spot&granularity=1d&start_time=2023-08-08T00:00:00Z&end_time=2023-08-11T00:00:00Z&api_key=$TEST_API_KEY&paging_from=start",
        )
    }

    @Test
    fun `test futures with default granularity`() {
        getResponse("/v4/timeseries/market-quotes?markets=bittrex-XRPUSD-future&start_time=2023-08-08T00:00:00Z&end_time=2023-08-11T00:00:00Z&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `test futures with granularity 10s results in unsupported error`() {
        assertResponse(
            400,
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'granularity'. Value '10s' is not supported. Supported values are 'raw', '1m', '1h', '1d'."}}""",
            "/v4/timeseries/market-quotes?markets=bittrex-XRPUSD-future&granularity=10s&start_time=2023-08-08T00:00:00Z&end_time=2023-08-11T00:00:00Z&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `test futures with granularity 1h`() {
        getResponse("/v4/timeseries/market-quotes?markets=bittrex-XRPUSD-future&granularity=1h&start_time=2023-08-08T00:00:00Z&end_time=2023-08-11T00:00:00Z&api_key=$TEST_API_KEY&paging_from=start").assertResponse()
    }

    @Test
    fun `test futures with granularity 1d`() {
        val expectedResponse =
            """{"data":[{"market":"bittrex-XRPUSD-future","time":"2023-08-09T00:00:00.000000000Z","coin_metrics_id":"1691539200000000-0","ask_price":"1.07","ask_size":"0.4","bid_price":"1.04","bid_size":"0.1"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-quotes?markets=bittrex-XRPUSD-future&granularity=1d&start_time=2023-08-08T00:00:00Z&end_time=2023-08-11T00:00:00Z&api_key=$TEST_API_KEY&paging_from=start",
        )
    }

    @Test
    fun `test option with default granularity`() {
        getResponse("/v4/timeseries/market-quotes?markets=bittrex-XRP-10AUG23-8000-C-option&start_time=2023-08-09T01:30:00Z&end_time=2023-08-09T02:01:00Z&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `test option with granularity 10s results in unsupported error`() {
        assertResponse(
            400,
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'granularity'. Value '10s' is not supported. Supported values are 'raw', '1m', '1h', '1d'."}}""",
            "/v4/timeseries/market-quotes?markets=bittrex-XRP-10AUG23-8000-C-option&granularity=10s&start_time=2023-08-09T01:30:00Z&end_time=2023-08-09T02:01:00Z&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `test option with granularity 1m`() {
        getResponse("/v4/timeseries/market-quotes?markets=bittrex-XRP-10AUG23-8000-C-option&granularity=1m&start_time=2023-08-09T00:00:00Z&end_time=2023-08-09T03:00:00Z&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `test option with granularity 1h`() {
        getResponse("/v4/timeseries/market-quotes?markets=bittrex-XRP-10AUG23-8000-C-option&granularity=1h&start_time=2023-08-09T00:00:00Z&end_time=2023-08-09T03:00:00Z&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `test option with granularity 1d`() {
        val expectedResponse =
            """{"data":[{"market":"bittrex-XRP-10AUG23-8000-C-option","time":"2023-08-09T00:00:00.000000000Z","coin_metrics_id":"1691539200000000-0","ask_price":"0.1045","ask_size":"243","bid_price":"0.101","bid_size":"514"},{"market":"bittrex-XRP-10AUG23-8000-C-option","time":"2023-08-10T00:00:00.000000000Z","coin_metrics_id":"1691625600000000-0","ask_price":"0.1045","ask_size":"243","bid_price":"0.101","bid_size":"514"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-quotes?markets=bittrex-XRP-10AUG23-8000-C-option&granularity=1d&start_time=2023-08-08T00:00:00Z&end_time=2023-08-10T00:00:00Z&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `test orderbook with granularity and timezone`() {
        getResponse("/v4/timeseries/market-quotes?markets=bittrex-xrp-usd-spot&granularity=1h&start_time=2023-08-09T03:00:00Z&end_time=2023-08-09T06:00:00Z&timezone=GMT%2b03:00&api_key=$TEST_API_KEY&paging_from=start").assertResponse()
    }

    @Test
    fun `test future with granularity and timezone`() {
        getResponse("/v4/timeseries/market-quotes?markets=bittrex-XRPUSD-future&granularity=1h&start_time=2023-08-09T03:00:00Z&end_time=2023-08-09T06:00:00Z&timezone=GMT%2b03:00&api_key=$TEST_API_KEY&paging_from=start").assertResponse()
    }

    @Test
    fun `test option with granularity and timezone`() {
        getResponse("/v4/timeseries/market-quotes?markets=bittrex-XRP-10AUG23-8000-C-option&granularity=1h&start_time=2023-08-09T03:00:00Z&end_time=2023-08-09T06:00:00Z&timezone=GMT%2b03:00&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `test quotes with multiple frequencies`() {
        assertResponse(
            400,
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'granularity'. Value '10s,1m' is not supported. Supported values are 'raw', '1m', '1h', '1d'."}}""",
            "/v4/timeseries/market-quotes?markets=bittrex-xrp-usd-spot&granularity=10s,1m&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `test quotes with unsupported granularity`() {
        assertResponse(
            400,
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'granularity'. Value '1s' is not supported. Supported values are 'raw', '1m', '1h', '1d'."}}""",
            "/v4/timeseries/market-quotes?markets=bittrex-xrp-usd-spot&granularity=1s&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `test all options`() {
        getResponse("/v4/timeseries/market-quotes?markets=*-option&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `test all futures`() {
        getResponse("/v4/timeseries/market-quotes?markets=*-future&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `given raw granularity when paging from end`() {
        getResponse("/v4/timeseries/market-quotes?markets=deribit-ETH-1OCT21-2850-P-option&granularity=raw&paging_from=end&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `given raw granularity when paging from start`() {
        getResponse("/v4/timeseries/market-quotes?markets=deribit-ETH-1OCT21-2850-P-option&granularity=raw&paging_from=start&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `given 1m granularity when paging from end`() {
        getResponse("/v4/timeseries/market-quotes?markets=deribit-ETH-1OCT21-2850-P-option&granularity=1m&paging_from=end&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `given 1m granularity when paging from start`() {
        getResponse("/v4/timeseries/market-quotes?markets=deribit-ETH-1OCT21-2850-P-option&granularity=1m&paging_from=start&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `given 1h granularity when paging from end`() {
        getResponse("/v4/timeseries/market-quotes?markets=bittrex-XRP-10AUG23-8000-C-option&granularity=1h&paging_from=end&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `given 1h granularity when paging from start`() {
        getResponse("/v4/timeseries/market-quotes?markets=bittrex-XRP-10AUG23-8000-C-option&granularity=1h&paging_from=start&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `given 1d granularity when paging from end`() {
        getResponse("/v4/timeseries/market-quotes?markets=bittrex-XRP-10AUG23-8000-C-option&granularity=1d&paging_from=end&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `given 1d granularity when paging from start`() {
        getResponse("/v4/timeseries/market-quotes?markets=bittrex-XRP-10AUG23-8000-C-option&granularity=1d&paging_from=start&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `given 1m granularity when requested time range falls outside depth 100 statistics range`() {
        getResponse("/v4/timeseries/market-quotes?markets=bittrex-XRPUSD-future&granularity=1m&start_time=2023-08-09T23:00:00&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `given 1m granularity and depth limit 1 and page size when paging from start`() {
        getResponse("/v4/timeseries/market-quotes?markets=bittrex-XRPUSD-future&granularity=1m&page_size=4&paging_from=start&api_key=$TEST_API_KEY").assertResponse("1")

        getResponse("/v4/timeseries/market-quotes?markets=bittrex-XRPUSD-future&granularity=1m&page_size=4&paging_from=start&api_key=$TEST_API_KEY&next_page_token=0.MjAyMy0wOC0wOVQwMjowMDowMFo").assertResponse("2")
    }

    @Test
    fun `given 1m granularity and depth limit 1 and page size when paging from end`() {
        getResponse("/v4/timeseries/market-quotes?markets=bittrex-XRPUSD-future&granularity=1m&page_size=4&paging_from=end&api_key=$TEST_API_KEY").assertResponse("1")

        getResponse("/v4/timeseries/market-quotes?markets=bittrex-XRPUSD-future&granularity=1m&page_size=4&paging_from=end&api_key=$TEST_API_KEY&next_page_token=0.MjAyMy0wOC0wOVQwMTowMDowMFo").assertResponse("2")
    }

    @Test
    fun `given no missing values during downsampling with raw granularity and paging from start`() {
        getResponse("/v4/timeseries/market-quotes?markets=coinbase-btc-usd-spot&start_time=2025-05-21T00:00:00&end_time=2025-05-22T00:00:00&paging_from=start&page_size=50&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `given no missing values during downsampling with 1h granularity and paging from start`() {
        getResponse("/v4/timeseries/market-quotes?markets=coinbase-btc-usd-spot&granularity=1h&start_time=2025-05-21T00:00:00&end_time=2025-05-22T00:00:00&paging_from=start&page_size=50&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `given no missing values during downsampling with 1d granularity and paging from start`() {
        getResponse("/v4/timeseries/market-quotes?markets=coinbase-btc-usd-spot&granularity=1d&start_time=2025-05-21T00:00:00&end_time=2025-05-22T00:00:00&paging_from=start&page_size=50&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `given no missing values during downsampling with raw granularity and paging from end`() {
        getResponse("/v4/timeseries/market-quotes?markets=coinbase-btc-usd-spot&start_time=2025-05-21T00:00:00&end_time=2025-05-22T00:00:00&paging_from=end&page_size=50&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `given no missing values during downsampling with 1h granularity and paging from end`() {
        getResponse("/v4/timeseries/market-quotes?markets=coinbase-btc-usd-spot&granularity=1h&start_time=2025-05-21T00:00:00&end_time=2025-05-22T00:00:00&paging_from=end&page_size=50&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `given no missing values during downsampling with 1d granularity and paging from end`() {
        getResponse("/v4/timeseries/market-quotes?markets=coinbase-btc-usd-spot&granularity=1d&start_time=2025-05-21T00:00:00&end_time=2025-05-22T00:00:00&paging_from=end&page_size=50&api_key=$TEST_API_KEY").assertResponse()
    }
}
