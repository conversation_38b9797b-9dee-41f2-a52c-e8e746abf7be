package io.coinmetrics.api.endpoints.timeseries.market

import io.coinmetrics.api.helper.BaseTest
import io.coinmetrics.api.helper.COMMUNITY_KEY
import io.coinmetrics.api.helper.EMPTY_RESPONSE
import io.coinmetrics.api.helper.TEST_API_KEY
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class TradesTest : BaseTest() {
    @Test
    fun `test trades error responses`() {
        listOf(
            "bittrex-invalid-asset-spot",
            "nonexistingexhange-btc-usd-spot",
        ).forEach { marketId ->
            assertResponse(
                400,
                """{"error":{"type":"bad_parameter","message":"Bad parameter 'markets'. Market '$marketId' is not supported."}}""",
                "/v4/timeseries/market-trades?markets=$marketId&api_key=$TEST_API_KEY",
            )
        }

        listOf(
            "bittrex-btc-usd-test-spot",
            "bitmex-xbtusd-spot",
            "bittrex-btc-usd",
        ).forEach { marketId ->
            assertResponse(
                400,
                """{"error":{"type":"bad_parameter","message":"Bad parameter 'markets'. Incorrect market '$marketId'."}}""",
                "/v4/timeseries/market-trades?markets=$marketId&api_key=$TEST_API_KEY",
            )
        }

        listOf(
            "201912-23",
            "20191223T00:00:05.000Z",
            "2019-12-23T00:00:05000Z",
        ).forEach { start_time ->
            assertResponse(
                400,
                """{"error":{"type":"bad_parameter","message":"Bad parameter 'start_time'. Incorrect time format '$start_time'. Supported formats are 'yyyy-MM-dd', 'yyyyMMdd', 'yyyy-MM-ddTHH:mm:ss', 'yyyy-MM-ddTHHmmss', 'yyyy-MM-ddTHH:mm:ss.SSS', 'yyyy-MM-ddTHHmmss.SSS', 'yyyy-MM-ddTHH:mm:ss.SSSSSS', 'yyyy-MM-ddTHHmmss.SSSSSS', 'yyyy-MM-ddTHH:mm:ss.SSSSSSSSS', 'yyyy-MM-ddTHHmmss.SSSSSSSSS'."}}""".trimMargin(),
                "/v4/timeseries/market-trades?markets=bittrex-btc-usd-spot&api_key=$TEST_API_KEY&start_time=$start_time",
            )
        }
    }

    @Test
    fun `test trades with no parameters`() {
        val expectedResponse =
            """{"data":[{"market":"bittrex-btc-usd-spot","time":"2019-03-15T09:51:28.851000000Z","coin_metrics_id":"3","amount":"0.1000000000000001","price":"1.00001","database_time":"2019-12-21T10:56:24.510288000Z","side":"buy"},{"market":"bittrex-btc-usd-spot","time":"2019-03-15T10:33:23.804000000Z","coin_metrics_id":"9","amount":"0.3","price":"1.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"sell"},{"market":"bittrex-btc-usd-spot","time":"2019-03-15T10:36:02.701000000Z","coin_metrics_id":"8","amount":"0.1","price":"4.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"buy"},{"market":"bittrex-btc-usd-spot","time":"2019-03-15T10:36:24.010000000Z","coin_metrics_id":"5","amount":"0.1","price":"1.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"buy"},{"market":"bittrex-btc-usd-spot","time":"2019-03-15T10:36:31.803000000Z","coin_metrics_id":"7","amount":"0.2","price":"1.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"buy"},{"market":"bittrex-btc-usd-spot","time":"2019-03-15T10:36:38.926000000Z","coin_metrics_id":"6","amount":"0.1","price":"1.0000100000000000001","database_time":"2019-12-21T10:56:24.918845000Z","side":"buy"},{"market":"bittrex-btc-usd-spot","time":"2019-03-15T10:36:40.247000000Z","coin_metrics_id":"1","amount":"0.1","price":"1.00001","database_time":"2019-12-21T10:56:24.418065000Z","side":"buy"},{"market":"bittrex-btc-usd-spot","time":"2019-03-15T10:36:46.275000000Z","coin_metrics_id":"2","amount":"0.1","price":"1.00001","database_time":"2019-12-21T10:56:24.418065000Z","side":"sell"},{"market":"bittrex-btc-usd-spot","time":"2019-03-15T10:36:46.428000000Z","coin_metrics_id":"4","amount":"0.1","price":"2.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"sell"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-trades?markets=bittrex-btc-usd-spot&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `should return trades in JSON format`() {
        val expectedResponse =
            """{"data":[{"market":"bittrex-btc-usd-spot","time":"2019-03-15T09:51:28.851000000Z","coin_metrics_id":"3","amount":"0.1000000000000001","price":"1.00001","database_time":"2019-12-21T10:56:24.510288000Z","side":"buy"},{"market":"bittrex-btc-usd-spot","time":"2019-03-15T10:33:23.804000000Z","coin_metrics_id":"9","amount":"0.3","price":"1.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"sell"},{"market":"bittrex-btc-usd-spot","time":"2019-03-15T10:36:02.701000000Z","coin_metrics_id":"8","amount":"0.1","price":"4.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"buy"},{"market":"bittrex-btc-usd-spot","time":"2019-03-15T10:36:24.010000000Z","coin_metrics_id":"5","amount":"0.1","price":"1.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"buy"},{"market":"bittrex-btc-usd-spot","time":"2019-03-15T10:36:31.803000000Z","coin_metrics_id":"7","amount":"0.2","price":"1.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"buy"},{"market":"bittrex-btc-usd-spot","time":"2019-03-15T10:36:38.926000000Z","coin_metrics_id":"6","amount":"0.1","price":"1.0000100000000000001","database_time":"2019-12-21T10:56:24.918845000Z","side":"buy"},{"market":"bittrex-btc-usd-spot","time":"2019-03-15T10:36:40.247000000Z","coin_metrics_id":"1","amount":"0.1","price":"1.00001","database_time":"2019-12-21T10:56:24.418065000Z","side":"buy"},{"market":"bittrex-btc-usd-spot","time":"2019-03-15T10:36:46.275000000Z","coin_metrics_id":"2","amount":"0.1","price":"1.00001","database_time":"2019-12-21T10:56:24.418065000Z","side":"sell"},{"market":"bittrex-btc-usd-spot","time":"2019-03-15T10:36:46.428000000Z","coin_metrics_id":"4","amount":"0.1","price":"2.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"sell"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-trades?markets=bittrex-btc-usd-spot&format=json&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `test trades with no parameters futures`() {
        val expectedResponse =
            """{"data":[{"market":"bitmex-XBTUSD-future","time":"2019-03-15T09:51:28.851000000Z","coin_metrics_id":"3","amount":"0.2000000000000001","price":"2.00001","database_time":"2019-12-21T10:56:24.510288000Z","side":"buy"},{"market":"bitmex-XBTUSD-future","time":"2019-03-15T10:36:38.926000000Z","coin_metrics_id":"6","amount":"0.2","price":"3.0000100000000000001","database_time":"2019-12-21T10:56:24.918845000Z","side":"buy"},{"market":"bitmex-XBTUSD-future","time":"2019-03-16T10:33:23.804000000Z","coin_metrics_id":"9","amount":"0.2","price":"3.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"sell"},{"market":"bitmex-XBTUSD-future","time":"2019-03-16T10:36:01.000000000Z","coin_metrics_id":"7","amount":"0.2","price":"3.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"buy"},{"market":"bitmex-XBTUSD-future","time":"2019-03-16T10:36:02.999999000Z","coin_metrics_id":"8","amount":"0.2","price":"4.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"buy"},{"market":"bitmex-XBTUSD-future","time":"2019-03-16T10:36:24.010000000Z","coin_metrics_id":"5","amount":"0.2","price":"2.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"buy"},{"market":"bitmex-XBTUSD-future","time":"2019-03-16T10:36:40.247000000Z","coin_metrics_id":"1","amount":"0.2","price":"2.00001","database_time":"2019-12-21T10:56:24.418065000Z","side":"buy"},{"market":"bitmex-XBTUSD-future","time":"2019-03-16T10:36:46.275000000Z","coin_metrics_id":"2","amount":"0.2","price":"2.00001","database_time":"2019-12-21T10:56:24.418065000Z","side":"sell"},{"market":"bitmex-XBTUSD-future","time":"2019-03-16T10:36:46.428000000Z","coin_metrics_id":"4","amount":"0.2","price":"1.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"sell"},{"market":"bitmex-XBTUSD-future","time":"2019-03-19T23:59:59.999999000Z","coin_metrics_id":"10","amount":"0.34","price":"4.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"buy"},{"market":"bitmex-XBTUSD-future","time":"2019-03-20T00:00:00.000000000Z","coin_metrics_id":"11","amount":"0.53","price":"3.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"buy"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-trades?markets=bitmex-XBTUSD-future&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `test trades with no parameters cme futures`() {
        val expectedResponse =
            """{"data":[{"market":"cme-BTCQ1-future","time":"2020-03-15T10:36:46.428000000Z","coin_metrics_id":"38","amount":"0.1","price":"2.00001","database_time":"2021-12-21T10:56:24.918845000Z","side":"sell"},{"market":"cme-BTCQ1-future","time":"2020-03-15T10:36:46.428000000Z","coin_metrics_id":"39","amount":"0.2","price":"4.00001","database_time":"2021-12-21T10:56:24.918845000Z","side":"sell"},{"market":"cme-BTCQ1-future","time":"2021-03-15T10:36:46.428000000Z","coin_metrics_id":"41","amount":"0.3","price":"3.00001","database_time":"2021-12-21T10:56:24.918845000Z","side":"sell"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-trades?markets=cme-BTCQ1-future&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `test trades with no parameters cme futures no permissions`() {
        getResponse("/v4/timeseries/market-trades?markets=cme-BTCQ1-future&api_key=$COMMUNITY_KEY").assertResponse()
    }

    @Test
    fun `test trades with end_time and page_size`() {
        val expectedResponse =
            """{"data":[{"market":"bittrex-btc-usd-spot","time":"2019-03-15T09:51:28.851000000Z","coin_metrics_id":"3","amount":"0.1000000000000001","price":"1.00001","database_time":"2019-12-21T10:56:24.510288000Z","side":"buy"},{"market":"bittrex-btc-usd-spot","time":"2019-03-15T10:33:23.804000000Z","coin_metrics_id":"9","amount":"0.3","price":"1.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"sell"}],"next_page_token":"0.MjAxOS0wMy0xNVQxMDozMzoyMy44MDRafDk","next_page_url":"http://127.0.0.1:8080/v4/timeseries/market-trades?markets=bittrex-btc-usd-spot&api_key=x1&start_time=20180819&page_size=2&paging_from=start&next_page_token=0.MjAxOS0wMy0xNVQxMDozMzoyMy44MDRafDk"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-trades?markets=bittrex-btc-usd-spot&api_key=$TEST_API_KEY&start_time=20180819&page_size=2&paging_from=start",
        )
    }

    @Test
    fun `test trades with end_time, paging_from and page_size, empty`() {
        val expectedResponse =
            """{"data":[]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-trades?markets=bittrex-btc-usd-spot&api_key=$TEST_API_KEY&end_time=20180819&page_size=2",
        )
    }

    @Test
    fun `test trades with end_time and paging_from`() {
        val expectedResponse =
            """{"data":[{"market":"bittrex-btc-usd-spot","time":"2019-03-15T09:51:28.851000000Z","coin_metrics_id":"3","amount":"0.1000000000000001","price":"1.00001","database_time":"2019-12-21T10:56:24.510288000Z","side":"buy"},{"market":"bittrex-btc-usd-spot","time":"2019-03-15T10:33:23.804000000Z","coin_metrics_id":"9","amount":"0.3","price":"1.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"sell"},{"market":"bittrex-btc-usd-spot","time":"2019-03-15T10:36:02.701000000Z","coin_metrics_id":"8","amount":"0.1","price":"4.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"buy"},{"market":"bittrex-btc-usd-spot","time":"2019-03-15T10:36:24.010000000Z","coin_metrics_id":"5","amount":"0.1","price":"1.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"buy"},{"market":"bittrex-btc-usd-spot","time":"2019-03-15T10:36:31.803000000Z","coin_metrics_id":"7","amount":"0.2","price":"1.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"buy"},{"market":"bittrex-btc-usd-spot","time":"2019-03-15T10:36:38.926000000Z","coin_metrics_id":"6","amount":"0.1","price":"1.0000100000000000001","database_time":"2019-12-21T10:56:24.918845000Z","side":"buy"},{"market":"bittrex-btc-usd-spot","time":"2019-03-15T10:36:40.247000000Z","coin_metrics_id":"1","amount":"0.1","price":"1.00001","database_time":"2019-12-21T10:56:24.418065000Z","side":"buy"},{"market":"bittrex-btc-usd-spot","time":"2019-03-15T10:36:46.275000000Z","coin_metrics_id":"2","amount":"0.1","price":"1.00001","database_time":"2019-12-21T10:56:24.418065000Z","side":"sell"},{"market":"bittrex-btc-usd-spot","time":"2019-03-15T10:36:46.428000000Z","coin_metrics_id":"4","amount":"0.1","price":"2.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"sell"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-trades?markets=bittrex-btc-usd-spot&api_key=$TEST_API_KEY&end_time=2019-12-23T00:00:05.000Z",
        )
    }

    @Test
    fun `test trades with end_time, paging_from and page_size`() {
        val expectedResponse =
            """{"data":[{"market":"bittrex-btc-usd-spot","time":"2019-03-15T10:36:40.247000000Z","coin_metrics_id":"1","amount":"0.1","price":"1.00001","database_time":"2019-12-21T10:56:24.418065000Z","side":"buy"},{"market":"bittrex-btc-usd-spot","time":"2019-03-15T10:36:46.275000000Z","coin_metrics_id":"2","amount":"0.1","price":"1.00001","database_time":"2019-12-21T10:56:24.418065000Z","side":"sell"},{"market":"bittrex-btc-usd-spot","time":"2019-03-15T10:36:46.428000000Z","coin_metrics_id":"4","amount":"0.1","price":"2.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"sell"}],"next_page_token":"0.MjAxOS0wMy0xNVQxMDozNjo0MC4yNDdafDE","next_page_url":"http://127.0.0.1:8080/v4/timeseries/market-trades?markets=bittrex-btc-usd-spot&api_key=x1&end_time=2019-12-23T00%3A00%3A05.000Z&page_size=3&next_page_token=0.MjAxOS0wMy0xNVQxMDozNjo0MC4yNDdafDE"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-trades?markets=bittrex-btc-usd-spot&api_key=$TEST_API_KEY&end_time=2019-12-23T00:00:05.000Z&page_size=3",
        )
    }

    @Test
    fun `test trades with cut end_time, paging_from and page_size`() {
        val expectedResponse =
            """{"data":[{"market":"bittrex-btc-usd-spot","time":"2019-03-15T09:51:28.851000000Z","coin_metrics_id":"3","amount":"0.1000000000000001","price":"1.00001","database_time":"2019-12-21T10:56:24.510288000Z","side":"buy"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-trades?markets=bittrex-btc-usd-spot&api_key=$TEST_API_KEY&end_time=2019-03-15T09:51:28.851000Z&page_size=3",
        )
    }

    @Test
    fun `test trades with nanotime filtering`() {
        val expectedResponse =
            """{"data":[{"market":"bittrex-btc-usd-spot","time":"2019-03-15T10:36:46.428000000Z","coin_metrics_id":"4","amount":"0.1","price":"2.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"sell"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-trades?markets=bittrex-btc-usd-spot&api_key=$TEST_API_KEY&start_time=2019-03-15T10:36:46.428000000&end_time=2019-03-15T10:36:46.428000000&page_size=1",
        )
    }

    @Test
    fun `test trades with nanotime filtering 2`() {
        val expectedResponse =
            """{"data":[]}"""
        // trade with time '2019-03-15T10:36:46.428000000Z' should not be included in the response
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-trades?markets=bittrex-btc-usd-spot&api_key=$TEST_API_KEY&start_time=2019-03-15T10:36:46.428000001&end_time=2019-03-15T10:36:46.428000001&page_size=1",
        )
    }

    @Test
    fun `test trades with incorrect key`() {
        val expectedResponse =
            """{"error":{"type":"wrong_credentials","message":"Supplied credentials are not valid."}}"""
        assertResponse(
            401,
            expectedResponse,
            "/v4/timeseries/market-trades?markets=bittrex-btc-usd-spot&api_key=invalid_key&end_time=20191223&page_size=2",
        )
    }

    @Test
    fun `test trades with key without permissions`() {
        getResponse("/v4/timeseries/market-trades?markets=bittrex-btc-usd-spot&api_key=x2&end_time=20191223&page_size=2").assertResponse()
    }

    @Test
    fun `test trades with different timezones`() {
        val expectedResponse =
            """{"data":[{"market":"bittrex-btc-usd-spot","time":"2019-03-15T10:36:46.275000000Z","coin_metrics_id":"2","amount":"0.1","price":"1.00001","database_time":"2019-12-21T10:56:24.418065000Z","side":"sell"},{"market":"bittrex-btc-usd-spot","time":"2019-03-15T10:36:46.428000000Z","coin_metrics_id":"4","amount":"0.1","price":"2.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"sell"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-trades?markets=bittrex-btc-usd-spot&api_key=$TEST_API_KEY&start_time=2019-03-15T10:36:46.275&page_size=3&paging_from=start",
        )

        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-trades?markets=bittrex-btc-usd-spot&api_key=$TEST_API_KEY&start_time=2019-03-15T13:36:46.275&page_size=3&timezone=Europe/Istanbul",
        )

        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-trades?markets=bittrex-btc-usd-spot&api_key=$TEST_API_KEY&start_time=2019-03-15T06:36:46.275&page_size=3&timezone=America/New_York",
        )
    }

    @Test
    fun `test trades next page from end and page_size = 1`() {
        val expectedResponsePage1 =
            """{"data":[{"market":"bittrex-btc-usd-spot","time":"2019-03-15T10:36:46.428000000Z","coin_metrics_id":"4","amount":"0.1","price":"2.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"sell"}],"next_page_token":"0.MjAxOS0wMy0xNVQxMDozNjo0Ni40MjhafDQ","next_page_url":"http://127.0.0.1:8080/v4/timeseries/market-trades?markets=bittrex-btc-usd-spot&api_key=x1&start_time=2019-03-15T10%3A36%3A46.275&page_size=1&next_page_token=0.MjAxOS0wMy0xNVQxMDozNjo0Ni40MjhafDQ"}"""
        assertResponse(
            200,
            expectedResponsePage1,
            "/v4/timeseries/market-trades?markets=bittrex-btc-usd-spot&api_key=$TEST_API_KEY&start_time=2019-03-15T10:36:46.275&page_size=1",
        )

        val expectedResponsePage2 =
            """{"data":[{"market":"bittrex-btc-usd-spot","time":"2019-03-15T10:36:46.275000000Z","coin_metrics_id":"2","amount":"0.1","price":"1.00001","database_time":"2019-12-21T10:56:24.418065000Z","side":"sell"}]}"""

        assertResponse(
            200,
            expectedResponsePage2,
            "/v4/timeseries/market-trades?markets=bittrex-btc-usd-spot&api_key=x1&start_time=2019-03-15T10%3A36%3A46.275&page_size=1&next_page_token=0.MjAxOS0wMy0xNVQxMDozNjo0Ni40MjhafDQ",
        )
    }

    @Test
    fun `test trades next page from start and page_size = 2`() {
        val expectedResponsePage1 =
            """{"data":[{"market":"bittrex-btc-usd-spot","time":"2019-03-15T10:36:40.247000000Z","coin_metrics_id":"1","amount":"0.1","price":"1.00001","database_time":"2019-12-21T10:56:24.418065000Z","side":"buy"},{"market":"bittrex-btc-usd-spot","time":"2019-03-15T10:36:46.275000000Z","coin_metrics_id":"2","amount":"0.1","price":"1.00001","database_time":"2019-12-21T10:56:24.418065000Z","side":"sell"}],"next_page_token":"0.MjAxOS0wMy0xNVQxMDozNjo0Ni4yNzVafDI","next_page_url":"http://127.0.0.1:8080/v4/timeseries/market-trades?markets=bittrex-btc-usd-spot&api_key=x1&start_time=2019-03-15T10%3A36%3A40.247&page_size=2&paging_from=start&next_page_token=0.MjAxOS0wMy0xNVQxMDozNjo0Ni4yNzVafDI"}"""
        assertResponse(
            200,
            expectedResponsePage1,
            "/v4/timeseries/market-trades?markets=bittrex-btc-usd-spot&api_key=$TEST_API_KEY&start_time=2019-03-15T10:36:40.247&page_size=2&paging_from=start",
        )

        val expectedResponsePage2 =
            """{"data":[{"market":"bittrex-btc-usd-spot","time":"2019-03-15T10:36:46.428000000Z","coin_metrics_id":"4","amount":"0.1","price":"2.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"sell"}]}"""

        assertResponse(
            200,
            expectedResponsePage2,
            "/v4/timeseries/market-trades?markets=bittrex-btc-usd-spot&api_key=x1&start_time=2019-03-15T10%3A36%3A40.247&page_size=2&paging_from=start&next_page_token=0.MjAxOS0wMy0xNVQxMDozNjo0Ni4yNzVafDI",
        )
    }

    @Test
    fun `test trades next page not present but number of elments on the page equal to page_size`() {
        val expectedResponse =
            """{"data":[{"market":"bittrex-btc-usd-spot","time":"2019-03-15T10:36:40.247000000Z","coin_metrics_id":"1","amount":"0.1","price":"1.00001","database_time":"2019-12-21T10:56:24.418065000Z","side":"buy"},{"market":"bittrex-btc-usd-spot","time":"2019-03-15T10:36:46.275000000Z","coin_metrics_id":"2","amount":"0.1","price":"1.00001","database_time":"2019-12-21T10:56:24.418065000Z","side":"sell"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-trades?markets=bittrex-btc-usd-spot&api_key=$TEST_API_KEY&start_time=2019-03-15T10:36:40.247&end_time=2019-03-15T10:36:46.427&page_size=2",
        )
    }

    @Test
    fun `test trades invalid next page token`() {
        listOf(
            "MjAxOS0wMy0xNVQxMDozNjo0Ni4yNzVafDII",
            "invalid_token",
            "MjAxOS0wMy0xNVQxMDoZNjo0Ni4yNzVafDI",
            "1111111111111",
            "2",
        ).forEach { nextPageToken ->
            assertResponse(
                400,
                """{"error":{"type":"bad_parameter","message":"Bad parameter 'next_page_token'."}}""",
                "/v4/timeseries/market-trades?markets=bittrex-btc-usd-spot&api_key=$TEST_API_KEY&start_time=2019-03-15T10%3A36%3A40.247&page_size=2&next_page_token=$nextPageToken",
            )
        }
    }

    @Test
    fun `test trades invalid page size`() {
        assertResponse(
            400,
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'page_size'. Must be at least 1."}}""",
            "/v4/timeseries/market-trades?markets=bittrex-btc-usd-spot&api_key=$TEST_API_KEY&page_size=-1",
        )
        assertResponse(
            400,
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'page_size'. Must be at most 10000."}}""",
            "/v4/timeseries/market-trades?markets=bittrex-btc-usd-spot&api_key=$TEST_API_KEY&page_size=100000",
        )

        listOf(
            100_000_000_000,
            -100_000_000_000,
        ).forEach { pageSize ->
            assertResponse(
                400,
                """{"error":{"type":"bad_parameter","message":"Bad parameter 'page_size'."}}""",
                "/v4/timeseries/market-trades?markets=bittrex-btc-usd-spot&api_key=$TEST_API_KEY&page_size=$pageSize",
            )
        }
    }

    @Test
    fun `test trades invalid paging_from`() {
        listOf(
            "f",
            "forward",
            "endd",
            "startting",
            "backwards",
            "none",
        ).forEach { pagingFrom ->
            assertResponse(
                400,
                """{"error":{"type":"bad_parameter","message":"Bad parameter 'paging_from'. Value '$pagingFrom' is not supported. Supported values are 'start', 'end'."}}""",
                "/v4/timeseries/market-trades?markets=bittrex-btc-usd-spot&api_key=$TEST_API_KEY&paging_from=$pagingFrom",
            )
        }
    }

    @Test
    fun `test trades empty paging_from`() {
        getResponse("/v4/timeseries/market-trades?markets=bittrex-btc-usd-spot&api_key=$TEST_API_KEY&paging_from=").assertResponse()
    }

    @Test
    fun `test trades valid timestamp ceiling for end_time and flooring for start_time sub second`() {
        val expectedResponse =
            """{"data":[{"market":"bitmex-XBTUSD-future","time":"2019-03-16T10:36:01.000000000Z","coin_metrics_id":"7","amount":"0.2","price":"3.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"buy"},{"market":"bitmex-XBTUSD-future","time":"2019-03-16T10:36:02.999999000Z","coin_metrics_id":"8","amount":"0.2","price":"4.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"buy"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-trades?markets=bitmex-XBTUSD-future&api_key=$TEST_API_KEY&start_time=2019-03-16T10:36:01&end_time=2019-03-16T10:36:02&page_size=2&paging_from=start",
        )
    }

    @Test
    fun `test trades valid timestamp ceiling for end_time and flooring for start_time sub day`() {
        val expectedResponse =
            """{"data":[{"market":"bitmex-XBTUSD-future","time":"2019-03-19T23:59:59.999999000Z","coin_metrics_id":"10","amount":"0.34","price":"4.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"buy"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-trades?markets=bitmex-XBTUSD-future&api_key=$TEST_API_KEY&start_time=2019-03-19&end_time=2019-03-19&page_size=2",
        )
    }

    @Test
    fun `test trades invalid start, end configuration`() {
        listOf(
            "2019-01-01" to "2018-12-31",
            "2019-01-01T01:01:01" to "2019-01-01T01:01:00",
            "2019-01-01T01:01:01.000002" to "2019-01-01T01:01:01.000001",
        ).forEach { (startTime, endTime) ->
            assertResponse(
                400,
                """{"error":{"type":"bad_parameter","message":"Bad parameter 'start_time'. Start time '$startTime' is later than the end time '$endTime'."}}""",
                "/v4/timeseries/market-trades?markets=bittrex-btc-usd-spot&api_key=$TEST_API_KEY&start_time=$startTime&end_time=$endTime",
            )
        }

        assertResponse(
            400,
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'start_time'. Start time '1970-01-01' is later than the end time '1969-12-31'."}}""",
            "/v4/timeseries/market-trades?markets=bittrex-btc-usd-spot&api_key=$TEST_API_KEY&end_time=1969-12-31",
        )
        assertResponse(
            400,
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'start_time'. Start time '2081-01-01' is later than the end time 'now'."}}""",
            "/v4/timeseries/market-trades?markets=bittrex-btc-usd-spot&api_key=$TEST_API_KEY&start_time=2081-01-01",
        )
    }

    @Test
    fun `test trades start inclusive, end exclusive`() {
        val expectedResponse =
            """{"data":[{"market":"bittrex-btc-usd-spot","time":"2019-03-15T10:36:40.247000000Z","coin_metrics_id":"1","amount":"0.1","price":"1.00001","database_time":"2019-12-21T10:56:24.418065000Z","side":"buy"},{"market":"bittrex-btc-usd-spot","time":"2019-03-15T10:36:46.275000000Z","coin_metrics_id":"2","amount":"0.1","price":"1.00001","database_time":"2019-12-21T10:56:24.418065000Z","side":"sell"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-trades?markets=bittrex-btc-usd-spot&api_key=$TEST_API_KEY&start_time=2019-03-15T10:36:40.247&end_time=2019-03-15T10:36:46.428&page_size=3&end_inclusive=false",
        )
    }

    @Test
    fun `test trades start exclusive, end inclusive`() {
        val expectedResponse =
            """{"data":[{"market":"bittrex-btc-usd-spot","time":"2019-03-15T10:36:46.275000000Z","coin_metrics_id":"2","amount":"0.1","price":"1.00001","database_time":"2019-12-21T10:56:24.418065000Z","side":"sell"},{"market":"bittrex-btc-usd-spot","time":"2019-03-15T10:36:46.428000000Z","coin_metrics_id":"4","amount":"0.1","price":"2.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"sell"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-trades?markets=bittrex-btc-usd-spot&api_key=$TEST_API_KEY&start_time=2019-03-15T10:36:40.247&end_time=2019-03-15T10:36:46.428&page_size=3&start_inclusive=false",
        )
    }

    @Test
    fun `test trades start exclusive, end exclusive`() {
        val expectedResponse =
            """{"data":[{"market":"bittrex-btc-usd-spot","time":"2019-03-15T10:36:46.275000000Z","coin_metrics_id":"2","amount":"0.1","price":"1.00001","database_time":"2019-12-21T10:56:24.418065000Z","side":"sell"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-trades?markets=bittrex-btc-usd-spot&api_key=$TEST_API_KEY&start_time=2019-03-15T10:36:40.247&end_time=2019-03-15T10:36:46.428&page_size=3&start_inclusive=false&end_inclusive=false",
        )
    }

    @Test
    fun `test option trades`() {
        val expectedResponse =
            """{"data":[{"market":"deribit-BTC-9APR21-50000-P-option","time":"2019-03-15T09:51:28.851000000Z","coin_metrics_id":"3","amount":"0.2000000000000001","price":"2.00001","database_time":"2019-12-21T10:56:24.510288000Z","side":"buy"},{"market":"deribit-BTC-9APR21-50000-P-option","time":"2019-03-15T09:51:28.851000000Z","coin_metrics_id":"15","amount":"0.1000000000000001","price":"1.00001","database_time":"2019-12-21T10:56:24.510288000Z","side":"buy"},{"market":"deribit-BTC-9APR21-50000-P-option","time":"2019-03-15T10:36:38.926000000Z","coin_metrics_id":"6","amount":"0.2","price":"3.0000100000000000001","database_time":"2019-12-21T10:56:24.918845000Z","side":"buy"},{"market":"deribit-BTC-9APR21-50000-P-option","time":"2019-03-15T10:36:40.247000000Z","coin_metrics_id":"13","amount":"0.1","price":"1.00001","database_time":"2019-12-21T10:56:24.418065000Z","side":"buy"},{"market":"deribit-BTC-9APR21-50000-P-option","time":"2019-03-15T10:36:46.275000000Z","coin_metrics_id":"14","amount":"0.1","price":"1.00001","database_time":"2019-12-21T10:56:24.418065000Z","side":"sell"},{"market":"deribit-BTC-9APR21-50000-P-option","time":"2019-03-15T10:36:46.428000000Z","coin_metrics_id":"16","amount":"0.1","price":"2.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"sell"},{"market":"deribit-BTC-9APR21-50000-P-option","time":"2019-03-16T10:33:23.804000000Z","coin_metrics_id":"9","amount":"0.2","price":"3.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"sell"},{"market":"deribit-BTC-9APR21-50000-P-option","time":"2019-03-16T10:36:01.000000000Z","coin_metrics_id":"7","amount":"0.2","price":"3.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"buy"},{"market":"deribit-BTC-9APR21-50000-P-option","time":"2019-03-16T10:36:02.999999000Z","coin_metrics_id":"8","amount":"0.2","price":"4.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"buy"},{"market":"deribit-BTC-9APR21-50000-P-option","time":"2019-03-16T10:36:24.010000000Z","coin_metrics_id":"5","amount":"0.2","price":"2.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"buy"},{"market":"deribit-BTC-9APR21-50000-P-option","time":"2019-03-16T10:36:40.247000000Z","coin_metrics_id":"1","amount":"0.2","price":"2.00001","database_time":"2019-12-21T10:56:24.418065000Z","side":"buy"},{"market":"deribit-BTC-9APR21-50000-P-option","time":"2019-03-16T10:36:46.275000000Z","coin_metrics_id":"2","amount":"0.2","price":"2.00001","database_time":"2019-12-21T10:56:24.418065000Z","side":"sell"},{"market":"deribit-BTC-9APR21-50000-P-option","time":"2019-03-16T10:36:46.428000000Z","coin_metrics_id":"4","amount":"0.2","price":"1.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"sell"},{"market":"deribit-BTC-9APR21-50000-P-option","time":"2019-03-19T23:59:59.999999000Z","coin_metrics_id":"10","amount":"0.34","price":"4.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"buy"},{"market":"deribit-BTC-9APR21-50000-P-option","time":"2019-03-20T00:00:00.000000000Z","coin_metrics_id":"11","amount":"0.53","price":"3.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"buy"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-trades?markets=deribit-BTC-9APR21-50000-P-option&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `invalid market id`() {
        val expectedResponse =
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'markets'. Market 'bittrex-usd-btc-spot' is not supported."}}"""
        assertResponse(
            400,
            expectedResponse,
            "/v4/timeseries/market-trades?markets=bittrex-usd-btc-spot&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `multiple markets, page 1`() {
        val expectedResponse =
            """{"data":[{"market":"bitmex-XBTUSD-future","time":"2019-03-20T00:00:00.000000000Z","coin_metrics_id":"11","amount":"0.53","price":"3.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"buy"},{"market":"bittrex-btc-usd-spot","time":"2019-03-15T09:51:28.851000000Z","coin_metrics_id":"3","amount":"0.1000000000000001","price":"1.00001","database_time":"2019-12-21T10:56:24.510288000Z","side":"buy"},{"market":"bittrex-btc-usd-spot","time":"2019-03-15T10:33:23.804000000Z","coin_metrics_id":"9","amount":"0.3","price":"1.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"sell"},{"market":"bittrex-btc-usd-spot","time":"2019-03-15T10:36:02.701000000Z","coin_metrics_id":"8","amount":"0.1","price":"4.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"buy"},{"market":"bittrex-btc-usd-spot","time":"2019-03-15T10:36:24.010000000Z","coin_metrics_id":"5","amount":"0.1","price":"1.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"buy"},{"market":"bittrex-btc-usd-spot","time":"2019-03-15T10:36:31.803000000Z","coin_metrics_id":"7","amount":"0.2","price":"1.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"buy"},{"market":"bittrex-btc-usd-spot","time":"2019-03-15T10:36:38.926000000Z","coin_metrics_id":"6","amount":"0.1","price":"1.0000100000000000001","database_time":"2019-12-21T10:56:24.918845000Z","side":"buy"},{"market":"bittrex-btc-usd-spot","time":"2019-03-15T10:36:40.247000000Z","coin_metrics_id":"1","amount":"0.1","price":"1.00001","database_time":"2019-12-21T10:56:24.418065000Z","side":"buy"},{"market":"bittrex-btc-usd-spot","time":"2019-03-15T10:36:46.275000000Z","coin_metrics_id":"2","amount":"0.1","price":"1.00001","database_time":"2019-12-21T10:56:24.418065000Z","side":"sell"},{"market":"bittrex-btc-usd-spot","time":"2019-03-15T10:36:46.428000000Z","coin_metrics_id":"4","amount":"0.1","price":"2.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"sell"}],"next_page_token":"1.MjAxOS0wMy0yMFQwMDowMDowMFp8MTE","next_page_url":"http://127.0.0.1:8080/v4/timeseries/market-trades?markets=bittrex-btc-usd-spot,bitmex-XBTUSD-future&api_key=x1&page_size=10&next_page_token=1.MjAxOS0wMy0yMFQwMDowMDowMFp8MTE"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-trades?markets=bittrex-btc-usd-spot,bitmex-XBTUSD-future&api_key=$TEST_API_KEY&page_size=10",
        )
    }

    @Test
    fun `multiple markets with wildcard`() {
        val expectedResponse =
            """{"data":[{"market":"binance-XBTUSD-future","time":"2019-03-15T09:51:28.851000000Z","coin_metrics_id":"3","amount":"0.1000000000000001","price":"1.00001","database_time":"2019-12-21T10:56:24.510288000Z","side":"buy"},{"market":"binance-XBTUSD-future","time":"2019-03-15T10:36:40.247000000Z","coin_metrics_id":"1","amount":"0.1","price":"1.00001","database_time":"2019-12-21T10:56:24.418065000Z","side":"buy"},{"market":"binance-XBTUSD-future","time":"2019-03-15T10:36:46.275000000Z","coin_metrics_id":"2","amount":"0.1","price":"1.00001","database_time":"2019-12-21T10:56:24.418065000Z","side":"sell"},{"market":"binance-XBTUSD-future","time":"2019-03-15T10:36:46.428000000Z","coin_metrics_id":"4","amount":"0.1","price":"2.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"sell"},{"market":"bitmex-FILUSD-future","time":"2023-09-17T00:15:51.000000000Z","coin_metrics_id":"295272266791454803656457771505522275176","amount":"5","price":"3.3","database_time":"2023-09-17T00:15:52.000000000Z","side":"sell"},{"market":"bitmex-XBTUSD-future","time":"2019-03-15T09:51:28.851000000Z","coin_metrics_id":"3","amount":"0.2000000000000001","price":"2.00001","database_time":"2019-12-21T10:56:24.510288000Z","side":"buy"},{"market":"bitmex-XBTUSD-future","time":"2019-03-15T10:36:38.926000000Z","coin_metrics_id":"6","amount":"0.2","price":"3.0000100000000000001","database_time":"2019-12-21T10:56:24.918845000Z","side":"buy"},{"market":"bitmex-XBTUSD-future","time":"2019-03-16T10:33:23.804000000Z","coin_metrics_id":"9","amount":"0.2","price":"3.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"sell"},{"market":"bitmex-XBTUSD-future","time":"2019-03-16T10:36:01.000000000Z","coin_metrics_id":"7","amount":"0.2","price":"3.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"buy"},{"market":"bitmex-XBTUSD-future","time":"2019-03-16T10:36:02.999999000Z","coin_metrics_id":"8","amount":"0.2","price":"4.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"buy"},{"market":"bitmex-XBTUSD-future","time":"2019-03-16T10:36:24.010000000Z","coin_metrics_id":"5","amount":"0.2","price":"2.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"buy"},{"market":"bitmex-XBTUSD-future","time":"2019-03-16T10:36:40.247000000Z","coin_metrics_id":"1","amount":"0.2","price":"2.00001","database_time":"2019-12-21T10:56:24.418065000Z","side":"buy"},{"market":"bitmex-XBTUSD-future","time":"2019-03-16T10:36:46.275000000Z","coin_metrics_id":"2","amount":"0.2","price":"2.00001","database_time":"2019-12-21T10:56:24.418065000Z","side":"sell"},{"market":"bitmex-XBTUSD-future","time":"2019-03-16T10:36:46.428000000Z","coin_metrics_id":"4","amount":"0.2","price":"1.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"sell"},{"market":"bitmex-XBTUSD-future","time":"2019-03-19T23:59:59.999999000Z","coin_metrics_id":"10","amount":"0.34","price":"4.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"buy"},{"market":"bitmex-XBTUSD-future","time":"2019-03-20T00:00:00.000000000Z","coin_metrics_id":"11","amount":"0.53","price":"3.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"buy"},{"market":"cme-BTCQ1-future","time":"2020-03-15T10:36:46.428000000Z","coin_metrics_id":"38","amount":"0.1","price":"2.00001","database_time":"2021-12-21T10:56:24.918845000Z","side":"sell"},{"market":"cme-BTCQ1-future","time":"2020-03-15T10:36:46.428000000Z","coin_metrics_id":"39","amount":"0.2","price":"4.00001","database_time":"2021-12-21T10:56:24.918845000Z","side":"sell"},{"market":"cme-BTCQ1-future","time":"2021-03-15T10:36:46.428000000Z","coin_metrics_id":"41","amount":"0.3","price":"3.00001","database_time":"2021-12-21T10:56:24.918845000Z","side":"sell"},{"market":"deribit-ETH-7JUN24-future","time":"2024-05-30T11:04:48.000000000Z","coin_metrics_id":"209092169","amount":"6650","price":"3754.25","database_time":"2024-05-30T11:04:49.000000000Z","side":"sell","mark_price":"3753.77","index_price":"3742.9","liquidation":"ETH-7JUN24_liquidation"},{"market":"deribit-ETH-PERPETUAL-future","time":"2024-05-30T11:03:14.000000000Z","coin_metrics_id":"209092104","amount":"1","price":"3743.15","database_time":"2024-05-30T11:03:14.000000000Z","side":"buy","mark_price":"3743.48","index_price":"3742.51","iv_trade":"0.123"},{"market":"ftx-SOL-0925-future","time":"2019-03-15T10:36:46.428000000Z","coin_metrics_id":"6","amount":"0.1","price":"2.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"sell"},{"market":"huobi-XBTUSD4-future","time":"2019-03-15T10:36:31.852000000Z","coin_metrics_id":"10004731385326753984320","amount":"0.18","price":"0.00001267","database_time":"2019-12-21T10:56:24.918845000Z","side":"sell"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-trades?markets=*-future&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `markets with dots in the name`() {
        val expectedResponse =
            """{"data":[{"market":"binance.us-btc-usd-spot","time":"2019-05-15T10:00:00.000000000Z","coin_metrics_id":"7","amount":"0.1","price":"2.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"sell"}],"next_page_token":"YmluYW5jZS51cy1idGMtdXNkLXNwb3Q.MjAxOS0wNS0xNVQxMDowMDowMFp8Nw","next_page_url":"http://127.0.0.1:8080/v4/timeseries/market-trades?markets=binance.us-*&api_key=x1&page_size=1&next_page_token=YmluYW5jZS51cy1idGMtdXNkLXNwb3Q.MjAxOS0wNS0xNVQxMDowMDowMFp8Nw"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-trades?markets=binance.us-*&api_key=$TEST_API_KEY&page_size=1",
        )

        val expectedResponse2 =
            """{"data":[{"market":"binance.us-btc-usd-spot","time":"2019-05-15T10:00:00.000000000Z","coin_metrics_id":"6","amount":"0.1","price":"2.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"sell"}],"next_page_token":"YmluYW5jZS51cy1idGMtdXNkLXNwb3Q.MjAxOS0wNS0xNVQxMDowMDowMFp8Ng","next_page_url":"http://127.0.0.1:8080/v4/timeseries/market-trades?markets=binance.us-*&api_key=x1&page_size=1&next_page_token=YmluYW5jZS51cy1idGMtdXNkLXNwb3Q.MjAxOS0wNS0xNVQxMDowMDowMFp8Ng"}"""
        assertResponse(
            200,
            expectedResponse2,
            "/v4/timeseries/market-trades?markets=binance.us-*&api_key=x1&page_size=1&next_page_token=YmluYW5jZS51cy1idGMtdXNkLXNwb3Q.MjAxOS0wNS0xNVQxMDowMDowMFp8Nw",
        )
    }

    @Test
    fun `test bad next page token for markets pattern`() {
        val expectedResponse =
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'next_page_token'."}}"""
        assertResponse(
            400,
            expectedResponse,
            "/v4/timeseries/market-trades?markets=binance.us-*&api_key=x1&page_size=1&next_page_token=0.MjAxOS0wNS0xNVQxMDowMDowMFp8Nw",
        )
    }

    @Test
    fun `multiple markets, page 2`() {
        val expectedResponse =
            """{"data":[{"market":"bitmex-XBTUSD-future","time":"2019-03-15T09:51:28.851000000Z","coin_metrics_id":"3","amount":"0.2000000000000001","price":"2.00001","database_time":"2019-12-21T10:56:24.510288000Z","side":"buy"},{"market":"bitmex-XBTUSD-future","time":"2019-03-15T10:36:38.926000000Z","coin_metrics_id":"6","amount":"0.2","price":"3.0000100000000000001","database_time":"2019-12-21T10:56:24.918845000Z","side":"buy"},{"market":"bitmex-XBTUSD-future","time":"2019-03-16T10:33:23.804000000Z","coin_metrics_id":"9","amount":"0.2","price":"3.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"sell"},{"market":"bitmex-XBTUSD-future","time":"2019-03-16T10:36:01.000000000Z","coin_metrics_id":"7","amount":"0.2","price":"3.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"buy"},{"market":"bitmex-XBTUSD-future","time":"2019-03-16T10:36:02.999999000Z","coin_metrics_id":"8","amount":"0.2","price":"4.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"buy"},{"market":"bitmex-XBTUSD-future","time":"2019-03-16T10:36:24.010000000Z","coin_metrics_id":"5","amount":"0.2","price":"2.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"buy"},{"market":"bitmex-XBTUSD-future","time":"2019-03-16T10:36:40.247000000Z","coin_metrics_id":"1","amount":"0.2","price":"2.00001","database_time":"2019-12-21T10:56:24.418065000Z","side":"buy"},{"market":"bitmex-XBTUSD-future","time":"2019-03-16T10:36:46.275000000Z","coin_metrics_id":"2","amount":"0.2","price":"2.00001","database_time":"2019-12-21T10:56:24.418065000Z","side":"sell"},{"market":"bitmex-XBTUSD-future","time":"2019-03-16T10:36:46.428000000Z","coin_metrics_id":"4","amount":"0.2","price":"1.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"sell"},{"market":"bitmex-XBTUSD-future","time":"2019-03-19T23:59:59.999999000Z","coin_metrics_id":"10","amount":"0.34","price":"4.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"buy"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-trades?markets=bittrex-btc-usd-spot,bitmex-XBTUSD-future&api_key=$TEST_API_KEY&page_size=10&next_page_token=1.MjAxOS0wMy0yMFQwMDowMDowMFp8MTE",
        )
    }

    @Test
    fun `multiple markets, limit_per_market`() {
        val expectedResponse =
            """{"data":[{"market":"bitmex-XBTUSD-future","time":"2019-03-20T00:00:00.000000000Z","coin_metrics_id":"11","amount":"0.53","price":"3.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"buy"},{"market":"bittrex-btc-usd-spot","time":"2019-03-15T10:36:46.428000000Z","coin_metrics_id":"4","amount":"0.1","price":"2.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"sell"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-trades?markets=bittrex-btc-usd-spot,bitmex-XBTUSD-future&api_key=$TEST_API_KEY&limit_per_market=1",
        )
    }

    @Test
    fun `the same time paging`() {
        val expectedResponse =
            """{"data":[{"market":"binance.us-btc-usd-spot","time":"2019-03-15T10:36:46.428000000Z","coin_metrics_id":"4","amount":"0.1","price":"2.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"sell"},{"market":"binance.us-btc-usd-spot","time":"2019-05-15T10:00:00.000000000Z","coin_metrics_id":"5","amount":"0.1","price":"2.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"sell"}],"next_page_token":"0.MjAxOS0wNS0xNVQxMDowMDowMFp8NQ","next_page_url":"http://127.0.0.1:8080/v4/timeseries/market-trades?markets=binance.us-btc-usd-spot&api_key=x1&paging_from=start&page_size=2&next_page_token=0.MjAxOS0wNS0xNVQxMDowMDowMFp8NQ"}""".trimIndent()
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-trades?markets=binance.us-btc-usd-spot&api_key=$TEST_API_KEY&paging_from=start&page_size=2",
        )
    }

    @Test
    fun `the same time paging, page 2`() {
        val expectedResponse =
            """
            {"data":[{"market":"binance.us-btc-usd-spot","time":"2019-05-15T10:00:00.000000000Z","coin_metrics_id":"6","amount":"0.1","price":"2.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"sell"},{"market":"binance.us-btc-usd-spot","time":"2019-05-15T10:00:00.000000000Z","coin_metrics_id":"7","amount":"0.1","price":"2.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"sell"}]}
            """.trimIndent()
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-trades?markets=binance.us-btc-usd-spot&api_key=$TEST_API_KEY&paging_from=start&page_size=2&next_page_token=0.MjAxOS0wNS0xNVQxMDowMDowMFp8NQ",
        )
    }

    @Test
    fun `should return all trades for defi market`() {
        val expectedResponse =
            """{"data":[{"market":"uniswap_v2_eth-usdc-weth-spot","time":"2020-05-05T21:09:32.000000000Z","coin_metrics_id":"tradeId1","amount":"0.001","price":"0.004859228160162","database_time":"2023-10-18T15:09:56.414745000Z","side":"buy","block_hash":"4ba33a650f9e3d8430f94b61a382e60490ec7a06c2f4441ecf225858ec748b78","block_height":"10008566","txid":"932cb88306450d481a0e43365a3ed832625b68f036e9887684ef6da594891366","initiator":"8688a84fcfd84d8f78020d0fc0b35987cc58911f","sender":"f164fc0ec4e93095b804a4795bbe1e041497b92a","beneficiary":"8688a84fcfd84d8f78020d0fc0b35987cc58911f"},{"market":"uniswap_v2_eth-usdc-weth-spot","time":"2020-05-05T21:12:35.000000000Z","coin_metrics_id":"tradeId2","amount":"0.01","price":"0.0049132889602724","database_time":"2023-10-18T15:09:56.743846000Z","side":"buy","block_hash":"9eadcc046043be7c86e398492e3c935f5d410692b3524e78e9e2c79cf9c25462","block_height":"10008585","txid":"697b7aaca56a80a8d3a2f560ed7f1ecb97c22b2edd6eddc88ba4e89d96acf3c2","initiator":"11e4857bb9993a50c685a79afad4e6f65d518dda","sender":"f164fc0ec4e93095b804a4795bbe1e041497b92a","beneficiary":"11e4857bb9993a50c685a79afad4e6f65d518dda"},{"market":"uniswap_v2_eth-usdc-weth-spot","time":"2020-05-06T16:24:55.000000000Z","coin_metrics_id":"tradeId3","amount":"0.001","price":"0.004968141208004","database_time":"2023-10-18T15:09:56.743846000Z","side":"buy","block_hash":"fddbc9d9a6155c54f18f83d230996366f35ead00741e082fadc71c0497f5a6ea","block_height":"10013764","txid":"43b6bfd06dde0814fe9c1b63ce98ec4c67c72d96169d73e13bf2b409d0551b58","initiator":"8688a84fcfd84d8f78020d0fc0b35987cc58911f","sender":"f164fc0ec4e93095b804a4795bbe1e041497b92a","beneficiary":"8688a84fcfd84d8f78020d0fc0b35987cc58911f"},{"market":"uniswap_v2_eth-usdc-weth-spot","time":"2020-05-06T18:51:12.000000000Z","coin_metrics_id":"tradeId4","amount":"0.000687","price":"0.0049671007218573508006","database_time":"2023-10-18T15:09:56.743846000Z","side":"sell","block_hash":"e367e15f16985e1786f5e0f109a430769c1de0fda753a227704c126245ab521f","block_height":"10014418","txid":"735cf98e86a5df67b6a837ae50de1d7a589d9f6baaf0e1ac95e992884a182ce2","initiator":"8688a84fcfd84d8f78020d0fc0b35987cc58911f","sender":"f164fc0ec4e93095b804a4795bbe1e041497b92a","beneficiary":"12ede161c702d1494612d19f05992f43aa6a26fb"},{"market":"uniswap_v2_eth-usdc-weth-spot","time":"2020-05-11T13:08:48.000000000Z","coin_metrics_id":"tradeId5","amount":"0.20487","price":"0.004866500707765900327","database_time":"2023-10-18T15:09:56.743846000Z","side":"buy","block_hash":"73eec17715249e66db06a563b8f0706b4c3fcc9eb8e33ace498e8c6ccd138f61","block_height":"10045107","txid":"840bb5ab0a779b4f733443651ac54dabe24e243c36062183fdb69c511f9b0bb8","initiator":"4d37f28d2db99e8d35a6c725a5f1749a085850a3","sender":"57ead0a9f49fafdd2447f615b036f3c316af5171","beneficiary":"57ead0a9f49fafdd2447f615b036f3c316af5171"},{"market":"uniswap_v2_eth-usdc-weth-spot","time":"2020-05-11T13:09:05.000000000Z","coin_metrics_id":"tradeId6","amount":"0.192066","price":"0.0048764098355619682817","database_time":"2023-10-18T15:09:56.743846000Z","side":"sell","block_hash":"5134e422203b65b2d101472cf9462fbd8dc2360f31e5b77d4125eca5ef54d12e","block_height":"10045110","txid":"12c446896c4a75adaad9536e0753feb69139ef32bc688c99258acde2b48e1e70","initiator":"4d37f28d2db99e8d35a6c725a5f1749a085850a3","sender":"57ead0a9f49fafdd2447f615b036f3c316af5171","beneficiary":"57ead0a9f49fafdd2447f615b036f3c316af5171"},{"market":"uniswap_v2_eth-usdc-weth-spot","time":"2020-05-12T11:08:31.000000000Z","coin_metrics_id":"tradeId7","amount":"0.049736","price":"0.0046976139101826443622","database_time":"2023-10-18T15:09:56.743846000Z","side":"sell","block_hash":"80c66ffefc5759f6ecafa629b4f034ba3b097c6fbb97482594253f7783672d85","block_height":"10051015","txid":"b708aa3c13aa6c09e724dec65464981157b4e081863ffb12ed7584438b56b8bc","initiator":"42ea16c9b9e529da492909f34f416fed2be7c280","sender":"f164fc0ec4e93095b804a4795bbe1e041497b92a","beneficiary":"379b4609bdf93b3584cf7b64bc78199cf185f1cd"},{"market":"uniswap_v2_eth-usdc-weth-spot","time":"2020-05-12T11:46:00.000000000Z","coin_metrics_id":"tradeId8","amount":"0.866644","price":"0.0053594035848787795219","database_time":"2023-10-18T15:09:56.743846000Z","side":"buy","block_hash":"3c355014a9cdcdf578896aca02a6e998355a23c11cf48f2e7f6b91993058d424","block_height":"10051195","txid":"10d0c21b4722533ba9bf7f01ea7abd511f3f92fae8a376e8d1ad86491010beaf","initiator":"42ea16c9b9e529da492909f34f416fed2be7c280","sender":"f164fc0ec4e93095b804a4795bbe1e041497b92a","beneficiary":"379b4609bdf93b3584cf7b64bc78199cf185f1cd"},{"market":"uniswap_v2_eth-usdc-weth-spot","time":"2020-05-12T14:18:42.000000000Z","coin_metrics_id":"tradeId9","amount":"0.35074","price":"0.0065057465869482180533","database_time":"2023-10-18T15:09:56.743846000Z","side":"buy","block_hash":"efbf08c3469815d32666b8a187c9d25096355020b08ebc4853de3218d0f52199","block_height":"10051877","txid":"4f426c26d42a1df21d3752b27171c8ec1f39ae3c203ab4f1e99db5be32658bb2","initiator":"42ea16c9b9e529da492909f34f416fed2be7c280","sender":"f164fc0ec4e93095b804a4795bbe1e041497b92a","beneficiary":"379b4609bdf93b3584cf7b64bc78199cf185f1cd"},{"market":"uniswap_v2_eth-usdc-weth-spot","time":"2020-05-12T16:44:41.000000000Z","coin_metrics_id":"tradeId10","amount":"0.62606","price":"0.0075807371196965258921","database_time":"2023-10-18T15:09:56.743846000Z","side":"buy","block_hash":"8e81f544e3e307bce7e3f03e5089e343538f91a3e569ce5cd0e1a28ea192439c","block_height":"10052500","txid":"3d3302879f80cd62a4f58cf66624417af75016c9cedfa3b773c83f2c0698e553","initiator":"90c29fe72e67db90a8b3a66277414da21e116652","sender":"f164fc0ec4e93095b804a4795bbe1e041497b92a","beneficiary":"379b4609bdf93b3584cf7b64bc78199cf185f1cd"}]}""".trimIndent()
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-trades?markets=uniswap_v2_eth-usdc-weth-spot&api_key=$TEST_API_KEY&min_confirmations=0",
        )
    }

    @Test
    fun `should return all trades for a wildcard defi market`() {
        val expectedResponse =
            """{"data":[{"market":"curve_eth-2-ageur_eth-eurc_eth-spot","time":"2020-05-12T16:44:41.000000000Z","coin_metrics_id":"tradeId3","amount":"9.522549994298410307","price":"13916037668.951342649","database_time":"2023-10-18T15:09:57.247539000Z","side":"sell","block_hash":"8e81f544e3e307bce7e3f03e5089e343538f91a3e569ce5cd0e1a28ea192439c","block_height":"10052500","txid":"f54815018aec7407554802f822f4803a8dd317039f42f7eb2dd16340b3262493","initiator":"f5369be6df50aa406db8212ae00cf81917c848ea","sender":"f592427a0aece92de3edee1f18e0157c05861564","beneficiary":"f5d599c3a0ff1de082011efddc58f1908eb6e6d8"},{"market":"sushiswap_v1_eth-sushi-usdt_eth-spot","time":"2020-09-04T10:44:02.000000000Z","coin_metrics_id":"tradeId1","amount":"0.180944723836971889","price":"5.5099700000000000042","database_time":"2023-10-18T15:09:56.414767000Z","side":"buy","block_hash":"1a6fcd08aaffac8816a8ea787c1243a9191471da913f713d9e109a938ef479f6","block_height":"10794365","txid":"8c3ac22b84511ba63d85493a09b0b2287f42de4d90a93a5063bbfe15cf395f91","initiator":"f942dba4159cb61f8ad88ca4a83f5204e8f4a6bd","sender":"d9e1ce17f2641f24ae83637ab66a2cca9c378b9f","beneficiary":"f942dba4159cb61f8ad88ca4a83f5204e8f4a6bd"},{"market":"sushiswap_v1_eth-sushi-usdt_eth-spot","time":"2020-09-04T15:53:03.000000000Z","coin_metrics_id":"tradeId2","amount":"29.91","price":"4.9620077566031427616","database_time":"2023-10-18T15:09:56.742686000Z","side":"sell","block_hash":"973e54607a91c117d71c5c907633ad9e120ad0e90296d480d983193f75806e06","block_height":"10795796","txid":"1768a1c85e545aba0ddf0ecd3ccc4470a77ac9f6124c2c052dcf4481ece7b726","initiator":"a582802247df3ff248e4e4bf082d733833095ef5","sender":"d9e1ce17f2641f24ae83637ab66a2cca9c378b9f","beneficiary":"a582802247df3ff248e4e4bf082d733833095ef5"},{"market":"sushiswap_v1_eth-sushi-usdt_eth-spot","time":"2020-09-04T18:05:45.000000000Z","coin_metrics_id":"tradeId3","amount":"18.652970617033515379","price":"4.7589864275526028813","database_time":"2023-10-18T15:09:56.742686000Z","side":"buy","block_hash":"0695c70509a5e56251df4fea8cd924ae8d5489410375c78b0bc210cd5ae6f9a0","block_height":"10796391","txid":"c7c022bdf8af459dc58087a6aee2dc4b08c59e678c7170f7c8cbe04a35b53440","initiator":"9525603189bc828b3af9de9454a79d9566e6e2fb","sender":"d9e1ce17f2641f24ae83637ab66a2cca9c378b9f","beneficiary":"9525603189bc828b3af9de9454a79d9566e6e2fb"},{"market":"sushiswap_v1_eth-sushi-usdt_eth-spot","time":"2020-09-04T18:33:40.000000000Z","coin_metrics_id":"tradeId4","amount":"3.423516588578713053","price":"5.01808785075352673","database_time":"2023-10-18T15:09:56.742686000Z","side":"sell","block_hash":"35a86620d6705fab08471022d225f1dc01892aa51cef7212b98a7e9cc8ebf784","block_height":"10796522","txid":"7cfa87c950c8cf24e1ecdc34f9e8248015aa9af6d4845fdaa6d247d7b0abe3ea","initiator":"78b4e75a2283970eab8900c8e7ff2d96c7a643ad","sender":"d9e1ce17f2641f24ae83637ab66a2cca9c378b9f","beneficiary":"78b4e75a2283970eab8900c8e7ff2d96c7a643ad"},{"market":"sushiswap_v1_eth-sushi-usdt_eth-spot","time":"2020-09-05T01:29:11.000000000Z","coin_metrics_id":"tradeId5","amount":"9.97","price":"4.7867376128385155466","database_time":"2023-10-18T15:09:56.742686000Z","side":"sell","block_hash":"e50c385849755b147a269b0bf8e83339220dc0b2f75d4f1188ae713d0f652b14","block_height":"10798438","txid":"2547061938586c38043f1e810bb6f6f3ade51465f33f6498e86b33c8abb4729c","initiator":"edb7367fd9d58fc59f9d28edd74c056ce84222d3","sender":"d9e1ce17f2641f24ae83637ab66a2cca9c378b9f","beneficiary":"edb7367fd9d58fc59f9d28edd74c056ce84222d3"},{"market":"sushiswap_v1_eth-sushi-usdt_eth-spot","time":"2020-09-05T08:30:59.000000000Z","coin_metrics_id":"tradeId6","amount":"0.082314561115590093","price":"4.6234225736267749121","database_time":"2023-10-18T15:09:56.742686000Z","side":"buy","block_hash":"61ddcb05f266eb46d1e6c8310afba9b609bd7914a1cdd6d6b2b5936ce5e44970","block_height":"10800302","txid":"903541798e4fd69cf97137ce57960303817713ac07ff4b0ff36340406223d086","initiator":"5853ed4f26a3fcea565b3fbc698bb19cdf6deb85","sender":"d9e1ce17f2641f24ae83637ab66a2cca9c378b9f","beneficiary":"5853ed4f26a3fcea565b3fbc698bb19cdf6deb85"},{"market":"sushiswap_v1_eth-sushi-usdt_eth-spot","time":"2020-09-05T09:15:03.000000000Z","coin_metrics_id":"tradeId7","amount":"0.082067617432243323","price":"4.6234192227313961309","database_time":"2023-10-18T15:09:56.742686000Z","side":"sell","block_hash":"8f232f3a1adcfcb3988be123638a32574ad5dd9579f65d832996e7f0897fa830","block_height":"10800497","txid":"7496db2ae961ec3637267b3074267c5a888f3feb8af27c0d3fec6b17bc0f6ac9","initiator":"5853ed4f26a3fcea565b3fbc698bb19cdf6deb85","sender":"d9e1ce17f2641f24ae83637ab66a2cca9c378b9f","beneficiary":"5853ed4f26a3fcea565b3fbc698bb19cdf6deb85"},{"market":"sushiswap_v1_eth-sushi-usdt_eth-spot","time":"2020-09-05T09:20:32.000000000Z","coin_metrics_id":"tradeId8","amount":"92.662725657713148481","price":"3.505093549694916545","database_time":"2023-10-18T15:09:56.742686000Z","side":"sell","block_hash":"22dd85a3411d2d5e3d6450f5aa0f902ca2596316c37c209db7aafbac60e70c98","block_height":"10800530","txid":"b5ad32042c6707f11ac03706e0a9fa53188c00c19bb40af33bc3c7add9c00219","initiator":"f3f83e41bb0c46766240d29302627e8008b2028b","sender":"860bd2dba9cd475a61e6d1b45e16c365f6d78f66","beneficiary":"860bd2dba9cd475a61e6d1b45e16c365f6d78f66"}]}""".trimIndent()
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-trades?markets=*_eth-spot&api_key=$TEST_API_KEY&min_confirmations=0",
        )
    }

    @Test
    fun `should return all trades for curve wildcard markets`() {
        val expectedResponse =
            """{"data":[{"market":"curve_eth-1-cbeth-eth-spot","time":"2020-05-12T16:44:41.000000000Z","coin_metrics_id":"tradeId2","amount":"9.522549994298410307","price":"0.013916037668951342649","database_time":"2023-10-18T15:09:57.247539000Z","side":"sell","block_hash":"8e81f544e3e307bce7e3f03e5089e343538f91a3e569ce5cd0e1a28ea192439c","block_height":"10052500","txid":"f54815018aec7407554802f822f4803a8dd317039f42f7eb2dd16340b3262493","initiator":"f5369be6df50aa406db8212ae00cf81917c848ea","sender":"f592427a0aece92de3edee1f18e0157c05861564","beneficiary":"f5d599c3a0ff1de082011efddc58f1908eb6e6d8"},{"market":"curve_eth-1-tusd_2_eth-dai-spot","time":"2020-05-12T16:44:41.000000000Z","coin_metrics_id":"tradeId1","amount":"9.522549994298410307","price":"0.013916037668951342649","database_time":"2023-10-18T15:09:57.247539000Z","side":"sell","block_hash":"8e81f544e3e307bce7e3f03e5089e343538f91a3e569ce5cd0e1a28ea192439c","block_height":"10052500","txid":"f54815018aec7407554802f822f4803a8dd317039f42f7eb2dd16340b3262493","initiator":"f5369be6df50aa406db8212ae00cf81917c848ea","sender":"f592427a0aece92de3edee1f18e0157c05861564","beneficiary":"f5d599c3a0ff1de082011efddc58f1908eb6e6d8"},{"market":"curve_eth-2-ageur_eth-eurc_eth-spot","time":"2020-05-12T16:44:41.000000000Z","coin_metrics_id":"tradeId3","amount":"9.522549994298410307","price":"13916037668.951342649","database_time":"2023-10-18T15:09:57.247539000Z","side":"sell","block_hash":"8e81f544e3e307bce7e3f03e5089e343538f91a3e569ce5cd0e1a28ea192439c","block_height":"10052500","txid":"f54815018aec7407554802f822f4803a8dd317039f42f7eb2dd16340b3262493","initiator":"f5369be6df50aa406db8212ae00cf81917c848ea","sender":"f592427a0aece92de3edee1f18e0157c05861564","beneficiary":"f5d599c3a0ff1de082011efddc58f1908eb6e6d8"}]}""".trimIndent()
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-trades?markets=curve_*&api_key=$TEST_API_KEY&min_confirmations=0",
        )
    }

    @Test
    fun `should return all trades for uniswap v3 wildcard markets on non-ETH networks`() {
        getResponse("/v4/timeseries/market-trades?markets=uniswap_v3_*&api_key=$TEST_API_KEY&min_confirmations=0").assertResponse()
    }

    @Test
    fun `should return all trades for uniswap v3 markets on non-ETH networks`() {
        getResponse("/v4/timeseries/market-trades?markets=uniswap_v3_base-1-usdc_base.eth-cbbtc_base.eth-spot&api_key=$TEST_API_KEY&min_confirmations=0").assertResponse()
    }

    @Test
    fun `should return all trades for sushiswap_v1_eth-sushi-usdt_eth-spot market`() {
        val expectedResponse =
            """{"data":[{"market":"sushiswap_v1_eth-sushi-usdt_eth-spot","time":"2020-09-04T10:44:02.000000000Z","coin_metrics_id":"tradeId1","amount":"0.180944723836971889","price":"5.5099700000000000042","database_time":"2023-10-18T15:09:56.414767000Z","side":"buy","block_hash":"1a6fcd08aaffac8816a8ea787c1243a9191471da913f713d9e109a938ef479f6","block_height":"10794365","txid":"8c3ac22b84511ba63d85493a09b0b2287f42de4d90a93a5063bbfe15cf395f91","initiator":"f942dba4159cb61f8ad88ca4a83f5204e8f4a6bd","sender":"d9e1ce17f2641f24ae83637ab66a2cca9c378b9f","beneficiary":"f942dba4159cb61f8ad88ca4a83f5204e8f4a6bd"},{"market":"sushiswap_v1_eth-sushi-usdt_eth-spot","time":"2020-09-04T15:53:03.000000000Z","coin_metrics_id":"tradeId2","amount":"29.91","price":"4.9620077566031427616","database_time":"2023-10-18T15:09:56.742686000Z","side":"sell","block_hash":"973e54607a91c117d71c5c907633ad9e120ad0e90296d480d983193f75806e06","block_height":"10795796","txid":"1768a1c85e545aba0ddf0ecd3ccc4470a77ac9f6124c2c052dcf4481ece7b726","initiator":"a582802247df3ff248e4e4bf082d733833095ef5","sender":"d9e1ce17f2641f24ae83637ab66a2cca9c378b9f","beneficiary":"a582802247df3ff248e4e4bf082d733833095ef5"},{"market":"sushiswap_v1_eth-sushi-usdt_eth-spot","time":"2020-09-04T18:05:45.000000000Z","coin_metrics_id":"tradeId3","amount":"18.652970617033515379","price":"4.7589864275526028813","database_time":"2023-10-18T15:09:56.742686000Z","side":"buy","block_hash":"0695c70509a5e56251df4fea8cd924ae8d5489410375c78b0bc210cd5ae6f9a0","block_height":"10796391","txid":"c7c022bdf8af459dc58087a6aee2dc4b08c59e678c7170f7c8cbe04a35b53440","initiator":"9525603189bc828b3af9de9454a79d9566e6e2fb","sender":"d9e1ce17f2641f24ae83637ab66a2cca9c378b9f","beneficiary":"9525603189bc828b3af9de9454a79d9566e6e2fb"},{"market":"sushiswap_v1_eth-sushi-usdt_eth-spot","time":"2020-09-04T18:33:40.000000000Z","coin_metrics_id":"tradeId4","amount":"3.423516588578713053","price":"5.01808785075352673","database_time":"2023-10-18T15:09:56.742686000Z","side":"sell","block_hash":"35a86620d6705fab08471022d225f1dc01892aa51cef7212b98a7e9cc8ebf784","block_height":"10796522","txid":"7cfa87c950c8cf24e1ecdc34f9e8248015aa9af6d4845fdaa6d247d7b0abe3ea","initiator":"78b4e75a2283970eab8900c8e7ff2d96c7a643ad","sender":"d9e1ce17f2641f24ae83637ab66a2cca9c378b9f","beneficiary":"78b4e75a2283970eab8900c8e7ff2d96c7a643ad"},{"market":"sushiswap_v1_eth-sushi-usdt_eth-spot","time":"2020-09-05T01:29:11.000000000Z","coin_metrics_id":"tradeId5","amount":"9.97","price":"4.7867376128385155466","database_time":"2023-10-18T15:09:56.742686000Z","side":"sell","block_hash":"e50c385849755b147a269b0bf8e83339220dc0b2f75d4f1188ae713d0f652b14","block_height":"10798438","txid":"2547061938586c38043f1e810bb6f6f3ade51465f33f6498e86b33c8abb4729c","initiator":"edb7367fd9d58fc59f9d28edd74c056ce84222d3","sender":"d9e1ce17f2641f24ae83637ab66a2cca9c378b9f","beneficiary":"edb7367fd9d58fc59f9d28edd74c056ce84222d3"},{"market":"sushiswap_v1_eth-sushi-usdt_eth-spot","time":"2020-09-05T08:30:59.000000000Z","coin_metrics_id":"tradeId6","amount":"0.082314561115590093","price":"4.6234225736267749121","database_time":"2023-10-18T15:09:56.742686000Z","side":"buy","block_hash":"61ddcb05f266eb46d1e6c8310afba9b609bd7914a1cdd6d6b2b5936ce5e44970","block_height":"10800302","txid":"903541798e4fd69cf97137ce57960303817713ac07ff4b0ff36340406223d086","initiator":"5853ed4f26a3fcea565b3fbc698bb19cdf6deb85","sender":"d9e1ce17f2641f24ae83637ab66a2cca9c378b9f","beneficiary":"5853ed4f26a3fcea565b3fbc698bb19cdf6deb85"},{"market":"sushiswap_v1_eth-sushi-usdt_eth-spot","time":"2020-09-05T09:15:03.000000000Z","coin_metrics_id":"tradeId7","amount":"0.082067617432243323","price":"4.6234192227313961309","database_time":"2023-10-18T15:09:56.742686000Z","side":"sell","block_hash":"8f232f3a1adcfcb3988be123638a32574ad5dd9579f65d832996e7f0897fa830","block_height":"10800497","txid":"7496db2ae961ec3637267b3074267c5a888f3feb8af27c0d3fec6b17bc0f6ac9","initiator":"5853ed4f26a3fcea565b3fbc698bb19cdf6deb85","sender":"d9e1ce17f2641f24ae83637ab66a2cca9c378b9f","beneficiary":"5853ed4f26a3fcea565b3fbc698bb19cdf6deb85"},{"market":"sushiswap_v1_eth-sushi-usdt_eth-spot","time":"2020-09-05T09:20:32.000000000Z","coin_metrics_id":"tradeId8","amount":"92.662725657713148481","price":"3.505093549694916545","database_time":"2023-10-18T15:09:56.742686000Z","side":"sell","block_hash":"22dd85a3411d2d5e3d6450f5aa0f902ca2596316c37c209db7aafbac60e70c98","block_height":"10800530","txid":"b5ad32042c6707f11ac03706e0a9fa53188c00c19bb40af33bc3c7add9c00219","initiator":"f3f83e41bb0c46766240d29302627e8008b2028b","sender":"860bd2dba9cd475a61e6d1b45e16c365f6d78f66","beneficiary":"860bd2dba9cd475a61e6d1b45e16c365f6d78f66"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-trades?markets=sushiswap_v1_eth-sushi-usdt_eth-spot&api_key=$TEST_API_KEY&min_confirmations=0",
        )
    }

    @Test
    fun `should return all trades filtered by implicit min_confirmations for defi market`() {
        val expectedResponse =
            """{"data":[{"market":"uniswap_v2_eth-usdc-weth-spot","time":"2020-05-05T21:09:32.000000000Z","coin_metrics_id":"tradeId1","amount":"0.001","price":"0.004859228160162","database_time":"2023-10-18T15:09:56.414745000Z","side":"buy","block_hash":"4ba33a650f9e3d8430f94b61a382e60490ec7a06c2f4441ecf225858ec748b78","block_height":"10008566","txid":"932cb88306450d481a0e43365a3ed832625b68f036e9887684ef6da594891366","initiator":"8688a84fcfd84d8f78020d0fc0b35987cc58911f","sender":"f164fc0ec4e93095b804a4795bbe1e041497b92a","beneficiary":"8688a84fcfd84d8f78020d0fc0b35987cc58911f"},{"market":"uniswap_v2_eth-usdc-weth-spot","time":"2020-05-05T21:12:35.000000000Z","coin_metrics_id":"tradeId2","amount":"0.01","price":"0.0049132889602724","database_time":"2023-10-18T15:09:56.743846000Z","side":"buy","block_hash":"9eadcc046043be7c86e398492e3c935f5d410692b3524e78e9e2c79cf9c25462","block_height":"10008585","txid":"697b7aaca56a80a8d3a2f560ed7f1ecb97c22b2edd6eddc88ba4e89d96acf3c2","initiator":"11e4857bb9993a50c685a79afad4e6f65d518dda","sender":"f164fc0ec4e93095b804a4795bbe1e041497b92a","beneficiary":"11e4857bb9993a50c685a79afad4e6f65d518dda"},{"market":"uniswap_v2_eth-usdc-weth-spot","time":"2020-05-06T16:24:55.000000000Z","coin_metrics_id":"tradeId3","amount":"0.001","price":"0.004968141208004","database_time":"2023-10-18T15:09:56.743846000Z","side":"buy","block_hash":"fddbc9d9a6155c54f18f83d230996366f35ead00741e082fadc71c0497f5a6ea","block_height":"10013764","txid":"43b6bfd06dde0814fe9c1b63ce98ec4c67c72d96169d73e13bf2b409d0551b58","initiator":"8688a84fcfd84d8f78020d0fc0b35987cc58911f","sender":"f164fc0ec4e93095b804a4795bbe1e041497b92a","beneficiary":"8688a84fcfd84d8f78020d0fc0b35987cc58911f"},{"market":"uniswap_v2_eth-usdc-weth-spot","time":"2020-05-06T18:51:12.000000000Z","coin_metrics_id":"tradeId4","amount":"0.000687","price":"0.0049671007218573508006","database_time":"2023-10-18T15:09:56.743846000Z","side":"sell","block_hash":"e367e15f16985e1786f5e0f109a430769c1de0fda753a227704c126245ab521f","block_height":"10014418","txid":"735cf98e86a5df67b6a837ae50de1d7a589d9f6baaf0e1ac95e992884a182ce2","initiator":"8688a84fcfd84d8f78020d0fc0b35987cc58911f","sender":"f164fc0ec4e93095b804a4795bbe1e041497b92a","beneficiary":"12ede161c702d1494612d19f05992f43aa6a26fb"},{"market":"uniswap_v2_eth-usdc-weth-spot","time":"2020-05-11T13:08:48.000000000Z","coin_metrics_id":"tradeId5","amount":"0.20487","price":"0.004866500707765900327","database_time":"2023-10-18T15:09:56.743846000Z","side":"buy","block_hash":"73eec17715249e66db06a563b8f0706b4c3fcc9eb8e33ace498e8c6ccd138f61","block_height":"10045107","txid":"840bb5ab0a779b4f733443651ac54dabe24e243c36062183fdb69c511f9b0bb8","initiator":"4d37f28d2db99e8d35a6c725a5f1749a085850a3","sender":"57ead0a9f49fafdd2447f615b036f3c316af5171","beneficiary":"57ead0a9f49fafdd2447f615b036f3c316af5171"},{"market":"uniswap_v2_eth-usdc-weth-spot","time":"2020-05-11T13:09:05.000000000Z","coin_metrics_id":"tradeId6","amount":"0.192066","price":"0.0048764098355619682817","database_time":"2023-10-18T15:09:56.743846000Z","side":"sell","block_hash":"5134e422203b65b2d101472cf9462fbd8dc2360f31e5b77d4125eca5ef54d12e","block_height":"10045110","txid":"12c446896c4a75adaad9536e0753feb69139ef32bc688c99258acde2b48e1e70","initiator":"4d37f28d2db99e8d35a6c725a5f1749a085850a3","sender":"57ead0a9f49fafdd2447f615b036f3c316af5171","beneficiary":"57ead0a9f49fafdd2447f615b036f3c316af5171"},{"market":"uniswap_v2_eth-usdc-weth-spot","time":"2020-05-12T11:08:31.000000000Z","coin_metrics_id":"tradeId7","amount":"0.049736","price":"0.0046976139101826443622","database_time":"2023-10-18T15:09:56.743846000Z","side":"sell","block_hash":"80c66ffefc5759f6ecafa629b4f034ba3b097c6fbb97482594253f7783672d85","block_height":"10051015","txid":"b708aa3c13aa6c09e724dec65464981157b4e081863ffb12ed7584438b56b8bc","initiator":"42ea16c9b9e529da492909f34f416fed2be7c280","sender":"f164fc0ec4e93095b804a4795bbe1e041497b92a","beneficiary":"379b4609bdf93b3584cf7b64bc78199cf185f1cd"},{"market":"uniswap_v2_eth-usdc-weth-spot","time":"2020-05-12T11:46:00.000000000Z","coin_metrics_id":"tradeId8","amount":"0.866644","price":"0.0053594035848787795219","database_time":"2023-10-18T15:09:56.743846000Z","side":"buy","block_hash":"3c355014a9cdcdf578896aca02a6e998355a23c11cf48f2e7f6b91993058d424","block_height":"10051195","txid":"10d0c21b4722533ba9bf7f01ea7abd511f3f92fae8a376e8d1ad86491010beaf","initiator":"42ea16c9b9e529da492909f34f416fed2be7c280","sender":"f164fc0ec4e93095b804a4795bbe1e041497b92a","beneficiary":"379b4609bdf93b3584cf7b64bc78199cf185f1cd"},{"market":"uniswap_v2_eth-usdc-weth-spot","time":"2020-05-12T14:18:42.000000000Z","coin_metrics_id":"tradeId9","amount":"0.35074","price":"0.0065057465869482180533","database_time":"2023-10-18T15:09:56.743846000Z","side":"buy","block_hash":"efbf08c3469815d32666b8a187c9d25096355020b08ebc4853de3218d0f52199","block_height":"10051877","txid":"4f426c26d42a1df21d3752b27171c8ec1f39ae3c203ab4f1e99db5be32658bb2","initiator":"42ea16c9b9e529da492909f34f416fed2be7c280","sender":"f164fc0ec4e93095b804a4795bbe1e041497b92a","beneficiary":"379b4609bdf93b3584cf7b64bc78199cf185f1cd"},{"market":"uniswap_v2_eth-usdc-weth-spot","time":"2020-05-12T16:44:41.000000000Z","coin_metrics_id":"tradeId10","amount":"0.62606","price":"0.0075807371196965258921","database_time":"2023-10-18T15:09:56.743846000Z","side":"buy","block_hash":"8e81f544e3e307bce7e3f03e5089e343538f91a3e569ce5cd0e1a28ea192439c","block_height":"10052500","txid":"3d3302879f80cd62a4f58cf66624417af75016c9cedfa3b773c83f2c0698e553","initiator":"90c29fe72e67db90a8b3a66277414da21e116652","sender":"f164fc0ec4e93095b804a4795bbe1e041497b92a","beneficiary":"379b4609bdf93b3584cf7b64bc78199cf185f1cd"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-trades?markets=uniswap_v2_eth-usdc-weth-spot&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `should return all trades filtered by explicit min_confirmations for defi market`() {
        val expectedResponse =
            """{"data":[{"market":"uniswap_v2_eth-usdc-weth-spot","time":"2020-05-05T21:09:32.000000000Z","coin_metrics_id":"tradeId1","amount":"0.001","price":"0.004859228160162","database_time":"2023-10-18T15:09:56.414745000Z","side":"buy","block_hash":"4ba33a650f9e3d8430f94b61a382e60490ec7a06c2f4441ecf225858ec748b78","block_height":"10008566","txid":"932cb88306450d481a0e43365a3ed832625b68f036e9887684ef6da594891366","initiator":"8688a84fcfd84d8f78020d0fc0b35987cc58911f","sender":"f164fc0ec4e93095b804a4795bbe1e041497b92a","beneficiary":"8688a84fcfd84d8f78020d0fc0b35987cc58911f"},{"market":"uniswap_v2_eth-usdc-weth-spot","time":"2020-05-05T21:12:35.000000000Z","coin_metrics_id":"tradeId2","amount":"0.01","price":"0.0049132889602724","database_time":"2023-10-18T15:09:56.743846000Z","side":"buy","block_hash":"9eadcc046043be7c86e398492e3c935f5d410692b3524e78e9e2c79cf9c25462","block_height":"10008585","txid":"697b7aaca56a80a8d3a2f560ed7f1ecb97c22b2edd6eddc88ba4e89d96acf3c2","initiator":"11e4857bb9993a50c685a79afad4e6f65d518dda","sender":"f164fc0ec4e93095b804a4795bbe1e041497b92a","beneficiary":"11e4857bb9993a50c685a79afad4e6f65d518dda"},{"market":"uniswap_v2_eth-usdc-weth-spot","time":"2020-05-06T16:24:55.000000000Z","coin_metrics_id":"tradeId3","amount":"0.001","price":"0.004968141208004","database_time":"2023-10-18T15:09:56.743846000Z","side":"buy","block_hash":"fddbc9d9a6155c54f18f83d230996366f35ead00741e082fadc71c0497f5a6ea","block_height":"10013764","txid":"43b6bfd06dde0814fe9c1b63ce98ec4c67c72d96169d73e13bf2b409d0551b58","initiator":"8688a84fcfd84d8f78020d0fc0b35987cc58911f","sender":"f164fc0ec4e93095b804a4795bbe1e041497b92a","beneficiary":"8688a84fcfd84d8f78020d0fc0b35987cc58911f"},{"market":"uniswap_v2_eth-usdc-weth-spot","time":"2020-05-06T18:51:12.000000000Z","coin_metrics_id":"tradeId4","amount":"0.000687","price":"0.0049671007218573508006","database_time":"2023-10-18T15:09:56.743846000Z","side":"sell","block_hash":"e367e15f16985e1786f5e0f109a430769c1de0fda753a227704c126245ab521f","block_height":"10014418","txid":"735cf98e86a5df67b6a837ae50de1d7a589d9f6baaf0e1ac95e992884a182ce2","initiator":"8688a84fcfd84d8f78020d0fc0b35987cc58911f","sender":"f164fc0ec4e93095b804a4795bbe1e041497b92a","beneficiary":"12ede161c702d1494612d19f05992f43aa6a26fb"},{"market":"uniswap_v2_eth-usdc-weth-spot","time":"2020-05-11T13:08:48.000000000Z","coin_metrics_id":"tradeId5","amount":"0.20487","price":"0.004866500707765900327","database_time":"2023-10-18T15:09:56.743846000Z","side":"buy","block_hash":"73eec17715249e66db06a563b8f0706b4c3fcc9eb8e33ace498e8c6ccd138f61","block_height":"10045107","txid":"840bb5ab0a779b4f733443651ac54dabe24e243c36062183fdb69c511f9b0bb8","initiator":"4d37f28d2db99e8d35a6c725a5f1749a085850a3","sender":"57ead0a9f49fafdd2447f615b036f3c316af5171","beneficiary":"57ead0a9f49fafdd2447f615b036f3c316af5171"},{"market":"uniswap_v2_eth-usdc-weth-spot","time":"2020-05-11T13:09:05.000000000Z","coin_metrics_id":"tradeId6","amount":"0.192066","price":"0.0048764098355619682817","database_time":"2023-10-18T15:09:56.743846000Z","side":"sell","block_hash":"5134e422203b65b2d101472cf9462fbd8dc2360f31e5b77d4125eca5ef54d12e","block_height":"10045110","txid":"12c446896c4a75adaad9536e0753feb69139ef32bc688c99258acde2b48e1e70","initiator":"4d37f28d2db99e8d35a6c725a5f1749a085850a3","sender":"57ead0a9f49fafdd2447f615b036f3c316af5171","beneficiary":"57ead0a9f49fafdd2447f615b036f3c316af5171"},{"market":"uniswap_v2_eth-usdc-weth-spot","time":"2020-05-12T11:08:31.000000000Z","coin_metrics_id":"tradeId7","amount":"0.049736","price":"0.0046976139101826443622","database_time":"2023-10-18T15:09:56.743846000Z","side":"sell","block_hash":"80c66ffefc5759f6ecafa629b4f034ba3b097c6fbb97482594253f7783672d85","block_height":"10051015","txid":"b708aa3c13aa6c09e724dec65464981157b4e081863ffb12ed7584438b56b8bc","initiator":"42ea16c9b9e529da492909f34f416fed2be7c280","sender":"f164fc0ec4e93095b804a4795bbe1e041497b92a","beneficiary":"379b4609bdf93b3584cf7b64bc78199cf185f1cd"},{"market":"uniswap_v2_eth-usdc-weth-spot","time":"2020-05-12T11:46:00.000000000Z","coin_metrics_id":"tradeId8","amount":"0.866644","price":"0.0053594035848787795219","database_time":"2023-10-18T15:09:56.743846000Z","side":"buy","block_hash":"3c355014a9cdcdf578896aca02a6e998355a23c11cf48f2e7f6b91993058d424","block_height":"10051195","txid":"10d0c21b4722533ba9bf7f01ea7abd511f3f92fae8a376e8d1ad86491010beaf","initiator":"42ea16c9b9e529da492909f34f416fed2be7c280","sender":"f164fc0ec4e93095b804a4795bbe1e041497b92a","beneficiary":"379b4609bdf93b3584cf7b64bc78199cf185f1cd"},{"market":"uniswap_v2_eth-usdc-weth-spot","time":"2020-05-12T14:18:42.000000000Z","coin_metrics_id":"tradeId9","amount":"0.35074","price":"0.0065057465869482180533","database_time":"2023-10-18T15:09:56.743846000Z","side":"buy","block_hash":"efbf08c3469815d32666b8a187c9d25096355020b08ebc4853de3218d0f52199","block_height":"10051877","txid":"4f426c26d42a1df21d3752b27171c8ec1f39ae3c203ab4f1e99db5be32658bb2","initiator":"42ea16c9b9e529da492909f34f416fed2be7c280","sender":"f164fc0ec4e93095b804a4795bbe1e041497b92a","beneficiary":"379b4609bdf93b3584cf7b64bc78199cf185f1cd"},{"market":"uniswap_v2_eth-usdc-weth-spot","time":"2020-05-12T16:44:41.000000000Z","coin_metrics_id":"tradeId10","amount":"0.62606","price":"0.0075807371196965258921","database_time":"2023-10-18T15:09:56.743846000Z","side":"buy","block_hash":"8e81f544e3e307bce7e3f03e5089e343538f91a3e569ce5cd0e1a28ea192439c","block_height":"10052500","txid":"3d3302879f80cd62a4f58cf66624417af75016c9cedfa3b773c83f2c0698e553","initiator":"90c29fe72e67db90a8b3a66277414da21e116652","sender":"f164fc0ec4e93095b804a4795bbe1e041497b92a","beneficiary":"379b4609bdf93b3584cf7b64bc78199cf185f1cd"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-trades?markets=uniswap_v2_eth-usdc-weth-spot&api_key=$TEST_API_KEY&min_confirmations=3",
        )
    }

    @Test
    fun `should return 400 for non existing defi market`() {
        val expectedResponse =
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'markets'. Market 'uniswap_v1_eth-usdc-weth-spot' is not supported."}}"""
        assertResponse(
            400,
            expectedResponse,
            "/v4/timeseries/market-trades?markets=uniswap_v1_eth-usdc-weth-spot&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `should return all trades filtered by end_time for defi market`() {
        val expectedResponse =
            """{"data":[{"market":"uniswap_v2_eth-usdc-weth-spot","time":"2020-05-05T21:09:32.000000000Z","coin_metrics_id":"tradeId1","amount":"0.001","price":"0.004859228160162","database_time":"2023-10-18T15:09:56.414745000Z","side":"buy","block_hash":"4ba33a650f9e3d8430f94b61a382e60490ec7a06c2f4441ecf225858ec748b78","block_height":"10008566","txid":"932cb88306450d481a0e43365a3ed832625b68f036e9887684ef6da594891366","initiator":"8688a84fcfd84d8f78020d0fc0b35987cc58911f","sender":"f164fc0ec4e93095b804a4795bbe1e041497b92a","beneficiary":"8688a84fcfd84d8f78020d0fc0b35987cc58911f"},{"market":"uniswap_v2_eth-usdc-weth-spot","time":"2020-05-05T21:12:35.000000000Z","coin_metrics_id":"tradeId2","amount":"0.01","price":"0.0049132889602724","database_time":"2023-10-18T15:09:56.743846000Z","side":"buy","block_hash":"9eadcc046043be7c86e398492e3c935f5d410692b3524e78e9e2c79cf9c25462","block_height":"10008585","txid":"697b7aaca56a80a8d3a2f560ed7f1ecb97c22b2edd6eddc88ba4e89d96acf3c2","initiator":"11e4857bb9993a50c685a79afad4e6f65d518dda","sender":"f164fc0ec4e93095b804a4795bbe1e041497b92a","beneficiary":"11e4857bb9993a50c685a79afad4e6f65d518dda"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-trades?markets=uniswap_v2_eth-usdc-weth-spot&api_key=$TEST_API_KEY&min_confirmations=3&end_time=2020-05-06T13:09:05Z",
        )
    }

    @Test
    fun `should return 400 for unknown defi market`() {
        val expectedResponse =
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'markets'. Market 'uniswap_v4_eth-usdc-weth-spot' is not supported."}}""".trimIndent()
        assertResponse(
            400,
            expectedResponse,
            "/v4/timeseries/market-trades?markets=uniswap_v4_eth-0-usdc-weth-spot&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `test spot trades with wildcard`() {
        getResponse("/v4/timeseries/market-trades?markets=*-spot&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `test future trades with wildcard`() {
        val expectedResponse =
            """{"data":[{"market":"binance-XBTUSD-future","time":"2019-03-15T09:51:28.851000000Z","coin_metrics_id":"3","amount":"0.1000000000000001","price":"1.00001","database_time":"2019-12-21T10:56:24.510288000Z","side":"buy"},{"market":"binance-XBTUSD-future","time":"2019-03-15T10:36:40.247000000Z","coin_metrics_id":"1","amount":"0.1","price":"1.00001","database_time":"2019-12-21T10:56:24.418065000Z","side":"buy"},{"market":"binance-XBTUSD-future","time":"2019-03-15T10:36:46.275000000Z","coin_metrics_id":"2","amount":"0.1","price":"1.00001","database_time":"2019-12-21T10:56:24.418065000Z","side":"sell"},{"market":"binance-XBTUSD-future","time":"2019-03-15T10:36:46.428000000Z","coin_metrics_id":"4","amount":"0.1","price":"2.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"sell"},{"market":"bitmex-FILUSD-future","time":"2023-09-17T00:15:51.000000000Z","coin_metrics_id":"295272266791454803656457771505522275176","amount":"5","price":"3.3","database_time":"2023-09-17T00:15:52.000000000Z","side":"sell"},{"market":"bitmex-XBTUSD-future","time":"2019-03-15T09:51:28.851000000Z","coin_metrics_id":"3","amount":"0.2000000000000001","price":"2.00001","database_time":"2019-12-21T10:56:24.510288000Z","side":"buy"},{"market":"bitmex-XBTUSD-future","time":"2019-03-15T10:36:38.926000000Z","coin_metrics_id":"6","amount":"0.2","price":"3.0000100000000000001","database_time":"2019-12-21T10:56:24.918845000Z","side":"buy"},{"market":"bitmex-XBTUSD-future","time":"2019-03-16T10:33:23.804000000Z","coin_metrics_id":"9","amount":"0.2","price":"3.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"sell"},{"market":"bitmex-XBTUSD-future","time":"2019-03-16T10:36:01.000000000Z","coin_metrics_id":"7","amount":"0.2","price":"3.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"buy"},{"market":"bitmex-XBTUSD-future","time":"2019-03-16T10:36:02.999999000Z","coin_metrics_id":"8","amount":"0.2","price":"4.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"buy"},{"market":"bitmex-XBTUSD-future","time":"2019-03-16T10:36:24.010000000Z","coin_metrics_id":"5","amount":"0.2","price":"2.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"buy"},{"market":"bitmex-XBTUSD-future","time":"2019-03-16T10:36:40.247000000Z","coin_metrics_id":"1","amount":"0.2","price":"2.00001","database_time":"2019-12-21T10:56:24.418065000Z","side":"buy"},{"market":"bitmex-XBTUSD-future","time":"2019-03-16T10:36:46.275000000Z","coin_metrics_id":"2","amount":"0.2","price":"2.00001","database_time":"2019-12-21T10:56:24.418065000Z","side":"sell"},{"market":"bitmex-XBTUSD-future","time":"2019-03-16T10:36:46.428000000Z","coin_metrics_id":"4","amount":"0.2","price":"1.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"sell"},{"market":"bitmex-XBTUSD-future","time":"2019-03-19T23:59:59.999999000Z","coin_metrics_id":"10","amount":"0.34","price":"4.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"buy"},{"market":"bitmex-XBTUSD-future","time":"2019-03-20T00:00:00.000000000Z","coin_metrics_id":"11","amount":"0.53","price":"3.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"buy"},{"market":"cme-BTCQ1-future","time":"2020-03-15T10:36:46.428000000Z","coin_metrics_id":"38","amount":"0.1","price":"2.00001","database_time":"2021-12-21T10:56:24.918845000Z","side":"sell"},{"market":"cme-BTCQ1-future","time":"2020-03-15T10:36:46.428000000Z","coin_metrics_id":"39","amount":"0.2","price":"4.00001","database_time":"2021-12-21T10:56:24.918845000Z","side":"sell"},{"market":"cme-BTCQ1-future","time":"2021-03-15T10:36:46.428000000Z","coin_metrics_id":"41","amount":"0.3","price":"3.00001","database_time":"2021-12-21T10:56:24.918845000Z","side":"sell"},{"market":"deribit-ETH-7JUN24-future","time":"2024-05-30T11:04:48.000000000Z","coin_metrics_id":"209092169","amount":"6650","price":"3754.25","database_time":"2024-05-30T11:04:49.000000000Z","side":"sell","mark_price":"3753.77","index_price":"3742.9","liquidation":"ETH-7JUN24_liquidation"},{"market":"deribit-ETH-PERPETUAL-future","time":"2024-05-30T11:03:14.000000000Z","coin_metrics_id":"209092104","amount":"1","price":"3743.15","database_time":"2024-05-30T11:03:14.000000000Z","side":"buy","mark_price":"3743.48","index_price":"3742.51","iv_trade":"0.123"},{"market":"ftx-SOL-0925-future","time":"2019-03-15T10:36:46.428000000Z","coin_metrics_id":"6","amount":"0.1","price":"2.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"sell"},{"market":"huobi-XBTUSD4-future","time":"2019-03-15T10:36:31.852000000Z","coin_metrics_id":"10004731385326753984320","amount":"0.18","price":"0.00001267","database_time":"2019-12-21T10:56:24.918845000Z","side":"sell"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-trades?markets=*-future&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `test option trades with wildcard`() {
        val expectedResponse =
            """{"data":[{"market":"bybit-BTC-9APR21-50000-C-option","time":"2024-02-27T21:33:47.010246000Z","coin_metrics_id":"1","amount":"0.2","price":"2.00001","database_time":"2024-02-27T21:33:47.010583000Z","side":"buy"},{"market":"deribit-BTC-21JUN24-70000-P-option","time":"2024-05-30T11:24:08.000000000Z","coin_metrics_id":"303557500","amount":"0.2","price":"0.061","database_time":"2024-05-30T11:24:09.000000000Z","side":"sell","mark_price":"0.06127307","index_price":"67852.63","iv_trade":"0.4866"},{"market":"deribit-BTC-29MAR21-54000-C-option","time":"2020-03-15T10:36:46.428000000Z","coin_metrics_id":"38","amount":"0.1","price":"2.00001","database_time":"2021-12-21T10:56:24.918845000Z","side":"sell"},{"market":"deribit-BTC-29MAR21-54000-C-option","time":"2020-03-15T10:36:46.428000000Z","coin_metrics_id":"39","amount":"0.2","price":"4.00001","database_time":"2021-12-21T10:56:24.918845000Z","side":"sell"},{"market":"deribit-BTC-29MAR21-54000-C-option","time":"2021-03-15T10:36:46.428000000Z","coin_metrics_id":"41","amount":"0.3","price":"3.00001","database_time":"2021-12-21T10:56:24.918845000Z","side":"sell"},{"market":"deribit-BTC-31MAY24-67500-C-option","time":"2024-05-30T11:27:44.000000000Z","coin_metrics_id":"303557715","amount":"0.1","price":"0.0135","database_time":"2024-05-30T11:27:46.000000000Z","side":"buy","mark_price":"0.01345659","index_price":"67932.39","iv_trade":"0.4871","liquidation":"BTC-31MAY24-67500-C_liquidation"},{"market":"deribit-BTC-9APR21-50000-C-option","time":"2024-02-27T21:33:47.010246000Z","coin_metrics_id":"1","amount":"0.2","price":"2.00001","database_time":"2024-02-27T21:33:47.010523000Z","side":"buy"},{"market":"deribit-BTC-9APR21-50000-P-option","time":"2019-03-15T09:51:28.851000000Z","coin_metrics_id":"3","amount":"0.2000000000000001","price":"2.00001","database_time":"2019-12-21T10:56:24.510288000Z","side":"buy"},{"market":"deribit-BTC-9APR21-50000-P-option","time":"2019-03-15T09:51:28.851000000Z","coin_metrics_id":"15","amount":"0.1000000000000001","price":"1.00001","database_time":"2019-12-21T10:56:24.510288000Z","side":"buy"},{"market":"deribit-BTC-9APR21-50000-P-option","time":"2019-03-15T10:36:38.926000000Z","coin_metrics_id":"6","amount":"0.2","price":"3.0000100000000000001","database_time":"2019-12-21T10:56:24.918845000Z","side":"buy"},{"market":"deribit-BTC-9APR21-50000-P-option","time":"2019-03-15T10:36:40.247000000Z","coin_metrics_id":"13","amount":"0.1","price":"1.00001","database_time":"2019-12-21T10:56:24.418065000Z","side":"buy"},{"market":"deribit-BTC-9APR21-50000-P-option","time":"2019-03-15T10:36:46.275000000Z","coin_metrics_id":"14","amount":"0.1","price":"1.00001","database_time":"2019-12-21T10:56:24.418065000Z","side":"sell"},{"market":"deribit-BTC-9APR21-50000-P-option","time":"2019-03-15T10:36:46.428000000Z","coin_metrics_id":"16","amount":"0.1","price":"2.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"sell"},{"market":"deribit-BTC-9APR21-50000-P-option","time":"2019-03-16T10:33:23.804000000Z","coin_metrics_id":"9","amount":"0.2","price":"3.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"sell"},{"market":"deribit-BTC-9APR21-50000-P-option","time":"2019-03-16T10:36:01.000000000Z","coin_metrics_id":"7","amount":"0.2","price":"3.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"buy"},{"market":"deribit-BTC-9APR21-50000-P-option","time":"2019-03-16T10:36:02.999999000Z","coin_metrics_id":"8","amount":"0.2","price":"4.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"buy"},{"market":"deribit-BTC-9APR21-50000-P-option","time":"2019-03-16T10:36:24.010000000Z","coin_metrics_id":"5","amount":"0.2","price":"2.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"buy"},{"market":"deribit-BTC-9APR21-50000-P-option","time":"2019-03-16T10:36:40.247000000Z","coin_metrics_id":"1","amount":"0.2","price":"2.00001","database_time":"2019-12-21T10:56:24.418065000Z","side":"buy"},{"market":"deribit-BTC-9APR21-50000-P-option","time":"2019-03-16T10:36:46.275000000Z","coin_metrics_id":"2","amount":"0.2","price":"2.00001","database_time":"2019-12-21T10:56:24.418065000Z","side":"sell"},{"market":"deribit-BTC-9APR21-50000-P-option","time":"2019-03-16T10:36:46.428000000Z","coin_metrics_id":"4","amount":"0.2","price":"1.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"sell"},{"market":"deribit-BTC-9APR21-50000-P-option","time":"2019-03-19T23:59:59.999999000Z","coin_metrics_id":"10","amount":"0.34","price":"4.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"buy"},{"market":"deribit-BTC-9APR21-50000-P-option","time":"2019-03-20T00:00:00.000000000Z","coin_metrics_id":"11","amount":"0.53","price":"3.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"buy"},{"market":"deribit-ETH-2APR21-1960-C-option","time":"2019-03-15T10:36:46.428000000Z","coin_metrics_id":"17","amount":"0.1","price":"2.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"sell"},{"market":"huobi-BTC-9APR21-50000-C-option","time":"2024-02-27T21:33:47.010246000Z","coin_metrics_id":"1","amount":"0.2","price":"2.00001","database_time":"2024-02-27T21:33:47.010246000Z","side":"buy"},{"market":"huobi-BTC-9APR21-50000-P4-option","time":"2019-03-15T10:36:31.852000000Z","coin_metrics_id":"10003731385326753984320","amount":"0.18","price":"0.00001267","database_time":"2019-12-21T10:56:24.918845000Z","side":"sell"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-trades?markets=*-option&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `test deribit spot trades`() {
        val expectedResponse = """{"data":[{"market":"deribit-btc-usdc-spot","time":"2024-05-30T10:21:45.000000000Z","coin_metrics_id":"1811347","amount":"0.0211","price":"67599","database_time":"2024-05-30T10:21:46.000000000Z","side":"buy","mark_price":"67569.3557","index_price":"67569.3557","iv_trade":"0.321"},{"market":"deribit-eth-usdc-spot","time":"2024-05-30T10:18:47.000000000Z","coin_metrics_id":"1811267","amount":"0.0001","price":"3716.9","database_time":"2024-05-30T10:18:48.000000000Z","side":"sell","mark_price":"3717.2447","index_price":"3717.2447","liquidation":"ETH_USDC_liquidation"}]}"""
        assertResponse(
            expectedCode = 200,
            expectedResponse = expectedResponse,
            pathAndQuery = "/v4/timeseries/market-trades?markets=deribit-*-spot&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `test deribit future trades`() {
        val expectedResponse = """{"data":[{"market":"deribit-ETH-7JUN24-future","time":"2024-05-30T11:04:48.000000000Z","coin_metrics_id":"209092169","amount":"6650","price":"3754.25","database_time":"2024-05-30T11:04:49.000000000Z","side":"sell","mark_price":"3753.77","index_price":"3742.9","liquidation":"ETH-7JUN24_liquidation"},{"market":"deribit-ETH-PERPETUAL-future","time":"2024-05-30T11:03:14.000000000Z","coin_metrics_id":"209092104","amount":"1","price":"3743.15","database_time":"2024-05-30T11:03:14.000000000Z","side":"buy","mark_price":"3743.48","index_price":"3742.51","iv_trade":"0.123"}]}"""
        assertResponse(
            expectedCode = 200,
            expectedResponse = expectedResponse,
            pathAndQuery = "/v4/timeseries/market-trades?markets=deribit-*-future&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `test deribit option trades`() {
        val expectedResponse = """{"data":[{"market":"deribit-BTC-21JUN24-70000-P-option","time":"2024-05-30T11:24:08.000000000Z","coin_metrics_id":"303557500","amount":"0.2","price":"0.061","database_time":"2024-05-30T11:24:09.000000000Z","side":"sell","mark_price":"0.06127307","index_price":"67852.63","iv_trade":"0.4866"},{"market":"deribit-BTC-29MAR21-54000-C-option","time":"2020-03-15T10:36:46.428000000Z","coin_metrics_id":"38","amount":"0.1","price":"2.00001","database_time":"2021-12-21T10:56:24.918845000Z","side":"sell"},{"market":"deribit-BTC-29MAR21-54000-C-option","time":"2020-03-15T10:36:46.428000000Z","coin_metrics_id":"39","amount":"0.2","price":"4.00001","database_time":"2021-12-21T10:56:24.918845000Z","side":"sell"},{"market":"deribit-BTC-29MAR21-54000-C-option","time":"2021-03-15T10:36:46.428000000Z","coin_metrics_id":"41","amount":"0.3","price":"3.00001","database_time":"2021-12-21T10:56:24.918845000Z","side":"sell"},{"market":"deribit-BTC-31MAY24-67500-C-option","time":"2024-05-30T11:27:44.000000000Z","coin_metrics_id":"303557715","amount":"0.1","price":"0.0135","database_time":"2024-05-30T11:27:46.000000000Z","side":"buy","mark_price":"0.01345659","index_price":"67932.39","iv_trade":"0.4871","liquidation":"BTC-31MAY24-67500-C_liquidation"},{"market":"deribit-BTC-9APR21-50000-C-option","time":"2024-02-27T21:33:47.010246000Z","coin_metrics_id":"1","amount":"0.2","price":"2.00001","database_time":"2024-02-27T21:33:47.010523000Z","side":"buy"},{"market":"deribit-BTC-9APR21-50000-P-option","time":"2019-03-15T09:51:28.851000000Z","coin_metrics_id":"3","amount":"0.2000000000000001","price":"2.00001","database_time":"2019-12-21T10:56:24.510288000Z","side":"buy"},{"market":"deribit-BTC-9APR21-50000-P-option","time":"2019-03-15T09:51:28.851000000Z","coin_metrics_id":"15","amount":"0.1000000000000001","price":"1.00001","database_time":"2019-12-21T10:56:24.510288000Z","side":"buy"},{"market":"deribit-BTC-9APR21-50000-P-option","time":"2019-03-15T10:36:38.926000000Z","coin_metrics_id":"6","amount":"0.2","price":"3.0000100000000000001","database_time":"2019-12-21T10:56:24.918845000Z","side":"buy"},{"market":"deribit-BTC-9APR21-50000-P-option","time":"2019-03-15T10:36:40.247000000Z","coin_metrics_id":"13","amount":"0.1","price":"1.00001","database_time":"2019-12-21T10:56:24.418065000Z","side":"buy"},{"market":"deribit-BTC-9APR21-50000-P-option","time":"2019-03-15T10:36:46.275000000Z","coin_metrics_id":"14","amount":"0.1","price":"1.00001","database_time":"2019-12-21T10:56:24.418065000Z","side":"sell"},{"market":"deribit-BTC-9APR21-50000-P-option","time":"2019-03-15T10:36:46.428000000Z","coin_metrics_id":"16","amount":"0.1","price":"2.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"sell"},{"market":"deribit-BTC-9APR21-50000-P-option","time":"2019-03-16T10:33:23.804000000Z","coin_metrics_id":"9","amount":"0.2","price":"3.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"sell"},{"market":"deribit-BTC-9APR21-50000-P-option","time":"2019-03-16T10:36:01.000000000Z","coin_metrics_id":"7","amount":"0.2","price":"3.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"buy"},{"market":"deribit-BTC-9APR21-50000-P-option","time":"2019-03-16T10:36:02.999999000Z","coin_metrics_id":"8","amount":"0.2","price":"4.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"buy"},{"market":"deribit-BTC-9APR21-50000-P-option","time":"2019-03-16T10:36:24.010000000Z","coin_metrics_id":"5","amount":"0.2","price":"2.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"buy"},{"market":"deribit-BTC-9APR21-50000-P-option","time":"2019-03-16T10:36:40.247000000Z","coin_metrics_id":"1","amount":"0.2","price":"2.00001","database_time":"2019-12-21T10:56:24.418065000Z","side":"buy"},{"market":"deribit-BTC-9APR21-50000-P-option","time":"2019-03-16T10:36:46.275000000Z","coin_metrics_id":"2","amount":"0.2","price":"2.00001","database_time":"2019-12-21T10:56:24.418065000Z","side":"sell"},{"market":"deribit-BTC-9APR21-50000-P-option","time":"2019-03-16T10:36:46.428000000Z","coin_metrics_id":"4","amount":"0.2","price":"1.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"sell"},{"market":"deribit-BTC-9APR21-50000-P-option","time":"2019-03-19T23:59:59.999999000Z","coin_metrics_id":"10","amount":"0.34","price":"4.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"buy"},{"market":"deribit-BTC-9APR21-50000-P-option","time":"2019-03-20T00:00:00.000000000Z","coin_metrics_id":"11","amount":"0.53","price":"3.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"buy"},{"market":"deribit-ETH-2APR21-1960-C-option","time":"2019-03-15T10:36:46.428000000Z","coin_metrics_id":"17","amount":"0.1","price":"2.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"sell"}]}"""
        assertResponse(
            expectedCode = 200,
            expectedResponse = expectedResponse,
            pathAndQuery = "/v4/timeseries/market-trades?markets=deribit-*-option&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `test spot trades with undefined currency pair`() {
        val expectedResponse =
            """{"data":[{"market":"binance-undef314159-usdt-spot","time":"2019-03-15T10:36:46.428000000Z","coin_metrics_id":"4","amount":"0.2","price":"1.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"sell"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-trades?markets=binance-undef314159-usdt-spot&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `should return empty response when aggregated DeFi market specified`() {
        assertResponse(
            200,
            EMPTY_RESPONSE,
            "/v4/timeseries/market-trades?markets=uniswap_v3_eth-agg-usdc-weth-spot&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `should return empty response when requesting unsupported pattern market`() {
        getResponse("/v4/timeseries/market-trades?markets=zaif-*-option&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should return empty response when requesting unsupported pattern markets`() {
        getResponse("/v4/timeseries/market-trades?markets=zaif-*-option,simex-CGSEUR-*&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should return trades in CSV format`() {
        getResponse("/v4/timeseries/market-trades?markets=*-spot&format=csv&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should return deribit option trades in CSV format`() {
        val expectedResponse =
            """
            market,time,coin_metrics_id,amount,price,database_time,side,block_hash,block_height,txid,initiator,sender,beneficiary,mark_price,index_price,iv_trade,liquidation
            deribit-BTC-21JUN24-70000-P-option,2024-05-30T11:24:08.000000000Z,303557500,0.2,0.061,2024-05-30T11:24:09.000000000Z,sell,,,,,,,0.06127307,67852.63,0.4866,
            deribit-BTC-29MAR21-54000-C-option,2020-03-15T10:36:46.428000000Z,38,0.1,2.00001,2021-12-21T10:56:24.918845000Z,sell,,,,,,,,,,
            deribit-BTC-29MAR21-54000-C-option,2020-03-15T10:36:46.428000000Z,39,0.2,4.00001,2021-12-21T10:56:24.918845000Z,sell,,,,,,,,,,
            deribit-BTC-29MAR21-54000-C-option,2021-03-15T10:36:46.428000000Z,41,0.3,3.00001,2021-12-21T10:56:24.918845000Z,sell,,,,,,,,,,
            deribit-BTC-31MAY24-67500-C-option,2024-05-30T11:27:44.000000000Z,303557715,0.1,0.0135,2024-05-30T11:27:46.000000000Z,buy,,,,,,,0.01345659,67932.39,0.4871,BTC-31MAY24-67500-C_liquidation
            deribit-BTC-9APR21-50000-C-option,2024-02-27T21:33:47.010246000Z,1,0.2,2.00001,2024-02-27T21:33:47.010523000Z,buy,,,,,,,,,,
            deribit-BTC-9APR21-50000-P-option,2019-03-15T09:51:28.851000000Z,3,0.2000000000000001,2.00001,2019-12-21T10:56:24.510288000Z,buy,,,,,,,,,,
            deribit-BTC-9APR21-50000-P-option,2019-03-15T09:51:28.851000000Z,15,0.1000000000000001,1.00001,2019-12-21T10:56:24.510288000Z,buy,,,,,,,,,,
            deribit-BTC-9APR21-50000-P-option,2019-03-15T10:36:38.926000000Z,6,0.2,3.0000100000000000001,2019-12-21T10:56:24.918845000Z,buy,,,,,,,,,,
            deribit-BTC-9APR21-50000-P-option,2019-03-15T10:36:40.247000000Z,13,0.1,1.00001,2019-12-21T10:56:24.418065000Z,buy,,,,,,,,,,
            deribit-BTC-9APR21-50000-P-option,2019-03-15T10:36:46.275000000Z,14,0.1,1.00001,2019-12-21T10:56:24.418065000Z,sell,,,,,,,,,,
            deribit-BTC-9APR21-50000-P-option,2019-03-15T10:36:46.428000000Z,16,0.1,2.00001,2019-12-21T10:56:24.918845000Z,sell,,,,,,,,,,
            deribit-BTC-9APR21-50000-P-option,2019-03-16T10:33:23.804000000Z,9,0.2,3.00001,2019-12-21T10:56:24.918845000Z,sell,,,,,,,,,,
            deribit-BTC-9APR21-50000-P-option,2019-03-16T10:36:01.000000000Z,7,0.2,3.00001,2019-12-21T10:56:24.918845000Z,buy,,,,,,,,,,
            deribit-BTC-9APR21-50000-P-option,2019-03-16T10:36:02.999999000Z,8,0.2,4.00001,2019-12-21T10:56:24.918845000Z,buy,,,,,,,,,,
            deribit-BTC-9APR21-50000-P-option,2019-03-16T10:36:24.010000000Z,5,0.2,2.00001,2019-12-21T10:56:24.918845000Z,buy,,,,,,,,,,
            deribit-BTC-9APR21-50000-P-option,2019-03-16T10:36:40.247000000Z,1,0.2,2.00001,2019-12-21T10:56:24.418065000Z,buy,,,,,,,,,,
            deribit-BTC-9APR21-50000-P-option,2019-03-16T10:36:46.275000000Z,2,0.2,2.00001,2019-12-21T10:56:24.418065000Z,sell,,,,,,,,,,
            deribit-BTC-9APR21-50000-P-option,2019-03-16T10:36:46.428000000Z,4,0.2,1.00001,2019-12-21T10:56:24.918845000Z,sell,,,,,,,,,,
            deribit-BTC-9APR21-50000-P-option,2019-03-19T23:59:59.999999000Z,10,0.34,4.00001,2019-12-21T10:56:24.918845000Z,buy,,,,,,,,,,
            deribit-BTC-9APR21-50000-P-option,2019-03-20T00:00:00.000000000Z,11,0.53,3.00001,2019-12-21T10:56:24.918845000Z,buy,,,,,,,,,,
            deribit-ETH-2APR21-1960-C-option,2019-03-15T10:36:46.428000000Z,17,0.1,2.00001,2019-12-21T10:56:24.918845000Z,sell,,,,,,,,,,

            """.trimIndent()

        assertResponseWithContentType(
            expectedCode = 200,
            expectedResponse = expectedResponse,
            pathAndQuery = "/v4/timeseries/market-trades?markets=deribit-*-option&format=csv&api_key=$TEST_API_KEY",
            contentType = "text/csv",
        )
    }

    @Test
    fun `should return paged trades in CSV format with next page token and next page URL in header`() {
        val url = "/v4/timeseries/market-trades?markets=bittrex-btc-usd-spot&format=csv&page_size=5&api_key=$TEST_API_KEY"
        val expectedResponse =
            """
            market,time,coin_metrics_id,amount,price,database_time,side,block_hash,block_height,txid,initiator,sender,beneficiary,mark_price,index_price,iv_trade,liquidation
            bittrex-btc-usd-spot,2019-03-15T10:36:31.803000000Z,7,0.2,1.00001,2019-12-21T10:56:24.918845000Z,buy,,,,,,,,,,
            bittrex-btc-usd-spot,2019-03-15T10:36:38.926000000Z,6,0.1,1.0000100000000000001,2019-12-21T10:56:24.918845000Z,buy,,,,,,,,,,
            bittrex-btc-usd-spot,2019-03-15T10:36:40.247000000Z,1,0.1,1.00001,2019-12-21T10:56:24.418065000Z,buy,,,,,,,,,,
            bittrex-btc-usd-spot,2019-03-15T10:36:46.275000000Z,2,0.1,1.00001,2019-12-21T10:56:24.418065000Z,sell,,,,,,,,,,
            bittrex-btc-usd-spot,2019-03-15T10:36:46.428000000Z,4,0.1,2.00001,2019-12-21T10:56:24.918845000Z,sell,,,,,,,,,,

            """.trimIndent()
        val expectedNextPageToken = "0.MjAxOS0wMy0xNVQxMDozNjozMS44MDNafDc"
        val expectedNextPageUrlSuffix = "$url&next_page_token=$expectedNextPageToken"
        val response = getResponse(url)
        assertNotNull(response)
        assertEquals(200, response.status)
        assertEquals("text/csv", response.headers["content-type"])
        assertEquals(expectedNextPageToken, response.headers["x-next-page-token"])
        assertNotNull(response.headers["x-next-page-url"])
        assertTrue(response.headers["x-next-page-url"]!!.endsWith(expectedNextPageUrlSuffix))
        assertEquals(expectedResponse, response.body)
    }

    @Test
    fun `test erisx spot trades`() {
        getResponse("/v4/timeseries/market-trades?markets=erisx-btc-usd-spot&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `test erisx future trades`() {
        assertResponse(
            400,
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'markets'. Market 'erisx-btc-usd-future' is not supported."}}""",
            "/v4/timeseries/market-trades?markets=erisx-btc-usd-future&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `test erisx trades when wildcard is being used instead of exchange`() {
        assertResponse(
            200,
            """{"data":[{"market":"binance.us-btc-usd-spot","time":"2019-03-15T10:36:46.428000000Z","coin_metrics_id":"4","amount":"0.1","price":"2.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"sell"},{"market":"binance.us-btc-usd-spot","time":"2019-05-15T10:00:00.000000000Z","coin_metrics_id":"5","amount":"0.1","price":"2.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"sell"},{"market":"binance.us-btc-usd-spot","time":"2019-05-15T10:00:00.000000000Z","coin_metrics_id":"6","amount":"0.1","price":"2.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"sell"},{"market":"binance.us-btc-usd-spot","time":"2019-05-15T10:00:00.000000000Z","coin_metrics_id":"7","amount":"0.1","price":"2.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"sell"},{"market":"bittrex-btc-usd-spot","time":"2019-03-15T09:51:28.851000000Z","coin_metrics_id":"3","amount":"0.1000000000000001","price":"1.00001","database_time":"2019-12-21T10:56:24.510288000Z","side":"buy"},{"market":"bittrex-btc-usd-spot","time":"2019-03-15T10:33:23.804000000Z","coin_metrics_id":"9","amount":"0.3","price":"1.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"sell"},{"market":"bittrex-btc-usd-spot","time":"2019-03-15T10:36:02.701000000Z","coin_metrics_id":"8","amount":"0.1","price":"4.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"buy"},{"market":"bittrex-btc-usd-spot","time":"2019-03-15T10:36:24.010000000Z","coin_metrics_id":"5","amount":"0.1","price":"1.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"buy"},{"market":"bittrex-btc-usd-spot","time":"2019-03-15T10:36:31.803000000Z","coin_metrics_id":"7","amount":"0.2","price":"1.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"buy"},{"market":"bittrex-btc-usd-spot","time":"2019-03-15T10:36:38.926000000Z","coin_metrics_id":"6","amount":"0.1","price":"1.0000100000000000001","database_time":"2019-12-21T10:56:24.918845000Z","side":"buy"},{"market":"bittrex-btc-usd-spot","time":"2019-03-15T10:36:40.247000000Z","coin_metrics_id":"1","amount":"0.1","price":"1.00001","database_time":"2019-12-21T10:56:24.418065000Z","side":"buy"},{"market":"bittrex-btc-usd-spot","time":"2019-03-15T10:36:46.275000000Z","coin_metrics_id":"2","amount":"0.1","price":"1.00001","database_time":"2019-12-21T10:56:24.418065000Z","side":"sell"},{"market":"bittrex-btc-usd-spot","time":"2019-03-15T10:36:46.428000000Z","coin_metrics_id":"4","amount":"0.1","price":"2.00001","database_time":"2019-12-21T10:56:24.918845000Z","side":"sell"}]}""",
            "/v4/timeseries/market-trades?markets=*-btc-usd-spot&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `test erisx trades when wildcard is being used instead of exchange and no specified markets by the pattern`() {
        assertResponse(
            200,
            EMPTY_RESPONSE,
            "/v4/timeseries/market-trades?markets=*-xrp-usdc-option&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `given two markets with the same market_base_name and market_quote_name in spot_metadata`() {
        val expectedResponse = """{"data":[{"market":"hitbtc-vanry-btc-spot","time":"2024-06-15T08:45:04.000000000Z","coin_metrics_id":"2406897024","amount":"0.1","price":"0.000002765","database_time":"2024-06-15T08:51:04.000000000Z","side":"buy"}]}"""
        assertResponse(
            expectedCode = 200,
            expectedResponse = expectedResponse,
            pathAndQuery = "/v4/timeseries/market-trades?markets=hitbtc-vanry-btc-spot&api_key=$TEST_API_KEY",
        )
    }
}
