package io.coinmetrics.api.endpoints.timeseries.market

import io.coinmetrics.api.helper.BaseTest
import io.coinmetrics.api.helper.COMMUNITY_KEY
import io.coinmetrics.api.helper.POSTGRES_IMAGE
import io.coinmetrics.api.helper.TEST_API_KEY
import io.coinmetrics.api.modules.common.CommonConfig
import io.coinmetrics.api.modules.main.MainApiConfig
import io.coinmetrics.api.utils.modifyDatabases
import io.coinmetrics.databases.Database
import io.coinmetrics.databases.DatabaseImpl
import io.coinmetrics.databases.DbConfig
import io.coinmetrics.databases.NopDbMonitoring
import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.AfterAll
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.testcontainers.containers.PostgreSQLContainer
import org.testcontainers.junit.jupiter.Container
import org.testcontainers.junit.jupiter.Testcontainers
import java.time.Duration
import java.time.Instant
import java.time.ZoneOffset
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit

/*
 * This class combines tests for 3 APIs:
 * 1. Market contract prices.
 * 2. Market implied volatility.
 * 3. Market greeks.
 * The name of the test class represents the name of the database table (option_ticker).
 */
@Testcontainers
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class DynamicTickerTest : BaseTest() {
    companion object {
        @Container
        val postgres =
            PostgreSQLContainer<Nothing>(POSTGRES_IMAGE).apply {
                withInitScript("postgres/init_db_streaming.sql")
            }
    }

    private val configuration by lazy { mainApiModule.config }
    private val futuresDb: Database by lazy { DatabaseImpl(configuration.databases.futures, NopDbMonitoring) }

    override fun commonConfig(): CommonConfig = super.commonConfig().copy(statisticsPollInterval = Duration.ofMillis(200))

    override fun mainApiConfig(): MainApiConfig =
        super
            .mainApiConfig()
            .copy(realtimeMetricsUpdateFrequencyMs = 200)
            .modifyDatabases {
                copy(
                    futures =
                        DbConfig(
                            appName = "API",
                            dbConfigName = "FUTURES",
                            env = "dev",
                            jdbcUrl = "jdbc:postgresql://${postgres.host}:${postgres.firstMappedPort}/${postgres.databaseName}?user=${postgres.username}&password=${postgres.password}",
                            envVariablesResolver = envVariablesResolver,
                        ),
                    tradesDeriv =
                        DbConfig(
                            appName = "API",
                            dbConfigName = "TRADES_DERIV",
                            env = "dev",
                            jdbcUrl = "jdbc:postgresql://${postgres.host}:${postgres.firstMappedPort}/${postgres.databaseName}?user=${postgres.username}&password=${postgres.password}",
                            envVariablesResolver = envVariablesResolver,
                        ),
                )
            }

    override fun statisticsConfig() =
        super.statisticsConfig().copy(
            marketFutureTickerStatisticsRefreshIntervalMs = 200,
            marketOptionTickerStatisticsRefreshIntervalMs = 200,
            marketTradesStatisticsRefreshIntervalMs = 200,
        )

    @AfterAll
    internal fun tearDown() {
        super.stopServer()
        postgres.close()
    }

    // ------------------------------ Implied volatility ------------------------------

    @Test
    fun `should return 400 when trying to get implied volatility for spot market`() {
        val expectedResponse =
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'markets'. Only option markets are supported."}}"""
        assertResponse(
            400,
            expectedResponse,
            "/v4/timeseries/market-implied-volatility?markets=bittrex-btc-usd-spot&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `should return 400 when trying to get implied volatility for futures market`() {
        executeQuery(
            """
            INSERT INTO ${configuration.databases.tradesDeriv.schema}.trades_futures_34 (trade_id,trade_symbol,trade_amount,trade_price,trade_buy,trade_time,trade_database_time)
            VALUES 
                (1,'XBTUSD',0.2,2.00001,'t',to_timestamp(${Instant.now().epochSecond}),to_timestamp(${Instant.now().epochSecond}))
            """.trimIndent(),
            statisticsConfig.marketFutureTickerStatisticsRefreshIntervalMs.toLong(),
        )

        val expectedResponse =
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'markets'. Only option markets are supported."}}"""
        assertResponse(
            400,
            expectedResponse,
            "/v4/timeseries/market-implied-volatility?markets=bitmex-XBTUSD-future&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `should return 400 when invalid next_page_token specified`() {
        val expectedResponse =
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'next_page_token'."}}"""
        assertResponse(
            400,
            expectedResponse,
            "/v4/timeseries/market-implied-volatility?markets=deribit-BTC-9APR21-50000-C-option&api_key=$TEST_API_KEY&next_page_token=12345",
        )
    }

    @Test
    fun `should return 400 when invalid paging_from specified`() {
        assertResponse(
            400,
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'paging_from'. Value '12345' is not supported. Supported values are 'start', 'end'."}}""",
            "/v4/timeseries/market-implied-volatility?markets=deribit-ETH-2APR21-1960-C-option&api_key=$TEST_API_KEY&paging_from=12345",
        )
    }

    @Test
    fun `should return 400 when start_time is after end_time`() {
        executeQuery(
            """
            INSERT INTO ${configuration.databases.tradesDeriv.schema}.trades_option_37 (trade_id,trade_symbol,trade_amount,trade_price,trade_buy,trade_time,trade_database_time)
            VALUES 
                (1,'BTC-9APR21-50000-P',0.2,2.00001,'t',to_timestamp(${Instant.now().epochSecond}),to_timestamp(${Instant.now().epochSecond}))
            """.trimIndent(),
            statisticsConfig.marketOptionTickerStatisticsRefreshIntervalMs.toLong(),
        )

        val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSz").withZone(ZoneOffset.UTC)

        val startTime = Instant.now()
        val startTimeString = formatter.format(startTime)
        val endTimeString = formatter.format(startTime.minus(30, ChronoUnit.DAYS))

        val expectedResponse =
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'start_time'. Start time '$startTimeString' is later than the end time '$endTimeString'."}}"""
        assertResponse(
            400,
            expectedResponse,
            "/v4/timeseries/market-implied-volatility?markets=deribit-BTC-9APR21-50000-P-option&api_key=$TEST_API_KEY&start_time=$startTimeString&end_time=$endTimeString",
        )
    }

    @Test
    fun `should return 400 error when market is not supported`() {
        val expectedResponse =
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'markets'. Market 'deribit-ETH-2APR21-1960-C-option' is not supported."}}"""
        assertResponse(
            400,
            expectedResponse,
            "/v4/timeseries/market-implied-volatility?markets=deribit-ETH-2APR21-1960-C-option&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `should return empty response when no option ticker data is available for market`() {
        executeQuery(
            """
            INSERT INTO ${configuration.databases.tradesDeriv.schema}.trades_option_37 (trade_id,trade_symbol,trade_amount,trade_price,trade_buy,trade_time,trade_database_time)
            VALUES 
                (1,'BTC-9APR21-50000-C',0.2,2.00001,'t',to_timestamp(${Instant.now().epochSecond}),to_timestamp(${Instant.now().epochSecond}))
            """.trimIndent(),
            statisticsConfig.marketOptionTickerStatisticsRefreshIntervalMs.toLong(),
        )

        assertResponse(
            expectedCode = 200,
            expectedResponse =
                """{"data":[]}""",
            pathAndQuery = "/v4/timeseries/market-implied-volatility?markets=deribit-BTC-9APR21-50000-C-option&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `should return non empty response for one market`() {
        executeQuery(
            """
            INSERT INTO ${configuration.databases.futures.schema}.option_ticker (ticker_exchange_id, ticker_symbol, ticker_time, ticker_exchange_time, ticker_price_last, ticker_price_bid, ticker_price_ask, ticker_price_mark, ticker_price_index, ticker_amount_bid, ticker_amount_ask, ticker_index_name, ticker_implied_vol_trade, ticker_implied_vol_bid, ticker_implied_vol_ask, ticker_implied_vol_mark, ticker_greek_delta, ticker_greek_gamma, ticker_greek_theta, ticker_greek_vega, ticker_greek_rho, ticker_database_time)
              VALUES  (37, 'ETH-25MAR22-1200-P', '2021-09-01 13:24:00.000000 +00:00', '2021-09-01 13:24:39.774000 +00:00', 0.0225, 0.02, 0.0225, 0.02126, 3529.34, 217, 22, 'ETH-25MAR22', null, 1.1492, 1.1869, 1.1685, -0.04137, 0.00003, -0.70691, 2.47772, -1.31245, '2021-09-01 13:24:41.266168 +00:00'),
                      (37, 'ETH-25MAR22-1200-P', '2021-09-01 13:25:00.000000 +00:00', '2021-09-01 13:25:40.116000 +00:00', 0.0765, 0.068, 0.073, 0.070643, 3529.34, 97, 5, 'ETH-25MAR22', null, 0.7626, 1.089, 0.9507, 0.85444, 0.00098, -15.07377, 0.56264, 0.13455, '2021-09-01 13:25:41.266168 +00:00');
            """.trimIndent(),
            statisticsConfig.marketOptionTickerStatisticsRefreshIntervalMs.toLong(),
        )
        assertResponse(
            expectedCode = 200,
            expectedResponse =
                """{"data":[{"market":"deribit-ETH-25MAR22-1200-P-option","time":"2021-09-01T13:24:00.000000000Z","database_time":"2021-09-01T13:24:41.266168000Z","iv_bid":"1.1492","iv_ask":"1.1869","iv_mark":"1.1685","exchange_time":"2021-09-01T13:24:39.774000000Z"},{"market":"deribit-ETH-25MAR22-1200-P-option","time":"2021-09-01T13:25:00.000000000Z","database_time":"2021-09-01T13:25:41.266168000Z","iv_bid":"0.7626","iv_ask":"1.089","iv_mark":"0.9507","exchange_time":"2021-09-01T13:25:40.116000000Z"}]}""",
            pathAndQuery = "/v4/timeseries/market-implied-volatility?markets=deribit-ETH-25MAR22-1200-P-option&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `should return one item with latest date when paging from end and limit per market is one`() {
        executeQuery(
            """
            INSERT INTO ${configuration.databases.futures.schema}.option_ticker (ticker_exchange_id, ticker_symbol, ticker_time, ticker_exchange_time, ticker_price_last, ticker_price_bid, ticker_price_ask, ticker_price_mark, ticker_price_index, ticker_amount_bid, ticker_amount_ask, ticker_index_name, ticker_implied_vol_trade, ticker_implied_vol_bid, ticker_implied_vol_ask, ticker_implied_vol_mark, ticker_greek_delta, ticker_greek_gamma, ticker_greek_theta, ticker_greek_vega, ticker_greek_rho, ticker_database_time)
              VALUES  (37, 'ETH-25MAR22-1201-S', '2021-09-01 13:24:00.000000 +00:00', '2021-09-01 13:24:39.774000 +00:00', 0.0225, 0.02, 0.0225, 0.02126, 3529.34, 217, 22, 'ETH-25MAR22', null, 1.1492, 1.1869, 1.1685, -0.04137, 0.00003, -0.70691, 2.47772, -1.31245, '2021-09-01 13:24:41.266168 +00:00'),
                      (37, 'ETH-25MAR22-1201-S', '2021-09-01 13:25:00.000000 +00:00', '2021-09-01 13:25:40.116000 +00:00', 0.0765, 0.068, 0.073, 0.070643, 3529.34, 97, 5, 'ETH-25MAR22', null, 0.7626, 1.089, 0.9507, 0.85444, 0.00098, -15.07377, 0.56264, 0.13455, '2021-09-01 13:25:41.266168 +00:00');
            """.trimIndent(),
            statisticsConfig.marketOptionTickerStatisticsRefreshIntervalMs.toLong(),
        )
        assertResponse(
            expectedCode = 200,
            expectedResponse =
                """{"data":[{"market":"deribit-ETH-25MAR22-1201-S-option","time":"2021-09-01T13:25:00.000000000Z","database_time":"2021-09-01T13:25:41.266168000Z","iv_bid":"0.7626","iv_ask":"1.089","iv_mark":"0.9507","exchange_time":"2021-09-01T13:25:40.116000000Z"}]}""",
            pathAndQuery = "/v4/timeseries/market-implied-volatility?markets=deribit-ETH-25MAR22-1201-S-option&api_key=$TEST_API_KEY&paging_from=end&limit_per_market=1",
        )
    }

    @Test
    fun `should return one item per market when limit per market is one`() {
        executeQuery(
            """
            INSERT INTO ${configuration.databases.futures.schema}.option_ticker (ticker_exchange_id, ticker_symbol, ticker_time, ticker_exchange_time, ticker_price_last, ticker_price_bid, ticker_price_ask, ticker_price_mark, ticker_price_index, ticker_amount_bid, ticker_amount_ask, ticker_index_name, ticker_implied_vol_trade, ticker_implied_vol_bid, ticker_implied_vol_ask, ticker_implied_vol_mark, ticker_greek_delta, ticker_greek_gamma, ticker_greek_theta, ticker_greek_vega, ticker_greek_rho, ticker_database_time)
              VALUES  (37, 'ETH-25MAR22-1205-S', '2021-09-01 13:24:00.000000 +00:00', '2021-09-01 13:24:39.774000 +00:00', 0.0225, 0.02, 0.0225, 0.02126, 3529.34, 217, 22, 'ETH-25MAR22', null, 1.1492, 1.1869, 1.1685, -0.04137, 0.00003, -0.70691, 2.47772, -1.31245, '2021-09-01 13:24:41.266168 +00:00'),
                      (37, 'ETH-25MAR22-1205-S', '2021-09-01 13:25:00.000000 +00:00', '2021-09-01 13:25:40.116000 +00:00', 0.0765, 0.068, 0.073, 0.070643, 3529.34, 97, 5, 'ETH-25MAR22', null, 0.7626, 1.089, 0.9507, 0.85444, 0.00098, -15.07377, 0.56264, 0.13455, '2021-09-01 13:25:41.266168 +00:00'),
                      (37, 'ETH-25MAR22-1206-S', '2021-09-01 13:24:00.000000 +00:00', '2021-09-01 13:24:39.774000 +00:00', 0.0225, 0.02, 0.0225, 0.02126, 3529.34, 217, 22, 'ETH-25MAR22', null, 1.1492, 1.1869, 1.1685, -0.04137, 0.00003, -0.70691, 2.47772, -1.31245, '2021-09-01 13:24:41.266168 +00:00'),
                      (37, 'ETH-25MAR22-1206-S', '2021-09-01 13:25:00.000000 +00:00', '2021-09-01 13:25:40.116000 +00:00', 0.0765, 0.068, 0.073, 0.070643, 3529.34, 97, 5, 'ETH-25MAR22', null, 0.7626, 1.089, 0.9507, 0.85444, 0.00098, -15.07377, 0.56264, 0.13455, '2021-09-01 13:25:41.266168 +00:00');
            """.trimIndent(),
            statisticsConfig.marketOptionTickerStatisticsRefreshIntervalMs.toLong(),
        )
        assertResponse(
            expectedCode = 200,
            expectedResponse =
                """{"data":[{"market":"deribit-ETH-25MAR22-1205-S-option","time":"2021-09-01T13:25:00.000000000Z","database_time":"2021-09-01T13:25:41.266168000Z","iv_bid":"0.7626","iv_ask":"1.089","iv_mark":"0.9507","exchange_time":"2021-09-01T13:25:40.116000000Z"},{"market":"deribit-ETH-25MAR22-1206-S-option","time":"2021-09-01T13:25:00.000000000Z","database_time":"2021-09-01T13:25:41.266168000Z","iv_bid":"0.7626","iv_ask":"1.089","iv_mark":"0.9507","exchange_time":"2021-09-01T13:25:40.116000000Z"}]}""",
            pathAndQuery = "/v4/timeseries/market-implied-volatility?markets=deribit-ETH-25MAR22-1205-S-option,deribit-ETH-25MAR22-1206-S-option,&api_key=$TEST_API_KEY&limit_per_market=1",
        )
    }

    @Test
    fun `should return all items ordered by ticker_time when paging_from is end and page size is maximum`() {
        executeQuery(
            """
            INSERT INTO ${configuration.databases.futures.schema}.option_ticker (ticker_exchange_id, ticker_symbol, ticker_time, ticker_exchange_time, ticker_price_last, ticker_price_bid, ticker_price_ask, ticker_price_mark, ticker_price_index, ticker_amount_bid, ticker_amount_ask, ticker_index_name, ticker_implied_vol_trade, ticker_implied_vol_bid, ticker_implied_vol_ask, ticker_implied_vol_mark, ticker_greek_delta, ticker_greek_gamma, ticker_greek_theta, ticker_greek_vega, ticker_greek_rho, ticker_database_time)
              VALUES  (37, 'ETH-25MAR22-1202-S', '2021-09-01 13:24:00.000000 +00:00', '2021-09-01 13:24:39.774000 +00:00', 0.0225, 0.02, 0.0225, 0.02126, 3529.34, 217, 22, 'ETH-25MAR22', null, 1.1492, 1.1869, 1.1685, -0.04137, 0.00003, -0.70691, 2.47772, -1.31245, '2021-09-01 13:24:41.266168 +00:00'),
                      (37, 'ETH-25MAR22-1202-S', '2021-09-01 13:25:00.000000 +00:00', '2021-09-01 13:25:40.116000 +00:00', 0.0765, 0.068, 0.073, 0.070643, 3529.34, 97, 5, 'ETH-25MAR22', null, 0.7626, 1.089, 0.9507, 0.85444, 0.00098, -15.07377, 0.56264, 0.13455, '2021-09-01 13:25:41.266168 +00:00');
            """.trimIndent(),
            statisticsConfig.marketOptionTickerStatisticsRefreshIntervalMs.toLong(),
        )
        assertResponse(
            expectedCode = 200,
            expectedResponse =
                """{"data":[{"market":"deribit-ETH-25MAR22-1202-S-option","time":"2021-09-01T13:24:00.000000000Z","database_time":"2021-09-01T13:24:41.266168000Z","iv_bid":"1.1492","iv_ask":"1.1869","iv_mark":"1.1685","exchange_time":"2021-09-01T13:24:39.774000000Z"},{"market":"deribit-ETH-25MAR22-1202-S-option","time":"2021-09-01T13:25:00.000000000Z","database_time":"2021-09-01T13:25:41.266168000Z","iv_bid":"0.7626","iv_ask":"1.089","iv_mark":"0.9507","exchange_time":"2021-09-01T13:25:40.116000000Z"}]}""",
            pathAndQuery = "/v4/timeseries/market-implied-volatility?markets=deribit-ETH-25MAR22-1202-S-option&api_key=$TEST_API_KEY&paging_from=end&page_size=2",
        )
    }

    @Test
    fun `should filter out historical data for community key`() {
        val now = Instant.now()
        val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss").withZone(ZoneOffset.UTC)
        val expectedTimeFormatted = "${formatter.format(now)}.000000000Z"
        executeQuery(
            """
            INSERT INTO ${configuration.databases.futures.schema}.option_ticker (ticker_exchange_id, ticker_symbol, ticker_time, ticker_exchange_time, ticker_price_last, ticker_price_bid, ticker_price_ask, ticker_price_mark, ticker_price_index, ticker_amount_bid, ticker_amount_ask, ticker_index_name, ticker_implied_vol_trade, ticker_implied_vol_bid, ticker_implied_vol_ask, ticker_implied_vol_mark, ticker_greek_delta, ticker_greek_gamma, ticker_greek_theta, ticker_greek_vega, ticker_greek_rho, ticker_database_time)
              VALUES  (37, 'ETH-25MAR22-1200-Q', '2019-09-01 13:24:00.000000 +00:00', '2019-09-01 13:24:39.774000 +00:00', 0.0225, 0.02, 0.0225, 0.02126, 3529.34, 217, 22, 'ETH-25MAR22', null, 1.1492, 1.1869, 1.1685, -0.04137, 0.00003, -0.70691, 2.47772, -1.31245, '2019-09-01 13:24:41.266168 +00:00'),
                      (37, 'ETH-25MAR22-1200-Q', to_timestamp(${now.epochSecond}), to_timestamp(${now.epochSecond}), 0.0765, 0.068, 0.073, 0.070643, 3529.34, 97, 5, 'ETH-25MAR22', null, 0.7626, 1.089, 0.9507, 0.85444, 0.00098, -15.07377, 0.56264, 0.13455, to_timestamp(${now.epochSecond}));
            """.trimIndent(),
            statisticsConfig.marketOptionTickerStatisticsRefreshIntervalMs.toLong(),
        )
        assertResponse(
            expectedCode = 200,
            expectedResponse =
                """{"data":[{"market":"deribit-ETH-25MAR22-1200-Q-option","time":"$expectedTimeFormatted","database_time":"$expectedTimeFormatted","iv_bid":"0.7626","iv_ask":"1.089","iv_mark":"0.9507","exchange_time":"$expectedTimeFormatted"}]}""",
            pathAndQuery = "/v4/timeseries/market-implied-volatility?markets=deribit-ETH-25MAR22-1200-Q-option&api_key=$COMMUNITY_KEY",
        )
    }

    // -------------------------------- Contract prices --------------------------------

    @Test
    fun `should return list of contract prices`() {
        executeQuery(
            """
            INSERT INTO ${configuration.databases.futures.schema}.option_ticker (ticker_exchange_id, ticker_symbol, ticker_time, ticker_exchange_time, ticker_price_last, ticker_price_bid, ticker_price_ask, ticker_price_mark, ticker_price_index, ticker_amount_bid, ticker_amount_ask, ticker_index_name, ticker_implied_vol_trade, ticker_implied_vol_bid, ticker_implied_vol_ask, ticker_implied_vol_mark, ticker_greek_delta, ticker_greek_gamma, ticker_greek_theta, ticker_greek_vega, ticker_greek_rho, ticker_database_time)
              VALUES  (37, 'ETH-25MAR22-1203-R', '2021-09-01 13:24:00.000000 +00:00', '2021-09-01 13:24:39.774000 +00:00', 0.0225, 0.02, 0.0225, 0.02126, 3529.34, 217, 22, 'ETH-25MAR22', null, 1.1492, 1.1869, 1.1685, -0.04137, 0.00003, -0.70691, 2.47772, -1.31245, '2021-09-01 13:24:41.266168 +00:00'),
                      (37, 'ETH-25MAR22-1203-R', '2021-09-01 13:25:00.000000 +00:00', '2021-09-01 13:25:40.116000 +00:00', 0.0765, 0.068, 0.073, 0.070643, 3529.34, 97, 5, 'ETH-25MAR22', null, 0.7626, 1.089, 0.9507, 0.85444, 0.00098, -15.07377, 0.56264, 0.13455, '2021-09-01 13:25:41.266168 +00:00');
            """.trimIndent(),
            statisticsConfig.marketOptionTickerStatisticsRefreshIntervalMs.toLong(),
        )
        assertResponse(
            expectedCode = 200,
            expectedResponse =
                """{"data":[{"market":"deribit-ETH-25MAR22-1203-R-option","time":"2021-09-01T13:24:00.000000000Z","database_time":"2021-09-01T13:24:41.266168000Z","mark_price":"0.02126","index_price":"3529.34","exchange_time":"2021-09-01T13:24:39.774000000Z"},{"market":"deribit-ETH-25MAR22-1203-R-option","time":"2021-09-01T13:25:00.000000000Z","database_time":"2021-09-01T13:25:41.266168000Z","mark_price":"0.070643","index_price":"3529.34","exchange_time":"2021-09-01T13:25:40.116000000Z"}]}""",
            pathAndQuery = "/v4/timeseries/market-contract-prices?markets=deribit-ETH-25MAR22-1203-R-option&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `should return empty list of contract prices when no option ticker data is available for market`() {
        executeQuery(
            """
            INSERT INTO ${configuration.databases.tradesDeriv.schema}.trades_option_37 (trade_id,trade_symbol,trade_amount,trade_price,trade_buy,trade_time,trade_database_time)
            VALUES 
                (1,'BTC-9APR21-50001-C',0.2,2.00001,'t',to_timestamp(${Instant.now().epochSecond}),to_timestamp(${Instant.now().epochSecond}))
            """.trimIndent(),
            statisticsConfig.marketOptionTickerStatisticsRefreshIntervalMs.toLong(),
        )

        assertResponse(
            expectedCode = 200,
            expectedResponse =
                """{"data":[]}""",
            pathAndQuery = "/v4/timeseries/market-contract-prices?markets=deribit-BTC-9APR21-50001-C-option&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `should filter out historical contract prices for community key`() {
        val now = Instant.now()
        val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss").withZone(ZoneOffset.UTC)
        val expectedTimeFormatted = "${formatter.format(now)}.000000000Z"
        executeQuery(
            """
            INSERT INTO ${configuration.databases.futures.schema}.option_ticker (ticker_exchange_id, ticker_symbol, ticker_time, ticker_exchange_time, ticker_price_last, ticker_price_bid, ticker_price_ask, ticker_price_mark, ticker_price_index, ticker_amount_bid, ticker_amount_ask, ticker_index_name, ticker_implied_vol_trade, ticker_implied_vol_bid, ticker_implied_vol_ask, ticker_implied_vol_mark, ticker_greek_delta, ticker_greek_gamma, ticker_greek_theta, ticker_greek_vega, ticker_greek_rho, ticker_database_time)
              VALUES  (37, 'ETH-25MAR22-2201-Q', '2019-09-01 13:24:00.000000 +00:00', '2019-09-01 13:24:39.774000 +00:00', 0.0225, 0.02, 0.0225, 0.02126, 3529.34, 217, 22, 'ETH-25MAR22', null, 1.1492, 1.1869, 1.1685, -0.04137, 0.00003, -0.70691, 2.47772, -1.31245, '2019-09-01 13:24:41.266168 +00:00'),
                      (37, 'ETH-25MAR22-2201-Q', to_timestamp(${now.epochSecond}), to_timestamp(${now.epochSecond}), 0.0765, 0.068, 0.073, 0.070643, 3529.34, 97, 5, 'ETH-25MAR22', null, 0.7626, 1.089, 0.9507, 0.85444, 0.00098, -15.07377, 0.56264, 0.13455, to_timestamp(${now.epochSecond}));
            """.trimIndent(),
            statisticsConfig.marketOptionTickerStatisticsRefreshIntervalMs.toLong(),
        )
        assertResponse(
            expectedCode = 200,
            expectedResponse =
                """{"data":[{"market":"deribit-ETH-25MAR22-2201-Q-option","time":"$expectedTimeFormatted","database_time":"$expectedTimeFormatted","mark_price":"0.070643","index_price":"3529.34","exchange_time":"$expectedTimeFormatted"}]}""",
            pathAndQuery = "/v4/timeseries/market-contract-prices?markets=deribit-ETH-25MAR22-2201-Q-option&api_key=$COMMUNITY_KEY",
        )
    }

    // -------------------------------- Greeks --------------------------------

    @Test
    fun `should return list of greeks`() {
        executeQuery(
            """
            INSERT INTO ${configuration.databases.futures.schema}.option_ticker (ticker_exchange_id, ticker_symbol, ticker_time, ticker_exchange_time, ticker_price_last, ticker_price_bid, ticker_price_ask, ticker_price_mark, ticker_price_index, ticker_amount_bid, ticker_amount_ask, ticker_index_name, ticker_implied_vol_trade, ticker_implied_vol_bid, ticker_implied_vol_ask, ticker_implied_vol_mark, ticker_greek_delta, ticker_greek_gamma, ticker_greek_theta, ticker_greek_vega, ticker_greek_rho, ticker_database_time)
              VALUES  (37, 'ETH-25MAR22-2203-R', '2021-09-01 13:24:00.000000 +00:00', '2021-09-01 13:24:39.774000 +00:00', 0.0225, 0.02, 0.0225, 0.02126, 3529.34, 217, 22, 'ETH-25MAR22', null, 1.1492, 1.1869, 1.1685, -0.04137, 0.00003, -0.70691, 2.47772, -1.31245, '2021-09-01 13:24:41.266168 +00:00'),
                      (37, 'ETH-25MAR22-2203-R', '2021-09-01 13:25:00.000000 +00:00', '2021-09-01 13:25:40.116000 +00:00', 0.0765, 0.068, 0.073, 0.070643, 3529.34, 97, 5, 'ETH-25MAR22', null, 0.7626, 1.089, 0.9507, 0.85444, 0.00098, -15.07377, 0.56264, 0.13455, '2021-09-01 13:25:41.266168 +00:00');
            """.trimIndent(),
            statisticsConfig.marketOptionTickerStatisticsRefreshIntervalMs.toLong(),
        )
        assertResponse(
            expectedCode = 200,
            expectedResponse =
                """{"data":[{"market":"deribit-ETH-25MAR22-2203-R-option","time":"2021-09-01T13:24:00.000000000Z","database_time":"2021-09-01T13:24:41.266168000Z","vega":"2.47772","theta":"-0.70691","rho":"-1.31245","delta":"-0.04137","gamma":"0.00003","exchange_time":"2021-09-01T13:24:39.774000000Z"},{"market":"deribit-ETH-25MAR22-2203-R-option","time":"2021-09-01T13:25:00.000000000Z","database_time":"2021-09-01T13:25:41.266168000Z","vega":"0.56264","theta":"-15.07377","rho":"0.13455","delta":"0.85444","gamma":"0.00098","exchange_time":"2021-09-01T13:25:40.116000000Z"}]}""",
            pathAndQuery = "/v4/timeseries/market-greeks?markets=deribit-ETH-25MAR22-2203-R-option&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `should return 400 when trying to get greeks for futures market`() {
        executeQuery(
            """
            INSERT INTO ${configuration.databases.tradesDeriv.schema}.trades_futures_34 (trade_id,trade_symbol,trade_amount,trade_price,trade_buy,trade_time,trade_database_time)
            VALUES 
                (1,'XBTUSD',0.2,2.00002,'t',to_timestamp(${Instant.now().epochSecond}),to_timestamp(${Instant.now().epochSecond}))
            """.trimIndent(),
            statisticsConfig.marketFutureTickerStatisticsRefreshIntervalMs.toLong(),
        )

        val expectedResponse =
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'markets'. Only option markets are supported."}}"""
        assertResponse(
            400,
            expectedResponse,
            "/v4/timeseries/market-greeks?markets=bitmex-XBTUSD-future&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `should return empty list of greeks when no option ticker data is available for market`() {
        executeQuery(
            """
            INSERT INTO ${configuration.databases.tradesDeriv.schema}.trades_option_37 (trade_id,trade_symbol,trade_amount,trade_price,trade_buy,trade_time,trade_database_time)
            VALUES 
                (1,'BTC-9APR21-60001-C',0.2,2.00001,'t',to_timestamp(${Instant.now().epochSecond}),to_timestamp(${Instant.now().epochSecond}))
            """.trimIndent(),
            statisticsConfig.marketOptionTickerStatisticsRefreshIntervalMs.toLong(),
        )

        assertResponse(
            expectedCode = 200,
            expectedResponse =
                """{"data":[]}""",
            pathAndQuery = "/v4/timeseries/market-greeks?markets=deribit-BTC-9APR21-60001-C-option&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `should filter out historical greeks for community key`() {
        val now = Instant.now()
        val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss").withZone(ZoneOffset.UTC)
        val expectedTimeFormatted = "${formatter.format(now)}.000000000Z"
        executeQuery(
            """
            INSERT INTO ${configuration.databases.futures.schema}.option_ticker (ticker_exchange_id, ticker_symbol, ticker_time, ticker_exchange_time, ticker_price_last, ticker_price_bid, ticker_price_ask, ticker_price_mark, ticker_price_index, ticker_amount_bid, ticker_amount_ask, ticker_index_name, ticker_implied_vol_trade, ticker_implied_vol_bid, ticker_implied_vol_ask, ticker_implied_vol_mark, ticker_greek_delta, ticker_greek_gamma, ticker_greek_theta, ticker_greek_vega, ticker_greek_rho, ticker_database_time)
              VALUES  (37, 'ETH-25MAR22-3201-Q', '2019-09-01 13:24:00.000000 +00:00', '2019-09-01 13:24:39.774000 +00:00', 0.0225, 0.02, 0.0225, 0.02126, 3529.34, 217, 22, 'ETH-25MAR22', null, 1.1492, 1.1869, 1.1685, -0.04137, 0.00003, -0.70691, 2.47772, -1.31245, '2019-09-01 13:24:41.266168 +00:00'),
                      (37, 'ETH-25MAR22-3201-Q', to_timestamp(${now.epochSecond}), to_timestamp(${now.epochSecond}), 0.0765, 0.068, 0.073, 0.070643, 3529.34, 97, 5, 'ETH-25MAR22', null, 0.7626, 1.089, 0.9507, 0.85444, 0.00098, -15.07377, 0.56264, 0.13455, to_timestamp(${now.epochSecond}));
            """.trimIndent(),
            statisticsConfig.marketOptionTickerStatisticsRefreshIntervalMs.toLong(),
        )
        assertResponse(
            expectedCode = 200,
            expectedResponse =
                """{"data":[{"market":"deribit-ETH-25MAR22-3201-Q-option","time":"$expectedTimeFormatted","database_time":"$expectedTimeFormatted","vega":"0.56264","theta":"-15.07377","rho":"0.13455","delta":"0.85444","gamma":"0.00098","exchange_time":"$expectedTimeFormatted"}]}""",
            pathAndQuery = "/v4/timeseries/market-greeks?markets=deribit-ETH-25MAR22-3201-Q-option&api_key=$COMMUNITY_KEY",
        )
    }

    private fun executeQuery(
        query: String,
        delayBetweenCallsMs: Long,
    ) = runBlocking {
        futuresDb.update(query) {
            it.execute()
        }
        delay(delayBetweenCallsMs * 4)
    }
}
