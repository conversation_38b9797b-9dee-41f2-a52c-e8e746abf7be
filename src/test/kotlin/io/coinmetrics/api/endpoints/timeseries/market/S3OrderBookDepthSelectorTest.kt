package io.coinmetrics.api.endpoints.timeseries.market

import io.coinmetrics.api.model.DerivativesMarketType
import io.coinmetrics.api.model.ParsedMarket
import io.coinmetrics.api.service.MarketStatisticsService
import io.coinmetrics.api.statistics.market.MarketStatistics
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.CsvSource
import org.junit.jupiter.params.provider.MethodSource
import org.junit.jupiter.params.provider.ValueSource
import java.time.Instant
import java.util.stream.Stream

class S3OrderBookDepthSelectorTest {
    private lateinit var marketStatisticsService: MarketStatisticsService
    private lateinit var depthSelector: S3OrderBookDepthSelector

    private val spotMarket = ParsedMarket.ParsedSpotMarket("binance", "btc", "usdt")

    private val baseTime = Instant.parse("2025-01-01T12:00:00Z")
    private val requestStartTime = baseTime
    private val requestEndTime = baseTime.plusSeconds(3600) // 1 hour later

    @BeforeEach
    fun setup() {
        marketStatisticsService = mockk()
        depthSelector = S3OrderBookDepthSelector(marketStatisticsService)
    }

    @ParameterizedTest
    @ValueSource(ints = [101, 500, 1000, 5000, 29999, 30000])
    fun `should use depth 30000 for parsedDepth greater than 100 and less than or equal to 30000`(depth: Int) {
        val result =
            depthSelector.getApplicableDepths(
                parsedMarket = spotMarket,
                parsedDepth = depth,
                requestedStartTime = requestStartTime,
                requestedEndTime = requestEndTime,
            )

        val expectedEnforcedDepth = if (depth == 30000) null else depth
        assertEquals(
            OrderBookDepthConfig(
                enforcedDepthS3 = expectedEnforcedDepth,
                enforcedDepthDb = expectedEnforcedDepth,
                statDepth = 30000,
                bucketNamePrefix = BookBucketNamePrefixes.SPOT_FULL,
            ),
            result,
            "Failed for depth $depth",
        )
    }

    @Test
    fun `should use depth 100 for 10pct_mid_price (parsedDepth = 0)`() {
        val result =
            depthSelector.getApplicableDepths(
                parsedMarket = spotMarket,
                parsedDepth = 0,
                requestedStartTime = requestStartTime,
                requestedEndTime = requestEndTime,
            )

        assertEquals(
            OrderBookDepthConfig(
                enforcedDepthS3 = null,
                enforcedDepthDb = null,
                statDepth = 100,
                bucketNamePrefix = BookBucketNamePrefixes.SPOT_10PCT,
            ),
            result,
        )
    }

    @Test
    fun `should fall back to depth 30000 when depth 100 statistics are unavailable`() {
        // Mock no statistics for depth 100
        every { marketStatisticsService.getTieredS3BooksStatisticsForCatalogV2() } returns
            mapOf(
                100 to emptyMap(),
                30000 to mapOf("binance-btc-usdt-spot" to MarketStatistics.Statistics("2025-01-01T10:00:00Z", "2025-01-01T14:00:00Z")),
            )

        assertFalse(marketStatisticsService.getTieredS3BooksStatisticsForCatalogV2()[100]!!.containsKey(spotMarket.toString()))
        assertTrue(marketStatisticsService.getTieredS3BooksStatisticsForCatalogV2()[30000]!!.containsKey(spotMarket.toString()))

        val result =
            depthSelector.getApplicableDepths(
                parsedMarket = spotMarket,
                parsedDepth = 50,
                requestedStartTime = requestStartTime,
                requestedEndTime = requestEndTime,
            )

        assertEquals(
            OrderBookDepthConfig(
                enforcedDepthS3 = 50,
                enforcedDepthDb = 50,
                statDepth = 30000,
                bucketNamePrefix = BookBucketNamePrefixes.SPOT_FULL,
            ),
            result,
        )
    }

    @Test
    fun `should fall back to depth 30000 when requested time range is outside depth 100 statistics`() {
        // Mock statistics where depth 100 range doesn't overlap with request
        val depth100Stats = MarketStatistics.Statistics("2025-01-01T08:00:00Z", "2025-01-01T10:00:00Z")
        val depth30000Stats = MarketStatistics.Statistics("2025-01-01T06:00:00Z", "2025-01-01T16:00:00Z")

        every { marketStatisticsService.getTieredS3BooksStatisticsForCatalogV2() } returns
            mapOf(
                100 to mapOf("binance-btc-usdt-spot" to depth100Stats),
                30000 to mapOf("binance-btc-usdt-spot" to depth30000Stats),
            )

        assertTrue(marketStatisticsService.getTieredS3BooksStatisticsForCatalogV2()[100]!!.containsKey(spotMarket.toString()))
        assertTrue(marketStatisticsService.getTieredS3BooksStatisticsForCatalogV2()[30000]!!.containsKey(spotMarket.toString()))

        // Request time is after depth 100 statistics end time
        val lateStartTime = Instant.parse("2025-01-01T11:00:00Z")
        val lateEndTime = Instant.parse("2025-01-01T13:00:00Z")

        val result =
            depthSelector.getApplicableDepths(
                parsedMarket = spotMarket,
                parsedDepth = 75,
                requestedStartTime = lateStartTime,
                requestedEndTime = lateEndTime,
            )

        assertEquals(
            OrderBookDepthConfig(
                enforcedDepthS3 = 75,
                enforcedDepthDb = 75,
                statDepth = 30000,
                bucketNamePrefix = BookBucketNamePrefixes.SPOT_FULL,
            ),
            result,
        )
    }

    @Test
    fun `should fall back to depth 30000 when depth 100 data is stale more than 1 day`() {
        // Mock statistics where depth 100 is more than 1 day staler than depth 30000
        val depth100Stats = MarketStatistics.Statistics("2025-01-01T10:00:00Z", "2025-01-01T12:00:00Z")
        val depth30000Stats = MarketStatistics.Statistics("2025-01-01T10:00:00Z", "2025-01-02T13:00:00Z") // 25 hours later

        every { marketStatisticsService.getTieredS3BooksStatisticsForCatalogV2() } returns
            mapOf(
                100 to mapOf("binance-btc-usdt-spot" to depth100Stats),
                30000 to mapOf("binance-btc-usdt-spot" to depth30000Stats),
            )

        assertTrue(marketStatisticsService.getTieredS3BooksStatisticsForCatalogV2()[100]!!.containsKey(spotMarket.toString()))
        assertTrue(marketStatisticsService.getTieredS3BooksStatisticsForCatalogV2()[30000]!!.containsKey(spotMarket.toString()))

        val result =
            depthSelector.getApplicableDepths(
                parsedMarket = spotMarket,
                parsedDepth = 100,
                requestedStartTime = requestStartTime,
                requestedEndTime = requestEndTime,
            )

        assertEquals(
            OrderBookDepthConfig(
                enforcedDepthS3 = 100,
                enforcedDepthDb = 100,
                statDepth = 30000,
                bucketNamePrefix = BookBucketNamePrefixes.SPOT_FULL,
            ),
            result,
        )
    }

    @Test
    fun `should use depth 100 when all conditions are met`() {
        // Mock statistics where depth 100 is available, overlaps with request, and is fresh
        val depth100Stats = MarketStatistics.Statistics("2025-01-01T10:00:00Z", "2025-01-01T14:00:00Z")
        val depth30000Stats = MarketStatistics.Statistics("2025-01-01T10:00:00Z", "2025-01-01T14:30:00Z") // 30 minutes later

        every { marketStatisticsService.getTieredS3BooksStatisticsForCatalogV2() } returns
            mapOf(
                100 to mapOf("binance-btc-usdt-spot" to depth100Stats),
                30000 to mapOf("binance-btc-usdt-spot" to depth30000Stats),
            )

        assertTrue(marketStatisticsService.getTieredS3BooksStatisticsForCatalogV2()[100]!!.containsKey(spotMarket.toString()))
        assertTrue(marketStatisticsService.getTieredS3BooksStatisticsForCatalogV2()[30000]!!.containsKey(spotMarket.toString()))

        val result =
            depthSelector.getApplicableDepths(
                parsedMarket = spotMarket,
                parsedDepth = 100,
                requestedStartTime = requestStartTime,
                requestedEndTime = requestEndTime,
            )

        assertEquals(
            OrderBookDepthConfig(
                enforcedDepthS3 = null,
                enforcedDepthDb = 100,
                statDepth = 100,
                bucketNamePrefix = BookBucketNamePrefixes.SPOT_100,
            ),
            result,
        )
    }

    @Test
    fun `should use depth 100 with enforced depth for parsedDepth less than 100`() {
        // Mock fresh statistics
        val depth100Stats = MarketStatistics.Statistics("2025-01-01T10:00:00Z", "2025-01-01T14:00:00Z")
        val depth30000Stats = MarketStatistics.Statistics("2025-01-01T10:00:00Z", "2025-01-01T14:30:00Z")

        every { marketStatisticsService.getTieredS3BooksStatisticsForCatalogV2() } returns
            mapOf(
                100 to mapOf("binance-btc-usdt-spot" to depth100Stats),
                30000 to mapOf("binance-btc-usdt-spot" to depth30000Stats),
            )

        assertTrue(marketStatisticsService.getTieredS3BooksStatisticsForCatalogV2()[100]!!.containsKey(spotMarket.toString()))
        assertTrue(marketStatisticsService.getTieredS3BooksStatisticsForCatalogV2()[30000]!!.containsKey(spotMarket.toString()))

        val result =
            depthSelector.getApplicableDepths(
                parsedMarket = spotMarket,
                parsedDepth = 50,
                requestedStartTime = requestStartTime,
                requestedEndTime = requestEndTime,
            )

        assertEquals(
            OrderBookDepthConfig(
                enforcedDepthS3 = 50,
                enforcedDepthDb = 50,
                statDepth = 100,
                bucketNamePrefix = BookBucketNamePrefixes.SPOT_100,
            ),
            result,
        )
    }

    @ParameterizedTest
    @MethodSource("marketTypeBucketTestCases")
    fun `should return correct bucket names for different market types`(
        market: ParsedMarket,
        parsedDepth: Int,
        expectedBucketPrefix: String,
    ) {
        // Mock fresh statistics
        val depth100Stats = MarketStatistics.Statistics("2025-01-01T10:00:00Z", "2025-01-01T14:00:00Z")
        val depth30000Stats = MarketStatistics.Statistics("2025-01-01T10:00:00Z", "2025-01-01T14:30:00Z")

        every { marketStatisticsService.getTieredS3BooksStatisticsForCatalogV2() } returns
            mapOf(
                100 to mapOf(market.toString() to depth100Stats),
                30000 to mapOf(market.toString() to depth30000Stats),
            )

        val result =
            depthSelector.getApplicableDepths(
                parsedMarket = market,
                parsedDepth = parsedDepth,
                requestedStartTime = requestStartTime,
                requestedEndTime = requestEndTime,
            )

        assertEquals(expectedBucketPrefix, result.bucketNamePrefix)
    }

    companion object {
        @JvmStatic
        fun marketTypeBucketTestCases(): Stream<Arguments> {
            val spotMarket = ParsedMarket.ParsedSpotMarket("binance", "btc", "usdt")
            val futureMarket = ParsedMarket.ParsedDerivativesMarket("binance", "BTCUSDT", DerivativesMarketType.FUTURE)
            val optionMarket = ParsedMarket.ParsedDerivativesMarket("deribit", "BTC-25DEC20-20000-C", DerivativesMarketType.OPTION)

            return Stream.of(
                // Spot market tests
                Arguments.of(spotMarket, 0, BookBucketNamePrefixes.SPOT_10PCT),
                Arguments.of(spotMarket, 100, BookBucketNamePrefixes.SPOT_100),
                Arguments.of(spotMarket, 30000, BookBucketNamePrefixes.SPOT_FULL),
                // Futures market tests
                Arguments.of(futureMarket, 0, BookBucketNamePrefixes.FUTURES_10PCT),
                Arguments.of(futureMarket, 100, BookBucketNamePrefixes.FUTURES_100),
                Arguments.of(futureMarket, 30000, BookBucketNamePrefixes.FUTURES_FULL),
                // Options market tests
                Arguments.of(optionMarket, 0, BookBucketNamePrefixes.OPTIONS_10PCT),
                Arguments.of(optionMarket, 100, BookBucketNamePrefixes.OPTIONS_100),
                Arguments.of(optionMarket, 30000, BookBucketNamePrefixes.OPTIONS_FULL),
            )
        }

        @JvmStatic
        fun enforcedDepthTestCases(): Stream<Arguments> =
            Stream.of(
                Arguments.of(0, null, null, 100), // 10pct_mid_price: no enforcement needed
                Arguments.of(50, 50, 50, 100), // < 100: enforce on both S3 and DB
                Arguments.of(100, null, 100, 100), // = 100: no S3 enforcement, DB enforcement
                Arguments.of(500, 500, 500, 30000), // > 100: enforce on both S3 and DB
                Arguments.of(30000, null, null, 30000), // = 30000: no enforcement needed
            )
    }

    @Test
    fun `should handle time range boundary cases correctly when request ends exactly at statistics start time (no overlap)`() {
        val depth100Stats = MarketStatistics.Statistics("2025-01-01T12:00:00Z", "2025-01-01T14:00:00Z")
        val depth30000Stats = MarketStatistics.Statistics("2025-01-01T10:00:00Z", "2025-01-01T16:00:00Z")

        every { marketStatisticsService.getTieredS3BooksStatisticsForCatalogV2() } returns
            mapOf(
                100 to mapOf("binance-btc-usdt-spot" to depth100Stats),
                30000 to mapOf("binance-btc-usdt-spot" to depth30000Stats),
            )

        assertTrue(marketStatisticsService.getTieredS3BooksStatisticsForCatalogV2()[100]!!.containsKey(spotMarket.toString()))
        assertTrue(marketStatisticsService.getTieredS3BooksStatisticsForCatalogV2()[30000]!!.containsKey(spotMarket.toString()))

        val requestStartTime = Instant.parse("2025-01-01T10:00:00Z")
        val requestEndTime = Instant.parse("2025-01-01T11:59:59Z") // Just before stats start

        val result =
            depthSelector.getApplicableDepths(
                parsedMarket = spotMarket,
                parsedDepth = 100,
                requestedStartTime = requestStartTime,
                requestedEndTime = requestEndTime,
            )
        assertEquals(30000, result.statDepth, "Should use depth 30000 when request ends before stats start")
    }

    @Test
    fun `should handle time range boundary cases correctly when request starts exactly at statistics end time (no overlap)`() {
        val depth100Stats = MarketStatistics.Statistics("2025-01-01T12:00:00Z", "2025-01-01T14:00:00Z")
        val depth30000Stats = MarketStatistics.Statistics("2025-01-01T10:00:00Z", "2025-01-01T16:00:00Z")

        every { marketStatisticsService.getTieredS3BooksStatisticsForCatalogV2() } returns
            mapOf(
                100 to mapOf("binance-btc-usdt-spot" to depth100Stats),
                30000 to mapOf("binance-btc-usdt-spot" to depth30000Stats),
            )

        assertTrue(marketStatisticsService.getTieredS3BooksStatisticsForCatalogV2()[100]!!.containsKey(spotMarket.toString()))
        assertTrue(marketStatisticsService.getTieredS3BooksStatisticsForCatalogV2()[30000]!!.containsKey(spotMarket.toString()))

        val requestStartTime = Instant.parse("2025-01-01T14:00:01Z") // Just after stats end
        val requestEndTime = Instant.parse("2025-01-01T15:00:00Z")

        val result =
            depthSelector.getApplicableDepths(
                parsedMarket = spotMarket,
                parsedDepth = 100,
                requestedStartTime = requestStartTime,
                requestedEndTime = requestEndTime,
            )
        assertEquals(30000, result.statDepth, "Should use depth 30000 when request starts after stats end")
    }

    @Test
    fun `should handle time range boundary cases correctly when request overlaps with statistics (should use depth 100)`() {
        val depth100Stats = MarketStatistics.Statistics("2025-01-01T12:00:00Z", "2025-01-01T14:00:00Z")
        val depth30000Stats = MarketStatistics.Statistics("2025-01-01T10:00:00Z", "2025-01-01T16:00:00Z")

        every { marketStatisticsService.getTieredS3BooksStatisticsForCatalogV2() } returns
            mapOf(
                100 to mapOf("binance-btc-usdt-spot" to depth100Stats),
                30000 to mapOf("binance-btc-usdt-spot" to depth30000Stats),
            )

        val requestStartTime = Instant.parse("2025-01-01T11:30:00Z")
        val requestEndTime = Instant.parse("2025-01-01T12:30:00Z")

        val result =
            depthSelector.getApplicableDepths(
                parsedMarket = spotMarket,
                parsedDepth = 100,
                requestedStartTime = requestStartTime,
                requestedEndTime = requestEndTime,
            )
        assertEquals(100, result.statDepth, "Should use depth 100 when request overlaps with stats")
    }

    @Test
    fun `should handle staleness boundary cases correctly with exactly 1 day difference`() {
        val depth100Stats = MarketStatistics.Statistics("2025-01-01T10:00:00Z", "2025-01-01T12:00:00Z")
        val depth30000Stats = MarketStatistics.Statistics("2025-01-01T10:00:00Z", "2025-01-02T12:00:00Z") // Exactly 24 hours later

        every { marketStatisticsService.getTieredS3BooksStatisticsForCatalogV2() } returns
            mapOf(
                100 to mapOf("binance-btc-usdt-spot" to depth100Stats),
                30000 to mapOf("binance-btc-usdt-spot" to depth30000Stats),
            )

        val result1 =
            depthSelector.getApplicableDepths(
                parsedMarket = spotMarket,
                parsedDepth = 100,
                requestedStartTime = requestStartTime,
                requestedEndTime = requestEndTime,
            )
        assertEquals(30000, result1.statDepth, "Should use depth 30000 when exactly 1 day stale")
    }

    @Test
    fun `should handle staleness boundary cases correctly with less than 1 day difference`() {
        val depth100Stats = MarketStatistics.Statistics("2025-01-01T10:00:00Z", "2025-01-01T12:00:00Z")
        val depth30000Stats = MarketStatistics.Statistics("2025-01-01T10:00:00Z", "2025-01-02T11:59:59Z") // 23h 59m 59s later

        every { marketStatisticsService.getTieredS3BooksStatisticsForCatalogV2() } returns
            mapOf(
                100 to mapOf("binance-btc-usdt-spot" to depth100Stats),
                30000 to mapOf("binance-btc-usdt-spot" to depth30000Stats),
            )

        val result =
            depthSelector.getApplicableDepths(
                parsedMarket = spotMarket,
                parsedDepth = 100,
                requestedStartTime = requestStartTime,
                requestedEndTime = requestEndTime,
            )
        assertEquals(100, result.statDepth, "Should use depth 100 when less than 1 day stale")
    }

    @Test
    fun `should handle missing depth 30000 statistics gracefully`() {
        // Mock only depth 100 statistics available
        val depth100Stats = MarketStatistics.Statistics("2025-01-01T10:00:00Z", "2025-01-01T14:00:00Z")

        every { marketStatisticsService.getTieredS3BooksStatisticsForCatalogV2() } returns
            mapOf(
                100 to mapOf("binance-btc-usdt-spot" to depth100Stats),
                30000 to emptyMap(), // No depth 30000 statistics
            )

        val result =
            depthSelector.getApplicableDepths(
                parsedMarket = spotMarket,
                parsedDepth = 100,
                requestedStartTime = requestStartTime,
                requestedEndTime = requestEndTime,
            )

        // Should use depth 100 since staleness check is skipped when depth 30000 stats are missing
        assertEquals(100, result.statDepth, "Should use depth 100 when depth 30000 stats are missing")
    }

    @ParameterizedTest
    @CsvSource(
        "1, 100", // Minimum depth -> should use 100
        "99, 100", // Just below 100 -> should use 100
        "100, 100", // Exactly 100 -> should use 100
        "101, 30000", // Just above 100 -> should use 30000
        "30000, 30000", // Maximum depth -> should use 30000
    )
    fun `should handle boundary values for parsedDepth correctly in the basic cases`(
        parsedDepth: Int,
        expectedStatDepth: Int,
    ) {
        val depth100Stats = MarketStatistics.Statistics(requestStartTime.toString(), requestEndTime.toString())
        val depth30000Stats = MarketStatistics.Statistics(requestStartTime.toString(), requestEndTime.toString())

        every { marketStatisticsService.getTieredS3BooksStatisticsForCatalogV2() } returns
            mapOf(
                100 to mapOf("binance-btc-usdt-spot" to depth100Stats),
                30000 to mapOf("binance-btc-usdt-spot" to depth30000Stats),
            )

        assertTrue(marketStatisticsService.getTieredS3BooksStatisticsForCatalogV2()[100]!!.containsKey(spotMarket.toString()))
        assertTrue(marketStatisticsService.getTieredS3BooksStatisticsForCatalogV2()[30000]!!.containsKey(spotMarket.toString()))

        val result =
            depthSelector.getApplicableDepths(
                parsedMarket = spotMarket,
                parsedDepth = parsedDepth,
                requestedStartTime = requestStartTime,
                requestedEndTime = requestEndTime,
            )

        assertEquals(
            expectedStatDepth,
            result.statDepth,
            "Failed for parsedDepth $parsedDepth, expected statDepth $expectedStatDepth",
        )
    }

    @ParameterizedTest
    @MethodSource("enforcedDepthTestCases")
    fun `should handle enforced depth correctly for different scenarios`(
        parsedDepth: Int,
        expectedEnforcedS3: Int?,
        expectedEnforcedDb: Int?,
        expectedStatDepth: Int,
    ) {
        // Mock fresh statistics
        val depth100Stats = MarketStatistics.Statistics("2025-01-01T10:00:00Z", "2025-01-01T14:00:00Z")
        val depth30000Stats = MarketStatistics.Statistics("2025-01-01T10:00:00Z", "2025-01-01T14:30:00Z")

        every { marketStatisticsService.getTieredS3BooksStatisticsForCatalogV2() } returns
            mapOf(
                100 to mapOf("binance-btc-usdt-spot" to depth100Stats),
                30000 to mapOf("binance-btc-usdt-spot" to depth30000Stats),
            )

        val result =
            depthSelector.getApplicableDepths(
                parsedMarket = spotMarket,
                parsedDepth = parsedDepth,
                requestedStartTime = requestStartTime,
                requestedEndTime = requestEndTime,
            )

        assertEquals(
            expectedEnforcedS3,
            result.enforcedDepthS3,
            "Failed enforcedDepthS3 for parsedDepth $parsedDepth",
        )
        assertEquals(
            expectedEnforcedDb,
            result.enforcedDepthDb,
            "Failed enforcedDepthDb for parsedDepth $parsedDepth",
        )
        assertEquals(
            expectedStatDepth,
            result.statDepth,
            "Failed statDepth for parsedDepth $parsedDepth",
        )
    }

    @Test
    fun `should handle empty statistics maps gracefully`() {
        // Mock completely empty statistics
        every { marketStatisticsService.getTieredS3BooksStatisticsForCatalogV2() } returns
            mapOf(
                100 to emptyMap(),
                30000 to emptyMap(),
            )

        val result =
            depthSelector.getApplicableDepths(
                parsedMarket = spotMarket,
                parsedDepth = 100,
                requestedStartTime = requestStartTime,
                requestedEndTime = requestEndTime,
            )

        // Should fall back to depth 30000 when no statistics are available
        assertEquals(30000, result.statDepth, "Should fall back to depth 30000 when no statistics available")
        assertEquals(100, result.enforcedDepthS3, "Should enforce depth when falling back")
        assertEquals(100, result.enforcedDepthDb, "Should enforce depth when falling back")
    }
}
