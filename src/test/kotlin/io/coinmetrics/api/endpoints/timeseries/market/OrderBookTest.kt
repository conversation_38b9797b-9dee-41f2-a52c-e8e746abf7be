package io.coinmetrics.api.endpoints.timeseries.market

import io.coinmetrics.api.helper.BaseTest
import io.coinmetrics.api.helper.INVALID_NEXT_PAGE_TOKEN_RESPONSE
import io.coinmetrics.api.helper.TEST_API_KEY
import io.coinmetrics.api.model.S3BooksMarketType
import io.coinmetrics.api.modules.main.MainApiConfig
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
open class OrderBookTest : BaseTest() {
    override fun mainApiConfig(): MainApiConfig =
        super.mainApiConfig().copy(
            tenPercentMidPriceBookStartTime = "2019-08-19T09:42:13.000000000Z",
        )

    override fun getAdditionalEnvVariables(): Map<String, String> =
        mapOf(
            "API_${S3BooksMarketType.SPOT_BOOKS}_TIERS" to "COLD:[..2023-08-09),HOT:[2023-08-09..]",
            "API_${S3BooksMarketType.SPOT_BOOKS}_TIER_COLD_TYPE" to "S3",
            "API_${S3BooksMarketType.FUTURES_BOOKS}_TIERS" to "COLD:[..2023-08-09),HOT:[2023-08-09..]",
            "API_${S3BooksMarketType.FUTURES_BOOKS}_TIER_COLD_TYPE" to "S3",
            "API_${S3BooksMarketType.OPTIONS_BOOKS}_TIERS" to "COLD:[..2023-08-09),HOT:[2023-08-09..]",
            "API_${S3BooksMarketType.OPTIONS_BOOKS}_TIER_COLD_TYPE" to "S3",
        )

    /**
     * Tests changes in "granularity" handling.
     */
    @Test
    fun `test futures with granularity 1m and depth 100`() {
        getResponse("/v4/timeseries/market-orderbooks?markets=bittrex-XRPUSD-future&granularity=1m&depth_limit=100&start_time=2023-08-09T00:00:00Z&end_time=2023-08-09T03:00:00Z&api_key=$TEST_API_KEY").assertResponse()
    }

    /**
     * Tests changes in "granularity" handling.
     */
    @Test
    fun `test orderbook with granularity 1m and depth 100`() {
        getResponse("/v4/timeseries/market-orderbooks?markets=bittrex-xrp-usd-spot&granularity=1m&depth_limit=100&start_time=2023-08-09T00:00:00Z&end_time=2023-08-09T03:00:00Z&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `test orderbook with format=json_stream`() {
        getResponse("/v4/timeseries/market-orderbooks?markets=bittrex-btc-usd-spot&api_key=$TEST_API_KEY&end_time=2019-12-23T00:00:05.000Z&format=json_stream").assertResponse("1")
        getResponse("/v4/timeseries/market-orderbooks?markets=bittrex-btc-usd-spot&api_key=$TEST_API_KEY&end_time=2019-12-23T00:00:05.000Z&format=json_stream&pretty=true").assertResponse("2")
    }

    @Test
    fun `test orderbook error responses`() {
        listOf(
            "bittrex-invalid-asset-spot",
            "nonexistingexhange-btc-usd-spot",
        ).forEach { marketId ->
            assertResponse(
                400,
                """{"error":{"type":"bad_parameter","message":"Bad parameter 'markets'. Market '$marketId' is not supported."}}""",
                "/v4/timeseries/market-orderbooks?markets=$marketId&api_key=$TEST_API_KEY",
            )
        }

        listOf(
            "bittrex-btc-usd-test-spot",
            "bitmex-xbtusd-spot",
            "bittrex-btc-usd",
        ).forEach { marketId ->
            assertResponse(
                400,
                """{"error":{"type":"bad_parameter","message":"Bad parameter 'markets'. Incorrect market '$marketId'."}}""",
                "/v4/timeseries/market-orderbooks?markets=$marketId&api_key=$TEST_API_KEY",
            )
        }

        listOf(
            "201912-23",
            "20191223T00:00:05.000Z",
            "2019-12-23T00:00:05000Z",
        ).forEach { start_time ->
            assertResponse(
                400,
                """{"error":{"type":"bad_parameter","message":"Bad parameter 'start_time'. Incorrect time format '$start_time'. Supported formats are 'yyyy-MM-dd', 'yyyyMMdd', 'yyyy-MM-ddTHH:mm:ss', 'yyyy-MM-ddTHHmmss', 'yyyy-MM-ddTHH:mm:ss.SSS', 'yyyy-MM-ddTHHmmss.SSS', 'yyyy-MM-ddTHH:mm:ss.SSSSSS', 'yyyy-MM-ddTHHmmss.SSSSSS', 'yyyy-MM-ddTHH:mm:ss.SSSSSSSSS', 'yyyy-MM-ddTHHmmss.SSSSSSSSS'."}}""".trimMargin(),
                "/v4/timeseries/market-orderbooks?markets=bittrex-btc-usd-spot&api_key=$TEST_API_KEY&start_time=$start_time",
            )
        }
    }

    @Test
    fun `should return 400 when exceeding limit of page_size for unlimited orderbooks`() {
        val expectedResponse =
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'page_size'. Maximum value for unlimited order books is 100."}}"""
        assertResponse(
            400,
            expectedResponse,
            "/v4/timeseries/market-orderbooks?markets=bittrex-btc-usd-spot&page_size=101&depth_limit=full_book&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `should not return 400 when page_size is more than 100 and depth_limit is 100 or less`() {
        getResponse("/v4/timeseries/market-orderbooks?markets=bittrex-btc-usd-spot&page_size=101&depth_limit=100&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `test orderbook with no parameters`() {
        getResponse("/v4/timeseries/market-orderbooks?markets=bittrex-btc-usd-spot&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `test orderbook with one sided and empty books`() {
        // bittrex-eth-usd-spot books contain one-sided and empty entries
        getResponse("/v4/timeseries/market-orderbooks?markets=bittrex-eth-usd-spot&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `test orderbook with depth limit`() {
        val expectedResponse =
            """{"data":[{"market":"binance-btc-usdt-spot","time":"2019-08-19T09:42:10.301782000Z","coin_metrics_id":"2","asks":[{"price":"1.077","size":"0.4"},{"price":"1.51","size":"0.3"}],"bids":[{"price":"1.041","size":"0.1"},{"price":"1.035","size":"0.11"}],"database_time":"2019-08-19T09:42:13.038938000Z"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-orderbooks?markets=binance-btc-usdt-spot&api_key=$TEST_API_KEY&depth_limit=full_book",
        )
    }

    @Test
    fun `test orderbook with start_time and page_size`() {
        getResponse("/v4/timeseries/market-orderbooks?markets=bittrex-btc-usd-spot&api_key=$TEST_API_KEY&start_time=20190819&page_size=2&paging_from=start").assertResponse()
    }

    @Test
    fun `test orderbook with end_time, paging_from and page_size, empty`() {
        val expectedResponse =
            """{"data":[]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-orderbooks?markets=bittrex-btc-usd-spot&api_key=$TEST_API_KEY&end_time=20190818&page_size=2",
        )
    }

    @Test
    fun `test orderbook with end_time and paging_from`() {
        getResponse("/v4/timeseries/market-orderbooks?markets=bittrex-btc-usd-spot&api_key=$TEST_API_KEY&end_time=2019-12-23T00:00:05.000Z").assertResponse()
    }

    @Test
    fun `test orderbook with end_time and paging_from and depth_limit=10pct_mid_price`() {
        getResponse("/v4/timeseries/market-orderbooks?markets=bittrex-btc-usd-spot&api_key=$TEST_API_KEY&end_time=2019-12-23T00:00:05.000Z&depth_limit=10pct_mid_price").assertResponse()
    }

    @Test
    fun `test orderbook with end_time, paging_from and page_size`() {
        getResponse("/v4/timeseries/market-orderbooks?markets=binance-btc-usdt-spot&api_key=$TEST_API_KEY&end_time=2019-12-23T00:00:05.000Z&page_size=3").assertResponse()
    }

    @Test
    fun `test orderbook with cut end_time, paging_from and page_size`() {
        val expectedResponse =
            """{"data":[{"market":"binance-btc-usdt-spot","time":"2019-08-19T09:42:10.301782000Z","coin_metrics_id":"2","asks":[{"price":"1.077","size":"0.4"},{"price":"1.51","size":"0.3"}],"bids":[{"price":"1.041","size":"0.1"},{"price":"1.035","size":"0.11"}],"database_time":"2019-08-19T09:42:13.038938000Z"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-orderbooks?markets=binance-btc-usdt-spot&api_key=$TEST_API_KEY&end_time=2019-08-19T09:42:10.301782Z&page_size=3",
        )
    }

    @Test
    fun `test spot orderbook with undefined currency pair`() {
        val expectedResponse =
            """{"data":[]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-orderbooks?markets=binance-undef314159-usdt-spot&api_key=$TEST_API_KEY",
        )
    }

    // TODO: Tests with inclusive/exclusive time boundaries
    // TODO: Tests with different timezones
    // TODO: Test for the next page
    // TODO: Test for the next page not present but elements returned withing the limit
    // TODO: test invalid next page token
    // TODO: Test for invalid page_size
    // TODO: Test for invalid paging_from
    // TODO: Test for invalid timestamp ceiling for end_time and flooring for start_time

    @Test
    fun `test orderbook with incorrect key`() {
        val expectedResponse =
            """{"error":{"type":"wrong_credentials","message":"Supplied credentials are not valid."}}"""
        assertResponse(
            401,
            expectedResponse,
            "/v4/timeseries/market-orderbooks?markets=bittrex-btc-usd-spot&api_key=invalid_key&start_time=20191223&page_size=2",
        )
    }

    @Test
    fun `test orderbook with key without permissions`() {
        getResponse("/v4/timeseries/market-orderbooks?markets=bittrex-btc-usd-spot&api_key=x2&start_time=20191223&page_size=2").assertResponse()
    }

    @Test
    fun `should return orderbook for future market`() {
        getResponse("/v4/timeseries/market-orderbooks?markets=bitmex-XBTUSD-future&api_key=$TEST_API_KEY&start_time=20190723&page_size=1").assertResponse()
    }

    @Test
    fun `should return orderbook for cme market`() {
        val expectedResponse =
            """{"data":[{"market":"cme-BTCQ1-future","time":"2019-08-19T09:42:13.301783000Z","coin_metrics_id":"5","asks":[{"price":"1.079","size":"0.4"},{"price":"1.54","size":"0.3"}],"bids":[{"price":"1.044","size":"0.1"},{"price":"1.032","size":"0.11"}],"database_time":"2019-08-19T09:42:13.038938000Z"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-orderbooks?markets=cme-BTCQ1-future&api_key=$TEST_API_KEY&start_time=20190723&page_size=1",
        )
    }

    @Test
    fun `should return empty response requesting orderbook for cme market`() {
        val expectedResponse =
            """{"data":[]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-orderbooks?markets=cme-BTCQ1-future&api_key=$TEST_API_KEY&start_time=20191223&page_size=1",
        )
    }

    @Test
    fun `should return empty response when requesting orderbook for deribit option market`() {
        val expectedResponse =
            """{"data":[]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-orderbooks?markets=deribit-BTC-29MAR21-54000-C-option&api_key=$TEST_API_KEY&start_time=20191223&page_size=1",
        )
    }

    @Test
    fun `should return empty response when requesting unsupported pattern market`() {
        getResponse("/v4/timeseries/market-orderbooks?markets=zaif-*-option&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should return empty response when requesting unsupported pattern markets`() {
        getResponse("/v4/timeseries/market-orderbooks?markets=zaif-*-option,simex-CGSEUR-*&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should return one value for bids and asks when depth_limit=1`() {
        getResponse("/v4/timeseries/market-orderbooks?markets=bittrex-eth-usd-spot&api_key=$TEST_API_KEY&depth_limit=1").assertResponse()
    }

    @Test
    fun `should return two values for bids and asks when depth_limit=2`() {
        getResponse("/v4/timeseries/market-orderbooks?markets=bittrex-eth-usd-spot&api_key=$TEST_API_KEY&depth_limit=2").assertResponse()
    }

    @Test
    fun `should return two values for bids and asks when depth_limit=3 because 2 values is all we have in db`() {
        getResponse("/v4/timeseries/market-orderbooks?markets=bittrex-eth-usd-spot&api_key=$TEST_API_KEY&depth_limit=3").assertResponse()
    }

    @Test
    fun `should return orderbooks with depth=30000 when depth_limit greater than 100`() {
        val expectedResponse =
            """{"data":[{"market":"bittrex-eth-usd-spot","time":"2019-08-19T09:42:10.301782000Z","coin_metrics_id":"2","asks":[{"price":"1986.705000007","size":"1.51031355"},{"price":"1987.168","size":"0.263"}],"bids":[{"price":"1985.815","size":"1.51006499"},{"price":"1985.763","size":"1.51010212"}],"database_time":"2019-08-19T09:42:13.038938000Z"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-orderbooks?markets=bittrex-eth-usd-spot&api_key=$TEST_API_KEY&depth_limit=103",
        )
    }

    @Test
    fun `should return orderbooks with depth=30000 when depth_limit=30000`() {
        val expectedResponse =
            """{"data":[{"market":"bittrex-eth-usd-spot","time":"2019-08-19T09:42:10.301782000Z","coin_metrics_id":"2","asks":[{"price":"1986.705000007","size":"1.51031355"},{"price":"1987.168","size":"0.263"}],"bids":[{"price":"1985.815","size":"1.51006499"},{"price":"1985.763","size":"1.51010212"}],"database_time":"2019-08-19T09:42:13.038938000Z"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-orderbooks?markets=bittrex-eth-usd-spot&api_key=$TEST_API_KEY&depth_limit=30000",
        )
    }

    @Test
    fun `should return orderbooks with depth=30000 when depth_limit less than 100 but there is no orderbooks with depth=100 in database`() {
        val expectedResponse =
            """{"data":[{"market":"coinbase-eth-usd-spot","time":"2019-08-19T09:42:10.301782000Z","coin_metrics_id":"2","asks":[{"price":"1986.705000007","size":"1.51031355"},{"price":"1987.168","size":"0.263"}],"bids":[{"price":"1985.815","size":"1.51006499"},{"price":"1985.763","size":"1.51010212"}],"database_time":"2019-08-19T09:42:13.038938000Z"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-orderbooks?markets=coinbase-eth-usd-spot&api_key=$TEST_API_KEY&depth_limit=88",
        )
    }

    @Test
    fun `should return orderbooks for option market and depth=1`() {
        val expectedResponse =
            """{"data":[{"market":"deribit-ETH-1OCT21-3200-P-option","time":"2021-09-30T15:59:23.594000000Z","coin_metrics_id":"1633017563594000-0","asks":[{"price":"0.0775","size":"10"}],"bids":[{"price":"0.021","size":"11"}],"database_time":"2021-09-30T15:59:24.419026000Z"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-orderbooks?markets=deribit-ETH-1OCT21-3200-P-option&api_key=$TEST_API_KEY&depth_limit=1",
        )
    }

    @Test
    fun `should return 400 when invalid depth_limit specified`() {
        val expectedResponse =
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'depth_limit'. Supported values are 1-30000 or '10pct_mid_price' or 'full_book'."}}"""
        assertResponse(
            400,
            expectedResponse,
            "/v4/timeseries/market-orderbooks?markets=coinbase-eth-usd-spot&api_key=$TEST_API_KEY&depth_limit=invalid",
        )
    }

    @Test
    fun `should return 400 when negative depth_limit specified`() {
        val expectedResponse =
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'depth_limit'. Supported values are 1-30000 or '10pct_mid_price' or 'full_book'."}}"""
        assertResponse(
            400,
            expectedResponse,
            "/v4/timeseries/market-orderbooks?markets=coinbase-eth-usd-spot&api_key=$TEST_API_KEY&depth_limit=-1",
        )
    }

    @Test
    fun `should return 400 when next_page_token is invalid`() {
        assertResponse(
            400,
            INVALID_NEXT_PAGE_TOKEN_RESPONSE,
            "/v4/timeseries/market-orderbooks?markets=coinbase-eth-usd-spot&api_key=$TEST_API_KEY&next_page_token=10",
        )
    }

    @Test
    fun `test orderbook with default granularity and depth 100`() {
        getResponse("/v4/timeseries/market-orderbooks?markets=bittrex-xrp-usd-spot&depth_limit=100&start_time=2023-08-09T01:30:00Z&end_time=2023-08-09T02:01:00Z&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `test orderbook with granularity 10s and depth 100 results in unsupported error`() {
        assertResponse(
            400,
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'granularity'. Value '10s' is not supported. Supported values are 'raw', '1m', '1h', '1d'."}}""",
            "/v4/timeseries/market-orderbooks?markets=bittrex-xrp-usd-spot&granularity=10s&depth_limit=100&start_time=2023-08-09T01:30:00Z&end_time=2023-08-09T02:01:00Z&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `test orderbook with granularity 1h and depth 100`() {
        getResponse("/v4/timeseries/market-orderbooks?markets=bittrex-xrp-usd-spot&granularity=1h&depth_limit=100&start_time=2023-08-09T00:00:00Z&end_time=2023-08-09T03:00:00Z&api_key=$TEST_API_KEY&paging_from=start").assertResponse()
    }

    @Test
    fun `test orderbook with granularity 1d and depth 100`() {
        val expectedResponse =
            """{"data":[{"market":"bittrex-xrp-usd-spot","time":"2023-08-09T00:00:00.000000000Z","coin_metrics_id":"2","asks":[{"price":"1986.705000007","size":"1.51031355"},{"price":"1987.168","size":"0.263"}],"bids":[{"price":"1985.815","size":"1.51006499"},{"price":"1985.763","size":"1.51010212"}],"database_time":"2023-08-09T00:00:00.000000000Z"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-orderbooks?markets=bittrex-xrp-usd-spot&granularity=1d&depth_limit=100&start_time=2023-08-08T00:00:00Z&end_time=2023-08-10T00:00:00Z&api_key=$TEST_API_KEY&paging_from=start",
        )
    }

    @Test
    fun `test orderbook with default granularity and depth 30000`() {
        val expectedResponse =
            """{"data":[{"market":"bittrex-xrp-usd-spot","time":"2023-08-09T00:00:00.000000000Z","coin_metrics_id":"2","asks":[{"price":"1986.705000007","size":"1.51031355"},{"price":"1987.168","size":"0.263"}],"bids":[{"price":"1985.815","size":"1.51006499"},{"price":"1985.763","size":"1.51010212"}],"database_time":"2023-08-09T00:00:00.000000000Z"},{"market":"bittrex-xrp-usd-spot","time":"2023-08-09T01:00:00.000000000Z","coin_metrics_id":"2","asks":[{"price":"1986.705000007","size":"1.51031355"},{"price":"1987.168","size":"0.263"}],"bids":[{"price":"1985.815","size":"1.51006499"},{"price":"1985.763","size":"1.51010212"}],"database_time":"2023-08-09T01:00:00.000000000Z"},{"market":"bittrex-xrp-usd-spot","time":"2023-08-09T02:00:00.000000000Z","coin_metrics_id":"2","asks":[{"price":"1986.705000007","size":"1.51031355"},{"price":"1987.168","size":"0.263"}],"bids":[{"price":"1985.815","size":"1.51006499"},{"price":"1985.763","size":"1.51010212"}],"database_time":"2023-08-09T02:00:00.000000000Z"},{"market":"bittrex-xrp-usd-spot","time":"2023-08-09T03:00:00.000000000Z","coin_metrics_id":"2","asks":[{"price":"1986.705000007","size":"1.51031355"},{"price":"1987.168","size":"0.263"}],"bids":[{"price":"1985.815","size":"1.51006499"},{"price":"1985.763","size":"1.51010212"}],"database_time":"2023-08-09T03:00:00.000000000Z"},{"market":"bittrex-xrp-usd-spot","time":"2023-08-10T00:00:00.000000000Z","coin_metrics_id":"2","asks":[{"price":"1986.705000007","size":"1.51031355"},{"price":"1987.168","size":"0.263"}],"bids":[{"price":"1985.815","size":"1.51006499"},{"price":"1985.763","size":"1.51010212"}],"database_time":"2023-08-10T00:00:00.000000000Z"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-orderbooks?markets=bittrex-xrp-usd-spot&depth_limit=30000&start_time=2023-08-08T00:00:00Z&end_time=2023-08-11T00:00:00Z&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `test orderbook with granularity 10s and depth 30000 results in unsupported error`() {
        assertResponse(
            400,
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'granularity'. Value '10s' is not supported. Supported values are 'raw', '1m', '1h', '1d'."}}""",
            "/v4/timeseries/market-orderbooks?markets=bittrex-xrp-usd-spot&granularity=10s&depth_limit=30000&start_time=2023-08-08T00:00:00Z&end_time=2023-08-11T00:00:00Z&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `test orderbook with granularity 1m and depth 30000`() {
        val expectedResponse =
            """{"data":[{"market":"bittrex-xrp-usd-spot","time":"2023-08-09T00:00:00.000000000Z","coin_metrics_id":"2","asks":[{"price":"1986.705000007","size":"1.51031355"},{"price":"1987.168","size":"0.263"}],"bids":[{"price":"1985.815","size":"1.51006499"},{"price":"1985.763","size":"1.51010212"}],"database_time":"2023-08-09T00:00:00.000000000Z"},{"market":"bittrex-xrp-usd-spot","time":"2023-08-09T01:00:00.000000000Z","coin_metrics_id":"2","asks":[{"price":"1986.705000007","size":"1.51031355"},{"price":"1987.168","size":"0.263"}],"bids":[{"price":"1985.815","size":"1.51006499"},{"price":"1985.763","size":"1.51010212"}],"database_time":"2023-08-09T01:00:00.000000000Z"},{"market":"bittrex-xrp-usd-spot","time":"2023-08-09T02:00:00.000000000Z","coin_metrics_id":"2","asks":[{"price":"1986.705000007","size":"1.51031355"},{"price":"1987.168","size":"0.263"}],"bids":[{"price":"1985.815","size":"1.51006499"},{"price":"1985.763","size":"1.51010212"}],"database_time":"2023-08-09T02:00:00.000000000Z"},{"market":"bittrex-xrp-usd-spot","time":"2023-08-09T03:00:00.000000000Z","coin_metrics_id":"2","asks":[{"price":"1986.705000007","size":"1.51031355"},{"price":"1987.168","size":"0.263"}],"bids":[{"price":"1985.815","size":"1.51006499"},{"price":"1985.763","size":"1.51010212"}],"database_time":"2023-08-09T03:00:00.000000000Z"},{"market":"bittrex-xrp-usd-spot","time":"2023-08-10T00:00:00.000000000Z","coin_metrics_id":"2","asks":[{"price":"1986.705000007","size":"1.51031355"},{"price":"1987.168","size":"0.263"}],"bids":[{"price":"1985.815","size":"1.51006499"},{"price":"1985.763","size":"1.51010212"}],"database_time":"2023-08-10T00:00:00.000000000Z"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-orderbooks?markets=bittrex-xrp-usd-spot&granularity=1m&depth_limit=30000&start_time=2023-08-08T00:00:00Z&end_time=2023-08-11T00:00:00Z&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `test orderbook with granularity 1h and depth 30000`() {
        val expectedResponse =
            """{"data":[{"market":"bittrex-xrp-usd-spot","time":"2023-08-09T00:00:00.000000000Z","coin_metrics_id":"2","asks":[{"price":"1986.705000007","size":"1.51031355"},{"price":"1987.168","size":"0.263"}],"bids":[{"price":"1985.815","size":"1.51006499"},{"price":"1985.763","size":"1.51010212"}],"database_time":"2023-08-09T00:00:00.000000000Z"},{"market":"bittrex-xrp-usd-spot","time":"2023-08-09T01:00:00.000000000Z","coin_metrics_id":"2","asks":[{"price":"1986.705000007","size":"1.51031355"},{"price":"1987.168","size":"0.263"}],"bids":[{"price":"1985.815","size":"1.51006499"},{"price":"1985.763","size":"1.51010212"}],"database_time":"2023-08-09T01:00:00.000000000Z"},{"market":"bittrex-xrp-usd-spot","time":"2023-08-09T02:00:00.000000000Z","coin_metrics_id":"2","asks":[{"price":"1986.705000007","size":"1.51031355"},{"price":"1987.168","size":"0.263"}],"bids":[{"price":"1985.815","size":"1.51006499"},{"price":"1985.763","size":"1.51010212"}],"database_time":"2023-08-09T02:00:00.000000000Z"},{"market":"bittrex-xrp-usd-spot","time":"2023-08-09T03:00:00.000000000Z","coin_metrics_id":"2","asks":[{"price":"1986.705000007","size":"1.51031355"},{"price":"1987.168","size":"0.263"}],"bids":[{"price":"1985.815","size":"1.51006499"},{"price":"1985.763","size":"1.51010212"}],"database_time":"2023-08-09T03:00:00.000000000Z"},{"market":"bittrex-xrp-usd-spot","time":"2023-08-10T00:00:00.000000000Z","coin_metrics_id":"2","asks":[{"price":"1986.705000007","size":"1.51031355"},{"price":"1987.168","size":"0.263"}],"bids":[{"price":"1985.815","size":"1.51006499"},{"price":"1985.763","size":"1.51010212"}],"database_time":"2023-08-10T00:00:00.000000000Z"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-orderbooks?markets=bittrex-xrp-usd-spot&granularity=1h&depth_limit=30000&start_time=2023-08-08T00:00:00Z&end_time=2023-08-11T00:00:00Z&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `test orderbook with granularity 1d and depth 30000`() {
        val expectedResponse =
            """{"data":[{"market":"bittrex-xrp-usd-spot","time":"2023-08-09T00:00:00.000000000Z","coin_metrics_id":"2","asks":[{"price":"1986.705000007","size":"1.51031355"},{"price":"1987.168","size":"0.263"}],"bids":[{"price":"1985.815","size":"1.51006499"},{"price":"1985.763","size":"1.51010212"}],"database_time":"2023-08-09T00:00:00.000000000Z"},{"market":"bittrex-xrp-usd-spot","time":"2023-08-10T00:00:00.000000000Z","coin_metrics_id":"2","asks":[{"price":"1986.705000007","size":"1.51031355"},{"price":"1987.168","size":"0.263"}],"bids":[{"price":"1985.815","size":"1.51006499"},{"price":"1985.763","size":"1.51010212"}],"database_time":"2023-08-10T00:00:00.000000000Z"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-orderbooks?markets=bittrex-xrp-usd-spot&granularity=1d&depth_limit=30000&start_time=2023-08-08T00:00:00Z&end_time=2023-08-11T00:00:00Z&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `test futures with default granularity and depth 100`() {
        getResponse("/v4/timeseries/market-orderbooks?markets=bittrex-XRPUSD-future&depth_limit=100&start_time=2023-08-09T01:30:00Z&end_time=2023-08-09T02:01:00Z&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `test futures with granularity 10s and depth 100 results in unsupported error`() {
        assertResponse(
            400,
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'granularity'. Value '10s' is not supported. Supported values are 'raw', '1m', '1h', '1d'."}}""",
            "/v4/timeseries/market-orderbooks?markets=bittrex-XRPUSD-future&granularity=10s&depth_limit=100&start_time=2023-08-09T01:30:00Z&end_time=2023-08-09T02:01:00Z&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `test futures with granularity 1h and depth 100`() {
        getResponse("/v4/timeseries/market-orderbooks?markets=bittrex-XRPUSD-future&granularity=1h&depth_limit=100&start_time=2023-08-09T00:00:00Z&end_time=2023-08-09T03:00:00Z&api_key=$TEST_API_KEY&paging_from=start").assertResponse()
    }

    @Test
    fun `test futures with granularity 1d and depth 100`() {
        val expectedResponse =
            """{"data":[{"market":"bittrex-XRPUSD-future","time":"2023-08-09T00:00:00.000000000Z","coin_metrics_id":"1691539200000000-0","asks":[{"price":"1.07","size":"0.4"},{"price":"1.5","size":"0.3"}],"bids":[{"price":"1.04","size":"0.1"},{"price":"1.03","size":"0.11"}],"database_time":"2023-08-09T00:00:00.000000000Z"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-orderbooks?markets=bittrex-XRPUSD-future&granularity=1d&depth_limit=100&start_time=2023-08-08T00:00:00Z&end_time=2023-08-10T00:00:00Z&api_key=$TEST_API_KEY&paging_from=start",
        )
    }

    @Test
    fun `test futures with default granularity and depth 30000`() {
        val expectedResponse =
            """{"data":[{"market":"bittrex-XRPUSD-future","time":"2023-08-09T00:00:00.000000000Z","coin_metrics_id":"1691539200000000-0","asks":[{"price":"1.07","size":"0.4"},{"price":"1.5","size":"0.3"}],"bids":[{"price":"1.04","size":"0.1"},{"price":"1.03","size":"0.11"}],"database_time":"2023-08-09T00:00:00.000000000Z"},{"market":"bittrex-XRPUSD-future","time":"2023-08-09T01:00:00.000000000Z","coin_metrics_id":"1691542800000000-0","asks":[{"price":"1.07","size":"0.4"},{"price":"1.5","size":"0.3"}],"bids":[{"price":"1.04","size":"0.1"},{"price":"1.03","size":"0.11"}],"database_time":"2023-08-09T01:00:00.000000000Z"},{"market":"bittrex-XRPUSD-future","time":"2023-08-09T02:00:00.000000000Z","coin_metrics_id":"1691546400000000-0","asks":[{"price":"1.07","size":"0.4"},{"price":"1.5","size":"0.3"}],"bids":[{"price":"1.04","size":"0.1"},{"price":"1.03","size":"0.11"}],"database_time":"2023-08-09T02:00:00.000000000Z"},{"market":"bittrex-XRPUSD-future","time":"2023-08-09T03:00:00.000000000Z","coin_metrics_id":"1691550000000000-0","asks":[{"price":"1.07","size":"0.4"},{"price":"1.5","size":"0.3"}],"bids":[{"price":"1.04","size":"0.1"},{"price":"1.03","size":"0.11"}],"database_time":"2023-08-09T03:00:00.000000000Z"},{"market":"bittrex-XRPUSD-future","time":"2023-08-10T00:00:00.000000000Z","coin_metrics_id":"1691625600000000-0","asks":[{"price":"1.07","size":"0.4"},{"price":"1.5","size":"0.3"}],"bids":[{"price":"1.04","size":"0.1"},{"price":"1.03","size":"0.11"}],"database_time":"2023-08-10T00:00:00.000000000Z"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-orderbooks?markets=bittrex-XRPUSD-future&depth_limit=30000&start_time=2023-08-08T00:00:00Z&end_time=2023-08-11T00:00:00Z&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `test futures with granularity 10s and depth 30000 results in unsupported error`() {
        assertResponse(
            400,
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'granularity'. Value '10s' is not supported. Supported values are 'raw', '1m', '1h', '1d'."}}""",
            "/v4/timeseries/market-orderbooks?markets=bittrex-XRPUSD-future&granularity=10s&depth_limit=30000&start_time=2023-08-08T00:00:00Z&end_time=2023-08-11T00:00:00Z&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `test futures with granularity 1m and depth 30000`() {
        val expectedResponse =
            """{"data":[{"market":"bittrex-XRPUSD-future","time":"2023-08-09T00:00:00.000000000Z","coin_metrics_id":"1691539200000000-0","asks":[{"price":"1.07","size":"0.4"},{"price":"1.5","size":"0.3"}],"bids":[{"price":"1.04","size":"0.1"},{"price":"1.03","size":"0.11"}],"database_time":"2023-08-09T00:00:00.000000000Z"},{"market":"bittrex-XRPUSD-future","time":"2023-08-09T01:00:00.000000000Z","coin_metrics_id":"1691542800000000-0","asks":[{"price":"1.07","size":"0.4"},{"price":"1.5","size":"0.3"}],"bids":[{"price":"1.04","size":"0.1"},{"price":"1.03","size":"0.11"}],"database_time":"2023-08-09T01:00:00.000000000Z"},{"market":"bittrex-XRPUSD-future","time":"2023-08-09T02:00:00.000000000Z","coin_metrics_id":"1691546400000000-0","asks":[{"price":"1.07","size":"0.4"},{"price":"1.5","size":"0.3"}],"bids":[{"price":"1.04","size":"0.1"},{"price":"1.03","size":"0.11"}],"database_time":"2023-08-09T02:00:00.000000000Z"},{"market":"bittrex-XRPUSD-future","time":"2023-08-09T03:00:00.000000000Z","coin_metrics_id":"1691550000000000-0","asks":[{"price":"1.07","size":"0.4"},{"price":"1.5","size":"0.3"}],"bids":[{"price":"1.04","size":"0.1"},{"price":"1.03","size":"0.11"}],"database_time":"2023-08-09T03:00:00.000000000Z"},{"market":"bittrex-XRPUSD-future","time":"2023-08-10T00:00:00.000000000Z","coin_metrics_id":"1691625600000000-0","asks":[{"price":"1.07","size":"0.4"},{"price":"1.5","size":"0.3"}],"bids":[{"price":"1.04","size":"0.1"},{"price":"1.03","size":"0.11"}],"database_time":"2023-08-10T00:00:00.000000000Z"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-orderbooks?markets=bittrex-XRPUSD-future&granularity=1m&depth_limit=30000&start_time=2023-08-08T00:00:00Z&end_time=2023-08-11T00:00:00Z&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `test futures with granularity 1h and depth 30000`() {
        val expectedResponse =
            """{"data":[{"market":"bittrex-XRPUSD-future","time":"2023-08-09T00:00:00.000000000Z","coin_metrics_id":"1691539200000000-0","asks":[{"price":"1.07","size":"0.4"},{"price":"1.5","size":"0.3"}],"bids":[{"price":"1.04","size":"0.1"},{"price":"1.03","size":"0.11"}],"database_time":"2023-08-09T00:00:00.000000000Z"},{"market":"bittrex-XRPUSD-future","time":"2023-08-09T01:00:00.000000000Z","coin_metrics_id":"1691542800000000-0","asks":[{"price":"1.07","size":"0.4"},{"price":"1.5","size":"0.3"}],"bids":[{"price":"1.04","size":"0.1"},{"price":"1.03","size":"0.11"}],"database_time":"2023-08-09T01:00:00.000000000Z"},{"market":"bittrex-XRPUSD-future","time":"2023-08-09T02:00:00.000000000Z","coin_metrics_id":"1691546400000000-0","asks":[{"price":"1.07","size":"0.4"},{"price":"1.5","size":"0.3"}],"bids":[{"price":"1.04","size":"0.1"},{"price":"1.03","size":"0.11"}],"database_time":"2023-08-09T02:00:00.000000000Z"},{"market":"bittrex-XRPUSD-future","time":"2023-08-09T03:00:00.000000000Z","coin_metrics_id":"1691550000000000-0","asks":[{"price":"1.07","size":"0.4"},{"price":"1.5","size":"0.3"}],"bids":[{"price":"1.04","size":"0.1"},{"price":"1.03","size":"0.11"}],"database_time":"2023-08-09T03:00:00.000000000Z"},{"market":"bittrex-XRPUSD-future","time":"2023-08-10T00:00:00.000000000Z","coin_metrics_id":"1691625600000000-0","asks":[{"price":"1.07","size":"0.4"},{"price":"1.5","size":"0.3"}],"bids":[{"price":"1.04","size":"0.1"},{"price":"1.03","size":"0.11"}],"database_time":"2023-08-10T00:00:00.000000000Z"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-orderbooks?markets=bittrex-XRPUSD-future&granularity=1h&depth_limit=30000&start_time=2023-08-08T00:00:00Z&end_time=2023-08-11T00:00:00Z&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `test futures with granularity 1d and depth 30000`() {
        val expectedResponse =
            """{"data":[{"market":"bittrex-XRPUSD-future","time":"2023-08-09T00:00:00.000000000Z","coin_metrics_id":"1691539200000000-0","asks":[{"price":"1.07","size":"0.4"},{"price":"1.5","size":"0.3"}],"bids":[{"price":"1.04","size":"0.1"},{"price":"1.03","size":"0.11"}],"database_time":"2023-08-09T00:00:00.000000000Z"},{"market":"bittrex-XRPUSD-future","time":"2023-08-10T00:00:00.000000000Z","coin_metrics_id":"1691625600000000-0","asks":[{"price":"1.07","size":"0.4"},{"price":"1.5","size":"0.3"}],"bids":[{"price":"1.04","size":"0.1"},{"price":"1.03","size":"0.11"}],"database_time":"2023-08-10T00:00:00.000000000Z"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-orderbooks?markets=bittrex-XRPUSD-future&granularity=1d&depth_limit=30000&start_time=2023-08-08T00:00:00Z&end_time=2023-08-11T00:00:00Z&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `test option with default granularity`() {
        getResponse("/v4/timeseries/market-orderbooks?markets=bittrex-XRP-10AUG23-8000-C-option&start_time=2023-08-09T01:30:00Z&end_time=2023-08-09T02:01:00Z&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `test option with granularity 10s results in unsupported error`() {
        assertResponse(
            400,
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'granularity'. Value '10s' is not supported. Supported values are 'raw', '1m', '1h', '1d'."}}""",
            "/v4/timeseries/market-orderbooks?markets=bittrex-XRP-10AUG23-8000-C-option&granularity=10s&start_time=2023-08-09T01:30:00Z&end_time=2023-08-09T02:01:00Z&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `test option with granularity 1m`() {
        getResponse("/v4/timeseries/market-orderbooks?markets=bittrex-XRP-10AUG23-8000-C-option&granularity=1m&start_time=2023-08-09T00:00:00Z&end_time=2023-08-09T03:00:00Z&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `test option with granularity 1h`() {
        getResponse("/v4/timeseries/market-orderbooks?markets=bittrex-XRP-10AUG23-8000-C-option&granularity=1h&start_time=2023-08-09T00:00:00Z&end_time=2023-08-09T03:00:00Z&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `test option with granularity 1d`() {
        val expectedResponse =
            """{"data":[{"market":"bittrex-XRP-10AUG23-8000-C-option","time":"2023-08-09T00:00:00.000000000Z","coin_metrics_id":"1691539200000000-0","asks":[{"price":"0.1045","size":"243"}],"bids":[{"price":"0.101","size":"514"}],"database_time":"2023-08-09T00:00:00.000000000Z"},{"market":"bittrex-XRP-10AUG23-8000-C-option","time":"2023-08-10T00:00:00.000000000Z","coin_metrics_id":"1691625600000000-0","asks":[{"price":"0.1045","size":"243"}],"bids":[{"price":"0.101","size":"514"}],"database_time":"2023-08-09T00:00:00.000000000Z"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-orderbooks?markets=bittrex-XRP-10AUG23-8000-C-option&granularity=1d&start_time=2023-08-08T00:00:00Z&end_time=2023-08-10T00:00:00Z&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `test orderbook with granularity and timezone`() {
        val expectedResponse =
            """{"data":[{"market":"bittrex-xrp-usd-spot","time":"2023-08-09T00:00:00.000000000Z","coin_metrics_id":"2","asks":[{"price":"1986.705000007","size":"1.51031355"},{"price":"1987.168","size":"0.263"}],"bids":[{"price":"1985.815","size":"1.51006499"},{"price":"1985.763","size":"1.51010212"}],"database_time":"2023-08-09T00:00:00.000000000Z"},{"market":"bittrex-xrp-usd-spot","time":"2023-08-09T01:00:00.000000000Z","coin_metrics_id":"2","asks":[{"price":"1986.705000007","size":"1.51031355"},{"price":"1987.168","size":"0.263"}],"bids":[{"price":"1985.815","size":"1.51006499"},{"price":"1985.763","size":"1.51010212"}],"database_time":"2023-08-09T01:00:00.000000000Z"},{"market":"bittrex-xrp-usd-spot","time":"2023-08-09T02:00:00.000000000Z","coin_metrics_id":"2","asks":[{"price":"1986.705000007","size":"1.51031355"},{"price":"1987.168","size":"0.263"}],"bids":[{"price":"1985.815","size":"1.51006499"},{"price":"1985.763","size":"1.51010212"}],"database_time":"2023-08-09T02:00:00.000000000Z"},{"market":"bittrex-xrp-usd-spot","time":"2023-08-09T03:00:00.000000000Z","coin_metrics_id":"2","asks":[{"price":"1986.705000007","size":"1.51031355"},{"price":"1987.168","size":"0.263"}],"bids":[{"price":"1985.815","size":"1.51006499"},{"price":"1985.763","size":"1.51010212"}],"database_time":"2023-08-09T03:00:00.000000000Z"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-orderbooks?markets=bittrex-xrp-usd-spot&granularity=1h&depth_limit=30000&start_time=2023-08-09T03:00:00Z&end_time=2023-08-09T06:00:00Z&timezone=GMT%2b03:00&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `test future with granularity and timezone`() {
        val expectedResponse =
            """{"data":[{"market":"bittrex-XRPUSD-future","time":"2023-08-09T00:00:00.000000000Z","coin_metrics_id":"1691539200000000-0","asks":[{"price":"1.07","size":"0.4"},{"price":"1.5","size":"0.3"}],"bids":[{"price":"1.04","size":"0.1"},{"price":"1.03","size":"0.11"}],"database_time":"2023-08-09T00:00:00.000000000Z"},{"market":"bittrex-XRPUSD-future","time":"2023-08-09T01:00:00.000000000Z","coin_metrics_id":"1691542800000000-0","asks":[{"price":"1.07","size":"0.4"},{"price":"1.5","size":"0.3"}],"bids":[{"price":"1.04","size":"0.1"},{"price":"1.03","size":"0.11"}],"database_time":"2023-08-09T01:00:00.000000000Z"},{"market":"bittrex-XRPUSD-future","time":"2023-08-09T02:00:00.000000000Z","coin_metrics_id":"1691546400000000-0","asks":[{"price":"1.07","size":"0.4"},{"price":"1.5","size":"0.3"}],"bids":[{"price":"1.04","size":"0.1"},{"price":"1.03","size":"0.11"}],"database_time":"2023-08-09T02:00:00.000000000Z"},{"market":"bittrex-XRPUSD-future","time":"2023-08-09T03:00:00.000000000Z","coin_metrics_id":"1691550000000000-0","asks":[{"price":"1.07","size":"0.4"},{"price":"1.5","size":"0.3"}],"bids":[{"price":"1.04","size":"0.1"},{"price":"1.03","size":"0.11"}],"database_time":"2023-08-09T03:00:00.000000000Z"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-orderbooks?markets=bittrex-XRPUSD-future&granularity=1h&depth_limit=30000&start_time=2023-08-09T03:00:00Z&end_time=2023-08-09T06:00:00Z&timezone=GMT%2b03:00&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `test option with granularity and timezone`() {
        val expectedResponse =
            """{"data":[{"market":"bittrex-XRP-10AUG23-8000-C-option","time":"2023-08-09T00:00:00.000000000Z","coin_metrics_id":"1691539200000000-0","asks":[{"price":"0.1045","size":"243"}],"bids":[{"price":"0.101","size":"514"}],"database_time":"2023-08-09T00:00:00.000000000Z"},{"market":"bittrex-XRP-10AUG23-8000-C-option","time":"2023-08-09T01:00:00.000000000Z","coin_metrics_id":"1691542800000000-0","asks":[{"price":"0.1045","size":"243"}],"bids":[{"price":"0.101","size":"514"}],"database_time":"2023-08-09T01:00:00.000000000Z"},{"market":"bittrex-XRP-10AUG23-8000-C-option","time":"2023-08-09T02:00:00.000000000Z","coin_metrics_id":"1691546400000000-0","asks":[{"price":"0.1045","size":"243"}],"bids":[{"price":"0.101","size":"514"}],"database_time":"2023-08-09T02:00:00.000000000Z"},{"market":"bittrex-XRP-10AUG23-8000-C-option","time":"2023-08-09T03:00:00.000000000Z","coin_metrics_id":"1691550000000000-0","asks":[{"price":"0.1045","size":"243"}],"bids":[{"price":"0.101","size":"514"}],"database_time":"2023-08-09T03:00:00.000000000Z"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-orderbooks?markets=bittrex-XRP-10AUG23-8000-C-option&granularity=1h&depth_limit=30000&start_time=2023-08-09T03:00:00Z&end_time=2023-08-09T06:00:00Z&timezone=GMT%2b03:00&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `test orderbook with multiple frequencies`() {
        assertResponse(
            400,
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'granularity'. Value '10s,1m' is not supported. Supported values are 'raw', '1m', '1h', '1d'."}}""",
            "/v4/timeseries/market-orderbooks?markets=bittrex-xrp-usd-spot&granularity=10s,1m&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `test orderbook with unsupported granularity`() {
        assertResponse(
            400,
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'granularity'. Value '1s' is not supported. Supported values are 'raw', '1m', '1h', '1d'."}}""",
            "/v4/timeseries/market-orderbooks?markets=bittrex-xrp-usd-spot&granularity=1s&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `test the market where the maximum time for depth 30000 exceeds 1 day compared to depth 100`() {
        val expectedResponse = """{"data":[{"market":"coinbase_derivatives-BCHQ24-future","time":"2024-08-13T11:00:00.000000000Z","coin_metrics_id":"1723546800000000-0","asks":[{"price":"10","size":"348.7"},{"price":"10","size":"348.75"}],"bids":[{"price":"10","size":"348.3"},{"price":"10","size":"348.2"}],"database_time":"2024-08-13T11:00:01.373296000Z"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/market-orderbooks?markets=coinbase_derivatives-BCHQ24-future&depth_limit=100&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `test all options`() {
        getResponse("/v4/timeseries/market-orderbooks?markets=*-option&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `test all futures`() {
        getResponse("/v4/timeseries/market-orderbooks?markets=*-future&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `given raw granularity and depth limit 10pct_mid_price when paging from end`() {
        getResponse("/v4/timeseries/market-orderbooks?markets=deribit-ETH-1OCT21-2850-P-option&granularity=raw&depth_limit=10pct_mid_price&paging_from=end&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `given raw granularity and depth limit 10pct_mid_price when paging from start`() {
        getResponse("/v4/timeseries/market-orderbooks?markets=deribit-ETH-1OCT21-2850-P-option&granularity=raw&depth_limit=10pct_mid_price&paging_from=start&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `given 1m granularity and depth limit 10pct_mid_price when paging from end`() {
        getResponse("/v4/timeseries/market-orderbooks?markets=deribit-ETH-1OCT21-2850-P-option&granularity=1m&depth_limit=10pct_mid_price&paging_from=end&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `given 1m granularity and depth limit 10pct_mid_price when paging from start`() {
        getResponse("/v4/timeseries/market-orderbooks?markets=deribit-ETH-1OCT21-2850-P-option&granularity=1m&depth_limit=10pct_mid_price&paging_from=start&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `given raw granularity and depth limit 100 when paging from end`() {
        getResponse("/v4/timeseries/market-orderbooks?markets=deribit-ETH-1OCT21-2850-P-option&granularity=raw&depth_limit=100&paging_from=end&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `given raw granularity and depth limit 100 when paging from start`() {
        getResponse("/v4/timeseries/market-orderbooks?markets=deribit-ETH-1OCT21-2850-P-option&granularity=raw&depth_limit=100&paging_from=start&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `given 1m granularity and depth limit 100 when paging from end`() {
        getResponse("/v4/timeseries/market-orderbooks?markets=deribit-ETH-1OCT21-2850-P-option&granularity=1m&depth_limit=100&paging_from=end&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `given 1m granularity and depth limit 100 when paging from start`() {
        getResponse("/v4/timeseries/market-orderbooks?markets=deribit-ETH-1OCT21-2850-P-option&granularity=1m&depth_limit=100&paging_from=start&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `given 1h granularity and depth limit 100 when paging from end`() {
        getResponse("/v4/timeseries/market-orderbooks?markets=deribit-ETH-1OCT21-2850-P-option&granularity=1h&depth_limit=100&paging_from=end&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `given 1h granularity and depth limit 100 when paging from start`() {
        getResponse("/v4/timeseries/market-orderbooks?markets=deribit-ETH-1OCT21-2850-P-option&granularity=1h&depth_limit=100&paging_from=start&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `given raw granularity and depth limit 30000 when paging from end`() {
        getResponse("/v4/timeseries/market-orderbooks?markets=bittrex-XRP-10AUG23-8000-C-option&granularity=raw&depth_limit=30000&paging_from=end&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `given raw granularity and depth limit 30000 when paging from start`() {
        getResponse("/v4/timeseries/market-orderbooks?markets=bittrex-XRP-10AUG23-8000-C-option&granularity=raw&depth_limit=30000&paging_from=start&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `given 1h granularity and depth limit 30000 when paging from end`() {
        getResponse("/v4/timeseries/market-orderbooks?markets=bittrex-XRP-10AUG23-8000-C-option&granularity=1h&depth_limit=30000&paging_from=end&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `given 1h granularity and depth limit 30000 when paging from start`() {
        getResponse("/v4/timeseries/market-orderbooks?markets=bittrex-XRP-10AUG23-8000-C-option&granularity=1h&depth_limit=30000&paging_from=start&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `given 1d granularity and depth limit 30000 when paging from end`() {
        getResponse("/v4/timeseries/market-orderbooks?markets=bittrex-XRP-10AUG23-8000-C-option&granularity=1d&depth_limit=30000&paging_from=end&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `given 1d granularity and depth limit 30000 when paging from start`() {
        getResponse("/v4/timeseries/market-orderbooks?markets=bittrex-XRP-10AUG23-8000-C-option&granularity=1d&depth_limit=30000&paging_from=start&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `given 1m granularity and depth limit 1 when requested time range falls outside depth 100 statistics range`() {
        getResponse("/v4/timeseries/market-orderbooks?markets=bittrex-XRPUSD-future&granularity=1m&depth_limit=1&start_time=2023-08-09T23:00:00&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `given 1m granularity and depth limit 100 when requested time range falls outside depth 100 statistics range`() {
        getResponse("/v4/timeseries/market-orderbooks?markets=bittrex-XRPUSD-future&granularity=1m&depth_limit=100&start_time=2023-08-09T23:00:00&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `given 1m granularity and depth limit 1 and page size when paging from start`() {
        getResponse("/v4/timeseries/market-orderbooks?markets=bittrex-XRPUSD-future&granularity=1m&depth_limit=1&page_size=1&paging_from=start&api_key=$TEST_API_KEY").assertResponse("1")

        getResponse("/v4/timeseries/market-orderbooks?markets=bittrex-XRPUSD-future&granularity=1m&depth_limit=1&page_size=1&paging_from=start&api_key=$TEST_API_KEY&next_page_token=0.MjAyMy0wOC0wOVQwMDowMDowMFo").assertResponse("2")

        getResponse("/v4/timeseries/market-orderbooks?markets=bittrex-XRPUSD-future&granularity=1m&depth_limit=1&page_size=1&paging_from=start&api_key=$TEST_API_KEY&next_page_token=0.MjAyMy0wOC0wOVQwMTowMDowMFo").assertResponse("3")

        getResponse("/v4/timeseries/market-orderbooks?markets=bittrex-XRPUSD-future&granularity=1m&depth_limit=1&page_size=1&paging_from=start&api_key=$TEST_API_KEY&next_page_token=0.MjAyMy0wOC0wOVQwMTo1OTo1MFo").assertResponse("4")

        getResponse("/v4/timeseries/market-orderbooks?markets=bittrex-XRPUSD-future&granularity=1m&depth_limit=1&page_size=1&paging_from=start&api_key=$TEST_API_KEY&next_page_token=0.MjAyMy0wOC0wOVQwMjowMDowMFo").assertResponse("5")

        getResponse("/v4/timeseries/market-orderbooks?markets=bittrex-XRPUSD-future&granularity=1m&depth_limit=1&page_size=1&paging_from=start&api_key=$TEST_API_KEY&next_page_token=0.MjAyMy0wOC0wOVQwMjowMDoxMFo").assertResponse("6")

        getResponse("/v4/timeseries/market-orderbooks?markets=bittrex-XRPUSD-future&granularity=1m&depth_limit=1&page_size=1&paging_from=start&api_key=$TEST_API_KEY&next_page_token=0.MjAyMy0wOC0wOVQwMjowMDoyMFo").assertResponse("7")

        getResponse("/v4/timeseries/market-orderbooks?markets=bittrex-XRPUSD-future&granularity=1m&depth_limit=1&page_size=1&paging_from=start&api_key=$TEST_API_KEY&next_page_token=0.MjAyMy0wOC0wOVQwMjowMDozMFo").assertResponse("8")

        getResponse("/v4/timeseries/market-orderbooks?markets=bittrex-XRPUSD-future&granularity=1m&depth_limit=1&page_size=1&paging_from=start&api_key=$TEST_API_KEY&next_page_token=0.MjAyMy0wOC0wOVQwMjowMDo0MFo").assertResponse("9")

        getResponse("/v4/timeseries/market-orderbooks?markets=bittrex-XRPUSD-future&granularity=1m&depth_limit=1&page_size=1&paging_from=start&api_key=$TEST_API_KEY&next_page_token=0.MjAyMy0wOC0wOVQwMjowMDo1MFo").assertResponse("10")
    }

    @Test
    fun `given 1m granularity and depth limit 1 and page size when paging from end`() {
        getResponse("/v4/timeseries/market-orderbooks?markets=bittrex-XRPUSD-future&granularity=1m&depth_limit=1&page_size=1&paging_from=end&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `given missing values during downsampling`() {
        getResponse("/v4/timeseries/market-orderbooks?markets=coinbase-btc-usd-spot&depth_limit=30000&start_time=2025-05-21T00:00:00&end_time=2025-05-22T00:00:00&paging_from=start&page_size=50&api_key=$TEST_API_KEY").assertResponse()
    }
}
