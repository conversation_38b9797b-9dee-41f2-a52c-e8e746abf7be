package io.coinmetrics.api.endpoints.timeseries.index

import com.fasterxml.jackson.module.kotlin.readValue
import io.coinmetrics.api.helper.BaseTest
import io.coinmetrics.api.helper.COMMUNITY_INDEXES_KEY_2
import io.coinmetrics.api.helper.COMMUNITY_KEY
import io.coinmetrics.api.helper.TEST_API_KEY
import io.coinmetrics.api.helper.TEST_API_KEY_2
import io.coinmetrics.api.models.IndexConstituents
import io.coinmetrics.api.models.IndexConstituentsConstituents
import io.coinmetrics.api.models.IndexConstituentsResponse
import io.coinmetrics.testing.autoexpect.AutoExpect
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import java.time.LocalDateTime
import java.time.ZoneOffset
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit
import java.util.Base64

@ExtendWith(AutoExpect::class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class ConstituentsTest : BaseTest() {
    @Test
    fun `should return 400 when index name is CMBIETHTt`() {
        getResponse("/v4/timeseries/index-constituents?indexes=CMBIETHTt&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should return 400 when index name is CMBIBTCTotal_price`() {
        getResponse("/v4/timeseries/index-constituents?indexes=CMBIBTCTotal_price&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should return 400 when index name is nonexistingbase-btc-total_price`() {
        getResponse("/v4/timeseries/index-constituents?indexes=nonexistingbase-btc-total_price&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should return 400 when index name is cmbi-nonexistingasset-total_price`() {
        getResponse("/v4/timeseries/index-constituents?indexes=cmbi-nonexistingasset-total_price&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should return 400 when index name is fid-eth-total_price`() {
        getResponse("/v4/timeseries/index-constituents?indexes=fid-eth-total_price&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should return 400 when index name is cmbi-btc`() {
        getResponse("/v4/timeseries/index-constituents?indexes=cmbi-btc&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should return 400 when end_time parameter is in incorrect format`() {
        listOf(
            "201912-23",
            "20191223T00:00:05.000Z",
            "2019-12-23T00:00:05000Z",
        ).forEach { end_time ->
            assertResponse(
                400,
                """{"error":{"type":"bad_parameter","message":"Bad parameter 'end_time'. Incorrect time format '$end_time'. Supported formats are 'yyyy-MM-dd', 'yyyyMMdd', 'yyyy-MM-ddTHH:mm:ss', 'yyyy-MM-ddTHHmmss', 'yyyy-MM-ddTHH:mm:ss.SSS', 'yyyy-MM-ddTHHmmss.SSS', 'yyyy-MM-ddTHH:mm:ss.SSSSSS', 'yyyy-MM-ddTHHmmss.SSSSSS', 'yyyy-MM-ddTHH:mm:ss.SSSSSSSSS', 'yyyy-MM-ddTHHmmss.SSSSSSSSS'."}}""".trimMargin(),
                "/v4/timeseries/index-constituents?indexes=CMBIETHT&api_key=$TEST_API_KEY&end_time=$end_time",
            )
        }
    }

    @Test
    fun `test constituents cmbi with no parameters`() {
        getResponse("/v4/timeseries/index-constituents?indexes=CMBI10&api_key=$TEST_API_KEY&paging_from=start").assertResponse()
    }

    @Test
    fun `test constituents cmbi with no parameters in a csv format`() {
        val expectedJsonResponse = getResponse("/v4/timeseries/index-constituents?indexes=CMBI10&api_key=$TEST_API_KEY&paging_from=start")
        expectedJsonResponse.assertResponse(idPrefix = "json")

        val expectedCsvResponse = getResponse("/v4/timeseries/index-constituents?indexes=CMBI10&format=csv&api_key=$TEST_API_KEY&paging_from=start")
        expectedCsvResponse.assertResponse(idPrefix = "csv")

        assertThat(jsonResponseToIndexConstituents(expectedJsonResponse.body))
            .usingRecursiveComparison()
            .isEqualTo(csvResponseToIndexConstituents(expectedCsvResponse.body))
    }

    @Test
    fun `should return 400 when requested CMBIAUE index constituents but data is not available`() {
        getResponse("/v4/timeseries/index-constituents?indexes=CMBIAUE&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should return 400 when requested CMBIAUE index constituents in csv format but data is not available`() {
        getResponse("/v4/timeseries/index-constituents?indexes=CMBIAUE&format=csv&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should return 400 when requested CMBIAUE index constituents for key that is not supported`() {
        getResponse("/v4/timeseries/index-constituents?indexes=CMBIAUE&api_key=$COMMUNITY_KEY").assertResponse()
    }

    @ParameterizedTest
    @ValueSource(strings = ["CMBI10", "cmbi10"])
    fun `test constituents cmbi start_time and page_size`(index: String) {
        getResponse("/v4/timeseries/index-constituents?indexes=$index&api_key=$TEST_API_KEY&start_time=20200502&page_size=2&paging_from=start").assertResponse()
    }

    @Test
    fun `test constituents cmbi with end time and paging_from`() {
        getResponse("/v4/timeseries/index-constituents?indexes=CMBI10&api_key=$TEST_API_KEY&end_time=2020-05-01T21:00:00").assertResponse()
    }

    @Test
    fun `test constituents cmbi with end time and paging_from and page_size`() {
        getResponse("/v4/timeseries/index-constituents?indexes=CMBI10&api_key=$TEST_API_KEY&end_time=2020-05-01T21:00:00&page_size=1").assertResponse()
    }

    @Test
    fun `test constituents cmbi with end time and paging_from and page_size in a csv format`() {
        val expectedResponse =
            """{"data":[{"index":"CMBI10","time":"2020-05-01T21:00:00.000000000Z","constituents":[{"asset":"bch","weight":"0.01903616488928038"},{"asset":"bsv","weight":"0.01354425765502612"},{"asset":"btc","weight":"0.7467986579928611"},{"asset":"etc","weight":"0.004584981150212094"},{"asset":"eth","weight":"0.1351506756421716"},{"asset":"ltc","weight":"0.01775264280108264"},{"asset":"xlm","weight":"0.008754226607142499"},{"asset":"xmr","weight":"0.006489823161695304"},{"asset":"xrp","weight":"0.03782672240812172"},{"asset":"xtz","weight":"0.01006184769240656"}]}],"next_page_token":"0.MjAyMC0wNS0wMVQyMTowMDowMFo","next_page_url":"http://127.0.0.1:8080/v4/timeseries/index-constituents?indexes=CMBI10&api_key=$TEST_API_KEY&end_time=2020-05-01T21%3A00%3A00&page_size=1&next_page_token=0.MjAyMC0wNS0wMVQyMTowMDowMFo"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/index-constituents?indexes=CMBI10&api_key=$TEST_API_KEY&end_time=2020-05-01T21:00:00&page_size=1",
        )

        val expectedResponseCsv =
            """
            index,time,asset,weight
            CMBI10,2020-05-01T21:00:00.000000000Z,bch,0.01903616488928038
            CMBI10,2020-05-01T21:00:00.000000000Z,bsv,0.01354425765502612
            CMBI10,2020-05-01T21:00:00.000000000Z,btc,0.7467986579928611
            CMBI10,2020-05-01T21:00:00.000000000Z,etc,0.004584981150212094
            CMBI10,2020-05-01T21:00:00.000000000Z,eth,0.1351506756421716
            CMBI10,2020-05-01T21:00:00.000000000Z,ltc,0.01775264280108264
            CMBI10,2020-05-01T21:00:00.000000000Z,xlm,0.008754226607142499
            CMBI10,2020-05-01T21:00:00.000000000Z,xmr,0.006489823161695304
            CMBI10,2020-05-01T21:00:00.000000000Z,xrp,0.03782672240812172
            CMBI10,2020-05-01T21:00:00.000000000Z,xtz,0.01006184769240656

            """.trimIndent()
        assertResponseWithContentType(
            expectedCode = 200,
            expectedResponse = expectedResponseCsv,
            contentType = "text/csv",
            pathAndQuery = "/v4/timeseries/index-constituents?indexes=CMBI10&format=csv&api_key=$TEST_API_KEY&end_time=2020-05-01T21:00:00&page_size=1",
        )

        assertThat(jsonResponseToIndexConstituents(expectedResponse))
            .usingRecursiveComparison()
            .isEqualTo(csvResponseToIndexConstituents(expectedResponseCsv))
    }

    @Test
    fun `test constituents cmbibtc`() {
        getResponse("/v4/timeseries/index-constituents?indexes=CMBIBTC&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `page 1`() {
        getResponse("/v4/timeseries/index-constituents?indexes=CMBI10&api_key=$TEST_API_KEY&page_size=2").assertResponse()
    }

    @Test
    fun `page 2`() {
        getResponse("/v4/timeseries/index-constituents?indexes=CMBI10&api_key=$TEST_API_KEY&page_size=2&next_page_token=0.MjAyMC0wNS0wMVQyMjowMDowMFo").assertResponse()
    }

    @ParameterizedTest
    @ValueSource(strings = ["CMBI10", "cmbi10"])
    fun `dynamic constituents, 1d-ny-close frequency`(index: String) {
        getResponse("/v4/timeseries/index-constituents?indexes=$index&api_key=$TEST_API_KEY&frequency=1d-ny-close").assertResponse()
    }

    @Test
    fun `dynamic constituents, 1d frequency`() {
        getResponse("/v4/timeseries/index-constituents?indexes=CMBI10&api_key=$TEST_API_KEY&frequency=1d").assertResponse()
    }

    @Test
    fun `dynamic constituents, 1h frequency`() {
        getResponse("/v4/timeseries/index-constituents?indexes=CMBI10&api_key=$TEST_API_KEY&frequency=1h&page_size=2").assertResponse()
    }

    @Test
    fun `fixed constituents, 1d frequency`() {
        getResponse("/v4/timeseries/index-constituents?indexes=CMBIBTC&api_key=$TEST_API_KEY&frequency=1d&page_size=2&end_time=2020-09-28").assertResponse()
    }

    @Test
    fun `fixed constituents, 1d frequency, paging from start`() {
        getResponse("/v4/timeseries/index-constituents?indexes=CMBIBTC&api_key=$TEST_API_KEY&frequency=1d&page_size=2&paging_from=start").assertResponse()
    }

    @Test
    fun `fixed constituents, 1d-ny-close frequency, paging from start`() {
        getResponse("/v4/timeseries/index-constituents?indexes=CMBIBTC&api_key=$TEST_API_KEY&frequency=1d-ny-close&page_size=2&paging_from=start").assertResponse()
    }

    @Test
    fun `fixed constituents, 1d-ny-close frequency, paging from start with time range`() {
        getResponse("/v4/timeseries/index-constituents?indexes=CMBIBTC&api_key=$TEST_API_KEY&start_time=2021-10-02T11:00:00&end_time=2021-10-08T11:00:00&frequency=1d-ny-close&page_size=2&paging_from=start").assertResponse()
    }

    @Test
    fun `fixed constituents, 1d-ny-close frequency, paging from end`() {
        getResponse("/v4/timeseries/index-constituents?indexes=CMBIBTC&api_key=$TEST_API_KEY&start_time=2021-10-02T11:00:00&end_time=2021-10-08T11:00:00&frequency=1d-ny-close&page_size=2&paging_from=end").assertResponse()
    }

    @Test
    fun `fixed constituents, 1d-ny-close frequency, paging from end, corner case on end_time`() {
        getResponse("/v4/timeseries/index-constituents?indexes=CMBIBTC&api_key=$TEST_API_KEY&start_time=2021-10-02T11:00:00&end_time=2021-10-08T20:00:00&frequency=1d-ny-close&page_size=2&paging_from=end").assertResponse()
    }

    @Test
    fun `fixed constituents, 1d-ny-close frequency, paging from end, corner case on end_time and end_inclusive=false`() {
        // If we don't use end_time here, test will be valid for a day and will fail on the next one
        getResponse("/v4/timeseries/index-constituents?indexes=CMBIBTC&api_key=$TEST_API_KEY&start_time=2021-10-02T11:00:00&end_time=2021-10-08T20:00:00&end_inclusive=false&frequency=1d-ny-close&page_size=2&paging_from=end").assertResponse()
    }

    @Test
    fun `fixed constituents, 1h frequency`() {
        getResponse("/v4/timeseries/index-constituents?indexes=CMBIBTC&api_key=$TEST_API_KEY&frequency=1h&page_size=2&end_time=2020-09-28").assertResponse()
    }

    @Test
    fun `should return all fidelity index-constituents`() {
        getResponse("/v4/timeseries/index-constituents?indexes=FIDBEIP&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should return fidelity index-constituents by frequency=1h`() {
        val now = LocalDateTime.now()
        val nowTruncatedToHours = now.truncatedTo(ChronoUnit.HOURS)
        val tenHoursAgo = nowTruncatedToHours.minusHours(5)

        fun LocalDateTime.plusHoursAndFormat(hours: Int) =
            DateTimeFormatter
                .ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSSSSSSS'Z'")
                .withZone(ZoneOffset.UTC)
                .format(this.plusHours(hours.toLong()))

        fun LocalDateTime.toNextPageToken(): String {
            val dateTimeString =
                DateTimeFormatter
                    .ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'")
                    .withZone(ZoneOffset.UTC)
                    .format(this)
            return "0." + String(Base64.getUrlEncoder().withoutPadding().encode(dateTimeString.toByteArray()))
        }

        val nextPageToken = nowTruncatedToHours.minusHours(4).toNextPageToken()

        val expectedResponse =
            """{"data":[{"index":"FIDBEIP","time":"${
                tenHoursAgo.plusHoursAndFormat(
                    1,
                )
            }","constituents":[{"asset":"eth","weight":"0.350869989251677"}]},{"index":"FIDBEIP","time":"${
                tenHoursAgo.plusHoursAndFormat(
                    2,
                )
            }","constituents":[{"asset":"eth","weight":"0.350869989251677"}]},{"index":"FIDBEIP","time":"${
                tenHoursAgo.plusHoursAndFormat(
                    3,
                )
            }","constituents":[{"asset":"eth","weight":"0.350869989251677"}]},{"index":"FIDBEIP","time":"${
                tenHoursAgo.plusHoursAndFormat(
                    4,
                )
            }","constituents":[{"asset":"eth","weight":"0.350869989251677"}]},{"index":"FIDBEIP","time":"${
                tenHoursAgo.plusHoursAndFormat(
                    5,
                )
            }","constituents":[{"asset":"eth","weight":"0.350869989251677"}]}],"next_page_token":"$nextPageToken","next_page_url":"http://127.0.0.1:8080/v4/timeseries/index-constituents?indexes=FIDBEIP&api_key=x1&frequency=1h&page_size=5&next_page_token=$nextPageToken"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/index-constituents?indexes=FIDBEIP&api_key=$TEST_API_KEY&frequency=1h&page_size=5",
        )
    }

    @Test
    fun `should return CMBIBTC index-constituents by frequency=1h in time range`() {
        getResponse("/v4/timeseries/index-constituents?indexes=CMBIBTC&api_key=$TEST_API_KEY&frequency=1h&page_size=2&start_time=2022-03-19T01:00:00&end_time=2022-03-19T04:00:00").assertResponse()
    }

    @Test
    fun `should return CMBIBTC index-constituents by frequency=1h in time range when there is less items than requested`() {
        getResponse("/v4/timeseries/index-constituents?indexes=CMBIBTC&api_key=$TEST_API_KEY&frequency=1h&page_size=3&start_time=2022-03-19T01:00:00&end_time=2022-03-19T02:00:00").assertResponse()
    }

    @Test
    fun `should return empty list of CMBIBTC index-constituents by frequency=1d-ny-close when there is no data`() {
        getResponse("/v4/timeseries/index-constituents?indexes=CMBIBTC&api_key=$TEST_API_KEY&frequency=1d-ny-close&page_size=3&start_time=2022-03-19T01:00:00&end_time=2022-03-19T02:00:00").assertResponse()
    }

    @Test
    fun `should return CMBIBTC index-constituents by frequency=1h in time range paging from start`() {
        getResponse("/v4/timeseries/index-constituents?indexes=CMBIBTC&api_key=$TEST_API_KEY&frequency=1h&paging_from=start&page_size=2&start_time=2022-03-19T01:00:00&end_time=2022-03-19T04:00:00").assertResponse()
    }

    @Test
    fun `should return first page of fidelity index-constituents`() {
        getResponse("/v4/timeseries/index-constituents?indexes=FIDBEIP&api_key=$TEST_API_KEY&page_size=2").assertResponse()
    }

    @Test
    fun `should return second page of fidelity index-constituents`() {
        getResponse("/v4/timeseries/index-constituents?indexes=FIDBEIP&api_key=$TEST_API_KEY&page_size=2&next_page_token=0.MjAyMi0wMy0xOVQwMzowMDowMFo").assertResponse()
    }

    @Test
    fun `should return 400 when wildcard is invalid`() {
        getResponse("/v4/timeseries/index-constituents?indexes=*BTC*&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should return empty data when wildcard does not match any known index`() {
        getResponse("/v4/timeseries/index-constituents?indexes=XYZ*&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should return empty data when wildcards does not match any known index`() {
        getResponse("/v4/timeseries/index-constituents?indexes=XYZ*,ZYX*&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should return 400 when wildcard is valid but specific index in not supported`() {
        getResponse("/v4/timeseries/index-constituents?indexes=CMBI*,DUMMY&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should return empty data when wildcard match indexes but there is no data in DB`() {
        getResponse("/v4/timeseries/index-constituents?indexes=AA*&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should return all indexes with constituents when asterisk specified`() {
        getResponse("/v4/timeseries/index-constituents?indexes=*&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should return indexes with constituents when searching indexes by prefix`() {
        getResponse("/v4/timeseries/index-constituents?indexes=CMBI*&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should return indexes with constituents when searching indexes by suffix`() {
        getResponse("/v4/timeseries/index-constituents?indexes=*BTC&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should return indexes with constituents when pattern and specific index used`() {
        getResponse("/v4/timeseries/index-constituents?indexes=FID*,CMBIHASH&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should return indexes with constituents when search patterns intersect`() {
        getResponse("/v4/timeseries/index-constituents?indexes=CMBI*,*BTC&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should return 403 when key has no access to specific index`() {
        getResponse("/v4/timeseries/index-constituents?indexes=CMBI*,FIDBTCT&api_key=$COMMUNITY_KEY").assertResponse()
    }

    @Test
    fun `should return 403 trying to access CMBIBTC index by API key with no permissions`() {
        getResponse("/v4/timeseries/index-constituents?indexes=CMBIBTC&frequency=1h&api_key=$TEST_API_KEY_2").assertResponse()
    }

    @Test
    fun `should return 403 trying to access CMBIBTC and CMBIETH index by API key with no permissions`() {
        getResponse("/v4/timeseries/index-constituents?indexes=CMBIBTC,CMBIETH&frequency=1h&api_key=$TEST_API_KEY_2").assertResponse()
    }

    @Test
    fun `should return 403 trying to access CMBIBTC, FIDBTCT, and CMBIETH index by API key with no permissions`() {
        getResponse("/v4/timeseries/index-constituents?indexes=CMBIBTC,FIDBTCT,CMBIETH&frequency=1h&api_key=$COMMUNITY_INDEXES_KEY_2").assertResponse()
    }

    @Test
    fun `should return first page of constituents when asterisk specified`() {
        getResponse("/v4/timeseries/index-constituents?indexes=*&api_key=$TEST_API_KEY&paging_from=start&page_size=20").assertResponse()
    }

    @Test
    fun `should return second page of constituents when asterisk specified`() {
        getResponse("/v4/timeseries/index-constituents?indexes=*&api_key=$TEST_API_KEY&paging_from=start&page_size=20&next_page_token=RklEQlRDUA.MjAxNC0xMi0zMVQwMDowMDowMFo").assertResponse()
    }

    @ParameterizedTest
    @ValueSource(strings = ["x4", "x4md"])
    fun `should return no constituents data when key has cmbi_indexes_community package and no access to CMBI indexes`(apiKey: String) {
        getResponse("/v4/timeseries/index-constituents?indexes=CMBI*&api_key=$apiKey").assertResponse()
    }

    @Test
    fun `should return full data when key has access to community and CMBI indexes package`() {
        getResponse("/v4/timeseries/index-constituents?indexes=CMBI*&api_key=$TEST_API_KEY").assertResponse()
    }

    private fun jsonResponseToIndexConstituents(jsonResponse: String): List<IndexConstituents> {
        val indexConstituentsResponse = commonModule.objectMapper.readValue<IndexConstituentsResponse>(jsonResponse)
        return indexConstituentsResponse.data.toList()
    }

    private fun csvResponseToIndexConstituents(csvResponse: String): List<IndexConstituents> {
        return csvResponse
            .lines()
            .drop(1)
            .mapNotNull { line ->
                val fields = line.split(",")
                if (fields.size < 2) return@mapNotNull null
                CsvItem(
                    index = fields[0],
                    time = fields[1],
                    asset = fields.getOrNull(2),
                    weight = fields.getOrNull(3),
                )
            }.groupBy { it.index to it.time }
            .map { (indexToTime, csvItems) ->
                val constituents =
                    csvItems.map {
                        IndexConstituentsConstituents(asset = it.asset, weight = it.weight)
                    }
                val (index, time) = indexToTime
                IndexConstituents(index = index, time = time, constituents = constituents)
            }
    }

    private data class CsvItem(
        // Name of the index.
        val index: String,
        // The time in ISO 8601 date-time format. Always with nanoseconds precision.
        val time: String,
        // Unique name of the asset.
        val asset: String? = null,
        // The weight of the constituent.
        val weight: String? = null,
    )
}
