package io.coinmetrics.api.endpoints.timeseries.market

import io.coinmetrics.api.helper.BaseTest
import io.coinmetrics.api.helper.Containers
import io.coinmetrics.api.modules.common.CommonConfig
import io.coinmetrics.api.modules.main.MainApiConfig
import io.coinmetrics.api.utils.modifyDatabases
import io.coinmetrics.databases.DbConfig
import org.junit.jupiter.api.TestInstance
import java.time.Duration

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
open class OptionTickerBaseTest : BaseTest() {
    override fun commonConfig(): CommonConfig = super.commonConfig().copy(statisticsPollInterval = Duration.ofMillis(200))

    override fun mainApiConfig(): MainApiConfig =
        super
            .mainApiConfig()
            .copy(realtimeMetricsUpdateFrequencyMs = 200)
            .modifyDatabases {
                copy(
                    futures =
                        DbConfig(
                            appName = "API",
                            dbConfigName = "FUTURES",
                            env = "dev",
                            jdbcUrl = "jdbc:postgresql://${Containers.postgreSQLContainer.host}:${Containers.postgreSQLContainer.port}/test?user=postgres",
                            envVariablesResolver = envVariablesResolver,
                        ),
                    tradesDeriv =
                        DbConfig(
                            appName = "API",
                            dbConfigName = "TRADES_DERIV",
                            env = "dev",
                            jdbcUrl = "jdbc:postgresql://${Containers.postgreSQLContainer.host}:${Containers.postgreSQLContainer.port}/test?user=postgres",
                            envVariablesResolver = envVariablesResolver,
                        ),
                )
            }

    override fun statisticsConfig() =
        super.statisticsConfig().copy(
            marketOptionTickerStatisticsRefreshIntervalMs = 200,
        )

    protected fun test(
        pathAndQuery: String,
        expectedCode: Int,
        expectedResponse: String,
    ) {
        assertResponse(
            expectedCode = expectedCode,
            expectedResponse = expectedResponse,
            pathAndQuery = pathAndQuery,
        )
    }
}
