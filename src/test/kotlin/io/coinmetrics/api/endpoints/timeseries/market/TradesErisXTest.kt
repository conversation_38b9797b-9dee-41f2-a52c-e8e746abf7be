package io.coinmetrics.api.endpoints.timeseries.market

import io.coinmetrics.api.helper.BaseTest
import io.coinmetrics.api.helper.ERIS_X_API_KEY
import io.coinmetrics.api.helper.ONLY_ERIS_X_API_KEY
import io.coinmetrics.api.helper.TEST_API_KEY
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class TradesErisXTest : BaseTest() {
    @Test
    fun `given api key with market_data_feed_package when all markets requested with wildcard`() {
        getResponse("/v4/timeseries/market-trades?markets=*&api_key=$ERIS_X_API_KEY").assertResponse()
    }

    @Test
    fun `given api key with access to erisx only when all markets requested with wildcard`() {
        getResponse("/v4/timeseries/market-trades?markets=*&api_key=$ONLY_ERIS_X_API_KEY").assertResponse()
    }

    @Test
    fun `given api key with market_data_feed_package when all erisx markets requested with wildcard`() {
        getResponse("/v4/timeseries/market-trades?markets=erisx-*&api_key=$ERIS_X_API_KEY").assertResponse()
    }

    @Test
    fun `given api key with market_data_feed_package when all erisx spot markets requested with wildcard`() {
        getResponse("/v4/timeseries/market-trades?markets=erisx-*-spot&api_key=$ERIS_X_API_KEY").assertResponse()
    }

    @Test
    fun `given api key with market_data_feed_package when all erisx future markets requested with wildcard`() {
        getResponse("/v4/timeseries/market-trades?markets=erisx-*-future&api_key=$ERIS_X_API_KEY").assertResponse()
    }

    @Test
    fun `given api key with market_data_feed_package when all erisx option markets requested with wildcard`() {
        getResponse("/v4/timeseries/market-trades?markets=erisx-*-option&api_key=$ERIS_X_API_KEY").assertResponse()
    }

    @Test
    fun `given api key with market_data_feed_package when specific erisx market requested`() {
        getResponse("/v4/timeseries/market-trades?markets=erisx-btc-usd-spot&api_key=$ERIS_X_API_KEY").assertResponse()
    }

    @Test
    fun `given api key without erisx access when wildcard market is requested`() {
        getResponse("/v4/timeseries/market-trades?markets=erisx-*&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `given api key without erisx access when specific market is requested`() {
        getResponse("/v4/timeseries/market-trades?markets=erisx-btc-usd-spot&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `given api key with market_data_feed_package when binance trades for wildcard market requested`() {
        getResponse("/v4/timeseries/market-trades?markets=binance-*&api_key=$ERIS_X_API_KEY").assertResponse()
    }

    @Test
    fun `given api key without access when binance trades for wildcard market requested`() {
        getResponse("/v4/timeseries/market-trades?markets=binance-*&api_key=$ONLY_ERIS_X_API_KEY").assertResponse()
    }

    @Test
    fun `given api key with market_data_feed_package when binance trades for specific market requested`() {
        getResponse("/v4/timeseries/market-trades?markets=binance-XBTUSD-future&api_key=$ERIS_X_API_KEY").assertResponse()
    }

    @Test
    fun `given api key without access when binance trades for specific market requested`() {
        getResponse("/v4/timeseries/market-trades?markets=binance-XBTUSD-future&api_key=$ONLY_ERIS_X_API_KEY").assertResponse()
    }
}
