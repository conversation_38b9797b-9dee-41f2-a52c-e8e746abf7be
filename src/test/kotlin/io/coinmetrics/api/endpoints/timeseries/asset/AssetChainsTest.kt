package io.coinmetrics.api.endpoints.timeseries.asset

import com.fasterxml.jackson.module.kotlin.readValue
import io.coinmetrics.api.helper.BaseTest
import io.coinmetrics.api.helper.EMPTY_CSV_RESPONSE
import io.coinmetrics.api.helper.TEST_API_KEY
import io.coinmetrics.api.models.AssetChainsResponse
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class AssetChainsTest : BaseTest() {
    @Test
    fun `asset chains`() {
        val expectedResponse =
            """{"data":[{"asset":"btc","time":"2021-01-01T08:00:00.000000000Z","chains_count":"1","blocks_count_at_tip":"1","chains":[[{"time":"2021-01-01T08:00:00.000000000Z","hash":"aa","height":"1"}]]},{"asset":"btc","time":"2021-01-02T08:00:00.000000000Z","chains_count":"2","blocks_count_at_tip":"2","chains":[[{"time":"2021-01-02T08:00:00.000000000Z","hash":"aa","height":"1"}]]},{"asset":"btc","time":"2021-01-02T08:00:00.000000000Z","chains_count":"2","blocks_count_at_tip":"2","chains":[[{"time":"2021-01-01T08:00:00.000000000Z","hash":"aa","height":"1"},{"time":"2021-01-02T08:00:00.000000000Z","hash":"bb","height":"2"}],[{"time":"2021-01-01T08:00:00.000000000Z","hash":"aa","height":"1"},{"time":"2021-01-02T08:00:01.000000000Z","hash":"cc","height":"2"}]]},{"asset":"btc","time":"2021-01-04T08:00:00.000000000Z","chains_count":"2","blocks_count_at_tip":"1","chains":[[{"time":"2021-01-01T08:00:00.000000000Z","hash":"aa","height":"1"},{"time":"2021-01-02T08:00:00.000000000Z","hash":"bb","height":"2"}],[{"time":"2021-01-01T08:00:00.000000000Z","hash":"aa","height":"1"},{"time":"2021-01-02T08:00:01.000000000Z","hash":"cc","height":"2"},{"time":"2021-01-04T08:00:00.000000000Z","hash":"dd","height":"3"}]],"reorg":"true","reorg_depth":"1"}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/asset-chains?assets=btc&api_key=$TEST_API_KEY",
            "X-Request-Timestamp",
            "1622472895000",
        )
    }

    @Test
    fun `asset chains in a csv format`() {
        val expectedResponse = """{"data":[{"asset":"btc","time":"2021-01-01T08:00:00.000000000Z","chains_count":"1","blocks_count_at_tip":"1","chains":[[{"time":"2021-01-01T08:00:00.000000000Z","hash":"aa","height":"1"}]]},{"asset":"btc","time":"2021-01-02T08:00:00.000000000Z","chains_count":"2","blocks_count_at_tip":"2","chains":[[{"time":"2021-01-02T08:00:00.000000000Z","hash":"aa","height":"1"}]]},{"asset":"btc","time":"2021-01-02T08:00:00.000000000Z","chains_count":"2","blocks_count_at_tip":"2","chains":[[{"time":"2021-01-01T08:00:00.000000000Z","hash":"aa","height":"1"},{"time":"2021-01-02T08:00:00.000000000Z","hash":"bb","height":"2"}],[{"time":"2021-01-01T08:00:00.000000000Z","hash":"aa","height":"1"},{"time":"2021-01-02T08:00:01.000000000Z","hash":"cc","height":"2"}]]},{"asset":"btc","time":"2021-01-04T08:00:00.000000000Z","chains_count":"2","blocks_count_at_tip":"1","chains":[[{"time":"2021-01-01T08:00:00.000000000Z","hash":"aa","height":"1"},{"time":"2021-01-02T08:00:00.000000000Z","hash":"bb","height":"2"}],[{"time":"2021-01-01T08:00:00.000000000Z","hash":"aa","height":"1"},{"time":"2021-01-02T08:00:01.000000000Z","hash":"cc","height":"2"},{"time":"2021-01-04T08:00:00.000000000Z","hash":"dd","height":"3"}]],"reorg":"true","reorg_depth":"1"}]}"""
        val expectedResponseCsv =
            """
            asset,time,chains_count,blocks_count_at_tip,reorg,reorg_depth,block_hash,block_height,block_time
            btc,2021-01-01T08:00:00.000000000Z,1,1,,,aa,1,2021-01-01T08:00:00.000000000Z
            btc,2021-01-02T08:00:00.000000000Z,2,2,,,aa,1,2021-01-02T08:00:00.000000000Z
            btc,2021-01-02T08:00:00.000000000Z,2,2,,,aa,1,2021-01-01T08:00:00.000000000Z
            btc,2021-01-02T08:00:00.000000000Z,2,2,,,bb,2,2021-01-02T08:00:00.000000000Z
            btc,2021-01-02T08:00:00.000000000Z,2,2,,,aa,1,2021-01-01T08:00:00.000000000Z
            btc,2021-01-02T08:00:00.000000000Z,2,2,,,cc,2,2021-01-02T08:00:01.000000000Z
            btc,2021-01-04T08:00:00.000000000Z,2,1,true,1,aa,1,2021-01-01T08:00:00.000000000Z
            btc,2021-01-04T08:00:00.000000000Z,2,1,true,1,bb,2,2021-01-02T08:00:00.000000000Z
            btc,2021-01-04T08:00:00.000000000Z,2,1,true,1,aa,1,2021-01-01T08:00:00.000000000Z
            btc,2021-01-04T08:00:00.000000000Z,2,1,true,1,cc,2,2021-01-02T08:00:01.000000000Z
            btc,2021-01-04T08:00:00.000000000Z,2,1,true,1,dd,3,2021-01-04T08:00:00.000000000Z

            """.trimIndent()

        testCsv(
            pathAndQuery = "/v4/timeseries/asset-chains?assets=btc&api_key=$TEST_API_KEY",
            expectedResponse = expectedResponse,
            expectedResponseCsv = expectedResponseCsv,
        )
    }

    @Test
    fun `start_time and end_time are set`() {
        val expectedResponse =
            """{"data":[{"asset":"btc","time":"2021-01-02T08:00:00.000000000Z","chains_count":"2","blocks_count_at_tip":"2","chains":[[{"time":"2021-01-02T08:00:00.000000000Z","hash":"aa","height":"1"}]]},{"asset":"btc","time":"2021-01-02T08:00:00.000000000Z","chains_count":"2","blocks_count_at_tip":"2","chains":[[{"time":"2021-01-01T08:00:00.000000000Z","hash":"aa","height":"1"},{"time":"2021-01-02T08:00:00.000000000Z","hash":"bb","height":"2"}],[{"time":"2021-01-01T08:00:00.000000000Z","hash":"aa","height":"1"},{"time":"2021-01-02T08:00:01.000000000Z","hash":"cc","height":"2"}]]}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/asset-chains?assets=btc&api_key=$TEST_API_KEY&start_time=2021-01-02&end_time=2021-01-03",
            "X-Request-Timestamp",
            "1622472895000",
        )
    }

    @Test
    fun `start_time is out of range`() {
        val expectedResponse =
            """{"data":[]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/asset-chains?assets=btc&api_key=$TEST_API_KEY&start_time=2021-02-01",
            "X-Request-Timestamp",
            "1622472895000",
        )
    }

    @Test
    fun `start_time is out of range in a csv format`() {
        assertResponseWithContentType(
            expectedCode = 200,
            expectedResponse = EMPTY_CSV_RESPONSE,
            contentType = "text/csv",
            pathAndQuery = "/v4/timeseries/asset-chains?assets=btc&format=csv&api_key=$TEST_API_KEY&start_time=2021-02-01",
            headers = arrayOf("X-Request-Timestamp", "1622472895000"),
        )
    }

    @Test
    fun `bad api_key`() {
        val expectedResponse =
            """{"error":{"type":"wrong_credentials","message":"Supplied credentials are not valid."}}"""
        assertResponse(
            401,
            expectedResponse,
            "/v4/timeseries/asset-chains?assets=btc&api_key=aaa",
        )
    }

    @Test
    fun `unsupported asset`() {
        val expectedResponse =
            """{"error":{"type":"bad_parameter","message":"Bad parameter 'assets'. Value 'xxx' is not supported."}}"""
        assertResponse(
            400,
            expectedResponse,
            "/v4/timeseries/asset-chains?assets=xxx&api_key=$TEST_API_KEY",
        )
    }

    @Test
    fun `paging_from is set to start`() {
        val expectedResponse =
            """{"data":[{"asset":"btc","time":"2021-01-01T08:00:00.000000000Z","chains_count":"1","blocks_count_at_tip":"1","chains":[[{"time":"2021-01-01T08:00:00.000000000Z","hash":"aa","height":"1"}]]},{"asset":"btc","time":"2021-01-02T08:00:00.000000000Z","chains_count":"2","blocks_count_at_tip":"2","chains":[[{"time":"2021-01-02T08:00:00.000000000Z","hash":"aa","height":"1"}]]}],"next_page_token":"0.MjAyMS0wMS0wMlQwODowMDowMFo","next_page_url":"http://127.0.0.1:8080/v4/timeseries/asset-chains?assets=btc&api_key=x1&paging_from=start&page_size=2&next_page_token=0.MjAyMS0wMS0wMlQwODowMDowMFo"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/asset-chains?assets=btc&api_key=$TEST_API_KEY&paging_from=start&page_size=2",
            "X-Request-Timestamp",
            "1622472895000",
        )
    }

    @Test
    fun `paging_from is set to start in a csv format`() {
        val expectedResponse = """{"data":[{"asset":"btc","time":"2021-01-01T08:00:00.000000000Z","chains_count":"1","blocks_count_at_tip":"1","chains":[[{"time":"2021-01-01T08:00:00.000000000Z","hash":"aa","height":"1"}]]},{"asset":"btc","time":"2021-01-02T08:00:00.000000000Z","chains_count":"2","blocks_count_at_tip":"2","chains":[[{"time":"2021-01-02T08:00:00.000000000Z","hash":"aa","height":"1"}]]}],"next_page_token":"0.MjAyMS0wMS0wMlQwODowMDowMFo","next_page_url":"http://127.0.0.1:8080/v4/timeseries/asset-chains?assets=btc&api_key=x1&paging_from=start&page_size=2&next_page_token=0.MjAyMS0wMS0wMlQwODowMDowMFo"}"""
        val expectedResponseCsv =
            """
            asset,time,chains_count,blocks_count_at_tip,reorg,reorg_depth,block_hash,block_height,block_time
            btc,2021-01-01T08:00:00.000000000Z,1,1,,,aa,1,2021-01-01T08:00:00.000000000Z
            btc,2021-01-02T08:00:00.000000000Z,2,2,,,aa,1,2021-01-02T08:00:00.000000000Z

            """.trimIndent()

        testCsv(
            pathAndQuery = "/v4/timeseries/asset-chains?assets=btc&api_key=$TEST_API_KEY&paging_from=start&page_size=2",
            expectedResponse = expectedResponse,
            expectedResponseCsv = expectedResponseCsv,
        )
    }

    @Test
    fun `paging_from is set to end`() {
        val expectedResponse =
            """{"data":[{"asset":"btc","time":"2021-01-02T08:00:00.000000000Z","chains_count":"2","blocks_count_at_tip":"2","chains":[[{"time":"2021-01-01T08:00:00.000000000Z","hash":"aa","height":"1"},{"time":"2021-01-02T08:00:00.000000000Z","hash":"bb","height":"2"}],[{"time":"2021-01-01T08:00:00.000000000Z","hash":"aa","height":"1"},{"time":"2021-01-02T08:00:01.000000000Z","hash":"cc","height":"2"}]]},{"asset":"btc","time":"2021-01-04T08:00:00.000000000Z","chains_count":"2","blocks_count_at_tip":"1","chains":[[{"time":"2021-01-01T08:00:00.000000000Z","hash":"aa","height":"1"},{"time":"2021-01-02T08:00:00.000000000Z","hash":"bb","height":"2"}],[{"time":"2021-01-01T08:00:00.000000000Z","hash":"aa","height":"1"},{"time":"2021-01-02T08:00:01.000000000Z","hash":"cc","height":"2"},{"time":"2021-01-04T08:00:00.000000000Z","hash":"dd","height":"3"}]],"reorg":"true","reorg_depth":"1"}],"next_page_token":"0.MjAyMS0wMS0wMlQwODowMDowMFo","next_page_url":"http://127.0.0.1:8080/v4/timeseries/asset-chains?assets=btc&api_key=x1&paging_from=end&page_size=2&next_page_token=0.MjAyMS0wMS0wMlQwODowMDowMFo"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/asset-chains?assets=btc&api_key=$TEST_API_KEY&paging_from=end&page_size=2",
            "X-Request-Timestamp",
            "1622472895000",
        )
    }

    @Test
    fun `page 1`() {
        val expectedResponse =
            """{"data":[{"asset":"btc","time":"2021-01-02T08:00:00.000000000Z","chains_count":"2","blocks_count_at_tip":"2","chains":[[{"time":"2021-01-01T08:00:00.000000000Z","hash":"aa","height":"1"},{"time":"2021-01-02T08:00:00.000000000Z","hash":"bb","height":"2"}],[{"time":"2021-01-01T08:00:00.000000000Z","hash":"aa","height":"1"},{"time":"2021-01-02T08:00:01.000000000Z","hash":"cc","height":"2"}]]},{"asset":"btc","time":"2021-01-04T08:00:00.000000000Z","chains_count":"2","blocks_count_at_tip":"1","chains":[[{"time":"2021-01-01T08:00:00.000000000Z","hash":"aa","height":"1"},{"time":"2021-01-02T08:00:00.000000000Z","hash":"bb","height":"2"}],[{"time":"2021-01-01T08:00:00.000000000Z","hash":"aa","height":"1"},{"time":"2021-01-02T08:00:01.000000000Z","hash":"cc","height":"2"},{"time":"2021-01-04T08:00:00.000000000Z","hash":"dd","height":"3"}]],"reorg":"true","reorg_depth":"1"}],"next_page_token":"0.MjAyMS0wMS0wMlQwODowMDowMFo","next_page_url":"http://127.0.0.1:8080/v4/timeseries/asset-chains?assets=btc&api_key=x1&page_size=2&next_page_token=0.MjAyMS0wMS0wMlQwODowMDowMFo"}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/asset-chains?assets=btc&api_key=$TEST_API_KEY&page_size=2",
            "X-Request-Timestamp",
            "1622472895000",
        )
    }

    @Test
    fun `page 1 in a csv format`() {
        val expectedResponse = """{"data":[{"asset":"btc","time":"2021-01-02T08:00:00.000000000Z","chains_count":"2","blocks_count_at_tip":"2","chains":[[{"time":"2021-01-01T08:00:00.000000000Z","hash":"aa","height":"1"},{"time":"2021-01-02T08:00:00.000000000Z","hash":"bb","height":"2"}],[{"time":"2021-01-01T08:00:00.000000000Z","hash":"aa","height":"1"},{"time":"2021-01-02T08:00:01.000000000Z","hash":"cc","height":"2"}]]},{"asset":"btc","time":"2021-01-04T08:00:00.000000000Z","chains_count":"2","blocks_count_at_tip":"1","chains":[[{"time":"2021-01-01T08:00:00.000000000Z","hash":"aa","height":"1"},{"time":"2021-01-02T08:00:00.000000000Z","hash":"bb","height":"2"}],[{"time":"2021-01-01T08:00:00.000000000Z","hash":"aa","height":"1"},{"time":"2021-01-02T08:00:01.000000000Z","hash":"cc","height":"2"},{"time":"2021-01-04T08:00:00.000000000Z","hash":"dd","height":"3"}]],"reorg":"true","reorg_depth":"1"}],"next_page_token":"0.MjAyMS0wMS0wMlQwODowMDowMFo","next_page_url":"http://127.0.0.1:8080/v4/timeseries/asset-chains?assets=btc&api_key=x1&page_size=2&next_page_token=0.MjAyMS0wMS0wMlQwODowMDowMFo"}"""
        val expectedResponseCsv =
            """
            asset,time,chains_count,blocks_count_at_tip,reorg,reorg_depth,block_hash,block_height,block_time
            btc,2021-01-02T08:00:00.000000000Z,2,2,,,aa,1,2021-01-01T08:00:00.000000000Z
            btc,2021-01-02T08:00:00.000000000Z,2,2,,,bb,2,2021-01-02T08:00:00.000000000Z
            btc,2021-01-02T08:00:00.000000000Z,2,2,,,aa,1,2021-01-01T08:00:00.000000000Z
            btc,2021-01-02T08:00:00.000000000Z,2,2,,,cc,2,2021-01-02T08:00:01.000000000Z
            btc,2021-01-04T08:00:00.000000000Z,2,1,true,1,aa,1,2021-01-01T08:00:00.000000000Z
            btc,2021-01-04T08:00:00.000000000Z,2,1,true,1,bb,2,2021-01-02T08:00:00.000000000Z
            btc,2021-01-04T08:00:00.000000000Z,2,1,true,1,aa,1,2021-01-01T08:00:00.000000000Z
            btc,2021-01-04T08:00:00.000000000Z,2,1,true,1,cc,2,2021-01-02T08:00:01.000000000Z
            btc,2021-01-04T08:00:00.000000000Z,2,1,true,1,dd,3,2021-01-04T08:00:00.000000000Z

            """.trimIndent()

        testCsv(
            pathAndQuery = "/v4/timeseries/asset-chains?assets=btc&api_key=$TEST_API_KEY&page_size=2",
            expectedResponse = expectedResponse,
            expectedResponseCsv = expectedResponseCsv,
        )
    }

    @Test
    fun `page 2`() {
        val expectedResponse =
            """{"data":[{"asset":"btc","time":"2021-01-01T08:00:00.000000000Z","chains_count":"1","blocks_count_at_tip":"1","chains":[[{"time":"2021-01-01T08:00:00.000000000Z","hash":"aa","height":"1"}]]}]}"""
        assertResponse(
            200,
            expectedResponse,
            "/v4/timeseries/asset-chains?assets=btc&api_key=$TEST_API_KEY&page_size=2&next_page_token=0.MjAyMS0wMS0wMlQwODowMDowMFo",
            "X-Request-Timestamp",
            "1622472895000",
        )
    }

    private fun testCsv(
        pathAndQuery: String,
        expectedResponse: String,
        expectedResponseCsv: String,
    ) {
        assertResponse(
            expectedCode = 200,
            expectedResponse = expectedResponse,
            pathAndQuery = pathAndQuery,
            headers = arrayOf("X-Request-Timestamp", "1622472895000"),
        )

        assertResponseWithContentType(
            expectedCode = 200,
            expectedResponse = expectedResponseCsv,
            contentType = "text/csv",
            pathAndQuery = "$pathAndQuery&format=csv",
            headers = arrayOf("X-Request-Timestamp", "1622472895000"),
        )

        assertEquals(jsonResponseToCsv(expectedResponse), expectedResponseCsv)
    }

    private fun jsonResponseToCsv(jsonResponse: String): String {
        val response = commonModule.objectMapper.readValue<AssetChainsResponse>(jsonResponse)
        val sb = StringBuilder("asset,time,chains_count,blocks_count_at_tip,reorg,reorg_depth,block_hash,block_height,block_time\n")
        response.data.forEach { assetChain ->
            assetChain.chains.forEach { assetChainBlocks ->
                assetChainBlocks.forEach { assetChainBlock ->
                    sb
                        .append(assetChain.asset)
                        .append(",")
                        .append(assetChain.time)
                        .append(",")
                        .append(assetChain.chainsCount)
                        .append(",")
                        .append(assetChain.blocksCountAtTip)
                        .append(",")
                        .append(assetChain.reorg ?: "")
                        .append(",")
                        .append(assetChain.reorgDepth ?: "")
                        .append(",")
                        .append(assetChainBlock.hash)
                        .append(",")
                        .append(assetChainBlock.height)
                        .append(",")
                        .append(assetChainBlock.time)
                        .append("\n")
                }
            }
        }
        return sb.toString()
    }
}
