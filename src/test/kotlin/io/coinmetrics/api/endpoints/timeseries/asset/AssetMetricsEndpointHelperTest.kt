package io.coinmetrics.api.endpoints.timeseries.asset

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.endpoints.timeseries.asset.AssetMetricsEndpointHelper.DataSourceGroup
import io.coinmetrics.api.endpoints.timeseries.asset.AssetMetricsEndpointHelper.Frequency
import org.junit.jupiter.api.Assertions.assertAll
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertInstanceOf
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.Arguments.of
import org.junit.jupiter.params.provider.MethodSource

class AssetMetricsEndpointHelperTest {
    companion object {
        private const val TEST_ASSET = "btc"
        private val badParameter = ApiError.BadParameter("test_param")

        @JvmStatic
        fun invalidMetricFrequencyCombinations(): List<Arguments> =
            listOf(
                of("ReferenceRate", Frequency.TEN_MINUTES),
                of("ReferenceRateUSD", Frequency.TEN_MINUTES),
                of("ReferenceRateEUR", Frequency.TEN_MINUTES),
                of("ReferenceRateBTC", Frequency.TEN_MINUTES),
                of("ReferenceRateETH", Frequency.TEN_MINUTES),
                of("principal_market_usd", Frequency.TEN_MINUTES),
                of("principal_market_eur", Frequency.TEN_MINUTES),
                of("principal_market_btc", Frequency.TEN_MINUTES),
                of("principal_market_eth", Frequency.TEN_MINUTES),
                of("volume_trusted_spot_usd_1d", Frequency.TEN_MINUTES),
                of("volatility_realized_usd_rolling_24h", Frequency.ONE_BLOCK),
                of("mempool_count", Frequency.TEN_MINUTES),
                of("mining_reward_mean", Frequency.TEN_MINUTES),
                of("time_inter_block_hi", Frequency.TEN_MINUTES),
                of("confirmation_suggestion_min", Frequency.TEN_MINUTES),
                of("block_fees", Frequency.TEN_MINUTES),
                of("sc_mint_assets", Frequency.TEN_MINUTES),
                of("MinerEntity", Frequency.TEN_MINUTES),
                of("FlowInBFXNtv", Frequency.TEN_MINUTES),
                of("IssFullParticipation", Frequency.TEN_MINUTES),
            )

        @JvmStatic
        fun validMetricFrequencyCombinations(): List<Arguments> =
            listOf(
                of("ReferenceRate", Frequency.ONE_SECOND, DataSourceGroup.PER_SECOND_REFERENCE_RATES),
                of("ReferenceRate", Frequency.ONE_MINUTE, DataSourceGroup.PER_SECOND_REFERENCE_RATES),
                of("ReferenceRate", Frequency.ONE_HOUR, DataSourceGroup.HOURLY_REFERENCE_RATES),
                of("ReferenceRate", Frequency.ONE_DAY, DataSourceGroup.HOURLY_REFERENCE_RATES),
                of("ReferenceRateUSD", Frequency.ONE_SECOND, DataSourceGroup.PER_SECOND_REFERENCE_RATES_USD),
                of("ReferenceRateUSD", Frequency.ONE_MINUTE, DataSourceGroup.PER_SECOND_REFERENCE_RATES_USD),
                of("ReferenceRateUSD", Frequency.ONE_HOUR, DataSourceGroup.HOURLY_REFERENCE_RATES_USD),
                of("ReferenceRateUSD", Frequency.ONE_DAY, DataSourceGroup.HOURLY_REFERENCE_RATES_USD),
                of("ReferenceRateEUR", Frequency.ONE_SECOND, DataSourceGroup.PER_SECOND_REFERENCE_RATES_EUR),
                of("ReferenceRateEUR", Frequency.ONE_MINUTE, DataSourceGroup.PER_SECOND_REFERENCE_RATES_EUR),
                of("ReferenceRateEUR", Frequency.ONE_HOUR, DataSourceGroup.HOURLY_REFERENCE_RATES_EUR),
                of("ReferenceRateEUR", Frequency.ONE_DAY, DataSourceGroup.HOURLY_REFERENCE_RATES_EUR),
                of("ReferenceRateBTC", Frequency.ONE_SECOND, DataSourceGroup.PER_SECOND_REFERENCE_RATES_BTC),
                of("ReferenceRateBTC", Frequency.ONE_MINUTE, DataSourceGroup.PER_SECOND_REFERENCE_RATES_BTC),
                of("ReferenceRateBTC", Frequency.ONE_HOUR, DataSourceGroup.HOURLY_REFERENCE_RATES_BTC),
                of("ReferenceRateBTC", Frequency.ONE_DAY, DataSourceGroup.HOURLY_REFERENCE_RATES_BTC),
                of("ReferenceRateETH", Frequency.ONE_SECOND, DataSourceGroup.PER_SECOND_REFERENCE_RATES_ETH),
                of("ReferenceRateETH", Frequency.ONE_MINUTE, DataSourceGroup.PER_SECOND_REFERENCE_RATES_ETH),
                of("ReferenceRateETH", Frequency.ONE_HOUR, DataSourceGroup.HOURLY_REFERENCE_RATES_ETH),
                of("ReferenceRateETH", Frequency.ONE_DAY, DataSourceGroup.HOURLY_REFERENCE_RATES_ETH),
                of("principal_market_usd", Frequency.ONE_SECOND, DataSourceGroup.PRINCIPAL_MARKET_PRICE_USD),
                of("principal_market_usd", Frequency.ONE_MINUTE, DataSourceGroup.PRINCIPAL_MARKET_PRICE_USD),
                of("principal_market_usd", Frequency.ONE_HOUR, DataSourceGroup.PRINCIPAL_MARKET_PRICE_USD),
                of("principal_market_usd", Frequency.ONE_DAY, DataSourceGroup.PRINCIPAL_MARKET_PRICE_USD),
                of("principal_market_eur", Frequency.ONE_SECOND, DataSourceGroup.PRINCIPAL_MARKET_PRICE_EUR),
                of("principal_market_eur", Frequency.ONE_MINUTE, DataSourceGroup.PRINCIPAL_MARKET_PRICE_EUR),
                of("principal_market_eur", Frequency.ONE_HOUR, DataSourceGroup.PRINCIPAL_MARKET_PRICE_EUR),
                of("principal_market_eur", Frequency.ONE_DAY, DataSourceGroup.PRINCIPAL_MARKET_PRICE_EUR),
                of("principal_market_btc", Frequency.ONE_SECOND, DataSourceGroup.PRINCIPAL_MARKET_PRICE_BTC),
                of("principal_market_btc", Frequency.ONE_MINUTE, DataSourceGroup.PRINCIPAL_MARKET_PRICE_BTC),
                of("principal_market_btc", Frequency.ONE_HOUR, DataSourceGroup.PRINCIPAL_MARKET_PRICE_BTC),
                of("principal_market_btc", Frequency.ONE_DAY, DataSourceGroup.PRINCIPAL_MARKET_PRICE_BTC),
                of("principal_market_eth", Frequency.ONE_SECOND, DataSourceGroup.PRINCIPAL_MARKET_PRICE_ETH),
                of("principal_market_eth", Frequency.ONE_MINUTE, DataSourceGroup.PRINCIPAL_MARKET_PRICE_ETH),
                of("principal_market_eth", Frequency.ONE_HOUR, DataSourceGroup.PRINCIPAL_MARKET_PRICE_ETH),
                of("principal_market_eth", Frequency.ONE_DAY, DataSourceGroup.PRINCIPAL_MARKET_PRICE_ETH),
                of("volume_trusted_spot_usd_1d", Frequency.ONE_HOUR, DataSourceGroup.ASSET_MARKET_METRICS),
                of("volume_trusted_spot_usd_1d", Frequency.ONE_DAY, DataSourceGroup.ASSET_MARKET_METRICS),
                of("volume_reported_spot_usd_1d", Frequency.ONE_HOUR, DataSourceGroup.ASSET_MARKET_METRICS),
                of("volume_reported_spot_usd_1d", Frequency.ONE_DAY, DataSourceGroup.ASSET_MARKET_METRICS),
                of("open_interest_reported_future_usd", Frequency.ONE_HOUR, DataSourceGroup.ASSET_MARKET_METRICS),
                of("open_interest_reported_future_usd", Frequency.ONE_DAY, DataSourceGroup.ASSET_MARKET_METRICS),
                of("futures_aggregate_funding_rate_all_margin_1d_period", Frequency.ONE_HOUR, DataSourceGroup.ASSET_MARKET_METRICS),
                of("futures_aggregate_funding_rate_all_margin_1d_period", Frequency.ONE_DAY, DataSourceGroup.ASSET_MARKET_METRICS),
                of("futures_cumulative_funding_rate_all_margin_1d", Frequency.ONE_HOUR, DataSourceGroup.ASSET_MARKET_METRICS),
                of("futures_cumulative_funding_rate_all_margin_1d", Frequency.ONE_DAY, DataSourceGroup.ASSET_MARKET_METRICS),
                of("volatility_realized_usd_rolling_24h", Frequency.ONE_DAY, DataSourceGroup.REALIZED_VOLATILITY_MARKET_METRICS),
                of("FlowInBFXNtv", Frequency.ONE_DAY, DataSourceGroup.DAILY_FLOWS_METRICS),
                of("FlowInBFXNtv", Frequency.ONE_BLOCK, DataSourceGroup.BBB_FLOW_METRICS),
            )
    }

    @Test
    fun `should return grouped data sources when all metrics mapped to data source and skipOnError is false`() {
        val metrics = listOf("ReferenceRateBTC", "ReferenceRateETH")
        val skipOnError = false
        val frequency = Frequency.ONE_SECOND
        val expectedValue =
            mapOf(
                DataSourceGroup.PER_SECOND_REFERENCE_RATES_BTC to listOf("ReferenceRateBTC"),
                DataSourceGroup.PER_SECOND_REFERENCE_RATES_ETH to listOf("ReferenceRateETH"),
            ).entries.toList()

        val result = AssetMetricsEndpointHelper.groupByDataSource(TEST_ASSET, metrics, frequency, skipOnError)
        assertAll(
            { assertInstanceOf(FunctionResult.Success::class.java, result) },
            { assertEquals(expectedValue, (result as FunctionResult.Success).value) },
        )
    }

    @Test
    fun `should return grouped data sources when all metrics mapped to data source and skipOnError is true`() {
        val metrics = listOf("ReferenceRate", "ReferenceRateEUR")
        val skipOnError = true
        val frequency = Frequency.ONE_SECOND
        val expectedValue =
            mapOf(
                DataSourceGroup.PER_SECOND_REFERENCE_RATES to listOf("ReferenceRate"),
                DataSourceGroup.PER_SECOND_REFERENCE_RATES_EUR to listOf("ReferenceRateEUR"),
            ).entries.toList()

        val result = AssetMetricsEndpointHelper.groupByDataSource(TEST_ASSET, metrics, frequency, skipOnError)
        assertAll(
            { assertInstanceOf(FunctionResult.Success::class.java, result) },
            { assertEquals(expectedValue, (result as FunctionResult.Success).value) },
        )
    }

    @Test
    fun `should return error when one of metrics is not mapped to data source and skipOnError is false`() {
        val metrics = listOf("ReferenceRateXYZ", "ReferenceRateUSD")
        val frequency = Frequency.ONE_SECOND
        val expectedErrorParamName = "metrics"
        val expectedErrorMessage = "Bad parameter '$expectedErrorParamName'. Metric 'ReferenceRateXYZ' is not supported."

        val result = AssetMetricsEndpointHelper.groupByDataSource(TEST_ASSET, metrics, frequency)
        assertAll(
            { assertInstanceOf(FunctionResult.Failure::class.java, result) },
            { assertInstanceOf(ApiError.BadParameter::class.java, ((result as FunctionResult.Failure).value as ApiError.BadParameter)) },
            { assertEquals(expectedErrorParamName, ((result as FunctionResult.Failure).value as ApiError.BadParameter).name) },
            { assertEquals(expectedErrorMessage, ((result as FunctionResult.Failure).value as ApiError.BadParameter).message) },
        )
    }

    @ParameterizedTest(name = "should return {2} when metric {0} with frequency {1}")
    @MethodSource("validMetricFrequencyCombinations")
    fun `test valid combinations`(
        metric: String,
        frequency: Frequency,
        expectedDataSourceGroup: DataSourceGroup,
    ) {
        val metrics = listOf(metric)
        val expectedValue = mapOf(expectedDataSourceGroup to metrics).entries.toList()

        val result = AssetMetricsEndpointHelper.groupByDataSource(TEST_ASSET, metrics, frequency)
        assertAll(
            { assertInstanceOf(FunctionResult.Success::class.java, result) },
            { assertEquals(expectedValue, (result as FunctionResult.Success).value) },
        )
    }

    @ParameterizedTest(name = "should fail when metric {0} with frequency {1}")
    @MethodSource("invalidMetricFrequencyCombinations")
    fun `test invalid combinations`(
        metric: String,
        frequency: Frequency,
    ) {
        val expectedErrorParamName = "frequency"
        val expectedErrorMessage = "Bad parameter '$expectedErrorParamName'. Unsupported frequency '${frequency.value}' for metric '$metric'."

        val result = AssetMetricsEndpointHelper.groupByDataSource(TEST_ASSET, listOf(metric), frequency)
        assertAll(
            { assertInstanceOf(FunctionResult.Failure::class.java, result) },
            { assertInstanceOf(ApiError.BadParameter::class.java, ((result as FunctionResult.Failure).value as ApiError.BadParameter)) },
            { assertEquals(expectedErrorParamName, ((result as FunctionResult.Failure).value as ApiError.BadParameter).name) },
            { assertEquals(expectedErrorMessage, ((result as FunctionResult.Failure).value as ApiError.BadParameter).message) },
        )
    }

    @Test
    fun `should return error when metric-frequency combination is not mapped to data source and skipOnError is false`() {
        val metrics = listOf("ReferenceRateUSD", "MinerEntity")
        val frequency = Frequency.ONE_BLOCK
        val expectedErrorParamName = "frequency"
        val expectedErrorMessage = "Bad parameter '$expectedErrorParamName'. Unsupported frequency '1b' for metric 'ReferenceRateUSD'."

        val result = AssetMetricsEndpointHelper.groupByDataSource(TEST_ASSET, metrics, frequency)
        assertAll(
            { assertInstanceOf(FunctionResult.Failure::class.java, result) },
            { assertInstanceOf(ApiError.BadParameter::class.java, ((result as FunctionResult.Failure).value as ApiError.BadParameter)) },
            { assertEquals(expectedErrorParamName, ((result as FunctionResult.Failure).value as ApiError.BadParameter).name) },
            { assertEquals(expectedErrorMessage, ((result as FunctionResult.Failure).value as ApiError.BadParameter).message) },
        )
    }

    @Test
    fun `should return grouped data sources when one of metrics is not mapped to data source and skipOnError is true`() {
        val metrics = listOf("ReferenceRateXYZ", "ReferenceRateUSD")
        val skipOnError = true
        val frequency = Frequency.ONE_SECOND
        val expectedValue =
            mapOf(
                DataSourceGroup.PER_SECOND_REFERENCE_RATES_USD to listOf("ReferenceRateUSD"),
            ).entries.toList()

        val result = AssetMetricsEndpointHelper.groupByDataSource(TEST_ASSET, metrics, frequency, skipOnError)
        assertAll(
            { assertInstanceOf(FunctionResult.Success::class.java, result) },
            { assertEquals(expectedValue, (result as FunctionResult.Success).value) },
        )
    }
}
