package io.coinmetrics.api.endpoints.timeseries.market

import io.coinmetrics.api.helper.BaseTest
import io.coinmetrics.api.helper.COMMUNITY_KEY
import io.coinmetrics.api.helper.TEST_API_KEY
import io.coinmetrics.api.models.MarketCandlesResponse
import io.coinmetrics.testing.autoexpect.AutoExpect
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertAll
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(AutoExpect::class)
class CandlesTest : BaseTest() {
    @Test
    fun `test candles error responses`() {
        listOf(
            "bittrex-invalid-asset-spot",
            "nonexistingexhange-btc-usd-spot",
        ).forEach { marketId ->
            assertResponse(
                400,
                """{"error":{"type":"bad_parameter","message":"Bad parameter 'markets'. Market '$marketId' is not supported."}}""",
                "/v4/timeseries/market-candles?markets=$marketId&api_key=$TEST_API_KEY",
            )
        }

        listOf(
            "bittrex-btc-usd-test-spot",
            "bitmex-xbtusd-spot",
            "bittrex-btc-usd",
        ).forEach { marketId ->
            assertResponse(
                400,
                """{"error":{"type":"bad_parameter","message":"Bad parameter 'markets'. Incorrect market '$marketId'."}}""",
                "/v4/timeseries/market-candles?markets=$marketId&api_key=$TEST_API_KEY",
            )
        }

        listOf(
            "201912-23",
            "20191223T00:00:05.000Z",
            "2019-12-23T00:00:05000Z",
        ).forEach { end_time ->
            assertResponse(
                400,
                """{"error":{"type":"bad_parameter","message":"Bad parameter 'end_time'. Incorrect time format '$end_time'. Supported formats are 'yyyy-MM-dd', 'yyyyMMdd', 'yyyy-MM-ddTHH:mm:ss', 'yyyy-MM-ddTHHmmss', 'yyyy-MM-ddTHH:mm:ss.SSS', 'yyyy-MM-ddTHHmmss.SSS', 'yyyy-MM-ddTHH:mm:ss.SSSSSS', 'yyyy-MM-ddTHHmmss.SSSSSS', 'yyyy-MM-ddTHH:mm:ss.SSSSSSSSS', 'yyyy-MM-ddTHHmmss.SSSSSSSSS'."}}""".trimMargin(),
                "/v4/timeseries/market-candles?markets=bittrex-btc-usd-spot&api_key=$TEST_API_KEY&end_time=$end_time",
            )
        }
    }

    @Test
    fun `test candles with no parameters`() {
        getResponse("/v4/timeseries/market-candles?markets=bittrex-btc-usd-spot&api_key=$TEST_API_KEY&paging_from=start").assertResponse()
    }

    @Test
    fun `test candles with no parameters in a csv format`() {
        val (_, actualResponse) = getResponse("/v4/timeseries/market-candles?markets=bittrex-btc-usd-spot&api_key=$TEST_API_KEY&paging_from=start").assertResponse("json")

        val (_, actualResponseCsv) = getResponse("/v4/timeseries/market-candles?markets=bittrex-btc-usd-spot&format=csv&api_key=$TEST_API_KEY&paging_from=start").assertResponse("csv")

        assertEquals(jsonResponseToCsvResponse(actualResponse), actualResponseCsv)
    }

    @Test
    fun `test candles with start_time and page_size`() {
        getResponse("/v4/timeseries/market-candles?markets=bittrex-btc-usd-spot&api_key=$TEST_API_KEY&start_time=20191220&page_size=2&paging_from=start").assertResponse()
    }

    @Test
    fun `test candles with start_time and page_size in a csv format`() {
        val (_, actualResponse) = getResponse("/v4/timeseries/market-candles?markets=bittrex-btc-usd-spot&api_key=$TEST_API_KEY&start_time=20191220&page_size=2&paging_from=start").assertResponse("json")

        val (_, actualResponseCsv) = getResponse("/v4/timeseries/market-candles?markets=bittrex-btc-usd-spot&format=csv&api_key=$TEST_API_KEY&start_time=20191220&page_size=2&paging_from=start").assertResponse("csv")

        assertEquals(jsonResponseToCsvResponse(actualResponse), actualResponseCsv)
    }

    @Test
    fun `test candles with reference time and paging_from`() {
        getResponse("/v4/timeseries/market-candles?markets=bittrex-btc-usd-spot&api_key=$TEST_API_KEY&end_time=2019-12-20T00:00:05.000Z").assertResponse()
    }

    @Test
    fun `test candles with end_time, paging_from and page_size`() {
        getResponse("/v4/timeseries/market-candles?markets=bittrex-btc-usd-spot&api_key=$TEST_API_KEY&end_time=2019-12-23T00:00:05.000Z&page_size=3").assertResponse()
    }

    @Test
    fun `test candles with incorrect key`() {
        getResponse("/v4/timeseries/market-candles?markets=bittrex-btc-usd-spot&api_key=invalid_key&end_time=20191223&page_size=2").assertResponse()
    }

    @Test
    fun `test candles with key without permissions`() {
        getResponse("/v4/timeseries/market-candles?markets=bittrex-btc-usd-spot&api_key=x2&start_time=20191223&page_size=2").assertResponse()
    }

    @Test
    fun `test candles with end_time, paging_from, page_size and granularity`() {
        getResponse("/v4/timeseries/market-candles?markets=bittrex-btc-usd-spot&api_key=$TEST_API_KEY&end_time=2020-01-23T00:00:05.000Z&page_size=3&frequency=5m").assertResponse()
    }

    @Test
    fun `test candles with cutting end_time, paging_from, page_size and granularity`() {
        getResponse("/v4/timeseries/market-candles?markets=bittrex-btc-usd-spot&api_key=$TEST_API_KEY&end_time=2020-01-19T21:03:00.000Z&page_size=3&frequency=5m").assertResponse()
    }

    @Test
    fun `test candles empty response`() {
        getResponse("/v4/timeseries/market-candles?markets=bittrex-btc-usd-spot&api_key=$TEST_API_KEY&end_time=2020-01-19T20:17:00.000Z&page_size=3&frequency=5m").assertResponse()
    }

    @Test
    fun `test candles empty response in a csv format`() {
        getResponse("/v4/timeseries/market-candles?markets=bittrex-btc-usd-spot&format=csv&api_key=$TEST_API_KEY&end_time=2020-01-19T20:17:00.000Z&page_size=3&frequency=5m").assertResponse()
    }

    @Test
    fun `should return empty response when requesting unsupported pattern market`() {
        getResponse("/v4/timeseries/market-candles?markets=zaif-*-option&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should return empty response when requesting unsupported pattern markets`() {
        getResponse("/v4/timeseries/market-candles?markets=zaif-*-option,simex-CGSEUR-*&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `test candles with granularity 10m`() {
        getResponse("/v4/timeseries/market-candles?markets=bittrex-btc-usd-spot&api_key=$TEST_API_KEY&frequency=10m").assertResponse()
    }

    @Test
    fun `test candles with granularity 15m`() {
        getResponse("/v4/timeseries/market-candles?markets=bittrex-btc-usd-spot&api_key=$TEST_API_KEY&frequency=15m").assertResponse()
    }

    @Test
    fun `test candles with granularity 30m`() {
        getResponse("/v4/timeseries/market-candles?markets=bittrex-btc-usd-spot&api_key=$TEST_API_KEY&frequency=30m").assertResponse()
    }

    @Test
    fun `test candles with granularity 1h`() {
        getResponse("/v4/timeseries/market-candles?markets=bittrex-btc-usd-spot&api_key=$TEST_API_KEY&frequency=1h").assertResponse()
    }

    @Test
    fun `test candles with invalid granularity`() {
        getResponse("/v4/timeseries/market-candles?markets=bittrex-btc-usd-spot&api_key=$TEST_API_KEY&frequency=102m").assertResponse("1")
        getResponse("/v4/timeseries/market-candles?markets=bittrex-btc-usd-spot&api_key=$TEST_API_KEY&frequency=10mi").assertResponse("2")
    }

    @Test
    fun `test candles with invalid page_size`() {
        getResponse("/v4/timeseries/market-candles?markets=bittrex-btc-usd-spot&api_key=$TEST_API_KEY&page_size=-1").assertResponse("1")
        getResponse("/v4/timeseries/market-candles?markets=bittrex-btc-usd-spot&api_key=$TEST_API_KEY&page_size=10000000").assertResponse("2")
    }

    @Test
    fun `test futures candles with granularity 10m`() {
        getResponse("/v4/timeseries/market-candles?markets=bitmex-XBTUSD-future&api_key=$TEST_API_KEY&frequency=10m").assertResponse()
    }

    @Test
    fun `test futures candles with granularity 15m`() {
        getResponse("/v4/timeseries/market-candles?markets=bitmex-XBTUSD-future&api_key=$TEST_API_KEY&frequency=15m").assertResponse()
    }

    @Test
    fun `test futures candles with granularity 30m`() {
        getResponse("/v4/timeseries/market-candles?markets=bitmex-XBTUSD-future&api_key=$TEST_API_KEY&frequency=30m").assertResponse()
    }

    @Test
    fun `test futures candles with granularity 1h`() {
        getResponse("/v4/timeseries/market-candles?markets=bitmex-XBTUSD-future&api_key=$TEST_API_KEY&frequency=1h").assertResponse()
    }

    @Test
    fun `test candles requested by market pattern returned only for supported markets`() {
        // not all markets that match *-spot have candles support, but we expect the response to contain only supported ones
        getResponse("/v4/timeseries/market-candles?markets=*-spot&api_key=$TEST_API_KEY&frequency=1h").assertResponse()
    }

    @Test
    fun `multiple patterns`() {
        getResponse("/v4/timeseries/market-candles?markets=binance-*-spot,binance-btc-usdt-spot,uniswap_v3_eth-agg-usdc-weth-spot,bybit*spot&api_key=$TEST_API_KEY&frequency=1h").assertResponse()
    }

    @Test
    fun `test candles requested by market pattern returned only for supported markets when not all markets accessible for the key`() {
        val keyWithoutDeFiMarketSupport = "x3"
        getResponse("/v4/timeseries/market-candles?markets=*-spot&api_key=$keyWithoutDeFiMarketSupport&frequency=1h").assertResponse()
    }

    @Test
    fun `test candles for defi`() {
        getResponse("/v4/timeseries/market-candles?markets=uniswap_v2_eth-aave-usdc-spot&api_key=$TEST_API_KEY&frequency=1d").assertResponse()
    }

    @Test
    fun `test candles returns error when explicitly requested unsupported by key market`() {
        val keyWithoutDeFiMarketSupport = "x3"
        getResponse("/v4/timeseries/market-candles?markets=uniswap_v2_eth-usdc-weth-spot&api_key=$keyWithoutDeFiMarketSupport&frequency=1h").assertResponse()
    }

    @Test
    fun `should return aggregated candles for DeFi market with supported pools`() {
        getResponse("/v4/timeseries/market-candles?markets=uniswap_v3_eth-agg-usdc-weth-spot&api_key=$TEST_API_KEY&frequency=1d").assertResponse()
    }

    @Test
    fun `should return 400 when invalid pool_id specified in DeFi market that supports pools`() {
        getResponse("/v4/timeseries/market-candles?markets=uniswap_v3_eth-inv-usdc-weth-spot&api_key=$TEST_API_KEY&frequency=1d").assertResponse()
    }

    @Test
    fun `should return 400 when invalid pool_id specified in DeFi market that doesn't support pools`() {
        getResponse("/v4/timeseries/market-candles?markets=uniswap_v2_eth-inv-usdc-weth-spot&api_key=$TEST_API_KEY&frequency=1d").assertResponse()
    }

    @Test
    fun `test futures candles with invalid granularity`() {
        getResponse("/v4/timeseries/market-candles?markets=bitmex-XBTUSD-future&api_key=$TEST_API_KEY&frequency=102m").assertResponse("1")
        getResponse("/v4/timeseries/market-candles?markets=bitmex-XBTUSD-future&api_key=$TEST_API_KEY&frequency=10mi").assertResponse("2")
    }

    @Test
    fun `test futures candles with invalid page_size`() {
        getResponse("/v4/timeseries/market-candles?markets=bitmex-XBTUSD-future&api_key=$TEST_API_KEY&page_size=-1").assertResponse("1")
        getResponse("/v4/timeseries/market-candles?markets=bitmex-XBTUSD-future&api_key=$TEST_API_KEY&page_size=10000000").assertResponse("2")
    }

    @Test
    fun `should return 403 when custom frequency offset is requested by community key`() {
        getResponse("/v4/timeseries/market-candles?markets=binance-btc-usdt-spot&frequency=1d-16:00&timezone=America/New_York&api_key=$COMMUNITY_KEY").assertResponse()
    }

    @Test
    fun `should return 400 when custom frequency is invalid`() {
        getResponse("/v4/timeseries/market-candles?markets=binance-btc-usdt-spot&frequency=1h-16:00&timezone=America/New_York&api_key=$TEST_API_KEY&paging_from=start").assertResponse()
    }

    @Test
    fun `should return 400 when custom frequency offset is invalid`() {
        getResponse("/v4/timeseries/market-candles?markets=binance-btc-usdt-spot&frequency=1d-16:30&timezone=America/New_York&api_key=$TEST_API_KEY&paging_from=start").assertResponse()
    }

    @Test
    fun `should return 400 when custom frequency offset is missing`() {
        getResponse("/v4/timeseries/market-candles?markets=binance-btc-usdt-spot&frequency=1d-&timezone=America/New_York&api_key=$TEST_API_KEY&paging_from=start").assertResponse()
    }

    @Test
    fun `should return 400 when custom frequency offset is combined with market pattern`() {
        getResponse("/v4/timeseries/market-candles?markets=*-spot&frequency=1d-16:00&timezone=America/New_York&api_key=$TEST_API_KEY&paging_from=start").assertResponse()
    }

    @Test
    fun `should return candles with custom frequency offset at NY TZ and markets specified`() {
        getResponse("/v4/timeseries/market-candles?markets=binance-btc-usdt-spot&frequency=1d-16:00&timezone=America/New_York&api_key=$TEST_API_KEY&paging_from=start").assertResponse()
    }

    @Test
    fun `should return candles when custom frequency offset is midnight at NY TZ and markets specified`() {
        getResponse("/v4/timeseries/market-candles?markets=binance-btc-usdt-spot&frequency=1d-00:00&timezone=America/New_York&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should return candles when custom frequency offset is 23 hours at NY TZ and markets specified`() {
        getResponse("/v4/timeseries/market-candles?markets=binance-btc-usdt-spot&frequency=1d-23:00&timezone=America/New_York&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should return 400 when custom frequency offset is 24 hours at NY TZ and markets specified`() {
        getResponse("/v4/timeseries/market-candles?markets=binance-btc-usdt-spot&frequency=1d-24:00&timezone=America/New_York&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should return candles with custom frequency offset at Chicago TZ and markets specified`() {
        getResponse("/v4/timeseries/market-candles?markets=binance-btc-usdt-spot&frequency=1d-16:00&timezone=America/Chicago&api_key=$TEST_API_KEY&paging_from=start").assertResponse()
    }

    @Test
    fun `should compare market candles of frequency 1d with custom 00 hours offset at NY TZ`() {
        val urlTemplate: (
            String,
        ) -> String = { frequency ->
            "/v4/timeseries/market-candles?markets=binance-btc-usdt-spot&frequency=$frequency&timezone=America/New_York&api_key=$TEST_API_KEY"
        }
        val expectedResponse =
            """
            {"data":[{"market":"binance-btc-usdt-spot","time":"2023-09-15T04:00:00.000000000Z","price_open":"26615.4","price_close":"26616.43","price_high":"26888","price_low":"26224","vwap":"26642.65735063656337374","volume":"9269.53077","candle_usd_volume":"705911546.28231","candle_trades_count":"675193"},{"market":"binance-btc-usdt-spot","time":"2023-09-16T04:00:00.000000000Z","price_open":"26616.44","price_close":"26529.25","price_high":"26622.22","price_low":"26416.16","vwap":"26527.5930180515154515","volume":"13326.77931","candle_usd_volume":"353629552.4122327","candle_trades_count":"507032"}]}
            """.trimIndent()
        val response1d = getResponse(urlTemplate("1d"))
        val response1d00 = getResponse(urlTemplate("1d-00:00"))
        assertAll(
            { assertEquals(200, response1d.status) },
            { assertEquals(expectedResponse, response1d.body) },
            { assertEquals(200, response1d00.status) },
            { assertEquals(expectedResponse, response1d00.body) },
        )
    }

    @Test
    fun `should return market candles when 1h frequency and timezone is specified`() {
        getResponse("/v4/timeseries/market-candles?markets=*-spot&api_key=$TEST_API_KEY&frequency=1h&timezone=America/New_York").assertResponse()
    }

    @Test
    fun `should return all option market candles when 1h frequency specified`() {
        getResponse("/v4/timeseries/market-candles?markets=*-option&api_key=$TEST_API_KEY&frequency=1h").assertResponse()
    }

    private fun jsonResponseToCsvResponse(jsonResponse: String): String = jsonResponseToCsvResponse<MarketCandlesResponse>(jsonResponse) { it.data }
}
