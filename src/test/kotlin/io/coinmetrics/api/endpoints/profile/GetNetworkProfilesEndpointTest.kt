package io.coinmetrics.api.endpoints.profile

import io.coinmetrics.api.helper.BaseTest
import io.coinmetrics.api.helper.TEST_API_KEY
import io.coinmetrics.api.helper.TEST_API_KEY_2
import io.coinmetrics.testing.autoexpect.AutoExpect
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.api.extension.ExtendWith
import java.nio.file.Path

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@ExtendWith(AutoExpect::class)
class GetNetworkProfilesEndpointTest : BaseTest() {
    @BeforeAll
    fun refreshNetworkProfilesDatas() =
        runBlocking {
            val testDataPath =
                GetNetworkProfilesEndpointTest::class.java
                    .getResource("/network-profiles")!!
                    .file
                    .let { Path.of(it) }
            mainApiModule.networkProfilesLocalStorage.refresh(testDataPath)
        }

    @Test
    fun `should return all network profilqes data`() {
        getResponse("/v4/profile/networks?api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should filter network profiles by network`() {
        getResponse("/v4/profile/networks?networks=btc,algorand&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should filter network profiles by network case-insensetive`() {
        getResponse("/v4/profile/networks?networks=eTh&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should filter network profiles by full_name`() {
        getResponse("/v4/profile/networks?full_names=Ethereum,Avalanche+C-Chain,Bitcoin&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should filter network profiles by full_name case-insensitive`() {
        getResponse("/v4/profile/networks?full_names=ETHEREUM,AVALANCHE+C-CHAIN,BITCOIN&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should paginate network profiles`() {
        assertSuccessResponseWithVariations("/v4/profile/networks?api_key=$TEST_API_KEY")
    }

    @Test
    fun `should return first network profile from start`() {
        getResponse("/v4/profile/networks?page_size=1&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should return first network profile from end`() {
        getResponse("/v4/profile/networks?page_size=1&paging_from=end&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should paginate over filtered result`() {
        assertSuccessResponseWithVariations("/v4/profile/networks?networks=btc,avaxp&api_key=$TEST_API_KEY")
    }

    @Test
    fun `should return empty result if no profile matches networks parameter`() {
        getResponse("/v4/profile/networks?networks=test1,test2&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should return empty result if no profile matches full_names parameter`() {
        getResponse("/v4/profile/networks?full_names=test1,test2&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should return 400 error if networks and full_names parameters specified`() {
        getResponse("/v4/profile/networks?networks=avaxx&full_names=Bitcoin&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should return 400 error if invalid next_page_token specified`() {
        getResponse("/v4/profile/networks?next_page_token=&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should return empty result if next_page_token is valid but does not corresponds to a known actual network`() {
        getResponse("/v4/profile/networks?next_page_token=unknown&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should return 401 error if api_key is not specified`() {
        getResponse("/v4/profile/networks").assertResponse()
    }

    @Test
    fun `should return 403 error if api_key does not have access to network profiles`() {
        getResponse("/v4/profile/networks?api_key=$TEST_API_KEY_2").assertResponse()
    }
}
