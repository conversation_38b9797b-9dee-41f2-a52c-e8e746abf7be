package io.coinmetrics.api.endpoints.profile

import io.coinmetrics.api.helper.BaseTest
import io.coinmetrics.api.helper.TEST_API_KEY
import io.coinmetrics.api.helper.TEST_API_KEY_2
import io.coinmetrics.testing.autoexpect.AutoExpect
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.api.extension.ExtendWith
import java.nio.file.Path

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@ExtendWith(AutoExpect::class)
class GetAssetProfilesEndpointTest : BaseTest() {
    @BeforeAll
    fun refreshAssetProfilesDatas() =
        runBlocking {
            val testDataPath =
                GetAssetProfilesEndpointTest::class.java
                    .getResource("/asset-profiles")!!
                    .file
                    .let { Path.of(it) }
            mainApiModule.assetProfilesLocalStorage.refresh(testDataPath)
        }

    @Test
    fun `should return all asset profiles data`() {
        getResponse("/v4/profile/assets?api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should filter asset profiles by asset`() {
        getResponse("/v4/profile/assets?assets=dydx,coti&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should filter asset profiles by asset case-insensetive`() {
        getResponse("/v4/profile/assets?assets=DYDX&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should filter asset profiles by full_name`() {
        getResponse("/v4/profile/assets?full_names=Curve+DAO+Token,Audius&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should filter asset profiles by full_name case-insensetive`() {
        getResponse("/v4/profile/assets?full_names=curve+dao+token&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should paginate asset profiles`() {
        assertSuccessResponseWithVariations("/v4/profile/assets?api_key=$TEST_API_KEY")
    }

    @Test
    fun `should return first asset profile from start`() {
        getResponse("/v4/profile/assets?page_size=1&paging_from=start&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should return first asset profile from end`() {
        getResponse("/v4/profile/assets?page_size=1&paging_from=end&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should paginate over filtered result`() {
        assertSuccessResponseWithVariations("/v4/profile/assets?assets=dydx,coti&api_key=$TEST_API_KEY")
    }

    @Test
    fun `should return empty result if no profile matches assets parameter`() {
        getResponse("/v4/profile/assets?assets=test1,test2&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should return empty result if no profile matches full_names parameter`() {
        getResponse("/v4/profile/assets?full_names=test1,test2&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should return 400 error if assets and full_names parameters specified`() {
        getResponse("/v4/profile/assets?assets=coti&full_names=dYdX&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should return 400 error if invalid next_page_token specified`() {
        getResponse("/v4/profile/assets?next_page_token=&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should return empty result if next_page_token is valid but does not corresponds to a known actual asset`() {
        getResponse("/v4/profile/assets?next_page_token=unknown&api_key=$TEST_API_KEY").assertResponse()
    }

    @Test
    fun `should return 401 error if api_key is not specified`() {
        getResponse("/v4/profile/assets").assertResponse()
    }

    @Test
    fun `should return 403 error if api_key does not have access to asset profiles`() {
        getResponse("/v4/profile/assets?api_key=$TEST_API_KEY_2").assertResponse()
    }

    @Test
    fun `should maintain the order by asset over filtered result`() {
        getResponse("/v4/profile/assets?full_names=Kusama,Keep3rV1,Kyber%20Network%20Crystal,Kava&api_key=$TEST_API_KEY").assertResponse()
    }
}
