package io.coinmetrics.api.helper

import io.temporal.api.workflowservice.v1.RegisterNamespaceRequest
import io.temporal.serviceclient.WorkflowServiceStubs
import io.temporal.serviceclient.WorkflowServiceStubsOptions
import org.testcontainers.containers.GenericContainer
import org.testcontainers.containers.Network
import org.testcontainers.containers.PostgreSQLContainer
import org.testcontainers.containers.wait.strategy.Wait
import org.testcontainers.utility.DockerImageName
import java.util.concurrent.TimeUnit

class TemporalTestEnv {
    val network = Network.newNetwork()
    val postgres =
        PostgreSQLContainer("postgres:16.3")
            .withDatabaseName("temporal")
            .withUsername("temporal")
            .withPassword("temporal")
            .withNetworkAliases("postgresql")
            .withExposedPorts(5432)
            .withNetwork(network)
            .also { it.start() }
    val server =
        GenericContainer(DockerImageName.parse("temporalio/auto-setup:1.22.2"))
            .dependsOn(postgres)
            .withEnv("DB", "postgresql")
            .withEnv("DB_PORT", "5432")
            .withEnv("POSTGRES_USER", "temporal")
            .withEnv("POSTGRES_PWD", "temporal")
            .withEnv("POSTGRES_SEEDS", "postgresql")
            .waitingFor(Wait.forLogMessage(".*Search attributes have been added.*", 1))
            .withExposedPorts(7233)
            .withNetworkAliases("temporal")
            .withNetwork(network)
            .also { it.start() }
    val serviceTarget = "${server.host}:${server.firstMappedPort}"
    val service =
        WorkflowServiceStubs.newServiceStubs(
            WorkflowServiceStubsOptions {
                setTarget(serviceTarget)
            },
        )

    fun createNamespace(namespace: String) {
        service.blockingStub().registerNamespace(
            RegisterNamespaceRequest
                .newBuilder()
                .setNamespace(namespace)
                .setWorkflowExecutionRetentionPeriod(
                    com.google.protobuf.Duration
                        .newBuilder()
                        .setSeconds(TimeUnit.HOURS.toSeconds(1)),
                ).build(),
        )
    }

    companion object {
        val instance = lazy { TemporalTestEnv() }
    }
}
