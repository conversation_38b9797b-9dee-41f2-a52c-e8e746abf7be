package io.coinmetrics.api.helper

import com.fasterxml.jackson.core.JsonParseException
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.node.ObjectNode
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import io.coinmetrics.api.Server
import io.coinmetrics.api.endpoints.Endpoint
import io.coinmetrics.api.endpoints.TestWsEndpointImpl
import io.coinmetrics.api.modules.ApiModule
import io.coinmetrics.api.modules.common.CommonConfig
import io.coinmetrics.api.modules.common.CommonModule
import io.coinmetrics.api.modules.main.MainApiConfig
import io.coinmetrics.api.modules.main.MainApiModule
import io.coinmetrics.api.statistics.app.StatisticsApp
import io.coinmetrics.api.statistics.app.StatisticsAppConfig
import io.coinmetrics.api.utils.CsvUtils
import io.coinmetrics.api.utils.convert
import io.coinmetrics.testing.autoexpect.AutoExpect
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.future.await
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.AfterAll
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.api.TestInstance.Lifecycle
import org.junit.jupiter.api.assertDoesNotThrow
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.api.fail
import org.junit.jupiter.api.io.TempDir
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import java.io.StringReader
import java.net.URI
import java.net.URLEncoder
import java.net.http.HttpClient
import java.net.http.HttpRequest
import java.net.http.HttpRequest.BodyPublishers
import java.net.http.HttpResponse
import java.net.http.WebSocket
import java.nio.charset.StandardCharsets
import java.nio.file.Path
import java.time.Clock
import java.time.Duration
import java.time.Instant
import java.time.ZoneId

const val EMPTY_RESPONSE =
    """{"data":[]}"""
const val EMPTY_CSV_RESPONSE = ""
const val NOT_FOUND_RESPONSE =
    """{"error":{"type":"not_found","message":"Not found."}}"""
const val INVALID_NEXT_PAGE_TOKEN_RESPONSE =
    """{"error":{"type":"bad_parameter","message":"Bad parameter 'next_page_token'."}}"""

const val KAFKA_IMAGE = "confluentinc/cp-kafka:7.3.0"
const val POSTGRES_IMAGE = "postgres:10-alpine"

const val TEST_API_KEY = "x1"
const val TEST_API_KEY_2 = "x2" // TEST_API_KEY2 has no permissions
const val COMMUNITY_KEY = "T1z1tZcIY2tnX4jcUhve"
const val COMMUNITY_INDEXES_KEY = "x4"
const val COMMUNITY_INDEXES_KEY_2 = "x5"
const val ERIS_X_API_KEY = "erisxapikey"
const val ONLY_ERIS_X_API_KEY = "onlyerisxapikey"
const val GOD_KEY = "nkxOvVzPbGvoIucAUdR6"
const val CANDLES_TOPIC_NAME = "realtime_candles_all.proto"
const val QUOTES_TOPIC_NAME = "quotes"

@ExtendWith(AutoExpect::class)
@TestInstance(Lifecycle.PER_CLASS)
abstract class BaseTest {
    lateinit var server: Server

    private val client: HttpClient =
        HttpClient
            .newBuilder()
            .connectTimeout(Duration.ofMinutes(5))
            .build()

    val log: Logger = LoggerFactory.getLogger(this::class.java)
    val clock = TestClock()
    val objectMapper = jacksonObjectMapper()

    companion object {
        @TempDir
        private lateinit var tmpDir: Path

        /**
         * The default expected header keys and value patterns.
         *
         * If the pattern is null it means the value must be tracked. Otherwise, the value should match the pattern.
         *
         * References:
         *  - https://docs.coinmetrics.io/api/v4/#tag/Rate-limits
         */
        private val defaultExpectedHeaders =
            mapOf<String, Regex?>(
                "access-control-allow-origin" to Regex.fromLiteral("*"),
                "access-control-expose-headers" to Regex.fromLiteral("x-ratelimit-limit, x-ratelimit-remaining, x-ratelimit-reset"),
                "cache-control" to Regex.fromLiteral("no-cache"),
                "content-type" to null,
                "expires" to Regex.fromLiteral("Thu, 01 Jan 1970 00:00:00 GMT"),
                "x-accel-buffering" to Regex("no"),
                "x-ratelimit-limit" to Regex("""\d+(,\s+\d+;w=\d+(;[a-zA-Z0-9\-]+=([a-zA-Z0-9\-]+|"[\x20-\x21\x23-\x7e]+"))*)*"""),
                "x-ratelimit-remaining" to Regex("""\d+"""),
                "x-ratelimit-reset" to Regex("""\d+"""),
            )

        /**
         * The default response headers that are ignored.
         */
        private val defaultIgnoredHeaders = setOf(":status", "content-length")
    }

    private val defaultEnvs =
        mapOf(
            "API_DB_SCHEMA" to "test",
            "API_AMS_REQUEST_TIMEOUT_MS" to Duration.ofMinutes(5).toMillis().toString(),
            "API_DB_THREADS" to "10",
            "API_DB_QUERY_TIMEOUT_SEC" to Duration.ofMinutes(5).toSeconds().toString(),
            "API_DB_SLOW_QUERY_MS" to "3000",
            "API_DB_CONNECTION_TIMEOUT_MS" to Duration.ofMinutes(5).toMillis().toString(),
            "API_DEFI_ENABLED" to "true",
            "API_PORT" to "8080",
            "API_TRADE_KEYS_CACHE_SIZE" to "1000",
            "API_DB" to "postgresql://${Containers.postgreSQLContainer.host}:${Containers.postgreSQLContainer.port}/test?user=postgres",
            "API_AMS_HOST" to (Containers.amsContainer.host.takeIf { it != "localhost" } ?: "127.0.0.1"),
            "API_AMS_PORT" to "${Containers.amsContainer.port}",
            "API_SPOT_BOOKS_TIER_COLD_S3_ENDPOINT" to "http://${Containers.minio.host}:${Containers.minio.port}",
            "API_FUTURES_BOOKS_TIER_COLD_S3_ENDPOINT" to "http://${Containers.minio.host}:${Containers.minio.port}",
            "API_OPTIONS_BOOKS_TIER_COLD_S3_ENDPOINT" to "http://${Containers.minio.host}:${Containers.minio.port}",
            "API_DB_ATLAS_SHARD_1" to "postgresql://${Containers.postgreSQLContainer.host}:${Containers.postgreSQLContainer.port}/test?user=postgres&password=postgres&currentSchema=test",
            "API_ATLAS_SHARD_1_ASSETS" to "BTC,USDC,ETH,ALGO,ETC,LTC,DOGE,SPELL",
        )

    protected val envVariablesResolver: (String) -> String? = { name: String ->
        val overriddenEnvs = getAdditionalEnvVariables()
        // we still need to use the defaultEnvVariablesResolver to resolve DOCKER-related ENV variable passed to tests by GitLab
        CommonConfig.defaultEnvVariablesResolver.invoke(name) ?: overriddenEnvs[name] ?: defaultEnvs[name]
    }

    protected val envVariablesProvider: () -> Map<String, String> = {
        defaultEnvs + getAdditionalEnvVariables()
    }

    protected lateinit var statisticsApp: StatisticsApp

    protected open fun getAdditionalEnvVariables(): Map<String, String> = emptyMap()

    protected val commonModule by lazy { CommonModule(commonConfig()) }

    protected val mainApiModule by lazy { MainApiModule(commonModule, mainApiConfig()) }

    protected val statisticsConfig by lazy { statisticsConfig() }

    protected open fun otherModules(): List<ApiModule> = listOf(mainApiModule)

    protected open fun commonConfig(): CommonConfig =
        CommonConfig(
            envVariablesResolver = envVariablesResolver,
            port = 0,
            statsS3Endpoint = "http://${Containers.minio.host}:${Containers.minio.port}",
            statisticsPollInterval = Duration.ofMillis(500),
            customEndpoints = customEndpoints(),
            inTest = true,
            clock = clock,
        )

    protected open fun mainApiConfig(): MainApiConfig =
        MainApiConfig(
            common = commonModule.config,
            apiDataBaseDir = tmpDir,
            envVariablesProvider = envVariablesProvider,
            taxonomyDataLoad = false,
            networkProfilesDataLoad = false,
            assetProfilesDataLoad = false,
            securityMasterDataLoad = false,
            ndExperimentalAssetMetricsFile = "/experimental_asset_metrics.json",
            ndExperimentalAtlasFile = "/experimental_atlas.json",
            useNewBooksTables = true,
        )

    protected open fun statisticsConfig(): StatisticsAppConfig {
        val commonConfig = commonModule.config
        return StatisticsAppConfig(
            envVariablesResolver = envVariablesResolver,
            databases = mainApiModule.config.databases,
            booksTiers = mainApiModule.config.booksTiers,
            statsS3Endpoint = commonConfig.statsS3Endpoint,
            disabledStatistics = commonConfig.disabledStatistics,
            useNewBooksTables = true,
        )
    }

    protected fun customEndpoints(): List<Pair<String, Endpoint<*>>> {
        val testWsEndpoint = TestWsEndpointImpl()
        return listOf(
            "/v4/timeseries-stream/test" to testWsEndpoint,
            "/v4/timeseries-stream/slow-test" to testWsEndpoint,
        )
    }

    @BeforeAll
    fun startServer() =
        runBlocking {
            if (System.getProperty("os.name").lowercase().contains("win")) {
                /**
                 * This is required for compatibility with Windows when receiving pretty json responses from API server
                 */
                System.setProperty("line.separator", "\n")
            }

            coroutineScope {
                launch {
                    Containers.minio.init()
                }
                if (Containers.kafka1.isInitialized()) {
                    launch {
                        Containers.kafka1.value.init()
                    }
                }
            }

            val commonConfig = commonModule.config

            statisticsApp = StatisticsApp(commonConfig.deFiRawDataParser, statisticsConfig())
            statisticsApp.start()

            check(commonConfig.port == 0) { "Config.port must be set to 0" }
            check(commonConfig.envVariablesResolver === envVariablesResolver) {
                "Config.envVariablesResolver must be set to BaseTest.envVariablesResolver"
            }

            server = Server(commonModule, otherModules = otherModules())

            server.start()
            log.info("Server has started on port ${server.actualPort()}.")
        }

    @AfterAll
    fun stopServer(): Unit =
        runBlocking {
            server.close()
            statisticsApp.close()
        }

    protected open fun interceptPathAndQuery(pathAndQuery: String): String = pathAndQuery

    protected open fun interceptResponseBody(responseBody: String): String = responseBody

    fun getResponse(
        pathAndQuery: String,
        vararg headers: String,
    ): TestResponse {
        val newPathAndQuery = interceptPathAndQuery(pathAndQuery)
        val builder =
            HttpRequest
                .newBuilder(URI.create("http://127.0.0.1:${server.actualPort()}$newPathAndQuery"))
                .GET()

        if (headers.isNotEmpty()) {
            builder.headers(*headers)
        }

        val request = builder.build()
        val response = client.send(request, HttpResponse.BodyHandlers.ofString())
        val responseBody = response.body()
        val responseHeaders =
            response
                .headers()
                .map()
                .entries
                .associate { it.key.lowercase() to it.value.first() }
        return TestResponse(response.statusCode(), interceptResponseBody(responseBody), responseHeaders)
    }

    suspend fun getResponseAsync(
        pathAndQuery: String,
        vararg headers: String,
    ): TestResponse {
        val newPathAndQuery = interceptPathAndQuery(pathAndQuery)
        val builder =
            HttpRequest
                .newBuilder(URI.create("http://127.0.0.1:${server.actualPort()}$newPathAndQuery"))
                .GET()

        if (headers.isNotEmpty()) {
            builder.headers(*headers)
        }

        val request = builder.build()
        val response = client.sendAsync(request, HttpResponse.BodyHandlers.ofString()).await()
        val responseBody = response.body()
        val responseHeaders =
            response
                .headers()
                .map()
                .entries
                .associate { it.key.lowercase() to it.value.first() }
        return TestResponse(response.statusCode(), interceptResponseBody(responseBody), responseHeaders)
    }

    fun put(
        pathAndQuery: String,
        formAttributes: List<Pair<String, String>>,
    ): TestResponse {
        fun List<Pair<String, String>>.toStr(): String =
            this.joinToString(separator = "&") {
                "${URLEncoder.encode(it.first, StandardCharsets.UTF_8)}=${URLEncoder.encode(it.second, StandardCharsets.UTF_8)}"
            }
        val newPathAndQuery = interceptPathAndQuery(pathAndQuery)
        val builder =
            HttpRequest
                .newBuilder(URI.create("http://127.0.0.1:${server.actualPort()}$newPathAndQuery"))
                .PUT(BodyPublishers.ofString(formAttributes.toStr()))

        builder.headers("content-type", "application/x-www-form-urlencoded")

        val request = builder.build()
        val response = client.send(request, HttpResponse.BodyHandlers.ofString())
        val responseBody = response.body()
        val responseHeaders =
            response
                .headers()
                .map()
                .entries
                .associate { it.key.lowercase() to it.value.first() }
        return TestResponse(response.statusCode(), interceptResponseBody(responseBody), responseHeaders)
    }

    fun assertResponse(
        expectedCode: Int,
        expectedResponse: String,
        pathAndQuery: String,
        vararg headers: String,
    ) {
        assertMatchOneOfResponses(expectedCode, listOf(expectedResponse), pathAndQuery, "application/json", *headers)
    }

    fun assertResponseWithContentType(
        expectedCode: Int,
        expectedResponse: String,
        pathAndQuery: String,
        contentType: String = "application/json",
        vararg headers: String,
    ) {
        assertMatchOneOfResponses(expectedCode, listOf(expectedResponse), pathAndQuery, contentType, *headers)
    }

    fun assertMatchOneOfResponses(
        expectedCode: Int,
        expectedResponses: List<String>,
        pathAndQuery: String,
        contentType: String = "application/json",
        vararg headers: String,
    ) {
        val response =
            assertStatusCodeAndHeaders(
                expectedCode,
                expectedHeaders = listOf("content-type" to contentType),
                pathAndQuery,
                *headers,
            )
        response.assertResponse(expectedResponses)
    }

    fun assertStatusCodeAndHeaders(
        expectedCode: Int,
        expectedHeaders: List<Pair<String, String>> = emptyList(),
        pathAndQuery: String,
        vararg headers: String,
    ): TestResponse {
        val response = getResponse(pathAndQuery, *headers)
        response.assertStatusCodeAndHeaders(expectedCode, expectedHeaders)
        return response
    }

    /**
     * Verifies JSON response matches expectations across the following variations using [AutoExpect]:
     *  - Page size = 10000
     *  - Page size = 10000, paging from end
     *  - Page size = 1, paging from start, and then next page up to two times
     *  - Page size = 1, paging from end, and then next page up to two times
     *  - Format = `json_stream`
     *
     * Each response is checked to have appropriate values for *data*, *next_page_token*, and *next_page_url*
     *
     * @param pathAndQuery The path and query portion with which to retrieve GET responses.
     * @param requestHeaders The request headers to send with the GET requests.
     * @param idPrefix An optional prefix prepended to the id used with AutoExpect.
     */
    fun assertSuccessResponseWithVariations(
        pathAndQuery: String,
        requestHeaders: Map<String, String> = mapOf(),
        idPrefix: String? = null,
    ) {
        val objectMapper = commonModule.objectMapper
        val headersArray = requestHeaders.entries.flatMap { (k, v) -> listOf(k, v) }.toTypedArray()
        val prefix = if (idPrefix != null) "$idPrefix." else ""

        fun getJsonResponse(
            extraQuery: String = "",
            format: String = "json",
        ): TestResponse {
            val formatQuery =
                when (format) {
                    "json" -> ""
                    "json_stream" -> "&format=json_stream"
                    else -> error("Bad format: $format")
                }
            val pq = "$pathAndQuery${extraQuery.takeIf { it.isNotEmpty() }?.let { "&$it" } ?: ""}$formatQuery"
            return getResponse(pq, *headersArray)
        }

        fun getPaginatedJsonResponse(
            extraQuery: String,
            withNextPageToken: String? = null,
        ): Pair<TestResponse, String?> {
            val request = "$pathAndQuery&$extraQuery" + if (withNextPageToken == null) "" else "&next_page_token=$withNextPageToken"
            val resp = getResponse(request, *headersArray)

            val body = resp.body.let { objectMapper.readValue<Map<String, Any>>(it) }
            val data = body["data"]
            Assertions.assertNotNull(data)
            val nextPageToken = body["next_page_token"] as String?
            val nextPageUrl = body["next_page_url"] as String?
            assertTrue((nextPageToken == null) == (nextPageUrl == null))
            if (nextPageToken == null) {
                assertEquals(listOf("data"), body.keys.toList())
            } else {
                assertEquals(listOf("data", "next_page_token", "next_page_url"), body.keys.toList())
                assertEquals(
                    "http://127.0.0.1:${server.actualPort()}$pathAndQuery&$extraQuery&next_page_token=$nextPageToken",
                    nextPageUrl,
                )
            }
            return resp to nextPageToken
        }

        val dataCount =
            getJsonResponse("page_size=10000").let { response ->
                response.assertResponse(idPrefix = "${prefix}page_size_10000")
                (objectMapper.readValue<Map<String, Any>>(response.body)["data"] as List<*>).size
            }
        getJsonResponse("page_size=10000&paging_from=end").assertResponse(idPrefix = "${prefix}page_size_10000.paging_from_end")

        // Verify paging from start.
        var nextPageToken =
            getPaginatedJsonResponse("page_size=1&paging_from=start").let { (response, nextPageToken) ->
                response.assertResponse(idPrefix = "${prefix}page_size_1.paging_from_start.page_1")
                nextPageToken
            }
        if (dataCount > 1) {
            for (page in 2..3) {
                if (nextPageToken == null) {
                    assertEquals(dataCount, page - 1)
                    break
                } else {
                    nextPageToken =
                        getPaginatedJsonResponse("page_size=1&paging_from=start", nextPageToken).let { (response, nextPageToken) ->
                            response.assertResponse(idPrefix = "${prefix}page_size_1.paging_from_start.page_$page")
                            nextPageToken
                        }
                }
            }
        }

        // Verify paging from end.
        nextPageToken =
            getPaginatedJsonResponse("page_size=1&paging_from=end").let { (response, nextPageToken) ->
                response.assertResponse(idPrefix = "${prefix}page_size_1.paging_from_end.page_1")
                nextPageToken
            }
        if (dataCount > 1) {
            for (page in 2..3) {
                if (nextPageToken == null) {
                    assertEquals(dataCount, page - 1)
                    break
                } else {
                    nextPageToken =
                        getPaginatedJsonResponse("page_size=1&paging_from=end", nextPageToken).let { (response, nextPageToken) ->
                            response.assertResponse(idPrefix = "${prefix}page_size_1.paging_from_end.page_$page")
                            nextPageToken
                        }
                }
            }
        }

        // Verify json_stream.
        getJsonResponse(format = "json_stream").assertResponse(idPrefix = "${prefix}format_json_stream")
    }

    fun connectToWebSocket(
        pathAndQuery: String,
        webSocketListener: WebSocket.Listener,
    ): WebSocket =
        client
            .newWebSocketBuilder()
            .buildAsync(
                URI.create("ws://127.0.0.1:${server.actualPort()}$pathAndQuery"),
                webSocketListener,
            ).join()

    suspend fun connectToWebSocketAsync(
        pathAndQuery: String,
        webSocketListener: WebSocket.Listener,
    ): WebSocket =
        client
            .newWebSocketBuilder()
            .buildAsync(
                URI.create("ws://127.0.0.1:${server.actualPort()}$pathAndQuery"),
                webSocketListener,
            ).await()

    inline fun <reified T : Any> jsonResponseToCsvResponse(
        jsonResponse: String,
        dataExtractor: (T) -> Any,
    ): String {
        val objectMapper = server.commonModule.objectMapper
        val marketResponse = objectMapper.readValue<T>(jsonResponse)
        val maps = objectMapper.convert<List<Map<String, String>>>(dataExtractor(marketResponse))

        return CsvUtils.toCsvData(data = maps.toTypedArray(), nullValue = "")
    }

    fun assertOneOf(
        actual: String,
        expected: List<String>,
    ) {
        require(expected.isNotEmpty()) { "Expected list must not be empty." }
        if (actual !in expected) {
            fail { "actual: <$actual>, expected one of: <$expected>" }
        }
    }

    fun TestResponse.assertResponse(
        expectedCode: Int,
        expectedResponse: String,
        expectedHeaders: List<Pair<String, String>> = emptyList(),
    ) {
        this.assertStatusCodeAndHeaders(expectedCode, expectedHeaders)
        this.assertResponse(listOf(expectedResponse))
    }

    private fun TestResponse.assertResponse(expectedResponses: List<String>) {
        var actual: String =
            this.body.replace(
                "http://127.0.0.1:${server.actualPort()}",
                "http://127.0.0.1:8080",
            )
        if (expectedResponses.size == 1) {
            var expected: String = expectedResponses[0]

            if (LocalTestConfiguration.isShowMultilineDiffs()) {
                // Try to pretty print as JSON for showing multiline diffs if the parser permits it, otherwise fallback to the original values
                try {
                    val expectedRawJson = objectMapper.readTree(StringReader(expected))
                    expected = objectMapper.writer().withDefaultPrettyPrinter().writeValueAsString(expectedRawJson)

                    val actualRawJson = objectMapper.readTree(StringReader(actual))
                    actual = objectMapper.writer().withDefaultPrettyPrinter().writeValueAsString(actualRawJson)
                } catch (_: JsonParseException) {
                    // Not an error, fallback to original values
                }
            }
            assertEquals(expected, actual)
        } else {
            assertTrue(expectedResponses.contains(actual), "response ${actual}\n is not in expected responses:\n$expectedResponses")
        }
    }

    fun TestResponse.assertStatusCodeAndHeaders(
        expectedCode: Int,
        expectedHeaders: List<Pair<String, String>> = emptyList(),
    ) {
        assertEquals(expectedCode, this.status) {
            "Response: ${this.body}"
        }
        expectedHeaders.forEach { (name, value) ->
            assertEquals(value, this.headers[name.lowercase()]) {
                "Response doesn't contain header $name. Response: ${this.body}"
            }
        }
    }

    /**
     * Asserts or records the status code, headers and content of [TestResponse] with [AutoExpect].
     *
     * @param idPrefix An optional prefix prepended to the id used with AutoExpect.
     * @param ignoreContent Whether the response content can be ignored.
     * @return The JSON object and content used in the test.
     *
     * @see [TestResponse.assertResponse]
     * @see [TestResponse.assertStatusCodeAndHeaders]
     */
    fun TestResponse.assertResponse(
        idPrefix: String? = null,
        ignoreContent: Boolean = false,
    ): Pair<ObjectNode, String> {
        val actual: String =
            this.body.replace(
                "http://127.0.0.1:${server.actualPort()}",
                "http://127.0.0.1:8080",
            )

        // Verify required response headers
        assertNotEquals(emptySet<String>(), defaultExpectedHeaders.keys - <EMAIL>, "Missing required headers")

        val contentType = <EMAIL>["content-type"]!!

        // Create object to use with AutoExpect that contains expected status, response headers, and content
        val autoExpectObject =
            objectMapper.createObjectNode().apply {
                put("status", <EMAIL>)

                val autoExpectHeaders = mutableSetOf<String>()
                (<EMAIL> - defaultIgnoredHeaders).forEach { (key, value) ->
                    when (val re = defaultExpectedHeaders[key]) {
                        is Regex -> assertTrue(value.matches(re)) { "Response header value for '$key' doesn't match `${re.pattern}`: $value" }
                        null -> autoExpectHeaders += key
                    }
                }

                if (autoExpectHeaders.isNotEmpty()) {
                    set<JsonNode>(
                        "headers",
                        objectMapper.createObjectNode().apply {
                            autoExpectHeaders.map { it.lowercase() }.sorted().forEach { key ->
                                if (key == "x-next-page-url") {
                                    put(
                                        key,
                                        <EMAIL>["x-next-page-url"]!!.replace(
                                            "http://127.0.0.1:${server.actualPort()}",
                                            "http://127.0.0.1:8080",
                                        ),
                                    )
                                } else {
                                    put(key, <EMAIL>[key])
                                }
                            }
                        },
                    )
                }

                if (!ignoreContent) {
                    when (contentType) {
                        "application/json" ->
                            set(
                                "content",
                                assertDoesNotThrow("Error reading JSON") {
                                    objectMapper.readTree(StringReader(actual))
                                },
                            )
                        "application/x-ndjson" ->
                            set<JsonNode>(
                                "content",
                                objectMapper.createArrayNode().apply {
                                    assertDoesNotThrow("Error reading NDJSON") {
                                        if (actual.isNotEmpty()) {
                                            addAll(actual.split('\n').map { objectMapper.readTree(it) })
                                        }
                                    }
                                },
                            )
                    }
                }
            }

        // Run auto expect verification based on content type
        when (contentType) {
            "application/json", "application/x-ndjson" -> {
                AutoExpect.id(idPrefix).verifyJson(autoExpectObject)
            }
            "text/csv" -> {
                val prefix = idPrefix?.let { "$it." } ?: ""
                AutoExpect.id("${prefix}status_and_headers").verifyJson(autoExpectObject)
                AutoExpect.id("${prefix}verbatim_content").verify(actual)
            }
            else ->
                fail(
                    """
                    Unsupported content type: '$contentType'. Available content storage variants:
                     - JSON (indented): 'application/json', 'application/x-ndjson'
                     - Verbatim: 'text/csv'
                    """.trimIndent(),
                )
        }

        return autoExpectObject to actual
    }
}

data class TestResponse(
    val status: Int,
    val body: String,
    val headers: Map<String, String>,
)

class TestClock(
    var instant: Instant = Instant.now(),
) : Clock() {
    override fun getZone(): ZoneId {
        TODO("Not yet implemented")
    }

    override fun withZone(zone: ZoneId?): Clock {
        TODO("Not yet implemented")
    }

    override fun instant(): Instant = instant
}
