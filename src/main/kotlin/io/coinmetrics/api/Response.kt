package io.coinmetrics.api

import com.fasterxml.jackson.annotation.JsonAutoDetect
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.PropertyAccessor
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.ObjectWriter
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import io.coinmetrics.api.utils.CsvUtils
import io.coinmetrics.api.utils.toResponseObject
import io.coinmetrics.httpserver.HttpRequest
import io.coinmetrics.httpserver.HttpResponse
import io.coinmetrics.httpserver.UpgradeToWebSocketResponse
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.asFlow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.toList
import java.io.ByteArrayOutputStream
import java.time.Instant
import java.time.ZoneOffset
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import kotlin.coroutines.CoroutineContext

sealed class Response<T> {
    abstract fun toHttpResponse(
        httpRequest: HttpRequest,
        context: CoroutineContext,
    ): HttpResponse

    data class RawHttpResponse<T>(
        val httpResponse: HttpResponse,
    ) : Response<T>() {
        override fun toHttpResponse(
            httpRequest: HttpRequest,
            context: CoroutineContext,
        ): HttpResponse =
            if (httpResponse is UpgradeToWebSocketResponse) {
                httpResponse
            } else if (httpResponse.chunkedStream == null) {
                httpResponse
            } else {
                newHttpResponseWithDisabledCache(
                    status = httpResponse.status,
                    headers = httpResponse.headers,
                    contentType = httpResponse.contentType,
                    chunkedStream = httpResponse.chunkedStream?.flowOn(context),
                )
            }
    }

    data class ObjectResponse<T>(
        val status: Int,
        val obj: T,
        val headers: List<Pair<String, String>> = emptyList(),
    ) : Response<T>() {
        override fun toHttpResponse(
            httpRequest: HttpRequest,
            context: CoroutineContext,
        ): HttpResponse =
            newHttpResponseWithDisabledCache(
                status = status,
                body = objToJson(obj, httpRequest),
                headers = headers,
            )
    }

    data class ChunkedJsonResponse<T>(
        val status: Int,
        val itemStream: Flow<Any>,
        val format: ChunkedResponseFormat.Json,
        val headers: List<Pair<String, String>> = emptyList(),
        val minChunkSize: Int,
    ) : Response<T>() {
        override fun toHttpResponse(
            httpRequest: HttpRequest,
            context: CoroutineContext,
        ): HttpResponse {
            val jsonWriter = jsonWriter(httpRequest)
            val jsonItemWriter = jsonItemWriter(httpRequest, format.allowNullValues)
            return newHttpResponseWithDisabledCache(
                status = status,
                headers = headers,
                chunkedStream = itemStream.toChunkedJson(jsonWriter, jsonItemWriter, minChunkSize).flowOn(context),
            )
        }
    }

    data class ChunkedJsonStreamResponse<T>(
        val status: Int,
        val itemStream: Flow<Any>,
        val headers: List<Pair<String, String>> = emptyList(),
        val minChunkSize: Int,
    ) : Response<T>() {
        override fun toHttpResponse(
            httpRequest: HttpRequest,
            context: CoroutineContext,
        ): HttpResponse =
            newHttpResponseWithDisabledCache(
                status = status,
                headers = headers,
                contentType = "application/x-ndjson",
                chunkedStream = itemStream.toChunkedJsonStream(jsonWriter, minChunkSize).flowOn(context),
            )
    }

    data class ChunkedCsvResponse<T>(
        val status: Int,
        val itemStream: Flow<Any>,
        val format: ChunkedResponseFormat.Csv,
        val headers: List<Pair<String, String>> = emptyList(),
        val minChunkSize: Int,
    ) : Response<T>() {
        override fun toHttpResponse(
            httpRequest: HttpRequest,
            context: CoroutineContext,
        ): HttpResponse =
            newHttpResponseWithDisabledCache(
                status = status,
                headers = headers,
                contentType = "text/csv",
                chunkedStream = itemStream.toChunkedCsv(format.nullValue, minChunkSize).flowOn(context),
            )
    }

    data class ErrorObjectResponse<T>(
        val status: Int,
        val errorObj: Any,
        val headers: List<Pair<String, String>> = emptyList(),
    ) : Response<T>() {
        override fun toHttpResponse(
            httpRequest: HttpRequest,
            context: CoroutineContext,
        ): HttpResponse =
            newHttpResponseWithDisabledCache(
                status = status,
                body = objToJson(errorObj, httpRequest),
                headers = headers,
            )

        fun toWebSocketTextMessage(httpRequest: HttpRequest): String = objToJson(errorObj, httpRequest)
    }

    companion object {
        private val jsonObjectMapper =
            ObjectMapper()
                .registerKotlinModule()
                .setDefaultPropertyInclusion(JsonInclude.Include.NON_NULL)
                // following lines are required to properly handle fields started with "n_"
                .setVisibility(PropertyAccessor.FIELD, JsonAutoDetect.Visibility.ANY)
                .setVisibility(PropertyAccessor.CREATOR, JsonAutoDetect.Visibility.NONE)
                .setVisibility(PropertyAccessor.GETTER, JsonAutoDetect.Visibility.NONE)
                .setVisibility(PropertyAccessor.SETTER, JsonAutoDetect.Visibility.NONE)
                .setVisibility(PropertyAccessor.IS_GETTER, JsonAutoDetect.Visibility.NONE)
        private val jsonWriter = jsonObjectMapper.writer()
        private val prettyJsonWriter = jsonWriter.withDefaultPrettyPrinter()
        private val jsonWriterNullable = jsonObjectMapper.copy().setDefaultPropertyInclusion(JsonInclude.Include.USE_DEFAULTS).writer()
        private val prettyJsonWriterNullable = jsonWriterNullable.withDefaultPrettyPrinter()

        private const val HTTP_MIN_CHUNK_SIZE = 16 * 1024
        private val newLine = "\n".encodeToByteArray()
        private val expiresHeaderDefaultValue =
            DateTimeFormatter
                .ofPattern("EEE, dd MMM yyyy HH:mm:ss 'GMT'")
                .format(ZonedDateTime.ofInstant(Instant.EPOCH, ZoneOffset.UTC))
        private const val FORCE_EMIT_TIMEOUT_MS = 5_000

        /**
         * In most cases, you don't want to call this method.
         * Instead, you want to call [chunkedResponse] that brings less load on the API server and allows to measure and shape response traffic.
         * [successResponse] can be used only in exceptional cases for very small responses (not for time series or catalog data).
         */
        fun <T> successResponse(
            obj: T,
            headers: List<Pair<String, String>> = emptyList(),
        ): ObjectResponse<T> = ObjectResponse(200, obj, headers)

        /**
         * Streams itemStream with backpressure support.
         */
        private fun <T> chunkedJsonResponse(
            itemStream: Flow<Any>,
            headers: List<Pair<String, String>> = emptyList(),
            format: ChunkedResponseFormat.Json,
            minChunkSize: Int = HTTP_MIN_CHUNK_SIZE,
        ): ChunkedJsonResponse<T> = ChunkedJsonResponse(200, itemStream, format, headers, minChunkSize)

        /**
         * Streams itemStream with backpressure support.
         */
        private fun <T> chunkedJsonStreamResponse(
            itemStream: Flow<Any>,
            headers: List<Pair<String, String>> = emptyList(),
            minChunkSize: Int = HTTP_MIN_CHUNK_SIZE,
        ): ChunkedJsonStreamResponse<T> = ChunkedJsonStreamResponse(200, itemStream, headers, minChunkSize)

        /**
         * Streams itemStream with backpressure support.
         */
        private fun <T> chunkedCsvResponse(
            itemStream: Flow<Any>,
            format: ChunkedResponseFormat.Csv,
            headers: List<Pair<String, String>> = emptyList(),
            minChunkSize: Int = HTTP_MIN_CHUNK_SIZE,
        ): ChunkedCsvResponse<T> = ChunkedCsvResponse(200, itemStream, format, headers, minChunkSize)

        suspend fun <T> chunkedResponse(
            items: Flow<Any>,
            headers: List<Pair<String, String>> = emptyList(),
            format: ChunkedResponseFormat,
            minChunkSize: Int = HTTP_MIN_CHUNK_SIZE,
        ): Response<T> =
            when (format) {
                is ChunkedResponseFormat.Json -> chunkedJsonResponse(items, headers, format, minChunkSize)
                ChunkedResponseFormat.JsonStream -> chunkedJsonStreamResponse(items, headers, minChunkSize)
                is ChunkedResponseFormat.Csv -> {
                    // todo remove this hack once paging support for CSV is redesigned to avoid buffering
                    val csvItems = items.toList(ArrayList()) as ArrayList
                    val newHeaders = headers.toMutableList()
                    newHeaders.add("content-disposition" to "inline; filename=data.csv")
                    csvItems.lastOrNull()?.let {
                        if (it is NextPageInfo) {
                            csvItems.removeLast()
                            newHeaders.add("x-next-page-token" to it.token)
                            newHeaders.add("x-next-page-url" to it.url)
                        }
                    }
                    chunkedCsvResponse(csvItems.asFlow(), format, newHeaders, minChunkSize)
                }
            }

        fun <T> errorResponse(
            apiError: ApiError,
            headers: List<Pair<String, String>> = emptyList(),
        ): ErrorObjectResponse<T> = ErrorObjectResponse(apiError.status, apiError.toResponseObject(), headers)

        fun <T> rawHttpResponse(httpResponse: HttpResponse): RawHttpResponse<T> = RawHttpResponse(httpResponse)

        private fun jsonWriter(httpRequest: HttpRequest): ObjectWriter =
            if (httpRequest.queryParameters["pretty"].equals("true", true)) prettyJsonWriter else jsonWriter

        private fun jsonItemWriter(
            httpRequest: HttpRequest,
            allowNullValues: Boolean,
        ): ObjectWriter =
            if (httpRequest.queryParameters["pretty"].equals("true", true)) {
                if (allowNullValues) prettyJsonWriterNullable else prettyJsonWriter
            } else if (allowNullValues) {
                jsonWriterNullable
            } else {
                jsonWriter
            }

        private fun objToJson(
            obj: Any?,
            httpRequest: HttpRequest,
        ): String {
            val writer = jsonWriter(httpRequest)
            return writer.writeValueAsString(obj)
        }

        fun objToJsonBytes(
            obj: Any?,
            httpRequest: HttpRequest,
        ): ByteArray {
            val writer = jsonWriter(httpRequest)
            return writer.writeValueAsBytes(obj)
        }

        /**
         * @param itemWriter is needed to have a different null include logic from [writer].
         */
        private fun Flow<Any>.toChunkedJson(
            writer: ObjectWriter,
            itemWriter: ObjectWriter,
            minChunkSize: Int,
        ): Flow<ByteArray> {
            // 8000 is the size of JsonGenerator internal write buffer
            assert(minChunkSize >= 8000)
            return flow<ByteArray> {
                val byteArrayOutputStream = ByteArrayOutputStream(2 * minChunkSize)
                // Returning the first chunk as soon as possible is crucial to prevent Cloudflare timeouts,
                // particularly in instances of high-volume granularities such as 1h or 1d,
                // or when multiple markets are requested within a short time range.
                var firstElement = true
                var lastEmitTime = 0L
                writer.createGenerator(byteArrayOutputStream).use { gen ->
                    var nextPageInfo: NextPageInfo? = null
                    gen.writeStartObject()
                    gen.writeArrayFieldStart("data")
                    collect { item ->
                        val forceFlushAndEmit: Boolean = (System.currentTimeMillis() - lastEmitTime) >= FORCE_EMIT_TIMEOUT_MS
                        if (item is NextPageInfo) {
                            nextPageInfo = item
                        } else if (item is ByteArray) {
                            if (!firstElement) {
                                gen.writeRaw(',')
                            }
                            // todo later: find a way how to avoid conversion to string to make the code more efficient
                            gen.writeRaw(item.decodeToString())
                            if (firstElement || forceFlushAndEmit) {
                                gen.flush()
                            }
                            val hasData: Boolean = byteArrayOutputStream.size() > 0
                            if ((forceFlushAndEmit && hasData) ||
                                (firstElement && hasData) ||
                                byteArrayOutputStream.size() >= minChunkSize
                            ) {
                                emit(byteArrayOutputStream.toByteArray())
                                lastEmitTime = System.currentTimeMillis()
                                byteArrayOutputStream.reset()
                            }
                        } else {
                            val rawJson = itemWriter.writeValueAsString(item)
                            gen.writeRawValue(rawJson)
                            if (firstElement || forceFlushAndEmit) {
                                gen.flush()
                            }
                            val hasData: Boolean = byteArrayOutputStream.size() > 0
                            if ((forceFlushAndEmit && hasData) ||
                                (firstElement && hasData) ||
                                byteArrayOutputStream.size() >= minChunkSize
                            ) {
                                emit(byteArrayOutputStream.toByteArray())
                                lastEmitTime = System.currentTimeMillis()
                                byteArrayOutputStream.reset()
                            }
                        }
                        firstElement = false
                    }
                    gen.writeEndArray()
                    nextPageInfo?.token?.also { gen.writeStringField("next_page_token", it) }
                    nextPageInfo?.url?.also { gen.writeStringField("next_page_url", it) }
                    gen.writeEndObject()
                }
                if (byteArrayOutputStream.size() > 0) {
                    emit(byteArrayOutputStream.toByteArray())
                }
            }
        }

        private fun Flow<Any>.toChunkedJsonStream(
            writer: ObjectWriter,
            minChunkSize: Int,
        ): Flow<ByteArray> {
            // 8000 is the size of JsonGenerator internal write buffer
            assert(minChunkSize >= 8000)
            return flow<ByteArray> {
                val byteArrayOutputStream = ByteArrayOutputStream(2 * minChunkSize)
                // Returning the first chunk as soon as possible is crucial to prevent Cloudflare timeouts,
                // particularly in instances of high-volume granularities such as 1h or 1d,
                // or when multiple markets are requested within a short time range.
                var firstItemProcessed = false
                var lastEmitTime = 0L
                collect { item ->
                    if (firstItemProcessed) {
                        byteArrayOutputStream.writeBytes(newLine)
                    }
                    val itemBytes = if (item is ByteArray) item else writer.writeValueAsBytes(item)
                    byteArrayOutputStream.writeBytes(itemBytes)
                    val forceEmit: Boolean = (System.currentTimeMillis() - lastEmitTime) >= FORCE_EMIT_TIMEOUT_MS
                    val hasData: Boolean = byteArrayOutputStream.size() > 0
                    if ((forceEmit && hasData) || (!firstItemProcessed && hasData) || byteArrayOutputStream.size() >= minChunkSize) {
                        emit(byteArrayOutputStream.toByteArray())
                        lastEmitTime = System.currentTimeMillis()
                        byteArrayOutputStream.reset()
                    }
                    firstItemProcessed = true
                }
                if (byteArrayOutputStream.size() > 0) {
                    emit(byteArrayOutputStream.toByteArray())
                }
            }
        }

        private fun Flow<Any>.toChunkedCsv(
            nullValue: String,
            minChunkSize: Int,
        ): Flow<ByteArray> {
            assert(minChunkSize > 0)
            return flow<ByteArray> {
                val byteArrayOutputStream = ByteArrayOutputStream(2 * minChunkSize)
                // Returning the first chunk as soon as possible is crucial to prevent Cloudflare timeouts,
                // particularly in instances of high-volume granularities such as 1h or 1d,
                // or when multiple markets are requested within a short time range.
                var firstItemProcessed = false
                collect { item ->
                    // at the moment, we assume that flow can't contain NextPageInfo
                    // because it's removed in the chunkedResponse method
                    val map =
                        @Suppress(
                            "UNCHECKED_CAST",
                        )
                        (item as Map<String, String?>)

                    if (!firstItemProcessed) {
                        val line = CsvUtils.headerLine(map)
                        byteArrayOutputStream.writeBytes(line.encodeToByteArray())
                        byteArrayOutputStream.writeBytes(newLine)
                    }

                    val line = CsvUtils.valuesLine(map, nullValue)
                    byteArrayOutputStream.writeBytes(line.encodeToByteArray())
                    byteArrayOutputStream.writeBytes(newLine)
                    val hasData: Boolean = byteArrayOutputStream.size() > 0
                    if ((!firstItemProcessed && hasData) || byteArrayOutputStream.size() >= minChunkSize) {
                        emit(byteArrayOutputStream.toByteArray())
                        byteArrayOutputStream.reset()
                    }
                    firstItemProcessed = true
                }
                if (byteArrayOutputStream.size() > 0) {
                    emit(byteArrayOutputStream.toByteArray())
                }
            }
        }
    }

    protected fun newHttpResponseWithDisabledCache(
        status: Int = 200,
        body: String = "",
        contentType: String = "application/json",
        headers: List<Pair<String, String>> = emptyList(),
        chunkedStream: Flow<ByteArray>? = null,
        binaryBody: ByteArray? = null,
    ): HttpResponse {
        val updatedHeaders =
            if ("cache-control" in headers || "expires" in headers) {
                headers
            } else {
                val newHeaders = ArrayList(headers)
                newHeaders.add("cache-control" to "no-cache")
                newHeaders.add("expires" to expiresHeaderDefaultValue)
                newHeaders
            }
        return HttpResponse(
            status,
            body,
            contentType,
            updatedHeaders,
            chunkedStream,
            binaryBody,
        )
    }

    private operator fun List<Pair<String, String>>.contains(headerName: String) =
        this.any { (key) ->
            key.equals(
                headerName,
                ignoreCase = true,
            )
        }
}

sealed class ChunkedResponseFormat(
    val name: String,
) {
    class Json(
        val allowNullValues: Boolean = false,
    ) : ChunkedResponseFormat("json")

    object JsonStream : ChunkedResponseFormat("json_stream")

    class Csv(
        val nullValue: String = "null",
    ) : ChunkedResponseFormat("csv")

    companion object {
        fun fromValueOrError(
            value: String?,
            supportedFormats: Iterable<ChunkedResponseFormat>,
            defaultFormat: String = "json",
        ): FunctionResult<ApiError, ChunkedResponseFormat> {
            return fromValue(value, supportedFormats, defaultFormat)?.toSuccess() ?: run {
                val supportedFormatsStr = supportedFormats.joinToString(separator = ", ") { it.name }
                return FunctionResult.Failure(ApiError.BadParameter("format", "Must be one of the following: $supportedFormatsStr."))
            }
        }

        private fun fromValue(
            value: String?,
            supportedFormats: Iterable<ChunkedResponseFormat>,
            defaultFormat: String,
        ): ChunkedResponseFormat? {
            val nonEmptyValue = value?.takeIf { it.isNotEmpty() } ?: defaultFormat
            return supportedFormats.find { it.name == nonEmptyValue }
        }
    }
}

data class NextPageInfo(
    val url: String,
    val token: String,
)
