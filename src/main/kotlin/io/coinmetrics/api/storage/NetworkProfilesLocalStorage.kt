package io.coinmetrics.api.storage

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import io.coinmetrics.api.models.NetworkProfileInfo
import org.slf4j.LoggerFactory
import java.io.File
import java.nio.file.Path

class NetworkProfilesLocalStorage(
    val objectMapper: ObjectMapper,
) : LocalStorage {
    companion object {
        private val log = LoggerFactory.getLogger(NetworkProfilesLocalStorage::class.java)
    }

    @Volatile
    var data: NetworkProfilesData = NetworkProfilesData()
        private set

    override fun refresh(dataDir: Path) {
        val networkProfilesDataFile = dataDir.resolve("network_profiles.json").toFile()
        val profiles = readNetworkProfilesData(networkProfilesDataFile) ?: return

        data =
            NetworkProfilesData(
                profiles = profiles.sortedBy { it.network },
                profilesPerNetwork = profiles.associateBy { it.network.lowercase() },
                profilesPerFullName = profiles.associateBy { it.fullName.lowercase() },
            )
    }

    private fun readNetworkProfilesData(networkProfilesDataFile: File) =
        try {
            objectMapper.readValue<List<NetworkProfileInfo>>(networkProfilesDataFile)
        } catch (e: Exception) {
            log.error("Could not read network profiles data file: ${networkProfilesDataFile.path}", e)
            null
        }

    data class NetworkProfilesData(
        val profiles: List<NetworkProfileInfo> = emptyList(),
        val profilesPerNetwork: Map<String, NetworkProfileInfo> = emptyMap(),
        val profilesPerFullName: Map<String, NetworkProfileInfo> = emptyMap(),
    )
}
