package io.coinmetrics.api.storage

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.model.taxonomy.TaxonomyAssetRevision
import io.coinmetrics.api.model.taxonomy.TaxonomyIndustryMetadata
import io.coinmetrics.api.model.taxonomy.TaxonomyMetadata
import io.coinmetrics.api.model.taxonomy.TaxonomySpecAsset
import io.coinmetrics.api.model.taxonomy.TaxonomySpecAssets
import io.coinmetrics.api.model.taxonomy.TaxonomySpecVersion
import io.coinmetrics.api.model.taxonomy.TaxonomySpecVersionClass
import io.coinmetrics.api.model.taxonomy.TaxonomySpecVersionSector
import io.coinmetrics.api.model.taxonomy.TaxonomySpecVersionSubsector
import io.coinmetrics.api.model.taxonomy.TaxonomySpecVersions
import io.coinmetrics.api.toSuccess
import io.coinmetrics.api.utils.TimeUtils
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import java.nio.file.Files
import java.nio.file.Path
import java.time.Instant
import java.time.ZoneOffset
import java.util.TreeMap
import kotlin.io.path.exists
import kotlin.io.path.name

class TaxonomyLocalStorage(
    private val objectMapper: ObjectMapper,
) : LocalStorage {
    companion object {
        private val log: Logger = LoggerFactory.getLogger(TaxonomyLocalStorage::class.java)
    }

    @Volatile
    var taxonomyMetadataPerVersion: Map<TaxonomyVersion, TaxonomyMetadata> = emptyMap()
        private set

    @Volatile
    var taxonomyAssetRevisionsPerAsset: Map<String, List<TaxonomyAssetRevision>> = emptyMap()
        private set

    @Volatile
    var taxonomyAssetRevisionsLatest: List<TaxonomyAssetRevision> = emptyList()
        private set

    @Volatile
    var taxonomyAssetRevisionsPerAssetNamePerVersion: Map<TaxonomyVersion, Map<String, TaxonomyAssetRevision>> = emptyMap()
        private set

    @Volatile
    var latestTaxonomyVersion: TaxonomyVersion? = null
        private set

    override fun refresh(dataDir: Path) {
        val targetDirectory = dataDir.resolve("taxonomy_data")
        val taxonomyVersionsMappedOnVersion = readTaxonomySpecVersions(targetDirectory) ?: return
        val taxonomyAssets = readTaxonomySpecAssets(targetDirectory) ?: return

        // map subsectorID on triple of (class, sector, subsector) for reference
        val taxonomySubsectorsPerVersion = getSubsectorsInfo(taxonomyVersionsMappedOnVersion) ?: return
        val taxonomyMetadataPerVersion =
            convertToTaxonomyMetadata(
                taxonomyVersionsMappedOnVersion,
                taxonomySubsectorsPerVersion,
            ).getOrElse {
                log.error("Classification validation failed. $it")
                return
            }

        val taxonomyAssetRevisionsPerAsset =
            taxonomyAssets
                .mapNotNull { taxonomyAsset ->
                    toTaxonomyAssetRevision(taxonomyAsset, taxonomySubsectorsPerVersion)
                }.groupBy { it.asset }

        val taxonomyAssetRevisionsLatest =
            taxonomyAssetRevisionsPerAsset.values.mapNotNull { assetRevisions ->
                assetRevisions.maxByOrNull { it.updatedAtTaxonomyVersion }
            }

        // collect Map<Version, Map<AssetName, AssetRevision>>
        val taxonomyAssetRevisionsPerAssetNamePerVersion =
            convertToTaxonomyAssetRevisionAndMap(
                taxonomyAssetRevisionsPerAsset,
                taxonomyMetadataPerVersion,
            )

        this.taxonomyMetadataPerVersion = taxonomyMetadataPerVersion
        this.taxonomyAssetRevisionsLatest = taxonomyAssetRevisionsLatest
        this.taxonomyAssetRevisionsPerAsset = taxonomyAssetRevisionsPerAsset
        this.taxonomyAssetRevisionsPerAssetNamePerVersion = taxonomyAssetRevisionsPerAssetNamePerVersion
        this.latestTaxonomyVersion = taxonomyVersionsMappedOnVersion.keys.last()

        log.info(
            "Taxonomy data has been updated. Versions count: ${taxonomyMetadataPerVersion.size}. " +
                "Latest version: ${this.latestTaxonomyVersion}.",
        )
    }

    private fun convertToTaxonomyAssetRevisionAndMap(
        taxonomyAssetsMap: Map<String, List<TaxonomyAssetRevision>>,
        taxonomyMetadataPerVersion: TreeMap<TaxonomyVersion, TaxonomyMetadata>,
    ): Map<TaxonomyVersion, HashMap<String, TaxonomyAssetRevision>> {
        val taxonomyAssetRevisionsPerVersionPerAssetName =
            taxonomyAssetsMap.entries
                .associate { (assetName, taxonomyAssets) ->
                    val taxonomyAssetRevisionsMappedOnVersion = taxonomyAssets.associateByTo(TreeMap()) { it.taxonomyVersion }

                    assetName to
                        taxonomyMetadataPerVersion.entries
                            .mapNotNull { (taxonomyVersion, taxonomyVersionSpec) ->
                                val taxonomyAsset = taxonomyAssetRevisionsMappedOnVersion[taxonomyVersion]
                                if (taxonomyAsset != null) {
                                    taxonomyVersion to taxonomyAsset
                                } else {
                                    val floorEntry = taxonomyAssetRevisionsMappedOnVersion.floorEntry(taxonomyVersion)
                                    if (floorEntry == null) {
                                        null
                                    } else {
                                        val (_, floorTaxonomyAsset) = floorEntry
                                        val classificationEndTime = floorTaxonomyAsset.classificationEndTime
                                        if (classificationEndTime == null ||
                                            !classificationEndTime.isBefore(taxonomyVersionSpec.taxonomyStartTime)
                                        ) {
                                            taxonomyVersion to floorTaxonomyAsset
                                        } else {
                                            null
                                        }
                                    }
                                }
                            }.associate { it }
                }

        return HashMap<TaxonomyVersion, HashMap<String, TaxonomyAssetRevision>>().also { map ->
            taxonomyAssetRevisionsPerVersionPerAssetName.entries.forEach { (asset, taxonomyAssetsPerVersion) ->
                taxonomyAssetsPerVersion.entries.forEach { (taxonomyVersion, taxonomyAsset) ->
                    map.computeIfAbsent(taxonomyVersion) { HashMap() }[asset] = taxonomyAsset
                }
            }
        }
    }

    private fun toTaxonomyAssetRevision(
        taxonomyAsset: TaxonomySpecAsset,
        taxonomySubsectorsPerVersion:
            Map<TaxonomyVersion, Map<String, Triple<TaxonomySpecVersionClass, TaxonomySpecVersionSector, TaxonomySpecVersionSubsector>>>?,
    ): TaxonomyAssetRevision? {
        val taxonomyVersion = TaxonomyVersion.fromString(taxonomyAsset.updatedAtVersion)
        return taxonomyVersion?.let {
            val subsectorInfo = taxonomySubsectorsPerVersion?.get(taxonomyVersion)?.get(taxonomyAsset.subsectorId)
            if (subsectorInfo == null) {
                log.warn("Subsector '${taxonomyAsset.subsectorId}' doesn't exist in version $taxonomyVersion.")
            }
            subsectorInfo?.let {
                val (aClass, sector, subsector) = subsectorInfo
                TaxonomyAssetRevision(
                    asset = taxonomyAsset.asset.lowercase(),
                    fullName = taxonomyAsset.name,
                    taxonomyVersion = taxonomyVersion,
                    updatedAtTaxonomyVersion = taxonomyVersion,
                    classificationStartTime = toInstant(taxonomyAsset.classificationStartDate),
                    classificationEndTime = taxonomyAsset.classificationEndDate?.let { date -> toInstant(date) },
                    classId = taxonomyAsset.classId,
                    propertyClass = aClass.name,
                    sectorId = taxonomyAsset.sectorId,
                    sector = sector.name,
                    subsectorId = taxonomyAsset.subsectorId,
                    subsector = subsector.name,
                )
            }
        }
    }

    private fun convertToTaxonomyMetadata(
        taxonomyVersionsMappedOnVersion: Map<TaxonomyVersion, TaxonomySpecVersion>,
        taxonomySubsectorsPerVersion:
            Map<TaxonomyVersion, Map<String, Triple<TaxonomySpecVersionClass, TaxonomySpecVersionSector, TaxonomySpecVersionSubsector>>>,
    ): FunctionResult<String, TreeMap<TaxonomyVersion, TaxonomyMetadata>> {
        val taxonomyMetadataMap =
            taxonomyVersionsMappedOnVersion.entries
                .takeIf { it.isNotEmpty() }
                ?.associateTo(TreeMap()) { (taxonomyVersion, taxonomyVersionSpec) ->
                    taxonomyVersion to
                        run {
                            val subsectors = taxonomySubsectorsPerVersion[taxonomyVersion] ?: emptyMap()
                            TaxonomyMetadata(
                                taxonomyVersion = taxonomyVersion,
                                taxonomyStartTime = toInstant(taxonomyVersionSpec.taxonomyStartDate),
                                taxonomyEndTime = taxonomyVersionSpec.taxonomyEndDate?.let { toInstant(it) },
                                subsectors =
                                    subsectors
                                        .map { (_, value) ->
                                            value.toTaxonomyIndustryMetadata()
                                        }.sortedBy { it.subsectorId },
                            )
                        }
                } ?: TreeMap()

        // Taxonomy classification validation
        taxonomyMetadataMap.values.zipWithNext { current, next ->
            val taxonomyEndTime = current.taxonomyEndTime
            if (taxonomyEndTime == null) {
                if (!current.taxonomyStartTime.isBefore(next.taxonomyStartTime)) {
                    return FunctionResult.Failure(
                        "Classification start time of version '${current.taxonomyVersion}' " +
                            "is after or equal to the classification start time of subsequent version '${next.taxonomyVersion}'.",
                    )
                } else {
                    log.warn(
                        "Classification end time for version '${current.taxonomyVersion}' is not set. " +
                            "Setting it to the classification start time of the next version '${next.taxonomyVersion}'.",
                    )
                    current.taxonomyEndTime = next.taxonomyStartTime
                }
            } else if (taxonomyEndTime.isAfter(next.taxonomyStartTime)) {
                return FunctionResult.Failure(
                    "Classification end time of taxonomy version '${current.taxonomyVersion}' " +
                        "is after the classification start time of subsequent version '${next.taxonomyVersion}'.",
                )
            } else if (!current.taxonomyStartTime.isBefore(taxonomyEndTime)) {
                return FunctionResult.Failure(
                    "Classification start time of version '${current.taxonomyVersion}' " +
                        "is after or equal to its classification end time.",
                )
            }
        }
        return taxonomyMetadataMap.toSuccess()
    }

    private fun getSubsectorsInfo(
        taxonomyVersionsMappedOnVersion: TreeMap<TaxonomyVersion, TaxonomySpecVersion>,
    ): Map<TaxonomyVersion, Map<String, Triple<TaxonomySpecVersionClass, TaxonomySpecVersionSector, TaxonomySpecVersionSubsector>>>? =
        taxonomyVersionsMappedOnVersion.entries
            .takeIf { it.isNotEmpty() }
            ?.associate { (taxonomyVersion, taxonomyVersionSpec) ->
                taxonomyVersion to
                    run {
                        taxonomyVersionSpec.classes
                            .flatMap { aClass ->
                                aClass.sectors.map { sector -> aClass to sector }
                            }.flatMap { (aClass, sector) ->
                                sector.subsectors.map { subsector -> Triple(aClass, sector, subsector) }
                            }.associate { (aClass, sector, subsector) -> subsector.id to Triple(aClass, sector, subsector) }
                    }
            }

    private fun readTaxonomySpecVersions(targetDirectory: Path): TreeMap<TaxonomyVersion, TaxonomySpecVersion>? {
        val versionsFilePath = targetDirectory.resolve("taxonomy_versions.json")
        val versionsFileAbsolutePathName = versionsFilePath.toAbsolutePath().name
        val versionsFile =
            versionsFilePath.takeIf { it.exists() } ?: run {
                log.error("File '$versionsFileAbsolutePathName' does not exist.")
                return null
            }

        val taxonomyVersionsSpec =
            try {
                objectMapper.readValue<TaxonomySpecVersions>(Files.readString(versionsFile))
            } catch (e: Exception) {
                log.error("Failed to parse taxonomy versions from '$versionsFileAbsolutePathName' file.", e)
                return null
            }
        if (taxonomyVersionsSpec.versions.isEmpty()) {
            log.warn("Found no taxonomy versions in '$versionsFileAbsolutePathName' file.")
            return null
        }

        return taxonomyVersionsSpec.versions
            .mapNotNull { item ->
                TaxonomyVersion.fromString(item.taxonomyVersion)?.let { it to item }
            }.associateTo(TreeMap()) { it }
    }

    private fun readTaxonomySpecAssets(targetDirectory: Path): List<TaxonomySpecAsset>? {
        val assetsFilePath = targetDirectory.resolve("taxonomy_assets.json")
        val assetsFileAbsolutePathName = assetsFilePath.toAbsolutePath().name
        val assetsFile =
            assetsFilePath.takeIf { it.exists() } ?: run {
                log.error("File '$assetsFileAbsolutePathName' does not exist.")
                return null
            }
        val taxonomyAssetsSpec =
            try {
                objectMapper.readValue<TaxonomySpecAssets>(Files.readString(assetsFile))
            } catch (e: Exception) {
                log.error("Failed to parse taxonomy assets from '$assetsFileAbsolutePathName' file.", e)
                return null
            }
        return taxonomyAssetsSpec.assets
    }

    private fun Triple<
        TaxonomySpecVersionClass,
        TaxonomySpecVersionSector,
        TaxonomySpecVersionSubsector,
    >.toTaxonomyIndustryMetadata(): TaxonomyIndustryMetadata {
        val (aClass, sector, subsector) = this
        return TaxonomyIndustryMetadata(
            classId = aClass.id,
            propertyClass = aClass.name,
            sectorId = sector.id,
            sector = sector.name,
            subsectorId = subsector.id,
            subsector = subsector.name,
        )
    }

    private fun toInstant(value: String): Instant =
        TimeUtils.parseTimeAndRound(value, ZoneOffset.UTC, roundToLatest = false).getOrElse {
            error(it)
        }
}

data class TaxonomyVersion(
    val component1: Int,
    val component2: Int,
) : Comparable<TaxonomyVersion> {
    companion object {
        val LATEST = TaxonomyVersion(Integer.MAX_VALUE, Integer.MAX_VALUE)

        fun fromString(value: String): TaxonomyVersion? {
            val values = value.split(".")
            return values.takeIf { it.size == 2 }?.let {
                val component1 = it[0].toIntOrNull()
                val component2 = it[1].toIntOrNull()
                if (component1 != null && component2 != null) {
                    TaxonomyVersion(component1, component2)
                } else {
                    null
                }
            }
        }
    }

    override fun compareTo(other: TaxonomyVersion): Int = compareValuesBy(this, other, { it.component1 }, { it.component2 })

    override fun toString(): String = "$component1.$component2"
}
