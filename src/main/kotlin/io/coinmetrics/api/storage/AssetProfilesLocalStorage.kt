package io.coinmetrics.api.storage

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import io.coinmetrics.api.models.AssetProfileInfo
import org.slf4j.LoggerFactory
import java.io.File
import java.nio.file.Path

class AssetProfilesLocalStorage(
    val objectMapper: ObjectMapper,
) : LocalStorage {
    companion object {
        private val log = LoggerFactory.getLogger(AssetProfilesLocalStorage::class.java)
    }

    @Volatile
    var data: AssetProfilesData = AssetProfilesData()
        private set

    override fun refresh(dataDir: Path) {
        val assetProfilesDataFile = dataDir.resolve("asset_profiles.json").toFile()
        val profiles = readAssetProfilesData(assetProfilesDataFile) ?: return

        data =
            AssetProfilesData(
                profiles = profiles.sortedBy { it.asset },
                profilesPerAsset = profiles.associateBy { it.asset.lowercase() },
                profilesPerFullName = profiles.associateBy { it.fullName.lowercase() },
            )
    }

    private fun readAssetProfilesData(assetProfilesDataFile: File) =
        try {
            objectMapper.readValue<List<AssetProfileInfo>>(assetProfilesDataFile)
        } catch (e: Exception) {
            log.error("Could not read asset profiles data file: ${assetProfilesDataFile.path}", e)
            null
        }

    data class AssetProfilesData(
        val profiles: List<AssetProfileInfo> = emptyList(),
        val profilesPerAsset: Map<String, AssetProfileInfo> = emptyMap(),
        val profilesPerFullName: Map<String, AssetProfileInfo> = emptyMap(),
    )
}
