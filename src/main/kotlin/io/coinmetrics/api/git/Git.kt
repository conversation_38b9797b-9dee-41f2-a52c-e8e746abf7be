package io.coinmetrics.api.git

import java.nio.file.Path

object Git {
    fun clone(
        path: Path,
        uri: String,
    ) {
        exec(path, listOf("git", "clone", uri))
    }

    fun checkout(
        path: Path,
        commitHash: String,
    ) {
        exec(path, listOf("git", "checkout", commitHash))
    }

    fun getCurrentCommitHash(path: Path): String = exec(path, listOf("git", "rev-parse", "HEAD"))

    fun fetch(path: Path) {
        exec(path, listOf("git", "fetch"))
    }

    fun reset(
        path: Path,
        commitHash: String,
    ) {
        exec(path, listOf("git", "reset", "--hard", commitHash))
    }

    private fun exec(
        path: Path,
        command: List<String>,
    ): String {
        val builder = ProcessBuilder(command).directory(path.toFile())
        val process = builder.start()

        var stdoutOutput: String? = null
        var stderrOutput: String? = null

        process.inputStream.use { stdout ->
            process.errorStream.use { stderr ->
                val stdoutArray = stdout.readAllBytes()!!
                val stderrArray = stderr.readAllBytes()!!
                if (stdoutArray.isNotEmpty()) {
                    stdoutOutput = String(stdoutArray)
                }
                if (stderrArray.isNotEmpty()) {
                    stderrOutput = String(stderrArray)
                }
            }
        }

        val returnCode = process.waitFor()
        if (returnCode != 0) {
            val output = (stderrOutput ?: stdoutOutput)?.trim()
            throw IllegalStateException(
                "Can't execute command '${command.joinToString(separator = " ")}' (code $returnCode). Output:\n$output",
            )
        }

        return (stdoutOutput ?: stderrOutput)?.trim() ?: ""
    }
}
