package io.coinmetrics.api

import io.coinmetrics.api.endpoints.DataSourcesEndpoint
import io.coinmetrics.api.endpoints.Endpoint
import io.coinmetrics.api.modules.ApiModule
import io.coinmetrics.api.modules.common.CommonModule
import io.coinmetrics.api.utils.TrafficShaper
import io.coinmetrics.api.utils.mdc
import io.coinmetrics.httpserver.HttpRequest
import io.coinmetrics.httpserver.HttpRequestHandler
import io.coinmetrics.httpserver.HttpResponse
import io.coinmetrics.httpserver.HttpServer
import io.coinmetrics.httpserver.NonOkResponseHandler
import io.coinmetrics.httpserver.UpgradeToWebSocketResponse
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.currentCoroutineContext
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.slf4j.MDCContext
import kotlinx.coroutines.withContext
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import java.util.concurrent.atomic.AtomicBoolean
import kotlin.coroutines.EmptyCoroutineContext

class HttpRequestHandlerImpl(
    val common: CommonModule,
    private val modules: List<ApiModule>,
) : HttpRequestHandler,
    NonOkResponseHandler {
    private val log: Logger = LoggerFactory.getLogger(HttpRequestHandlerImpl::class.java)

    private val trafficShaper =
        TrafficShaper(
            instanceBandwidthBytesPerSec = common.config.totalBandwidthBytesPerSec,
        )

    private val defaultRouter = Router(mapOf("/v4/datasources" to DataSourcesEndpoint(modules)))

    private fun resolveEndpoint(httpRequest: HttpRequest): Endpoint<*>? {
        modules.forEach { module ->
            val endpoint = module.resolveEndpoint(httpRequest)
            if (endpoint != null) return endpoint
        }
        return defaultRouter.endpoint(httpRequest)
    }

    override fun getMDC(httpRequest: HttpRequest) = httpRequest.mdc

    override suspend fun handle(
        httpRequest: HttpRequest,
        httpServer: HttpServer,
    ): HttpResponse {
        if (httpRequest.method == "OPTIONS") {
            return handlePreflightRequest()
        }
        if (httpRequest.isCommunity()) {
            httpRequest.forcedApiKey = common.config.communityApiKey
        }
        val apiKey = httpRequest.effectiveApiKey() ?: ""
        val processingDispatcher = httpServer.newProcessingDispatcher()
        return try {
            val endpoint = resolveEndpoint(httpRequest)
            // it monitors request count before response is ready
            common.monitoring.requestsTotal
                .labelValues(httpRequest.pathExpression, apiKey)
                .inc()

            if (endpoint == null) {
                val response = Response.errorResponse<Unit>(ApiError.NotFound)
                convertResponseToHttpResponse(response, httpRequest, processingDispatcher)
            } else {
                common.monitoring.requestsQueuePending
                    .labelValues(httpRequest.pathExpression, apiKey)
                    .inc()
                val activeRequestCountLimitKey = if (httpRequest.forcedApiKey == null) apiKey else httpRequest.clientIp
                val queueTime =
                    common.monitoring.requestsQueueTime
                        .labelValues(httpRequest.pathExpression, apiKey)
                        .startTimer()

                // A releaseFun callback is needed to release resources after the response is fully sent to client or failed.
                // It can happen even after returning from this method.
                common.activeRequestCountLimitService.execute(activeRequestCountLimitKey, action = { releaseFun ->
                    common.monitoring.requestsQueuePending
                        .labelValues(httpRequest.pathExpression, apiKey)
                        .dec()
                    queueTime.observeDuration()
                    common.monitoring.requestsActive
                        .labelValues(httpRequest.pathExpression, apiKey)
                        .inc()
                    val timer =
                        common.monitoring.requestDuration
                            .labelValues(httpRequest.pathExpression, apiKey)
                            .startTimer()
                    val releaseFunWrapperExecuted = AtomicBoolean()
                    val releaseFunWrapper = {
                        if (releaseFunWrapperExecuted.compareAndSet(false, true)) {
                            common.monitoring.requestsActive
                                .labelValues(httpRequest.pathExpression, apiKey)
                                .dec()
                            releaseFun.invoke()
                        }
                    }
                    try {
                        withContext(processingDispatcher) {
                            val response = endpoint.handle(httpRequest)
                            convertResponseToHttpResponse(response, httpRequest, processingDispatcher)
                                .setUpTrafficShapingAndResourceReleasers(
                                    httpRequest = httpRequest,
                                    activeRequestCountLimitKey = activeRequestCountLimitKey,
                                    apiKey = apiKey,
                                    onFirstChunkSent = { timer.observeDuration() },
                                    onLastChunkSent = releaseFunWrapper,
                                )
                        }
                    } catch (e: Exception) {
                        timer.observeDuration()
                        releaseFunWrapper.invoke()
                        throw e
                    }
                }, onQueueOverflow = { limit ->
                    log.warn("HTTP request queue overflow (limit of {} was reached)", limit)
                    common.monitoring.requestsQueueOverflow
                        .labelValues(apiKey)
                        .inc()
                    common.monitoring.requestsQueuePending
                        .labelValues(httpRequest.pathExpression, apiKey)
                        .dec()
                    queueTime.observeDuration()
                    val response = Response.errorResponse<Unit>(ApiError.TooManyRequests)
                    convertResponseToHttpResponse(response, httpRequest, processingDispatcher)
                }, onCancellationDuringWaitingForAPermit = {
                    common.monitoring.requestsQueuePending
                        .labelValues(httpRequest.pathExpression, apiKey)
                        .dec()
                    queueTime.observeDuration()
                })
            }
        } catch (e: CancellationException) {
            throw e
        } catch (e: Throwable) {
            log.error("Internal server error", e)
            val response = Response.errorResponse<Unit>(ApiError.OperationFailed)
            convertResponseToHttpResponse(response, httpRequest, processingDispatcher)
        }
    }

    /**
     * @param onFirstChunkSent is called even for empty responses.
     * @param onLastChunkSent is called even for empty responses.
     */
    private fun HttpResponse.setUpTrafficShapingAndResourceReleasers(
        httpRequest: HttpRequest,
        activeRequestCountLimitKey: String,
        apiKey: String,
        onFirstChunkSent: () -> Unit,
        onLastChunkSent: () -> Unit,
    ): HttpResponse {
        val chunkedStream = chunkedStream
        return if (chunkedStream != null) {
            val onDelayMonitoring = { delayMs: Long ->
                // todo: later, move monitoring out of the hot loop here
                common.monitoring.trafficDelays
                    .labelValues(apiKey)
                    .inc(delayMs.toDouble() / 1000)
            }
            val newChunkedStream =
                trafficShaper
                    .shape(chunkedStream, activeRequestCountLimitKey, onDelayMonitoring)
                    .onEach { bytes ->
                        // todo: later, move monitoring out of the hot loop here
                        common.monitoring.trafficSentBytes
                            .labelValues(httpRequest.pathExpression, apiKey)
                            .inc(bytes.size.toDouble())
                    }
            var firstChunkSent = false
            // it can't be an instance of WebSocket handshake, so it's safe to create a new instance of HttpResponse here
            HttpResponse(
                status = status,
                contentType = contentType,
                headers = headers,
                chunkedStream =
                    newChunkedStream
                        .onEach {
                            if (!firstChunkSent) {
                                firstChunkSent = true
                                onFirstChunkSent.invoke()
                            }
                        }.onCompletion {
                            if (!firstChunkSent) onFirstChunkSent.invoke()
                            onLastChunkSent.invoke()
                        },
            )
        } else {
            onFirstChunkSent.invoke()
            onLastChunkSent.invoke()
            this
        }
    }

    private fun handlePreflightRequest(): HttpResponse =
        HttpResponse(
            204,
            headers =
                listOf(
                    "Access-Control-Allow-Origin" to "*",
                    "Access-Control-Allow-Methods" to "GET, OPTIONS",
                    "Access-Control-Allow-Headers" to "*",
                    // = 24 hours
                    "Access-Control-Max-Age" to "86400",
                ),
        )

    override fun onNonOkResponse(
        httpRequest: HttpRequest?,
        statusCode: Int,
    ) {
        val apiKey =
            if (httpRequest == null) {
                ""
            } else {
                httpRequest.forcedApiKey?.let {
                    if (httpRequest.isCommunity()) {
                        common.config.communityApiKey
                    } else {
                        null
                    }
                } ?: httpRequest.queryParameters["api_key"] ?: ""
            }

        common.monitoring.nonOkResponses
            .labelValues(statusCode.toString(), apiKey)
            .inc()
    }

    /**
     * @param processingDispatcher is used to serialize a response to JSON
     */
    private suspend fun convertResponseToHttpResponse(
        response: Response<*>,
        httpRequest: HttpRequest,
        processingDispatcher: CoroutineDispatcher,
    ): HttpResponse =
        if (response is Response.ErrorObjectResponse && httpRequest.webSocketUpgradeRequest) {
            // if error happens before websocket handshake we should do handshake now and send error via WebSocket frame
            UpgradeToWebSocketResponse { webSocket ->
                // Error responses are small and rare, no need to move this to processingDispatcher.
                val msg = response.toWebSocketTextMessage(httpRequest)
                webSocket.sendTextMessageAsync(msg)
                webSocket.closeNow(exception = RuntimeException("An error happened during a WebSocket handshake: '$msg'."))
            }
        } else {
            withContext(processingDispatcher) {
                val mdcContext = currentCoroutineContext()[MDCContext] ?: EmptyCoroutineContext
                response.toHttpResponse(httpRequest, processingDispatcher + mdcContext)
            }
        }
}

fun HttpRequest.isCommunity(): Boolean = headers["host"]?.startsWith("community-api") ?: false

fun HttpRequest.effectiveApiKey(): String? = forcedApiKey ?: queryParameters["api_key"]
