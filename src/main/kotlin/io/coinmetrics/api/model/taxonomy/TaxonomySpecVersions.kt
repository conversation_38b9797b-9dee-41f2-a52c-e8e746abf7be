package io.coinmetrics.api.model.taxonomy

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty

@JsonIgnoreProperties(ignoreUnknown = true)
data class TaxonomySpecVersions(
    val versions: List<TaxonomySpecVersion>,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class TaxonomySpecVersion(
    @JsonProperty("taxonomy_version")
    val taxonomyVersion: String,
    @JsonProperty("taxonomy_start_date")
    val taxonomyStartDate: String,
    @JsonProperty("taxonomy_end_date")
    val taxonomyEndDate: String?,
    val classes: List<TaxonomySpecVersionClass>,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class TaxonomySpecVersionClass(
    val id: String,
    val name: String,
    val sectors: List<TaxonomySpecVersionSector>,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class TaxonomySpecVersionSector(
    val id: String,
    val name: String,
    val subsectors: List<TaxonomySpecVersionSubsector>,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class TaxonomySpecVersionSubsector(
    val id: String,
    val name: String,
)
