package io.coinmetrics.api.model.taxonomy

import com.fasterxml.jackson.annotation.JsonFormat
import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.databind.annotation.JsonSerialize
import io.coinmetrics.api.conversion.TaxonomyVersionSerializer
import io.coinmetrics.api.storage.TaxonomyVersion
import java.time.Instant

data class TaxonomyAssetRevision(
    val asset: String,
    @JsonProperty("full_name")
    val fullName: String,
    @JsonProperty("taxonomy_version")
    @JsonSerialize(using = TaxonomyVersionSerializer::class)
    val taxonomyVersion: TaxonomyVersion,
    @JsonProperty("updated_at_taxonomy_version")
    @JsonSerialize(using = TaxonomyVersionSerializer::class)
    val updatedAtTaxonomyVersion: TaxonomyVersion,
    @JsonProperty("classification_start_time")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "UTC")
    val classificationStartTime: Instant,
    @JsonProperty("classification_end_time")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "UTC")
    val classificationEndTime: Instant? = null,
    @JsonProperty("class_id")
    val classId: String,
    @JsonProperty("class")
    val propertyClass: String,
    @JsonProperty("subsector_id")
    val subsectorId: String,
    val subsector: String,
    @JsonProperty("sector_id")
    val sectorId: String,
    val sector: String,
)
