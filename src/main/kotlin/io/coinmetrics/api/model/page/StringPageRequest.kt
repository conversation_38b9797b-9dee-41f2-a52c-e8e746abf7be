package io.coinmetrics.api.model.page

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.badNextPageToken
import io.coinmetrics.api.models.PagingFrom
import io.coinmetrics.api.toFailure
import io.coinmetrics.api.toSuccess
import io.coinmetrics.api.utils.paging.PageToken

data class StringPageRequest(
    val entity: String?,
    val pageSize: Int,
    val pagingFrom: PagingFrom,
) {
    companion object {
        fun create(
            nextPageToken: String?,
            pageSize: Int,
            pagingFrom: PagingFrom,
        ): FunctionResult<ApiError, StringPageRequest> {
            val entity =
                nextPageToken?.let {
                    PageToken.StringPageToken
                        .parseCatching(it) {
                            return badNextPageToken().toFailure()
                        }?.string
                }
            return StringPageRequest(entity, pageSize, pagingFrom).toSuccess()
        }
    }
}
