package io.coinmetrics.api.ams

import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import io.coinmetrics.api.monitoring.AmsMonitoring
import io.coinmetrics.api.utils.ThrottledLogger
import io.coinmetrics.api.utils.Utils
import kotlinx.coroutines.future.await
import org.slf4j.LoggerFactory
import java.net.ConnectException
import java.net.URI
import java.net.http.HttpClient
import java.net.http.HttpRequest
import java.net.http.HttpResponse
import java.time.Duration
import java.util.concurrent.CancellationException
import java.util.concurrent.CompletableFuture
import java.util.concurrent.Executor
import java.util.concurrent.TimeUnit
import kotlin.math.round

/**
 * Asynchronous AMS client.
 *
 * Response statuses:
 * 200 - success
 * 400 - bad parameters
 * 401 - invalid or expired api_key
 * 429 - too many requests
 * 403 - forbidden
 * 500 - internal server error
 */
class AmsClient(
    host: String = "127.0.0.1",
    port: Int = 7771,
    version: String = "1",
    private val statusUri: String = "http://$host:$port/status",
    private val origin: String = "http://$host:$port/v$version",
    connectionTimeoutMs: Long = TimeUnit.SECONDS.toMillis(10),
    private val requestTimeoutMs: Long = TimeUnit.SECONDS.toMillis(10),
    executor: Executor,
    private val monitoring: AmsMonitoring,
) {
    companion object {
        private val log = LoggerFactory.getLogger(AmsClient::class.java)
        private val amsLogger = ThrottledLogger(log)
        private val objectMapper = ObjectMapper()
        private const val SLOW_CHECK_QUERY_LOGGING_MS = 300
        private val emptyPublisher = HttpRequest.BodyPublishers.noBody()
        private val stringBodyHandler = HttpResponse.BodyHandlers.ofString()
        private val byteArrayBodyHandler = HttpResponse.BodyHandlers.ofByteArray()
    }

    private val client =
        HttpClient
            .newBuilder()
            .connectTimeout(Duration.ofMillis(connectionTimeoutMs))
            .executor(executor)
            .version(HttpClient.Version.HTTP_2)
            .build()

    // limit the number of parallel requests
//    private val semaphore = Semaphore(10_000)
    // we switched to the http2 pipelining instead

    suspend fun status(timeMs: Long? = null): StatusResult {
        val requestBuilder =
            HttpRequest
                .newBuilder(URI.create(statusUri))
                .GET()
                .timeout(Duration.ofMillis(requestTimeoutMs))

        if (timeMs != null) {
            requestBuilder.header("X-Request-Timestamp", timeMs.toString())
        }

        val timer = monitoring.amsRequestDuration.labelValues("status").startTimer()

        val request = requestBuilder.build()
        val response =
            execute(
                block = {
                    client.sendAsync(request, byteArrayBodyHandler).whenComplete { _, _ ->
                        timer.observeDuration()
                    }
                },
                logOnException = { e -> log.error("Status AMS request failed: {}", request, e) },
                onConnectException = { e ->
                    return StatusResult.Failed(-1, "Status AMS request failed with the following reason: $e")
                },
                onException = { return StatusResult.Failed(500, "AMS request failed.") },
            )

        return when (response.statusCode()) {
            200 -> {
                StatusResult.Success
            }

            else -> {
                try {
                    StatusResult.Failed(response.statusCode(), response.body().decodeToString())
                } catch (e: Exception) {
                    StatusResult.Failed(response.statusCode(), response.body().decodeToString())
                }
            }
        }
    }

    suspend fun discovery(
        time: Long? = null,
        apiKey: String? = null,
        regularPackageName: String,
        target: String,
        filters: Map<String, String> = emptyMap(),
        ignoreUnsupportedValues: Boolean = false,
        ignoreDiscoveryScript: Boolean = false,
    ): DiscoveryResult {
        val queryParams = HashMap<String, String>()
        queryParams["package"] = regularPackageName
        queryParams["target"] = target
        if (ignoreUnsupportedValues) queryParams["ignore_unsupported_values"] = "true"
        if (ignoreDiscoveryScript) queryParams["ignore_discovery_script"] = "true"
        if (apiKey != null) {
            queryParams["api_key"] = apiKey
        }
        queryParams.putAll(filters)

        val request =
            get("/discovery?${queryParams.map { it.key + "=" + Utils.encodeURIComponent(it.value) }.joinToString(separator = "&")}", time)
        val timer = monitoring.amsRequestDuration.labelValues("discovery").startTimer()

        val response =
            execute(
                block = {
                    client.sendAsync(request, byteArrayBodyHandler).whenComplete { _, _ ->
                        timer.observeDuration()
                    }
                },
                logOnException = { e -> log.error("Discovery AMS request failed: {}", request, e) },
                onException = { return DiscoveryResult.Failed(500, "AMS request failed.") },
            )

        return when (response.statusCode()) {
            200 -> {
                // parse response
                val array = objectMapper.readTree(response.body())
                val listOfArrays =
                    array.map { row ->
                        row.map { it.toTextOrNullString() }.toTypedArray()
                    }
                DiscoveryResult.Success(listOfArrays)
            }

            else -> {
                // something wrong
                try {
                    val json = objectMapper.readTree(response.body())
                    val errorNode = json.path("error")

                    if (errorNode.path("type").textValue() == "bad_parameter") {
                        DiscoveryResult.BadParameter(
                            errorNode.path("parameter_name").textValue(),
                            errorNode.path("message").textValue(),
                        )
                    } else {
                        DiscoveryResult.Failed(response.statusCode(), response.body().decodeToString())
                    }
                } catch (e: Exception) {
                    DiscoveryResult.Failed(response.statusCode(), response.body().decodeToString())
                }
            }
        }
    }

    suspend fun check(
        time: Long? = null,
        apiKey: String,
        regularPackageName: String? = null,
        parameters: Map<String, String> = emptyMap(),
    ): CheckResult {
        if (parameters.values.any { it.contains(',') }) throw IllegalArgumentException("Arrays are not supported.")
        val queryParams = HashMap<String, String>()
        queryParams["api_key"] = apiKey
        if (regularPackageName != null) queryParams["package"] = regularPackageName
        queryParams.putAll(parameters)

        val request =
            get("/check?${queryParams.map { it.key + "=" + Utils.encodeURIComponent(it.value) }.joinToString(separator = "&")}", time)

        val timer = monitoring.amsRequestDuration.labelValues("check").startTimer()

        val response =
            execute(
                block = {
                    client.sendAsync(request, stringBodyHandler).whenComplete { _, _ ->
                        val durationMs = round(timer.observeDuration() * 1000).toInt()
                        if (durationMs >= SLOW_CHECK_QUERY_LOGGING_MS) {
                            amsLogger.log { warn("Slow AMS check query took {} ms, query: {}", durationMs, request.uri()) }
                        }
                    }
                },
                logOnException = { e -> log.error("Check AMS request failed: {}", request, e) },
                onException = { return CheckResult.Failed(500, "AMS request failed.") },
            )

        return when (response.statusCode()) {
            200 -> {
                CheckResult.Success()
            }

            201 -> {
                // adjusted
                val node = objectMapper.readTree(response.body())
                val adjustedParameters =
                    node
                        .fields()
                        .asSequence()
                        .associateTo(HashMap()) { entry ->
                            val longArray = entry.value.map { it.longValue() }.toLongArray()
                            entry.key to longArray
                        }
                CheckResult.Adjusted(adjustedParameters)
            }

            else -> {
                // something wrong
                try {
                    val json = objectMapper.readTree(response.body())
                    val errorNode = json.path("error")

                    if (errorNode.path("type").textValue() == "bad_parameter") {
                        CheckResult.BadParameter(
                            errorNode.path("parameter_name").textValue(),
                            errorNode.path("message").textValue(),
                        )
                    } else {
                        CheckResult.Failed(response.statusCode(), response.body())
                    }
                } catch (e: Exception) {
                    CheckResult.Failed(response.statusCode(), response.body())
                }
            }
        }
    }

    suspend fun checkRateLimits(
        apiKey: String,
        endpoint: String,
        ip: String,
        timeSec: Long,
    ): CheckLimitsResult {
        val queryParams = ArrayList<Pair<String, String>>()
        queryParams.add("api_key" to apiKey)
        queryParams.add("endpoint" to endpoint)
        queryParams.add("ip" to ip)

        val request =
            post(
                "/rate-limits?${
                    queryParams.joinToString(separator = "&") {
                        it.first + "=" + Utils.encodeURIComponent(it.second)
                    }
                }",
                timeSec * 1000,
            )

        val timer = monitoring.amsRequestDuration.labelValues("rate-limits").startTimer()

        val response =
            execute(
                block = {
                    client.sendAsync(request, stringBodyHandler).whenComplete { _, _ ->
                        val durationMs = round(timer.observeDuration() * 1000).toInt()
                        if (durationMs >= SLOW_CHECK_QUERY_LOGGING_MS) {
                            amsLogger.log { warn("Slow AMS check rate limits query took {} ms, query: {}", durationMs, request.uri()) }
                        }
                    }
                },
                logOnException = { e -> log.error("Check rate limits AMS request failed: {}", request, e) },
                onException = { return CheckLimitsResult.Failed(500, "AMS request failed.", emptyList()) },
            )

        val xRateHeaders = ArrayList<Pair<String, String>>(4)
        xRateHeaders.add("access-control-expose-headers" to "x-ratelimit-limit, x-ratelimit-remaining, x-ratelimit-reset")
        var apiKeyType: String? = null
        response.headers().map().forEach { (key, value) ->
            if (key.startsWith("x-rate")) {
                xRateHeaders.add(key to value.first())
            } else if (key == "x-key-type") {
                apiKeyType = value.first()
            }
        }

        return when (response.statusCode()) {
            200 -> CheckLimitsResult.Success(xRateHeaders, apiKeyType)
            429 -> CheckLimitsResult.TooManyRequests(xRateHeaders)
            else -> {
                // something wrong
                CheckLimitsResult.Failed(response.statusCode(), response.body(), xRateHeaders)
            }
        }
    }

    private fun get(
        relativeUri: String,
        time: Long? = null,
    ): HttpRequest {
        val request =
            HttpRequest
                .newBuilder(URI.create(origin + relativeUri))
                .GET()
                .timeout(Duration.ofMillis(requestTimeoutMs))

        if (time != null) {
            request.header("X-Request-Timestamp", time.toString())
        }
        return request.build()
    }

    private fun post(
        relativeUri: String,
        timeMs: Long? = null,
    ): HttpRequest {
        val request =
            HttpRequest
                .newBuilder(URI.create(origin + relativeUri))
                .POST(emptyPublisher)
                .timeout(Duration.ofMillis(requestTimeoutMs))

        if (timeMs != null) {
            request.header("X-Request-Timestamp", timeMs.toString())
        }
        return request.build()
    }

    private suspend inline fun <T> execute(
        block: () -> CompletableFuture<HttpResponse<T>>,
        logOnException: (Exception) -> Unit,
        onException: (Exception) -> HttpResponse<T>,
    ): HttpResponse<T> = execute(block, logOnException, onException, onConnectException = onException)

    private suspend inline fun <T> execute(
        block: () -> CompletableFuture<HttpResponse<T>>,
        logOnException: (Exception) -> Unit,
        onException: (Exception) -> HttpResponse<T>,
        onConnectException: (Exception) -> HttpResponse<T>,
    ): HttpResponse<T> =
        try {
            // semaphore.withPermit {
            block().await()
            // }
        } catch (e: CancellationException) {
            // This may happen when an API client interrupts the in-progress request;
            // in this case, the request's job is canceled,
            // and CancellationException is thrown after CompletableFuture completes.
            // See the @io.coinmetrics.httpserver.impl.VertxHttpServerImpl for `job.cancel()`.
            onException(e)
        } catch (e: Exception) {
            // exception catching is required here because otherwise Kotlin-CompletableFuture integration loses stacktrace
            logOnException(e)
            if (e is ConnectException) {
                onConnectException(e)
            } else {
                onException(e)
            }
        }

    /**
     * Initiates a graceful shutdown (in-flight requests will be completed). Doesn't block or wait.
     */
    fun close() {
        client.shutdown()
    }
}

private fun JsonNode.toTextOrNullString(): String = this.textValue() ?: "null"
