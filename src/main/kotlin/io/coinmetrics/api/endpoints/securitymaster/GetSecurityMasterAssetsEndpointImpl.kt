package io.coinmetrics.api.endpoints.securitymaster

import io.coinmetrics.api.ChunkedResponseFormat
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.Response
import io.coinmetrics.api.Response.Companion.errorResponse
import io.coinmetrics.api.badNextPageToken
import io.coinmetrics.api.endpoints.GetSecurityMasterAssetsEndpoint
import io.coinmetrics.api.endpoints.GetSecurityMasterAssetsRequest
import io.coinmetrics.api.models.SecurityMasterAssetData
import io.coinmetrics.api.models.SecurityMasterAssetsResponse
import io.coinmetrics.api.resources.Resources
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.getSecurityMasterRateLimitHeaders
import io.coinmetrics.api.storage.AssetProfilesLocalStorage
import io.coinmetrics.api.storage.SecurityMasterLocalStorage
import io.coinmetrics.api.utils.CommonEndpointUtils
import io.coinmetrics.api.utils.paging.ListPagingUtils
import io.coinmetrics.api.utils.paging.PageToken
import io.coinmetrics.api.utils.paging.getPageFlow
import io.coinmetrics.api.utils.streams.BufferedSuspendableStream

class GetSecurityMasterAssetsEndpointImpl(
    private val amsService: AmsService,
    private val securityMasterLocalStorage: SecurityMasterLocalStorage,
    private val assetProfilesLocalStorage: AssetProfilesLocalStorage,
) : GetSecurityMasterAssetsEndpoint() {
    override suspend fun handle(request: GetSecurityMasterAssetsRequest): Response<SecurityMasterAssetsResponse> {
        val headers =
            amsService
                .getSecurityMasterRateLimitHeaders(request.apiKey, request.httpRequest)
                .getOrElse { (apiError, rateLimitHeaders) -> return errorResponse(apiError, rateLimitHeaders) }

        amsService.check(apiKey = request.apiKey, resource = "security_master").getOrElse { (apiError) ->
            return errorResponse(apiError, headers)
        }
        val assetProfiles =
            when (amsService.check(apiKey = request.apiKey, resource = "asset_profiles")) {
                is FunctionResult.Success -> assetProfilesLocalStorage.data.profilesPerAsset
                is FunctionResult.Failure -> emptyMap()
            }

        CommonEndpointUtils
            .validateMutuallyExclusiveFilters(
                listOf(
                    "assets" to request.assets,
                    "codes" to request.codes,
                ),
            ).getOrElse { return errorResponse(it, headers) }

        val assets =
            when {
                request.assets != null -> Resources.currencies.filter { it.cmTicker in request.assets }
                request.codes != null -> {
                    val assetIds = request.codes.map { securityMasterLocalStorage.data.assetCodeToAssetId[it] }
                    Resources.currencies.filter { it.id in assetIds }
                }

                else -> Resources.currencies
            }

        val offset =
            request.nextPageToken?.let {
                PageToken.IntPageToken.parseCatching(it) { return errorResponse(badNextPageToken()) }
            }

        val stream =
            BufferedSuspendableStream(
                initialState = offset,
                bufferLoader = ListPagingUtils.pageLoader(assets, request.pagingFrom),
                bufferSize = request.pageSize,
                stateResolver = {
                    ListPagingUtils.pageTokenValueResolver(offset?.value, request.pageSize, assets.lastIndex)
                },
            )

        val page =
            stream
                .map { currency ->
                    SecurityMasterAssetData(
                        asset = currency.cmTicker,
                        code = securityMasterLocalStorage.data.assetIdToAssetCode[currency.id],
                        // asset profiles data
                        description = assetProfiles[currency.cmTicker]?.description,
                        overview = assetProfiles[currency.cmTicker]?.overview,
                        website = assetProfiles[currency.cmTicker]?.website,
                        whitepaper = assetProfiles[currency.cmTicker]?.whitepaperUrl,
                        // asset metadata
                        decimals = currency.metadata.decimals?.toString(),
                        creationDate = currency.metadata.genesis,
                        type = currency.metadata.type,
                        parentAsset = currency.metadata.parent,
                        pricingAsset = currency.metadata.pricingCurrency,
                        erc20TokenContract = currency.metadata.contract,
                        fiat = currency.fiat.takeIf { it },
                    )
                }.getPageFlow(request.httpRequest, request.pageSize, request.pagingFrom)

        return Response.chunkedResponse(
            items = page,
            headers = headers,
            format = ChunkedResponseFormat.Json(),
        )
    }
}
