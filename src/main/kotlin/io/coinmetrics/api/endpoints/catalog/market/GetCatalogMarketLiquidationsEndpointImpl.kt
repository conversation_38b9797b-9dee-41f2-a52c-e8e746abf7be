package io.coinmetrics.api.endpoints.catalog.market

import io.coinmetrics.api.Response
import io.coinmetrics.api.endpoints.GetCatalogMarketLiquidationsEndpoint
import io.coinmetrics.api.endpoints.GetCatalogMarketLiquidationsRequest
import io.coinmetrics.api.models.CatalogMarketLiquidationsResponse
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.catalog.market.CatalogMarketRequest
import io.coinmetrics.api.service.catalog.market.CatalogV1MarketService
import io.coinmetrics.api.service.getCatalogRateLimitHeaders

class GetCatalogMarketLiquidationsEndpointImpl(
    private val amsService: AmsService,
    private val catalogMarketService: CatalogV1MarketService,
) : GetCatalogMarketLiquidationsEndpoint() {
    override suspend fun handle(request: GetCatalogMarketLiquidationsRequest): Response<CatalogMarketLiquidationsResponse> {
        val headers =
            amsService
                .getCatalogRateLimitHeaders(
                    apiKey = request.apiKey,
                    httpRequest = request.httpRequest,
                ).getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }

        val catalogMarketRequest =
            CatalogMarketRequest(
                request.httpRequest,
                request.apiKey,
                request.markets,
                request.exchange,
                request.type,
                request.base,
                request.quote,
                request.asset,
                request.symbol,
                request.format,
                request.limit,
                request.pretty,
            )

        return catalogMarketService
            .getLiquidations(catalogMarketRequest)
            .getOrElse {
                return Response.errorResponse(it, headers)
            }.let { (format, liquidations) ->
                Response.chunkedResponse(items = liquidations, format = format, headers = headers)
            }
    }
}
