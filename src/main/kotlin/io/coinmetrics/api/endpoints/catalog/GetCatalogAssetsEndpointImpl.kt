package io.coinmetrics.api.endpoints.catalog

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.ChunkedResponseFormat
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.Response
import io.coinmetrics.api.endpoints.GetCatalogAssetsEndpoint
import io.coinmetrics.api.endpoints.GetCatalogAssetsRequest
import io.coinmetrics.api.models.AssetInfo
import io.coinmetrics.api.models.AssetsResponse
import io.coinmetrics.api.resources.Resources
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.BlockchainEndpointService
import io.coinmetrics.api.service.MarketDiscoveryContextFactory
import io.coinmetrics.api.service.catalog.metric.impl.AssetMetricsService
import io.coinmetrics.api.service.discovery.MarketDiscoveryResult
import io.coinmetrics.api.service.discovery.MarketMatchedBy
import io.coinmetrics.api.service.getCatalogRateLimitHeaders
import io.coinmetrics.api.utils.CommonEndpointUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.sync.Semaphore
import kotlinx.coroutines.sync.withPermit

class GetCatalogAssetsEndpointImpl(
    private val amsService: AmsService,
    private val assetMetricsService: AssetMetricsService,
    private val marketDiscoveryContextFactory: MarketDiscoveryContextFactory,
    private val blockchainEndpointService: BlockchainEndpointService,
) : GetCatalogAssetsEndpoint() {
    companion object {
        private val amsParamsToEndpointParams =
            hashMapOf(
                "asset" to "assets",
                "base" to "assets",
                "quote" to "assets",
            )
        private val optionalResponseFields = setOf("metrics", "exchanges", "markets")
    }

    // not more than 15 parallel requests to prevent OOM
    private val limitedParallelism = Semaphore(permits = 15)

    override suspend fun handle(request: GetCatalogAssetsRequest): Response<AssetsResponse> {
        val headers =
            amsService
                .getCatalogRateLimitHeaders(
                    apiKey = request.apiKey,
                    httpRequest = request.httpRequest,
                ).getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }
        return handle(
            request.apiKey,
            request.assets?.map { it.lowercase() },
            request.include,
            request.exclude,
            headers,
        )
    }

    suspend fun handle(
        apiKey: String?,
        assets: List<String>?,
        include: List<String>?,
        exclude: List<String>?,
        rateLimitHeaders: List<Pair<String, String>>,
    ): Response<AssetsResponse> =
        limitedParallelism.withPermit {
            val fieldsToBeIncluded =
                CommonEndpointUtils
                    .determineFieldsToInclude(include, exclude, optionalResponseFields)
                    .getOrElse { apiError ->
                        return Response.errorResponse(apiError = apiError, headers = rateLimitHeaders)
                    }

            // it's important to have it as a Set because we are going to call contains/in many times later
            val assetIds =
                assets
                    ?.sorted()
                    ?.onEach { assetId ->
                        Resources.getCurrencyInfo(assetId) ?: return Response.errorResponse(
                            apiError = ApiError.BadParameter("assets", "Value '$assetId' is not supported."),
                            headers = rateLimitHeaders,
                        )
                    }?.toSet() ?: emptySet()

            val (marketsDataResultsDeferred, assetMetricsResultsDeferred) =
                coroutineScope {
                    findMarketsData(apiKey, assetIds) to
                        assetMetricsService.findSupportedCatalogV1AssetMetrics(
                            apiKey,
                            assetIds,
                        )
                }

            val exchangesIncluded = "exchanges" in fieldsToBeIncluded
            val marketsIncluded = "markets" in fieldsToBeIncluded

            val foundAssets = HashSet<String>()
            val marketsPerAsset = HashMap<String, HashSet<String>>()
            val exchangesPerAsset = HashMap<String, HashSet<String>>()

            marketsDataResultsDeferred
                .await()
                .getOrElse { return Response.errorResponse(apiError = it, headers = rateLimitHeaders) }
                .forEach { (marketAndMetadata, matchers) ->
                    val assetsByMatchers =
                        matchers
                            .map { matcher ->
                                when (matcher) {
                                    is MarketMatchedBy.Base -> listOf(matcher.base)
                                    is MarketMatchedBy.Quote -> listOf(matcher.quote)
                                    is MarketMatchedBy.BaseAndQuote -> listOfNotNull(matcher.base, matcher.quote)
                                }
                            }.flatten()
                            .distinct()

                    val market = marketAndMetadata.market
                    assetsByMatchers.forEach { asset ->
                        foundAssets.add(asset)
                        if (marketsIncluded) {
                            marketsPerAsset.computeIfAbsent(asset) { HashSet(1) }.add(market.toString())
                        }
                        if (exchangesIncluded) {
                            exchangesPerAsset.computeIfAbsent(asset) { HashSet(1) }.add(market.exchange)
                        }
                    }
                }

            val metricsIncluded = "metrics" in fieldsToBeIncluded
            val assetMetrics = HashMap<String, LinkedHashMap<String, ArrayList<String>>>()

            assetMetricsResultsDeferred.forEach {
                val (asset, assetMetricsResult) = it.await()
                when (assetMetricsResult) {
                    is FunctionResult.Failure -> {
                        val (apiError) = assetMetricsResult.value
                        if (apiError !is ApiError.Forbidden && asset !in foundAssets) {
                            return Response.errorResponse(
                                ApiError.ForbiddenWithMessage("Requested asset '$asset' is not available with supplied credentials."),
                                rateLimitHeaders,
                            )
                        }
                    }

                    is FunctionResult.Success -> {
                        assetMetricsResult.value
                            .filter { (_, metric, _) ->
                                !metric.startsWith("liquidations_reported_future_")
                            }.forEach { (rAsset, metric, frequency) ->
                                foundAssets.add(rAsset)
                                if (metricsIncluded) {
                                    val metrics = assetMetrics.computeIfAbsent(rAsset) { LinkedHashMap() }
                                    val frequencies = metrics.computeIfAbsent(metric) { ArrayList(1) }
                                    frequencies.add(frequency)
                                }
                            }
                    }
                }
            }

            val atlasAvailabilityPerAsset = blockchainEndpointService.findSupportedAssets(apiKey)
            atlasAvailabilityPerAsset
                .filterTo(HashMap()) { it.value && (assetIds.isEmpty() || assetIds.contains(it.key)) }
                .forEach {
                    foundAssets.add(it.key)
                }

            val assetsStream =
                flow {
                    // the following loop is executing with back pressure from the client
                    foundAssets.sorted().forEach { asset ->
                        val currencyInfo = Resources.getCurrencyInfo(asset)
                        if (currencyInfo != null) {
                            val metrics = assetMetrics[asset] ?: emptyMap()
                            val exchanges = exchangesPerAsset[asset]?.sorted() ?: emptyList()
                            // todo sort markets by components, not by the whole string
                            val markets = marketsPerAsset[asset]?.sorted() ?: emptyList()

                            val assetInfo =
                                AssetInfo(
                                    asset = asset,
                                    fullName = currencyInfo.name,
                                    experimental = currencyInfo.metadata.experimental,
                                    metrics =
                                        metrics
                                            .mapNotNull { (metric, frequencies) ->
                                                assetMetricsService.createAssetMetricInfo(
                                                    apiKey = apiKey,
                                                    asset = asset,
                                                    metric = metric,
                                                    frequencies = frequencies,
                                                )
                                            }.takeIf { it.isNotEmpty() },
                                    exchanges = exchanges.takeIf { it.isNotEmpty() },
                                    markets = markets.takeIf { it.isNotEmpty() },
                                    atlas = (atlasAvailabilityPerAsset[asset] ?: false).takeIf { it },
                                )
                            emit(assetInfo)
                        }
                    }
                }

            return Response.chunkedResponse(
                items = assetsStream,
                headers = rateLimitHeaders,
                format = ChunkedResponseFormat.Json(),
            )
        }

    private fun CoroutineScope.findMarketsData(
        apiKey: String?,
        assets: Set<String>,
    ): Deferred<FunctionResult<ApiError, Sequence<MarketDiscoveryResult>>> =
        async {
            val context = marketDiscoveryContextFactory.createContext(apiKey, emptySet(), amsParamsToEndpointParams)
            context
                .discover(
                    type = null,
                    exchange = null,
                    markets = null,
                    base = null,
                    quote = null,
                    symbol = null,
                    assets = assets,
                    catalogVersion = 1,
                    reverseOrder = false,
                    ignoreMatchedByData = false,
                ).mapFailure { context.discoveryToApiError(it) }
        }
}
