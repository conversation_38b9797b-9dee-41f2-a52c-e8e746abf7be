package io.coinmetrics.api.endpoints.catalog

import io.coinmetrics.api.Response
import io.coinmetrics.api.endpoints.GetCatalogInstitutionsEndpoint
import io.coinmetrics.api.endpoints.GetCatalogInstitutionsRequest
import io.coinmetrics.api.models.InstitutionsResponse
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.catalog.metric.impl.InstitutionMetricsService
import io.coinmetrics.api.service.getCatalogRateLimitHeaders
import java.time.Instant

class GetCatalogInstitutionsEndpointImpl(
    private val amsService: AmsService,
    private val institutionMetricsService: InstitutionMetricsService,
) : GetCatalogInstitutionsEndpoint() {
    override suspend fun handle(request: GetCatalogInstitutionsRequest): Response<InstitutionsResponse> {
        val headers =
            amsService
                .getCatalogRateLimitHeaders(
                    apiKey = request.apiKey,
                    httpRequest = request.httpRequest,
                ).getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }

        return handle(
            request.apiKey,
            request.institutions?.map { it.lowercase() }?.toTypedArray(),
            headers,
            request.httpRequest.receivedTime,
        )
    }

    suspend fun handle(
        apiKey: String?,
        institutions: Array<String>?,
        rateLimitHeaders: List<Pair<String, String>>,
        requestReceivedTime: Instant,
    ): Response<InstitutionsResponse> {
        val institutionsSet = institutions?.toSortedSet()
        return institutionMetricsService
            .findSupportedCatalogV1InstitutionMetrics(apiKey, institutionsSet, requestReceivedTime)
            .map { Response.successResponse(it, rateLimitHeaders) }
            .getOrElse { return Response.errorResponse(it, rateLimitHeaders) }
    }
}
