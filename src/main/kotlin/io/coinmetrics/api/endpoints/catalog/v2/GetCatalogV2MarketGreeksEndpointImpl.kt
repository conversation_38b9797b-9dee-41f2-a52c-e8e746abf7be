package io.coinmetrics.api.endpoints.catalog.v2

import io.coinmetrics.api.Response
import io.coinmetrics.api.badNextPageToken
import io.coinmetrics.api.endpoints.GetCatalogV2MarketGreeksEndpoint
import io.coinmetrics.api.endpoints.GetCatalogV2MarketGreeksRequest
import io.coinmetrics.api.model.page.StringPageRequest
import io.coinmetrics.api.models.CatalogV2MarketGreeksResponse
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.catalog.market.CatalogMarketRequest
import io.coinmetrics.api.service.catalog.market.CatalogV2MarketService
import io.coinmetrics.api.service.getCatalogRateLimitHeaders
import io.coinmetrics.api.utils.paging.PageToken

class GetCatalogV2MarketGreeksEndpointImpl(
    private val amsService: AmsService,
    private val catalogMarketService: CatalogV2MarketService,
) : GetCatalogV2MarketGreeksEndpoint() {
    override suspend fun handle(request: GetCatalogV2MarketGreeksRequest): Response<CatalogV2MarketGreeksResponse> {
        val headers =
            amsService
                .getCatalogRateLimitHeaders(
                    apiKey = request.apiKey,
                    httpRequest = request.httpRequest,
                ).getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }

        val market =
            request.nextPageToken?.let {
                PageToken.StringPageToken
                    .parseCatching(it) {
                        return Response.errorResponse(badNextPageToken())
                    }?.string
            }

        val catalogMarketRequest =
            CatalogMarketRequest(
                request.httpRequest,
                request.apiKey,
                request.markets,
                request.exchange,
                request.type,
                request.base,
                request.quote,
                request.asset,
                request.symbol,
                request.format,
                pretty = request.pretty,
                pageRequest =
                    StringPageRequest(
                        entity = market,
                        pageSize = request.pageSize,
                        pagingFrom = request.pagingFrom,
                    ),
            )

        return catalogMarketService
            .getGreeks(catalogMarketRequest)
            .getOrElse {
                return Response.errorResponse(it, headers)
            }.let { (format, trades) ->
                Response.chunkedResponse(items = trades, format = format, headers = headers)
            }
    }
}
