package io.coinmetrics.api.endpoints.catalog

import io.coinmetrics.api.ChunkedResponseFormat
import io.coinmetrics.api.Response
import io.coinmetrics.api.endpoints.GetCatalogIndexesEndpoint
import io.coinmetrics.api.endpoints.GetCatalogIndexesRequest
import io.coinmetrics.api.models.IndexesResponse
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.catalog.CatalogIndexService
import io.coinmetrics.api.service.getCatalogRateLimitHeaders
import io.coinmetrics.api.utils.toChunkedResponse

class GetCatalogIndexesEndpointImpl(
    private val amsService: AmsService,
    private val indexService: CatalogIndexService,
) : GetCatalogIndexesEndpoint() {
    override suspend fun handle(request: GetCatalogIndexesRequest): Response<IndexesResponse> {
        val headers =
            amsService
                .getCatalogRateLimitHeaders(request.apiKey, request.httpRequest)
                .getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }

        return indexService
            .getIndexes(
                request.apiKey,
                request.indexes,
                format = ChunkedResponseFormat.Json().name,
                request.httpRequest,
            ).toChunkedResponse(headers)
    }
}
