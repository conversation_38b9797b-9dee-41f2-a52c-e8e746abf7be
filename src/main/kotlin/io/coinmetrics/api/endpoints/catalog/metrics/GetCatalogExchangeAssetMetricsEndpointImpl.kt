package io.coinmetrics.api.endpoints.catalog.metrics

import io.coinmetrics.api.Response
import io.coinmetrics.api.endpoints.GetCatalogExchangeAssetMetricsEndpoint
import io.coinmetrics.api.endpoints.GetCatalogExchangeAssetMetricsRequest
import io.coinmetrics.api.models.CatalogExchangeAssetMetricsResponse
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.catalog.metric.impl.ExchangeAssetMetricsService
import io.coinmetrics.api.service.getCatalogRateLimitHeaders

class GetCatalogExchangeAssetMetricsEndpointImpl(
    private val amsService: AmsService,
    private val exchangeAssetMetricsService: ExchangeAssetMetricsService,
) : GetCatalogExchangeAssetMetricsEndpoint() {
    override suspend fun handle(request: GetCatalogExchangeAssetMetricsRequest): Response<CatalogExchangeAssetMetricsResponse> {
        val headers =
            amsService
                .getCatalogRateLimitHeaders(
                    apiKey = request.apiKey,
                    httpRequest = request.httpRequest,
                ).getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }

        return exchangeAssetMetricsService
            .findSupportedCatalogV1ExchangeAssetMetrics(request.apiKey, request.metrics, request.reviewable)
            .getOrElse { return Response.errorResponse(it, headers) }
            .let { Response.successResponse(it, headers) }
    }
}
