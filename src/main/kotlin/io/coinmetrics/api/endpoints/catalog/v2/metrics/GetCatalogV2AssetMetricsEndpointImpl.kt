package io.coinmetrics.api.endpoints.catalog.v2.metrics

import io.coinmetrics.api.Response
import io.coinmetrics.api.endpoints.GetCatalogV2AssetMetricsEndpoint
import io.coinmetrics.api.endpoints.GetCatalogV2AssetMetricsRequest
import io.coinmetrics.api.model.page.StringPageRequest
import io.coinmetrics.api.models.CatalogV2AssetMetricsResponse
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.catalog.metric.impl.AssetMetricsService
import io.coinmetrics.api.service.getCatalogRateLimitHeaders

class GetCatalogV2AssetMetricsEndpointImpl(
    private val amsService: AmsService,
    private val assetMetricsService: AssetMetricsService,
) : GetCatalogV2AssetMetricsEndpoint() {
    override suspend fun handle(request: GetCatalogV2AssetMetricsRequest): Response<CatalogV2AssetMetricsResponse> {
        val headers =
            amsService
                .getCatalogRateLimitHeaders(
                    apiKey = request.apiKey,
                    httpRequest = request.httpRequest,
                ).getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }

        return StringPageRequest
            .create(request.nextPageToken, request.pageSize, request.pagingFrom)
            .flatMap { pageRequest ->
                assetMetricsService.findSupportedCatalogV2AssetMetrics(
                    request.apiKey,
                    request.assets,
                    request.metrics,
                    request.reviewable,
                    request.format,
                    request.httpRequest,
                    pageRequest,
                )
            }.getOrElse { return Response.errorResponse(it, headers) }
            .let { (format, assets) -> Response.chunkedResponse(assets, headers, format) }
    }
}
