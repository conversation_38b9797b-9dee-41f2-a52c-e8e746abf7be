package io.coinmetrics.api.endpoints.catalog.v2.metrics

import io.coinmetrics.api.Response
import io.coinmetrics.api.endpoints.GetCatalogV2ExchangeMetricsEndpoint
import io.coinmetrics.api.endpoints.GetCatalogV2ExchangeMetricsRequest
import io.coinmetrics.api.model.page.StringPageRequest
import io.coinmetrics.api.models.CatalogV2ExchangeMetricsResponse
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.catalog.metric.impl.ExchangeMetricsService
import io.coinmetrics.api.service.getCatalogRateLimitHeaders

class GetCatalogV2ExchangeMetricsEndpointImpl(
    private val amsService: AmsService,
    private val exchangeMetricsService: ExchangeMetricsService,
) : GetCatalogV2ExchangeMetricsEndpoint() {
    override suspend fun handle(request: GetCatalogV2ExchangeMetricsRequest): Response<CatalogV2ExchangeMetricsResponse> {
        val headers =
            amsService
                .getCatalogRateLimitHeaders(
                    apiKey = request.apiKey,
                    httpRequest = request.httpRequest,
                ).getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }

        return StringPageRequest
            .create(request.nextPageToken, request.pageSize, request.pagingFrom)
            .flatMap { pageRequest ->
                exchangeMetricsService.findSupportedCatalogV2ExchangeMetrics(
                    request.apiKey,
                    request.exchanges,
                    request.metrics,
                    request.format,
                    request.httpRequest,
                    pageRequest,
                )
            }.getOrElse { return Response.errorResponse(it, headers) }
            .let { (format, exchanges) -> Response.chunkedResponse(exchanges, headers, format) }
    }
}
