package io.coinmetrics.api.endpoints.catalog.market

import io.coinmetrics.api.Response
import io.coinmetrics.api.endpoints.GetCatalogMarketFundingRatesEndpoint
import io.coinmetrics.api.endpoints.GetCatalogMarketFundingRatesRequest
import io.coinmetrics.api.models.CatalogMarketFundingRatesResponse
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.catalog.market.CatalogMarketRequest
import io.coinmetrics.api.service.catalog.market.CatalogV1MarketService
import io.coinmetrics.api.service.getCatalogRateLimitHeaders

class GetCatalogMarketFundingRatesEndpointImpl(
    private val amsService: AmsService,
    private val catalogMarketService: CatalogV1MarketService,
) : GetCatalogMarketFundingRatesEndpoint() {
    override suspend fun handle(request: GetCatalogMarketFundingRatesRequest): Response<CatalogMarketFundingRatesResponse> {
        val headers =
            amsService
                .getCatalogRateLimitHeaders(
                    apiKey = request.apiKey,
                    httpRequest = request.httpRequest,
                ).getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }

        val catalogMarketRequest =
            CatalogMarketRequest(
                request.httpRequest,
                request.apiKey,
                request.markets,
                request.exchange,
                request.type,
                request.base,
                request.quote,
                request.asset,
                request.symbol,
                request.format,
                request.limit,
                request.pretty,
            )

        return catalogMarketService
            .getFundingRates(catalogMarketRequest)
            .getOrElse {
                return Response.errorResponse(it, headers)
            }.let { (format, fundingRates) ->
                Response.chunkedResponse(items = fundingRates, format = format, headers = headers)
            }
    }
}
