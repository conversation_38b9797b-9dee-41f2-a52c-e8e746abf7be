package io.coinmetrics.api.endpoints.catalog.market

import io.coinmetrics.api.Response
import io.coinmetrics.api.endpoints.GetCatalogMarketGreeksEndpoint
import io.coinmetrics.api.endpoints.GetCatalogMarketGreeksRequest
import io.coinmetrics.api.models.CatalogMarketGreeksResponse
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.catalog.market.CatalogMarketRequest
import io.coinmetrics.api.service.catalog.market.CatalogV1MarketService
import io.coinmetrics.api.service.getCatalogRateLimitHeaders

class GetCatalogMarketGreeksEndpointImpl(
    private val amsService: AmsService,
    private val catalogMarketService: CatalogV1MarketService,
) : GetCatalogMarketGreeksEndpoint() {
    override suspend fun handle(request: GetCatalogMarketGreeksRequest): Response<CatalogMarketGreeksResponse> {
        val headers =
            amsService
                .getCatalogRateLimitHeaders(
                    apiKey = request.apiKey,
                    httpRequest = request.httpRequest,
                ).getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }

        val catalogMarketRequest =
            CatalogMarketRequest(
                request.httpRequest,
                request.apiKey,
                request.markets,
                request.exchange,
                request.type,
                request.base,
                request.quote,
                request.asset,
                request.symbol,
                request.format,
                request.limit,
                request.pretty,
            )

        return catalogMarketService
            .getGreeks(catalogMarketRequest)
            .getOrElse {
                return Response.errorResponse(it, headers)
            }.let { (format, greeks) ->
                Response.chunkedResponse(items = greeks, format = format, headers = headers)
            }
    }
}
