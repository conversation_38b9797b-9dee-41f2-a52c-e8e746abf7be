package io.coinmetrics.api.endpoints.catalog.v2.pair

import io.coinmetrics.api.Response
import io.coinmetrics.api.endpoints.GetCatalogV2PairCandlesEndpoint
import io.coinmetrics.api.endpoints.GetCatalogV2PairCandlesRequest
import io.coinmetrics.api.model.page.StringPageRequest
import io.coinmetrics.api.models.CatalogV2PairCandlesResponse
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.catalog.CatalogPairService
import io.coinmetrics.api.service.getCatalogRateLimitHeaders
import io.coinmetrics.api.utils.toChunkedResponse

class GetCatalogV2PairCandlesEndpointImpl(
    private val amsService: AmsService,
    private val pairService: CatalogPairService,
) : GetCatalogV2PairCandlesEndpoint() {
    override suspend fun handle(request: GetCatalogV2PairCandlesRequest): Response<CatalogV2PairCandlesResponse> {
        val headers =
            amsService
                .getCatalogRateLimitHeaders(request.apiKey, request.httpRequest)
                .getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }

        return StringPageRequest
            .create(request.nextPageToken, request.pageSize, request.pagingFrom)
            .flatMap { pageRequest ->
                pairService.getPairCandles(
                    request.apiKey,
                    request.pairs,
                    request.format,
                    request.httpRequest,
                    pageRequest,
                )
            }.toChunkedResponse(headers)
    }
}
