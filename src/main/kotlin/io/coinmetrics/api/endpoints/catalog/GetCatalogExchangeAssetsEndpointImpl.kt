package io.coinmetrics.api.endpoints.catalog

import io.coinmetrics.api.Response
import io.coinmetrics.api.endpoints.GetCatalogExchangeAssetsEndpoint
import io.coinmetrics.api.endpoints.GetCatalogExchangeAssetsRequest
import io.coinmetrics.api.models.ExchangeAssetsResponse
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.catalog.metric.impl.ExchangeAssetMetricsService
import io.coinmetrics.api.service.getCatalogRateLimitHeaders
import java.time.Instant

class GetCatalogExchangeAssetsEndpointImpl(
    private val amsService: AmsService,
    private val exchangeAssetMetricsService: ExchangeAssetMetricsService,
) : GetCatalogExchangeAssetsEndpoint() {
    override suspend fun handle(request: GetCatalogExchangeAssetsRequest): Response<ExchangeAssetsResponse> {
        val headers =
            amsService
                .getCatalogRateLimitHeaders(
                    apiKey = request.apiKey,
                    httpRequest = request.httpRequest,
                ).getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }
        return handle(
            request.apiKey,
            request.exchangeAssets?.map { it.lowercase() }?.toTypedArray(),
            headers,
            request.httpRequest.receivedTime,
        )
    }

    suspend fun handle(
        apiKey: String?,
        exchangeAssets: Array<String>?,
        rateLimitHeaders: List<Pair<String, String>>,
        requestReceivedTime: Instant,
    ): Response<ExchangeAssetsResponse> {
        val exchangeAssetsSet = exchangeAssets?.toSortedSet()
        return exchangeAssetMetricsService
            .findSupportedCatalogV1ExchangeAssetMetrics(
                apiKey,
                exchangeAssetsSet,
                requestReceivedTime,
            ).map { Response.successResponse(it, rateLimitHeaders) }
            .getOrElse { return Response.errorResponse(it, rateLimitHeaders) }
    }
}
