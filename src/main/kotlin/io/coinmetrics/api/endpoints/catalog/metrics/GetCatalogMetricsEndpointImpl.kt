package io.coinmetrics.api.endpoints.catalog.metrics

import io.coinmetrics.api.Response
import io.coinmetrics.api.endpoints.GetCatalogMetricsEndpoint
import io.coinmetrics.api.endpoints.GetCatalogMetricsRequest
import io.coinmetrics.api.models.CatalogMetricsResponse
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.catalog.metric.impl.AssetMetricsService
import io.coinmetrics.api.service.getCatalogRateLimitHeaders

class GetCatalogMetricsEndpointImpl(
    private val amsService: AmsService,
    private val assetMetricsService: AssetMetricsService,
) : GetCatalogMetricsEndpoint() {
    override suspend fun handle(request: GetCatalogMetricsRequest): Response<CatalogMetricsResponse> {
        val headers =
            amsService
                .getCatalogRateLimitHeaders(
                    apiKey = request.apiKey,
                    httpRequest = request.httpRequest,
                ).getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }

        return assetMetricsService
            .findSupportedCatalogV1Metrics(request.apiKey, request.metrics, request.reviewable)
            .getOrElse { return Response.errorResponse(it, headers) }
            .let { r ->
                Response
                    .successResponse(
                        r.copy(data = r.data.filter { info -> !info.metric.startsWith("liquidations_reported_future_") }),
                        headers,
                    )
            }
    }
}
