package io.coinmetrics.api.endpoints.blockchain.metadata

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.ChunkedResponseFormat
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.Response
import io.coinmetrics.api.badNextPageToken
import io.coinmetrics.api.endpoints.GetBlockchainMetadataEntitiesEndpoint
import io.coinmetrics.api.endpoints.GetBlockchainMetadataEntitiesRequest
import io.coinmetrics.api.model.tagging.TaggedEntity
import io.coinmetrics.api.models.BlockchainMetadataTaggedEntitiesResponse
import io.coinmetrics.api.persistence.AddressTaggingDatabase
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.getBlockchainMetadataRateLimitHeaders
import io.coinmetrics.api.statistics.tagging.AddressTaggingStatistics
import io.coinmetrics.api.utils.CommonEndpointUtils
import io.coinmetrics.api.utils.DataUtils
import io.coinmetrics.api.utils.QueryTextBuilder
import io.coinmetrics.api.utils.SqlUtils.escapeAndQuote
import io.coinmetrics.api.utils.SqlUtils.escapeQuoteAndAppend
import io.coinmetrics.api.utils.TaggedEntityRangeQuery
import io.coinmetrics.api.utils.paging.TaggedEntityPageToken
import io.coinmetrics.api.utils.paging.getPageFlow
import io.coinmetrics.api.utils.streams.SuspendableStream

class GetBlockchainMetadataTaggedEntitiesEndpointImpl(
    private val amsService: AmsService,
    private val statistics: AddressTaggingStatistics,
    private val db: AddressTaggingDatabase?,
) : GetBlockchainMetadataEntitiesEndpoint() {
    companion object {
        private const val ENTITY_MAX_LENGTH = 2006
        private val entityIdPattern = "[ _0-9a-zA-Z]{0,$ENTITY_MAX_LENGTH}".toRegex()
        private val allColumns =
            listOf(
                "r.entity",
                "r.location",
                "r.tag",
                "r.timestamp_start",
                "r.started_by",
                "r.timestamp_end",
                "r.tagger_type",
                "r.tagger_method",
                "r.block_height_start",
                "r.block_height_end",
                "r.block_hash_start",
                "r.block_hash_end",
                "e.type",
            )
        val tagsCountValidationError = ApiError.BadParameter("tags", "A single tag is allowed per each request.")
        val locationsCountValidationError =
            ApiError.BadParameter("locations", "A single entity location is allowed per each request.")
        val tagsValidationError = ApiError.BadParameter("tags", "All requested tags aren't supported.")
        val locationsValidationError = ApiError.BadParameter("locations", "All requested locations aren't supported.")
    }

    override suspend fun handle(request: GetBlockchainMetadataEntitiesRequest): Response<BlockchainMetadataTaggedEntitiesResponse> {
        val headers =
            amsService
                .getBlockchainMetadataRateLimitHeaders(
                    apiKey = request.apiKey,
                    httpRequest = request.httpRequest,
                ).getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }

        request.tags?.let {
            // TODO: this limit should be removed in the future releases
            if (it.size > 1) {
                return@handle Response.errorResponse(tagsCountValidationError, headers)
            }
        }

        request.locations?.let {
            // TODO: this limit should be removed in the future releases
            if (it.size > 1) {
                return@handle Response.errorResponse(locationsCountValidationError, headers)
            }
        }

        val accessibleTags =
            amsService
                .discovery(
                    apiKey = request.apiKey,
                    resource = "tags",
                    target = "tag",
                ) { it }
                .getOrElse { (apiError) ->
                    return Response.errorResponse(apiError, headers)
                }.values
                .map { it[0] }
                .toHashSet()

        val (requestedItemColumnName, requestedItems) =
            handleMutuallyExclusiveParameters(
                request,
                accessibleTags,
            ).getOrElse { error ->
                return Response.errorResponse(error, headers)
            }

        val validLocations =
            request.locations?.takeIf { it.isNotEmpty() }?.let {
                validateLocations(request.locations).getOrElse { error -> return Response.errorResponse(error, headers) }
            } ?: emptyList()

        val initialState =
            request.nextPageToken?.let {
                TaggedEntityPageToken.parseCatching(it) { return Response.errorResponse(badNextPageToken()) }
            }

        val stream =
            handleInternal(
                requestedItems = requestedItems,
                requestedItemColumnName = requestedItemColumnName,
                locations = validLocations,
                initialState = initialState,
                bufferSize = request.pageSize + 1,
            )

        val page = stream.map { it.value }.getPageFlow(request.httpRequest, request.pageSize, true)
        return Response.chunkedResponse(
            items = page,
            headers = headers,
            format = ChunkedResponseFormat.Json(),
        )
    }

    private fun handleMutuallyExclusiveParameters(
        request: GetBlockchainMetadataEntitiesRequest,
        accessibleTags: Set<String>,
    ): FunctionResult<ApiError, Pair<String, Collection<String>>> {
        CommonEndpointUtils
            .validateMutuallyExclusiveFilters(
                listOf(
                    "tags" to request.tags,
                    "entities" to request.entities,
                ),
            ).getOrElse { return FunctionResult.Failure(it) }

        val validTags =
            request.tags?.let {
                validateTags(it, accessibleTags).getOrElse { error -> return FunctionResult.Failure(error) }
            } ?: emptyList()

        val validEntities =
            request.entities?.let {
                validateEntities(it).getOrElse { error -> return FunctionResult.Failure(error) }
            } ?: emptyList()

        return when {
            validTags.isNotEmpty() && validEntities.isEmpty() -> FunctionResult.Success(Pair("r.tag", validTags))
            validTags.isEmpty() && validEntities.isNotEmpty() -> FunctionResult.Success(Pair("r.entity", validEntities))
            else -> FunctionResult.Failure(ApiError.BadParameters("Either 'tags' or 'entities' must be specified, but both were missing."))
        }
    }

    private fun handleInternal(
        requestedItems: Collection<String>,
        requestedItemColumnName: String,
        locations: Collection<String>,
        initialState: TaggedEntityPageToken?,
        bufferSize: Int,
    ): SuspendableStream<TaggedEntity, TaggedEntityPageToken> {
        if (db == null) return SuspendableStream.empty()
        val itemNames = requestedItems.joinToString(transform = escapeAndQuote())
        val locationNames = locations.joinToString(transform = escapeAndQuote())
        val locationsInClause = if (locations.isEmpty()) "" else "AND r.location IN ($locationNames)"
        val filterByItemsAndLocations = "$requestedItemColumnName IN ($itemNames) $locationsInClause"
        val orderByTagOrEntity =
            if (requestedItemColumnName ==
                "r.entity"
            ) {
                "r.entity, r.location, r.tag, r.started_by, r.timestamp_start"
            } else {
                "r.tag, r.location, r.entity, r.started_by, r.timestamp_start"
            }
        val locationNamesAppendLocationType =
            locations.joinToString(transform = escapeQuoteAndAppend("::location"))
        val itemNamesAppendTagType = requestedItems.joinToString(transform = escapeQuoteAndAppend("::tag"))
        val queryTextBuilder: QueryTextBuilder =
            if (requestedItemColumnName == "r.entity" || (requestedItemColumnName == "r.tag" && locations.isEmpty())) {
                { filter, limit ->

                    """
                    SELECT ${allColumns.joinToString()}
                    FROM entities_tags r
                    JOIN entities e ON r.entity = e.name
                    WHERE $filterByItemsAndLocations $filter
                    ORDER BY $orderByTagOrEntity
                    LIMIT $limit
                    """.trimIndent()
                }
            } else {
                { filter, limit ->
                    """
                    WITH l AS (
                    SELECT * 
                    FROM (VALUES ($locationNamesAppendLocationType)) AS t(l)
                    ),
                    r AS (
                    SELECT *
                    FROM entities_tags r
                    JOIN l ON r.location = l.l
                    WHERE r.tag IN ($itemNamesAppendTagType) $filter
                    LIMIT $limit
                    )
                    SELECT ${allColumns.joinToString()}
                    FROM r
                    JOIN entities e ON r.entity = e.name
                    ORDER BY $orderByTagOrEntity
                    LIMIT $limit;
                    """.trimIndent()
                }
            }

        return DataUtils.createStream(
            db = db,
            queryTextBuilder = queryTextBuilder,
            bufferSize = bufferSize,
            keyNames =
                if (requestedItemColumnName == "r.entity") {
                    arrayOf("r.entity", "r.location", "r.tag", "r.started_by", "r.timestamp_start")
                } else {
                    arrayOf("r.tag", "r.location", "r.entity", "r.started_by", "r.timestamp_start")
                },
            dataMapper = TaggedEntity.mapper,
            rangeQuery = TaggedEntityRangeQuery.createRangeQuery(),
            initialState = initialState,
            stateResolver = { TaggedEntityPageToken(it.key) },
            streamId = null,
        )
    }

    private fun validateLocations(locations: List<String>): FunctionResult<ApiError, Collection<String>> {
        val validLocations = locations.toSet().retainAll(statistics.getSupportedLocations())
        return failIfEmpty(validLocations, locationsValidationError)
    }

    private fun validateTags(
        tags: List<String>,
        allowedTags: Set<String>,
    ): FunctionResult<ApiError, Collection<String>> {
        val accessibleTags = tags.toSet().retainAll(allowedTags)
        if (accessibleTags.isEmpty()) {
            return FunctionResult.Failure(tagsValidationError)
        }

        val validTags = accessibleTags.retainAll(statistics.getSupportedTagNames("ENTITY"))
        return failIfEmpty(validTags, tagsValidationError)
    }

    private fun Set<String>.retainAll(supportedItems: Set<String>): Set<String> {
        val unsupportedItems = this - supportedItems
        return this - unsupportedItems
    }

    private fun failIfEmpty(
        items: Collection<String>,
        error: ApiError,
    ): FunctionResult<ApiError, Collection<String>> = if (items.isEmpty()) FunctionResult.Failure(error) else FunctionResult.Success(items)

    private fun validateEntities(entities: List<String>): FunctionResult<ApiError, Collection<String>> {
        if (entities.size > 100) {
            return FunctionResult.Failure(
                ApiError.BadParameter(
                    "entities",
                    "Parameter has too many items. Max number of items is 100.",
                ),
            )
        }
        return if (!entities.all(entityIdPattern::matches)) {
            FunctionResult.Failure(
                ApiError.BadParameter(
                    "entities",
                    "Entity ID must contain only digits and letters and be no longer than $ENTITY_MAX_LENGTH symbols.",
                ),
            )
        } else {
            FunctionResult.Success(entities.toList())
        }
    }
}
