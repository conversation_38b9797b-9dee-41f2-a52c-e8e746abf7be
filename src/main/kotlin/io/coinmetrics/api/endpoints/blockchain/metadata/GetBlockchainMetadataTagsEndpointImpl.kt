package io.coinmetrics.api.endpoints.blockchain.metadata

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.ChunkedResponseFormat
import io.coinmetrics.api.Response
import io.coinmetrics.api.badNextPageToken
import io.coinmetrics.api.endpoints.GetBlockchainMetadataTagsEndpoint
import io.coinmetrics.api.endpoints.GetBlockchainMetadataTagsRequest
import io.coinmetrics.api.models.BlockchainMetadataTagInfo
import io.coinmetrics.api.models.BlockchainMetadataTagsResponse
import io.coinmetrics.api.models.PagingFrom
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.getBlockchainMetadataRateLimitHeaders
import io.coinmetrics.api.statistics.tagging.AddressTaggingStatistics
import io.coinmetrics.api.statistics.tagging.TagInfo
import io.coinmetrics.api.utils.paging.ListPagingUtils
import io.coinmetrics.api.utils.paging.PageToken
import io.coinmetrics.api.utils.paging.getPageFlow
import io.coinmetrics.api.utils.streams.BufferedSuspendableStream

class GetBlockchainMetadataTagsEndpointImpl(
    private val amsService: AmsService,
    private val statistics: AddressTaggingStatistics,
) : GetBlockchainMetadataTagsEndpoint() {
    override suspend fun handle(request: GetBlockchainMetadataTagsRequest): Response<BlockchainMetadataTagsResponse> {
        val headers =
            amsService
                .getBlockchainMetadataRateLimitHeaders(
                    apiKey = request.apiKey,
                    httpRequest = request.httpRequest,
                ).getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }

        val accessibleTags =
            amsService
                .discovery(
                    apiKey = request.apiKey,
                    resource = "tags",
                    target = "tag",
                ) { it }
                .getOrElse { (apiError) ->
                    return Response.errorResponse(apiError, headers)
                }.values
                .map { it[0] }
                .toHashSet()

        if (request.type != null && !statistics.getSupportedTagTypes().contains(request.type)) {
            return Response.errorResponse(
                ApiError.UnsupportedParameterValue("type", request.type),
                headers,
            )
        }
        val tags =
            request.type
                ?.let { statistics.getSupportedTags(request.type) }
                ?: statistics
                    .getSupportedTags()
                    .filter { accessibleTags.contains(it.name) }

        val initialState =
            request.nextPageToken?.let {
                PageToken.IntPageToken.parseCatching(it) { return Response.errorResponse(badNextPageToken()) }
            }
        val stream =
            BufferedSuspendableStream(
                initialState = initialState,
                bufferLoader = ListPagingUtils.pageLoader(tags),
                bufferSize = request.pageSize,
                stateResolver = {
                    ListPagingUtils.pageTokenValueResolver(initialState?.value, request.pageSize, tags.lastIndex)
                },
            )
        val page = stream.map(tagInfoMapper).getPageFlow(request.httpRequest, request.pageSize, PagingFrom.START)

        return Response.chunkedResponse(
            items = page,
            headers = headers,
            format = ChunkedResponseFormat.Json(),
        )
    }

    private val tagInfoMapper: (TagInfo) -> BlockchainMetadataTagInfo = { tagInfo ->
        BlockchainMetadataTagInfo(
            tag = tagInfo.name,
            type = tagInfo.type,
            description = tagInfo.description,
        )
    }
}
