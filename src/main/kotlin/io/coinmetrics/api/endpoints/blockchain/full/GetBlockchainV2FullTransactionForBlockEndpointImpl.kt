package io.coinmetrics.api.endpoints.blockchain.full

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.Response
import io.coinmetrics.api.endpoints.GetBlockchainV2FullTransactionForBlockEndpoint
import io.coinmetrics.api.endpoints.GetBlockchainV2FullTransactionForBlockRequest
import io.coinmetrics.api.endpoints.blockchain.BlockchainEndpointUtils
import io.coinmetrics.api.endpoints.blockchain.BlockchainEndpointUtils.findBalanceUpdatesByBlockHashAndTxid
import io.coinmetrics.api.endpoints.blockchain.BlockchainEndpointUtils.toBlockHashSqlValue
import io.coinmetrics.api.endpoints.blockchain.BlockchainEndpointUtils.toBlockchainFullSingleTransactionResponse
import io.coinmetrics.api.endpoints.blockchain.BlockchainEndpointUtils.toTxidSqlValue
import io.coinmetrics.api.models.BlockchainFullSingleTransactionResponseV2
import io.coinmetrics.api.persistence.Databases
import io.coinmetrics.api.service.BlockchainEndpointService
import io.coinmetrics.databases.Database
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope

class GetBlockchainV2FullTransactionForBlockEndpointImpl(
    private val databases: Databases,
    private val blockchainEndpointService: BlockchainEndpointService,
    private val communityKey: String,
) : GetBlockchainV2FullTransactionForBlockEndpoint() {
    override suspend fun handle(
        request: GetBlockchainV2FullTransactionForBlockRequest,
    ): Response<BlockchainFullSingleTransactionResponseV2> {
        val headers =
            blockchainEndpointService
                .validateV2Request<BlockchainFullSingleTransactionResponseV2>(
                    request.apiKey,
                    request.asset,
                    request.httpRequest,
                    dbTableNamePartsToBeUsed = listOf("transactions", "balance_updates", "stale_blocks"),
                ).getOrElse { return it }

        return when (val result = handleInternal(getInternalRequest(request))) {
            is FunctionResult.Success -> Response.successResponse(result.value, headers)
            is FunctionResult.Failure -> Response.errorResponse(result.value, headers)
        }
    }

    /**
     * Applies restrictions when the API key is community key. Otherwise, it returns the original request unchanged.
     */
    private fun getInternalRequest(request: GetBlockchainV2FullTransactionForBlockRequest): GetBlockchainV2FullTransactionForBlockRequest {
        if (request.apiKey == communityKey) {
            // Apply restrictions for community API key
            return GetBlockchainV2FullTransactionForBlockRequest(
                httpRequest = request.httpRequest,
                apiKey = request.apiKey,
                asset = request.asset,
                blockHash = request.blockHash,
                txid = request.txid,
                includeSubAccounts = false,
                pretty = request.pretty,
            )
        }
        return request
    }

    private suspend fun handleInternal(
        request: GetBlockchainV2FullTransactionForBlockRequest,
    ): FunctionResult<ApiError, BlockchainFullSingleTransactionResponseV2> {
        val db = databases.resolveAtlasDatabase(request.asset)
        val blockHashSqlValue = toBlockHashSqlValue(request.asset, request.blockHash) { return FunctionResult.Failure(it) }
        val txidSqlValue = toTxidSqlValue(request.asset, request.txid) { return FunctionResult.Failure(it) }

        val (nullableTxMeta, txBalanceUpdates) =
            coroutineScope {
                val txMeta =
                    async {
                        findTransactionByBlockHashAndTxid(db, request.asset, blockHashSqlValue, txidSqlValue)
                    }
                val txBalanceUpdates =
                    async {
                        findBalanceUpdatesByBlockHashAndTxid(db, request.asset, request.includeSubAccounts, blockHashSqlValue, txidSqlValue)
                    }
                txMeta.await() to txBalanceUpdates.await()
            }

        val txMeta = nullableTxMeta ?: return FunctionResult.Failure(ApiError.NotFound)

        val isCommunityApi = request.apiKey == communityKey
        val timeConstraints = if (isCommunityApi) blockchainEndpointService.getTimeConstraints(request.apiKey) else null

        if (isCommunityApi && timeConstraints?.first?.isAfter(txMeta.time) == true) {
            return FunctionResult.Failure(ApiError.ForbiddenWithMessage("Requested transaction is not available for Community API."))
        }

        return FunctionResult.Success(toBlockchainFullSingleTransactionResponse(txMeta, txBalanceUpdates))
    }

    private suspend fun findTransactionByBlockHashAndTxid(
        db: Database,
        asset: String,
        blockHashSqlValue: String,
        txidSqlValue: String,
    ): BlockchainEndpointUtils.BlockchainTransactionInfoWrapperV2? {
        val transactionsTable = BlockchainEndpointUtils.transactionsTable(db, asset)
        val staleBlocksTableName = BlockchainEndpointUtils.staleBlocksTable(db, asset)
        return db.query(
            """
            SELECT 
                t.*,
                sb.block_height AS stale_block_height
            FROM $transactionsTable t
                LEFT JOIN $staleBlocksTableName sb ON t.block_hash = sb.block_hash
            WHERE t.block_hash=$blockHashSqlValue AND t.hash=$txidSqlValue
            """.trimIndent(),
        ) { query ->
            query.map { rs -> BlockchainEndpointUtils.transactionMapper(rs, asset) }.firstOrNull()
        }
    }
}
