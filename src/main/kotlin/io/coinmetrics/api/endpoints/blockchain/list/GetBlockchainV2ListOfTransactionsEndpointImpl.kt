package io.coinmetrics.api.endpoints.blockchain.list

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.Response
import io.coinmetrics.api.badNextPageToken
import io.coinmetrics.api.endpoints.GetBlockchainV2ListOfTransactionsEndpoint
import io.coinmetrics.api.endpoints.GetBlockchainV2ListOfTransactionsRequest
import io.coinmetrics.api.endpoints.blockchain.BlockchainEndpointUtils
import io.coinmetrics.api.endpoints.blockchain.BlockchainEndpointUtils.staleBlocksTable
import io.coinmetrics.api.endpoints.blockchain.BlockchainEndpointUtils.transactionsTable
import io.coinmetrics.api.models.BlockchainTransactionsResponseV2
import io.coinmetrics.api.models.PagingFrom
import io.coinmetrics.api.persistence.Databases
import io.coinmetrics.api.service.BlockchainEndpointService
import io.coinmetrics.api.utils.CommonEndpointUtils
import io.coinmetrics.api.utils.CommonEndpointUtils.validateMutuallyExclusiveFilters
import io.coinmetrics.api.utils.DataUtils
import io.coinmetrics.api.utils.QueryTextBuilder
import io.coinmetrics.api.utils.RangeQuery
import io.coinmetrics.api.utils.paging.PageToken
import io.coinmetrics.api.utils.paging.PagingUtils
import io.coinmetrics.api.utils.paging.getPage
import io.coinmetrics.atlas.v2.codec.Codec
import io.coinmetrics.databases.Database

class GetBlockchainV2ListOfTransactionsEndpointImpl(
    private val databases: Databases,
    private val blockchainEndpointService: BlockchainEndpointService,
    private val communityKey: String,
) : GetBlockchainV2ListOfTransactionsEndpoint() {
    override suspend fun handle(request: GetBlockchainV2ListOfTransactionsRequest): Response<BlockchainTransactionsResponseV2> {
        val headers =
            blockchainEndpointService
                .validateV2Request<BlockchainTransactionsResponseV2>(
                    request.apiKey,
                    request.asset,
                    request.httpRequest,
                    dbTableNamePartsToBeUsed = listOf("transactions", "stale_blocks"),
                ).getOrElse { return it }

        val internalRequest =
            when (val validationResult = validateAndGetInternalRequest(request)) {
                is FunctionResult.Failure -> return Response.errorResponse(validationResult.value, headers)
                is FunctionResult.Success -> validationResult.value!!
            }

        return when (val result = handleInternal(internalRequest)) {
            is FunctionResult.Success -> Response.successResponse(result.value, headers)
            is FunctionResult.Failure -> Response.errorResponse(result.value, headers)
        }
    }

    /**
     * Validates input parameters and applies restrictions when the API key is community key. Otherwise, it returns the original request unchanged.
     */
    private fun validateAndGetInternalRequest(
        request: GetBlockchainV2ListOfTransactionsRequest,
    ): FunctionResult<ApiError, GetBlockchainV2ListOfTransactionsRequest?> {
        CommonEndpointUtils.validateChainParameter(request.chain)?.let { return FunctionResult.Failure(it) }

        if (request.apiKey == communityKey) {
            CommonEndpointUtils
                .validateSingleParametersForCommunity(
                    "txids" to request.txids,
                    "block_hashes" to request.blockHashes,
                )?.let { return FunctionResult.Failure(it) }

            // Apply restrictions for community API key
            return FunctionResult.Success(
                request.copy(
                    startTime = null,
                    endTime = null,
                    startHeight = null,
                    endHeight = null,
                    chain = "main",
                    startInclusive = true,
                    endInclusive = true,
                    pageSize = minOf(BlockchainEndpointUtils.getCommunityMaxPageSize(), request.pageSize),
                ),
            )
        }
        return FunctionResult.Success(request)
    }

    private suspend fun handleInternal(
        request: GetBlockchainV2ListOfTransactionsRequest,
    ): FunctionResult<ApiError, BlockchainTransactionsResponseV2> {
        val validationResult = validateFilterParameters(request)
        if (validationResult != null) {
            return FunctionResult.Failure(validationResult)
        }

        val initialState =
            request.nextPageToken?.let {
                try {
                    PageToken.BigIntegerAndStringPageToken.parse(
                        pageToken = it,
                        strTransformer = DataUtils.createPageTokenStrTransformer(request.asset, Codec::decodeHash),
                    )
                } catch (e: Exception) {
                    log.error("Can't parse provided next_page_token '{}'.", it, e)
                    return FunctionResult.Failure(badNextPageToken())
                }
            }

        val db = databases.resolveAtlasDatabase(request.asset)
        val rangeQueryFunctionResult = determineRangeQuery(db, request)
        val (rangeQuery, additionalFilter) =
            when (rangeQueryFunctionResult) {
                is FunctionResult.Failure -> return FunctionResult.Failure(rangeQueryFunctionResult.value)
                is FunctionResult.Success -> rangeQueryFunctionResult.value
            }
        if (rangeQuery == null) {
            return FunctionResult.Success(BlockchainTransactionsResponseV2(data = emptyList()))
        }

        val queryTextBuilder =
            when (val queryTextBuilderMaybe = getQueryTextBuilder(db, request)) {
                is FunctionResult.Failure -> return FunctionResult.Failure(queryTextBuilderMaybe.value)
                is FunctionResult.Success -> queryTextBuilderMaybe.value
            }

        val isCommunityApi = request.apiKey == communityKey
        val timeConstraints = if (isCommunityApi) blockchainEndpointService.getTimeConstraints(request.apiKey) else null

        val stream =
            DataUtils
                .createStream(
                    db = db,
                    queryTextBuilder = queryTextBuilder,
                    bufferSize = request.pageSize + 1,
                    keyNames = arrayOf("t.tx_position", "t.hash"),
                    dataMapper = { rs -> BlockchainEndpointUtils.transactionMapper(rs, request.asset, isCommunityApi, timeConstraints) },
                    rangeQuery = rangeQuery,
                    initialState = initialState,
                    stateResolver = {
                        PageToken.BigIntegerAndStringPageToken(
                            it.txPosition,
                            it.txid,
                            DataUtils.createPageTokenStrTransformer(request.asset, Codec::decodeHash),
                        )
                    },
                    streamId = null,
                ).let {
                    if (additionalFilter != null) {
                        it.filter(additionalFilter)
                    } else {
                        it
                    }
                }

        val page =
            @Suppress("DEPRECATION")
            stream.getPage(request.pageSize, request.pagingFrom == PagingFrom.START)
        val nextPageUrl = page.nextPageToken?.let { PagingUtils.createNextPageUrl(request.httpRequest, it) }

        if (isCommunityApi) {
            val isSingleResourceQuery =
                CommonEndpointUtils.getFirstNonEmptyParameter(
                    "block_hashes" to request.blockHashes,
                    "txids" to request.txids,
                ) != null

            if (isSingleResourceQuery && page.items.isNotEmpty() && timeConstraints?.first?.isAfter(page.items[0].time) == true) {
                return FunctionResult.Failure(
                    ApiError.ForbiddenWithMessage("Requested transaction is outside the allowed time range for Community API."),
                )
            }
        }

        var isHiddenElementsForCommunity = false
        return FunctionResult.Success(
            BlockchainTransactionsResponseV2(
                data =
                    page.items.mapNotNull {
                        isHiddenElementsForCommunity = isHiddenElementsForCommunity || it.isRestrictedForCommunity
                        it.transaction
                    },
                nextPageToken = page.nextPageToken,
                nextPageUrl = nextPageUrl,
                // todo: this custom field prevents us from migration to chunked encoding
                warning = if (isHiddenElementsForCommunity) BlockchainEndpointUtils.getWarningObject() else null,
            ),
        )
    }

    private fun getQueryTextBuilder(
        db: Database,
        request: GetBlockchainV2ListOfTransactionsRequest,
    ): FunctionResult<ApiError, QueryTextBuilder> {
        val additionalWhereExpression =
            when {
                request.txids != null && request.txids.isNotEmpty() -> {
                    val decodedTxids =
                        BlockchainEndpointUtils.decodeTxids(request.asset, request.txids).getOrElse {
                            return FunctionResult.Failure(it)
                        }
                    "AND t.hash IN (${DataUtils.convertListOfBytesArraysToSqlCollection(decodedTxids)})"
                }

                request.blockHashes != null && request.blockHashes.isNotEmpty() -> {
                    val decodedBlockHashes =
                        BlockchainEndpointUtils.decodeBlockHashes(request.asset, request.blockHashes).getOrElse {
                            return FunctionResult.Failure(it)
                        }
                    "AND t.block_hash IN (${DataUtils.convertListOfBytesArraysToSqlCollection(decodedBlockHashes)})"
                }

                else -> ""
            }

        val staleBlocksTableName = staleBlocksTable(db, request.asset)
        val includeStaleBlocks = request.chain != "main"
        val staleBlocksFilter = if (includeStaleBlocks) "" else " AND t.block_hash NOT IN (SELECT block_hash FROM $staleBlocksTableName)"
        val staleBlocksSubQuery =
            if (includeStaleBlocks) {
                "(SELECT sb.block_height FROM " +
                    "$staleBlocksTableName sb WHERE sb.block_hash = t.block_hash)"
            } else {
                "NULL"
            }

        val queryTextBuilder: QueryTextBuilder = { filter, limit ->
            val ordering = if (request.pagingFrom == PagingFrom.START) "ASC" else "DESC"
            """
                SELECT
                    t.*,
                    $staleBlocksSubQuery AS stale_block_height
                FROM ${transactionsTable(db, request.asset)} t
                WHERE TRUE $filter $additionalWhereExpression $staleBlocksFilter
                ORDER BY t.tx_position $ordering, t.hash $ordering
                LIMIT $limit;
                """
        }
        return FunctionResult.Success(queryTextBuilder)
    }

    private suspend fun determineRangeQuery(
        db: Database,
        request: GetBlockchainV2ListOfTransactionsRequest,
    ): FunctionResult<
        ApiError,
        out Pair<
            RangeQuery.BigIntegerAndStringRangeQuery?,
            (
                (BlockchainEndpointUtils.BlockchainTransactionInfoWrapperV2) -> Boolean
            )?,
        >,
    > =
        if (request.startTime != null || request.endTime != null) {
            BlockchainEndpointUtils.createRangeQueryFromTimeBoundaries(
                db,
                tableName = transactionsTable(db, request.asset),
                numberColumn = "tx_position",
                startTime = request.startTime,
                endTime = request.endTime,
                startInclusive = request.startInclusive,
                endInclusive = request.endInclusive,
                timezone = request.timezone,
                pagingFrom = request.pagingFrom,
            )
        } else if (request.startHeight != null || request.endHeight != null) {
            BlockchainEndpointUtils.createRangeQueryFromHeightBoundaries(
                db,
                tableName = transactionsTable(db, request.asset),
                sequenceNumberColumn = "tx_position",
                startHeight = request.startHeight,
                endHeight = request.endHeight,
                startInclusive = request.startInclusive,
                endInclusive = request.endInclusive,
                pagingFrom = request.pagingFrom,
            )
        } else {
            DataUtils
                .createBigIntegerAndStringRangeQuery(
                    startBigInteger = null,
                    request.startInclusive,
                    endBigInteger = null,
                    request.endInclusive,
                    request.pagingFrom,
                ).map {
                    it to null
                }
        }

    private fun validateFilterParameters(request: GetBlockchainV2ListOfTransactionsRequest): ApiError? {
        val validateTimeAndHeightResult =
            validateMutuallyExclusiveFilters(
                listOf(
                    "*_time" to (request.startTime to request.endTime),
                    "*_height" to (request.startHeight to request.endHeight),
                ),
            )
        return when (validateTimeAndHeightResult) {
            is FunctionResult.Failure -> validateTimeAndHeightResult.value
            is FunctionResult.Success -> {
                val validateBlockHashesAndTransactionHashesResult =
                    validateMutuallyExclusiveFilters(
                        listOf(
                            "transaction_ids" to request.txids,
                            "block_hashes" to request.blockHashes,
                        ),
                    )
                when (validateBlockHashesAndTransactionHashesResult) {
                    is FunctionResult.Failure -> validateBlockHashesAndTransactionHashesResult.value
                    is FunctionResult.Success -> null
                }
            }
        }
    }
}
