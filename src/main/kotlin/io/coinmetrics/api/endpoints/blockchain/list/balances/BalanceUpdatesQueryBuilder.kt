package io.coinmetrics.api.endpoints.blockchain.list.balances

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.endpoints.GetBlockchainV2ListOfBalanceUpdatesRequest
import io.coinmetrics.api.endpoints.blockchain.BlockchainEndpointUtils
import io.coinmetrics.api.models.PagingFrom
import io.coinmetrics.api.utils.DataUtils
import io.coinmetrics.api.utils.QueryTextBuilder
import io.coinmetrics.api.utils.RangeQuery

sealed class BalanceUpdatesQueryBuilder(
    val request: GetBlockchainV2ListOfBalanceUpdatesRequest,
    val rangeQuery: RangeQuery.BigIntegerAndStringRangeQuery,
    val keyNames: Array<String>,
    val tableNameResolver: () -> Pair<String, String>,
) {
    class Default(
        request: GetBlockchainV2ListOfBalanceUpdatesRequest,
        rangeQuery: RangeQuery.BigIntegerAndStringRangeQuery,
        keyNames: Array<String>,
        tableNameResolver: () -> Pair<String, String>,
    ) : BalanceUpdatesQueryBuilder(request, rangeQuery, keyNames, tableNameResolver) {
        override fun getAdditionalWhereExpression(): FunctionResult<ApiError, String> {
            val whereExpression =
                listOf(
                    { getTxIdsFilterExpression() },
                    { getBlockHashesFilterExpression() },
                    { getAccountFilterExpression() },
                ).map {
                    it().getOrElse { apiError -> return FunctionResult.Failure(apiError) }
                }.filterNotNull()
                    .firstOrNull() ?: ""
            return FunctionResult.Success(whereExpression)
        }

        override fun getQueryTextBuilder(
            additionalWhereExpression: String,
            subAccountFilterExpression: String,
            withValuesClause: String,
            withValuesJoinExpression: String,
            ordering: String,
        ): FunctionResult<ApiError, QueryTextBuilder> {
            val includeStaleBlocks = request.chain != "main"
            val hasAccounts = request.accounts?.isNotEmpty() == true
            val hasTxids = request.txids?.isNotEmpty() == true
            val hasBlockHashes = request.blockHashes?.isNotEmpty() == true
            val (balanceUpdatesTableName, staleBlocksTableName) = tableNameResolver()
            return FunctionResult.Success { filter, limit ->

                val staleBlocksFilter =
                    if (includeStaleBlocks) {
                        ""
                    } else {
                        " " +
                            "AND bu.block_hash NOT IN (SELECT block_hash FROM $staleBlocksTableName)"
                    }
                val staleBlocksSubQuery =
                    if (includeStaleBlocks) {
                        "(SELECT sb.block_height " +
                            "FROM $staleBlocksTableName sb WHERE sb.block_hash = bu.block_hash)"
                    } else {
                        "NULL"
                    }

                if (hasAccounts || hasTxids || hasBlockHashes) {
                    """
                    SELECT 
                        bu.*, 
                        $staleBlocksSubQuery AS stale_block_height 
                    FROM $balanceUpdatesTableName bu, (
                        $withValuesClause
                        SELECT bu.*
                        FROM a
                            JOIN LATERAL ( 
                                SELECT chain_sequence_number, block_hash 
                                FROM $balanceUpdatesTableName bu
                                WHERE $withValuesJoinExpression $filter $subAccountFilterExpression $staleBlocksFilter
                                ORDER BY bu.chain_sequence_number $ordering, bu.block_hash $ordering
                                LIMIT $limit
                            ) bu ON true
                        WHERE TRUE $filter
                        ORDER BY bu.chain_sequence_number $ordering, bu.block_hash $ordering
                        LIMIT $limit
                    ) AS seq_block_ordered 
                    WHERE bu.chain_sequence_number = seq_block_ordered.chain_sequence_number AND bu.block_hash = seq_block_ordered.block_hash 
                    ORDER BY bu.chain_sequence_number $ordering, bu.block_hash $ordering
                    LIMIT $limit;
                    """.trimIndent()
                } else {
                    """
                    SELECT
                        bu.*,
                        $staleBlocksSubQuery AS stale_block_height
                    FROM $balanceUpdatesTableName bu
                    WHERE TRUE $filter $additionalWhereExpression $subAccountFilterExpression $staleBlocksFilter
                    ORDER BY bu.chain_sequence_number $ordering, bu.block_hash $ordering
                    LIMIT $limit;
                    """.trimIndent()
                }
            }
        }
    }

    class WithLimitPerAccount(
        request: GetBlockchainV2ListOfBalanceUpdatesRequest,
        rangeQuery: RangeQuery.BigIntegerAndStringRangeQuery,
        keyNames: Array<String>,
        tableNameResolver: () -> Pair<String, String>,
    ) : BalanceUpdatesQueryBuilder(request, rangeQuery, keyNames, tableNameResolver) {
        override fun getAdditionalWhereExpression(): FunctionResult<ApiError, String> {
            val whereExpression =
                listOf(
                    { getTxIdsFilterExpression() },
                    { getBlockHashesFilterExpression() },
                ).map {
                    it().getOrElse { apiError -> return FunctionResult.Failure(apiError) }
                }.filterNotNull()
                    .firstOrNull() ?: ""
            return FunctionResult.Success(whereExpression)
        }

        override fun getQueryTextBuilder(
            additionalWhereExpression: String,
            subAccountFilterExpression: String,
            withValuesClause: String,
            withValuesJoinExpression: String,
            ordering: String,
        ): FunctionResult<ApiError, QueryTextBuilder> {
            val limitPerAccount = request.limitPerAccount ?: error("Limit per account must be specified.")
            val accounts = request.accounts?.distinct() ?: error("Accounts must be specified.")
            val decodedAccounts =
                BlockchainEndpointUtils
                    .decodeAccounts(request.asset, accounts)
                    .getOrElse {
                        return FunctionResult.Failure(it)
                    }.map { DataUtils.convertByteArrayToSqlParam(it) }
            val includeStaleBlocks = request.chain != "main"
            val (balanceUpdatesTableName, staleBlocksTableName) = tableNameResolver()
            val subQueryFilter = DataUtils.createWhereExpression(rangeQuery, arrayOf("bu.chain_sequence_number", "bu.block_hash"))
            return FunctionResult.Success { filter, limit ->

                val innerQuery =
                    if (includeStaleBlocks) {
                        """
                    SELECT 
                        bu.*, 
                        (SELECT sb.block_height FROM $staleBlocksTableName sb WHERE sb.block_hash = bu.block_hash) AS stale_block_height
                    FROM $balanceUpdatesTableName bu
                    WHERE TRUE
                        $subQueryFilter $additionalWhereExpression $subAccountFilterExpression
                        AND bu.account = a.account::bytea
                    ORDER BY bu.chain_sequence_number $ordering, bu.block_hash $ordering
                    LIMIT $limitPerAccount
                    """
                    } else {
                        """
                    SELECT 
                        bu.*, 
                        NULL AS stale_block_height
                    FROM $balanceUpdatesTableName bu
                    WHERE TRUE
                        $subQueryFilter $additionalWhereExpression $subAccountFilterExpression
                        AND bu.account = a.account::bytea
                        AND bu.block_hash NOT IN (SELECT block_hash FROM $staleBlocksTableName)
                    ORDER BY bu.chain_sequence_number $ordering, bu.block_hash $ordering
                    LIMIT $limitPerAccount
                    """
                    }

                """
                WITH a(account) AS (VALUES ${decodedAccounts.joinToString(",") { "($it)" }})
                SELECT bu.*
                FROM a
                JOIN LATERAL ( $innerQuery ) bu ON true
                WHERE TRUE $filter
                ORDER BY bu.chain_sequence_number $ordering, bu.block_hash $ordering
                LIMIT $limit;
                """.trimIndent()
            }
        }
    }

    fun queryTextBuilder(): FunctionResult<ApiError, QueryTextBuilder> {
        val subAccountFilterExpression =
            request.subAccounts
                ?.takeIf { it.isNotEmpty() }
                ?.let { subAccounts ->
                    BlockchainEndpointUtils.decodeSubAccounts(request.asset, subAccounts).getOrElse { return FunctionResult.Failure(it) }
                }?.let { "AND bu.sub_account IN (${DataUtils.convertListOfBytesArraysToSqlCollection(it, truncateToSize = 1000)})" }
                ?: ""

        val additionalWhereExpression =
            getAdditionalWhereExpression().getOrElse {
                return FunctionResult.Failure(it)
            }

        val withValuesClause =
            getWithValuesClause().getOrElse {
                return FunctionResult.Failure(it)
            }

        val withValuesJoinExpression = getWithValuesJoinExpression()

        val ordering = if (request.pagingFrom == PagingFrom.START) "ASC" else "DESC"
        return getQueryTextBuilder(
            subAccountFilterExpression = subAccountFilterExpression,
            additionalWhereExpression = additionalWhereExpression,
            withValuesClause = withValuesClause,
            withValuesJoinExpression = withValuesJoinExpression,
            ordering = ordering,
        )
    }

    abstract fun getAdditionalWhereExpression(): FunctionResult<ApiError, String>

    abstract fun getQueryTextBuilder(
        additionalWhereExpression: String,
        subAccountFilterExpression: String,
        withValuesClause: String,
        withValuesJoinExpression: String,
        ordering: String,
    ): FunctionResult<ApiError, QueryTextBuilder>

    protected fun getTxIdsFilterExpression(): FunctionResult<ApiError, String?> {
        val filterExpression =
            if (request.txids != null && request.txids.isNotEmpty()) {
                val decodedTxids =
                    BlockchainEndpointUtils.decodeTxids(request.asset, request.txids).getOrElse {
                        return FunctionResult.Failure(it)
                    }
                "AND bu.tx_hash IN (${DataUtils.convertListOfBytesArraysToSqlCollection(decodedTxids)})"
            } else {
                null
            }
        return FunctionResult.Success(filterExpression)
    }

    protected fun getBlockHashesFilterExpression(): FunctionResult<ApiError, String?> {
        val filterExpression =
            if (request.blockHashes != null && request.blockHashes.isNotEmpty()) {
                val decodedBlockHashes =
                    BlockchainEndpointUtils.decodeBlockHashes(request.asset, request.blockHashes).getOrElse {
                        return FunctionResult.Failure(it)
                    }
                "AND bu.block_hash IN (${DataUtils.convertListOfBytesArraysToSqlCollection(decodedBlockHashes)})"
            } else {
                null
            }
        return FunctionResult.Success(filterExpression)
    }

    protected fun getAccountFilterExpression(): FunctionResult<ApiError, String?> {
        val accounts = request.accounts?.distinct()
        val filterExpression =
            if (accounts != null && accounts.isNotEmpty()) {
                val decodedAccounts =
                    BlockchainEndpointUtils.decodeAccounts(request.asset, accounts).getOrElse {
                        return FunctionResult.Failure(it)
                    }
                "AND bu.account IN (${DataUtils.convertListOfBytesArraysToSqlCollection(decodedAccounts)})"
            } else {
                null
            }
        return FunctionResult.Success(filterExpression)
    }

    protected fun getWithValuesClause(): FunctionResult<ApiError, String> {
        val clause =
            listOf(
                { getTxIdsWithValuesClause() },
                { getBlockHashesWithValuesClause() },
                { getAccountWithValuesClause() },
            ).map {
                it().getOrElse { apiError -> return FunctionResult.Failure(apiError) }
            }.filterNotNull()
                .firstOrNull() ?: ""
        return FunctionResult.Success(clause)
    }

    protected fun getBlockHashesWithValuesClause(): FunctionResult<ApiError, String?> =
        getWithValuesClause("block_hash", request.blockHashes, BlockchainEndpointUtils::decodeBlockHashes)

    protected fun getTxIdsWithValuesClause(): FunctionResult<ApiError, String?> =
        getWithValuesClause("tx_hash", request.txids, BlockchainEndpointUtils::decodeTxids)

    protected fun getAccountWithValuesClause(): FunctionResult<ApiError, String?> =
        getWithValuesClause("account", request.accounts, BlockchainEndpointUtils::decodeAccounts)

    private fun getWithValuesClause(
        fieldName: String,
        elements: List<String>?,
        decoderFunction: (asset: String, elements: List<String>) -> FunctionResult<ApiError, List<ByteArray>>,
    ): FunctionResult<ApiError, String?> {
        if (elements?.isNotEmpty() == true) {
            val decodedElements =
                decoderFunction(request.asset, elements)
                    .getOrElse {
                        return FunctionResult.Failure(it)
                    }.map { DataUtils.convertByteArrayToSqlParam(it) }

            val withValuesClause = "WITH a($fieldName) AS ( VALUES ${decodedElements.joinToString(",") { "($it)" }} )"

            return FunctionResult.Success(withValuesClause)
        }
        return FunctionResult.Success(null)
    }

    protected fun getWithValuesJoinExpression(): String =
        listOf(
            if (request.txids?.isNotEmpty() == true) "bu.tx_hash = a.tx_hash::bytea" else null,
            if (request.blockHashes?.isNotEmpty() == true) "bu.block_hash = a.block_hash::bytea" else null,
            if (request.accounts?.isNotEmpty() == true) "bu.account = a.account::bytea" else null,
        ).filterNotNull().firstOrNull() ?: ""
}

fun balanceUpdatesQueryBuilder(
    request: GetBlockchainV2ListOfBalanceUpdatesRequest,
    rangeQuery: RangeQuery.BigIntegerAndStringRangeQuery,
    keyNames: Array<String>,
    tableNameResolver: () -> Pair<String, String>,
): FunctionResult<ApiError, QueryTextBuilder> =
    when {
        request.limitPerAccount != null && !request.accounts.isNullOrEmpty() -> {
            BalanceUpdatesQueryBuilder.WithLimitPerAccount(request, rangeQuery, keyNames, tableNameResolver)
        }

        else -> BalanceUpdatesQueryBuilder.Default(request, rangeQuery, keyNames, tableNameResolver)
    }.queryTextBuilder()
