package io.coinmetrics.api.endpoints.blockchain.list

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.Response
import io.coinmetrics.api.badNextPageToken
import io.coinmetrics.api.endpoints.GetBlockchainV2ListOfBlocksEndpoint
import io.coinmetrics.api.endpoints.GetBlockchainV2ListOfBlocksRequest
import io.coinmetrics.api.endpoints.blockchain.BlockchainEndpointUtils
import io.coinmetrics.api.endpoints.blockchain.BlockchainEndpointUtils.blocksTable
import io.coinmetrics.api.endpoints.blockchain.BlockchainEndpointUtils.staleBlocksTable
import io.coinmetrics.api.models.BlockchainBlocksResponseV2
import io.coinmetrics.api.models.PagingFrom
import io.coinmetrics.api.persistence.Databases
import io.coinmetrics.api.service.BlockchainEndpointService
import io.coinmetrics.api.utils.CommonEndpointUtils
import io.coinmetrics.api.utils.CommonEndpointUtils.validateMutuallyExclusiveFilters
import io.coinmetrics.api.utils.DataUtils
import io.coinmetrics.api.utils.QueryTextBuilder
import io.coinmetrics.api.utils.RangeQuery
import io.coinmetrics.api.utils.WithTime
import io.coinmetrics.api.utils.paging.PageToken
import io.coinmetrics.api.utils.paging.PagingUtils
import io.coinmetrics.api.utils.paging.getPage
import io.coinmetrics.atlas.v2.codec.Codec
import io.coinmetrics.databases.Database

class GetBlockchainV2ListOfBlocksEndpointImpl(
    private val databases: Databases,
    private val blockchainEndpointService: BlockchainEndpointService,
    private val communityKey: String,
) : GetBlockchainV2ListOfBlocksEndpoint() {
    override suspend fun handle(request: GetBlockchainV2ListOfBlocksRequest): Response<BlockchainBlocksResponseV2> {
        val headers =
            blockchainEndpointService
                .validateV2Request<BlockchainBlocksResponseV2>(
                    request.apiKey,
                    request.asset,
                    request.httpRequest,
                    dbTableNamePartsToBeUsed = listOf("blocks", "stale_blocks"),
                ).getOrElse { return it }

        val internalRequest =
            when (val validationResult = validateAndGetInternalRequest(request)) {
                is FunctionResult.Failure -> return Response.errorResponse(validationResult.value, headers)
                is FunctionResult.Success -> validationResult.value!!
            }

        return when (val result = handleInternal(internalRequest)) {
            is FunctionResult.Success -> Response.successResponse(result.value, headers)
            is FunctionResult.Failure -> Response.errorResponse(result.value, headers)
        }
    }

    /**
     * Validates input parameters and applies restrictions when the API key is community key. Otherwise, it returns the original request unchanged.
     */
    private fun validateAndGetInternalRequest(
        request: GetBlockchainV2ListOfBlocksRequest,
    ): FunctionResult<ApiError, GetBlockchainV2ListOfBlocksRequest?> {
        CommonEndpointUtils.validateChainParameter(request.chain)?.let { return FunctionResult.Failure(it) }

        if (request.apiKey == communityKey) {
            CommonEndpointUtils
                .validateSingleParametersForCommunity(
                    "block_hashes" to request.blockHashes,
                    "heights" to request.heights,
                )?.let { return FunctionResult.Failure(it) }

            // Apply restrictions for community API key
            return FunctionResult.Success(
                request.copy(
                    startTime = null,
                    endTime = null,
                    startHeight = null,
                    endHeight = null,
                    chain = "main",
                    startInclusive = true,
                    endInclusive = true,
                    pageSize = minOf(BlockchainEndpointUtils.getCommunityMaxPageSize(), request.pageSize),
                ),
            )
        }
        return FunctionResult.Success(request)
    }

    private suspend fun handleInternal(request: GetBlockchainV2ListOfBlocksRequest): FunctionResult<ApiError, BlockchainBlocksResponseV2> {
        val validationResult = validateFilterParameters(request)
        if (validationResult != null) {
            return FunctionResult.Failure(validationResult)
        }

        val initialState =
            request.nextPageToken?.let {
                try {
                    PageToken.BigIntegerAndStringPageToken.parse(
                        pageToken = it,
                        strTransformer = DataUtils.createPageTokenStrTransformer(request.asset, Codec::decodeHash),
                    )
                } catch (e: Exception) {
                    log.error("Can't parse provided next_page_token '{}'.", it, e)
                    return FunctionResult.Failure(badNextPageToken())
                }
            }

        val db = databases.resolveAtlasDatabase(request.asset)
        val rangeQueryFunctionResult = determineRangeQuery(db, request)
        val (rangeQuery, additionalFilter) =
            when (rangeQueryFunctionResult) {
                is FunctionResult.Failure -> return FunctionResult.Failure(rangeQueryFunctionResult.value)
                is FunctionResult.Success -> rangeQueryFunctionResult.value
            }
        if (rangeQuery == null) {
            return FunctionResult.Success(BlockchainBlocksResponseV2(data = emptyList()))
        }

        val queryTextBuilder =
            when (val queryTextBuilderMaybe = getQueryTextBuilder(db, request)) {
                is FunctionResult.Failure -> return FunctionResult.Failure(queryTextBuilderMaybe.value)
                is FunctionResult.Success -> queryTextBuilderMaybe.value
            }

        val isCommunityApi = request.apiKey == communityKey
        val timeConstraints = if (isCommunityApi) blockchainEndpointService.getTimeConstraints(request.apiKey) else null

        val stream =
            DataUtils
                .createStream(
                    db = db,
                    queryTextBuilder = queryTextBuilder,
                    bufferSize = request.pageSize + 1,
                    keyNames = arrayOf("b.height", "b.hash"),
                    dataMapper = { rs -> BlockchainEndpointUtils.blockMapper(rs, request.asset, isCommunityApi, timeConstraints) },
                    rangeQuery = rangeQuery,
                    initialState = initialState,
                    stateResolver = {
                        PageToken.BigIntegerAndStringPageToken(
                            it.height,
                            it.blockHash,
                            DataUtils.createPageTokenStrTransformer(request.asset, Codec::decodeHash),
                        )
                    },
                    streamId = null,
                ).let {
                    if (additionalFilter != null) {
                        it.filter(additionalFilter)
                    } else {
                        it
                    }
                }

        val page =
            @Suppress("DEPRECATION")
            stream.getPage(request.pageSize, request.pagingFrom == PagingFrom.START)
        val nextPageUrl = page.nextPageToken?.let { PagingUtils.createNextPageUrl(request.httpRequest, it) }

        if (isCommunityApi) {
            val isSingleResourceQuery =
                CommonEndpointUtils.getFirstNonEmptyParameter(
                    "block_hashes" to request.blockHashes,
                    "heights" to request.heights,
                ) != null

            if (isSingleResourceQuery && page.items.isNotEmpty() && timeConstraints?.first?.isAfter(page.items[0].time) == true) {
                return FunctionResult.Failure(
                    ApiError.ForbiddenWithMessage("Requested block is outside the allowed time range for Community API."),
                )
            }
        }

        var isHiddenElementsForCommunity = false
        return FunctionResult.Success(
            BlockchainBlocksResponseV2(
                data =
                    page.items.mapNotNull {
                        isHiddenElementsForCommunity = isHiddenElementsForCommunity || it.isRestrictedForCommunity
                        it.block
                    },
                nextPageToken = page.nextPageToken,
                nextPageUrl = nextPageUrl,
                // todo: this custom field prevent us from migration to chunked encoding
                warning = if (isHiddenElementsForCommunity) BlockchainEndpointUtils.getWarningObject() else null,
            ),
        )
    }

    private fun getQueryTextBuilder(
        db: Database,
        request: GetBlockchainV2ListOfBlocksRequest,
    ): FunctionResult<ApiError, QueryTextBuilder> {
        val additionalWhereExpression =
            when {
                request.blockHashes != null && request.blockHashes.isNotEmpty() -> {
                    val decodedBlockHashes =
                        BlockchainEndpointUtils.decodeBlockHashes(request.asset, request.blockHashes).getOrElse {
                            return FunctionResult.Failure(it)
                        }
                    "AND b.hash IN (${DataUtils.convertListOfBytesArraysToSqlCollection(decodedBlockHashes)})"
                }

                request.heights != null && request.heights.isNotEmpty() -> {
                    val params =
                        request.heights
                            .map {
                                it.toIntOrNull() ?: return FunctionResult.Failure(ApiError.BadParameter("heights"))
                            }.joinToString(separator = ",")
                    "AND b.height IN ($params)"
                }

                else -> ""
            }

        val blocksTableName = blocksTable(db, request.asset)
        val staleBlocksTableName = staleBlocksTable(db, request.asset)

        val includeStaleBlocks = request.chain != "main"
        val staleBlocksFilter = if (includeStaleBlocks) "" else " AND b.hash NOT IN (SELECT block_hash FROM $staleBlocksTableName)"
        val staleBlocksSubQuery =
            if (includeStaleBlocks) {
                "(SELECT sb.block_height FROM $staleBlocksTableName " +
                    "sb WHERE sb.block_hash = b.hash)"
            } else {
                "NULL"
            }

        return FunctionResult.Success { filter, limit ->
            val ordering = if (request.pagingFrom == PagingFrom.START) "ASC" else "DESC"
            """
            SELECT
                encode(nonce, 'hex') AS nonce_hexed, 
                encode(extra_data, 'hex') AS extra_data_hexed, 
                $staleBlocksSubQuery AS stale_block_height,
                b.*
            FROM $blocksTableName b
            WHERE TRUE $filter $additionalWhereExpression $staleBlocksFilter
            ORDER BY b.height $ordering, b.hash $ordering
            LIMIT $limit
            """
        }
    }

    private fun validateFilterParameters(request: GetBlockchainV2ListOfBlocksRequest): ApiError? {
        val validateTimeAndHeightResult =
            validateMutuallyExclusiveFilters(
                listOf(
                    "*_time" to (request.startTime to request.endTime),
                    "*_height" to (request.startHeight to request.endHeight),
                ),
            )
        return when (validateTimeAndHeightResult) {
            is FunctionResult.Failure -> validateTimeAndHeightResult.value
            is FunctionResult.Success -> {
                val validateBlockHashesAndHeightsResult =
                    validateMutuallyExclusiveFilters(
                        listOf(
                            "block_hashes" to request.blockHashes,
                            "heights" to request.heights,
                        ),
                    )
                when (validateBlockHashesAndHeightsResult) {
                    is FunctionResult.Failure -> validateBlockHashesAndHeightsResult.value
                    is FunctionResult.Success -> null
                }
            }
        }
    }

    private suspend fun determineRangeQuery(
        db: Database,
        request: GetBlockchainV2ListOfBlocksRequest,
    ): FunctionResult<ApiError, out Pair<RangeQuery.BigIntegerAndStringRangeQuery?, ((WithTime) -> Boolean)?>> {
        return if (request.startTime != null || request.endTime != null) {
            BlockchainEndpointUtils.createRangeQueryFromTimeBoundaries(
                db,
                tableName = blocksTable(db, request.asset),
                numberColumn = "height",
                startTime = request.startTime,
                endTime = request.endTime,
                startInclusive = request.startInclusive,
                endInclusive = request.endInclusive,
                timezone = request.timezone,
                pagingFrom = request.pagingFrom,
            )
        } else {
            val startHeight = request.startHeight
            val endHeight = request.endHeight
            if (startHeight != null && endHeight != null && startHeight > endHeight) {
                return FunctionResult.Failure(ApiError.BadParameter(name = "start_height", "Start height is less than end height."))
            }
            DataUtils
                .createBigIntegerAndStringRangeQuery(
                    request.startHeight?.toBigInteger(),
                    request.startInclusive,
                    request.endHeight?.toBigInteger(),
                    request.endInclusive,
                    request.pagingFrom,
                ).map { it to null }
        }
    }
}
