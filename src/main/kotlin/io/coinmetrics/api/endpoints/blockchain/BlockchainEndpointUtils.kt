package io.coinmetrics.api.endpoints.blockchain

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.models.BlockchainBalanceUpdate
import io.coinmetrics.api.models.BlockchainBalanceUpdateSubAccount
import io.coinmetrics.api.models.BlockchainBalanceUpdateV2
import io.coinmetrics.api.models.BlockchainBlockInfo
import io.coinmetrics.api.models.BlockchainBlockInfoV2
import io.coinmetrics.api.models.BlockchainFullSingleTransactionResponseV2
import io.coinmetrics.api.models.BlockchainTransactionBalanceUpdateV2
import io.coinmetrics.api.models.BlockchainTransactionInfo
import io.coinmetrics.api.models.BlockchainTransactionInfoV2
import io.coinmetrics.api.models.PagingFrom
import io.coinmetrics.api.models.WarningObject
import io.coinmetrics.api.toFailure
import io.coinmetrics.api.toSuccess
import io.coinmetrics.api.utils.CommonUtils
import io.coinmetrics.api.utils.DataUtils
import io.coinmetrics.api.utils.RangeQuery
import io.coinmetrics.api.utils.SqlUtils
import io.coinmetrics.api.utils.TimeUtils
import io.coinmetrics.api.utils.WithHeight
import io.coinmetrics.api.utils.WithTime
import io.coinmetrics.atlas.v2.codec.Codec
import io.coinmetrics.databases.Database
import io.coinmetrics.databases.getIntOrNull
import io.coinmetrics.databases.getLongOrNull
import io.coinmetrics.jobs.networkdata.ApiNdJobs
import java.math.BigInteger
import java.sql.ResultSet
import java.time.Instant

object BlockchainEndpointUtils {
    val transactionMapper = { rs: ResultSet ->
        val time = rs.getTimestamp("consensus_time").toInstant()
        val timeFormatted = TimeUtils.dateTimeFormatter.format(time)

        BlockchainTransactionInfoWrapper(
            minChainSequenceNumber = rs.getLong("min_chain_sequence_number").toBigInteger(),
            time = time,
            transaction =
                BlockchainTransactionInfo(
                    nBalanceUpdates = rs.getInt("n_balance_updates").toString(),
                    blockHash = rs.getString("block_hash"),
                    height = rs.getInt("block_height").toString(),
                    consensusTime = timeFormatted,
                    amount = CommonUtils.formatBigDecimal(rs.getBigDecimal("amount")),
                    maxChainSequenceNumber = rs.getLong("max_chain_sequence_number").toString(),
                    minChainSequenceNumber = rs.getLong("min_chain_sequence_number").toString(),
                    transactionHash = rs.getString("hash"),
                ),
        )
    }

    val balanceUpdateMapper = { rs: ResultSet ->
        val time = rs.getTimestamp("consensus_time").toInstant()
        val timeFormatted = TimeUtils.dateTimeFormatter.format(time)

        BlockchainBalanceUpdateWrapper(
            chainSequenceNumber = rs.getLong("chain_sequence_number").toBigInteger(),
            time = time,
            balanceUpdate =
                BlockchainBalanceUpdate(
                    transactionHash = rs.getString("tx_hash"),
                    consensusTime = timeFormatted,
                    height = rs.getInt("block_height").toString(),
                    blockHash = rs.getString("block_hash"),
                    account = rs.getString("account"),
                    accountCreationHeight = rs.getInt("account_creation_height").toString(),
                    chainSequenceNumber = rs.getLong("chain_sequence_number").toString(),
                    change = CommonUtils.formatBigDecimal(rs.getBigDecimal("change")),
                    newBalance = CommonUtils.formatBigDecimal(rs.getBigDecimal("new_balance")),
                    previousBalance = CommonUtils.formatBigDecimal(rs.getBigDecimal("previous_balance")),
                    previousChainSequenceNumber =
                        rs.getLong("previous_chain_sequence_number").let {
                            if (rs.wasNull()) {
                                null
                            } else {
                                it.toString()
                            }
                        },
                    previousCreditHeight =
                        rs.getInt("previous_credit_height").let {
                            if (rs.wasNull()) {
                                null
                            } else {
                                it.toString()
                            }
                        },
                    previousDebitHeight =
                        rs.getInt("previous_debit_height").let {
                            if (rs.wasNull()) {
                                null
                            } else {
                                it.toString()
                            }
                        },
                    previousNCredits = rs.getLong("previous_n_credits").toString(),
                    previousNDebits = rs.getLong("previous_n_debits").toString(),
                    transactionSequenceNumber = rs.getInt("tx_sequence_number").toString(),
                ),
        )
    }

    val blockMapper = { rs: ResultSet ->
        val time = rs.getTimestamp("consensus_time").toInstant()
        val timeFormatted = TimeUtils.dateTimeFormatter.format(time)

        val minerTime = rs.getTimestamp("miner_time").toInstant()
        val minerTimeFormatted = TimeUtils.dateTimeFormatter.format(minerTime)

        val height = rs.getInt("height").toBigInteger()

        BlockchainBlockInfoWrapper(
            height = height,
            time = time,
            block =
                BlockchainBlockInfo(
                    parentBlockHash = rs.getString("parent"),
                    blockHash = rs.getString("hash"),
                    minerTime = minerTimeFormatted,
                    consensusTime = timeFormatted,
                    height = height.toString(),
                    consensusSize =
                        rs.getLong("consensus_size").let {
                            if (rs.wasNull()) {
                                null
                            } else {
                                it.toString()
                            }
                        },
                    consensusSizeLimit =
                        rs.getLong("consensus_size_limit").let {
                            if (rs.wasNull()) {
                                null
                            } else {
                                it.toString()
                            }
                        },
                    difficulty = rs.getBigDecimal("difficulty")?.let { CommonUtils.formatBigDecimal(it) },
                    physicalSize =
                        rs.getLong("physical_size").let {
                            if (rs.wasNull()) {
                                null
                            } else {
                                it.toString()
                            }
                        },
                    nTransactions = rs.getInt("n_transactions").toString(),
                    nBalanceUpdates = rs.getInt("n_balance_updates").toString(),
                ),
        )
    }

    fun transactionMapper(
        rs: ResultSet,
        asset: String,
        isCommunityApi: Boolean = false,
        timeConstraints: Pair<Instant?, Instant?>? = null,
    ): BlockchainTransactionInfoWrapperV2 {
        val consensusTime = rs.getTimestamp("consensus_time").toInstant()
        val consensusTimeFormatted = TimeUtils.dateTimeFormatter.format(consensusTime)
        val txid = Codec.encodeHash(asset, rs.getBytes("hash"))
        val height = rs.getInt("block_height").toString()
        val blockHash = Codec.encodeHash(asset, rs.getBytes("block_hash"))
        val txPosition = rs.getLong("tx_position").toBigInteger()

        val isRestrictedForCommunity = isCommunityApi && timeConstraints?.first?.isAfter(consensusTime) == true

        return if (isRestrictedForCommunity) {
            BlockchainTransactionInfoWrapperV2(
                txPosition = txPosition,
                time = consensusTime,
                txid = txid,
                height = height,
                blockHash = blockHash,
                transaction = null,
                isRestrictedForCommunity = true,
            )
        } else {
            BlockchainTransactionInfoWrapperV2(
                txPosition = txPosition,
                time = consensusTime,
                txid = txid,
                height = height,
                blockHash = blockHash,
                transaction =
                    BlockchainTransactionInfoV2(
                        nBalanceUpdates = rs.getInt("n_balance_updates").toString(),
                        blockHash = blockHash,
                        height = height,
                        consensusTime = consensusTimeFormatted,
                        minerTime = rs.getTimestamp("miner_time")?.let { TimeUtils.dateTimeFormatter.format(it.toInstant()) },
                        amount = rs.getBigDecimal("amount")?.let { CommonUtils.formatBigDecimal(it) } ?: "0",
                        maxChainSequenceNumber = rs.getLongOrNull("max_chain_sequence_number")?.toString(),
                        minChainSequenceNumber = rs.getLongOrNull("min_chain_sequence_number")?.toString(),
                        txid = txid,
                        txPosition = txPosition.toString(),
                        version = rs.getLongOrNull("version")?.toString(),
                        physicalSize = rs.getLongOrNull("physical_size")?.toString(),
                        consensusSize = rs.getLongOrNull("consensus_size")?.toString(),
                        fee = rs.getBigDecimal("fee")?.let { CommonUtils.formatBigDecimal(it) },
                        stale = rs.getIntOrNull("stale_block_height")?.let { "true" },
                    ),
            )
        }
    }

    fun balanceUpdateMapper(
        rs: ResultSet,
        asset: String,
        includeSubAccount: Boolean,
        isCommunityApi: Boolean = false,
        timeConstraints: Pair<Instant?, Instant?>? = null,
    ): BlockchainBalanceUpdateWrapperV2 {
        val chainSequenceNumber = rs.getLong("chain_sequence_number")
        val time = rs.getTimestamp("consensus_time").toInstant()
        val timeFormatted = TimeUtils.dateTimeFormatter.format(time)
        val height = rs.getInt("block_height").toString()
        val blockHash = Codec.encodeHash(asset, rs.getBytes("block_hash"))
        val txid = rs.getBytes("tx_hash")?.let { Codec.encodeHash(asset, it) }

        val isRestrictedForCommunity = isCommunityApi && timeConstraints?.first?.isAfter(time) == true

        val subAccountString =
            rs
                .getBytes("sub_account")
                ?.let { Codec.encodeSubAccount(asset, it).toString() }
                ?.takeIf { it != "null" }

        val subAccount =
            if (includeSubAccount && !subAccountString.isNullOrBlank()) {
                BlockchainBalanceUpdateSubAccount(
                    previousBalance = rs.getBigDecimal("previous_sub_account_balance")?.let { CommonUtils.formatBigDecimal(it) },
                    newBalance = rs.getBigDecimal("new_sub_account_balance")?.let { CommonUtils.formatBigDecimal(it) },
                    subAccount = subAccountString,
                    nDebits = rs.getLongOrNull("n_sub_account_debits")?.toString(),
                    nCredits = rs.getLongOrNull("n_sub_account_credits")?.toString(),
                    previousCreditHeight = rs.getIntOrNull("previous_sub_account_credit_height")?.toString(),
                    previousDebitHeight = rs.getIntOrNull("previous_sub_account_debit_height")?.toString(),
                    previousChainSequenceNumber = rs.getLongOrNull("previous_sub_account_chain_sequence_number")?.toString(),
                    totalReceived = rs.getBigDecimal("total_received_sub_account")?.let { CommonUtils.formatBigDecimal(it) },
                    totalSent = rs.getBigDecimal("total_sent_sub_account")?.let { CommonUtils.formatBigDecimal(it) },
                    creationHeight = rs.getIntOrNull("sub_account_creation_height")?.toString(),
                )
            } else {
                null
            }

        return if (isRestrictedForCommunity) {
            BlockchainBalanceUpdateWrapperV2(
                chainSequenceNumber = chainSequenceNumber.toBigInteger(),
                time = time,
                height = height,
                blockHash = blockHash,
                txid = txid,
                balanceUpdate = null,
                isRestrictedForCommunity = true,
            )
        } else {
            BlockchainBalanceUpdateWrapperV2(
                chainSequenceNumber = chainSequenceNumber.toBigInteger(),
                time = time,
                height = height,
                blockHash = blockHash,
                txid = txid,
                balanceUpdate =
                    BlockchainBalanceUpdateV2(
                        txid = txid,
                        consensusTime = timeFormatted,
                        height = height,
                        blockHash = blockHash,
                        account = Codec.encodeAccount(asset, rs.getBytes("account")),
                        accountCreationHeight = rs.getInt("account_creation_height").toString(),
                        chainSequenceNumber = chainSequenceNumber.toString(),
                        change = CommonUtils.formatBigDecimal(rs.getBigDecimal("change")),
                        newBalance = CommonUtils.formatBigDecimal(rs.getBigDecimal("new_balance")),
                        previousBalance = CommonUtils.formatBigDecimal(rs.getBigDecimal("previous_balance")),
                        previousChainSequenceNumber = rs.getLongOrNull("previous_chain_sequence_number")?.toString(),
                        previousCreditHeight = rs.getIntOrNull("previous_credit_height")?.toString(),
                        previousDebitHeight = rs.getIntOrNull("previous_debit_height")?.toString(),
                        nCredits = rs.getLong("n_credits").toString(),
                        nDebits = rs.getLong("n_debits").toString(),
                        transactionSequenceNumber = rs.getInt("tx_sequence_number").toString(),
                        credit = rs.getBoolean("is_credit"),
                        totalReceived = CommonUtils.formatBigDecimal(rs.getBigDecimal("total_received")),
                        totalSent = CommonUtils.formatBigDecimal(rs.getBigDecimal("total_sent")),
                        subAccount = subAccount,
                        stale = rs.getIntOrNull("stale_block_height")?.let { "true" },
                    ),
            )
        }
    }

    fun blockMapper(
        rs: ResultSet,
        asset: String,
        isCommunityApi: Boolean = false,
        timeConstraints: Pair<Instant?, Instant?>? = null,
    ): BlockchainBlockInfoWrapperV2 {
        val time = rs.getTimestamp("consensus_time").toInstant()
        val height = rs.getInt("height").toBigInteger()
        val blockHash = Codec.encodeHash(asset, rs.getBytes("hash"))

        val isRestrictedForCommunity = isCommunityApi && timeConstraints?.first?.isAfter(time) == true

        return if (isRestrictedForCommunity) {
            BlockchainBlockInfoWrapperV2(
                height,
                time,
                blockHash,
                null,
                true,
            )
        } else {
            BlockchainBlockInfoWrapperV2(
                height = height,
                time = time,
                blockHash = blockHash,
                block =
                    BlockchainBlockInfoV2(
                        parentBlockHash = rs.getBytes("parent")?.let { Codec.encodeHash(asset, it) },
                        blockHash = blockHash,
                        minerTime = TimeUtils.dateTimeFormatter.format(rs.getTimestamp("miner_time").toInstant()),
                        consensusTime = TimeUtils.dateTimeFormatter.format(time),
                        height = height.toString(),
                        nonce = rs.getString("nonce_hexed"),
                        extraData = rs.getString("extra_data_hexed"),
                        consensusSize = rs.getLongOrNull("consensus_size")?.toString(),
                        consensusSizeLimit = rs.getLongOrNull("consensus_size_limit")?.toString(),
                        difficulty = rs.getBigDecimal("difficulty")?.let { CommonUtils.formatBigDecimal(it) },
                        version = rs.getLongOrNull("version")?.toString(),
                        physicalSize = rs.getLongOrNull("physical_size")?.toString(),
                        nTransactions = rs.getInt("n_transactions").toString(),
                        nBalanceUpdates = rs.getInt("n_balance_updates").toString(),
                        stale = rs.getIntOrNull("stale_block_height")?.let { "true" },
                    ),
            )
        }
    }

    suspend fun findBalanceUpdatesByBlockHashAndTxid(
        db: Database,
        asset: String,
        includeSubAccount: Boolean,
        blockHashSqlValue: String,
        txidSqlValue: String,
    ): List<BlockchainBalanceUpdateWrapperV2> {
        val balanceUpdatesTable = balanceUpdatesTable(db, asset)
        val staleBlocksTable = staleBlocksTable(db, asset)
        return db.query(
            """
            SELECT 
                bu.*,
                sb.block_height AS stale_block_height 
            FROM $balanceUpdatesTable bu
                LEFT JOIN $staleBlocksTable sb ON bu.block_hash = sb.block_hash
            WHERE bu.block_hash=$blockHashSqlValue AND bu.tx_hash=$txidSqlValue
            ORDER BY chain_sequence_number
            """.trimIndent(),
        ) { query ->
            query.map { rs -> balanceUpdateMapper(rs, asset, includeSubAccount) }.toList()
        }
    }

    suspend inline fun <reified T : WithTime> createRangeQueryFromTimeBoundaries(
        db: Database,
        tableName: String,
        timeColumn: String = "consensus_time",
        numberColumn: String = "chain_sequence_number",
        startTime: String?,
        endTime: String?,
        startInclusive: Boolean,
        endInclusive: Boolean,
        timezone: String,
        pageSize: Int,
        pagingFrom: PagingFrom,
    ): FunctionResult<ApiError, Pair<RangeQuery.BigIntegerAndStringRangeQuery?, ((T) -> Boolean)?>> {
        val (startTimeParsed, endTimeParsed) =
            when (
                val result =
                    DataUtils.parseTimeParameters(
                        startTime,
                        startInclusive,
                        endTime,
                        endInclusive,
                        timezone,
                    )
            ) {
                is FunctionResult.Success -> result.value
                is FunctionResult.Failure -> return FunctionResult.Failure(result.value)
            }

        val timeFilter =
            DataUtils.createTimeFilter(
                startTimeParsed,
                startInclusive,
                endTimeParsed,
                endInclusive,
            )
        val additionalFilter: (WithTime) -> Boolean = { timeFilter(it.time) }
        val cornerSequenceNumbers: List<Pair<Instant, BigInteger>> =
            db.query(
                """
                (SELECT * FROM $tableName
                WHERE $timeColumn ${TimeUtils.toSqlCompareExpression(">=", startTimeParsed)}
                ORDER BY $timeColumn, $numberColumn
                LIMIT 1)
                UNION
                (SELECT * FROM $tableName
                WHERE $timeColumn ${TimeUtils.toSqlCompareExpression("<=", endTimeParsed)}
                ORDER BY $timeColumn DESC, $numberColumn DESC
                LIMIT 1);
                """.trimIndent(),
            ) { it.map { rs -> rs.getTimestamp(timeColumn).toInstant() to rs.getLong(numberColumn).toBigInteger() }.toList() }

        val (minCreationTime, minSequenceNumber) =
            cornerSequenceNumbers.minByOrNull { it.second }
                ?: return FunctionResult.Success(null to null)
        val (maxCreationTime, maxSequenceNumber) =
            cornerSequenceNumbers.maxByOrNull { it.second }
                ?: return FunctionResult.Success(null to null)

        val startInclusiveDerived = if (minCreationTime > startTimeParsed) true else startInclusive
        val endInclusiveDerived = if (maxCreationTime < endTimeParsed) true else endInclusive

        // expand range to catch bad miners timestamps
        val startSequenceNumber = minSequenceNumber - BigInteger.TWO
        val endSequenceNumber = maxSequenceNumber + BigInteger.TWO

        return when (
            val result =
                DataUtils.createBigIntegerAndStringRangeQuery(
                    startSequenceNumber,
                    startInclusiveDerived,
                    endSequenceNumber,
                    endInclusiveDerived,
                    pageSize,
                    pagingFrom,
                )
        ) {
            is FunctionResult.Success -> FunctionResult.Success(result.value to additionalFilter)
            is FunctionResult.Failure -> FunctionResult.Failure(result.value)
        }
    }

    suspend inline fun <reified T : WithHeight> createRangeQueryFromHeightBoundaries(
        db: Database,
        tableName: String,
        heightColumn: String = "block_height",
        sequenceNumberColumn: String = "chain_sequence_number",
        startHeight: Long?,
        endHeight: Long?,
        startInclusive: Boolean,
        endInclusive: Boolean,
        pageSize: Int,
        pagingFrom: PagingFrom,
    ): FunctionResult<ApiError, Pair<RangeQuery.BigIntegerAndStringRangeQuery?, ((T) -> Boolean)?>> {
        if (startHeight != null && endHeight != null && startHeight > endHeight) {
            return FunctionResult.Failure(ApiError.BadParameter(name = "start_height", "Start height is less than end height."))
        }

        // Introducing ordering here to handle the situation where several rows can have the same height:
        // height   |   seq_num
        // 123      |   18
        // 123      |   13
        // Having ordering of seq_num based on `inclusive` parameter allows us to filter out unnecessary data.
        val minValueOrdering = if (startInclusive) "ASC" else "DESC"
        val maxValueOrdering = if (endInclusive) "DESC" else "ASC"

        val cornerSequenceNumbers: List<Pair<Int, BigInteger>> =
            db.query(
                """
                (SELECT $heightColumn, $sequenceNumberColumn FROM $tableName
                ${if (startHeight == null) "" else "WHERE $heightColumn >= $startHeight"}
                ORDER BY $heightColumn, $sequenceNumberColumn $minValueOrdering
                LIMIT 1)
                UNION
                (SELECT $heightColumn, $sequenceNumberColumn FROM $tableName
                ${if (endHeight == null) "" else "WHERE $heightColumn <= $endHeight"}
                ORDER BY $heightColumn DESC, $sequenceNumberColumn $maxValueOrdering
                LIMIT 1);
                """.trimIndent(),
            ) { it.map { rs -> rs.getInt(heightColumn) to rs.getLong(sequenceNumberColumn).toBigInteger() }.toList() }

        val (minHeight, minSequenceNumber) =
            cornerSequenceNumbers
                .minByOrNull { (_, sequenceNumber) -> sequenceNumber }
                ?.takeIf { (height, _) -> startHeight == null || height >= startHeight }
                ?: return FunctionResult.Success(null to null)
        val (maxHeight, maxSequenceNumber) =
            cornerSequenceNumbers
                .maxByOrNull { (_, sequenceNumber) -> sequenceNumber }
                ?.takeIf { (height, _) -> endHeight == null || height <= endHeight }
                ?: return FunctionResult.Success(null to null)

        val startSequenceNumber = minSequenceNumber.takeIf { startHeight != null }
        val endSequenceNumber = maxSequenceNumber.takeIf { endHeight != null }

        val startInclusiveDerived = if (startHeight != null && minHeight > startHeight) true else startInclusive
        val endInclusiveDerived = if (endHeight != null && maxHeight > endHeight) true else endInclusive

        val additionalFilter: (T) -> Boolean = { entity ->
            blockHeightFilter(startHeight, startInclusive, endHeight, endInclusive)
                .invoke(entity.height())
        }

        return when (
            val result =
                DataUtils.createBigIntegerAndStringRangeQuery(
                    startSequenceNumber,
                    startInclusiveDerived,
                    endSequenceNumber,
                    endInclusiveDerived,
                    pageSize,
                    pagingFrom,
                )
        ) {
            is FunctionResult.Success -> FunctionResult.Success(result.value to additionalFilter)
            is FunctionResult.Failure -> FunctionResult.Failure(result.value)
        }
    }

    suspend fun createRangeQueryFromSequenceNumberBoundaries(
        db: Database,
        tableName: String,
        sequenceNumberColumn: String = "creation_chain_sequence_number",
        startChainSequenceNumber: Long?,
        endChainSequenceNumber: Long?,
        startSequenceNumberColumn: String = "start_chain_sequence_number",
        endSequenceNumberColumn: String = "end_chain_sequence_number",
        startInclusive: Boolean,
        endInclusive: Boolean,
        pageSize: Int,
        pagingFrom: PagingFrom,
    ): FunctionResult<ApiError, Pair<RangeQuery.BigIntegerAndStringRangeQuery?, Nothing?>> {
        var startChainSequenceNumberBigInteger = startChainSequenceNumber?.toBigInteger()
        var endChainSequenceNumberBigInteger = endChainSequenceNumber?.toBigInteger()
        if (startChainSequenceNumberBigInteger != null &&
            endChainSequenceNumberBigInteger != null &&
            startChainSequenceNumberBigInteger > endChainSequenceNumberBigInteger
        ) {
            return FunctionResult.Failure(
                ApiError.BadParameter(
                    name = startSequenceNumberColumn,
                    message = "Parameter '$startSequenceNumberColumn' must be greater than '$endSequenceNumberColumn'.",
                ),
            )
        }

        if (startChainSequenceNumberBigInteger == null && endChainSequenceNumberBigInteger == null) {
            val cornerSequenceNumbers: List<BigInteger> =
                db.query(
                    """
                    (SELECT $sequenceNumberColumn FROM $tableName
                    ORDER BY $sequenceNumberColumn
                    LIMIT 1)
                    UNION
                    (SELECT $sequenceNumberColumn FROM $tableName
                    ORDER BY $sequenceNumberColumn DESC
                    LIMIT 1);
                    """.trimIndent(),
                ) { it.map { rs -> rs.getLong(sequenceNumberColumn).toBigInteger() }.toList() }

            startChainSequenceNumberBigInteger = cornerSequenceNumbers.minByOrNull { it } ?: return FunctionResult.Success(null to null)
            endChainSequenceNumberBigInteger = cornerSequenceNumbers.maxByOrNull { it } ?: return FunctionResult.Success(null to null)
        }

        return DataUtils
            .createBigIntegerAndStringRangeQuery(
                startChainSequenceNumberBigInteger,
                startInclusive,
                endChainSequenceNumberBigInteger,
                endInclusive,
                pageSize,
                pagingFrom,
            ).map { it to null }
    }

    inline fun toBlockHashSqlValue(
        asset: String,
        blockHash: String,
        errorMapper: (ApiError) -> String,
    ): String {
        return decodeBlockHashes(asset, listOf(blockHash), "block_hash")
            .map { it[0] }
            .getOrElse {
                return errorMapper(it)
            }.let {
                DataUtils.convertByteArrayToSqlParam(it)
            }
    }

    inline fun toTxidSqlValue(
        asset: String,
        txid: String,
        errorMapper: (ApiError) -> String,
    ): String {
        return decodeTxids(asset, listOf(txid), "txid")
            .map { it[0] }
            .getOrElse {
                return errorMapper(it)
            }.let {
                DataUtils.convertByteArrayToSqlParam(it)
            }
    }

    fun decodeBlockHashes(
        asset: String,
        blockHashes: List<String>,
        paramName: String = "block_hashes",
    ): FunctionResult<ApiError, List<ByteArray>> =
        decode(asset, blockHashes, Codec::decodeHash) { invalidItem ->
            ApiError.BadParameter(name = paramName, message = "Invalid block hash specified: '$invalidItem'.")
        }

    fun decodeTxids(
        asset: String,
        txids: List<String>,
        paramName: String = "txids",
    ): FunctionResult<ApiError, List<ByteArray>> =
        decode(asset, txids, Codec::decodeHash) { invalidItem ->
            ApiError.BadParameter(name = paramName, message = "Invalid txid specified: '$invalidItem'.")
        }

    fun decodeAccounts(
        asset: String,
        accounts: List<String>,
    ): FunctionResult<ApiError, List<ByteArray>> =
        decode(asset, accounts, Codec::decodeAccount) { invalidItem ->
            ApiError.BadParameter(name = "accounts", message = "Invalid account specified: '$invalidItem'.")
        }

    fun decodeAccount(
        asset: String,
        account: String,
    ): FunctionResult<ApiError, ByteArray> = decodeAccounts(asset, listOf(account)).map { it[0] }

    fun decodeSubAccounts(
        asset: String,
        subAccounts: List<String>,
    ): FunctionResult<ApiError, List<ByteArray>> =
        decode(asset, subAccounts, Codec::decodeSubAccount) { invalidItem ->
            ApiError.BadParameter(name = "sub_accounts", message = "Invalid sub-account specified: '$invalidItem'.")
        }

    private fun decode(
        asset: String,
        items: List<String>,
        decodeFunction: (String, String) -> ByteArray,
        apiErrorProvider: (String) -> ApiError,
    ): FunctionResult<ApiError, List<ByteArray>> {
        val decodedList =
            items.map { item ->
                try {
                    decodeFunction(asset, item)
                } catch (e: IllegalArgumentException) {
                    return FunctionResult.Failure(apiErrorProvider(item))
                }
            }
        return FunctionResult.Success(decodedList)
    }

    fun accountsTable(
        db: Database,
        asset: String,
    ): String = getTable(db, asset, tableSuffix = "accounts")

    fun subAccountsTable(
        db: Database,
        asset: String,
    ): String = getTable(db, asset, tableSuffix = "sub_accounts")

    fun balanceUpdatesTable(
        db: Database,
        asset: String,
    ): String = getTable(db, asset, tableSuffix = "balance_updates")

    fun blocksTable(
        db: Database,
        asset: String,
    ): String = getTable(db, asset, tableSuffix = "blocks")

    fun transactionsTable(
        db: Database,
        asset: String,
    ): String = getTable(db, asset, tableSuffix = "transactions")

    fun staleBlocksTable(
        db: Database,
        asset: String,
    ): String = getTable(db, asset, tableSuffix = "stale_blocks")

    private fun getTable(
        db: Database,
        asset: String,
        tableSuffix: String,
    ): String = """${db.config.schema}."${SqlUtils.escapeSql(asset)}_udm_v2_$tableSuffix""""

    fun toBlockchainFullSingleTransactionResponse(
        txMeta: BlockchainTransactionInfoWrapperV2,
        balanceUpdates: List<BlockchainBalanceUpdateWrapperV2>,
    ): BlockchainFullSingleTransactionResponseV2 =
        BlockchainFullSingleTransactionResponseV2(
            blockHash = txMeta.transaction!!.blockHash,
            height = txMeta.transaction.height,
            txid = txMeta.transaction.txid,
            txPosition = txMeta.transaction.txPosition,
            consensusTime = txMeta.transaction.consensusTime,
            minerTime = txMeta.transaction.minerTime,
            minChainSequenceNumber = txMeta.transaction.minChainSequenceNumber,
            maxChainSequenceNumber = txMeta.transaction.maxChainSequenceNumber,
            amount = txMeta.transaction.amount,
            nBalanceUpdates = txMeta.transaction.nBalanceUpdates,
            balanceUpdates = balanceUpdates.mapNotNull { it.balanceUpdate?.let { bu -> toTransactionBalanceUpdate(bu) } },
            stale = txMeta.transaction.stale,
        )

    fun toTransactionBalanceUpdate(update: BlockchainBalanceUpdateV2) =
        BlockchainTransactionBalanceUpdateV2(
            transactionSequenceNumber = update.transactionSequenceNumber,
            nDebits = update.nDebits,
            nCredits = update.nCredits,
            previousDebitHeight = update.previousDebitHeight,
            previousCreditHeight = update.previousCreditHeight,
            previousChainSequenceNumber = update.previousChainSequenceNumber,
            previousBalance = update.previousBalance,
            newBalance = update.newBalance,
            change = update.change,
            chainSequenceNumber = update.chainSequenceNumber,
            accountCreationHeight = update.accountCreationHeight,
            account = update.account,
            subAccount = update.subAccount,
            stale = update.stale,
        )

    fun blockHeightFilter(
        startHeight: Long?,
        startInclusive: Boolean,
        endHeight: Long?,
        endInclusive: Boolean,
    ): (String) -> Boolean {
        val additionalFilters =
            ArrayList<(Long) -> Boolean>().also {
                if (startHeight != null) {
                    it.add { height ->
                        if (startInclusive) {
                            height >= startHeight
                        } else {
                            height > startHeight
                        }
                    }
                }
                if (endHeight != null) {
                    it.add { height ->
                        if (endInclusive) {
                            height <= endHeight
                        } else {
                            height < endHeight
                        }
                    }
                }
            }
        return { heightAsString -> additionalFilters.all { it(heightAsString.toLong()) } }
    }

    class BlockchainTransactionInfoWrapper(
        val minChainSequenceNumber: BigInteger,
        val time: Instant,
        val transaction: BlockchainTransactionInfo,
    )

    class BlockchainBlockInfoWrapper(
        val height: BigInteger,
        val time: Instant,
        val block: BlockchainBlockInfo,
    )

    class BlockchainBalanceUpdateWrapper(
        val chainSequenceNumber: BigInteger,
        val time: Instant,
        val balanceUpdate: BlockchainBalanceUpdate,
    )

    class BlockchainTransactionInfoWrapperV2(
        val txPosition: BigInteger,
        override val time: Instant,
        val txid: String,
        val height: String,
        val blockHash: String,
        val transaction: BlockchainTransactionInfoV2?,
        val isRestrictedForCommunity: Boolean = false,
    ) : WithTime,
        WithHeight {
        override fun height() = height
    }

    class BlockchainBalanceUpdateWrapperV2(
        val chainSequenceNumber: BigInteger,
        override val time: Instant,
        val height: String,
        val blockHash: String,
        val txid: String?,
        val balanceUpdate: BlockchainBalanceUpdateV2?,
        val isRestrictedForCommunity: Boolean = false,
    ) : WithTime,
        WithHeight {
        override fun height() = height
    }

    class BlockchainBlockInfoWrapperV2(
        val height: BigInteger,
        override val time: Instant,
        val blockHash: String,
        val block: BlockchainBlockInfoV2?,
        val isRestrictedForCommunity: Boolean = false,
    ) : WithTime

    fun getCommunityMaxPageSize(): Int {
        // TODO: Make it configurable
        return 100
    }

    fun getCommunityBalanceUpdatesMaxLimitPerAccount(): Int {
        // TODO: Make it configurable
        return 100
    }

    fun getWarningObject(): WarningObject =
        WarningObject(
            "items_not_available_in_community_api",
            "This is a partial result set. Only the last 30 days of data are available for Community users. " +
                "Access with a Professional key to get the full result set.",
        )

    // Blockchain jobs only support a single asset per request at this time
    fun validateJobAssetList(assets: List<String>): FunctionResult<ApiError, Unit> =
        if (assets.count() == 1) {
            Unit.toSuccess()
        } else {
            ApiError.BadParameter(name = "assets", "Only one asset per request is supported.").toFailure()
        }

    // Don't allow any slot-based request params if the asset doesn't support slots
    fun validateJobSlotParam(
        asset: String,
        paramName: String,
        slot: Long?,
    ): FunctionResult<ApiError, Unit> =
        if (slot == null || ApiNdJobs.supportsSlots(asset)) {
            Unit.toSuccess()
        } else {
            ApiError.BadParameter(paramName, "Slot not supported for asset '$asset'.").toFailure()
        }
}
