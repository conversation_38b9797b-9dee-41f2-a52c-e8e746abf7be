package io.coinmetrics.api.endpoints.blockchain.list

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.ChunkedResponseFormat
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.Response
import io.coinmetrics.api.badNextPageToken
import io.coinmetrics.api.endpoints.GetBlockchainV2ListOfAccountsEndpoint
import io.coinmetrics.api.endpoints.GetBlockchainV2ListOfAccountsRequest
import io.coinmetrics.api.endpoints.blockchain.BlockchainEndpointUtils
import io.coinmetrics.api.endpoints.blockchain.BlockchainEndpointUtils.accountsTable
import io.coinmetrics.api.endpoints.blockchain.BlockchainEndpointUtils.balanceUpdatesTable
import io.coinmetrics.api.endpoints.blockchain.BlockchainEndpointUtils.decodeAccounts
import io.coinmetrics.api.endpoints.blockchain.BlockchainEndpointUtils.staleBlocksTable
import io.coinmetrics.api.models.BlockchainAccountsResponseV2
import io.coinmetrics.api.models.BlockchainAccountsResponseV2Data
import io.coinmetrics.api.models.PagingFrom
import io.coinmetrics.api.persistence.Databases
import io.coinmetrics.api.service.BlockchainEndpointService
import io.coinmetrics.api.utils.CommonEndpointUtils
import io.coinmetrics.api.utils.CommonEndpointUtils.validateMutuallyExclusiveFilters
import io.coinmetrics.api.utils.CommonUtils
import io.coinmetrics.api.utils.DataUtils
import io.coinmetrics.api.utils.QueryTextBuilder
import io.coinmetrics.api.utils.RangeQuery
import io.coinmetrics.api.utils.TimeUtils
import io.coinmetrics.api.utils.WithHeight
import io.coinmetrics.api.utils.WithTime
import io.coinmetrics.api.utils.paging.PageToken
import io.coinmetrics.api.utils.paging.getPageFlow
import io.coinmetrics.atlas.v2.codec.Codec
import io.coinmetrics.databases.Database
import io.coinmetrics.databases.getIntOrNull
import io.coinmetrics.databases.getLongOrNull
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emptyFlow
import java.math.BigInteger
import java.sql.ResultSet
import java.time.Instant

class GetBlockchainV2ListOfAccountsEndpointImpl(
    private val databases: Databases,
    private val blockchainEndpointService: BlockchainEndpointService,
    private val communityKey: String,
) : GetBlockchainV2ListOfAccountsEndpoint() {
    override suspend fun handle(request: GetBlockchainV2ListOfAccountsRequest): Response<BlockchainAccountsResponseV2> {
        val headers =
            blockchainEndpointService
                .validateV2Request<BlockchainAccountsResponseV2>(
                    request.apiKey,
                    request.asset,
                    request.httpRequest,
                    dbTableNamePartsToBeUsed = listOf("accounts", "balance_updates", "stale_blocks"),
                ).getOrElse { return it }

        val internalRequest =
            when (val validationResult = validateAndGetInternalRequest(request)) {
                is FunctionResult.Failure -> return Response.errorResponse(validationResult.value, headers)
                is FunctionResult.Success -> validationResult.value!!
            }

        return when (val result = handleInternal(internalRequest)) {
            is FunctionResult.Success -> Response.chunkedResponse(result.value, headers, ChunkedResponseFormat.Json())
            is FunctionResult.Failure -> Response.errorResponse(result.value, headers)
        }
    }

    /**
     * Validates input parameters and applies restrictions when the API key is community key. Otherwise, it returns the original request unchanged.
     */
    private fun validateAndGetInternalRequest(
        request: GetBlockchainV2ListOfAccountsRequest,
    ): FunctionResult<ApiError, GetBlockchainV2ListOfAccountsRequest?> {
        if (request.apiKey == communityKey) {
            CommonEndpointUtils
                .validateSingleParametersForCommunity(
                    "accounts" to request.accounts,
                )?.let { return FunctionResult.Failure(it) }

            // Apply restrictions for community API key
            return FunctionResult.Success(
                request.copy(
                    startTime = null,
                    endTime = null,
                    startHeight = null,
                    endHeight = null,
                    startChainSequenceNumber = null,
                    endChainSequenceNumber = null,
                    startInclusive = true,
                    endInclusive = true,
                    pageSize = minOf(BlockchainEndpointUtils.getCommunityMaxPageSize(), request.pageSize),
                ),
            )
        }
        return FunctionResult.Success(request)
    }

    private suspend fun handleInternal(request: GetBlockchainV2ListOfAccountsRequest): FunctionResult<ApiError, Flow<Any>> {
        val validationResult = validateFilterParameters(request)
        if (validationResult is FunctionResult.Failure) {
            return FunctionResult.Failure(validationResult.value)
        }

        val initialState =
            request.nextPageToken?.let {
                try {
                    PageToken.BigIntegerAndStringPageToken.parse(
                        pageToken = it,
                        strTransformer = DataUtils.createPageTokenStrTransformer(request.asset, Codec::decodeAccount),
                    )
                } catch (e: Exception) {
                    log.error("Can't parse provided next_page_token '{}'.", it, e)
                    return FunctionResult.Failure(badNextPageToken())
                }
            }

        val db = databases.resolveAtlasDatabase(request.asset)
        val rangeQueryAndAdditionalFilter =
            when (val rangeQueryFunctionResult = determineRangeQuery(db, request)) {
                is FunctionResult.Failure -> return FunctionResult.Failure(rangeQueryFunctionResult.value)
                is FunctionResult.Success -> rangeQueryFunctionResult.value
            }

        val rangeQuery = rangeQueryAndAdditionalFilter.first ?: return FunctionResult.Success(emptyFlow())
        val additionalFilter = rangeQueryAndAdditionalFilter.second

        val queryTextBuilder =
            when (val queryTextBuilderMaybe = getQueryTextBuilder(db, request)) {
                is FunctionResult.Failure -> return FunctionResult.Failure(queryTextBuilderMaybe.value)
                is FunctionResult.Success -> queryTextBuilderMaybe.value
            }

        val stream =
            DataUtils
                .createStream(
                    db = db,
                    queryTextBuilder = queryTextBuilder,
                    bufferSize = request.pageSize + 1,
                    keyNames = arrayOf("a.creation_chain_sequence_number", "a.account"),
                    dataMapper = { rs -> getBlockchainAccount(rs, request.asset) },
                    rangeQuery = rangeQuery,
                    initialState = initialState,
                    stateResolver = {
                        PageToken.BigIntegerAndStringPageToken(
                            it.creationChainSequenceNumber,
                            it.account.account,
                            DataUtils.createPageTokenStrTransformer(request.asset, Codec::decodeAccount),
                        )
                    },
                    streamId = null,
                ).let {
                    if (additionalFilter != null) it.filter(additionalFilter) else it
                }

        val page = stream.map { it.account }.getPageFlow(request.httpRequest, request.pageSize, request.pagingFrom)
        return FunctionResult.Success(page)
    }

    private fun getQueryTextBuilder(
        db: Database,
        request: GetBlockchainV2ListOfAccountsRequest,
    ): FunctionResult<ApiError, QueryTextBuilder> {
        val accountsTableName = accountsTable(db, request.asset)
        val balanceUpdatesTableName = balanceUpdatesTable(db, request.asset)
        val staleBlocksTableName = staleBlocksTable(db, request.asset)
        val accountsFilter =
            request.accounts
                ?.takeIf { it.isNotEmpty() }
                ?.let { accounts ->
                    val decodedAccounts =
                        decodeAccounts(request.asset, accounts).getOrElse {
                            return FunctionResult.Failure(it)
                        }
                    "AND a.account IN (${DataUtils.convertListOfBytesArraysToSqlCollection(decodedAccounts, truncateToSize = 1000)})"
                } ?: ""
        val queryTextBuilder: QueryTextBuilder = { filter, limit ->
            val ordering = if (request.pagingFrom == PagingFrom.START) "ASC" else "DESC"
            """
            SELECT a.*, b.*
                FROM $accountsTableName a 
            LEFT JOIN LATERAL (
                SELECT b.is_credit,
                       b.new_balance,
                       b.n_debits,
                       b.n_credits,
                       b.chain_sequence_number,
                       b.block_height,
                       b.previous_debit_height,
                       b.previous_credit_height
                FROM $balanceUpdatesTableName b
                WHERE b.account = a.account AND b.block_hash NOT IN (SELECT block_hash FROM $staleBlocksTableName)
                ORDER BY b.chain_sequence_number DESC
                LIMIT 1
            ) b ON TRUE
            WHERE TRUE $accountsFilter $filter
            ORDER BY a.creation_chain_sequence_number $ordering, a.account $ordering
            LIMIT $limit;
            """.trimIndent()
        }
        return FunctionResult.Success(queryTextBuilder)
    }

    private fun getBlockchainAccount(
        rs: ResultSet,
        asset: String,
    ): BlockchainAccountInfoWrapper {
        val time = rs.getTimestamp("creation_consensus_time").toInstant()
        val timeFormatted = TimeUtils.dateTimeFormatter.format(time)

        val credit = rs.getBoolean("is_credit")
        val (lastCreditHeight, lastDebitHeight) =
            if (credit) {
                rs.getIntOrNull("block_height") to rs.getIntOrNull("previous_debit_height")
            } else {
                rs.getIntOrNull("previous_credit_height") to rs.getIntOrNull("block_height")
            }
        return BlockchainAccountInfoWrapper(
            creationChainSequenceNumber = rs.getLong("creation_chain_sequence_number").toBigInteger(),
            time = time,
            account =
                BlockchainAccountsResponseV2Data(
                    account = Codec.encodeAccount(asset, rs.getBytes("account")),
                    type = rs.getString("account_type"),
                    creationChainSequenceNumber = rs.getLong("creation_chain_sequence_number").toString(),
                    creationHeight = rs.getInt("creation_height").toString(),
                    creationBlockHash = Codec.encodeHash(asset, rs.getBytes("creation_block_hash")),
                    creationTime = timeFormatted,
                    balance = rs.getBigDecimal("new_balance")?.let { CommonUtils.formatBigDecimal(it) },
                    lastChainSequenceNumber = rs.getLongOrNull("chain_sequence_number")?.toString(),
                    lastCreditHeight = lastCreditHeight?.toString(),
                    lastDebitHeight = lastDebitHeight?.toString(),
                    nCredits = rs.getLongOrNull("n_credits")?.toString(),
                    nDebits = rs.getLongOrNull("n_debits")?.toString(),
                ),
        )
    }

    private fun validateFilterParameters(request: GetBlockchainV2ListOfAccountsRequest): FunctionResult<ApiError, Any?> {
        val timeRelatedParameters =
            listOf(
                "*_time" to (request.startTime to request.endTime),
                "*_height" to (request.startHeight to request.endHeight),
                "*_chain_sequence_number" to (request.startChainSequenceNumber to request.endChainSequenceNumber),
            )
        return validateMutuallyExclusiveFilters(timeRelatedParameters)
    }

    private suspend fun determineRangeQuery(
        db: Database,
        request: GetBlockchainV2ListOfAccountsRequest,
    ): FunctionResult<ApiError, out Pair<RangeQuery.BigIntegerAndStringRangeQuery?, ((BlockchainAccountInfoWrapper) -> Boolean)?>> =
        if (request.startTime != null || request.endTime != null) {
            BlockchainEndpointUtils.createRangeQueryFromTimeBoundaries(
                db,
                tableName = accountsTable(db, request.asset),
                timeColumn = "creation_consensus_time",
                numberColumn = "creation_chain_sequence_number",
                startTime = request.startTime,
                endTime = request.endTime,
                startInclusive = request.startInclusive,
                endInclusive = request.endInclusive,
                timezone = request.timezone,
                pagingFrom = request.pagingFrom,
            )
        } else if (request.startHeight != null || request.endHeight != null) {
            BlockchainEndpointUtils.createRangeQueryFromHeightBoundaries(
                db,
                tableName = accountsTable(db, request.asset),
                heightColumn = "creation_height",
                sequenceNumberColumn = "creation_chain_sequence_number",
                startHeight = request.startHeight,
                endHeight = request.endHeight,
                startInclusive = request.startInclusive,
                endInclusive = request.endInclusive,
                pagingFrom = request.pagingFrom,
            )
        } else {
            BlockchainEndpointUtils.createRangeQueryFromSequenceNumberBoundaries(
                db,
                tableName = accountsTable(db, request.asset),
                startChainSequenceNumber = request.startChainSequenceNumber,
                endChainSequenceNumber = request.endChainSequenceNumber,
                startInclusive = request.startInclusive,
                endInclusive = request.endInclusive,
                pagingFrom = request.pagingFrom,
            )
        }

    private class BlockchainAccountInfoWrapper(
        val creationChainSequenceNumber: BigInteger,
        override val time: Instant,
        val account: BlockchainAccountsResponseV2Data,
    ) : WithTime,
        WithHeight {
        override fun height() = account.creationHeight
    }
}
