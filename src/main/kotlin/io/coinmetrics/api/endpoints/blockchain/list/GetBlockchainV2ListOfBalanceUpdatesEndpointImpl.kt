package io.coinmetrics.api.endpoints.blockchain.list

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.Response
import io.coinmetrics.api.badNextPageToken
import io.coinmetrics.api.endpoints.GetBlockchainV2ListOfBalanceUpdatesEndpoint
import io.coinmetrics.api.endpoints.GetBlockchainV2ListOfBalanceUpdatesRequest
import io.coinmetrics.api.endpoints.blockchain.BlockchainEndpointUtils
import io.coinmetrics.api.endpoints.blockchain.BlockchainEndpointUtils.balanceUpdateMapper
import io.coinmetrics.api.endpoints.blockchain.BlockchainEndpointUtils.balanceUpdatesTable
import io.coinmetrics.api.endpoints.blockchain.BlockchainEndpointUtils.staleBlocksTable
import io.coinmetrics.api.endpoints.blockchain.list.balances.balanceUpdatesQueryBuilder
import io.coinmetrics.api.models.BlockchainBalanceUpdatesResponseV2
import io.coinmetrics.api.models.PagingFrom
import io.coinmetrics.api.persistence.Databases
import io.coinmetrics.api.service.BlockchainEndpointService
import io.coinmetrics.api.utils.CommonEndpointUtils
import io.coinmetrics.api.utils.CommonEndpointUtils.validateMutuallyExclusiveFilters
import io.coinmetrics.api.utils.DataUtils
import io.coinmetrics.api.utils.RangeQuery
import io.coinmetrics.api.utils.paging.PageToken
import io.coinmetrics.api.utils.paging.PagingUtils
import io.coinmetrics.api.utils.paging.getPage
import io.coinmetrics.atlas.v2.codec.Codec
import io.coinmetrics.databases.Database

class GetBlockchainV2ListOfBalanceUpdatesEndpointImpl(
    private val databases: Databases,
    private val blockchainEndpointService: BlockchainEndpointService,
    private val communityKey: String,
) : GetBlockchainV2ListOfBalanceUpdatesEndpoint() {
    override suspend fun handle(request: GetBlockchainV2ListOfBalanceUpdatesRequest): Response<BlockchainBalanceUpdatesResponseV2> {
        val headers =
            blockchainEndpointService
                .validateV2Request<BlockchainBalanceUpdatesResponseV2>(
                    request.apiKey,
                    request.asset,
                    request.httpRequest,
                    dbTableNamePartsToBeUsed = listOf("balance_updates", "stale_blocks"),
                ).getOrElse { return it }

        val internalRequest =
            when (val validationResult = validateAndGetInternalRequest(request)) {
                is FunctionResult.Failure -> return Response.errorResponse(validationResult.value, headers)
                is FunctionResult.Success -> validationResult.value!!
            }

        return when (val result = handleInternal(internalRequest)) {
            is FunctionResult.Success -> Response.successResponse(result.value, headers)
            is FunctionResult.Failure -> Response.errorResponse(result.value, headers)
        }
    }

    /**
     * Validates input parameters and applies restrictions when the API key is community key. Otherwise, it returns the original request unchanged.
     */
    private fun validateAndGetInternalRequest(
        request: GetBlockchainV2ListOfBalanceUpdatesRequest,
    ): FunctionResult<ApiError, GetBlockchainV2ListOfBalanceUpdatesRequest?> {
        if (request.apiKey == communityKey) {
            CommonEndpointUtils
                .validateSingleParametersForCommunity(
                    "block_hashes" to request.blockHashes,
                    "txids" to request.txids,
                    "accounts" to request.accounts,
                )?.let { return FunctionResult.Failure(it) }

            CommonEndpointUtils
                .validateNotPresentParametersForCommunity(
                    "sub_accounts" to request.subAccounts,
                )?.let { return FunctionResult.Failure(it) }

            // Apply restrictions for community API key
            return FunctionResult.Success(
                request.copy(
                    limitPerAccount =
                        if (request.limitPerAccount != null) {
                            minOf(
                                BlockchainEndpointUtils.getCommunityBalanceUpdatesMaxLimitPerAccount(),
                                request.limitPerAccount,
                            )
                        } else {
                            BlockchainEndpointUtils.getCommunityBalanceUpdatesMaxLimitPerAccount()
                        },
                    startTime = null,
                    endTime = null,
                    startHeight = null,
                    endHeight = null,
                    startChainSequenceNumber = null,
                    endChainSequenceNumber = null,
                    includeSubAccounts = false,
                    chain = "main",
                    startInclusive = true,
                    endInclusive = true,
                    pageSize = minOf(BlockchainEndpointUtils.getCommunityMaxPageSize(), request.pageSize),
                ),
            )
        }
        return FunctionResult.Success(request)
    }

    private suspend fun handleInternal(
        request: GetBlockchainV2ListOfBalanceUpdatesRequest,
    ): FunctionResult<ApiError, BlockchainBalanceUpdatesResponseV2> {
        val validationResult = validateFilterParameters(request)
        if (validationResult != null) {
            return FunctionResult.Failure(validationResult)
        }

        val initialState =
            request.nextPageToken?.let {
                try {
                    PageToken.BigIntegerAndStringPageToken.parse(
                        pageToken = it,
                        strTransformer = DataUtils.createPageTokenStrTransformer(request.asset, Codec::decodeHash),
                    )
                } catch (e: Exception) {
                    log.error("Can't parse provided next_page_token '{}'.", it, e)
                    return FunctionResult.Failure(badNextPageToken())
                }
            }

        val db = databases.resolveAtlasDatabase(request.asset)
        val rangeQueryFunctionResult = determineRangeQuery(db, request)

        val (rangeQuery, additionalFilter) =
            when (rangeQueryFunctionResult) {
                is FunctionResult.Failure -> return FunctionResult.Failure(rangeQueryFunctionResult.value)
                is FunctionResult.Success -> rangeQueryFunctionResult.value
            }
        if (rangeQuery == null) {
            return FunctionResult.Success(BlockchainBalanceUpdatesResponseV2(data = emptyList()))
        }

        val keyNames = arrayOf("bu.chain_sequence_number", "bu.block_hash")
        val queryTextBuilder =
            balanceUpdatesQueryBuilder(request, rangeQuery, keyNames) {
                balanceUpdatesTable(db, request.asset) to staleBlocksTable(db, request.asset)
            }.getOrElse { return FunctionResult.Failure(it) }

        val isCommunityApi = request.apiKey == communityKey
        val timeConstraints = if (isCommunityApi) blockchainEndpointService.getTimeConstraints(request.apiKey) else null

        val stream =
            DataUtils
                .createStream(
                    db = db,
                    queryTextBuilder = queryTextBuilder,
                    bufferSize = request.pageSize + 1,
                    keyNames = keyNames,
                    dataMapper = { rs ->
                        balanceUpdateMapper(rs, request.asset, request.includeSubAccounts, isCommunityApi, timeConstraints)
                    },
                    rangeQuery = rangeQuery,
                    initialState = initialState,
                    stateResolver = {
                        PageToken.BigIntegerAndStringPageToken(
                            it.chainSequenceNumber,
                            it.blockHash,
                            DataUtils.createPageTokenStrTransformer(request.asset, Codec::decodeHash),
                        )
                    },
                    streamId = null,
                ).let {
                    if (additionalFilter != null) it.filter(additionalFilter) else it
                }

        val page =
            @Suppress("DEPRECATION")
            stream.getPage(request.pageSize, request.pagingFrom == PagingFrom.START)
        val nextPageUrl = page.nextPageToken?.let { PagingUtils.createNextPageUrl(request.httpRequest, it) }

        if (isCommunityApi) {
            val isSingleResourceQuery =
                CommonEndpointUtils.getFirstNonEmptyParameter(
                    "block_hashes" to request.blockHashes,
                    "txids" to request.txids,
                ) != null

            if (isSingleResourceQuery && page.items.isNotEmpty() && timeConstraints?.first?.isAfter(page.items[0].time) == true) {
                return FunctionResult.Failure(
                    ApiError.ForbiddenWithMessage("Requested balance update is outside the allowed time range for Community API."),
                )
            }
        }

        var isHiddenElementsForCommunity = false
        return FunctionResult.Success(
            BlockchainBalanceUpdatesResponseV2(
                data =
                    page.items.mapNotNull {
                        isHiddenElementsForCommunity = isHiddenElementsForCommunity || it.isRestrictedForCommunity
                        it.balanceUpdate
                    },
                nextPageToken = page.nextPageToken,
                nextPageUrl = nextPageUrl,
                // todo: non-standard warning object prevents us from migration of the endpoint to chunked encoding
                warning = if (isHiddenElementsForCommunity) BlockchainEndpointUtils.getWarningObject() else null,
            ),
        )
    }

    private suspend fun determineRangeQuery(
        db: Database,
        request: GetBlockchainV2ListOfBalanceUpdatesRequest,
    ): FunctionResult<
        ApiError,
        out Pair<
            RangeQuery.BigIntegerAndStringRangeQuery?,
            (
                (BlockchainEndpointUtils.BlockchainBalanceUpdateWrapperV2) -> Boolean
            )?,
        >,
    > =
        if (request.startTime != null || request.endTime != null) {
            BlockchainEndpointUtils.createRangeQueryFromTimeBoundaries(
                db,
                tableName = balanceUpdatesTable(db, request.asset),
                startTime = request.startTime,
                endTime = request.endTime,
                startInclusive = request.startInclusive,
                endInclusive = request.endInclusive,
                timezone = request.timezone,
                pagingFrom = request.pagingFrom,
            )
        } else if (request.startHeight != null || request.endHeight != null) {
            BlockchainEndpointUtils.createRangeQueryFromHeightBoundaries(
                db,
                tableName = balanceUpdatesTable(db, request.asset),
                startHeight = request.startHeight,
                endHeight = request.endHeight,
                startInclusive = request.startInclusive,
                endInclusive = request.endInclusive,
                pagingFrom = request.pagingFrom,
            )
        } else {
            BlockchainEndpointUtils.createRangeQueryFromSequenceNumberBoundaries(
                db,
                tableName = balanceUpdatesTable(db, request.asset),
                sequenceNumberColumn = "chain_sequence_number",
                startChainSequenceNumber = request.startChainSequenceNumber,
                endChainSequenceNumber = request.endChainSequenceNumber,
                startInclusive = request.startInclusive,
                endInclusive = request.endInclusive,
                pagingFrom = request.pagingFrom,
            )
        }

    private fun validateFilterParameters(request: GetBlockchainV2ListOfBalanceUpdatesRequest): ApiError? {
        CommonEndpointUtils.validateChainParameter(request.chain)?.let { return it }

        if (request.subAccounts != null && request.accounts == null) {
            return ApiError.BadParameters("Accounts filter is required when sub-accounts specified.")
        }

        val validateByKindResult =
            validateMutuallyExclusiveFilters(
                listOf(
                    "transaction_ids" to request.txids,
                    "block_hashes" to request.blockHashes,
                    "accounts" to request.accounts,
                ),
            )
        if (validateByKindResult is FunctionResult.Failure) {
            return validateByKindResult.value
        }

        val validateByTimeFiltersResult =
            validateMutuallyExclusiveFilters(
                listOf(
                    "*_time" to (request.startTime to request.endTime),
                    "*_height" to (request.startHeight to request.endHeight),
                    "*_chain_sequence_number" to (request.startChainSequenceNumber to request.endChainSequenceNumber),
                ),
            )
        if (validateByTimeFiltersResult is FunctionResult.Failure) {
            return validateByTimeFiltersResult.value
        }
        return null
    }
}
