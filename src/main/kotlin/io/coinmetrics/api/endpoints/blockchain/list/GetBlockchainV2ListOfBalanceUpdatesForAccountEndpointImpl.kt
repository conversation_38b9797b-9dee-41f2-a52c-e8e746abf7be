package io.coinmetrics.api.endpoints.blockchain.list

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.ChunkedResponseFormat
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.Response
import io.coinmetrics.api.badNextPageToken
import io.coinmetrics.api.endpoints.GetBlockchainV2ListOfBalanceUpdatesForAccountEndpoint
import io.coinmetrics.api.endpoints.GetBlockchainV2ListOfBalanceUpdatesForAccountRequest
import io.coinmetrics.api.endpoints.blockchain.BlockchainEndpointUtils
import io.coinmetrics.api.endpoints.blockchain.BlockchainEndpointUtils.balanceUpdateMapper
import io.coinmetrics.api.endpoints.blockchain.BlockchainEndpointUtils.balanceUpdatesTable
import io.coinmetrics.api.endpoints.blockchain.BlockchainEndpointUtils.staleBlocksTable
import io.coinmetrics.api.endpoints.blockchain.list.balances.balanceUpdatesForAccountQueryBuilder
import io.coinmetrics.api.models.BlockchainBalanceUpdatesResponseV2
import io.coinmetrics.api.persistence.Databases
import io.coinmetrics.api.service.BlockchainEndpointService
import io.coinmetrics.api.utils.CommonEndpointUtils
import io.coinmetrics.api.utils.CommonEndpointUtils.validateMutuallyExclusiveFilters
import io.coinmetrics.api.utils.DataUtils
import io.coinmetrics.api.utils.RangeQuery
import io.coinmetrics.api.utils.paging.PageToken
import io.coinmetrics.api.utils.paging.getPageFlow
import io.coinmetrics.atlas.v2.codec.Codec
import io.coinmetrics.databases.Database
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emptyFlow

class GetBlockchainV2ListOfBalanceUpdatesForAccountEndpointImpl(
    private val databases: Databases,
    private val blockchainEndpointService: BlockchainEndpointService,
) : GetBlockchainV2ListOfBalanceUpdatesForAccountEndpoint() {
    override suspend fun handle(
        request: GetBlockchainV2ListOfBalanceUpdatesForAccountRequest,
    ): Response<BlockchainBalanceUpdatesResponseV2> {
        val headers =
            blockchainEndpointService
                .validateV2Request<BlockchainBalanceUpdatesResponseV2>(
                    request.apiKey,
                    request.asset,
                    request.httpRequest,
                    dbTableNamePartsToBeUsed = listOf("balance_updates", "stale_blocks"),
                ).getOrElse { return it }

        return when (val result = handleInternal(request)) {
            is FunctionResult.Success -> Response.chunkedResponse(result.value, headers, ChunkedResponseFormat.Json())
            is FunctionResult.Failure -> Response.errorResponse(result.value, headers)
        }
    }

    private suspend fun handleInternal(request: GetBlockchainV2ListOfBalanceUpdatesForAccountRequest): FunctionResult<ApiError, Flow<Any>> {
        validateFilterParameters(request)?.let { return FunctionResult.Failure(it) }

        val initialState =
            request.nextPageToken?.let {
                try {
                    PageToken.BigIntegerAndStringPageToken.parse(
                        pageToken = it,
                        strTransformer = DataUtils.createPageTokenStrTransformer(request.asset, Codec::decodeHash),
                    )
                } catch (e: Exception) {
                    log.error("Can't parse provided next_page_token '{}'.", it, e)
                    return FunctionResult.Failure(badNextPageToken())
                }
            }

        val db = databases.resolveAtlasDatabase(request.asset)
        val decodedAccountSqlValue =
            DataUtils.convertByteArrayToSqlParam(
                BlockchainEndpointUtils.decodeAccount(request.asset, request.account).getOrElse {
                    return FunctionResult.Failure(it)
                },
            )
        findAccount(db, request, decodedAccountSqlValue).getOrElse { return FunctionResult.Failure(it) }
        val rangeQueryFunctionResult = determineRangeQuery(db, request)

        val (rangeQuery, additionalFilter) =
            when (rangeQueryFunctionResult) {
                is FunctionResult.Failure -> return FunctionResult.Failure(rangeQueryFunctionResult.value)
                is FunctionResult.Success -> rangeQueryFunctionResult.value
            }
        if (rangeQuery == null) {
            return FunctionResult.Success(emptyFlow())
        }

        val keyNames = arrayOf("bu.chain_sequence_number", "bu.block_hash")
        val queryTextBuilder =
            balanceUpdatesForAccountQueryBuilder(
                request = request,
                account = decodedAccountSqlValue,
                rangeQuery = rangeQuery,
                keyNames = keyNames,
                balanceUpdatesTableName = balanceUpdatesTable(db, request.asset),
                staleBlocksTableName = staleBlocksTable(db, request.asset),
            ).getOrElse { return FunctionResult.Failure(it) }

        val stream =
            DataUtils
                .createStream(
                    db = db,
                    queryTextBuilder = queryTextBuilder,
                    bufferSize = request.pageSize + 1,
                    keyNames = keyNames,
                    dataMapper = { rs -> balanceUpdateMapper(rs, request.asset, request.includeSubAccounts, false, null) },
                    rangeQuery = rangeQuery,
                    initialState = initialState,
                    stateResolver = {
                        PageToken.BigIntegerAndStringPageToken(
                            it.chainSequenceNumber,
                            it.blockHash,
                            DataUtils.createPageTokenStrTransformer(request.asset, Codec::decodeHash),
                        )
                    },
                    streamId = null,
                ).let {
                    if (additionalFilter != null) it.filter(additionalFilter) else it
                }

        val page =
            stream.map { it.balanceUpdate }.getPageFlow(
                request.httpRequest,
                request.pageSize,
                request.pagingFrom,
            )
        return FunctionResult.Success(page)
    }

    private suspend fun findAccount(
        db: Database,
        request: GetBlockchainV2ListOfBalanceUpdatesForAccountRequest,
        decodedAccountSqlValue: String,
    ): FunctionResult<ApiError, String> {
        val accountsTable = BlockchainEndpointUtils.accountsTable(db, request.asset)
        val res =
            db.query(
                """
                SELECT 1
                FROM $accountsTable
                WHERE account=$decodedAccountSqlValue
                """.trimIndent(),
            ) { query ->
                query.map { decodedAccountSqlValue }.firstOrNull()
            }

        return if (res?.isNotEmpty() == true) {
            FunctionResult.Success(
                decodedAccountSqlValue,
            )
        } else {
            FunctionResult.Failure(ApiError.NotFoundWithMessage("Account '${request.account}' not found."))
        }
    }

    private suspend fun determineRangeQuery(
        db: Database,
        request: GetBlockchainV2ListOfBalanceUpdatesForAccountRequest,
    ): FunctionResult<
        ApiError,
        out Pair<
            RangeQuery.BigIntegerAndStringRangeQuery?,
            (
                (BlockchainEndpointUtils.BlockchainBalanceUpdateWrapperV2) -> Boolean
            )?,
        >,
    > =
        if (request.startTime != null || request.endTime != null) {
            BlockchainEndpointUtils.createRangeQueryFromTimeBoundaries(
                db,
                tableName = balanceUpdatesTable(db, request.asset),
                startTime = request.startTime,
                endTime = request.endTime,
                startInclusive = request.startInclusive,
                endInclusive = request.endInclusive,
                timezone = request.timezone,
                pagingFrom = request.pagingFrom,
            )
        } else if (request.startHeight != null || request.endHeight != null) {
            BlockchainEndpointUtils.createRangeQueryFromHeightBoundaries(
                db,
                tableName = balanceUpdatesTable(db, request.asset),
                startHeight = request.startHeight,
                endHeight = request.endHeight,
                startInclusive = request.startInclusive,
                endInclusive = request.endInclusive,
                pagingFrom = request.pagingFrom,
            )
        } else {
            BlockchainEndpointUtils.createRangeQueryFromSequenceNumberBoundaries(
                db,
                tableName = balanceUpdatesTable(db, request.asset),
                sequenceNumberColumn = "chain_sequence_number",
                startChainSequenceNumber = request.startChainSequenceNumber,
                endChainSequenceNumber = request.endChainSequenceNumber,
                startInclusive = request.startInclusive,
                endInclusive = request.endInclusive,
                pagingFrom = request.pagingFrom,
            )
        }

    private fun validateFilterParameters(request: GetBlockchainV2ListOfBalanceUpdatesForAccountRequest): ApiError? {
        CommonEndpointUtils.validateChainParameter(request.chain)?.let { return it }

        val validateByKindResult =
            validateMutuallyExclusiveFilters(
                listOf(
                    "txids" to request.txids,
                    "block_hashes" to request.blockHashes,
                ),
            )
        if (validateByKindResult is FunctionResult.Failure) {
            return validateByKindResult.value
        }

        val validateByTimeFiltersResult =
            validateMutuallyExclusiveFilters(
                listOf(
                    "*_time" to (request.startTime to request.endTime),
                    "*_height" to (request.startHeight to request.endHeight),
                    "*_chain_sequence_number" to (request.startChainSequenceNumber to request.endChainSequenceNumber),
                ),
            )
        if (validateByTimeFiltersResult is FunctionResult.Failure) {
            return validateByTimeFiltersResult.value
        }
        return null
    }
}
