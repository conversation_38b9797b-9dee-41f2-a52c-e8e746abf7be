package io.coinmetrics.api.endpoints.blockchain.full

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.Response
import io.coinmetrics.api.endpoints.GetBlockchainV2FullTransactionEndpoint
import io.coinmetrics.api.endpoints.GetBlockchainV2FullTransactionRequest
import io.coinmetrics.api.endpoints.blockchain.BlockchainEndpointUtils
import io.coinmetrics.api.endpoints.blockchain.BlockchainEndpointUtils.findBalanceUpdatesByBlockHashAndTxid
import io.coinmetrics.api.endpoints.blockchain.BlockchainEndpointUtils.toBlockHashSqlValue
import io.coinmetrics.api.endpoints.blockchain.BlockchainEndpointUtils.toBlockchainFullSingleTransactionResponse
import io.coinmetrics.api.endpoints.blockchain.BlockchainEndpointUtils.toTxidSqlValue
import io.coinmetrics.api.models.BlockchainFullSingleTransactionResponseV2
import io.coinmetrics.api.persistence.Databases
import io.coinmetrics.api.service.BlockchainEndpointService
import io.coinmetrics.databases.Database

class GetBlockchainV2FullTransactionEndpointImpl(
    private val databases: Databases,
    private val blockchainEndpointService: BlockchainEndpointService,
    private val communityKey: String,
) : GetBlockchainV2FullTransactionEndpoint() {
    override suspend fun handle(request: GetBlockchainV2FullTransactionRequest): Response<BlockchainFullSingleTransactionResponseV2> {
        val headers =
            blockchainEndpointService
                .validateV2Request<BlockchainFullSingleTransactionResponseV2>(
                    request.apiKey,
                    request.asset,
                    request.httpRequest,
                    dbTableNamePartsToBeUsed = listOf("transactions", "balance_updates", "stale_blocks"),
                ).getOrElse { return it }

        return when (val result = handleInternal(getInternalRequest(request))) {
            is FunctionResult.Success -> Response.successResponse(result.value, headers)
            is FunctionResult.Failure -> Response.errorResponse(result.value, headers)
        }
    }

    /**
     * Applies restrictions when the API key is community key. Otherwise, it returns the original request unchanged.
     */
    private fun getInternalRequest(request: GetBlockchainV2FullTransactionRequest): GetBlockchainV2FullTransactionRequest {
        if (request.apiKey == communityKey) {
            // Apply restrictions for community API key
            return GetBlockchainV2FullTransactionRequest(
                httpRequest = request.httpRequest,
                apiKey = request.apiKey,
                asset = request.asset,
                txid = request.txid,
                includeSubAccounts = false,
                pretty = request.pretty,
            )
        }
        return request
    }

    private suspend fun handleInternal(
        request: GetBlockchainV2FullTransactionRequest,
    ): FunctionResult<ApiError, BlockchainFullSingleTransactionResponseV2> {
        val db = databases.resolveAtlasDatabase(request.asset)

        val txidSqlValue = toTxidSqlValue(request.asset, request.txid) { return FunctionResult.Failure(it) }
        val txMeta = findTransactionById(db, request.asset, txidSqlValue) ?: return FunctionResult.Failure(ApiError.NotFound)

        val isCommunityApi = request.apiKey == communityKey
        val timeConstraints = if (isCommunityApi) blockchainEndpointService.getTimeConstraints(request.apiKey) else null

        if (isCommunityApi && timeConstraints?.first?.isAfter(txMeta.time) == true) {
            return FunctionResult.Failure(ApiError.ForbiddenWithMessage("Requested transaction is not available for Community API."))
        }

        val blockHashSqlValue = toBlockHashSqlValue(request.asset, txMeta.blockHash) { return FunctionResult.Failure(it) }
        val txBalanceUpdates =
            findBalanceUpdatesByBlockHashAndTxid(db, request.asset, request.includeSubAccounts, blockHashSqlValue, txidSqlValue)

        return FunctionResult.Success(toBlockchainFullSingleTransactionResponse(txMeta, txBalanceUpdates))
    }

    private suspend fun findTransactionById(
        db: Database,
        asset: String,
        txidSqlValue: String,
    ): BlockchainEndpointUtils.BlockchainTransactionInfoWrapperV2? {
        val transactionsTable = BlockchainEndpointUtils.transactionsTable(db, asset)
        val staleBlocksTableName = BlockchainEndpointUtils.staleBlocksTable(db, asset)
        return db.query(
            """
            SELECT 
                t.*,
                sb.block_height AS stale_block_height
            FROM $transactionsTable t 
                LEFT JOIN $staleBlocksTableName sb ON t.block_hash = sb.block_hash
            WHERE t.hash=$txidSqlValue LIMIT 1
            """.trimIndent(),
        ) { query ->
            query.map { rs -> BlockchainEndpointUtils.transactionMapper(rs, asset) }.firstOrNull()
        }
    }
}
