package io.coinmetrics.api.endpoints.constituents

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.ChunkedResponseFormat
import io.coinmetrics.api.Response
import io.coinmetrics.api.badNextPageToken
import io.coinmetrics.api.endpoints.GetConstituentTimeframesAssetMetricsEndpoint
import io.coinmetrics.api.endpoints.GetConstituentTimeframesAssetMetricsRequest
import io.coinmetrics.api.endpoints.constituents.ConstituentEndpointUtils.checkAccess
import io.coinmetrics.api.endpoints.constituents.ConstituentEndpointUtils.parseStartEndTimes
import io.coinmetrics.api.models.ConstituentTimeframesAssetMetricsResponse
import io.coinmetrics.api.resources.Resources
import io.coinmetrics.api.resources.constituents.AssetMetricConstituentProvider
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.getConstituentsRateLimitHeaders
import io.coinmetrics.api.utils.paging.ListPagingUtils
import io.coinmetrics.api.utils.paging.PageToken
import io.coinmetrics.api.utils.paging.getPageFlow
import io.coinmetrics.api.utils.streams.BufferedSuspendableStream

class GetConstituentTimeframesAssetMetricsEndpointImpl(
    val amsService: AmsService,
    private val assetMetricConstituentProviders: Map<String, AssetMetricConstituentProvider>,
) : GetConstituentTimeframesAssetMetricsEndpoint() {
    override suspend fun handle(request: GetConstituentTimeframesAssetMetricsRequest): Response<ConstituentTimeframesAssetMetricsResponse> {
        val headers =
            amsService
                .getConstituentsRateLimitHeaders(
                    apiKey = request.apiKey,
                    httpRequest = request.httpRequest,
                ).getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }

        checkAccess(amsService, request.apiKey, resource = "asset_metrics", target = request.metric)
            .getOrElse { apiError ->
                if (apiError is ApiError.Forbidden) {
                    return Response.errorResponse(
                        ApiError.ForbiddenWithMessage("Requested metric '${request.metric}' is not available with supplied credentials."),
                    )
                }
                return Response.errorResponse(apiError, headers)
            }

        val offset =
            request.nextPageToken?.let {
                PageToken.IntPageToken.parseCatching(it) { return Response.errorResponse(badNextPageToken()) }
            }
        val (startTime, endTime) =
            parseStartEndTimes(request.startTime, request.endTime).getOrElse { (name, message) ->
                return Response.errorResponse(ApiError.BadParameter(name, message))
            }
        val constituents =
            request.constituents
                ?.asSequence()
                ?.map { it.lowercase() }
                ?.distinct()
                ?.mapNotNull { Resources.getExchangeByName(it).onFailure { message -> log.warn(message) }.getOrNull() }
                ?.map { it.getNormalizedName() }
                ?.toHashSet()
                ?: Resources.exchangeNameToIdMap.keys

        val provider =
            assetMetricConstituentProviders[request.metric]
                ?: return Response.errorResponse(ApiError.UnsupportedParameterValue("metric", request.metric), headers)

        val timeframes = provider.getTimeframes(startTime, endTime, constituents)
        val stream =
            BufferedSuspendableStream(
                initialState = offset,
                bufferLoader = ListPagingUtils.pageLoader(timeframes, request.pagingFrom),
                bufferSize = request.pageSize,
                stateResolver = {
                    ListPagingUtils.pageTokenValueResolver(offset?.value, request.pageSize, timeframes.lastIndex)
                },
            )

        val page = stream.getPageFlow(request.httpRequest, request.pageSize, request.pagingFrom)

        return Response.chunkedResponse(
            items = page,
            headers = headers,
            format = if (request.format == "csv") ChunkedResponseFormat.Csv() else ChunkedResponseFormat.Json(allowNullValues = true),
        )
    }
}
