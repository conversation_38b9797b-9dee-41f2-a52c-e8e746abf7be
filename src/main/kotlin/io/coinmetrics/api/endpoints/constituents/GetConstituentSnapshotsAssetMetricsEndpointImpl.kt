package io.coinmetrics.api.endpoints.constituents

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.ApiError.BadParameter
import io.coinmetrics.api.ApiError.UnsupportedParameterValue
import io.coinmetrics.api.ChunkedResponseFormat
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.Response
import io.coinmetrics.api.badNextPageToken
import io.coinmetrics.api.endpoints.GetConstituentSnapshotsAssetMetricsEndpoint
import io.coinmetrics.api.endpoints.GetConstituentSnapshotsAssetMetricsRequest
import io.coinmetrics.api.endpoints.constituents.ConstituentEndpointUtils.checkAccess
import io.coinmetrics.api.endpoints.constituents.ConstituentEndpointUtils.parseStartEndTimes
import io.coinmetrics.api.models.ConstituentSnapshotsAssetMetricsResponse
import io.coinmetrics.api.resources.constituents.AssetMetricConstituentProvider
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.getConstituentsRateLimitHeaders
import io.coinmetrics.api.toFailure
import io.coinmetrics.api.toSuccess
import io.coinmetrics.api.utils.CommonEndpointUtils
import io.coinmetrics.api.utils.TimeUtils
import io.coinmetrics.api.utils.paging.ListPagingUtils
import io.coinmetrics.api.utils.paging.PageToken
import io.coinmetrics.api.utils.paging.getPageFlow
import io.coinmetrics.api.utils.streams.BufferedSuspendableStream
import java.time.Instant
import java.time.ZoneOffset.UTC

class GetConstituentSnapshotsAssetMetricsEndpointImpl(
    val amsService: AmsService,
    private val assetMetricConstituentProviders: Map<String, AssetMetricConstituentProvider>,
) : GetConstituentSnapshotsAssetMetricsEndpoint() {
    override suspend fun handle(request: GetConstituentSnapshotsAssetMetricsRequest): Response<ConstituentSnapshotsAssetMetricsResponse> {
        val headers =
            amsService
                .getConstituentsRateLimitHeaders(
                    apiKey = request.apiKey,
                    httpRequest = request.httpRequest,
                ).getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }

        checkAccess(amsService, request.apiKey, resource = "asset_metrics", target = request.metric)
            .getOrElse { apiError ->
                if (apiError is ApiError.Forbidden) {
                    return Response.errorResponse(
                        ApiError.ForbiddenWithMessage("Requested metric '${request.metric}' is not available with supplied credentials."),
                    )
                }
                return Response.errorResponse(apiError, headers)
            }

        CommonEndpointUtils
            .validateMutuallyExclusiveFilters(
                listOf(
                    "at_time" to request.atTime,
                    "start_time/end_time" to (request.startTime to request.endTime),
                ),
            ).getOrElse {
                return Response.errorResponse(it, headers)
            }

        val offset =
            request.nextPageToken?.let {
                PageToken.IntPageToken.parseCatching(it) { return Response.errorResponse(badNextPageToken()) }
            }

        val provider =
            assetMetricConstituentProviders[request.metric]
                ?: return Response.errorResponse(UnsupportedParameterValue("metric", request.metric), headers)

        val atTime =
            parseAtTime(request.atTime).getOrElse { (name, message) ->
                return Response.errorResponse(BadParameter(name, message))
            }
        val (startTime, endTime) =
            parseStartEndTimes(request.startTime, request.endTime).getOrElse { (name, message) ->
                return Response.errorResponse(BadParameter(name, message))
            }

        val snapshots = atTime?.let { provider.getSnapshotsAtTime(atTime) } ?: provider.getSnapshots(startTime, endTime)

        val stream =
            BufferedSuspendableStream(
                initialState = offset,
                bufferLoader = ListPagingUtils.pageLoader(snapshots, request.pagingFrom),
                bufferSize = request.pageSize,
                stateResolver = {
                    ListPagingUtils.pageTokenValueResolver(offset?.value, request.pageSize, snapshots.lastIndex)
                },
            )

        val page = stream.getPageFlow(request.httpRequest, request.pageSize, request.pagingFrom)
        return Response.chunkedResponse(
            items = page,
            headers = headers,
            format = if (request.format == "csv") ChunkedResponseFormat.Csv() else ChunkedResponseFormat.Json(allowNullValues = true),
        )
    }

    private fun parseAtTime(time: String?): FunctionResult<Pair<String, String>, Instant?> {
        val atTime =
            if (time == "now") {
                Instant.now()
            } else {
                time?.let {
                    TimeUtils.parseTimeAndRound(time, UTC, roundToLatest = false).getOrElse {
                        return ("at_time" to it).toFailure()
                    }
                }
            }
        return atTime.toSuccess()
    }
}
