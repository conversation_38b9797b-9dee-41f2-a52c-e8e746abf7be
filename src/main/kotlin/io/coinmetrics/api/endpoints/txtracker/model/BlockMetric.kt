package io.coinmetrics.api.endpoints.txtracker.model

import io.coinmetrics.api.utils.CommonUtils
import io.coinmetrics.atlas.v2.codec.Codec
import java.math.BigInteger
import java.sql.ResultSet

data class BlockMetric(
    val blockHeight: BigInteger,
    val blockHash: String,
    val metric: String,
    val value: String,
) {
    companion object {
        fun blockMetricMapper(asset: String) =
            { rs: ResultSet ->
                BlockMetric(
                    blockHash = Codec.encodeHash(asset, rs.getBytes("block_hash")),
                    blockHeight = rs.getBigDecimal("block_height").toBigInteger(),
                    metric = rs.getString("name"),
                    value = CommonUtils.formatBigDecimal(rs.getBigDecimal("value")),
                )
            }
    }
}
