package io.coinmetrics.api.endpoints.txtracker.model

import io.coinmetrics.atlas.v2.codec.Codec
import io.coinmetrics.databases.getLongOrNull
import java.math.BigInteger
import java.sql.ResultSet
import java.time.Instant

data class Block(
    val blockHeight: BigInteger,
    val blockHash: String,
    val parentBlockHash: String?,
    val consensusTime: Instant?,
    val physicalSize: Long?,
    val nTransactions: Int?,
) {
    companion object {
        fun getBlockMapper(
            rs: ResultSet,
            asset: String,
        ): Block =
            Block(
                blockHash = Codec.encodeHash(asset, rs.getBytes("hash")),
                blockHeight = rs.getInt("height").toBigInteger(),
                parentBlockHash = Codec.encodeHash(asset, rs.getBytes("parent")),
                consensusTime = rs.getTimestamp("consensus_time").toInstant(),
                physicalSize = rs.getLongOrNull("physical_size"),
                nTransactions = rs.getInt("n_transactions"),
            )
    }
}
