package io.coinmetrics.api.endpoints.txtracker

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.ApiError.BadParameter
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.Response
import io.coinmetrics.api.Response.Companion.errorResponse
import io.coinmetrics.api.endpoints.GetTxTrackerEndpoint
import io.coinmetrics.api.endpoints.GetTxTrackerRequest
import io.coinmetrics.api.endpoints.txtracker.TxTrackerUtils.geoMapper
import io.coinmetrics.api.endpoints.txtracker.model.AddressType
import io.coinmetrics.api.endpoints.txtracker.model.LightFeerateBand.Companion.lightFeerateBandMapper
import io.coinmetrics.api.endpoints.txtracker.model.TransactionAddress
import io.coinmetrics.api.endpoints.txtracker.model.TxStatusUpdate
import io.coinmetrics.api.endpoints.txtracker.model.TxStatusUpdate.Companion.statusUpdateMapper
import io.coinmetrics.api.endpoints.txtracker.model.TxTrackerMode
import io.coinmetrics.api.endpoints.txtracker.model.WrappedTxTrackerTransaction
import io.coinmetrics.api.endpoints.txtracker.model.WrappedTxTrackerTransaction.Companion.wrappedTxMapper
import io.coinmetrics.api.models.PagingFrom
import io.coinmetrics.api.models.TxTrackerResponse
import io.coinmetrics.api.models.TxTrackerTxOutput
import io.coinmetrics.api.models.TxTrackerTxStatusUpdate
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.getTimeseriesRateLimitHeaders
import io.coinmetrics.api.types.Bytes
import io.coinmetrics.api.utils.BlockchainUtils
import io.coinmetrics.api.utils.CommonEndpointUtils
import io.coinmetrics.api.utils.DataUtils
import io.coinmetrics.api.utils.QueryTextBuilder
import io.coinmetrics.api.utils.SqlUtils
import io.coinmetrics.api.utils.TimeUtils
import io.coinmetrics.api.utils.paging.Page
import io.coinmetrics.api.utils.paging.PageToken
import io.coinmetrics.api.utils.paging.PagingUtils
import io.coinmetrics.api.utils.paging.getPage
import io.coinmetrics.api.utils.toHex
import io.coinmetrics.atlas.v2.codec.Codec
import io.coinmetrics.databases.Database
import java.time.Instant

class TxTrackerEndpointImpl(
    private val chainMonitorDb: Database,
    private val amsService: AmsService,
    private val assetHeightResolver: suspend (String) -> Int?,
) : GetTxTrackerEndpoint() {
    private val assetsWithAddressSupport = setOf("btc")

    override suspend fun handle(request: GetTxTrackerRequest): Response<TxTrackerResponse> {
        val headers =
            amsService
                .getTimeseriesRateLimitHeaders(
                    apiKey = request.apiKey,
                    httpRequest = request.httpRequest,
                ).getOrElse { (apiError, rateLimitHeaders) -> return errorResponse(apiError, rateLimitHeaders) }

        val asset = request.asset.lowercase()
        amsService
            .check(
                apiKey = request.apiKey,
                resource = "tx_tracker",
                parameters =
                    hashMapOf(
                        "asset" to asset,
                    ),
            ) { "asset" }
            .getOrElse { (apiError, _) -> return errorResponse(apiError) }

        val mode = TxTrackerMode.fromAsset(asset)

        if (mode == TxTrackerMode.ATLAS) {
            for (
            (v, n) in listOf(
                request.replacementsForTxids to "replacements_for_txids",
                request.replacementsOnly to "replacements_only",
            )
            ) {
                if (v !== null) {
                    return errorResponse(
                        BadParameter(
                            n,
                            "Not supported for '$asset' asset.",
                        ),
                        headers,
                    )
                }
            }
        }

        CommonEndpointUtils
            .validateMutuallyExclusiveFilters(
                listOf(
                    "replacements_for_txids" to request.replacementsForTxids,
                    "txids" to request.txids,
                    "addresses" to request.addresses,
                ),
            ).getOrElse { return errorResponse(it, headers) }

        val addresses = request.addresses?.sorted()?.toList() ?: emptyList()

        if (addresses.isEmpty() && request.unconfirmedOnly != null) {
            return errorResponse(
                BadParameter(
                    "unconfirmed_only",
                    "'unconfirmed_only' parameter can be used only in conjunction with 'addresses' parameter.",
                ),
                headers,
            )
        }

        if (addresses.isNotEmpty() && !assetsWithAddressSupport.contains(asset)) {
            return errorResponse(BadParameter("addresses", "Not supported for '$asset' asset."), headers)
        }

        val normalizedTxids =
            (request.txids ?: emptyList())
                .map {
                    BlockchainUtils.normalizeTransactionHash(
                        asset = asset,
                        hash = SqlUtils.escapeSql(it),
                    )
                }.sorted()

        val replacedTxids =
            (request.replacementsForTxids ?: emptyList())
                .map {
                    BlockchainUtils.normalizeTransactionHash(
                        asset = asset,
                        hash = SqlUtils.escapeSql(it),
                    )
                }.sorted()

        normalizedTxids.find { !BlockchainUtils.isHexEncodedString(it) }?.also { txid ->
            return errorResponse(BadParameter("txids", "Invalid format of txid '$txid'."), headers)
        }

        replacedTxids.find { !BlockchainUtils.isHexEncodedString(it) }?.also { txid ->
            return errorResponse(BadParameter("replacements_for_txids", "Invalid format of txid '$txid'."), headers)
        }

        return when (
            val result =
                handleInternal(
                    request = request,
                    asset = asset,
                    normalizedTxids = normalizedTxids,
                    onlyReplacementTxs = request.replacementsOnly == true,
                    replacedTxids = replacedTxids,
                    addresses = addresses,
                    unconfirmedOnly = request.unconfirmedOnly ?: true,
                    mode = mode,
                )
        ) {
            is FunctionResult.Success -> Response.successResponse(result.value, headers)
            is FunctionResult.Failure -> errorResponse(result.value, headers)
        }
    }

    suspend fun handleInternal(
        request: GetTxTrackerRequest,
        asset: String,
        normalizedTxids: List<String>,
        onlyReplacementTxs: Boolean,
        replacedTxids: List<String>,
        addresses: List<String>,
        unconfirmedOnly: Boolean,
        mode: TxTrackerMode,
        populateAddresses: Boolean = true,
    ): FunctionResult<ApiError, TxTrackerResponse> {
        val tableName = "${asset}_tx_tracker"

        val additionalWhereExpression =
            when {
                normalizedTxids.isNotEmpty() -> {
                    val params =
                        normalizedTxids.joinToString(separator = ",") {
                            "'\\x$it'"
                        }
                    "AND txid IN ($params)"
                }

                replacedTxids.isNotEmpty() -> {
                    val params =
                        replacedTxids.joinToString(separator = ",") {
                            "'\\x$it'"
                        }
                    "AND replaced_txid IN ($params)"
                }

                else -> ""
            }

        val onlyReplacementTxsWhereExpression =
            when {
                onlyReplacementTxs -> {
                    "AND replaced_txid IS NOT NULL"
                }

                else -> ""
            }

        val initialState =
            request.nextPageToken?.let {
                try {
                    PageToken.TimeAndByteArrayPageToken.parse(it)
                } catch (e: Exception) {
                    log.error("Can't parse provided next_page_token '{}'.", it, e)
                    return FunctionResult.Failure(
                        BadParameter(
                            "next_page_token",
                        ),
                    )
                }
            }

        val rangeQueryFunctionResult =
            DataUtils.createTimeAndByteArrayRangeQuery(
                request.startTime,
                request.startInclusive,
                request.endTime,
                request.endInclusive,
                request.timezone,
                request.pagingFrom,
            )

        val rangeQuery =
            when (rangeQueryFunctionResult) {
                is FunctionResult.Failure -> return FunctionResult.Failure(rangeQueryFunctionResult.value)
                is FunctionResult.Success -> rangeQueryFunctionResult.value
            }

        val timeFilter = DataUtils.createTimeFilter(rangeQuery)

        val additionalFilter: (WrappedTxTrackerTransaction) -> Boolean = {
            timeFilter.invoke(it.firstSeenTime)
        }

        val queryTextBuilder: QueryTextBuilder = { filter, limit ->
            val ordering = if (request.pagingFrom == PagingFrom.START) "ASC" else "DESC"
            if (addresses.isNotEmpty()) {
                createAddressFilteringQuery(
                    asset = asset,
                    addresses = addresses,
                    unconfirmedOnly = unconfirmedOnly,
                    filter = filter,
                    ordering = ordering,
                    limit = limit,
                )
            } else if (additionalWhereExpression.isEmpty()) {
                """
                SELECT
                    *
                FROM $tableName
                    WHERE TRUE $filter
                    $onlyReplacementTxsWhereExpression
                ORDER BY first_seen_time $ordering, txid $ordering
                LIMIT $limit
                """
            } else {
                """
                WITH t1 AS (
                    SELECT
                        *
                    FROM $tableName
                    WHERE TRUE $additionalWhereExpression
                    $onlyReplacementTxsWhereExpression
                )
                SELECT
                    *
                FROM t1
                WHERE TRUE $filter
                ORDER BY first_seen_time $ordering, txid $ordering
                LIMIT $limit
                """
            }
        }

        val keyNames = if (addresses.isNotEmpty()) arrayOf("tx_time", "tx_id") else arrayOf("first_seen_time", "txid")

        val stream =
            DataUtils
                .createStream(
                    db = chainMonitorDb,
                    queryTextBuilder = queryTextBuilder,
                    bufferSize = request.pageSize + 1,
                    keyNames = keyNames,
                    dataMapper = wrappedTxMapper,
                    rangeQuery = rangeQuery,
                    initialState = initialState,
                    stateResolver = { PageToken.TimeAndByteArrayPageToken(it.firstSeenTime, it.txid) },
                    streamId = null,
                ).filter(additionalFilter)

        @Suppress("DEPRECATION")
        val page = stream.getPage(request.pageSize, request.pagingFrom == PagingFrom.START)
        // todo: PLAT-708 this part of code is not expected after "getPage". It prevents the migration of this endpoint to Response.chunkedResponse. All transformations must be done before calling getPage.

        val geoByTxid =
            if (mode == TxTrackerMode.ATLAS || page.items.isEmpty()) {
                emptyMap()
            } else {
                chainMonitorDb
                    .query(
                        """
                        SELECT * FROM ${request.asset}_tx_tracker_geo
                        WHERE txid IN (${page.items.joinToString(separator = ",") { "'\\x${it.txid.toHex()}'" }})
                        """.trimIndent(),
                    ) {
                        it.map(geoMapper).toList()
                    }.groupBy({ it.first }, { it.second })
            }

        val statusUpdatesByTxid =
            if (page.items.isEmpty()) {
                emptyMap()
            } else {
                val txIdToFirstSeenTimes = page.items.associate { Bytes(it.txid) to it.firstSeenTime }
                chainMonitorDb
                    .query(
                        """
                        SELECT * FROM ${request.asset}_tx_tracker_status_updates
                        WHERE txid IN (${page.items.joinToString(separator = ",") { "'\\x${it.txid.toHex()}'" }})
                        """.trimIndent(),
                    ) {
                        it.map(statusUpdateMapper).toList()
                    }.groupByTo(HashMap()) { Bytes(it.txid) }
                    .mapValuesTo(HashMap()) { (txid, list) ->
                        TxTrackerUtils.mergeTxStatusUpdateHistory(txIdToFirstSeenTimes[txid]!!, list)
                    }
            }

        val mostRecentMempoolFeerates =
            if (mode == TxTrackerMode.ATLAS ||
                page.items.isEmpty() ||
                page.items.none { el ->
                    statusUpdatesByTxid[Bytes(el.txid)]
                        ?.maxByOrNull { it.time }
                        ?.status == TxStatusUpdate.TxStatus.UNCONFIRMED
                }
            ) {
                emptyList()
            } else {
                chainMonitorDb
                    .query(
                        """
                        SELECT feerate, count, consensus_size
                        FROM ${request.asset}_mempool_feerates
                        ORDER BY time DESC
                        LIMIT 1
                        """.trimIndent(),
                    ) {
                        it.map(lightFeerateBandMapper).toList()
                    }.firstOrNull()
                    ?: emptyList()
            }

        val chainHeight =
            if (statusUpdatesByTxid.values
                    .asSequence()
                    .flatten()
                    .any { it.height != null }
            ) {
                assetHeightResolver.invoke(request.asset)
            } else {
                null
            }
        val addressesByTx = if (populateAddresses) findAssociatedAddresses(asset, page) else emptyMap()

        val pageData =
            page.items.map { txWrapper ->
                // TODO the tx can be potentially deleted already, fix it
                val statusUpdates = statusUpdatesByTxid.getValue(Bytes(txWrapper.txid))
                val lastStatusUpdate = statusUpdates.maxByOrNull { it.time }!!
                val geo =
                    if (mode == TxTrackerMode.MEMPOOL) {
                        geoByTxid[Bytes(txWrapper.txid)] ?: emptyList()
                    } else {
                        null
                    }

                val (height, blockHash) =
                    if (mode == TxTrackerMode.MEMPOOL) {
                        // fill height & block_hash if we have at least one CONFIRMED status update in the history
                        val confirmedStatusUpdates = statusUpdates.filter { it.status == TxStatusUpdate.TxStatus.CONFIRMED }
                        if (confirmedStatusUpdates.isNotEmpty()) {
                            val lastConfirmedStatusUpdate = confirmedStatusUpdates.maxByOrNull { it.time }!!
                            lastConfirmedStatusUpdate.height to lastConfirmedStatusUpdate.blockHash
                        } else {
                            null to null
                        }
                    } else {
                        statusUpdates.lastOrNull()?.let { it.height to it.blockHash }
                            ?: (null to null)
                    }

                val mempoolPrediction =
                    if (mode == TxTrackerMode.MEMPOOL) {
                        TxTrackerUtils
                            .getMempoolPrediction(
                                request.asset,
                                lastStatusUpdate.status,
                                txWrapper.data.details.feerate,
                                mostRecentMempoolFeerates,
                            )
                    } else {
                        null
                    }
                val txAddresses = addressesByTx[txWrapper.data.txid]
                txWrapper.data.copy(
                    time = TimeUtils.dateTimeFormatter.format(Instant.ofEpochSecond(request.httpRequest.timeSec)),
                    status = lastStatusUpdate.status.name,
                    statusUpdateTime = TimeUtils.dateTimeFormatter.format(lastStatusUpdate.time),
                    statusUpdates =
                        statusUpdates.map { update ->
                            TxTrackerTxStatusUpdate(
                                time = TimeUtils.dateTimeFormatter.format(update.time),
                                status = update.status.name,
                                height = update.height?.toString(),
                                blockHash = update.blockHash?.toHex(),
                                removalReason = update.removalReason?.name,
                                replacementTxid = update.replacementTxid?.toHex(),
                            )
                        },
                    height = height?.toString(),
                    blockHash = blockHash?.toHex(),
                    nConfirmations =
                        height?.let { txHeight ->
                            // handle chainHeight = null and chainHeight < txHeight cases
                            val effectiveChainHeight = chainHeight?.let { maxOf(it, txHeight) } ?: txHeight
                            (effectiveChainHeight - txHeight + 1).toString()
                        },
                    mempoolApproxQueuePosition = mempoolPrediction?.approxQueuePosition,
                    nextBlockApproxSettlementProbabilityPct = mempoolPrediction?.nextBlockApproxSettlementProbabilityPct,
                    geo = geo?.sortedBy { it.seenTime },
                    inputs = txAddresses?.toTxOutputs(AddressType.INPUT),
                    outputs = txAddresses?.toTxOutputs(AddressType.OUTPUT),
                )
            }

        val nextPageUrl = page.nextPageToken?.let { PagingUtils.createNextPageUrl(request.httpRequest, it) }

        return FunctionResult.Success(
            TxTrackerResponse(
                data = pageData,
                nextPageToken = page.nextPageToken,
                nextPageUrl = nextPageUrl,
            ),
        )
    }

    private fun createAddressFilteringQuery(
        asset: String,
        addresses: List<String>,
        unconfirmedOnly: Boolean,
        filter: String,
        ordering: String,
        limit: Int,
    ): String {
        val addressValuesSql =
            addresses.joinToString(separator = ",") {
                val decodedAddress = Codec.decodeAccount(asset, it)
                val sqlParam = DataUtils.convertByteArrayToSqlParam(decodedAddress)
                "($sqlParam::bytea)"
            }
        val unconfirmedFilterSql = if (unconfirmedOnly) "tx_last_status = 'UNCONFIRMED'" else "TRUE"

        // read more about the motivation behind this query in https://docs.google.com/document/d/1XQdX8xUhVjFvG7ZszHaOTs8-ApiajhOFTyucwwP33po#heading=h.5ferzdk6f7kn
        return """
            WITH transactions AS (SELECT DISTINCT tx_time, tx_id
                       FROM (VALUES $addressValuesSql) addresses(address)
                                CROSS JOIN LATERAL (
                           SELECT DISTINCT tx_time, tx_id
                           FROM ${asset}_tx_tracker_addresses tx_addresses
                           WHERE $unconfirmedFilterSql 
                             AND tx_addresses.address = addresses.address
                             $filter
                           ORDER BY 1 $ordering, 2 $ordering
                           LIMIT $limit
                           ) t
                       ORDER BY 1 $ordering, 2 $ordering
                       LIMIT $limit)
            SELECT *
            FROM ${asset}_tx_tracker tracker
                    INNER JOIN transactions ON tracker.txid = transactions.tx_id AND tracker.first_seen_time = transactions.tx_time
            ORDER BY tracker.first_seen_time $ordering, tracker.txid $ordering;
            """.trimIndent()
    }

    private suspend fun findAssociatedAddresses(
        asset: String,
        page: Page<WrappedTxTrackerTransaction>,
    ): Map<String, List<TransactionAddress>> {
        if (!assetsWithAddressSupport.contains(asset) || page.items.isEmpty()) {
            return emptyMap()
        }
        val txIdsSql = page.items.joinToString(separator = ",") { "'\\x${it.txid.toHex()}'" }
        return chainMonitorDb.query(
            """
            SELECT tx_id, address, address_type, address_index
            FROM ${asset}_tx_tracker_addresses
            WHERE tx_id IN ($txIdsSql)
            """.trimIndent(),
        ) { query ->
            query
                .map {
                    val txId = it.getBytes("tx_id").toHex()
                    val address = Codec.encodeAccount(asset, it.getBytes("address"))
                    val addressType = AddressType.valueOf(it.getString("address_type"))
                    val addressIndex = it.getInt("address_index")
                    txId to TransactionAddress(address, addressType, addressIndex)
                }.groupBy(keySelector = { it.first }, valueTransform = { it.second })
        }
    }

    private fun List<TransactionAddress>.toTxOutputs(type: AddressType) =
        filter { it.type == type }
            .sortedBy { it.index }
            .map { TxTrackerTxOutput(it.address) }
            .takeIf { it.isNotEmpty() }
}
