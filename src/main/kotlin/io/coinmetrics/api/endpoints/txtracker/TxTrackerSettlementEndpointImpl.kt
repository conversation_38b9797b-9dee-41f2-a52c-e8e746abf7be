package io.coinmetrics.api.endpoints.txtracker

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.Response
import io.coinmetrics.api.endpoints.GetTxTrackerRequest
import io.coinmetrics.api.endpoints.GetTxTrackerSettlementEndpoint
import io.coinmetrics.api.endpoints.GetTxTrackerSettlementRequest
import io.coinmetrics.api.endpoints.txtracker.model.Block
import io.coinmetrics.api.endpoints.txtracker.model.BlockMetric.Companion.blockMetricMapper
import io.coinmetrics.api.endpoints.txtracker.model.TxStatusUpdate
import io.coinmetrics.api.endpoints.txtracker.model.TxTrackerMode
import io.coinmetrics.api.endpoints.txtracker.model.VirtualBlockType
import io.coinmetrics.api.models.TxTrackerSettlementBlock
import io.coinmetrics.api.models.TxTrackerSettlementResponse
import io.coinmetrics.api.models.TxTrackerTransaction
import io.coinmetrics.api.models.TxTrackerTxDetails
import io.coinmetrics.api.persistence.Databases
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.getTimeseriesRateLimitHeaders
import io.coinmetrics.api.utils.BlockchainUtils
import io.coinmetrics.api.utils.CommonUtils
import io.coinmetrics.api.utils.SqlUtils
import io.coinmetrics.api.utils.TimeUtils
import io.coinmetrics.databases.Database
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import java.math.BigDecimal
import java.math.BigInteger
import java.sql.ResultSet
import java.time.Instant

class TxTrackerSettlementEndpointImpl(
    private val databases: Databases,
    private val amsService: AmsService,
    private val txTrackerEndpointImpl: TxTrackerEndpointImpl,
) : GetTxTrackerSettlementEndpoint() {
    override suspend fun handle(request: GetTxTrackerSettlementRequest): Response<TxTrackerSettlementResponse> {
        val headers =
            amsService
                .getTimeseriesRateLimitHeaders(
                    apiKey = request.apiKey,
                    httpRequest = request.httpRequest,
                ).getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }

        val asset = SqlUtils.escapeSql(request.asset).lowercase()

        amsService
            .check(
                apiKey = request.apiKey,
                resource = "tx_tracker_settlement",
                parameters =
                    hashMapOf(
                        "asset" to request.asset,
                    ),
            ) { "asset" }
            .getOrElse { (apiError, _) -> return Response.errorResponse(apiError) }

        val txids =
            (request.txids?.asSequence() ?: sequenceOf())
                .map {
                    BlockchainUtils.normalizeTransactionHash(
                        asset = asset,
                        hash = SqlUtils.escapeSql(it),
                    )
                }.sorted()
                .toList()

        txids.find { !BlockchainUtils.isValidBtcTransactionId(it) }?.also { txid ->
            return Response.errorResponse(ApiError.BadParameter("txids", "Invalid format of txid '$txid'."), headers)
        }

        return when (
            val result =
                handleInternal(
                    request,
                    normalizedTxids = txids,
                )
        ) {
            is FunctionResult.Success -> Response.successResponse(result.value, headers)
            is FunctionResult.Failure -> Response.errorResponse(result.value, headers)
        }
    }

    suspend fun handleInternal(
        request: GetTxTrackerSettlementRequest,
        normalizedTxids: List<String>,
    ): FunctionResult<ApiError, TxTrackerSettlementResponse> {
        val asset = request.asset
        val mode = TxTrackerMode.fromAsset(asset)

        val blocksDb = databases.resolveAtlasDatabase(asset)
        val lastHeight =
            getLastHeight(
                asset = asset,
                blocksDb = blocksDb,
            )

        val blockIdsToTxs =
            getBlockIdsToTxs(request, normalizedTxids, lastHeight, mode, blocksDb)
                .getOrElse {
                    return FunctionResult.Failure(it)
                }

        val heights =
            (
                blockIdsToTxs.keys.mapNotNull { (height, _) -> height } +
                    ((lastHeight - request.recentBlocks + 1)..lastHeight).toList().map { it.toString() }
            ).distinct()
        val (
            blockIdsToBlocks,
            blockIdsToMetrics,
            lastMempoolNextBlockInclusionApproxFeerateMin,
        ) =
            coroutineScope {
                val blockIdsToBlocks =
                    async {
                        getBlockIdsToBlocks(
                            asset = asset,
                            blocksDb = blocksDb,
                            heights = heights,
                        )
                    }

                val blockIdsToMetrics =
                    async {
                        getBlockIdsToMetrics(
                            asset = asset,
                            chainMonitorDb = databases.chainMonitor,
                            heights = heights,
                        )
                    }

                val lastMempoolNextBlockInclusionApproxFeerateMin =
                    if (mode == TxTrackerMode.MEMPOOL) {
                        async {
                            getLastMempoolNextBlockInclusionApproxFeerateMin(asset)
                        }
                    } else {
                        null
                    }

                Triple(
                    blockIdsToBlocks.await(),
                    blockIdsToMetrics.await(),
                    lastMempoolNextBlockInclusionApproxFeerateMin?.await(),
                )
            }

        val settlementErrorTxs = mutableListOf<TxTrackerTransaction>()
        val lowFeeTxs = mutableListOf<TxTrackerTransaction>()
        val potentialNextBlockTxs = mutableListOf<TxTrackerTransaction>()
        val unknownTxs = mutableListOf<TxTrackerTransaction>()
        // Transactions that are reported as confirmed by mempool but the block not available in Atlas yet.
        val partialBlockTxs = mutableListOf<TxTrackerTransaction>()
        val blocks = mutableListOf<TxTrackerSettlementBlock>()

        for ((blockHeight, blockHash) in (blockIdsToTxs.keys + blockIdsToBlocks.keys).distinct()) {
            val txs = blockIdsToTxs[blockHeight to blockHash]
            val block = blockIdsToBlocks[blockHeight to blockHash]

            if (txs == null && block != null) {
                blocks.add(
                    createBlock(
                        blockHeight = blockHeight,
                        blockHash = blockHash,
                        block = block,
                        txs = emptyList(),
                        blockIdsToMetrics = blockIdsToMetrics,
                    ),
                )
            } else if (txs != null && block == null) {
                txs.forEach { tx ->
                    val lastStatus = tx.statusUpdates.lastOrNull()
                    if (lastStatus != null &&
                        (
                            (
                                mode == TxTrackerMode.MEMPOOL &&
                                    lastStatus.status == TxStatusUpdate.TxStatus.REMOVED.toString() &&
                                    lastStatus.removalReason == null
                            ) ||
                                (mode == TxTrackerMode.ATLAS && lastStatus.status == TxStatusUpdate.TxStatus.UNCONFIRMED.toString())
                        )
                    ) {
                        settlementErrorTxs.add(tx)
                    } else {
                        when (mode) {
                            TxTrackerMode.MEMPOOL -> {
                                if (tx.details.feerate == null ||
                                    lastMempoolNextBlockInclusionApproxFeerateMin == null
                                ) {
                                    unknownTxs.add(tx)
                                } else if (tx.height == null) {
                                    tx.details.feerate.let { feerate ->
                                        if (feerate.toBigDecimal() >= lastMempoolNextBlockInclusionApproxFeerateMin) {
                                            potentialNextBlockTxs.add(tx)
                                        } else {
                                            lowFeeTxs.add(tx)
                                        }
                                    }
                                } else {
                                    partialBlockTxs.add(tx)
                                }
                            }

                            TxTrackerMode.ATLAS -> unknownTxs.add(tx)
                        }
                    }
                }
            } else if (txs != null && block != null) {
                val normalTxs = mutableListOf<TxTrackerTransaction>()
                txs.forEach { tx ->
                    val lastStatus = tx.statusUpdates.lastOrNull()
                    if (lastStatus != null &&
                        (
                            (
                                mode == TxTrackerMode.MEMPOOL &&
                                    lastStatus.status == TxStatusUpdate.TxStatus.REMOVED.toString() &&
                                    lastStatus.removalReason == null
                            ) ||
                                (mode == TxTrackerMode.ATLAS && lastStatus.status == TxStatusUpdate.TxStatus.UNCONFIRMED.toString())
                        )
                    ) {
                        settlementErrorTxs.add(tx)
                    } else {
                        normalTxs.add(tx)
                    }
                }

                blocks.add(
                    createBlock(
                        blockHeight = blockHeight,
                        blockHash = blockHash,
                        block = block,
                        txs = normalTxs,
                        blockIdsToMetrics = blockIdsToMetrics,
                    ),
                )
            }
        }

        if (partialBlockTxs.isNotEmpty()) {
            val heightToTxs = partialBlockTxs.groupBy { it.height!!.toInt() }
            for (txs in heightToTxs.values) {
                val tx = txs.first()
                blocks.add(
                    createBlock(
                        blockHeight = tx.height,
                        blockHash = tx.blockHash,
                        block =
                            Block(
                                blockHeight = tx.height!!.toBigInteger(),
                                tx.blockHash!!,
                                parentBlockHash = null,
                                consensusTime = null,
                                physicalSize = null,
                                nTransactions = txs.size,
                            ),
                        txs,
                        blockIdsToMetrics,
                    ),
                )
            }
        }

        blocks.sortByDescending { it.height?.toBigInteger() }

        return FunctionResult.Success(
            TxTrackerSettlementResponse(
                data = (
                    listOf(
                        createVirtualBlock(
                            transactions = settlementErrorTxs,
                            type = VirtualBlockType.SETTLEMENT_ERROR,
                        ),
                        if (mode == TxTrackerMode.MEMPOOL) {
                            createVirtualBlock(
                                transactions = lowFeeTxs + unknownTxs,
                                type = VirtualBlockType.LOW_FEE,
                            )
                        } else {
                            createVirtualBlock(
                                transactions = unknownTxs,
                                type = VirtualBlockType.UNKNOWN,
                            )
                        },
                        createVirtualBlock(
                            transactions = potentialNextBlockTxs,
                            type = VirtualBlockType.POTENTIAL_NEXT_BLOCK,
                        ),
                    ) + blocks
                ),
            ),
        )
    }

    private suspend fun getLastHeight(
        asset: String,
        blocksDb: Database,
    ): Int =
        blocksDb
            .query(
                """
                SELECT height 
                FROM ${blocksDb.config.schema}."${asset}_udm_v2_blocks" 
                ORDER BY height DESC
                LIMIT 1
                """.trimIndent(),
            ) {
                it
                    .map { rs: ResultSet ->
                        rs.getString("height").toInt()
                    }.toList()
            }.first()

    private suspend fun getBlockIdsToBlocks(
        asset: String,
        blocksDb: Database,
        heights: List<String>,
    ): Map<Pair<String, String>, Block> =
        blocksDb
            .query(
                """
                SELECT hash, height, parent, consensus_time, physical_size, n_transactions 
                FROM ${blocksDb.config.schema}.${asset}_udm_v2_blocks 
                WHERE height IN (${heights.joinToString(separator = ",")})
                """.trimIndent(),
            ) {
                it.map { rs -> Block.getBlockMapper(rs, asset) }.toList()
            }.associateBy(
                {
                    Pair(
                        it.blockHeight.toString(),
                        it.blockHash,
                    )
                },
                {
                    it
                },
            )

    private suspend fun getBlockIdsToMetrics(
        asset: String,
        chainMonitorDb: Database,
        heights: List<String>,
    ): Map<Triple<BigInteger, String, String>, String> =
        chainMonitorDb
            .query(
                """
                SELECT block_height, block_hash, name, value
                FROM ${chainMonitorDb.config.schema}.${asset}_bbb_metrics 
                WHERE name IN (
                        'block_feerate_min',
                        'block_feerate_max',
                        'block_feerate_mean',
                        'block_fees'
                    ) AND block_height IN (${heights.joinToString(separator = ",")})
                """.trimIndent(),
            ) {
                it.map(blockMetricMapper(asset)).toList()
            }.associateBy(
                {
                    Triple(
                        it.blockHeight,
                        it.blockHash,
                        it.metric,
                    )
                },
                {
                    it.value
                },
            )

    private suspend fun getLastMempoolNextBlockInclusionApproxFeerateMin(asset: String): BigDecimal? =
        databases.chainMonitor
            .query(
                """
                SELECT value
                FROM ${asset}_metrics 
                WHERE name='mempool_next_block_inclusion_approx_feerate_min'
                ORDER BY time DESC
                LIMIT 1
                """.trimIndent(),
            ) {
                it
                    .map { rs: ResultSet ->
                        rs.getString("value")?.toBigDecimal()
                    }.toList()
            }.firstOrNull()

    private fun createVirtualBlock(
        transactions: List<TxTrackerTransaction>,
        type: VirtualBlockType,
    ): TxTrackerSettlementBlock =
        TxTrackerSettlementBlock(
            transactions = transactions,
            virtualBlockType = type.toString(),
        )

    private fun createBlock(
        blockHeight: String?,
        blockHash: String?,
        block: Block,
        txs: List<TxTrackerTransaction>,
        blockIdsToMetrics: Map<Triple<BigInteger, String, String>, String>,
    ): TxTrackerSettlementBlock =
        TxTrackerSettlementBlock(
            height = blockHeight,
            blockHash = blockHash,
            parentBlockHash = block.parentBlockHash,
            consensusTime =
                block.consensusTime?.let {
                    TimeUtils.dateTimeFormatter.format(it)
                },
            physicalSize = block.physicalSize?.toString(),
            nTransactions = block.nTransactions?.toString(),
            transactions = txs,
            feerateMin =
                blockIdsToMetrics[
                    Triple(
                        blockHeight?.toBigInteger(),
                        blockHash,
                        "block_feerate_min",
                    ),
                ],
            feerateMax =
                blockIdsToMetrics[
                    Triple(
                        blockHeight?.toBigInteger(),
                        blockHash,
                        "block_feerate_max",
                    ),
                ],
            feerateMean =
                blockIdsToMetrics[
                    Triple(
                        blockHeight?.toBigInteger(),
                        blockHash,
                        "block_feerate_mean",
                    ),
                ],
            fees =
                blockIdsToMetrics[
                    Triple(
                        blockHeight?.toBigInteger(),
                        blockHash,
                        "block_fees",
                    ),
                ],
        )

    private suspend fun getBlockIdsToTxs(
        request: GetTxTrackerSettlementRequest,
        normalizedTxids: List<String>,
        lastHeight: Int,
        mode: TxTrackerMode,
        blocksDb: Database,
    ): FunctionResult<ApiError, Map<Pair<String?, String?>, List<TxTrackerTransaction>>> {
        if (request.txids?.isEmpty() != false) {
            return FunctionResult.Success(mapOf())
        }
        val txs =
            txTrackerEndpointImpl
                .handleInternal(
                    request =
                        GetTxTrackerRequest(
                            httpRequest = request.httpRequest,
                            apiKey = request.apiKey,
                            asset = request.asset,
                            txids = request.txids,
                            replacementsForTxids = null,
                            replacementsOnly = null,
                            startTime = null,
                            endTime = null,
                            pageSize = Int.MAX_VALUE - 1000,
                            nextPageToken = null,
                            addresses = null,
                            unconfirmedOnly = null,
                        ),
                    asset = request.asset,
                    normalizedTxids = normalizedTxids,
                    onlyReplacementTxs = false,
                    replacedTxids = listOf(),
                    addresses = emptyList(),
                    unconfirmedOnly = false,
                    mode = mode,
                    populateAddresses = false,
                ).getOrElse {
                    return FunctionResult.Failure(it)
                }.data

        val untrackedTxIds = normalizedTxids - txs.asSequence().map { it.txid }.toSet()
        val untrackedTxs = findUntrackedTxs(untrackedTxIds, request.asset, blocksDb)

        val unknownTxIds = untrackedTxIds - untrackedTxs.asSequence().map { it.txid }.toSet()
        val unknownTxs =
            unknownTxIds
                .asSequence()
                .map { txId ->
                    val time = TimeUtils.dateTimeFormatter.format(Instant.ofEpochSecond(request.httpRequest.timeSec))
                    TxTrackerTransaction(
                        txId,
                        time,
                        firstSeenTime = time,
                        // Special status for the settlement endpoint only.
                        status = "UNKNOWN",
                        statusUpdateTime = time,
                        statusUpdates = emptyList(),
                        TxTrackerTxDetails(
                            "0",
                            fee = "0",
                        ),
                    )
                }

        return FunctionResult.Success(
            (txs.asSequence() + untrackedTxs.asSequence() + unknownTxs)
                .map {
                    val height = it.height?.toInt() ?: return@map it
                    val nConfirmations = maxOf(lastHeight, height) - height + 1
                    it.copy(nConfirmations = nConfirmations.toString())
                }.groupBy { Pair(it.height, it.blockHash) },
        )
    }

    private suspend fun findUntrackedTxs(
        normalizedTxIds: List<String>,
        asset: String,
        db: Database,
    ): List<TxTrackerTransaction> {
        if (normalizedTxIds.isEmpty()) {
            return listOf()
        }

        val txIdsSql = normalizedTxIds.joinToString(", ") { "'$it'" }
        return db.query(
            """
            SELECT hash, block_hash, block_height, consensus_time, amount
            FROM ${db.config.schema}.${asset}_udm_v2_transactions
            WHERE hash IN ($txIdsSql)
            """.trimIndent(),
        ) {
            it
                .map { rs: ResultSet ->
                    val txId = rs.getString("hash")
                    val blockHash = rs.getString("block_hash")
                    val blockHeight = rs.getString("block_height")
                    val consensusTimeStr = TimeUtils.dateTimeFormatter.format(rs.getTimestamp("consensus_time").toInstant())
                    val amountStr = CommonUtils.formatBigDecimal(rs.getBigDecimal("amount"))
                    TxTrackerTransaction(
                        txId,
                        time = consensusTimeStr,
                        firstSeenTime = consensusTimeStr,
                        status = TxStatusUpdate.TxStatus.CONFIRMED.name,
                        statusUpdateTime = consensusTimeStr,
                        statusUpdates = emptyList(),
                        TxTrackerTxDetails(
                            amountStr,
                            fee = "0",
                        ),
                        blockHash = blockHash,
                        height = blockHeight.toString(),
                    )
                }.toList()
        }
    }
}
