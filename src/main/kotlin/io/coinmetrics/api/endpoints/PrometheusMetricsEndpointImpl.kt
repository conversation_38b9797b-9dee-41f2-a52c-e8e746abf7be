package io.coinmetrics.api.endpoints

import io.coinmetrics.api.Response
import io.coinmetrics.httpserver.HttpRequest
import io.coinmetrics.httpserver.HttpResponse
import io.prometheus.metrics.expositionformats.ExpositionFormats
import io.prometheus.metrics.model.registry.PrometheusRegistry
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.withContext
import java.io.ByteArrayOutputStream

class PrometheusMetricsEndpointImpl(
    private val dispatcher: CoroutineDispatcher,
    private val registry: PrometheusRegistry,
) : Endpoint<Unit> {
    private val expositionFormats = ExpositionFormats.init()

    override suspend fun handle(httpRequest: HttpRequest): Response<Unit> =
        withContext(dispatcher) {
            val responseBuffer = ByteArrayOutputStream()
            val writer = expositionFormats.findWriter(httpRequest.headers["accept"])
            val snapshots = registry.scrape()
            writer.write(responseBuffer, snapshots)
            Response.rawHttpResponse(
                HttpResponse(
                    binaryBody = responseBuffer.toByteArray(),
                    contentType = writer.contentType,
                ),
            )
        }
}
