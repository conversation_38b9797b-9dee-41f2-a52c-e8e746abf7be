package io.coinmetrics.api.endpoints.taxonomy

import com.fasterxml.jackson.databind.ObjectMapper
import io.coinmetrics.api.Response
import io.coinmetrics.api.endpoints.GetTaxonomyAssetsEndpoint
import io.coinmetrics.api.endpoints.GetTaxonomyAssetsRequest
import io.coinmetrics.api.endpoints.taxonomy.utils.TaxonomyApiUtils
import io.coinmetrics.api.model.taxonomy.TaxonomyAssetRevision
import io.coinmetrics.api.model.taxonomy.request.TaxonomyAssetsRequest
import io.coinmetrics.api.models.TaxonomyAsset
import io.coinmetrics.api.models.TaxonomyAssetsResponse
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.TaxonomyService
import io.coinmetrics.api.service.getTaxonomyRateLimitHeaders
import io.coinmetrics.api.utils.convert
import io.coinmetrics.api.utils.paging.PagingUtils

class GetTaxonomyAssetsEndpointImpl(
    private val objectMapper: ObjectMapper,
    private val amsService: AmsService,
    private val taxonomyService: TaxonomyService,
) : GetTaxonomyAssetsEndpoint() {
    override suspend fun handle(request: GetTaxonomyAssetsRequest): Response<TaxonomyAssetsResponse> {
        val headers =
            amsService
                .getTaxonomyRateLimitHeaders(
                    apiKey = request.apiKey,
                    httpRequest = request.httpRequest,
                ).getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }

        amsService.check(apiKey = request.apiKey, resource = "taxonomy_data").getOrElse { (apiError) ->
            return Response.errorResponse(apiError, headers)
        }

        val classificationStartTime =
            TaxonomyApiUtils
                .parseStartTime(request.classificationStartTime, request.startInclusive)
                ?.getOrElse { return Response.errorResponse(it, headers) }

        val classificationEndTime =
            TaxonomyApiUtils
                .parseEndTime(request.classificationEndTime, request.endInclusive)
                ?.getOrElse { return Response.errorResponse(it, headers) }

        val version = request.version ?: "latest".takeIf { classificationStartTime == null && classificationEndTime == null }

        val taxonomyAssetsRequest =
            TaxonomyAssetsRequest(
                assets = request.assets?.toSet() ?: emptySet(),
                classIds = request.classIds?.toSet() ?: emptySet(),
                sectorIds = request.sectorIds?.toSet() ?: emptySet(),
                subsectorIds = request.subsectorIds?.toSet() ?: emptySet(),
                version = version,
                classificationStartTime = classificationStartTime,
                classificationEndTime = classificationEndTime,
                pageSize = request.pageSize + 1,
                pagingFrom = request.pagingFrom,
                nextPageToken =
                    request.nextPageToken?.let {
                        TaxonomyApiUtils.parsePageToken(it, expectedItemsSize = 2).getOrElse { error ->
                            return Response.errorResponse(error, headers)
                        }
                    },
            )

        val assetTaxonomy: List<TaxonomyAssetRevision> =
            taxonomyService.findAssetTaxonomy(taxonomyAssetsRequest).getOrElse {
                return Response.errorResponse(it, headers)
            }
        val (revisions, lastItem) =
            if (assetTaxonomy.size == taxonomyAssetsRequest.pageSize) {
                assetTaxonomy.take(taxonomyAssetsRequest.pageSize - 1) to assetTaxonomy.last()
            } else {
                assetTaxonomy to null
            }

        val nextPageToken = lastItem?.let { PagingUtils.createPageToken(it.asset, it.classificationStartTime.toEpochMilli()) }
        val nextPageUrl = nextPageToken?.let { PagingUtils.createNextPageUrl(request.httpRequest, it) }

        val result =
            revisions
                .sortedWith(compareBy({ it.asset }, { it.classificationStartTime }))
                .map { objectMapper.convert<TaxonomyAsset>(it) }

        return Response.successResponse(
            TaxonomyAssetsResponse(data = result, nextPageToken = nextPageToken, nextPageUrl = nextPageUrl),
            headers,
        )
    }
}
