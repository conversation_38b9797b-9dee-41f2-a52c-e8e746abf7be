package io.coinmetrics.api.endpoints.taxonomy.metadata

import com.fasterxml.jackson.databind.ObjectMapper
import io.coinmetrics.api.Response
import io.coinmetrics.api.endpoints.GetTaxonomyMetadataAssetsEndpoint
import io.coinmetrics.api.endpoints.GetTaxonomyMetadataAssetsRequest
import io.coinmetrics.api.endpoints.taxonomy.utils.TaxonomyApiUtils
import io.coinmetrics.api.model.taxonomy.request.TaxonomyMetadataAssetsRequest
import io.coinmetrics.api.models.TaxonomyMetadataAsset
import io.coinmetrics.api.models.TaxonomyMetadataAssetsResponse
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.TaxonomyService
import io.coinmetrics.api.service.getTaxonomyRateLimitHeaders
import io.coinmetrics.api.utils.convert
import io.coinmetrics.api.utils.paging.PagingUtils

class GetTaxonomyMetadataAssetsEndpointImpl(
    private val objectMapper: ObjectMapper,
    private val amsService: AmsService,
    private val taxonomyService: TaxonomyService,
) : GetTaxonomyMetadataAssetsEndpoint() {
    override suspend fun handle(request: GetTaxonomyMetadataAssetsRequest): Response<TaxonomyMetadataAssetsResponse> {
        val headers =
            amsService
                .getTaxonomyRateLimitHeaders(
                    apiKey = request.apiKey,
                    httpRequest = request.httpRequest,
                ).getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }

        amsService.check(apiKey = request.apiKey, resource = "taxonomy_data").getOrElse { (apiError) ->
            return Response.errorResponse(apiError, headers)
        }

        val startTime =
            TaxonomyApiUtils
                .parseStartTime(request.startTime, request.startInclusive)
                ?.getOrElse { return Response.errorResponse(it, headers) }

        val endTime =
            TaxonomyApiUtils
                .parseEndTime(request.endTime, request.endInclusive)
                ?.getOrElse { return Response.errorResponse(it, headers) }
        val version = request.version ?: "latest".takeIf { startTime == null && endTime == null }

        val taxonomyMetadataAssetsRequest =
            TaxonomyMetadataAssetsRequest(
                version = version,
                startTime = startTime,
                endTime = endTime,
                pageSize = request.pageSize + 1,
                pagingFrom = request.pagingFrom,
                nextPageToken =
                    request.nextPageToken?.let {
                        TaxonomyApiUtils.parsePageToken(it, expectedItemsSize = 1).getOrElse { error ->
                            return Response.errorResponse(error, headers)
                        }
                    },
            )

        val assetTaxonomyMetadata =
            taxonomyService.findAssetTaxonomyMetadata(taxonomyMetadataAssetsRequest).getOrElse {
                return Response.errorResponse(it, headers)
            }
        val (metadataList, lastItem) =
            if (assetTaxonomyMetadata.size == taxonomyMetadataAssetsRequest.pageSize) {
                assetTaxonomyMetadata.take(taxonomyMetadataAssetsRequest.pageSize - 1) to assetTaxonomyMetadata.last()
            } else {
                assetTaxonomyMetadata to null
            }

        val nextPageToken = lastItem?.let { PagingUtils.createPageToken(it.taxonomyVersion) }
        val nextPageUrl = nextPageToken?.let { PagingUtils.createNextPageUrl(request.httpRequest, it) }

        val result =
            metadataList
                .sortedWith(compareBy { it.taxonomyVersion })
                .map { objectMapper.convert<TaxonomyMetadataAsset>(it) }

        return Response.successResponse(
            TaxonomyMetadataAssetsResponse(data = result, nextPageToken = nextPageToken, nextPageUrl = nextPageUrl),
            headers,
        )
    }
}
