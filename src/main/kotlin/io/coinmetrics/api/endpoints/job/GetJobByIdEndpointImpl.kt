package io.coinmetrics.api.endpoints.job

import io.coinmetrics.api.Response
import io.coinmetrics.api.endpoints.GetJobByIdEndpoint
import io.coinmetrics.api.endpoints.GetJobByIdRequest
import io.coinmetrics.api.models.JobsDetailsResponse
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.getJobRateLimitHeaders
import io.coinmetrics.api.service.job.JobService

class GetJobByIdEndpointImpl(
    private val amsService: AmsService,
    private val jobService: JobService,
) : GetJobByIdEndpoint() {
    override suspend fun handle(request: GetJobByIdRequest): Response<JobsDetailsResponse> {
        val headers =
            amsService
                .getJobRateLimitHeaders(
                    apiKey = request.apiKey,
                    httpRequest = request.httpRequest,
                ).getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }

        return amsService
            .check(request.apiKey)
            .mapFailure { (err, _) -> err }
            .flatMap { jobService.findJob(request.ids, request.httpRequest.receivedTime) }
            .map {
                if (it == null) {
                    listOf()
                } else {
                    listOf(it)
                }
            }.map { Response.successResponse(obj = JobsDetailsResponse(it), headers = headers) }
            .getOrElse { return Response.errorResponse(it, headers) }
    }
}
