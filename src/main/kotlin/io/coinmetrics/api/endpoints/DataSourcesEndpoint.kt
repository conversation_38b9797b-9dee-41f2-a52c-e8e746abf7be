package io.coinmetrics.api.endpoints

import io.coinmetrics.api.Response
import io.coinmetrics.api.model.KafkaDataProcessorInfo
import io.coinmetrics.api.modules.ApiModule
import io.coinmetrics.api.utils.tiering.DynamicTierConfig
import io.coinmetrics.databases.Database
import io.coinmetrics.databases.DatabaseWithFailoverImpl
import io.coinmetrics.httpserver.HttpRequest
import io.coinmetrics.httpserver.HttpResponse

/**
 * Local test:
 * 1) Run the API server with the "API_DB_SCHEMA=test" env variable.
 * 2) Open the localhost:8080/v4/datasources?api_key=n3j0af-wn3Kanv49 in your browser.
 */
class DataSourcesEndpoint(
    private val modules: List<ApiModule>,
) : Endpoint<Unit> {
    private val databases: List<Database> by lazy {
        modules.flatMap { it.databases() }
    }

    private val kafkas: List<KafkaDataProcessorInfo> by lazy {
        modules.flatMap { it.kafkas() }
    }

    override suspend fun handle(httpRequest: HttpRequest): Response<Unit> {
        if (httpRequest.queryParameters["api_key"] != "n3j0af-wn3Kanv49") {
            return Response.rawHttpResponse(HttpResponse(status = 403))
        }

        val tiersByDataTypes: List<Pair<String, List<DynamicTierConfig>>> =
            modules.flatMap { it.tiers().toList() }

        val tiersHtml =
            tiersByDataTypes.joinToString(separator = "") { (dataType, tiers) ->
                val configsHtml =
                    tiers.joinToString(separator = "") { tier ->
                        """
                        <li>
                            <strong>${tier.name}</strong> Type: ${tier.type} Time range: ${tier.timeRange}
                        </li>
                        """.trimIndent()
                    }
                """
                <h3>$dataType</h3>
                <ol>
                    $configsHtml
                </ol>
                """.trimIndent()
            }

        val databasesHtml =
            databases.joinToString(separator = "") {
                val configs =
                    if (it is DatabaseWithFailoverImpl) {
                        val activeIndex = it.activeDatabaseIndex()
                        it.databases.mapIndexed { index, db ->
                            db.config to (index == activeIndex)
                        }
                    } else {
                        listOf(it.config to true)
                    }
                val configsHtml =
                    configs.joinToString(separator = "") { (dbConfig, currentlyUsed) ->
                        val used =
                            if (currentlyUsed) {
                                """ <span style="font-weight:bold;color:#00aa00">(currently used)</span>"""
                            } else {
                                ""
                            }
                        """
                        <li>
                            ${mask(dbConfig.jdbcUrl)}$used
                        </li>
                        """.trimIndent()
                    }
                """
                <h3>${it.config.dbConfigName}</h3>
                <ol>
                    $configsHtml
                </ol>
                """.trimIndent()
            }

        val kafkasHtml =
            kafkas.joinToString(separator = "") {
                val configsHtml =
                    it.config.kafkaServers
                        .mapIndexed { index, kafkaConfig ->
                            val used =
                                if (it.mainDataSourceIndex != null) {
                                    if (it.mainDataSourceIndex ==
                                        index
                                    ) {
                                        """ <span style="font-weight:bold;color:#00aa00">(currently used)</span>"""
                                    } else {
                                        ""
                                    }
                                } else {
                                    """ <span style="font-weight:bold;color:#00aa00">(currently used)</span>"""
                                }
                            """
                            <li>
                                ${kafkaConfig.serverUrl}$used
                            </li>
                            """.trimIndent()
                        }.joinToString(separator = "")
                """
                <h3>${it.config.configName.lowercase()} (topic: ${it.config.topicName})</h3>
                <ol>
                    $configsHtml
                </ol>
                """.trimIndent()
            }

        val body =
            """
            <html>
            <head>
                <title>Data sources</title>
                <style>
                body {
                    font-family: Arial;
                    background-color:#ffffff;
                    color:#000;
                }
                </style>
            </head>
            <body>
                <h1>Data sources</h1>
                <h2>Tiers</h2>
                $tiersHtml
                <h2>Databases<h2>
                $databasesHtml
                <h2>Kafka instances</h2>
                $kafkasHtml
            </body>
            </html>
            """.trimIndent()

        return Response.rawHttpResponse(
            HttpResponse(
                contentType = "text/html; charset=utf-8",
                body = body,
                headers = listOf("X-Robots-Tag" to "noindex"),
            ),
        )
    }

    private val userPattern = Regex("(user=)([^&]+)")
    private val passwordPattern = Regex("(password=)([^&]+)")

    private fun mask(jdbcUrl: String): String =
        jdbcUrl
            .replace(userPattern, "$1***")
            .replace(passwordPattern, "$1***")
            .removePrefix("jdbc:")
}
