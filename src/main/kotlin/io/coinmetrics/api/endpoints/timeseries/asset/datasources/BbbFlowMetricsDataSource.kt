package io.coinmetrics.api.endpoints.timeseries.asset.datasources

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.endpoints.GetTimeseriesAssetMetricsRequest
import io.coinmetrics.api.endpoints.timeseries.asset.AssetMetricsEndpointImpl
import io.coinmetrics.api.models.PagingFrom
import io.coinmetrics.api.toSuccess
import io.coinmetrics.api.utils.DataUtils
import io.coinmetrics.api.utils.QueryTextBuilder
import io.coinmetrics.api.utils.RangeQuery
import io.coinmetrics.api.utils.SqlUtils
import io.coinmetrics.api.utils.TimeUtils
import io.coinmetrics.api.utils.Utils
import io.coinmetrics.api.utils.paging.PageToken
import io.coinmetrics.api.utils.streams.SuspendableStream
import io.coinmetrics.api.utils.toSqlOrdering
import io.coinmetrics.databases.Database

class BbbFlowMetricsDataSource(
    private val db: Database,
) {
    internal data class BbbFlowMetricsDataSourceSpecificData(
        val rangeQuery: RangeQuery.BigIntegerAndStringRangeQuery?,
        val additionalFilter: ((BbbUtils.BlockMetricWithTimeAndHeightWrapper) -> Boolean)?,
    ) : AssetMetricsEndpointImpl.AssetMetricsDataSourceSpecificData

    suspend fun checkParameters(
        request: GetTimeseriesAssetMetricsRequest,
        asset: String,
        pagingFrom: PagingFrom,
    ): FunctionResult<ApiError, AssetMetricsEndpointImpl.AssetMetricsDataSourceSpecificData> {
        val tempMetricName = "TxExCnt"

        // cast 'time' range or 'hash' range to 'height' range
        val rangeQueryFunctionResult =
            if (request.startHash != null || request.endHash != null) {
                BbbUtils.createRangeQueryFromHashBoundaries(db, asset, pagingFrom, request, tempMetricName)
            } else if (request.startTime != null || request.endTime != null) {
                BbbUtils.createRangeQueryFromTimeBoundaries(db, asset, pagingFrom, request, tempMetricName)
            } else {
                BbbUtils.createRangeQueryFromHeightBoundaries(pagingFrom, request)
            }

        val (rangeQuery, additionalFilter) = rangeQueryFunctionResult.getOrElse { return FunctionResult.Failure(it) }

        return BbbFlowMetricsDataSourceSpecificData(rangeQuery, additionalFilter).toSuccess()
    }

    fun query(
        asset: String,
        patternRequested: Boolean,
        bufferSize: Int,
        pageToken: String?,
        metrics: List<String>,
        pagingFrom: PagingFrom,
        request: GetTimeseriesAssetMetricsRequest,
        rangeQuery: RangeQuery.BigIntegerAndStringRangeQuery?,
        additionalFilter: ((BbbUtils.BlockMetricWithTimeAndHeightWrapper) -> Boolean)?,
    ): SuspendableStream<BbbUtils.BlockMetricWithTimeAndHeightWrapper, PageToken.BigIntegerAndStringPageToken> {
        val assetSqlParam = SqlUtils.escapeSql(asset)
        val tableName = "${db.config.schema}.statistics_flows_realtime"
        val staleBlocksTableName = "${db.config.schema}.stale_blocks"
        val sortKey = "height"
        val minConfirmations =
            request.minConfirmations ?: when (asset) {
                "btc" -> 2
                "eth" -> 99
                else -> throw IllegalStateException("'min_confirmations' default value for '$asset' is not defined.")
            }

        val initialState =
            pageToken?.let {
                try {
                    PageToken.BigIntegerAndStringPageToken.parse(it)
                } catch (_: Exception) {
                    error("Can't parse provided next_page_token '$it'.")
                }
            }

        val rangeQueryWithPageToken = rangeQuery?.withPageToken(initialState)

        if (rangeQueryWithPageToken == null) {
            return SuspendableStream.empty()
        }

        val metricsInClauseSqlParams = metrics.joinToString { "'${SqlUtils.escapeSql(it)}'" }
        val statusFilter = Utils.getStatusFilter(request.status)
        val metricBlockFilter =
            if (statusFilter == "") {
                "metric='TxExCnt'"
            } else {
                "metric IN ($metricsInClauseSqlParams)"
            }

        // For block by block all the metrics in the single table are committed at once with a single transaction
        // that's why we can rely on a metric that always exists for all bbb assets.
        val queryTextBuilder: QueryTextBuilder = { filter, limit ->
            val ordering = pagingFrom.toSqlOrdering()
            """
                WITH heights AS (
                    SELECT 
                        DISTINCT height
                    FROM $tableName
                    WHERE 
                        asset='$assetSqlParam'
                        AND $metricBlockFilter
                        AND height <= (SELECT MAX(height)-$minConfirmations FROM $tableName WHERE asset='$assetSqlParam')
                        AND block_hash NOT IN (SELECT hash FROM $staleBlocksTableName WHERE asset='$assetSqlParam') 
                        $statusFilter
                        $filter
                    ORDER BY $sortKey $ordering
                    LIMIT $limit
                ), blocks AS (
                    SELECT 
                        DISTINCT $sortKey, block_hash, ENCODE(block_hash, 'hex') AS block_hash_encoded
                    FROM heights INNER JOIN $tableName USING ($sortKey)
                    WHERE 
                        asset='$assetSqlParam'
                        AND $metricBlockFilter 
                        AND block_hash NOT IN (SELECT hash FROM $staleBlocksTableName WHERE asset='$assetSqlParam') 
                        $statusFilter
                        $filter
                    ORDER BY $sortKey $ordering, block_hash_encoded $ordering 
                    LIMIT $limit
                )
                SELECT 
                    $sortKey, metric, value, block_hash_encoded AS block_hash,
                    ENCODE(parent_block_hash, 'hex') AS parent_block_hash, time, status, status_timestamp
                FROM blocks INNER JOIN $tableName USING ($sortKey, block_hash)
                WHERE
                    asset='$assetSqlParam'
                    AND metric IN ($metricsInClauseSqlParams)
                    $statusFilter
                ORDER BY $sortKey $ordering, block_hash_encoded $ordering
            """
        }

        return DataUtils
            .createStream(
                db = db,
                queryTextBuilder = queryTextBuilder,
                bufferSize = bufferSize,
                keyNames = arrayOf(sortKey, "ENCODE(block_hash, 'hex')"),
                dataMapper = BbbUtils.createRealtimeMetricsBlocksMapper(withStatus = true),
                rangeQuery = rangeQueryWithPageToken,
                initialState = initialState,
                stateResolver = { PageToken.BigIntegerAndStringPageToken(it.height, it.hash) },
                streamId = if (patternRequested) asset else null,
            ).collapseByKey { it.height to it.hash }
            .map { (heightHashPair, singleBlockData) ->
                val (height, hash) = heightHashPair
                val firstMetric = singleBlockData.first()

                val entryMetrics = ArrayList<Pair<String, String?>>()
                for (row in singleBlockData) {
                    entryMetrics.add(row.metric to row.value)
                    entryMetrics.add("${row.metric}-status" to row.status!!)
                    entryMetrics.add("${row.metric}-status-time" to TimeUtils.dateTimeFormatter.format(row.statusTime!!))
                }

                BbbUtils.BlockMetricWithTimeAndHeightWrapper(
                    height = height,
                    hash = hash,
                    parentHash = firstMetric.parentHash,
                    time = firstMetric.time,
                    // Map<metricName, metricValue>
                    metrics = entryMetrics,
                )
            }.let {
                if (additionalFilter != null) {
                    it.filter(additionalFilter)
                } else {
                    it
                }
            }
    }
}
