package io.coinmetrics.api.endpoints.timeseries.asset.datasources

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.endpoints.GetTimeseriesAssetMetricsRequest
import io.coinmetrics.api.endpoints.timeseries.asset.AssetMetricsEndpointImpl
import io.coinmetrics.api.models.PagingFrom
import io.coinmetrics.api.toSuccess
import io.coinmetrics.api.utils.DataUtils
import io.coinmetrics.api.utils.DataUtils.adjust
import io.coinmetrics.api.utils.QueryTextBuilder
import io.coinmetrics.api.utils.RangeQuery
import io.coinmetrics.api.utils.SqlUtils
import io.coinmetrics.api.utils.TimeUtils
import io.coinmetrics.api.utils.paging.PageToken
import io.coinmetrics.api.utils.streams.SuspendableStream
import io.coinmetrics.api.utils.toSqlOrdering
import io.coinmetrics.databases.Database
import java.text.DecimalFormat
import java.time.Instant

class ReferenceRatesDataSource(
    private val db: Database,
) {
    fun checkParameters(): FunctionResult<ApiError, AssetMetricsEndpointImpl.AssetMetricsDataSourceSpecificData> =
        AssetMetricsEndpointImpl.AssetMetricsDataSourceSpecificData.EMPTY.toSuccess()

    fun query(
        asset: String,
        patternRequested: Boolean,
        bufferSize: Int,
        pageToken: String?,
        frequency: String,
        frequencyOffset: TimeUtils.NormalizedFrequencyOffset,
        startTime: Instant,
        endTime: Instant,
        pagingFrom: PagingFrom,
        request: GetTimeseriesAssetMetricsRequest,
        metric: String,
        enforcedStartTime: Instant? = null,
    ): SuspendableStream<MetricDataUtils.DataWithTimeWrapper, PageToken.TimePageToken> {
        val timeFieldName = "rate_time"
        val valueFieldName = "rate_price"

        val suffix =
            when {
                metric.endsWith("EUR") -> "eur"
                metric.endsWith("BTC") -> "btc"
                metric.endsWith("ETH") -> "eth"
                else -> "usd"
            }

        val tableSuffix = "${asset}_$suffix"
        val initialState =
            pageToken?.let {
                try {
                    PageToken.TimePageToken.parse(it)
                } catch (_: Exception) {
                    error("Can't parse provided next_page_token '$it'.")
                }
            }

        val (startTimeParam, endTimeParam) =
            Pair(startTime, endTime)
                /**
                 * Returning an empty stream for simplicity.
                 * We could enhance this later, for example, by returning a function failure result if a pattern was not requested —
                 * but that would require more refactoring, which may not be necessary at this point.
                 */
                .adjust(enforcedStartTime) ?: return SuspendableStream.empty()

        val rangeQuery =
            RangeQuery
                .TimeRangeQuery(
                    startTimeParam,
                    request.startInclusive,
                    endTimeParam,
                    request.endInclusive,
                    pagingFrom,
                ).withPageToken(initialState)

        val timeFilter = DataUtils.createTimeFilter(rangeQuery)
        val additionalFilter = { it: MetricDataUtils.DataWithTimeWrapper ->
            timeFilter.invoke(it.time)
        }

        val decimalFormat = DecimalFormat("00")
        val timeZone = SqlUtils.escapeSql(frequencyOffset.forcedTimeZone ?: request.timezone)

        // timezone env is required only for queries which use INTERVAL keyword
        val (beforeQuery, afterQuery) =
            if (frequency == "1d") {
                "SET timezone='$timeZone'" to "SET timezone='UTC'"
            } else {
                null to null
            }

        val queryTextBuilder: QueryTextBuilder = { filter, limit ->
            val (joinQuery, timeFilteringQuery) =
                if (frequency == "1d") {
                    val startTime =
                        decimalFormat.format(frequencyOffset.hours) + ":" + decimalFormat.format(frequencyOffset.minutes) + ":00"
                    val interval = "1 day"

                    if (pagingFrom == PagingFrom.START) {
                        listOf(
                            """
                    INNER JOIN generate_series('1970-01-01 $startTime'::timestamp, NOW()::timestamp, '$interval'::interval) AS t(time)
                    ON r.$timeFieldName = t.time::timestamptz AT TIME ZONE 'UTC'
                    """,
                            "",
                        )
                    } else {
                        listOf(
                            """
                    INNER JOIN generate_series(CONCAT(NOW()::date, ' $startTime')::timestamp, '1970-01-01'::timestamp, -'$interval'::interval) AS t(time)
                    ON r.$timeFieldName = t.time::timestamptz AT TIME ZONE 'UTC'
                    """,
                            "",
                        )
                    }
                } else if (frequency == "1m") {
                    // old approach
                    val timeFilteringQuery =
                        SqlUtils.createTimeFilteringQuery(
                            "r.$timeFieldName",
                            frequency,
                            hourlyTable = false,
                            dayOffsetHour = frequencyOffset.hours,
                            dayOffsetMinute = frequencyOffset.minutes,
                            timezone = frequencyOffset.forcedTimeZone ?: request.timezone,
                        )
                    listOf("", timeFilteringQuery)
                } else {
                    // 1s
                    listOf("", "")
                }

            val tableName =
                if ((frequency == "1d" || frequency == "1h") && frequencyOffset.minutes == 0) {
                    "hourly_$tableSuffix"
                } else {
                    "realtime_$tableSuffix"
                }

            val order = pagingFrom.toSqlOrdering()

            """
            SELECT
                r.$timeFieldName,
                r.$valueFieldName::NUMERIC
            FROM ${db.config.schema}.$tableName r
            $joinQuery
            WHERE
                TRUE
                $timeFilteringQuery
                $filter
            ORDER BY r.$timeFieldName $order
            LIMIT $limit
            """
        }

        return DataUtils
            .createStream(
                db = db,
                queryTextBuilder = queryTextBuilder,
                bufferSize = bufferSize,
                keyNames = arrayOf("r.$timeFieldName"),
                dataMapper = MetricDataUtils.createMapper(metric, timeFieldName, valueFieldName),
                rangeQuery = rangeQuery,
                initialState = initialState,
                stateResolver = { PageToken.TimePageToken(it.time) },
                beforeQuery = beforeQuery,
                afterQuery = afterQuery,
                streamId = if (patternRequested) asset else null,
            ).filter(additionalFilter)
    }
}
