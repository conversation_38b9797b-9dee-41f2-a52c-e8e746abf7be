package io.coinmetrics.api.endpoints.timeseries.index

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.ChunkedResponseFormat
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.IndexConstituentsDbProperties
import io.coinmetrics.api.Response
import io.coinmetrics.api.endpoints.GetTimeseriesIndexConstituentsEndpoint
import io.coinmetrics.api.endpoints.GetTimeseriesIndexConstituentsRequest
import io.coinmetrics.api.models.IndexConstituents
import io.coinmetrics.api.models.IndexConstituentsConstituents
import io.coinmetrics.api.models.IndexConstituentsResponse
import io.coinmetrics.api.models.PagingFrom
import io.coinmetrics.api.resources.IndexInfo
import io.coinmetrics.api.resources.Resources
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.IndexDiscoveryService
import io.coinmetrics.api.service.getTimeseriesRateLimitHeaders
import io.coinmetrics.api.statistics.index.IndexConstituentsStatistics
import io.coinmetrics.api.toFailure
import io.coinmetrics.api.toSuccess
import io.coinmetrics.api.utils.BatchUtils
import io.coinmetrics.api.utils.CommonUtils
import io.coinmetrics.api.utils.DataUtils
import io.coinmetrics.api.utils.DataUtils.adjust
import io.coinmetrics.api.utils.QueryTextBuilder
import io.coinmetrics.api.utils.RangeQuery
import io.coinmetrics.api.utils.SqlUtils
import io.coinmetrics.api.utils.TimeUtils
import io.coinmetrics.api.utils.WildcardUtils
import io.coinmetrics.api.utils.paging.PageToken
import io.coinmetrics.api.utils.paging.getPageFlow
import io.coinmetrics.api.utils.streams.SuspendableStream
import io.coinmetrics.databases.Database
import kotlinx.coroutines.flow.asFlow
import kotlinx.coroutines.flow.flatMapConcat
import kotlinx.coroutines.flow.flowOf
import java.sql.ResultSet
import java.sql.Timestamp
import java.time.Instant
import java.time.ZoneOffset
import java.time.ZonedDateTime

/**
 * We fill the gaps for fixed constituents and don't fill the gaps for dynamic ones (those have hourly constituents data).
 * That way, we preserve our data consistency, we expect that static constituents will not be changed, so we can fill the gaps.
 * In dynamic constituents, we expect changes, so we will not fill the gaps returning data as it is.
 */
class GetTimeseriesIndexConstituentsEndpointImpl(
    private val db: Database,
    private val amsService: AmsService,
    private val indexConstituentsStatistics: IndexConstituentsStatistics,
    private val indexDiscoveryService: IndexDiscoveryService,
) : GetTimeseriesIndexConstituentsEndpoint() {
    override suspend fun handle(request: GetTimeseriesIndexConstituentsRequest): Response<IndexConstituentsResponse> {
        val headers =
            amsService
                .getTimeseriesRateLimitHeaders(
                    apiKey = request.apiKey,
                    httpRequest = request.httpRequest,
                ).getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }

        val parsedFrequency =
            request.frequency?.let {
                val (frequency, offset) = TimeUtils.parseFrequency(it)
                val normalizedOffset =
                    TimeUtils.normalizeFrequencyOffset(offset)
                        ?: return Response.errorResponse(
                            ApiError.BadParameter("frequency", "Frequency '${request.frequency}' is not supported."),
                        )
                frequency to normalizedOffset
            }

        val (parseInfo, patternRequested) =
            WildcardUtils
                .parseRequestCompositeParameter(
                    paramName = "indexes",
                    paramValues = request.indexes.map { it.uppercase() }.toHashSet(),
                    universeOfItems = indexConstituentsStatistics.getIndexes(),
                ).getOrElse { return Response.errorResponse(it) }

        val supportedIndexesMetadata =
            parseInfo
                .flatMap { (indexes, unwrappedFromPattern) ->
                    validate(
                        indexes = indexes,
                        patternRequested = unwrappedFromPattern,
                        request = request,
                    ).getOrElse { return Response.errorResponse(it) }.entries
                }.associate { entry -> entry.key to entry.value }

        return when (
            val result =
                BatchUtils.sortIdsAndConcatStreams(
                    streams = supportedIndexesMetadata.asSequence().map { it.toPair() },
                    pagingFrom = request.pagingFrom,
                    nextPageToken = request.nextPageToken,
                    initialStreamStateParser = { PageToken.TimePageToken.parse(it) },
                    streamSupplier = { index, state, indexMetadata ->
                        handleInternal(
                            index = index,
                            patternRequested = patternRequested,
                            indexInfo = indexMetadata.indexInfo,
                            initialState = state,
                            parsedFrequency = parsedFrequency,
                            rangeQuery = indexMetadata.rangeQuery,
                            request = request,
                        )
                    },
                    httpRequestCoroutineContext = request.httpRequest.coroutineContext,
                    streamIdsAreResolvedDynamically = patternRequested,
                    logger = log,
                )
        ) {
            is FunctionResult.Success -> {
                val page =
                    result.value.getPageFlow(request.httpRequest, request.pageSize, request.pagingFrom).let {
                        if (request.format == "csv") {
                            it.flatMapConcat { indexConstituents ->
                                if (indexConstituents is IndexConstituents) {
                                    indexConstituents.constituents
                                        .map { constituent ->
                                            mapOf(
                                                "index" to indexConstituents.index,
                                                "time" to indexConstituents.time,
                                                "asset" to constituent.asset,
                                                "weight" to constituent.weight,
                                            )
                                        }.asFlow()
                                } else {
                                    flowOf(indexConstituents)
                                }
                            }
                        } else {
                            it
                        }
                    }

                Response.chunkedResponse(
                    items = page,
                    headers = headers,
                    format = if (request.format == "csv") ChunkedResponseFormat.Csv(nullValue = "") else ChunkedResponseFormat.Json(),
                )
            }

            is FunctionResult.Failure -> Response.errorResponse(result.value, headers)
        }
    }

    private fun handleInternal(
        index: String,
        patternRequested: Boolean,
        indexInfo: IndexInfo,
        initialState: PageToken.TimePageToken?,
        parsedFrequency: Pair<String, TimeUtils.NormalizedFrequencyOffset>?,
        rangeQuery: RangeQuery.TimeRangeQuery,
        request: GetTimeseriesIndexConstituentsRequest,
    ): SuspendableStream<IndexConstituents, PageToken.TimePageToken> {
        val indexConstituentsDbProperties = getIndexConstituentsDbProperties(index)
        val (
            tableName,
            indexIdColumnName,
            indexTimeColumnName,
            currencyTickerColumnName,
            weightColumnName,
        ) = indexConstituentsDbProperties

        val indexTimeColumnNameWithTableAlias =
            if (parsedFrequency == null || indexInfo.hasHourlyConstituents) {
                indexTimeColumnName
            } else {
                "g.$indexTimeColumnName"
            }

        val timeFilteringQuery =
            parsedFrequency?.let { (frequency, normalizedFrequencyOffset) ->
                SqlUtils.createTimeFilteringQuery(
                    timeFieldName = indexTimeColumnNameWithTableAlias,
                    frequency = frequency,
                    hourlyTable = true,
                    dayOffsetHour = normalizedFrequencyOffset.hours,
                    dayOffsetMinute = normalizedFrequencyOffset.minutes,
                    timezone = normalizedFrequencyOffset.forcedTimeZone ?: request.timezone,
                )
            } ?: ""

        val timeFilter = DataUtils.createTimeFilter(rangeQuery)
        val additionalFilter = { it: IndexConstituentRow ->
            timeFilter.invoke(it.time)
        }

        val fullTableName = "${db.config.schema}.$tableName"

        val ordering = if (request.pagingFrom == PagingFrom.START) "ASC" else "DESC"
        val queryTextBuilder: QueryTextBuilder =
            if (parsedFrequency == null || indexInfo.hasHourlyConstituents) {
                { filter, limit ->
                    """
            SELECT 
                $indexTimeColumnName, $currencyTickerColumnName, $weightColumnName 
            FROM $fullTableName 
            WHERE 
                $indexIdColumnName=${indexInfo.id} AND 
                $indexTimeColumnName in (
                    SELECT DISTINCT($indexTimeColumnName) 
                    FROM $fullTableName 
                    WHERE 
                        $indexIdColumnName=${indexInfo.id}
                        $timeFilteringQuery
                        $filter
                    ORDER BY 1 $ordering 
                    LIMIT $limit) 
            ORDER BY 1 $ordering, 2
            """
                }
            } else {
                { filter, limit ->
                    // emulate hourly data
                    val generateSeriesSubquery = buildGenerateSeriesSubquery(rangeQuery)
                    /**
                     * Example query:
                     WITH all_series AS (SELECT g.index_time
                     FROM generate_series((date_trunc('hour', '2022-05-06 10:00:00.0'::timestamp))::timestamp, '2009-01-01 00:00:00.0'::timestamp, -'1 hour'::interval) AS g(index_time)
                     WHERE TRUE
                     AND g.index_time >= '1970-01-01 00:00:00.0'::timestamp AND g.index_time <= '2022-05-06 10:10:19.387118'::timestamp
                     ORDER BY g.index_time DESC),
                     applicable_series AS (SELECT s.index_time
                     FROM all_series s
                     WHERE EXISTS(
                     SELECT currency_ticker
                     FROM staging.fidelity_index_constituents c
                     WHERE index_id = 1003
                     AND c.index_time <= s.index_time
                     )
                     LIMIT 101)
                     SELECT g.index_time,
                     coalesce(t.currency_ticker, (SELECT currency_ticker
                     FROM staging.fidelity_index_constituents
                     WHERE index_id = 1003
                     AND index_time < g.index_time
                     ORDER BY index_time DESC
                     LIMIT 1)) AS currency_ticker,
                     coalesce(t.weight, (SELECT weight
                     FROM staging.fidelity_index_constituents
                     WHERE index_id = 1003
                     AND index_time < g.index_time
                     ORDER BY index_time DESC
                     LIMIT 1))          AS weight
                     FROM applicable_series g
                     LEFT OUTER JOIN staging.fidelity_index_constituents AS t ON t.index_id = 1003 AND t.index_time = g.index_time
                     ORDER BY 1 DESC, 2;
                     */
                    """
                    WITH all_series AS (SELECT g.$indexTimeColumnName
                                        FROM $generateSeriesSubquery AS g($indexTimeColumnName)
                                        WHERE TRUE 
                                            $filter 
                                            $timeFilteringQuery
                                        ORDER BY g.$indexTimeColumnName $ordering),
                         applicable_series AS (SELECT s.$indexTimeColumnName
                                               FROM all_series s
                                               WHERE EXISTS(
                                                             SELECT $currencyTickerColumnName
                                                             FROM $fullTableName c
                                                             WHERE $indexIdColumnName = ${indexInfo.id}
                                                               AND c.$indexTimeColumnName <= s.$indexTimeColumnName
                                                         )
                                               LIMIT $limit)
                    SELECT g.$indexTimeColumnName,
                           coalesce(t.$currencyTickerColumnName, (SELECT $currencyTickerColumnName
                                                        FROM $fullTableName
                                                        WHERE $indexIdColumnName = ${indexInfo.id}
                                                          AND $indexTimeColumnName < g.$indexTimeColumnName
                                                        ORDER BY $indexTimeColumnName DESC
                                                        LIMIT 1)) AS $currencyTickerColumnName,
                           coalesce(t.$weightColumnName, (SELECT $weightColumnName
                                               FROM $fullTableName
                                               WHERE $indexIdColumnName = ${indexInfo.id}
                                                 AND $indexTimeColumnName < g.$indexTimeColumnName
                                               ORDER BY $indexTimeColumnName DESC
                                               LIMIT 1))          AS $weightColumnName
                    FROM applicable_series g
                             LEFT OUTER JOIN $fullTableName AS t ON t.$indexIdColumnName = ${indexInfo.id} AND t.$indexTimeColumnName = g.$indexTimeColumnName
                    ORDER BY 1 $ordering, 2;
                    """.trimIndent()
                }
            }

        return DataUtils
            .createStream(
                db = db,
                queryTextBuilder = queryTextBuilder,
                bufferSize = request.pageSize + 1,
                keyNames = arrayOf(indexTimeColumnNameWithTableAlias),
                dataMapper = mapper(indexConstituentsDbProperties),
                rangeQuery = rangeQuery,
                initialState = initialState,
                stateResolver = { PageToken.TimePageToken(it.time) },
                streamId = if (patternRequested) index else null,
            ).filter(additionalFilter)
            .collapseByKey { it.time }
            .map { (time, rows) ->
                IndexConstituents(
                    time = TimeUtils.dateTimeFormatter.format(time),
                    index = index,
                    constituents = rows.map { it.constituent }.sortedBy { it.asset },
                )
            }
    }

    private suspend fun validate(
        indexes: List<String>,
        patternRequested: Boolean,
        request: GetTimeseriesIndexConstituentsRequest,
    ): FunctionResult<ApiError, Map<String, IndexMetadata>> {
        val discoveryResultPerIndex =
            indexDiscoveryService
                .discoverIndexes(
                    apiKey = request.apiKey,
                    layer = "constituents",
                    requestedIndexes = indexes,
                    ignoreUnsupportedAndForbidden = patternRequested,
                    ignoreDiscoveryScript = true,
                ).getOrElse {
                    return it.toFailure()
                }.associate { (index, _, timeRestrictions) -> index to timeRestrictions }

        val forbiddenIndexes: Set<String> =
            if (discoveryResultPerIndex.size < indexes.size) {
                indexes.minus(discoveryResultPerIndex.keys).toSet()
            } else {
                emptySet()
            }

        if (forbiddenIndexes.isNotEmpty() && !patternRequested) {
            val indexesStr = forbiddenIndexes.take(3).joinToString(", ")
            return FunctionResult.Failure(
                ApiError.ForbiddenWithMessage(
                    "Requested indexes are not available with supplied credentials: $indexesStr.",
                ),
            )
        }

        return indexes
            .minus(forbiddenIndexes)
            .mapNotNull { index ->
                val timeRestrictions = discoveryResultPerIndex.getValue(index) ?: return@mapNotNull null
                val indexMetadata =
                    toIndexMetadata(index, timeRestrictions, request)
                        .getOrElse { return it.toFailure() }
                index to indexMetadata
            }.associate { it }
            .toSuccess()
    }

    private fun toIndexMetadata(
        index: String,
        timeRestrictions: List<String>,
        request: GetTimeseriesIndexConstituentsRequest,
    ): FunctionResult<ApiError, IndexMetadata> {
        val enforcedTime =
            indexConstituentsStatistics.getStatistics(index)?.let { statistics ->
                TimeUtils
                    .getEnforcedStartTime(
                        timeRestrictions,
                        request.timezone,
                        statistics.maxTime.toString(),
                        request.httpRequest.receivedTime,
                    ).getOrElse { return it.toFailure() }
            }

        return DataUtils
            .parseTimeParameters(
                request.startTime,
                request.startInclusive,
                request.endTime,
                request.endInclusive,
                request.timezone,
            ).map {
                it.adjust(enforcedTime) ?: return ApiError
                    .ForbiddenWithMessage("Requested time range is not available with supplied credentials.")
                    .toFailure()
            }.map { (startTime, endTime) ->
                RangeQuery.TimeRangeQuery(
                    startTime,
                    request.startInclusive,
                    endTime,
                    request.endInclusive,
                    request.pagingFrom,
                )
            }.map { rangeQuery ->
                val indexInfo =
                    Resources.getIndexInfo(index).getOrElse {
                        return ApiError.UnsupportedParameterValue("indexes", index).toFailure()
                    }
                IndexMetadata(indexInfo, rangeQuery)
            }
    }

    private fun buildGenerateSeriesSubquery(timeRange: RangeQuery.TimeRangeQuery): String {
        /*
         * from - either 2009-01-01 or specified by user (but not before 2009-01-01)
         * to - either current time or specified by user (but not after current time)
         */
        val fromInstant =
            maxOf(
                ZonedDateTime.of(2009, 1, 1, 0, 0, 0, 0, ZoneOffset.UTC).toInstant(),
                timeRange.startKey.atStartOfAnHour(),
            )
        val toInstant = minOf(timeRange.endKey.atStartOfAnHour(), Instant.now())
        val interval = "1 hour"

        val (from, to, intervalSign) =
            if (timeRange.pagingFrom == PagingFrom.START) {
                Triple(
                    // we may not truncate to hour, because 'from' is always at start of the day, and 'to' doesn't matter
                    "'${Timestamp.from(fromInstant)}'",
                    "'${Timestamp.from(toInstant)}'",
                    "",
                )
            } else {
                Triple(
                    // we can truncate to hour because interval is 1 hour anyway
                    "(date_trunc('hour', '${Timestamp.from(toInstant)}'::timestamp))",
                    "'${Timestamp.from(fromInstant)}'",
                    "-",
                )
            }
        /*
         * Examples:
         *   - generate_series('2009-01-01 00:00:00.0'::timestamp, '2021-11-02 13:00:00.0'::timestamp, '1 hour'::interval)
         *   - generate_series((date_trunc('hour', '2021-10-08 11:00:00.0'::timestamp))::timestamp, (date_trunc('hour', '2021-10-02 11:00:00.0'::timestamp))::timestamp, -'1 hour'::interval)
         */
        return """generate_series($from::timestamp, $to::timestamp, $intervalSign'$interval'::interval)"""
    }

    private fun mapper(dbProperties: IndexConstituentsDbProperties) =
        { rs: ResultSet ->
            val time = rs.getTimestamp(dbProperties.indexTimeColumnName).toInstant()
            val constituentTicker = rs.getString(dbProperties.currencyTickerColumnName)
            val constituentWeight = rs.getBigDecimal(dbProperties.weightColumnName)

            IndexConstituentRow(
                time,
                IndexConstituentsConstituents(constituentTicker, CommonUtils.formatBigDecimal(constituentWeight)),
            )
        }

    private fun getIndexConstituentsDbProperties(indexName: String): IndexConstituentsDbProperties =
        if (indexName in Resources.fidelityMultiAssetIndexNames) {
            IndexConstituentsDbProperties.fidelity
        } else {
            IndexConstituentsDbProperties.cmbi
        }

    private fun Instant.atStartOfAnHour(): Instant =
        ZonedDateTime
            .ofInstant(this, ZoneOffset.UTC)
            .withMinute(0)
            .withSecond(0)
            .withNano(0)
            .toInstant()

    private class IndexConstituentRow(
        val time: Instant,
        val constituent: IndexConstituentsConstituents,
    )
}
