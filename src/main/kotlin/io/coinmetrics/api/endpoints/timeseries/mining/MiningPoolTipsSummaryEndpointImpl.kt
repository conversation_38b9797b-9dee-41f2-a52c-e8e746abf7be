package io.coinmetrics.api.endpoints.timeseries.mining

import io.coinmetrics.api.ChunkedResponseFormat
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.Response
import io.coinmetrics.api.endpoints.GetTimeseriesMiningPoolTipsSummaryEndpoint
import io.coinmetrics.api.endpoints.GetTimeseriesMiningPoolTipsSummaryRequest
import io.coinmetrics.api.models.MiningPoolTipsSummary
import io.coinmetrics.api.models.MiningPoolTipsSummaryResponse
import io.coinmetrics.api.models.MiningPoolTipsSummaryTips
import io.coinmetrics.api.models.PagingFrom
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.getTimeseriesRateLimitHeaders
import io.coinmetrics.api.utils.BatchUtils
import io.coinmetrics.api.utils.DataUtils
import io.coinmetrics.api.utils.QueryTextBuilder
import io.coinmetrics.api.utils.RangeQuery
import io.coinmetrics.api.utils.TimeUtils
import io.coinmetrics.api.utils.paging.PageToken
import io.coinmetrics.api.utils.paging.getPageFlow
import io.coinmetrics.api.utils.streams.SuspendableStream
import io.coinmetrics.api.utils.toHex
import io.coinmetrics.databases.Database
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.flow.asFlow
import kotlinx.coroutines.flow.flatMapConcat
import kotlinx.coroutines.flow.flowOf
import java.sql.ResultSet
import java.sql.Timestamp
import java.time.Instant

class MiningPoolTipsSummaryEndpointImpl(
    private val db: Database,
    private val amsService: AmsService,
) : GetTimeseriesMiningPoolTipsSummaryEndpoint() {
    private val amsParamsToEndpointParams =
        hashMapOf(
            "asset" to "assets",
        )

    private val mapper = { rs: ResultSet ->
        val tipLastTime =
            @Suppress(
                "UNCHECKED_CAST",
            )
            (rs.getArray("tip_last_time").array as Array<Timestamp>)
        val tipHeight =
            @Suppress(
                "UNCHECKED_CAST",
            )
            (rs.getArray("tip_height").array as Array<Int>)
        val tipHash =
            @Suppress(
                "UNCHECKED_CAST",
            )
            (rs.getArray("tip_hash").array as Array<ByteArray>)
        val tipPoolCount =
            @Suppress(
                "UNCHECKED_CAST",
            )
            (rs.getArray("tip_pool_count").array as Array<Int>)
        assert(tipLastTime.size == tipHeight.size)
        assert(tipHeight.size == tipHash.size)
        assert(tipHash.size == tipPoolCount.size)
        MiningPoolTipsSummaryRow(
            time = rs.getTimestamp("time").toInstant(),
            tipsCount = rs.getInt("tips_count"),
            blockHashesAtTip = rs.getInt("block_hashes_at_tip"),
            tips =
                tipLastTime.mapIndexed { index, lastTime ->
                    MiningPoolTipsSummaryRow.Tip(
                        lastTime = lastTime.toInstant(),
                        height = tipHeight[index],
                        hash = tipHash[index],
                        poolCount = tipPoolCount[index],
                    )
                },
        )
    }

    override suspend fun handle(request: GetTimeseriesMiningPoolTipsSummaryRequest): Response<MiningPoolTipsSummaryResponse> {
        val headers =
            amsService
                .getTimeseriesRateLimitHeaders(
                    apiKey = request.apiKey,
                    httpRequest = request.httpRequest,
                ).getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }

        val amsCheckResults =
            coroutineScope {
                request.assets
                    .distinct()
                    .map { asset ->
                        async {
                            amsService
                                .check(
                                    apiKey = request.apiKey,
                                    resource = "mining_pool_tips",
                                    parameters = hashMapOf("asset" to asset),
                                ) { amsParamsToEndpointParams[it] }
                        }
                    }.awaitAll()
            }

        for (checkResult in amsCheckResults) {
            checkResult.getOrElse { (e) -> return Response.errorResponse(e) }
        }

        val (startTime, endTime) =
            DataUtils
                .parseTimeParameters(
                    request.startTime,
                    request.startInclusive,
                    request.endTime,
                    request.endInclusive,
                    request.timezone,
                ).getOrElse { return Response.errorResponse(it) }

        return when (
            val result =
                BatchUtils.sortIdsAndConcatStreams(
                    streams = request.assets.asSequence().map { it to Unit },
                    streamIdsAreResolvedDynamically = false,
                    pagingFrom = request.pagingFrom,
                    nextPageToken = request.nextPageToken,
                    initialStreamStateParser = { PageToken.TimePageToken.parse(it) },
                    streamSupplier = { id, state, _ ->
                        val rangeQuery =
                            RangeQuery
                                .TimeRangeQuery(
                                    startTime,
                                    request.startInclusive,
                                    endTime,
                                    request.endInclusive,
                                    request.pagingFrom,
                                ).withPageToken(state)

                        handleInternal(id, state, request, rangeQuery)
                    },
                    httpRequestCoroutineContext = request.httpRequest.coroutineContext,
                    logger = log,
                )
        ) {
            is FunctionResult.Success -> {
                val page =
                    result.value.getPageFlow(request.httpRequest, request.pageSize, request.pagingFrom).let {
                        if (request.format == "csv") {
                            it.flatMapConcat { obj ->
                                if (obj is MiningPoolTipsSummary) {
                                    obj.tips
                                        .map { tip ->
                                            obj.toMap(tip)
                                        }.asFlow()
                                } else {
                                    flowOf(obj)
                                }
                            }
                        } else {
                            it
                        }
                    }
                Response.chunkedResponse(
                    items = page,
                    headers = headers,
                    format = if (request.format == "csv") ChunkedResponseFormat.Csv(nullValue = "") else ChunkedResponseFormat.Json(),
                )
            }

            is FunctionResult.Failure -> Response.errorResponse(result.value, headers)
        }
    }

    private fun MiningPoolTipsSummary.toMap(tip: MiningPoolTipsSummaryTips) =
        mapOf(
            "asset" to asset,
            "time" to time,
            "tips_count" to tipsCount,
            "block_hashes_at_tip" to blockHashesAtTip,
            "last_time" to tip.lastTime,
            "height" to tip.height,
            "hash" to tip.hash,
            "pool_count" to tip.poolCount,
        )

    private fun handleInternal(
        asset: String,
        initialState: PageToken.TimePageToken?,
        request: GetTimeseriesMiningPoolTipsSummaryRequest,
        rangeQuery: RangeQuery.TimeRangeQuery,
    ): SuspendableStream<MiningPoolTipsSummary, PageToken.TimePageToken> {
        val tableName = "${db.config.schema}.${asset}_mining_pool_tips_summary"

        val queryTextBuilder: QueryTextBuilder = { filter, limit ->
            val ordering = if (request.pagingFrom == PagingFrom.START) "ASC" else "DESC"
            """
            SELECT 
                time, tips_count, block_hashes_at_tip, tip_last_time,
                tip_height, tip_hash, tip_pool_count
            FROM $tableName 
            WHERE 
                TRUE
                $filter
            ORDER BY time $ordering 
            LIMIT $limit
            """
        }

        val timeFilter = DataUtils.createTimeFilter(rangeQuery)
        val additionalFilter = { it: MiningPoolTipsSummaryRow ->
            timeFilter.invoke(it.time)
        }

        val stream =
            DataUtils
                .createStream(
                    db = db,
                    queryTextBuilder = queryTextBuilder,
                    bufferSize = request.pageSize + 1,
                    keyNames = arrayOf("time"),
                    dataMapper = mapper,
                    rangeQuery = rangeQuery,
                    initialState = initialState,
                    stateResolver = { PageToken.TimePageToken(it.time) },
                    streamId = null,
                ).filter(additionalFilter)
                .map { row ->
                    MiningPoolTipsSummary(
                        asset = asset,
                        time = TimeUtils.dateTimeFormatter.format(row.time),
                        tipsCount = row.tipsCount.toString(),
                        blockHashesAtTip = row.blockHashesAtTip.toString(),
                        tips =
                            row.tips.map { tip ->
                                MiningPoolTipsSummaryTips(
                                    lastTime = TimeUtils.dateTimeFormatter.format(tip.lastTime),
                                    height = tip.height.toString(),
                                    hash = tip.hash.toHex(),
                                    poolCount = tip.poolCount.toString(),
                                )
                            },
                    )
                }

        return stream
    }

    private class MiningPoolTipsSummaryRow(
        val time: Instant,
        val tipsCount: Int,
        val blockHashesAtTip: Int,
        val tips: List<Tip>,
    ) {
        class Tip(
            val lastTime: Instant,
            val height: Int,
            val hash: ByteArray,
            val poolCount: Int,
        )
    }
}
