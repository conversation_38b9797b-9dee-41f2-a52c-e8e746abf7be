package io.coinmetrics.api.endpoints.timeseries.asset.datasources

import io.coinmetrics.api.utils.CommonUtils
import io.coinmetrics.api.utils.DataUtils
import io.coinmetrics.api.utils.WithTime
import java.sql.ResultSet
import java.time.Clock
import java.time.Instant
import java.time.temporal.ChronoUnit

object MetricDataUtils {
    fun enforcedStartTime(
        clock: Clock,
        apiKey: String,
        communityApiKey: String,
        delayDays: Long,
    ): Instant? = DataUtils.communityEnforcedStart(apiKey, communityApiKey, delayDays, clock)?.minus(1, ChronoUnit.HOURS)

    fun createMapper(
        metric: String,
        timeFieldName: String,
        valueFieldName: String,
    ): (ResultSet) -> DataWithTimeWrapper =
        { rs: ResultSet ->
            val time = rs.getTimestamp(timeFieldName).toInstant()
            DataWithTimeWrapper(
                metric,
                time,
                CommonUtils.formatBigDecimal(rs.getBigDecimal(valueFieldName)),
            )
        }

    class DataWithTimeWrapper(
        val metric: String,
        override val time: Instant,
        val metricValue: String,
    ) : WithTime

    class MetricsHolderWithTime(
        override val time: Instant,
        val metrics: Map<String, String?>,
    ) : WithTime
}
