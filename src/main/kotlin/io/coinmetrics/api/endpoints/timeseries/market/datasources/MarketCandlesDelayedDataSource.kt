package io.coinmetrics.api.endpoints.timeseries.market.datasources

import io.coinmetrics.api.endpoints.timeseries.market.datasources.MarketCandlesUtil.TIME_COLUMN_NAME
import io.coinmetrics.api.endpoints.timeseries.market.model.MarketCandlesWrapper
import io.coinmetrics.api.model.DerivativesMarketType
import io.coinmetrics.api.model.NormalizedMarket
import io.coinmetrics.api.resources.Resources
import io.coinmetrics.api.utils.DataUtils
import io.coinmetrics.api.utils.QueryTextBuilder
import io.coinmetrics.api.utils.RangeQuery
import io.coinmetrics.api.utils.SqlUtils
import io.coinmetrics.api.utils.TimeUtils
import io.coinmetrics.api.utils.paging.PageToken
import io.coinmetrics.api.utils.streams.SuspendableStream
import io.coinmetrics.api.utils.toSqlOrdering
import io.coinmetrics.databases.Database

class MarketCandlesDelayedDataSource(
    private val db: Database,
) {
    private val schema = db.config.schema

    fun query(
        market: String,
        marketId: NormalizedMarket,
        marketPatternRequested: Boolean,
        frequency: String,
        rangeQuery: RangeQuery.TimeRangeQuery,
        pageToken: PageToken.TimePageToken?,
        bufferSize: Int,
    ): SuspendableStream<MarketCandlesWrapper, PageToken.TimePageToken> {
        val ordering = rangeQuery.pagingFrom.toSqlOrdering()
        val (tableName, instrumentsFilter) = queryDetails(marketId, frequency)
        val queryTextBuilder =
            queryBuilder(
                tableName = tableName,
                instrumentsFilter = instrumentsFilter,
                ordering = ordering,
                nonDelayedCandleQueryBuilder =
                    getUnionNonDelayedCandlesQueryBuilder(
                        tableName = tableName,
                        instrumentsFilter = instrumentsFilter,
                        marketId = marketId,
                        frequency = frequency,
                        ordering = ordering,
                    ),
            )

        val timeFilter = DataUtils.createTimeFilter(rangeQuery)
        val additionalFilter = { it: MarketCandlesWrapper -> timeFilter.invoke(it.time) }

        return DataUtils
            .createStream(
                db = db,
                queryTextBuilder = queryTextBuilder,
                bufferSize = bufferSize,
                keyNames = arrayOf(TIME_COLUMN_NAME),
                dataMapper = MarketCandlesUtil.createMapper(market),
                rangeQuery = rangeQuery,
                initialState = pageToken,
                stateResolver = { PageToken.TimePageToken(it.time) },
                streamId = if (marketPatternRequested) market else null,
                lbKey = market,
            ).filter(additionalFilter)
    }

    /**
     * In the future, 1d candle aggregation should be performed at the API level rather than in SQL to ensure proper functioning of cold-hot tiering.
     */
    fun query1dCustomOffset(
        market: String,
        marketId: NormalizedMarket,
        marketPatternRequested: Boolean,
        frequency: String,
        frequencyOffset: TimeUtils.NormalizedFrequencyOffset,
        frequencyOffsetTimezone: String,
        rangeQuery: RangeQuery.TimeRangeQuery,
        pageToken: PageToken.TimePageToken?,
        bufferSize: Int,
    ): SuspendableStream<MarketCandlesWrapper, PageToken.TimePageToken> {
        assert(!frequencyOffset.default)
        val ordering = rangeQuery.pagingFrom.toSqlOrdering()
        val (tableName, instrumentsFilter) = queryDetails(marketId, frequency)
        val queryTextBuilder: QueryTextBuilder =
            customFrequencyOffset1dQueryBuilder(
                tableName = tableName,
                instrumentsFilter = instrumentsFilter,
                ordering = ordering,
                offsetHours = frequencyOffset.hours,
                offsetTimezone = frequencyOffsetTimezone,
                nonDelayedCandleQueryBuilder =
                    getUnionNonDelayedCandlesQueryBuilder(
                        tableName = tableName,
                        instrumentsFilter = instrumentsFilter,
                        marketId = marketId,
                        frequency = frequency,
                        ordering = ordering,
                        /**
                         * For each day we need 24 1-hours candles + 24 to cover last candle in case if start of time interval != custom offset.
                         * However, we won't store more than 1 day in non-delayed candle tables so we can use a fixed value
                         */
                        limitOverride = 24,
                    ),
            )

        val timeFilter = DataUtils.createTimeFilter(rangeQuery)
        val additionalFilter = { it: MarketCandlesWrapper -> timeFilter.invoke(it.time) }

        return DataUtils
            .createStream(
                db = db,
                queryTextBuilder = queryTextBuilder,
                bufferSize = bufferSize,
                keyNames = arrayOf(TIME_COLUMN_NAME),
                dataMapper = MarketCandlesUtil.createMapper(market),
                rangeQuery = rangeQuery,
                initialState = pageToken,
                stateResolver = { PageToken.TimePageToken(it.time) },
                streamId = if (marketPatternRequested) market else null,
                lbKey = market,
            ).filter(additionalFilter)
    }

    private fun queryBuilder(
        tableName: String,
        instrumentsFilter: String,
        ordering: String,
        nonDelayedCandleQueryBuilder: (String, Int) -> String,
    ): QueryTextBuilder =
        { filter, limit ->
            val nonDelayedCandleQuery = nonDelayedCandleQueryBuilder(filter, limit)
            val unionStartWithNonDelayedCandleQuery =
                if (nonDelayedCandleQuery.isNotEmpty()) {
                    """
                    (
                    $nonDelayedCandleQuery
                    )
                    UNION ALL
                    (
                    """
                } else {
                    ""
                }
            val unionEnd =
                if (nonDelayedCandleQuery.isNotEmpty()) {
                    """
                    )
                    ORDER BY $TIME_COLUMN_NAME $ordering
                    LIMIT $limit
                    """
                } else {
                    ""
                }

            """
            $unionStartWithNonDelayedCandleQuery
            SELECT
                $TIME_COLUMN_NAME,
                candle_open_price::NUMERIC,
                candle_close_price::NUMERIC,
                candle_low_price::NUMERIC,
                candle_high_price::NUMERIC,
                candle_vwap::NUMERIC,
                candle_volume::NUMERIC,
                candle_usd_volume::NUMERIC,
                candle_trades_count::NUMERIC
            FROM $schema.$tableName
            WHERE 
                $instrumentsFilter
                $filter
            ORDER BY $TIME_COLUMN_NAME $ordering
            LIMIT $limit
            $unionEnd
            """.trimIndent()
        }

    private fun customFrequencyOffset1dQueryBuilder(
        tableName: String,
        instrumentsFilter: String,
        ordering: String,
        offsetHours: Int,
        offsetTimezone: String,
        nonDelayedCandleQueryBuilder: (String, Int) -> String,
    ): QueryTextBuilder =

        { filter, limit ->
            // 0 is overridden later
            val nonDelayedCandleQuery = nonDelayedCandleQueryBuilder(filter, 0)
            val unionWithNonDelayedCandleQuery =
                if (nonDelayedCandleQuery.isNotEmpty()) {
                    """
                    (
                    $nonDelayedCandleQuery
                    )
                    UNION ALL                         
                    """
                } else {
                    ""
                }
            """
            SELECT
                DATE_TRUNC('day', $TIME_COLUMN_NAME AT TIME ZONE 'utc' AT TIME ZONE '$offsetTimezone' - '$offsetHours hours'::INTERVAL),
                MIN($TIME_COLUMN_NAME)                                                                  AS $TIME_COLUMN_NAME,
                (MIN(ARRAY [EXTRACT(EPOCH FROM $TIME_COLUMN_NAME), candle_open_price]))[2]::NUMERIC     AS candle_open_price,
                (MAX(ARRAY [EXTRACT(EPOCH FROM $TIME_COLUMN_NAME), candle_close_price]))[2]::NUMERIC    AS candle_close_price,
                MIN(candle_low_price)::NUMERIC                                                          AS candle_low_price,
                MAX(candle_high_price)::NUMERIC                                                         AS candle_high_price,
                SUM(candle_volume)::NUMERIC                                                             AS candle_volume,
                (CASE
                    WHEN SUM(candle_volume)=0 THEN
                        (MIN(ARRAY [EXTRACT(EPOCH FROM $TIME_COLUMN_NAME), candle_vwap]))[2]::NUMERIC
                    ELSE
                        (SUM(candle_vwap*candle_volume)/SUM(candle_volume))::NUMERIC
                END)                                                                           AS candle_vwap,
                SUM(candle_usd_volume)::NUMERIC                                                         AS candle_usd_volume,
                SUM(candle_trades_count)::NUMERIC                                                       AS candle_trades_count
            FROM
                -- subquery is used to improve performance by limiting amount of 1h candles needed to be sorted/grouped when client specifies broad or open interval.
                (
                    $unionWithNonDelayedCandleQuery
                    (SELECT $TIME_COLUMN_NAME::timestamp,
                        candle_open_price::NUMERIC,
                        candle_close_price::NUMERIC,
                        candle_low_price::NUMERIC,
                        candle_high_price::NUMERIC,
                        candle_vwap::NUMERIC,
                        candle_volume::NUMERIC,
                        candle_usd_volume::NUMERIC,
                        candle_trades_count::NUMERIC
                    FROM $schema.$tableName
                    WHERE
                        $instrumentsFilter
                        $filter
                    ORDER BY $TIME_COLUMN_NAME $ordering
                    -- for each day we need 24 1-hours candles + 24 to cover last candle in case if start of time interval != custom offset
                    LIMIT $limit * 24 + 24)
                ) candles
            GROUP BY 1
            -- make sure we return only candles that were aggregated across the whole day and ignore "partial" candles that may be aggregated on the time interval ends.
            HAVING DATE_PART('hours', MIN($TIME_COLUMN_NAME) AT TIME ZONE 'utc' AT TIME ZONE '$offsetTimezone') = $offsetHours
               AND DATE_PART('hours', MAX($TIME_COLUMN_NAME) AT TIME ZONE 'utc' AT TIME ZONE '$offsetTimezone') = ${(if (offsetHours == 0) 24 else offsetHours) - 1}
            ORDER BY 1 $ordering
            LIMIT $limit;
            """.trimIndent()
        }

    private fun getUnionNonDelayedCandlesQueryBuilder(
        tableName: String,
        instrumentsFilter: String,
        marketId: NormalizedMarket,
        frequency: String,
        ordering: String,
        limitOverride: Int? = null,
    ): (String, Int) -> String {
        if (marketId is NormalizedMarket.DefiNormalizedMarket) {
            return { _, _ -> "" }
        }
        if (marketId is NormalizedMarket.DerivativesNormalizedMarket && marketId.type == DerivativesMarketType.OPTION) {
            return { _, _ -> "" }
        }

        val (tableNameNonDelayed, instrumentsFilterNonDelayed) = MarketCandlesNonDelayedDataSource.queryDetails(marketId)
        val intervalSqlParam = MarketCandlesUtil.candleIntervalToMinutesMap.getValue(frequency)
        val exchangeId = marketId.exchange

        return { filter, limit ->
            """
            SELECT candle_start_time::timestamp,
                candle_open_price::NUMERIC,
                candle_close_price::NUMERIC,
                candle_low_price::NUMERIC,
                candle_high_price::NUMERIC,
                candle_vwap::NUMERIC,
                candle_volume::NUMERIC,
                candle_usd_volume::NUMERIC,
                candle_trades_count::NUMERIC
            FROM $schema.$tableNameNonDelayed
            WHERE
                 $instrumentsFilterNonDelayed 
                 $filter
                 AND candle_exchange_id = $exchangeId
                 AND candle_interval = $intervalSqlParam
                 -- The last candle start time query should always return data, otherwise, it should have failed when checking the market for statistics. 
                 AND candle_start_time > (
                    SELECT $TIME_COLUMN_NAME FROM $schema.$tableName WHERE $instrumentsFilter ORDER BY $TIME_COLUMN_NAME DESC LIMIT 1
                 )
            ORDER BY $TIME_COLUMN_NAME $ordering
            LIMIT ${limitOverride ?: limit}
            """.trimIndent()
        }
    }

    companion object {
        /**
         * @return table name and instruments filter.
         */
        fun queryDetails(
            marketId: NormalizedMarket,
            frequency: String,
        ): Pair<String, String> =
            when (marketId) {
                is NormalizedMarket.SpotNormalizedMarket ->
                    Pair(
                        "candles_market_spot_${marketId.exchange}_$frequency",
                        "candle_base_id=${marketId.base} AND candle_quote_id=${marketId.quote}",
                    )

                is NormalizedMarket.DerivativesNormalizedMarket -> {
                    val type =
                        when (marketId.type) {
                            DerivativesMarketType.FUTURE -> "futures"
                            DerivativesMarketType.OPTION -> "options"
                        }
                    val symbolSqlParam = SqlUtils.escapeSql(marketId.symbol)
                    Pair(
                        "candles_market_${type}_${marketId.exchange}_$frequency",
                        "candle_symbol='$symbolSqlParam'",
                    )
                }

                is NormalizedMarket.DefiNormalizedMarket -> {
                    val exchange = Resources.getExchangeById(marketId.exchange).getOrNull()!!
                    val poolId = if (exchange.defiPoolsSupported) marketId.poolId else 0
                    Pair(
                        "candles_market_defi_${marketId.exchange}_$frequency",
                        "candle_pool_id=$poolId AND candle_base_id=${marketId.base} AND candle_quote_id=${marketId.quote}",
                    )
                }
            }
    }
}
