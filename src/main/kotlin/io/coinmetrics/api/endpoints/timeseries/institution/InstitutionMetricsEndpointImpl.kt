package io.coinmetrics.api.endpoints.timeseries.institution

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.ChunkedResponseFormat
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.Response
import io.coinmetrics.api.badNextPageToken
import io.coinmetrics.api.endpoints.GetTimeseriesInstitutionMetricsEndpoint
import io.coinmetrics.api.endpoints.GetTimeseriesInstitutionMetricsRequest
import io.coinmetrics.api.endpoints.timeseries.institution.datasources.InstitutionMetricsDataSource
import io.coinmetrics.api.models.InstitutionMetricsResponse
import io.coinmetrics.api.resources.Resources
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.catalog.metric.impl.InstitutionMetricsService
import io.coinmetrics.api.service.getTimeseriesRateLimitHeaders
import io.coinmetrics.api.toFailure
import io.coinmetrics.api.toSuccess
import io.coinmetrics.api.utils.BatchUtils
import io.coinmetrics.api.utils.ComparablePair
import io.coinmetrics.api.utils.DataUtils
import io.coinmetrics.api.utils.MetricUtils
import io.coinmetrics.api.utils.RangeQuery
import io.coinmetrics.api.utils.Utils
import io.coinmetrics.api.utils.WildcardUtils
import io.coinmetrics.api.utils.paging.PageToken
import io.coinmetrics.api.utils.paging.getPageFlow
import io.coinmetrics.api.utils.streams.SuspendableStream
import io.coinmetrics.databases.Database
import kotlin.math.max

// todo: don't allocate LinkedHashMap for each result's row, use List<Institution<String, String>> instead
class InstitutionMetricsEndpointImpl(
    candlesDb: Database,
    private val amsService: AmsService,
    private val institutionMetricsService: InstitutionMetricsService,
) : GetTimeseriesInstitutionMetricsEndpoint() {
    private val institutionMetricsDataSource = InstitutionMetricsDataSource(candlesDb)

    override suspend fun handle(request: GetTimeseriesInstitutionMetricsRequest): Response<InstitutionMetricsResponse> {
        val headers =
            amsService
                .getTimeseriesRateLimitHeaders(
                    apiKey = request.apiKey,
                    httpRequest = request.httpRequest,
                ).getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }

        val (institutions, patternRequested) =
            WildcardUtils
                .parseSimpleRequestParameter(
                    paramName = "institutions",
                    paramValues = request.institutions.toHashSet(),
                    universeOfItems = institutionMetricsService.supportedInstitutions(),
                ).getOrElse { return Response.errorResponse(it) }

        val metricsAvailabilityMap =
            institutionMetricsService
                .findTimeseriesMetrics(
                    apiKey = request.apiKey,
                    frequency = request.frequency,
                    entities = institutions,
                    metrics = request.metrics.toSet(),
                    ignoreForbiddenAndUnsupportedErrors = patternRequested,
                ).getOrElse {
                    return Response.errorResponse(it)
                }

        val streamIdToStreamSpecificData =
            metricsAvailabilityMap.mapValues { (institution, metricsAvailability) ->
                val institutionId =
                    Resources.getInstitutionIdByName(institution).getOrElse { errorMessage ->
                        log.warn(errorMessage)
                        return Response.errorResponse(
                            ApiError.BadParameter("institutions", "Value '$institution' is not supported."),
                        )
                    }
                val supportedMetrics =
                    MetricUtils
                        .validateMetricsAvailability(
                            institution,
                            "institution",
                            request.frequency,
                            request.metrics.toHashSet(),
                            metricsAvailability.metricToAvailability,
                        ).getOrElse { return Response.errorResponse(it) }
                StreamSpecificData(institutionId, supportedMetrics)
            }

        val (startTime, endTime) =
            DataUtils
                .parseTimeParameters(
                    request.startTime,
                    request.startInclusive,
                    request.endTime,
                    request.endInclusive,
                    request.timezone,
                ).getOrElse { return Response.errorResponse(it) }
        val rangeQuery =
            RangeQuery.TimeRangeQuery(
                startTime,
                request.startInclusive,
                endTime,
                request.endInclusive,
                request.pagingFrom,
            )

        val stream =
            if (request.sort == "time") {
                // sort by (time, institution) or (height, institution)

                if (patternRequested) {
                    return Response.errorResponse(
                        ApiError.BadParameter("sort", "Sorting by 'time' is not supported when wildcards are used."),
                    )
                }

                // optimization for buffer size, lower limit of buffer size is 2
                // why not 1? because an additional item is needed to check availability of the next page
                // smaller buffer size is better when we don't want to transfer a lot of redundant data via network
                val bufferSizePerStream = max(1, request.pageSize / request.institutions.size) + 1

                val mergeKeyExtractor: (LinkedHashMap<String, String?>) -> Comparable<*> = { map ->
                    ComparablePair(map["time"]!!, map["institution"]!!)
                }

                when (
                    val result =
                        BatchUtils.mergeSortStreams(
                            streamIds = streamIdToStreamSpecificData.keys.toTypedArray(),
                            pagingFrom = request.pagingFrom,
                            nextPageToken = request.nextPageToken,
                            mergeKeyExtractor = mergeKeyExtractor,
                            limitPerStream = request.limitPerInstitution,
                            streamSupplier = { id, pageToken ->
                                val initialState =
                                    pageToken
                                        ?.let {
                                            try {
                                                return@let FunctionResult.Success(PageToken.TimePageToken.parse(it))
                                            } catch (e: Exception) {
                                                log.error("Can't parse provided next_page_token '{}'.", it, e)
                                                return@let FunctionResult.Failure(badNextPageToken())
                                            }
                                        }?.getOrElse { return@mergeSortStreams it.toFailure() }
                                val streamSpecificData =
                                    streamIdToStreamSpecificData[id] ?: error("$id not found in supported institutions.")
                                handleInternal(
                                    institution = id,
                                    patternRequested = patternRequested,
                                    institutionId = streamSpecificData.institutionId,
                                    supportedMetrics = streamSpecificData.supportedMetrics,
                                    bufferSize = bufferSizePerStream,
                                    initialState = initialState,
                                    request = request,
                                    rangeQuery = rangeQuery,
                                ).toSuccess()
                            },
                        )
                ) {
                    is FunctionResult.Success -> result.value
                    is FunctionResult.Failure -> return Response.errorResponse(result.value, headers)
                }
            } else {
                // sort by (institution, time)
                val (prefetch, bufferSizePerStream) =
                    Utils.getFetchProperties(
                        request.limitPerInstitution,
                        streamIdToStreamSpecificData.size,
                        request.pageSize,
                    )

                when (
                    val result =
                        BatchUtils.sortIdsAndConcatStreams(
                            streams = streamIdToStreamSpecificData.asSequence().map { it.toPair() },
                            pagingFrom = request.pagingFrom,
                            nextPageToken = request.nextPageToken,
                            initialStreamStateParser = { PageToken.TimePageToken.parse(it) },
                            numberOfStreamsToPrefetch = prefetch,
                            limitPerStream = request.limitPerInstitution,
                            streamSupplier = { id, pageToken, (institutionId, supportedMetrics) ->
                                handleInternal(
                                    institution = id,
                                    patternRequested = patternRequested,
                                    institutionId = institutionId,
                                    supportedMetrics = supportedMetrics,
                                    bufferSize = bufferSizePerStream,
                                    initialState = pageToken,
                                    request = request,
                                    rangeQuery = rangeQuery,
                                )
                            },
                            httpRequestCoroutineContext = request.httpRequest.coroutineContext,
                            streamIdsAreResolvedDynamically = patternRequested,
                            logger = log,
                        )
                ) {
                    is FunctionResult.Success -> result.value
                    is FunctionResult.Failure -> return Response.errorResponse(result.value)
                }
            }

        val page = stream.getPageFlow(request.httpRequest, request.pageSize, request.pagingFrom)

        return Response.chunkedResponse(
            items = page,
            headers = headers,
            format = if (request.format == "csv") ChunkedResponseFormat.Csv() else ChunkedResponseFormat.Json(allowNullValues = true),
        )
    }

    private fun handleInternal(
        institution: String,
        patternRequested: Boolean,
        institutionId: Int,
        supportedMetrics: Set<String>,
        bufferSize: Int,
        initialState: PageToken.TimePageToken?,
        request: GetTimeseriesInstitutionMetricsRequest,
        rangeQuery: RangeQuery.TimeRangeQuery,
    ): SuspendableStream<LinkedHashMap<String, String?>, PageToken.TimePageToken> {
        val stream =
            institutionMetricsDataSource.query(
                institution = institution,
                patternRequested = patternRequested,
                institutionId = institutionId,
                bufferSize = bufferSize,
                initialState = initialState,
                metrics = supportedMetrics,
                rangeQuery = rangeQuery.withPageToken(initialState),
            )
        return MetricUtils.transformMetricsStream(
            metricsStream = stream,
            requestedMetrics = request.metrics,
            requestedFormat = request.format,
            entity = institution,
            entityName = "institution",
            supportedMetrics = supportedMetrics,
        )
    }
}

private data class StreamSpecificData(
    val institutionId: Int,
    val supportedMetrics: Set<String>,
)
