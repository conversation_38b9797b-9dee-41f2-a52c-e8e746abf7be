package io.coinmetrics.api.endpoints.timeseries.market.datasources

import io.coinmetrics.api.endpoints.timeseries.market.model.MarketCandlesWrapper
import io.coinmetrics.api.models.MarketCandle
import io.coinmetrics.api.utils.CommonUtils.formatBigDecimal
import io.coinmetrics.api.utils.TimeUtils
import java.sql.ResultSet

object MarketCandlesUtil {
    const val TIME_COLUMN_NAME = "candle_start_time"

    val candleIntervalToMinutesMap: Map<String, Int> =
        mapOf(
            "1m" to 1,
            "5m" to 5,
            "10m" to 10,
            "15m" to 15,
            "30m" to 30,
            "1h" to 60,
            "4h" to 240,
            "1d" to 1440,
        )

    fun createMapper(marketId: String): (ResultSet) -> MarketCandlesWrapper =
        { rs: ResultSet ->
            val time = rs.getTimestamp(TIME_COLUMN_NAME).toInstant()
            val timeFormatted = TimeUtils.dateTimeFormatter.format(time)

            MarketCandlesWrapper(
                time,
                MarketCandle(
                    time = timeFormatted,
                    market = marketId,
                    priceOpen = formatBigDecimal(rs.getBigDecimal("candle_open_price")),
                    priceHigh = formatBigDecimal(rs.getBigDecimal("candle_high_price")),
                    priceLow = formatBigDecimal(rs.getBigDecimal("candle_low_price")),
                    priceClose = formatBigDecimal(rs.getBigDecimal("candle_close_price")),
                    vwap = formatBigDecimal(rs.getBigDecimal("candle_vwap")),
                    volume = formatBigDecimal(rs.getBigDecimal("candle_volume")),
                    candleUsdVolume = formatBigDecimal(rs.getBigDecimal("candle_usd_volume")),
                    candleTradesCount = formatBigDecimal(rs.getBigDecimal("candle_trades_count")),
                ),
            )
        }
}
