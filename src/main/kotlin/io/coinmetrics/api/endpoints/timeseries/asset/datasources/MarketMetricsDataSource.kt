package io.coinmetrics.api.endpoints.timeseries.asset.datasources

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.endpoints.GetTimeseriesAssetMetricsRequest
import io.coinmetrics.api.endpoints.timeseries.asset.AssetMetricsEndpointImpl
import io.coinmetrics.api.models.PagingFrom
import io.coinmetrics.api.toSuccess
import io.coinmetrics.api.utils.DataUtils
import io.coinmetrics.api.utils.MetricUtils
import io.coinmetrics.api.utils.QueryTextBuilder
import io.coinmetrics.api.utils.RangeQuery
import io.coinmetrics.api.utils.SqlUtils
import io.coinmetrics.api.utils.paging.PageToken
import io.coinmetrics.api.utils.streams.SuspendableStream
import io.coinmetrics.api.utils.toSqlOrdering
import io.coinmetrics.databases.Database
import java.time.Instant

class MarketMetricsDataSource(
    val db: Database,
) {
    fun checkParameters(): FunctionResult<ApiError, AssetMetricsEndpointImpl.AssetMetricsDataSourceSpecificData> =
        AssetMetricsEndpointImpl.AssetMetricsDataSourceSpecificData.EMPTY.toSuccess()

    fun query(
        tableName: String,
        asset: String,
        patternRequested: Boolean,
        bufferSize: Int,
        pageToken: String?,
        frequency: String,
        metrics: List<String>,
        startTime: Instant,
        endTime: Instant,
        pagingFrom: PagingFrom,
        request: GetTimeseriesAssetMetricsRequest,
        doResampling: Boolean = false,
    ): SuspendableStream<MetricUtils.MetricsWithTimeWrapper, PageToken.TimePageToken> {
        val assetSqlParam = SqlUtils.escapeSql(asset)
        val timeFieldName = "time"
        val fullTableName = "${db.config.schema}.$tableName"
        val metricsInClauseSqlParams = metrics.joinToString { "'${SqlUtils.escapeSql(it)}'" }

        val initialState =
            pageToken?.let {
                try {
                    PageToken.TimePageToken.parse(it)
                } catch (_: Exception) {
                    error("Can't parse provided next_page_token '$it'.")
                }
            }

        val timeZone = SqlUtils.escapeSql(request.timezone)

        // timezone env is required only for queries which use INTERVAL keyword
        val (beforeQuery, afterQuery) =
            if (frequency == "1d") {
                "SET timezone='$timeZone'" to "SET timezone='UTC'"
            } else {
                null to null
            }

        val rangeQuery =
            RangeQuery
                .TimeRangeQuery(
                    startTime,
                    request.startInclusive,
                    endTime,
                    request.endInclusive,
                    pagingFrom,
                ).withPageToken(initialState)

        val timeFilter = DataUtils.createTimeFilter(rangeQuery)
        val additionalFilter = { it: MetricUtils.MetricsWithTimeWrapper ->
            timeFilter.invoke(it.time)
        }

        val queryTextBuilder: QueryTextBuilder = { filter, limit ->
            val ordering = pagingFrom.toSqlOrdering()

            val joinQuery =
                if (frequency == "1d") {
                    val startTime = "00:00:00"
                    val interval = "1 day"

                    if (pagingFrom == PagingFrom.START) {
                        """
                    INNER JOIN generate_series('1970-01-01 $startTime'::timestamp, NOW()::timestamp, '$interval'::interval) AS t(series_time)
                    ON $timeFieldName = t.series_time::timestamptz AT TIME ZONE 'UTC'
                    """
                    } else {
                        """
                    INNER JOIN generate_series(CONCAT(NOW()::date, ' $startTime')::timestamp, '1970-01-01'::timestamp, -'$interval'::interval) AS t(series_time)
                    ON $timeFieldName = t.series_time::timestamptz AT TIME ZONE 'UTC'
                    """
                    }
                } else {
                    // 1h
                    ""
                }

            val timeFilteringQuery =
                if (doResampling) {
                    SqlUtils.createTimeFilteringQuery(
                        timeFieldName = timeFieldName,
                        frequency = request.frequency,
                        timezone = request.timezone,
                    )
                } else {
                    ""
                }

            val distinctTimestamps =
                """
               SELECT 
                   DISTINCT($timeFieldName) 
               FROM $fullTableName
               $joinQuery
               WHERE 
                   asset='$assetSqlParam'
                   AND metric IN ($metricsInClauseSqlParams) 
                   $filter
                   $timeFilteringQuery
               ORDER BY $timeFieldName $ordering
               LIMIT $limit
            """

            """
                SELECT 
                    $timeFieldName, metric, value 
                FROM $fullTableName
                WHERE
                    asset='$assetSqlParam'
                    AND metric IN ($metricsInClauseSqlParams)
                    AND $timeFieldName IN (
                        $distinctTimestamps
                    )
                ORDER BY 1 $ordering
            """
        }

        return DataUtils
            .createStream(
                db = db,
                queryTextBuilder = queryTextBuilder,
                bufferSize = bufferSize,
                keyNames = arrayOf(timeFieldName),
                dataMapper = MetricUtils.createMetricsMapper(populateStatusData = false),
                rangeQuery = rangeQuery,
                initialState = initialState,
                stateResolver = { PageToken.TimePageToken(it.time) },
                beforeQuery = beforeQuery,
                afterQuery = afterQuery,
                streamId = if (patternRequested) asset else null,
            ).collapseByKey { it.time }
            .map { (time, singleTimeData) ->
                // convert rows (timeInstant, apiMetricName, valueString) to DataWithTimeWrapper
                MetricUtils.MetricsWithTimeWrapper(
                    time = time,
                    // Map<metricName, metricValue>
                    metrics = singleTimeData.map { row -> row.metric to row.value },
                )
            }.filter(additionalFilter)
    }
}
