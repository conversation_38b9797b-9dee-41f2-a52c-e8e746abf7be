package io.coinmetrics.api.endpoints.timeseries.exchangeasset

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.ApiError.UnsupportedParameterValue
import io.coinmetrics.api.ChunkedResponseFormat
import io.coinmetrics.api.Response
import io.coinmetrics.api.endpoints.GetTimeseriesExchangeAssetMetricsEndpoint
import io.coinmetrics.api.endpoints.GetTimeseriesExchangeAssetMetricsRequest
import io.coinmetrics.api.endpoints.timeseries.exchange.datasources.ExchangeBasedMetricsDataSource
import io.coinmetrics.api.endpoints.timeseries.exchange.datasources.ExchangeBasedMetricsDataSource.TimeRangeParams
import io.coinmetrics.api.endpoints.timeseries.exchangeasset.datasources.ExchangeAsset
import io.coinmetrics.api.models.ExchangeAssetMetricsResponse
import io.coinmetrics.api.resources.Resources
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.catalog.metric.impl.ExchangeAssetMetricsService
import io.coinmetrics.api.service.catalog.metric.selectUnsupportedMetrics
import io.coinmetrics.api.service.getTimeseriesRateLimitHeaders
import io.coinmetrics.api.toSuccess
import io.coinmetrics.api.utils.BatchUtils
import io.coinmetrics.api.utils.ComparablePair
import io.coinmetrics.api.utils.DataUtils
import io.coinmetrics.api.utils.MetricUtils
import io.coinmetrics.api.utils.Utils
import io.coinmetrics.api.utils.WildcardUtils
import io.coinmetrics.api.utils.paging.getPageFlow
import io.coinmetrics.api.utils.streams.SuspendableStream
import io.coinmetrics.api.utils.streams.operations.MergingStateSerializer
import java.time.Instant
import kotlin.math.max

// todo: don't allocate LinkedHashMap for each result's row, use List<Pair<String, String>> instead

/**
 * We support metrics with the following suffixes:
 * 1h
 * 5m
 * 1d
 */
class ExchangeAssetMetricsEndpointImpl(
    private val amsService: AmsService,
    private val exchangeAssetMetricsDataSource: ExchangeBasedMetricsDataSource,
    private val exchangeAssetMetricsService: ExchangeAssetMetricsService,
) : GetTimeseriesExchangeAssetMetricsEndpoint() {
    override suspend fun handle(request: GetTimeseriesExchangeAssetMetricsRequest): Response<ExchangeAssetMetricsResponse> {
        val headers =
            amsService
                .getTimeseriesRateLimitHeaders(
                    apiKey = request.apiKey,
                    httpRequest = request.httpRequest,
                ).getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }

        val (parseInfo, patternRequested) =
            WildcardUtils
                .parseRequestCompositeParameter(
                    paramName = "exchange_assets",
                    paramValues = request.exchangeAssets.toHashSet(),
                    universeOfItems = exchangeAssetMetricsService.supportedExchanges(),
                ).getOrElse { return Response.errorResponse(it) }

        val metricsAvailabilityMap =
            parseInfo
                .flatMap { (exchangeAssets, unwrappedFromPattern) ->
                    val metricsAvailability =
                        exchangeAssetMetricsService
                            .findTimeseriesMetrics(
                                apiKey = request.apiKey,
                                frequency = request.frequency,
                                entities = exchangeAssets.toSet(),
                                metrics = request.metrics.toSet(),
                                ignoreForbiddenAndUnsupportedErrors = patternRequested,
                            ).getOrElse { return Response.errorResponse(it) }

                    exchangeAssetMetricsService.filterSupportedMetrics(metricsAvailability, unwrappedFromPattern)
                }.associate { entry -> entry.key to entry.value }

        val (startTime, endTime) =
            DataUtils
                .parseTimeParameters(
                    request.startTime,
                    request.startInclusive,
                    request.endTime,
                    request.endInclusive,
                    request.timezone,
                ).getOrElse { return Response.errorResponse(it) }

        val exchangeAssetToStreamSpecificDataList =
            metricsAvailabilityMap.mapNotNull { (exchangeAsset, metricsAvailability) ->
                val unsupportedMetrics = metricsAvailability.metricToAvailability.selectUnsupportedMetrics()
                val supportedMetrics = request.metrics.toHashSet() - unsupportedMetrics
                if (supportedMetrics.isEmpty()) {
                    if (patternRequested) {
                        return@mapNotNull null
                    } else {
                        // all metrics are unsupported
                        return Response.errorResponse(
                            ApiError.BadParameter(
                                "metrics",
                                "All requested metrics aren't supported for exchange-asset '$exchangeAsset' and frequency '${request.frequency}'.",
                            ),
                        )
                    }
                }

                val (exchange, asset) = exchangeAsset.split("-")
                val exchangeId =
                    Resources.getExchangeIdByName(exchange).getOrElse { errorMessage ->
                        log.warn(errorMessage)
                        return Response.errorResponse(UnsupportedParameterValue("exchange_assets", exchangeAsset))
                    }
                val assetId =
                    Resources.getCurrencyInfo(asset)?.id
                        ?: return Response.errorResponse(UnsupportedParameterValue("exchange_assets", exchangeAsset))

                exchangeAsset.lowercase() to Triple(supportedMetrics, exchangeId, assetId)
            }

        val stream =
            if (request.sort == "time") {
                // sort by (time, exchange_asset) or (height, exchange_asset)

                // optimization for buffer size, lower limit of buffer size is 2
                // why not 1? because an additional item is needed to check availability of the next page
                // smaller buffer size is better when we don't want to transfer a lot of redundant data via network
                val bufferSizePerStream = max(1, request.pageSize / request.exchangeAssets.size) + 1

                val mergeKeyExtractor: (LinkedHashMap<String, String?>) -> Comparable<*> = { map ->
                    ComparablePair(
                        map["time"]!!,
                        map["exchange_asset"]!!,
                    )
                }

                val exchangeAssetToStreamSpecificData = exchangeAssetToStreamSpecificDataList.toMap()

                BatchUtils
                    .mergeSortStreams(
                        streamIds = exchangeAssetToStreamSpecificData.keys.toTypedArray(),
                        pagingFrom = request.pagingFrom,
                        nextPageToken = request.nextPageToken,
                        mergeKeyExtractor = mergeKeyExtractor,
                        limitPerStream = request.limitPerExchangeAsset,
                        streamSupplier = { id, pageToken ->
                            val (supportedMetrics, exchangeId, assetId) = exchangeAssetToStreamSpecificData.getValue(id)
                            handleInternal(
                                exchangeAsset = id,
                                exchangeId = exchangeId,
                                assetId = assetId,
                                patternRequested = patternRequested,
                                supportedMetrics = supportedMetrics,
                                timeRangeParams = request.toTimeRangeParams(startTime, endTime),
                                bufferSize = bufferSizePerStream,
                                pageToken = pageToken,
                                frequency = request.frequency,
                                request = request,
                            ).toSuccess()
                        },
                    ).getOrElse {
                        return Response.errorResponse(it, headers)
                    }
            } else {
                // sort by (exchange_asset, time)
                val (prefetch, bufferSizePerStream) =
                    Utils.getFetchProperties(
                        request.limitPerExchangeAsset,
                        exchangeAssetToStreamSpecificDataList.size,
                        request.pageSize,
                    )

                BatchUtils
                    .sortIdsAndConcatStreams(
                        streams = exchangeAssetToStreamSpecificDataList.asSequence(),
                        pagingFrom = request.pagingFrom,
                        nextPageToken = request.nextPageToken,
                        initialStreamStateParser = { it },
                        numberOfStreamsToPrefetch = prefetch,
                        limitPerStream = request.limitPerExchangeAsset,
                        streamSupplier = { id, pageToken, (supportedMetrics, exchangeId, assetId) ->
                            handleInternal(
                                exchangeAsset = id,
                                exchangeId = exchangeId,
                                assetId = assetId,
                                patternRequested = patternRequested,
                                supportedMetrics = supportedMetrics,
                                timeRangeParams = request.toTimeRangeParams(startTime, endTime),
                                bufferSize = bufferSizePerStream,
                                pageToken = pageToken,
                                frequency = request.frequency,
                                request = request,
                            )
                        },
                        httpRequestCoroutineContext = request.httpRequest.coroutineContext,
                        streamIdsAreResolvedDynamically = patternRequested,
                        logger = log,
                    ).getOrElse {
                        return Response.errorResponse(it)
                    }
            }

        val page = stream.getPageFlow(request.httpRequest, request.pageSize, request.pagingFrom)

        return Response.chunkedResponse(
            items = page,
            headers = headers,
            format = if (request.format == "csv") ChunkedResponseFormat.Csv() else ChunkedResponseFormat.Json(allowNullValues = true),
        )
    }

    private fun handleInternal(
        exchangeAsset: String,
        exchangeId: Int,
        assetId: Int,
        patternRequested: Boolean,
        supportedMetrics: Set<String>,
        timeRangeParams: TimeRangeParams,
        bufferSize: Int,
        pageToken: String?,
        frequency: String,
        request: GetTimeseriesExchangeAssetMetricsRequest,
    ): SuspendableStream<LinkedHashMap<String, String?>, SuspendableStream.State> {
        val pageTokenForDataSource =
            MergingStateSerializer
                .deserialize(pageToken, 1)
                .getOrElse { error(it) }
                .states
                .first()

        val minDateFor1d =
            if (frequency == "1d") {
                exchangeAssetMetricsService.findMinDateFor1dFrequency(exchangeAsset, supportedMetrics)
            } else {
                null
            }
        return exchangeAssetMetricsDataSource
            .query(
                entityId = exchangeAsset,
                patternRequested = patternRequested,
                tableName = "exchange_asset_metrics_v2",
                valueMapping = ExchangeAsset(exchangeId, assetId),
                bufferSize = bufferSize,
                pageToken = pageTokenForDataSource,
                frequency = frequency,
                metrics = supportedMetrics.toList(),
                timeRangeParams = timeRangeParams,
                minDateFor1d = minDateFor1d,
            ).let { stream ->
                MetricUtils.transformMetricsStream(
                    metricsStream = stream,
                    requestedMetrics = request.metrics,
                    requestedFormat = request.format,
                    entity = exchangeAsset,
                    entityName = "exchange_asset",
                    supportedMetrics = supportedMetrics,
                )
            }
    }

    private fun GetTimeseriesExchangeAssetMetricsRequest.toTimeRangeParams(
        startTime: Instant,
        endTime: Instant,
    ) = TimeRangeParams(
        startTime,
        startInclusive,
        endTime,
        endInclusive,
        timezone,
        pageSize,
        pagingFrom,
    )
}
