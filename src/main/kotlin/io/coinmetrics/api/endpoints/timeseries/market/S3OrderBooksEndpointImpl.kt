package io.coinmetrics.api.endpoints.timeseries.market

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.ChunkedResponseFormat
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.Response
import io.coinmetrics.api.effectiveApiKey
import io.coinmetrics.api.endpoints.GetTimeseriesMarketOrderbooksEndpoint
import io.coinmetrics.api.endpoints.GetTimeseriesMarketOrderbooksRequest
import io.coinmetrics.api.endpoints.timeseries.market.datasources.S3CompatibleDataTier
import io.coinmetrics.api.endpoints.timeseries.market.datasources.S3OrderBooksDataSource
import io.coinmetrics.api.model.DerivativesMarketType
import io.coinmetrics.api.model.DerivativesMarketType.OPTION
import io.coinmetrics.api.model.ParsedMarket
import io.coinmetrics.api.model.ParsedMarket.ParsedDerivativesMarket
import io.coinmetrics.api.model.S3BooksMarketType
import io.coinmetrics.api.models.MarketOrderbookResponse
import io.coinmetrics.api.models.PagingFrom
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.MarketConstraints
import io.coinmetrics.api.service.MarketResolvingService
import io.coinmetrics.api.service.MarketStatisticsService
import io.coinmetrics.api.service.getTimeseriesRateLimitHeaders
import io.coinmetrics.api.toFailure
import io.coinmetrics.api.utils.BatchUtils
import io.coinmetrics.api.utils.DataUtils
import io.coinmetrics.api.utils.DataUtils.adjust
import io.coinmetrics.api.utils.RangeQuery
import io.coinmetrics.api.utils.TimeUtils
import io.coinmetrics.api.utils.Utils
import io.coinmetrics.api.utils.paging.PageToken
import io.coinmetrics.api.utils.paging.getPageFlow
import io.coinmetrics.api.utils.streams.SuspendableStream
import io.coinmetrics.api.utils.tiering.DynamicTierConfig
import io.coinmetrics.s3databases.Batch
import io.coinmetrics.s3databases.read.Reader
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emptyFlow
import java.time.Instant
import java.time.ZoneId

/**
 * Endpoint implementation for timeseries market order books.
 * Supports spot, futures, and options books stored across dynamic tiers (HOT in Postgres, COLD in S3/MinIO).
 */
class S3OrderBooksEndpointImpl(
    private val dynamicTierConfigGenerator: (S3BooksMarketType) -> List<DynamicTierConfig>,
    private val s3DataSources: Map<S3BooksMarketType, S3OrderBooksDataSource>,
    private val orderBooksDbService: OrderBooksDbService,
    private val amsService: AmsService,
    private val marketResolvingService: MarketResolvingService,
    marketStatisticsService: MarketStatisticsService,
    private val communityApiKey: String,
    private val tenPercentMidPriceBookStartTime: String,
) : GetTimeseriesMarketOrderbooksEndpoint() {
    companion object {
        private val lowerGranularitiesRequiringAlignment = setOf("1m", "1h")
    }

    private val bookDepthSelector = S3OrderBookDepthSelector(marketStatisticsService)

    override suspend fun handle(request: GetTimeseriesMarketOrderbooksRequest): Response<MarketOrderbookResponse> {
        val headers =
            amsService
                .getTimeseriesRateLimitHeaders(
                    apiKey = request.apiKey,
                    httpRequest = request.httpRequest,
                ).getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }

        val (marketsConstraints, marketPatternRequested) =
            marketResolvingService
                .parseAndCheckMarkets(
                    request.apiKey,
                    request.markets,
                    marketFilter = { market ->
                        market is ParsedMarket.ParsedSpotMarket ||
                            (
                                market is ParsedDerivativesMarket &&
                                    (market.type == DerivativesMarketType.FUTURE || market.type == OPTION)
                            )
                    },
                    errorMessageForFilteredOutMarketsIfPatternIsNotRequested = { "Only spot, future and option markets are supported." },
                ).getOrElse { return Response.errorResponse(it) }

        val patchedRequest =
            if (request.format == "json_stream") {
                request.copy(
                    pagingFrom = PagingFrom.START,
                    nextPageToken = null,
                )
            } else {
                request
            }

        return buildResponseStream(patchedRequest, marketsConstraints, marketPatternRequested)
            .getOrElse { return Response.errorResponse(it, headers) }
            .let { result ->
                if (patchedRequest.format == "json_stream") {
                    Response.chunkedResponse(
                        items = result.asFlow(),
                        headers = headers,
                        format = ChunkedResponseFormat.JsonStream,
                    )
                } else {
                    // json
                    val pageFlow =
                        result.getPageFlow(
                            patchedRequest.httpRequest,
                            patchedRequest.pageSize,
                            patchedRequest.pagingFrom == PagingFrom.START,
                        )
                    Response.chunkedResponse(
                        items = pageFlow,
                        headers = headers,
                        format = ChunkedResponseFormat.Json(),
                    )
                }
            }
    }

    internal suspend fun buildResponseStream(
        request: GetTimeseriesMarketOrderbooksRequest,
        marketsConstraints: Map<String, MarketConstraints>,
        marketPatternRequested: Boolean,
    ): FunctionResult<ApiError, SuspendableStream<ByteArray, SuspendableStream.State>> {
        val (prefetch, bufferSizePerStream) =
            Utils.getFetchProperties(
                request.limitPerMarket,
                marketsConstraints.size,
                request.pageSize,
            )

        val parsedDepth =
            orderBooksDbService.parseAndValidateDepth(request.depthLimit, request.pageSize).getOrElse {
                return FunctionResult.Failure(it)
            }

        val startTimeStr =
            if (request.depthLimit == "10pct_mid_price" &&
                (request.startTime == null || request.startTime < tenPercentMidPriceBookStartTime)
            ) {
                tenPercentMidPriceBookStartTime
            } else {
                request.startTime
            }

        val parsedTimeParams =
            DataUtils
                .parseAndValidateTimeParameters(
                    startTimeStr,
                    request.startInclusive,
                    request.endTime,
                    request.endInclusive,
                    request.timezone,
                    request.pageSize,
                    request.pagingFrom,
                ).getOrElse { return it.toFailure() }

        val downSamplingConfigDbLevel =
            buildDownsamplingConfigDbLevel(
                depth = parsedDepth,
                pagingFrom = request.pagingFrom,
                /**
                 * The timezone has already been validated by DataUtils.parseAndValidateTimeParameters
                 */
                timezone = request.timezone,
            )

        return BatchUtils.sortIdsAndConcatStreams(
            streams = marketsConstraints.entries.asSequence().map { it.toPair() },
            numberOfStreamsToPrefetch = prefetch,
            pagingFromStart = request.pagingFrom == PagingFrom.START,
            nextPageToken = request.nextPageToken,
            initialStreamStateParser = { PageToken.TimePageToken.parse(it) },
            limitPerStream = request.limitPerMarket,
            streamSupplier = { id, state, marketConstraints ->
                handleInternal(
                    market = id,
                    initialState = state,
                    request = request,
                    marketPatternRequested = marketPatternRequested,
                    marketConstraints = marketConstraints,
                    parsedDepth = parsedDepth,
                    parsedTimeParams = parsedTimeParams,
                    downSamplingConfigDbLevel = downSamplingConfigDbLevel,
                    postgresBufferSizePerStream = bufferSizePerStream,
                ).map { bookWrapper ->
                    bookWrapper.bytes
                }
            },
            streamIdsAreResolvedDynamically = marketPatternRequested,
            httpRequestCoroutineContext = request.httpRequest.coroutineContext,
            logger = log,
        )
    }

    suspend fun handleInternal(
        market: String,
        initialState: PageToken.TimePageToken?,
        request: GetTimeseriesMarketOrderbooksRequest,
        marketPatternRequested: Boolean,
        marketConstraints: MarketConstraints,
        parsedDepth: Int,
        parsedTimeParams: Triple<Instant, Instant, Boolean>,
        downSamplingConfigDbLevel: TimeUtils.StatefulDownSamplerConfig?,
        postgresBufferSizePerStream: Int,
    ): SuspendableStream<Reader.TimedByteArray, PageToken.TimePageToken> {
        val (marketId, keyMinTime, keyMaxTime, parsedMarket) = marketConstraints
        val (startTime, endTime, pagingFromStart) =
            parsedTimeParams.adjust(
                DataUtils.communityEnforcedStart(request.apiKey, communityApiKey),
                keyMinTime,
                keyMaxTime,
            )
                /**
                 * Returning an empty stream for simplicity.
                 * We could enhance this later, for example, by returning a function failure result if a pattern was not requested —
                 * but that would require more refactoring, which may not be necessary at this point.
                 */
                ?: return SuspendableStream.empty()
        val rangeQuery =
            RangeQuery
                .TimeRangeQuery(
                    startTime,
                    request.startInclusive,
                    endTime,
                    request.endInclusive,
                    pagingFromStart,
                ).withPageToken(initialState)

        val (enforcedDepthS3, enforcedDepthDb, statDepth, bucketNamePrefix) =
            bookDepthSelector.getApplicableDepths(
                parsedMarket = parsedMarket,
                parsedDepth = parsedDepth,
                requestedStartTime = rangeQuery.startKey,
                requestedEndTime = rangeQuery.endKey,
            )

        /**
         * Construct a downsampling configuration per market for final data output, as depth may vary per market depending on available statistics.
         */
        val downSamplingConfigPresentationLevel =
            buildDownsamplingConfigPresentationLevel(
                depth = statDepth,
                granularity = request.granularity,
                pagingFrom = request.pagingFrom,
                timezone = request.timezone,
            )
        val downSamplingFilter = downSamplingConfigPresentationLevel?.let { TimeUtils.createStatefulDownSampler(it) }
        val booksDataType =
            S3BooksMarketType.fromMarketType(parsedMarket.getType())
                ?: error("Unsupported market type: ${parsedMarket.getType()}")
        val tiers = dynamicTierConfigGenerator.invoke(booksDataType)

        val s3CompatibleTiers =
            tiers.map { tier ->
                when (BookTierName.valueOf(tier.name)) {
                    BookTierName.HOT -> {
                        object : S3CompatibleDataTier {
                            override val supportedTimeRange = tier.timeRange

                            override fun newFlow(rangeQuery: RangeQuery.TimeRangeQuery): Flow<Batch<Reader.TimedByteArray>> =
                                orderBooksDbService
                                    .handleInternal(
                                        market = parsedMarket,
                                        marketId = marketId,
                                        rangeQuery = rangeQuery,
                                        // null because rangeQuery was already adjusted
                                        initialState = null,
                                        request = request,
                                        marketPatternRequested = marketPatternRequested,
                                        enforcedDepth = enforcedDepthS3,
                                        dbDepth = statDepth,
                                        bufferSize = postgresBufferSizePerStream,
                                        downSamplingConfig = downSamplingConfigDbLevel,
                                    ).map { bookWrapper ->
                                        listOf(
                                            Reader.TimedByteArray(
                                                bytes =
                                                    Response.objToJsonBytes(
                                                        bookWrapper.orderBook,
                                                        request.httpRequest,
                                                    ),
                                                preParsedTime = lazyOf(bookWrapper.time),
                                                timeExtractor = { error("timeExtractor must not be called.") },
                                            ),
                                        )
                                    }.asFlow()
                        }
                    }

                    BookTierName.COLD -> {
                        object : S3CompatibleDataTier {
                            override val supportedTimeRange = tier.timeRange

                            override fun newFlow(rangeQuery: RangeQuery.TimeRangeQuery): Flow<Batch<Reader.TimedByteArray>> =
                                s3DataSources[booksDataType]?.read(
                                    bucketName = bucketNamePrefix,
                                    statDepth = statDepth,
                                    parsedMarket = parsedMarket,
                                    rangeQuery = rangeQuery,
                                    enforcedDepth = enforcedDepthS3,
                                    key =
                                        request.httpRequest.queryParameters["api_key"]
                                            ?: request.httpRequest.clientIp,
                                    apiKey = request.httpRequest.effectiveApiKey() ?: "",
                                ) ?: emptyFlow()
                        }
                    }
                }
            }

        return DataUtils
            .createTieredStream(
                tiers = s3CompatibleTiers,
                rangeQuery = rangeQuery,
                initialState = initialState,
                stateResolver = { PageToken.TimePageToken(it.time.value) },
                streamId =
                    if (marketPatternRequested) {
                        market
                    } else {
                        null
                    },
                httpRequestCoroutineContext = request.httpRequest.coroutineContext,
            ).let { stream ->
                if (downSamplingFilter == null) {
                    stream
                } else {
                    stream.filter { downSamplingFilter(it.time.value) }
                }
            }
    }

    /**
     * Apply downsampling at DB-level if raw data granularity doesn't match expected presentation intervals.
     */
    private fun buildDownsamplingConfigDbLevel(
        depth: Int,
        pagingFrom: PagingFrom,
        timezone: String,
    ): TimeUtils.StatefulDownSamplerConfig? {
        /**
         * Only raw granularity requires downsampling at the DB level.
         * Other granularities are handled at the presentation layer.
         * Handles 'depth = 0', i.e., 10% mid-price (10pct_mid_price), as well.
         */
        val effectiveGranularity = if (depth <= 100) "10s" else "1h"

        return TimeUtils
            .createStatefulDownSamplerConfig(
                granularity = effectiveGranularity,
                pagingFromStart = pagingFrom == PagingFrom.START,
                withAlignment = true,
                /**
                 * The timezone has already been validated by DataUtils.parseAndValidateTimeParameters
                 */
                timezone = ZoneId.of(timezone),
                /**
                 * This exception is not expected under normal conditions, but we handle it defensively.
                 */
            ).getOrElse { apiError -> throw IllegalStateException(apiError.message) }
    }

    private fun buildDownsamplingConfigPresentationLevel(
        depth: Int,
        granularity: String,
        pagingFrom: PagingFrom,
        timezone: String,
    ): TimeUtils.StatefulDownSamplerConfig? {
        /**
         * Data in S3 and Postgres (after deduplication) is already downsampled for raw granularity:
         * - 10s for depths ≤100
         * - 1h for depths >100
         */
        if (granularity == "raw") {
            return null
        }
        /**
         * Low granularities like 1m or 1h are not applicable for depths >100, which only support 1h.
         */
        if (lowerGranularitiesRequiringAlignment.contains(granularity) && depth > 100) {
            return null
        }
        return TimeUtils
            .createStatefulDownSamplerConfig(
                granularity = granularity,
                pagingFromStart = pagingFrom == PagingFrom.START,
                withAlignment = true,
                /**
                 * The timezone has already been validated by DataUtils.parseAndValidateTimeParameters
                 */
                timezone = ZoneId.of(timezone),
                /**
                 * This exception is not expected under normal conditions, but we handle it defensively.
                 */
            ).getOrElse { apiError -> throw IllegalStateException(apiError.message) }
    }
}
