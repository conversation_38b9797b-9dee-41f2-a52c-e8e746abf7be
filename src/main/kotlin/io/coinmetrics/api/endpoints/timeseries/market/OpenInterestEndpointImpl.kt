package io.coinmetrics.api.endpoints.timeseries.market

import io.coinmetrics.api.ChunkedResponseFormat
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.Response
import io.coinmetrics.api.endpoints.GetTimeseriesMarketOpenInteresetEndpoint
import io.coinmetrics.api.endpoints.GetTimeseriesMarketOpenInteresetRequest
import io.coinmetrics.api.model.DerivativesMarketType
import io.coinmetrics.api.model.NormalizedMarket
import io.coinmetrics.api.model.ParsedMarket
import io.coinmetrics.api.models.MarketOpenInterest
import io.coinmetrics.api.models.MarketOpenInterestResponse
import io.coinmetrics.api.models.PagingFrom
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.MarketConstraints
import io.coinmetrics.api.service.MarketResolvingService
import io.coinmetrics.api.service.getTimeseriesRateLimitHeaders
import io.coinmetrics.api.utils.BatchUtils
import io.coinmetrics.api.utils.CommonUtils
import io.coinmetrics.api.utils.DataUtils
import io.coinmetrics.api.utils.DataUtils.adjust
import io.coinmetrics.api.utils.QueryTextBuilder
import io.coinmetrics.api.utils.RangeQuery
import io.coinmetrics.api.utils.SqlUtils
import io.coinmetrics.api.utils.TimeUtils
import io.coinmetrics.api.utils.Utils
import io.coinmetrics.api.utils.paging.PageToken.TimePageToken
import io.coinmetrics.api.utils.paging.getPageFlow
import io.coinmetrics.api.utils.streams.SuspendableStream
import io.coinmetrics.databases.Database
import kotlinx.coroutines.flow.map
import java.sql.ResultSet
import java.time.Instant
import java.time.ZoneId

class OpenInterestEndpointImpl(
    private val db: Database,
    private val amsService: AmsService,
    private val marketResolvingService: MarketResolvingService,
    private val communityApiKey: String,
) : GetTimeseriesMarketOpenInteresetEndpoint() {
    override suspend fun handle(request: GetTimeseriesMarketOpenInteresetRequest): Response<MarketOpenInterestResponse> {
        val headers =
            amsService
                .getTimeseriesRateLimitHeaders(
                    apiKey = request.apiKey,
                    httpRequest = request.httpRequest,
                ).getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }

        val parsedTimeParams =
            DataUtils
                .parseTimeParameters(
                    startTimeStr = request.startTime,
                    startInclusive = request.startInclusive,
                    endTimeStr = request.endTime,
                    endInclusive = request.endInclusive,
                    timezone = request.timezone,
                ).getOrElse { return Response.errorResponse(it) }

        val (marketsConstraints, marketPatternRequested) =
            marketResolvingService
                .parseAndCheckMarkets(
                    apiKey = request.apiKey,
                    requestedMarkets = request.markets,
                    marketFilter = { market -> market is ParsedMarket.ParsedDerivativesMarket },
                    errorMessageForFilteredOutMarketsIfPatternIsNotRequested = { "Only future and option markets are supported." },
                ).getOrElse { return Response.errorResponse(it) }

        val (prefetch, bufferSizePerStream) =
            Utils.getFetchProperties(
                request.limitPerMarket,
                marketsConstraints.size,
                request.pageSize,
            )

        val downSamplingConfig =
            TimeUtils
                .createStatefulDownSamplerConfig(
                    granularity = request.granularity,
                    pagingFromStart = request.pagingFrom == PagingFrom.START,
                    withAlignment = true,
                    timezone = ZoneId.of(request.timezone),
                ).getOrElse {
                    return Response.errorResponse(it)
                }

        return when (
            val result =
                BatchUtils.sortIdsAndConcatStreams(
                    streams = marketsConstraints.asSequence().map { it.toPair() },
                    numberOfStreamsToPrefetch = prefetch,
                    pagingFrom = request.pagingFrom,
                    nextPageToken = request.nextPageToken,
                    initialStreamStateParser = { TimePageToken.parse(it) },
                    limitPerStream = request.limitPerMarket,
                    streamSupplier = { id, state, marketConstraints ->
                        handleInternal(
                            market = id,
                            initialState = state,
                            request = request,
                            marketPatternRequested = marketPatternRequested,
                            marketConstraints = marketConstraints,
                            parsedTimeParams = parsedTimeParams,
                            bufferSize = bufferSizePerStream,
                            downSamplingConfig = downSamplingConfig,
                        )
                    },
                    streamIdsAreResolvedDynamically = marketPatternRequested,
                    httpRequestCoroutineContext = request.httpRequest.coroutineContext,
                    logger = log,
                )
        ) {
            is FunctionResult.Success -> {
                val page =
                    result.value
                        .map { it.marketOpenInterest }
                        .getPageFlow(
                            request.httpRequest,
                            request.pageSize,
                            request.pagingFrom,
                        ).let {
                            if (request.format == "csv") {
                                it.map { obj -> (obj as? MarketOpenInterest)?.toMap() ?: obj }
                            } else {
                                it
                            }
                        }
                Response.chunkedResponse(
                    items = page,
                    headers = headers,
                    format = if (request.format == "csv") ChunkedResponseFormat.Csv(nullValue = "") else ChunkedResponseFormat.Json(),
                )
            }

            is FunctionResult.Failure -> Response.errorResponse(result.value, headers)
        }
    }

    private fun MarketOpenInterest.toMap() =
        mapOf(
            "market" to market,
            "time" to time,
            "contract_count" to contractCount,
            "value_usd" to valueUsd,
            "database_time" to databaseTime,
            "exchange_time" to exchangeTime,
        )

    private fun handleInternal(
        market: String,
        initialState: TimePageToken?,
        request: GetTimeseriesMarketOpenInteresetRequest,
        marketPatternRequested: Boolean,
        marketConstraints: MarketConstraints,
        parsedTimeParams: Pair<Instant, Instant>,
        bufferSize: Int,
        downSamplingConfig: TimeUtils.StatefulDownSamplerConfig?,
    ): SuspendableStream<MarketOpenInterestWrapper, TimePageToken> {
        val (marketId, keyMinTime, keyMaxTime) = marketConstraints

        marketId as NormalizedMarket.DerivativesNormalizedMarket

        val timeFieldName = "open_interest_time"
        val queryConfig =
            run {
                val symbolSqlParam = SqlUtils.escapeSql(marketId.symbol)
                when (marketId.type) {
                    DerivativesMarketType.FUTURE ->
                        QueryConfiguration(
                            db = db,
                            tableName = "futures_open_interest",
                            instrumentFilter = "open_interest_symbol='$symbolSqlParam'",
                        )

                    DerivativesMarketType.OPTION ->
                        QueryConfiguration(
                            db = db,
                            tableName = "option_open_interest",
                            instrumentFilter = "open_interest_symbol='$symbolSqlParam'",
                        )
                }
            }

        val (startTime, endTime) =
            parsedTimeParams.adjust(
                DataUtils.communityEnforcedStart(request.apiKey, communityApiKey),
                keyMinTime,
                keyMaxTime,
            )
                /**
                 * Returning an empty stream for simplicity.
                 * We could enhance this later, for example, by returning a function failure result if a pattern was not requested —
                 * but that would require more refactoring, which may not be necessary at this point.
                 */
                ?: return SuspendableStream.empty()

        val rangeQuery =
            RangeQuery
                .TimeRangeQuery(
                    startKey = startTime,
                    startInclusive = request.startInclusive,
                    endKey = endTime,
                    endInclusive = request.endInclusive,
                    pagingFrom = request.pagingFrom,
                ).withPageToken(initialState)

        val timeFilter = DataUtils.createTimeFilter(rangeQuery)
        val additionalFilter = { it: MarketOpenInterestWrapper ->
            timeFilter.invoke(it.time)
        }

        val queryTextBuilder: QueryTextBuilder = { filter, limit ->
            val ordering = if (request.pagingFrom == PagingFrom.START) "ASC" else "DESC"
            """
            SELECT 
                $timeFieldName,
                open_interest_exchange_id,
                open_interest_symbol,
                open_interest_contract_count,
                open_interest_value_usd,
                open_interest_database_time
            FROM ${queryConfig.db.config.schema}.${queryConfig.tableName}
            WHERE 
                ${queryConfig.instrumentFilter}
                AND open_interest_exchange_id=${marketId.exchange}
                $filter
            ORDER BY $timeFieldName $ordering
            LIMIT $limit
            """
        }

        val downSamplingFilter = downSamplingConfig?.let { TimeUtils.createStatefulDownSampler(it) }

        return DataUtils
            .createStream(
                db = queryConfig.db,
                queryTextBuilder = queryTextBuilder,
                bufferSize = bufferSize,
                keyNames = arrayOf(timeFieldName),
                dataMapper = createMapper(market),
                rangeQuery = rangeQuery,
                initialState = initialState,
                stateResolver = { TimePageToken(it.time) },
                streamId =
                    if (marketPatternRequested) {
                        market
                    } else {
                        null
                    },
            ).filter(additionalFilter)
            .let { stream ->
                if (downSamplingFilter == null) {
                    stream
                } else {
                    stream.filter { downSamplingFilter(it.time) }
                }
            }
    }

    private fun createMapper(market: String): (ResultSet) -> MarketOpenInterestWrapper =
        { rs ->
            val time = rs.getTimestamp("open_interest_time").toInstant()
            val timeFormatted = TimeUtils.dateTimeFormatter.format(time)

            val dbTime =
                rs.getTimestamp("open_interest_database_time").toInstant().let {
                    TimeUtils.dateTimeFormatter.format(it)
                }

            val contractCount = rs.getBigDecimal("open_interest_contract_count")
            val contractValueUsd = rs.getBigDecimal("open_interest_value_usd")

            MarketOpenInterestWrapper(
                time,
                MarketOpenInterest(
                    time = timeFormatted,
                    market = market,
                    contractCount = CommonUtils.formatBigDecimal(contractCount),
                    valueUsd = CommonUtils.formatBigDecimal(contractValueUsd),
                    databaseTime = dbTime,
                    exchangeTime = timeFormatted,
                ),
            )
        }

    private data class MarketOpenInterestWrapper(
        val time: Instant,
        val marketOpenInterest: MarketOpenInterest,
        val isValid: Boolean = true,
    )

    private class QueryConfiguration(
        val db: Database,
        val tableName: String,
        val instrumentFilter: String,
    )
}
