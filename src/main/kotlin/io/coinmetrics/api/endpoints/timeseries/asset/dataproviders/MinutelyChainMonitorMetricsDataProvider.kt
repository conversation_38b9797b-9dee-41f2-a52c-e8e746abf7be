package io.coinmetrics.api.endpoints.timeseries.asset.dataproviders

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.endpoints.GetTimeseriesAssetMetricsRequest
import io.coinmetrics.api.endpoints.timeseries.asset.AssetMetricsEndpointHelper
import io.coinmetrics.api.endpoints.timeseries.asset.AssetMetricsEndpointImpl
import io.coinmetrics.api.endpoints.timeseries.asset.datasources.MinutelyChainMonitorMetricsDataSource
import io.coinmetrics.api.models.PagingFrom
import io.coinmetrics.api.utils.MetricUtils
import io.coinmetrics.api.utils.TimeUtils
import io.coinmetrics.api.utils.paging.PageToken
import io.coinmetrics.api.utils.streams.SuspendableStream
import java.time.Instant

class MinutelyChainMonitorMetricsDataProvider(
    private val minutelyChainMonitorMetricsDataSource: MinutelyChainMonitorMetricsDataSource,
) : AssetMetricDataProvider<MetricUtils.MetricsWithTimeWrapper> {
    override fun isApplicable(
        metrics: List<String>,
        frequency: AssetMetricsEndpointHelper.Frequency,
        dataSourceGroup: AssetMetricsEndpointHelper.DataSourceGroup,
    ): Boolean =
        dataSourceGroup == AssetMetricsEndpointHelper.DataSourceGroup.MINUTELY_CHAIN_MONITOR_METRICS &&
            frequency == AssetMetricsEndpointHelper.Frequency.ONE_MINUTE

    override suspend fun checkParameters(
        asset: String,
        metrics: List<String>,
    ): FunctionResult<ApiError, AssetMetricsEndpointImpl.AssetMetricsDataSourceSpecificData> =
        minutelyChainMonitorMetricsDataSource.checkParameters()

    override fun provide(
        asset: String,
        patternRequested: Boolean,
        metrics: List<String>,
        startTime: Instant,
        endTime: Instant,
        pagingFrom: PagingFrom,
        request: GetTimeseriesAssetMetricsRequest,
        pageTokenForDataSource: String?,
        frequencyOffset: TimeUtils.NormalizedFrequencyOffset,
        frequency: AssetMetricsEndpointHelper.Frequency,
        bufferSize: Int,
        communityApiKey: String,
        dataSourceSpecificData: AssetMetricsEndpointImpl.AssetMetricsDataSourceSpecificData?,
    ): SuspendableStream<MetricUtils.MetricsWithTimeWrapper, PageToken.TimePageToken> =
        minutelyChainMonitorMetricsDataSource.query(
            asset = asset,
            patternRequested = patternRequested,
            bufferSize = bufferSize,
            pageToken = pageTokenForDataSource,
            metrics = metrics,
            startTime = startTime,
            endTime = endTime,
            pagingFrom = pagingFrom,
            request = request,
        )
}
