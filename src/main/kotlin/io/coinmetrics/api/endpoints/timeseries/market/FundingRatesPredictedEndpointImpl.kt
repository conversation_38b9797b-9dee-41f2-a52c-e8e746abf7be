package io.coinmetrics.api.endpoints.timeseries.market

import io.coinmetrics.api.ChunkedResponseFormat
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.Response
import io.coinmetrics.api.endpoints.GetTimeseriesMarketFundingRatesPredictedEndpoint
import io.coinmetrics.api.endpoints.GetTimeseriesMarketFundingRatesPredictedRequest
import io.coinmetrics.api.model.DerivativesMarketType
import io.coinmetrics.api.model.NormalizedMarket
import io.coinmetrics.api.model.ParsedMarket
import io.coinmetrics.api.models.MarketFundingRatePredicted
import io.coinmetrics.api.models.MarketFundingRatesPredictedResponse
import io.coinmetrics.api.models.PagingFrom
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.MarketConstraints
import io.coinmetrics.api.service.MarketResolvingService
import io.coinmetrics.api.service.MarketStatisticsService
import io.coinmetrics.api.service.getTimeseriesRateLimitHeaders
import io.coinmetrics.api.utils.BatchUtils
import io.coinmetrics.api.utils.CommonUtils
import io.coinmetrics.api.utils.DataUtils
import io.coinmetrics.api.utils.DataUtils.adjust
import io.coinmetrics.api.utils.QueryTextBuilder
import io.coinmetrics.api.utils.RangeQuery
import io.coinmetrics.api.utils.SqlUtils
import io.coinmetrics.api.utils.TimeUtils
import io.coinmetrics.api.utils.Utils
import io.coinmetrics.api.utils.paging.PageToken
import io.coinmetrics.api.utils.paging.getPageFlow
import io.coinmetrics.api.utils.streams.SuspendableStream
import io.coinmetrics.databases.Database
import java.sql.ResultSet
import java.time.Instant

class FundingRatesPredictedEndpointImpl(
    private val db: Database,
    private val amsService: AmsService,
    private val marketResolvingService: MarketResolvingService,
    private val marketStatisticsService: MarketStatisticsService,
    private val communityApiKey: String,
) : GetTimeseriesMarketFundingRatesPredictedEndpoint() {
    override suspend fun handle(request: GetTimeseriesMarketFundingRatesPredictedRequest): Response<MarketFundingRatesPredictedResponse> {
        val headers =
            amsService
                .getTimeseriesRateLimitHeaders(
                    apiKey = request.apiKey,
                    httpRequest = request.httpRequest,
                ).getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }

        val parsedTimeParams =
            DataUtils
                .parseTimeParameters(
                    startTimeStr = request.startTime,
                    startInclusive = request.startInclusive,
                    endTimeStr = request.endTime,
                    endInclusive = request.endInclusive,
                    timezone = request.timezone,
                ).getOrElse { return Response.errorResponse(it) }

        val (marketsConstraints, marketPatternRequested) =
            marketResolvingService
                .parseAndCheckMarkets(
                    apiKey = request.apiKey,
                    requestedMarkets = request.markets,
                    marketFilter = { market ->
                        market is ParsedMarket.ParsedDerivativesMarket && market.type == DerivativesMarketType.FUTURE
                    },
                    errorMessageForFilteredOutMarketsIfPatternIsNotRequested = { "Only future markets are supported." },
                ).getOrElse { return Response.errorResponse(it) }

        val (prefetch, bufferSizePerStream) =
            Utils.getFetchProperties(
                request.limitPerMarket,
                marketsConstraints.size,
                request.pageSize,
            )

        return when (
            val result =
                BatchUtils.sortIdsAndConcatStreams(
                    streams = marketsConstraints.asSequence().map { it.toPair() },
                    numberOfStreamsToPrefetch = prefetch,
                    pagingFrom = request.pagingFrom,
                    nextPageToken = request.nextPageToken,
                    initialStreamStateParser = { PageToken.TimePageToken.parse(it) },
                    limitPerStream = request.limitPerMarket,
                    streamSupplier = { id, state, marketConstraints ->
                        handleInternal(
                            market = id,
                            initialState = state,
                            request = request,
                            marketPatternRequested = marketPatternRequested,
                            marketConstraints = marketConstraints,
                            parsedTimeParams = parsedTimeParams,
                            bufferSize = bufferSizePerStream,
                        )
                    },
                    streamIdsAreResolvedDynamically = marketPatternRequested,
                    httpRequestCoroutineContext = request.httpRequest.coroutineContext,
                    logger = log,
                )
        ) {
            is FunctionResult.Success -> {
                val pageFlow =
                    result.value.map { it.marketFundingRate }.getPageFlow(
                        httpRequest = request.httpRequest,
                        pageSize = request.pageSize,
                        pagingFrom = request.pagingFrom,
                    )

                Response.chunkedResponse(
                    items = pageFlow,
                    headers = headers,
                    format = ChunkedResponseFormat.Json(),
                )
            }

            is FunctionResult.Failure -> Response.errorResponse(result.value, headers)
        }
    }

    private fun handleInternal(
        market: String,
        initialState: PageToken.TimePageToken?,
        request: GetTimeseriesMarketFundingRatesPredictedRequest,
        marketPatternRequested: Boolean,
        marketConstraints: MarketConstraints,
        parsedTimeParams: Pair<Instant, Instant>,
        bufferSize: Int,
    ): SuspendableStream<MarketFundingRateWrapper, PageToken.TimePageToken> {
        // Return empty stream when sharded table does not exist
        if (marketStatisticsService.getFundingRatesPredictedStatistics(market) == null) {
            return SuspendableStream.empty()
        }

        val (marketId, keyMinTime, keyMaxTime) = marketConstraints

        val derivativeMarket = marketId as NormalizedMarket.DerivativesNormalizedMarket

        if (MarketEndpointUtil.isDerivativeMarketOperationTimeNotOverlap(
                marketName = market,
                marketType = marketId.type,
                timeRange = parsedTimeParams.first to parsedTimeParams.second,
                marketStatisticsService = marketStatisticsService,
            )
        ) {
            return SuspendableStream.empty()
        }

        val timeFieldName = "ticker_deduplication_time"
        val tableName = "${db.config.schema}.futures_ticker_${derivativeMarket.exchange}"
        val symbolSqlParam = SqlUtils.escapeSql(derivativeMarket.symbol)
        val instrumentFilter = "ticker_symbol='$symbolSqlParam'"

        val (startTime, endTime) =
            parsedTimeParams.adjust(
                DataUtils.communityEnforcedStart(request.apiKey, communityApiKey),
                keyMinTime,
                keyMaxTime,
            )
                /**
                 * Returning an empty stream for simplicity.
                 * We could enhance this later, for example, by returning a function failure result if a pattern was not requested —
                 * but that would require more refactoring, which may not be necessary at this point.
                 */
                ?: return SuspendableStream.empty()

        val rangeQuery =
            RangeQuery
                .TimeRangeQuery(
                    startKey = startTime,
                    startInclusive = request.startInclusive,
                    endKey = endTime,
                    endInclusive = request.endInclusive,
                    pagingFrom = request.pagingFrom,
                ).withPageToken(initialState)

        val timeFilter = DataUtils.createTimeFilter(rangeQuery)
        val additionalFilter = { it: MarketFundingRateWrapper ->
            timeFilter.invoke(it.time)
        }

        val queryTextBuilder: QueryTextBuilder = { filter, limit ->
            val ordering = if (request.pagingFrom == PagingFrom.START) "ASC" else "DESC"
            """
            SELECT 
                $timeFieldName,
                ${derivativeMarket.exchange} AS ticker_exchange_id,
                ticker_symbol,
                ticker_estimated_funding_rate,
                ticker_estimated_funding_rate_time,
                ticker_database_time
            FROM $tableName
            WHERE 
                $instrumentFilter
                $filter
            AND ticker_estimated_funding_rate IS NOT NULL    
            ORDER BY $timeFieldName $ordering
            LIMIT $limit
            """
        }

        val stream =
            DataUtils
                .createStream(
                    db = db,
                    queryTextBuilder = queryTextBuilder,
                    bufferSize = bufferSize,
                    keyNames = arrayOf(timeFieldName),
                    dataMapper = createMapper(market),
                    rangeQuery = rangeQuery,
                    initialState = initialState,
                    stateResolver = { PageToken.TimePageToken(it.time) },
                    streamId =
                        if (marketPatternRequested) {
                            market
                        } else {
                            null
                        },
                ).filter(additionalFilter)

        return stream
    }

    private fun createMapper(market: String): (ResultSet) -> MarketFundingRateWrapper =
        { rs ->
            val time = rs.getTimestamp("ticker_deduplication_time").toInstant()
            val timeFormatted = TimeUtils.dateTimeFormatter.format(time)

            val dbTime =
                rs.getTimestamp("ticker_database_time").toInstant().let {
                    TimeUtils.dateTimeFormatter.format(it)
                }

            MarketFundingRateWrapper(
                time,
                MarketFundingRatePredicted(
                    time = timeFormatted,
                    market = market,
                    ratePredicted = CommonUtils.formatBigDecimal(rs.getBigDecimal("ticker_estimated_funding_rate")),
                    rateTime =
                        rs
                            .getTimestamp(
                                "ticker_estimated_funding_rate_time",
                            )?.let { TimeUtils.dateTimeFormatter.format(it.toInstant()) },
                    databaseTime = dbTime,
                ),
            )
        }

    private class MarketFundingRateWrapper(
        val time: Instant,
        val marketFundingRate: MarketFundingRatePredicted,
    )
}
