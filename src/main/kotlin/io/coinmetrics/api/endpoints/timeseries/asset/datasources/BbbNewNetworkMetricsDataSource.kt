package io.coinmetrics.api.endpoints.timeseries.asset.datasources

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.endpoints.GetTimeseriesAssetMetricsRequest
import io.coinmetrics.api.endpoints.blockchain.BlockchainEndpointUtils
import io.coinmetrics.api.endpoints.timeseries.asset.AssetMetricsEndpointImpl
import io.coinmetrics.api.models.PagingFrom
import io.coinmetrics.api.resources.Resources
import io.coinmetrics.api.toSuccess
import io.coinmetrics.api.utils.CommonUtils
import io.coinmetrics.api.utils.DataUtils
import io.coinmetrics.api.utils.QueryTextBuilder
import io.coinmetrics.api.utils.RangeQuery
import io.coinmetrics.api.utils.SqlUtils
import io.coinmetrics.api.utils.TimeUtils
import io.coinmetrics.api.utils.paging.PageToken
import io.coinmetrics.api.utils.streams.SuspendableStream
import io.coinmetrics.atlas.v2.codec.Codec
import io.coinmetrics.databases.Database
import java.math.BigInteger
import java.sql.ResultSet
import java.time.Instant

class BbbNewNetworkMetricsDataSource(
    private val db: Database,
) {
    companion object {
        // hardcoded "staging" schema until production schema is ready
        private const val TABLE_NAME = "public.\"1b_metrics\""
        private const val BASE_METRIC_NAME: String = "BlkHgt"
    }

    internal data class BbbNewNetworkMetricsDataSourceSpecificData(
        val rangeQuery: RangeQuery.BigIntegerAndStringRangeQuery?,
        val additionalFilter: ((BbbUtils.BlockMetricWithTimeAndHeightWrapper) -> Boolean)?,
    ) : AssetMetricsEndpointImpl.AssetMetricsDataSourceSpecificData

    suspend fun checkParameters(
        request: GetTimeseriesAssetMetricsRequest,
        asset: String,
        pagingFromStart: Boolean,
    ): FunctionResult<ApiError, AssetMetricsEndpointImpl.AssetMetricsDataSourceSpecificData> {
        val assetId = Resources.getCurrencyInfo(asset)!!.id

        // cast 'time' range or 'hash' range to 'height' range
        val rangeQueryFunctionResult =
            if (request.startHash != null || request.endHash != null) {
                createRangeQueryFromHashBoundaries(db, asset, pagingFromStart, request)
            } else if (request.startTime != null || request.endTime != null) {
                createRangeQueryFromTimeBoundaries(db, assetId, pagingFromStart, request)
            } else {
                createRangeQueryFromHeightBoundaries(request, pagingFromStart)
            }

        val (rangeQuery, additionalFilter) = rangeQueryFunctionResult.getOrElse { return FunctionResult.Failure(it) }

        return BbbNewNetworkMetricsDataSourceSpecificData(rangeQuery, additionalFilter).toSuccess()
    }

    fun query(
        asset: String,
        patternRequested: Boolean,
        bufferSize: Int,
        pageToken: String?,
        metrics: List<String>,
        pagingFromStart: Boolean,
        rangeQuery: RangeQuery.BigIntegerAndStringRangeQuery?,
        additionalFilter: ((BbbUtils.BlockMetricWithTimeAndHeightWrapper) -> Boolean)?,
    ): SuspendableStream<BbbUtils.BlockMetricWithTimeAndHeightWrapper, PageToken.BigIntegerAndStringPageToken> {
        val initialState =
            pageToken?.let {
                try {
                    PageToken.BigIntegerAndStringPageToken.parse(
                        pageToken = it,
                        strTransformer = { value ->
                            val decodedHash = Codec.decodeHash(asset, value)
                            DataUtils.convertByteArrayToSqlParamWithoutQuotes(decodedHash)
                        },
                    )
                } catch (_: Exception) {
                    error("Can't parse provided next_page_token '$it'.")
                }
            }

        val assetId = Resources.getCurrencyInfo(asset)!!.id

        val rangeQueryWithPageToken = rangeQuery?.withPageToken(initialState)

        if (rangeQueryWithPageToken == null) {
            return SuspendableStream.empty()
        }

        val metricsInClauseSqlParams = metrics.joinToString { "'${SqlUtils.escapeSql(it)}'" }

        // For block by block all the metrics in the single table are committed at once with a single transaction
        // that's why we can rely on a metric that always exists for all bbb assets.
        val queryTextBuilder: QueryTextBuilder = { filter, limit ->
            val ordering = if (pagingFromStart) "ASC" else "DESC"
            """
            WITH blocks AS (
                SELECT 
                    DISTINCT block_height, block_hash 
                FROM $TABLE_NAME
                WHERE asset_id=$assetId AND metric='$BASE_METRIC_NAME' AND latest_revision=TRUE $filter
                ORDER BY block_height $ordering, block_hash $ordering
                LIMIT $limit
            )
            SELECT 
                $TABLE_NAME.*
            FROM blocks INNER JOIN $TABLE_NAME USING (block_height, block_hash)
            WHERE
                asset_id=$assetId
                AND metric IN ($metricsInClauseSqlParams)
                AND latest_revision=TRUE
            ORDER BY block_height $ordering, block_hash $ordering;
            """.trimIndent()
        }

        return DataUtils
            .createStream(
                db = db,
                queryTextBuilder = queryTextBuilder,
                bufferSize = bufferSize,
                keyNames = arrayOf("block_height", "block_hash"),
                dataMapper = { mapper(asset, it) },
                rangeQuery = rangeQueryWithPageToken,
                initialState = initialState,
                stateResolver = { PageToken.BigIntegerAndStringPageToken(it.height, it.hash) },
                streamId = if (patternRequested) asset else null,
            ).collapseByKey { it.height to it.hash }
            .map { (heightHashPair, singleBlockData) ->
                val (height, hash) = heightHashPair
                val firstMetric = singleBlockData.first()
                BbbUtils.BlockMetricWithTimeAndHeightWrapper(
                    height = height,
                    hash = hash,
                    parentHash = firstMetric.parentHash,
                    time = firstMetric.time,
                    metrics = singleBlockData.map { row -> row.metric to row.value },
                )
            }.let {
                if (additionalFilter != null) {
                    it.filter(additionalFilter)
                } else {
                    it
                }
            }
    }

    private suspend fun createRangeQueryFromHashBoundaries(
        db: Database,
        asset: String,
        pagingFromStart: Boolean,
        request: GetTimeseriesAssetMetricsRequest,
    ): FunctionResult<ApiError, Pair<RangeQuery.BigIntegerAndStringRangeQuery, (BbbUtils.BlockMetricWithTimeAndHeightWrapper) -> Boolean>> {
        val blockHashes = listOfNotNull(request.startHash, request.endHash)
        val blockHashesArrayAsSql =
            DataUtils.convertListOfBytesArraysToSqlCollection(
                BlockchainEndpointUtils.decodeBlockHashes(asset, blockHashes).getOrElse {
                    return FunctionResult.Failure(it)
                },
            )

        val assetId = Resources.getCurrencyInfo(asset)!!.id
        val heightMap: Map<String, BigInteger> =
            db.query(
                """
                SELECT block_hash, block_height 
                    FROM $TABLE_NAME 
                WHERE asset_id='$assetId' 
                    AND metric='$BASE_METRIC_NAME' 
                    AND block_hash IN ($blockHashesArrayAsSql)
                    AND latest_revision IS TRUE
                """.trimIndent(),
            ) {
                it
                    .map { rs ->
                        Codec.encodeHash(asset, rs.getBytes("block_hash")) to rs.getBigDecimal("block_height").toBigInteger()
                    }.toMap(HashMap())
            }

        val startHeight =
            request.startHash?.let {
                heightMap[it] ?: return FunctionResult.Failure(ApiError.BadParameter("start_hash", "Block with hash '$it' not found."))
            }

        val endHeight =
            request.endHash?.let {
                heightMap[it] ?: return FunctionResult.Failure(ApiError.BadParameter("end_hash", "Block with hash '$it' not found."))
            }

        return when (
            val result =
                DataUtils.createBigIntegerAndStringRangeQuery(
                    startHeight,
                    request.startInclusive,
                    endHeight,
                    request.endInclusive,
                    request.pageSize,
                    if (pagingFromStart) PagingFrom.START else PagingFrom.END,
                )
        ) {
            is FunctionResult.Success -> {
                val additionalFilter: (BbbUtils.BlockMetricWithTimeAndHeightWrapper) -> Boolean = { true }
                FunctionResult.Success(result.value to additionalFilter)
            }

            is FunctionResult.Failure -> FunctionResult.Failure(result.value)
        }
    }

    private suspend fun createRangeQueryFromTimeBoundaries(
        db: Database,
        assetId: Int,
        pagingFromStart: Boolean,
        request: GetTimeseriesAssetMetricsRequest,
    ): FunctionResult<
        ApiError,
        Pair<
            RangeQuery.BigIntegerAndStringRangeQuery?,
            (
                (
                    BbbUtils.BlockMetricWithTimeAndHeightWrapper,
                ) -> Boolean
            )?,
        >,
    > {
        val (startTime, endTime) =
            when (
                val result =
                    DataUtils.parseTimeParameters(
                        request.startTime,
                        request.startInclusive,
                        request.endTime,
                        request.endInclusive,
                        request.timezone,
                    )
            ) {
                is FunctionResult.Success -> result.value
                is FunctionResult.Failure -> return FunctionResult.Failure(result.value)
            }

        val timeFilter =
            DataUtils.createTimeFilter(
                startTime,
                request.startInclusive,
                endTime,
                request.endInclusive,
            )

        val additionalFilter: (BbbUtils.BlockMetricWithTimeAndHeightWrapper) -> Boolean = {
            timeFilter.invoke(it.time)
        }

        var startInclusive = request.startInclusive
        var endInclusive = request.endInclusive

        val startTimeSqlExpression = TimeUtils.toSqlCompareExpression(">=", startTime)
        val startHeight =
            db
                .query(
                    """
                SELECT block_time, block_height
                    FROM $TABLE_NAME
                WHERE asset_id='$assetId' AND block_time $startTimeSqlExpression
                ORDER BY block_time
                LIMIT 1;
            """,
                ) { it.map { rs -> getBlockTimeAndHeight(rs) }.firstOrNull() }
                ?.let { (time, height) ->
                    if (time > startTime) startInclusive = true
                    // expand range to catch bad miners timestamps
                    height - BigInteger.TWO
                } ?: run {
                // no matched rows
                return FunctionResult.Success(null to null)
            }

        val endTimeSqlExpression = TimeUtils.toSqlCompareExpression("<=", endTime)
        val endHeight =
            db
                .query(
                    """
                SELECT block_time, block_height
                    FROM $TABLE_NAME
                WHERE asset_id='$assetId' AND block_time $endTimeSqlExpression
                ORDER BY block_time DESC
                LIMIT 1;
            """,
                ) { it.map { rs -> getBlockTimeAndHeight(rs) }.firstOrNull() }
                ?.let { (time, height) ->
                    if (time < endTime) endInclusive = true
                    // expand range to catch bad miners timestamps
                    height + BigInteger.TWO
                } ?: run {
                // no matched rows
                return FunctionResult.Success(null to null)
            }

        return when (
            val result =
                DataUtils.createBigIntegerAndStringRangeQuery(
                    startHeight,
                    startInclusive,
                    endHeight,
                    endInclusive,
                    request.pageSize,
                    if (pagingFromStart) PagingFrom.START else PagingFrom.END,
                )
        ) {
            is FunctionResult.Success -> {
                FunctionResult.Success(result.value to additionalFilter)
            }

            is FunctionResult.Failure -> FunctionResult.Failure(result.value)
        }
    }

    private fun getBlockTimeAndHeight(rs: ResultSet): Pair<Instant, BigInteger> =
        rs.getTimestamp("block_time").toInstant() to rs.getBigDecimal("block_height").toBigInteger()

    private fun createRangeQueryFromHeightBoundaries(
        request: GetTimeseriesAssetMetricsRequest,
        pagingFromStart: Boolean,
    ): FunctionResult<ApiError, Pair<RangeQuery.BigIntegerAndStringRangeQuery, (BbbUtils.BlockMetricWithTimeAndHeightWrapper) -> Boolean>> {
        val startHeight = request.startHeight?.toBigInteger() ?: BigInteger.ZERO
        val endHeight = request.endHeight?.toBigInteger()

        return when (
            val result =
                DataUtils.createBigIntegerAndStringRangeQuery(
                    startBigInteger = startHeight,
                    startInclusive = request.startInclusive,
                    endBigInteger = endHeight,
                    endInclusive = request.endInclusive,
                    pageSize = request.pageSize,
                    pagingFrom = if (pagingFromStart) PagingFrom.START else PagingFrom.END,
                )
        ) {
            is FunctionResult.Success -> {
                val additionalFilter: (BbbUtils.BlockMetricWithTimeAndHeightWrapper) -> Boolean = { true }
                FunctionResult.Success(result.value to additionalFilter)
            }

            is FunctionResult.Failure -> FunctionResult.Failure(result.value)
        }
    }

    private fun mapper(
        asset: String,
        rs: ResultSet,
    ) = BbbUtils.BlockMetricRow(
        hash = Codec.encodeHash(asset, rs.getBytes("block_hash")),
        time = rs.getTimestamp("block_time").toInstant(),
        parentHash = rs.getBytes("block_parent_hash")?.let { Codec.encodeHash(asset, it) },
        height = rs.getBigDecimal("block_height").toBigInteger(),
        metric = rs.getString("metric"),
        value = rs.getBigDecimal("value")?.let { CommonUtils.formatBigDecimal(it) },
        status = null,
        statusTime = null,
    )
}
