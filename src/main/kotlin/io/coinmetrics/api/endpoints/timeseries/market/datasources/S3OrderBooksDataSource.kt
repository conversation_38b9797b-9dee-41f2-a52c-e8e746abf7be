package io.coinmetrics.api.endpoints.timeseries.market.datasources

import io.coinmetrics.api.model.ParsedMarket
import io.coinmetrics.api.models.PagingFrom
import io.coinmetrics.api.service.MarketStatisticsService
import io.coinmetrics.api.service.MemoryLimitService
import io.coinmetrics.api.statistics.market.SpotMetadataStatistics
import io.coinmetrics.api.utils.RangeQuery
import io.coinmetrics.s3databases.Batch
import io.coinmetrics.s3databases.S3Databases
import io.coinmetrics.s3databases.read.Reader
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emptyFlow
import kotlinx.coroutines.flow.map
import org.slf4j.LoggerFactory
import java.time.Instant
import java.time.Period

/**
 * We store data in .json.gz files.
 * Each file's content has a size of 64 MB or less. The size is denoted in the file name:
 * <start_time>_<end_time>_<uncompressed_size>.json.gz
 *
 * This class has only two public methods "read" and "close".
 *
 * We keep only a limited number of data in memory and use file uncompressed sizes to control/limit memory usage.
 * The "read" requests exceeding memory limits are queued up per key (API key or IP address).
 * See MemoryLimitService.kt for details.
 */
class S3OrderBooksDataSource(
    private val s3Databases: S3Databases?,
    private val marketStatisticsService: MarketStatisticsService,
    private val memoryLimitService: MemoryLimitService,
    private val spotMetadataStatistics: SpotMetadataStatistics,
) {
    companion object {
        private val log = LoggerFactory.getLogger(S3OrderBooksDataSource::class.java)

        private fun Flow<Batch<Reader.TimedByteArray>>.prependMarketField(market: String): Flow<Batch<Reader.TimedByteArray>> {
            val bytesToPrepend =
                """
                {"market":"$market",
                """.trimIndent().encodeToByteArray()
            return map { batch ->
                // todo: parallelize if performance is not enough
                batch.map {
                    val newBytes = ByteArray(it.bytes.size + bytesToPrepend.size - 1)
                    // concatenation: "<bytesToPrepend><bookBytesWoFirstByte>"
                    System.arraycopy(bytesToPrepend, 0, newBytes, 0, bytesToPrepend.size)
                    System.arraycopy(it.bytes, 1, newBytes, bytesToPrepend.size, it.bytes.size - 1)
                    it.withBytes(newBytes)
                }
            }
        }
    }

    private val depthLimiter = DepthLimiter()

    /**
     * @param key API key/IP address to honor parallel processing limits.
     * @param apiKey API key for monitoring only.
     */
    fun read(
        bucketName: String,
        statDepth: Int,
        parsedMarket: ParsedMarket,
        rangeQuery: RangeQuery.TimeRangeQuery,
        enforcedDepth: Int?,
        key: String,
        apiKey: String,
    ): Flow<Batch<Reader.TimedByteArray>> {
        if (s3Databases == null) return emptyFlow()

        val marketSymbol = getMarketSymbol(parsedMarket) ?: return emptyFlow()

        val (statStartTime, statEndTime) =
            run {
                val marketStat =
                    marketStatisticsService.getTieredS3BooksStatisticsForCatalogV2()[statDepth]?.get(parsedMarket.toString())
                // ignore market with no data
                val statStartTime = marketStat?.minTime?.let { Instant.parse(it) } ?: return emptyFlow()
                val statEndTime = marketStat.maxTime.let { Instant.parse(it) }
                statStartTime to statEndTime
            }

        val effectiveStartTime = maxOf(rangeQuery.startKey, statStartTime)
        val effectiveStartTimeInclusive =
            if (effectiveStartTime == rangeQuery.startKey) {
                rangeQuery.startInclusive
            } else {
                true
            }

        // The following logic is needed not to depend on possibly delayed statistic calculation
        // and return fresh data in time series endpoints.
        // We rely on statEndTime to calculate effectiveEndTime only if it has not being updated for N days.
        val statEndTimeAgeToBeUsedFrom = Period.ofDays(7)
        val now = Instant.now()
        val effectiveEndTime =
            if (statEndTime < now - statEndTimeAgeToBeUsedFrom) {
                minOf(rangeQuery.endKey, statEndTime)
            } else {
                rangeQuery.endKey
            }
        val effectiveEndTimeInclusive =
            if (effectiveEndTime == rangeQuery.endKey) {
                rangeQuery.endInclusive
            } else {
                true
            }

        if (effectiveStartTime > effectiveEndTime) return emptyFlow()

        val reader =
            s3Databases.reader<Reader.TimedByteArray>(
                S3Databases.ReadConfig(
                    memoryLimiterFun = { size, callback ->
                        memoryLimitService.execute(key, apiKey, size, callback)
                    },
                    // re-use the current processing dispatcher
                    dispatcher = Dispatchers.Unconfined,
                    onCorruptedFile = { _, _, exception ->
                        log.warn("A corrupted file was skipped.", exception)
                    },
                ),
            )

        return reader
            .read(
                partitionKey = S3Databases.PartitionKey(bucketName, listOf(parsedMarket.exchange, marketSymbol)),
                startTime = effectiveStartTime,
                endTime = effectiveEndTime,
                startTimeInclusive = effectiveStartTimeInclusive,
                endTimeInclusive = effectiveEndTimeInclusive,
                readFromStart = rangeQuery.pagingFrom == PagingFrom.START,
            ).limitDepthIfNeeded(enforcedDepth)
            .prependMarketField(parsedMarket.toString())
    }

    private fun Flow<Batch<Reader.TimedByteArray>>.limitDepthIfNeeded(enforcedDepth: Int?): Flow<Batch<Reader.TimedByteArray>> =
        if (enforcedDepth == null) {
            this
        } else {
            map { batch ->
                // todo: parallelize if performance is not enough
                batch.map {
                    val newBytes = depthLimiter.limit(it.bytes, enforcedDepth)
                    if (newBytes.size == it.bytes.size) {
                        it
                    } else {
                        it.withBytes(newBytes)
                    }
                }
            }
        }

    private fun getMarketSymbol(parsedMarket: ParsedMarket): String? {
        return when (parsedMarket) {
            is ParsedMarket.ParsedSpotMarket -> spotMetadataStatistics.get(parsedMarket.toString())?.symbol ?: return null
            is ParsedMarket.ParsedDerivativesMarket -> parsedMarket.symbol
            is ParsedMarket.ParsedDefiMarket -> error("Unsupported market type: $parsedMarket")
        }
    }
}
