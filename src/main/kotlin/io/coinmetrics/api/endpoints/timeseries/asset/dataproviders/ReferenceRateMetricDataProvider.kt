package io.coinmetrics.api.endpoints.timeseries.asset.dataproviders

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.endpoints.GetTimeseriesAssetMetricsRequest
import io.coinmetrics.api.endpoints.timeseries.asset.AssetMetricsEndpointHelper
import io.coinmetrics.api.endpoints.timeseries.asset.AssetMetricsEndpointImpl
import io.coinmetrics.api.endpoints.timeseries.asset.datasources.MetricDataUtils
import io.coinmetrics.api.endpoints.timeseries.asset.datasources.ReferenceRatesDataSource
import io.coinmetrics.api.models.PagingFrom
import io.coinmetrics.api.utils.TimeUtils
import io.coinmetrics.api.utils.paging.PageToken
import io.coinmetrics.api.utils.streams.SuspendableStream
import java.time.Clock
import java.time.Instant

class ReferenceRateMetricDataProvider(
    private val referenceRatesDataSource: ReferenceRatesDataSource,
    private val clock: Clock,
) : AssetMetricDataProvider<MetricDataUtils.DataWithTimeWrapper> {
    override fun isApplicable(
        metrics: List<String>,
        frequency: AssetMetricsEndpointHelper.Frequency,
        dataSourceGroup: AssetMetricsEndpointHelper.DataSourceGroup,
    ): Boolean = metrics.any { it.startsWith("ReferenceRate") }

    override suspend fun checkParameters(
        asset: String,
        metrics: List<String>,
    ): FunctionResult<ApiError, AssetMetricsEndpointImpl.AssetMetricsDataSourceSpecificData> = referenceRatesDataSource.checkParameters()

    override fun provide(
        asset: String,
        patternRequested: Boolean,
        metrics: List<String>,
        startTime: Instant,
        endTime: Instant,
        pagingFrom: PagingFrom,
        request: GetTimeseriesAssetMetricsRequest,
        pageTokenForDataSource: String?,
        frequencyOffset: TimeUtils.NormalizedFrequencyOffset,
        frequency: AssetMetricsEndpointHelper.Frequency,
        bufferSize: Int,
        communityApiKey: String,
        dataSourceSpecificData: AssetMetricsEndpointImpl.AssetMetricsDataSourceSpecificData?,
    ): SuspendableStream<MetricDataUtils.DataWithTimeWrapper, PageToken.TimePageToken> {
        check(metrics.size == 1)
        val metric = metrics.first()

        return referenceRatesDataSource.query(
            asset,
            patternRequested,
            bufferSize,
            pageTokenForDataSource,
            frequency.value,
            frequencyOffset,
            startTime,
            endTime,
            pagingFrom,
            request,
            metric,
            MetricDataUtils.enforcedStartTime(
                clock = clock,
                apiKey = request.apiKey,
                communityApiKey = communityApiKey,
                /**
                 * MD-3542
                 * Expand community permission to ReferenceRate* metrics served through /timeseries/asset-metrics, all frequencies, to have 7 days of history (currently it is 1 day).
                 */
                delayDays = 7,
            ),
        )
    }
}
