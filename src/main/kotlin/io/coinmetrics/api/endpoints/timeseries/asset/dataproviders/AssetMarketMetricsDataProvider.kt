package io.coinmetrics.api.endpoints.timeseries.asset.dataproviders

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.endpoints.GetTimeseriesAssetMetricsRequest
import io.coinmetrics.api.endpoints.timeseries.asset.AssetMetricsEndpointHelper
import io.coinmetrics.api.endpoints.timeseries.asset.AssetMetricsEndpointHelper.DataSourceGroup
import io.coinmetrics.api.endpoints.timeseries.asset.AssetMetricsEndpointImpl
import io.coinmetrics.api.endpoints.timeseries.asset.datasources.MarketMetricsDataSource
import io.coinmetrics.api.models.PagingFrom
import io.coinmetrics.api.utils.MetricUtils.MetricsWithTimeWrapper
import io.coinmetrics.api.utils.TimeUtils
import io.coinmetrics.api.utils.paging.PageToken
import io.coinmetrics.api.utils.streams.SuspendableStream
import java.time.Instant

class AssetMarketMetricsDataProvider(
    private val marketMetricsDataSource: MarketMetricsDataSource,
) : AssetMetricDataProvider<MetricsWithTimeWrapper> {
    companion object {
        val applicableFrequencies =
            hashSetOf(
                AssetMetricsEndpointHelper.Frequency.FIVE_MINUTES,
                AssetMetricsEndpointHelper.Frequency.ONE_HOUR,
                AssetMetricsEndpointHelper.Frequency.ONE_DAY,
            )
    }

    override fun isApplicable(
        metrics: List<String>,
        frequency: AssetMetricsEndpointHelper.Frequency,
        dataSourceGroup: DataSourceGroup,
    ): Boolean =
        frequency in applicableFrequencies &&
            dataSourceGroup == DataSourceGroup.ASSET_MARKET_METRICS

    override suspend fun checkParameters(
        asset: String,
        metrics: List<String>,
    ): FunctionResult<ApiError, AssetMetricsEndpointImpl.AssetMetricsDataSourceSpecificData> = marketMetricsDataSource.checkParameters()

    override fun provide(
        asset: String,
        patternRequested: Boolean,
        metrics: List<String>,
        startTime: Instant,
        endTime: Instant,
        pagingFrom: PagingFrom,
        request: GetTimeseriesAssetMetricsRequest,
        pageTokenForDataSource: String?,
        frequencyOffset: TimeUtils.NormalizedFrequencyOffset,
        frequency: AssetMetricsEndpointHelper.Frequency,
        bufferSize: Int,
        communityApiKey: String,
        dataSourceSpecificData: AssetMetricsEndpointImpl.AssetMetricsDataSourceSpecificData?,
    ): SuspendableStream<MetricsWithTimeWrapper, PageToken.TimePageToken> =
        marketMetricsDataSource.query(
            tableName = "asset_metrics",
            asset,
            patternRequested,
            bufferSize,
            pageTokenForDataSource,
            frequency.value,
            metrics,
            startTime,
            endTime,
            pagingFrom,
            request,
        )
}
