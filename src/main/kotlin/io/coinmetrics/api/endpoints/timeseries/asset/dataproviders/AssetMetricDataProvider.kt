package io.coinmetrics.api.endpoints.timeseries.asset.dataproviders

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.endpoints.GetTimeseriesAssetMetricsRequest
import io.coinmetrics.api.endpoints.timeseries.asset.AssetMetricsEndpointHelper
import io.coinmetrics.api.endpoints.timeseries.asset.AssetMetricsEndpointImpl
import io.coinmetrics.api.utils.TimeUtils
import io.coinmetrics.api.utils.WithTime
import io.coinmetrics.api.utils.paging.PageToken
import io.coinmetrics.api.utils.streams.SuspendableStream
import java.time.Instant

/**
 * Asset metric data provider is aimed to obtain data from specific data store.
 * Each data provider should implement isApplicable method to identify whether it should be used to provide data or not.
 */
interface AssetMetricDataProvider<T : WithTime> {
    fun isApplicable(
        metrics: List<String>,
        frequency: AssetMetricsEndpointHelper.Frequency,
        dataSourceGroup: AssetMetricsEndpointHelper.DataSourceGroup,
    ): Boolean

    suspend fun checkParameters(
        asset: String,
        metrics: List<String>,
    ): FunctionResult<ApiError, AssetMetricsEndpointImpl.AssetMetricsDataSourceSpecificData>

    fun provide(
        asset: String,
        patternRequested: Boolean,
        metrics: List<String>,
        startTime: Instant,
        endTime: Instant,
        pagingFromStart: Boolean,
        request: GetTimeseriesAssetMetricsRequest,
        pageTokenForDataSource: String?,
        frequencyOffset: TimeUtils.NormalizedFrequencyOffset,
        frequency: AssetMetricsEndpointHelper.Frequency,
        bufferSize: Int,
        communityApiKey: String,
        dataSourceSpecificData: AssetMetricsEndpointImpl.AssetMetricsDataSourceSpecificData?,
    ): SuspendableStream<T, PageToken.TimePageToken>
}
