package io.coinmetrics.api.endpoints.timeseries.asset.datasources

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.endpoints.GetTimeseriesAssetMetricsRequest
import io.coinmetrics.api.endpoints.timeseries.asset.AssetMetricsEndpointHelper
import io.coinmetrics.api.endpoints.timeseries.asset.AssetMetricsEndpointImpl
import io.coinmetrics.api.models.PagingFrom
import io.coinmetrics.api.toSuccess
import io.coinmetrics.api.utils.DataUtils
import io.coinmetrics.api.utils.QueryTextBuilder
import io.coinmetrics.api.utils.RangeQuery
import io.coinmetrics.api.utils.SqlUtils
import io.coinmetrics.api.utils.paging.PageToken
import io.coinmetrics.api.utils.streams.SuspendableStream
import io.coinmetrics.api.utils.toSqlOrdering
import io.coinmetrics.databases.Database

class BbbMetricsDataSource(
    val db: Database,
    val dataSource: AssetMetricsEndpointHelper.DataSourceGroup,
) {
    internal data class BbbMetricsDataSourceSpecificData(
        val rangeQuery: RangeQuery.BigIntegerAndStringRangeQuery?,
        val additionalFilter: ((BbbUtils.BlockMetricWithTimeAndHeightWrapper) -> Boolean)?,
    ) : AssetMetricsEndpointImpl.AssetMetricsDataSourceSpecificData

    suspend fun checkParameters(
        request: GetTimeseriesAssetMetricsRequest,
        asset: String,
        metrics: List<String>,
        pagingFrom: PagingFrom,
    ): FunctionResult<ApiError, AssetMetricsEndpointImpl.AssetMetricsDataSourceSpecificData> {
        // cast 'time' range or 'hash' range to 'height' range
        val rangeQueryFunctionResult =
            if (request.startHash != null || request.endHash != null) {
                return FunctionResult.Failure(
                    ApiError.BadParameters(
                        "Metrics $metrics don't support 'start_hash' and 'end_hash' filtering.",
                    ),
                )
            } else if (request.startTime != null || request.endTime != null) {
                ChainMonitorUtils.createRangeQueryFromTimeBoundaries(db, asset, pagingFrom, request, dataSource)
            } else {
                BbbUtils.createRangeQueryFromHeightBoundaries(pagingFrom, request)
            }

        val (rangeQuery, additionalFilter) = rangeQueryFunctionResult.getOrElse { return FunctionResult.Failure(it) }

        return BbbMetricsDataSourceSpecificData(rangeQuery, additionalFilter).toSuccess()
    }

    fun query(
        asset: String,
        patternRequested: Boolean,
        bufferSize: Int,
        pageToken: String?,
        metrics: List<String>,
        pagingFrom: PagingFrom,
        rangeQuery: RangeQuery.BigIntegerAndStringRangeQuery?,
        additionalFilter: ((BbbUtils.BlockMetricWithTimeAndHeightWrapper) -> Boolean)?,
    ): SuspendableStream<BbbUtils.BlockMetricWithTimeAndHeightWrapper, PageToken.BigIntegerAndStringPageToken> {
        val tableName =
            when (dataSource) {
                AssetMetricsEndpointHelper.DataSourceGroup.BBB_CHAIN_MONITOR_METRICS ->
                    "${db.config.schema}.${asset}_bbb_metrics"

                AssetMetricsEndpointHelper.DataSourceGroup.BBB_ETH_SC_METRICS ->
                    "${db.config.schema}.${asset}_sc_metrics"

                else ->
                    throw IllegalStateException("Unsupported data source '$dataSource' used as BBB metrics data source.")
            }

        val initialState =
            pageToken?.let {
                try {
                    PageToken.BigIntegerAndStringPageToken.parse(it)
                } catch (_: Exception) {
                    error("Can't parse provided next_page_token '$it'.")
                }
            }

        val rangeQueryWithPageToken = rangeQuery?.withPageToken(initialState)

        if (rangeQueryWithPageToken == null) {
            return SuspendableStream.empty()
        }

        val metricsInClauseSqlParams = metrics.joinToString { "'${SqlUtils.escapeSql(it)}'" }

        val queryTextBuilder: QueryTextBuilder = { filter, limit ->
            val ordering = pagingFrom.toSqlOrdering()
            """
                WITH heights AS (
                    SELECT DISTINCT ON (block_height) 
                        block_height, 
                        name 
                    FROM $tableName
                    WHERE name IN ($metricsInClauseSqlParams) $filter
                    ORDER BY block_height $ordering
                    LIMIT $limit
                ),
                blocks AS (
                    SELECT DISTINCT 
                        block_height, 
                        block_hash 
                    FROM heights 
                    JOIN $tableName USING (block_height, name)
                    WHERE name IN ($metricsInClauseSqlParams) $filter
                    ORDER BY block_height $ordering, block_hash $ordering
                    LIMIT $limit
                )
                SELECT 
                    block_height, 
                    name, 
                    value, 
                    ENCODE(block_hash, 'hex') AS block_hash, 
                    ENCODE(parent_block_hash, 'hex') AS parent_block_hash, 
                    time
                FROM blocks INNER JOIN $tableName USING (block_height, block_hash) 
                WHERE
                    name IN ($metricsInClauseSqlParams)
                ORDER BY block_height $ordering, block_hash $ordering
            """
        }

        return DataUtils
            .createStream(
                db = db,
                queryTextBuilder = queryTextBuilder,
                bufferSize = bufferSize,
                keyNames = arrayOf("block_height", "ENCODE(block_hash, 'hex')"),
                dataMapper = ChainMonitorUtils.bbbMetricsBlocksMapper,
                rangeQuery = rangeQueryWithPageToken,
                initialState = initialState,
                stateResolver = { PageToken.BigIntegerAndStringPageToken(it.height, it.hash) },
                streamId = if (patternRequested) asset else null,
            ).collapseByKey { it.height to it.hash }
            .map { (heightHashPair, singleBlockData) ->
                val (height, hash) = heightHashPair
                val firstMetric = singleBlockData.first()
                BbbUtils.BlockMetricWithTimeAndHeightWrapper(
                    height = height,
                    hash = hash,
                    parentHash = firstMetric.parentHash,
                    time = firstMetric.time,
                    // Map<metricName, metricValue>
                    metrics = singleBlockData.map { row -> row.metric to row.value },
                )
            }.let {
                if (additionalFilter != null) {
                    it.filter(additionalFilter)
                } else {
                    it
                }
            }
    }
}
