package io.coinmetrics.api.endpoints.timeseries.mempool

import io.coinmetrics.api.ChunkedResponseFormat
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.Response
import io.coinmetrics.api.endpoints.GetMempoolFeeratesEndpoint
import io.coinmetrics.api.endpoints.GetMempoolFeeratesRequest
import io.coinmetrics.api.models.MempoolFeerate
import io.coinmetrics.api.models.MempoolFeerateBand
import io.coinmetrics.api.models.MempoolFeeratesResponse
import io.coinmetrics.api.models.PagingFrom
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.getTimeseriesRateLimitHeaders
import io.coinmetrics.api.utils.BatchUtils
import io.coinmetrics.api.utils.CommonUtils
import io.coinmetrics.api.utils.DataUtils
import io.coinmetrics.api.utils.QueryTextBuilder
import io.coinmetrics.api.utils.RangeQuery
import io.coinmetrics.api.utils.TimeUtils
import io.coinmetrics.api.utils.Utils
import io.coinmetrics.api.utils.paging.PageToken
import io.coinmetrics.api.utils.paging.getPageFlow
import io.coinmetrics.api.utils.streams.SuspendableStream
import io.coinmetrics.databases.Database
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.flow.asFlow
import kotlinx.coroutines.flow.flatMapConcat
import kotlinx.coroutines.flow.flowOf
import java.math.BigDecimal
import java.sql.ResultSet
import java.time.Instant

class MempoolFeeratesEndpointImpl(
    private val db: Database,
    private val amsService: AmsService,
) : GetMempoolFeeratesEndpoint() {
    private val mapper = { rs: ResultSet ->
        val feerates = Utils.extractArray<Long>(rs, "feerate")
        val counts = Utils.extractArray<Int>(rs, "count")
        val consensusSize = Utils.extractArray<Long>(rs, "consensus_size")
        val physicalSize = Utils.extractArray<Long>(rs, "physical_size")
        val fees = Utils.extractArray<BigDecimal>(rs, "fees")

        Feerate(
            time = rs.getTimestamp("time").toInstant(),
            feerateBands =
                feerates.mapIndexed { index, feerate ->
                    Feerate.FeerateBand(
                        feerate = feerate,
                        count = counts[index],
                        consensusSize = consensusSize[index],
                        physicalSize = physicalSize.getOrNull(index),
                        fees = fees[index],
                    )
                },
        )
    }

    override suspend fun handle(request: GetMempoolFeeratesRequest): Response<MempoolFeeratesResponse> {
        val headers =
            amsService
                .getTimeseriesRateLimitHeaders(
                    apiKey = request.apiKey,
                    httpRequest = request.httpRequest,
                ).getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }

        val checkResults =
            coroutineScope {
                request.assets
                    .distinct()
                    .map { asset ->
                        async {
                            amsService
                                .check(
                                    apiKey = request.apiKey,
                                    resource = "mempool_feerates",
                                    parameters = hashMapOf("asset" to asset),
                                ) { "asset" }
                        }
                    }.awaitAll()
            }

        for (checkResult in checkResults) {
            checkResult.getOrElse { (e) -> return Response.errorResponse(e) }
        }

        val (startTime, endTime) =
            DataUtils
                .parseTimeParameters(
                    request.startTime,
                    request.startInclusive,
                    request.endTime,
                    request.endInclusive,
                    request.timezone,
                ).getOrElse { return Response.errorResponse(it) }

        return when (
            val result =
                BatchUtils.sortIdsAndConcatStreams(
                    streams = request.assets.asSequence().map { it to Unit },
                    streamIdsAreResolvedDynamically = false,
                    pagingFrom = request.pagingFrom,
                    nextPageToken = request.nextPageToken,
                    initialStreamStateParser = { PageToken.TimePageToken.parse(it) },
                    streamSupplier = { id, state, _ ->
                        val rangeQuery =
                            RangeQuery
                                .TimeRangeQuery(
                                    startTime,
                                    request.startInclusive,
                                    endTime,
                                    request.endInclusive,
                                    request.pagingFrom,
                                ).withPageToken(state)

                        handleInternal(id, state, request, rangeQuery)
                    },
                    httpRequestCoroutineContext = request.httpRequest.coroutineContext,
                    logger = log,
                )
        ) {
            is FunctionResult.Success -> {
                val page =
                    result.value.getPageFlow(request.httpRequest, request.pageSize, request.pagingFrom).let {
                        if (request.format == "csv") {
                            it.flatMapConcat { obj ->
                                if (obj is MempoolFeerate) {
                                    obj.feerates
                                        .map { feeRate ->
                                            obj.toMap(feeRate)
                                        }.asFlow()
                                } else {
                                    flowOf(obj)
                                }
                            }
                        } else {
                            it
                        }
                    }

                Response.chunkedResponse(
                    items = page,
                    headers = headers,
                    format = if (request.format == "csv") ChunkedResponseFormat.Csv(nullValue = "") else ChunkedResponseFormat.Json(),
                )
            }

            is FunctionResult.Failure -> Response.errorResponse(result.value, headers)
        }
    }

    private fun MempoolFeerate.toMap(band: MempoolFeerateBand) =
        mapOf(
            "asset" to asset,
            "time" to time,
            "feerate" to band.feerate,
            "count" to band.count,
            "consensus_size" to band.consensusSize,
            "fees" to band.fees,
            "physical_size" to band.physicalSize,
        )

    private fun handleInternal(
        asset: String,
        initialState: PageToken.TimePageToken?,
        request: GetMempoolFeeratesRequest,
        rangeQuery: RangeQuery.TimeRangeQuery,
    ): SuspendableStream<MempoolFeerate, PageToken.TimePageToken> {
        val tableName = "${db.config.schema}.${asset}_mempool_feerates"

        val timeFilter = DataUtils.createTimeFilter(rangeQuery)
        val additionalFilter: (Feerate) -> Boolean = {
            timeFilter.invoke(it.time)
        }

        val queryTextBuilder: QueryTextBuilder = { filter, limit ->
            val ordering = if (request.pagingFrom == PagingFrom.START) "ASC" else "DESC"
            """
                SELECT
                    time, feerate, count, consensus_size, physical_size, fees
                FROM $tableName
                WHERE TRUE $filter
                ORDER BY time $ordering
                LIMIT $limit
            """
        }

        val stream =
            DataUtils
                .createStream(
                    db = db,
                    queryTextBuilder = queryTextBuilder,
                    bufferSize = request.pageSize + 1,
                    keyNames = arrayOf("time"),
                    dataMapper = mapper,
                    rangeQuery = rangeQuery,
                    initialState = initialState,
                    stateResolver = { PageToken.TimePageToken(it.time) },
                    streamId = null,
                ).filter(additionalFilter)
                .map { feerate ->
                    MempoolFeerate(
                        asset = asset,
                        time = TimeUtils.dateTimeFormatter.format(feerate.time),
                        feerates =
                            feerate.feerateBands.map { feerateBand ->
                                MempoolFeerateBand(
                                    feerate = feerateBand.feerate.toString(),
                                    count = feerateBand.count.toString(),
                                    consensusSize = feerateBand.consensusSize.toString(),
                                    physicalSize = feerateBand.physicalSize?.toString(),
                                    fees = CommonUtils.formatBigDecimal(feerateBand.fees),
                                )
                            },
                    )
                }

        return stream
    }

    private class Feerate(
        val time: Instant,
        val feerateBands: List<FeerateBand>,
    ) {
        class FeerateBand(
            val feerate: Long,
            val count: Int,
            val consensusSize: Long,
            val physicalSize: Long?,
            val fees: BigDecimal,
        )
    }
}
