package io.coinmetrics.api.endpoints.timeseries.market.datasources

import io.coinmetrics.api.endpoints.timeseries.market.datasources.MarketCandlesUtil.TIME_COLUMN_NAME
import io.coinmetrics.api.endpoints.timeseries.market.model.MarketCandlesWrapper
import io.coinmetrics.api.model.DerivativesMarketType
import io.coinmetrics.api.model.NormalizedMarket
import io.coinmetrics.api.utils.DataUtils
import io.coinmetrics.api.utils.QueryTextBuilder
import io.coinmetrics.api.utils.RangeQuery
import io.coinmetrics.api.utils.SqlUtils
import io.coinmetrics.api.utils.paging.PageToken
import io.coinmetrics.api.utils.streams.SuspendableStream
import io.coinmetrics.api.utils.toSqlOrdering
import io.coinmetrics.databases.Database

class MarketCandlesNonDelayedDataSource(
    private val db: Database,
) {
    private val schema = db.config.schema

    fun query(
        market: String,
        marketId: NormalizedMarket,
        marketPatternRequested: Boolean,
        frequency: String,
        rangeQuery: RangeQuery.TimeRangeQuery,
        pageToken: PageToken.TimePageToken?,
        bufferSize: Int,
    ): SuspendableStream<MarketCandlesWrapper, PageToken.TimePageToken> {
        /**
         * We don't support real-time Defi candles.
         */
        if (marketId is NormalizedMarket.DefiNormalizedMarket) {
            return SuspendableStream.empty()
        }
        /**
         * We don't support real-time Option candles.
         */
        if (marketId is NormalizedMarket.DerivativesNormalizedMarket && marketId.type == DerivativesMarketType.OPTION) {
            return SuspendableStream.empty()
        }

        val queryTextBuilder = queryBuilder(marketId, rangeQuery, frequency)
        val timeFilter = DataUtils.createTimeFilter(rangeQuery)
        val additionalFilter = { it: MarketCandlesWrapper -> timeFilter.invoke(it.time) }

        return DataUtils
            .createStream(
                db = db,
                queryTextBuilder = queryTextBuilder,
                bufferSize = bufferSize,
                keyNames = arrayOf(TIME_COLUMN_NAME),
                dataMapper = MarketCandlesUtil.createMapper(market),
                rangeQuery = rangeQuery,
                initialState = pageToken,
                stateResolver = { PageToken.TimePageToken(it.time) },
                streamId = if (marketPatternRequested) market else null,
                lbKey = market,
            ).filter(additionalFilter)
    }

    private fun queryBuilder(
        marketId: NormalizedMarket,
        rangeQuery: RangeQuery.TimeRangeQuery,
        frequency: String,
    ): QueryTextBuilder {
        val (tableName, instrumentsFilter) = queryDetails(marketId)
        val intervalSqlParam = MarketCandlesUtil.candleIntervalToMinutesMap.getValue(frequency)
        val exchangeId = marketId.exchange
        val ordering = rangeQuery.pagingFrom.toSqlOrdering()

        return { filter, limit ->
            """
            SELECT
                $TIME_COLUMN_NAME,
                candle_open_price::NUMERIC,
                candle_close_price::NUMERIC,
                candle_low_price::NUMERIC,
                candle_high_price::NUMERIC,
                candle_vwap::NUMERIC,
                candle_volume::NUMERIC,
                candle_usd_volume::NUMERIC,
                candle_trades_count::NUMERIC
            FROM $schema.$tableName
            WHERE 
                $instrumentsFilter
                $filter
                AND candle_exchange_id = $exchangeId
                AND candle_interval = $intervalSqlParam
            ORDER BY $TIME_COLUMN_NAME $ordering
            LIMIT $limit
            """.trimIndent()
        }
    }

    companion object {
        /**
         * @return table name and instruments filter.
         */
        fun queryDetails(marketId: NormalizedMarket): Pair<String, String> =
            when (marketId) {
                is NormalizedMarket.SpotNormalizedMarket ->
                    Pair(
                        "instant_candles_market_spot",
                        "candle_base_id=${marketId.base} AND candle_quote_id=${marketId.quote}",
                    )

                is NormalizedMarket.DerivativesNormalizedMarket -> {
                    val symbolSqlParam = SqlUtils.escapeSql(marketId.symbol)
                    Pair(
                        "instant_candles_market_futures",
                        "candle_symbol='$symbolSqlParam'",
                    )
                }

                else -> throw IllegalArgumentException("Unsupported market $marketId")
            }
    }
}
