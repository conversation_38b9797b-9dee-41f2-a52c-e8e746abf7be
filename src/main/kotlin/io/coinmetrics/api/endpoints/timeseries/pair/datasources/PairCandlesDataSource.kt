package io.coinmetrics.api.endpoints.timeseries.pair.datasources

import io.coinmetrics.api.CommonConstants
import io.coinmetrics.api.models.PairCandle
import io.coinmetrics.api.utils.CommonUtils
import io.coinmetrics.api.utils.DataUtils
import io.coinmetrics.api.utils.QueryTextBuilder
import io.coinmetrics.api.utils.RangeQuery.TimeRangeQuery
import io.coinmetrics.api.utils.TimeUtils
import io.coinmetrics.api.utils.paging.PageToken
import io.coinmetrics.api.utils.streams.SuspendableStream
import io.coinmetrics.databases.Database
import java.sql.ResultSet
import java.time.Instant

class PairCandlesDataSource(
    private val db: Database,
) {
    companion object {
        const val TIME_COLUMN_NAME = "candle_start_time"
        private const val HOURS_IN_DAY = 24
    }

    private val schema = db.config.schema

    fun query(
        pair: String,
        patternRequested: Boolean,
        frequency: String,
        frequencyOffset: TimeUtils.NormalizedFrequencyOffset,
        frequencyOffsetTimezone: String,
        rangeQuery: TimeRangeQuery,
        pageToken: PageToken.TimePageToken?,
        bufferSize: Int,
    ): SuspendableStream<PairCandleWrapper, SuspendableStream.State> {
        val tableSuffix = pair.replace("-", "_")
        val tableName = "${CommonConstants.RATE_CANDLE_TABLE_PREFIX}$tableSuffix"

        val formattedFrequency = CommonConstants.candleFrequenciesMap.getValue(frequency)
        val ordering = if (rangeQuery.pagingFromStart) "ASC" else "DESC"
        val queryTextBuilder: QueryTextBuilder =
            if (frequencyOffset.default) {
                defaultFrequencyOffsetQueryBuilder(tableName, formattedFrequency, ordering)
            } else {
                customFrequencyOffsetQueryBuilder(tableName, formattedFrequency, ordering, frequencyOffset.hours, frequencyOffsetTimezone)
            }

        val timeFilter = DataUtils.createTimeFilter(rangeQuery)
        return DataUtils
            .createStream(
                db = db,
                queryTextBuilder = queryTextBuilder,
                bufferSize = bufferSize,
                keyNames = arrayOf(TIME_COLUMN_NAME),
                dataMapper = createMapper(pair),
                rangeQuery = rangeQuery,
                initialState = pageToken,
                stateResolver = { PageToken.TimePageToken(it.time) },
                lbKey = pair,
                streamId = if (patternRequested) pair else null,
            ).filter { timeFilter.invoke(it.time) }
    }

    private fun defaultFrequencyOffsetQueryBuilder(
        tableName: String,
        formattedFrequency: String,
        ordering: String,
    ): QueryTextBuilder =
        { filter, limit ->
            """
            SELECT
                $TIME_COLUMN_NAME,
                candle_open_price::NUMERIC,
                candle_close_price::NUMERIC,
                candle_low_price::NUMERIC,
                candle_high_price::NUMERIC
            FROM $schema.$tableName
            WHERE 
                candle_duration = '$formattedFrequency'::interval
                $filter
            ORDER BY $TIME_COLUMN_NAME $ordering
            LIMIT $limit
            """.trimIndent()
        }

    private fun customFrequencyOffsetQueryBuilder(
        tableName: String,
        formattedFrequency: String,
        ordering: String,
        offsetHours: Int,
        offsetTimezone: String,
    ): QueryTextBuilder =
        { filter, limit ->
            """
            SELECT
                DATE_TRUNC('day', $TIME_COLUMN_NAME AT TIME ZONE 'utc' AT TIME ZONE '$offsetTimezone' - '$offsetHours hours'::INTERVAL),
                MIN($TIME_COLUMN_NAME)                                                                  AS $TIME_COLUMN_NAME,
                (MIN(ARRAY [EXTRACT(EPOCH FROM $TIME_COLUMN_NAME), candle_open_price]))[2]::NUMERIC     AS candle_open_price,
                (MAX(ARRAY [EXTRACT(EPOCH FROM $TIME_COLUMN_NAME), candle_close_price]))[2]::NUMERIC    AS candle_close_price,
                MIN(candle_low_price)::NUMERIC                                                          AS candle_low_price,
                MAX(candle_high_price)::NUMERIC                                                         AS candle_high_price
            FROM
                -- subquery is used to improve performance by limiting amount of 1h candles needed to be sorted/grouped when client specifies broad or open interval.
                (
                    SELECT
                        $TIME_COLUMN_NAME::timestamp,
                        candle_open_price::NUMERIC,
                        candle_close_price::NUMERIC,
                        candle_low_price::NUMERIC,
                        candle_high_price::NUMERIC
                    FROM $schema.$tableName
                    WHERE 
                        candle_duration = '$formattedFrequency'::interval
                        $filter
                    ORDER BY $TIME_COLUMN_NAME $ordering
                    -- for each day we need 24 1-hours candles + 24 to cover last candle in case if start of time interval != custom offset
                    LIMIT $limit * 24 + 24
                ) candles
            GROUP BY 1
            -- make sure we return only candles that were aggregated across the whole day and ignore "partial" candles that may be aggregated on the time interval ends.
            HAVING DATE_PART('hours', MIN($TIME_COLUMN_NAME) AT TIME ZONE 'utc' AT TIME ZONE '$offsetTimezone') = $offsetHours
               AND DATE_PART('hours', MAX($TIME_COLUMN_NAME) AT TIME ZONE 'utc' AT TIME ZONE '$offsetTimezone') = ${(if (offsetHours == 0) HOURS_IN_DAY else offsetHours) - 1}
            ORDER BY 1 $ordering
            LIMIT $limit;
            """.trimIndent()
        }

    private fun createMapper(pair: String): (ResultSet) -> PairCandleWrapper =
        { rs: ResultSet ->
            val time = rs.getTimestamp("candle_start_time").toInstant()
            val timeFormatted = TimeUtils.dateTimeFormatter.format(time)

            PairCandleWrapper(
                time,
                PairCandle(
                    time = timeFormatted,
                    pair = pair,
                    priceOpen = CommonUtils.formatBigDecimal(rs.getBigDecimal("candle_open_price")),
                    priceHigh = CommonUtils.formatBigDecimal(rs.getBigDecimal("candle_high_price")),
                    priceLow = CommonUtils.formatBigDecimal(rs.getBigDecimal("candle_low_price")),
                    priceClose = CommonUtils.formatBigDecimal(rs.getBigDecimal("candle_close_price")),
                ),
            )
        }

    class PairCandleWrapper(
        val time: Instant,
        val pairCandle: PairCandle,
    )
}
