package io.coinmetrics.api.endpoints.timeseries.asset

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.ApiError.UnsupportedParameterValue
import io.coinmetrics.api.ChunkedResponseFormat
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.Response
import io.coinmetrics.api.endpoints.GetTimeseriesAssetMetricsEndpoint
import io.coinmetrics.api.endpoints.GetTimeseriesAssetMetricsRequest
import io.coinmetrics.api.endpoints.timeseries.asset.AssetMetricsEndpointHelper.DataSourceGroup
import io.coinmetrics.api.endpoints.timeseries.asset.AssetMetricsEndpointHelper.Frequency
import io.coinmetrics.api.endpoints.timeseries.asset.AssetMetricsEndpointHelper.groupByDataSource
import io.coinmetrics.api.endpoints.timeseries.asset.dataproviders.AssetMarketMetricsDataProvider
import io.coinmetrics.api.endpoints.timeseries.asset.dataproviders.AssetMetricDataProvider
import io.coinmetrics.api.endpoints.timeseries.asset.dataproviders.DailyFlowMetricsDataProvider
import io.coinmetrics.api.endpoints.timeseries.asset.dataproviders.MinutelyChainMonitorMetricsDataProvider
import io.coinmetrics.api.endpoints.timeseries.asset.dataproviders.NetworkMetricsDataProvider
import io.coinmetrics.api.endpoints.timeseries.asset.dataproviders.PrincipalPriceMetricDataProvider
import io.coinmetrics.api.endpoints.timeseries.asset.dataproviders.RealizedVolatilityMarketsMetricsDataProvider
import io.coinmetrics.api.endpoints.timeseries.asset.dataproviders.ReferenceRateMetricDataProvider
import io.coinmetrics.api.endpoints.timeseries.asset.datasources.BbbFlowMetricsDataSource
import io.coinmetrics.api.endpoints.timeseries.asset.datasources.BbbMetricsDataSource
import io.coinmetrics.api.endpoints.timeseries.asset.datasources.BbbNetworkMetricsDataSource
import io.coinmetrics.api.endpoints.timeseries.asset.datasources.BbbNewNetworkMetricsDataSource
import io.coinmetrics.api.endpoints.timeseries.asset.datasources.DailyFlowMetricsDataSource
import io.coinmetrics.api.endpoints.timeseries.asset.datasources.MarketMetricsDataSource
import io.coinmetrics.api.endpoints.timeseries.asset.datasources.MetricDataUtils
import io.coinmetrics.api.endpoints.timeseries.asset.datasources.MinutelyChainMonitorMetricsDataSource
import io.coinmetrics.api.endpoints.timeseries.asset.datasources.NetworkMetricsDataSource
import io.coinmetrics.api.endpoints.timeseries.asset.datasources.PrincipalPriceDataSource
import io.coinmetrics.api.endpoints.timeseries.asset.datasources.ReferenceRatesDataSource
import io.coinmetrics.api.models.AssetMetricsResponse
import io.coinmetrics.api.models.PagingFrom
import io.coinmetrics.api.resources.Resources
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.MarketStatisticsService
import io.coinmetrics.api.service.catalog.metric.getFirstForbiddenError
import io.coinmetrics.api.service.catalog.metric.getFirstNonForbiddenError
import io.coinmetrics.api.service.catalog.metric.impl.AssetMetricsService
import io.coinmetrics.api.service.catalog.metric.selectErrorMetrics
import io.coinmetrics.api.service.catalog.metric.selectUnsupportedMetrics
import io.coinmetrics.api.service.getTimeseriesRateLimitHeaders
import io.coinmetrics.api.toFailure
import io.coinmetrics.api.toSuccess
import io.coinmetrics.api.utils.BatchUtils
import io.coinmetrics.api.utils.ComparablePair
import io.coinmetrics.api.utils.DataUtils
import io.coinmetrics.api.utils.MetricUtils
import io.coinmetrics.api.utils.TimeUtils
import io.coinmetrics.api.utils.Utils
import io.coinmetrics.api.utils.WithTime
import io.coinmetrics.api.utils.paging.getPageFlow
import io.coinmetrics.api.utils.streams.SuspendableStream
import io.coinmetrics.api.utils.streams.SuspendableStreamAsMergeSource
import io.coinmetrics.api.utils.streams.asMergeSource
import io.coinmetrics.api.utils.streams.operations.MergingStateSerializer
import io.coinmetrics.api.utils.streams.operations.mergeJoin
import io.coinmetrics.databases.Database
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.sync.Semaphore
import kotlinx.coroutines.sync.withPermit
import java.time.Clock
import java.time.Instant
import kotlin.math.max

// todo: don't allocate LinkedHashMap for each result's row, use List<Pair<String, String>> instead

/**
 * We support metrics with the following suffixes:
 * 1s - Reference rate real time
 * 1h - Network metrics, flow metrics, Reference rate
 * 1d(close) - Network daily metrics, flows daily metrics, reference rate
 * 1b - network block by block metrics, flows block by block metrics
 *
 * In the single request user can only ask for multiple metrics with the same suffix.
 * I.e. requests for metrics=PriceUSD1b,PriceUSD-1d are prohibited.
 */
class AssetMetricsEndpointImpl(
    networkDb: Database,
    hourlyNetworkDb: Database,
    minutelyNetworkDb: Database,
    ratesDb: Database,
    chainMonitorDb: Database,
    marketMetricsDb: Database,
    factoryDb: Database,
    principalPriceDb: Database,
    marketStatisticsService: MarketStatisticsService,
    private val amsService: AmsService,
    private val assetMetricsService: AssetMetricsService,
    private val communityApiKey: String,
    clock: Clock,
) : GetTimeseriesAssetMetricsEndpoint() {
    private val referenceRatesDataSource = ReferenceRatesDataSource(ratesDb)
    private val dailyNetworkMetricsDataSource = NetworkMetricsDataSource(networkDb, "statistics")
    private val hourlyNetworkMetricsDataSource = NetworkMetricsDataSource(hourlyNetworkDb, "statistics_hourly")
    private val minutesNetworkMetricsDataSource = NetworkMetricsDataSource(minutelyNetworkDb, "statistics_minutes")
    private val bbbNetworkMetricsDataSource = BbbNetworkMetricsDataSource(networkDb)
    private val bbbNewNetworkMetricsDataSource = BbbNewNetworkMetricsDataSource(factoryDb)
    private val bbbNetworkMetaMetricsDataSource =
        BbbNetworkMetricsDataSource(
            networkDb,
            "statistics_realtime_meta",
            "MinerEntity",
            "metadata_entry",
        )
    private val dailyFlowMetricsDataSource = DailyFlowMetricsDataSource(networkDb)
    private val bbbFlowMetricsDataSource = BbbFlowMetricsDataSource(networkDb)
    private val minutelyChainMonitorMetricsDataSource = MinutelyChainMonitorMetricsDataSource(chainMonitorDb)
    private val bbbChainMonitorMetricsDataSource =
        BbbMetricsDataSource(
            chainMonitorDb,
            DataSourceGroup.BBB_CHAIN_MONITOR_METRICS,
        )
    private val marketMetricsDataSource = MarketMetricsDataSource(marketMetricsDb)

    private val assetMarketMetricsDataProvider = AssetMarketMetricsDataProvider(marketMetricsDataSource)
    private val realizedVolatilityMetricsDataProvider = RealizedVolatilityMarketsMetricsDataProvider(marketMetricsDataSource)
    private val referenceRateMetricDataProvider = ReferenceRateMetricDataProvider(referenceRatesDataSource, clock)
    private val minutelyChainMonitorMetricsDataProvider = MinutelyChainMonitorMetricsDataProvider(minutelyChainMonitorMetricsDataSource)
    private val minutesNetworkMetricsDataProvider =
        NetworkMetricsDataProvider(minutesNetworkMetricsDataSource, Frequency.ONE_MINUTE, DataSourceGroup.MINUTELY_NETWORK_METRICS)
    private val hourlyNetworkMetricsDataProvider =
        NetworkMetricsDataProvider(hourlyNetworkMetricsDataSource, Frequency.ONE_HOUR, DataSourceGroup.HOURLY_NETWORK_METRICS)
    private val dailyNetworkMetricsDataProvider =
        NetworkMetricsDataProvider(dailyNetworkMetricsDataSource, Frequency.ONE_DAY, DataSourceGroup.DAILY_NETWORK_METRICS)
    private val dailyFlowMetricsDataProvider = DailyFlowMetricsDataProvider(dailyFlowMetricsDataSource)
    private val bbbEthScMetricsDataSource =
        BbbMetricsDataSource(
            factoryDb,
            DataSourceGroup.BBB_ETH_SC_METRICS,
        )
    private val principalPriceDataSource = PrincipalPriceDataSource(principalPriceDb, marketStatisticsService)
    private val principalPriceMetricDataProvider = PrincipalPriceMetricDataProvider(principalPriceDataSource, clock)

    private val dataProviders: List<AssetMetricDataProvider<out WithTime>> =
        listOf(
            referenceRateMetricDataProvider,
            assetMarketMetricsDataProvider,
            realizedVolatilityMetricsDataProvider,
            minutelyChainMonitorMetricsDataProvider,
            hourlyNetworkMetricsDataProvider,
            dailyNetworkMetricsDataProvider,
            dailyFlowMetricsDataProvider,
            principalPriceMetricDataProvider,
            minutesNetworkMetricsDataProvider,
        )

    // not more than 60 parallel requests to prevent OOMs. See PLAT-112.
    private val limitedParallelism = Semaphore(permits = 60)

    override suspend fun handle(request: GetTimeseriesAssetMetricsRequest): Response<AssetMetricsResponse> {
        val headers =
            amsService
                .getTimeseriesRateLimitHeaders(
                    apiKey = request.apiKey,
                    httpRequest = request.httpRequest,
                ).getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }
        return limitedParallelism.withPermit {
            handleInternal(request, headers)
        }
    }

    private suspend fun handleInternal(
        request: GetTimeseriesAssetMetricsRequest,
        headers: List<Pair<String, String>>,
    ): Response<AssetMetricsResponse> {
        if (request.minConfirmations != null && request.frequency != "1b") {
            return Response.errorResponse(
                ApiError.BadParameter("min_confirmations", "'min_confirmations' parameter can only be used with '1b' frequency."),
                headers,
            )
        }

        if (request.frequency.contains(",")) {
            return Response.errorResponse(ApiError.BadParameter("frequency", "Frequency must be a single value, not a list."), headers)
        }

        val (normalizedFrequency, frequencyOffset) = TimeUtils.parseFrequency(request.frequency)
        val normalizedFrequencyOffset =
            TimeUtils.normalizeFrequencyOffset(frequencyOffset)
                ?: return Response.errorResponse(UnsupportedParameterValue("frequency", request.frequency), headers)
        val frequency =
            Frequency
                .fromString(normalizedFrequency)
                ?: return Response.errorResponse(UnsupportedParameterValue("frequency", normalizedFrequency))

        if (!normalizedFrequencyOffset.default && normalizedFrequency != "1d") {
            return Response.errorResponse(
                ApiError.BadParameter("frequency", "Custom time offset is only supported for '1d' frequency."),
                headers,
            )
        }

        if (!normalizedFrequencyOffset.default &&
            !request.metrics.all { it.startsWith("ReferenceRate") || it.startsWith("principal_market") }
        ) {
            return Response.errorResponse(
                ApiError.BadParameter(
                    "frequency",
                    "Custom time offset is only supported by 'ReferenceRate' and 'principal_market_*' metrics.",
                ),
                headers,
            )
        }

        if (request.sort == "height" && normalizedFrequency != "1b") {
            return Response.errorResponse(
                ApiError.BadParameter("sort", "Sorting by 'height' is only available for metrics with `1b` frequency."),
                headers,
            )
        }

        if (request.sort == "time" && normalizedFrequency == "1b") {
            return Response.errorResponse(
                ApiError.BadParameter("sort", "Sorting by 'time' is not allowed for metrics with `1b` frequency."),
                headers,
            )
        }

        val requestAssets =
            request.assets.asSequence().map { it.lowercase() }.toSet().let {
                if (it.contains("*")) {
                    // remove other assets if "*" is specified
                    setOf("*")
                } else {
                    it
                }
            }

        val patternRequested = requestAssets.contains("*")
        val metricsAvailabilityMap =
            assetMetricsService
                .findTimeseriesMetrics(
                    apiKey = request.apiKey,
                    frequency = request.frequency,
                    entities = requestAssets,
                    metrics = request.metrics.toSet(),
                    ignoreForbiddenAndUnsupportedErrors = request.ignoreUnsupportedErrors,
                ).getOrElse { return Response.errorResponse(it) }

        val sortingFirstByTimeOrHeight = request.sort == "time" || request.sort == "height" || shouldUseSortByTimeWorkaround(request)

        // Select assets based on metric availability map
        val assets =
            metricsAvailabilityMap.mapNotNull { (asset, metricsAvailability) ->
                val metricsToAvailability = metricsAvailability.metricToAvailability
                val errors = metricsToAvailability.selectErrorMetrics()
                // return immediately if at least one metric is an error and ignore forbidden/unsupported errors is true
                if (errors.isNotEmpty()) {
                    if (!request.ignoreForbiddenErrors) {
                        metricsToAvailability.getFirstForbiddenError()?.also {
                            return Response.errorResponse(it.apiError)
                        }
                    }
                    if (!request.ignoreUnsupportedErrors) {
                        metricsToAvailability.getFirstNonForbiddenError()?.also {
                            return Response.errorResponse(it.apiError)
                        }
                    }
                }

                val unsupportedMetrics = metricsToAvailability.selectUnsupportedMetrics()
                val metrics = request.metrics.toHashSet()
                val supportedMetrics = metrics.toHashSet() - unsupportedMetrics - errors
                if (supportedMetrics.isEmpty() && unsupportedMetrics.isNotEmpty()) {
                    // all metrics are unsupported
                    if (!request.ignoreUnsupportedErrors) {
                        return Response.errorResponse(
                            ApiError.BadParameter(
                                "metrics",
                                "All requested metrics aren't supported for asset '$asset' and frequency '$normalizedFrequency'.",
                            ),
                        )
                    } else {
                        return@mapNotNull null
                    }
                }

                val metricsGroupedByDataSource =
                    groupByDataSource(asset, supportedMetrics, frequency, request.ignoreUnsupportedErrors).getOrElse {
                        if (!request.ignoreUnsupportedErrors) {
                            return Response.errorResponse(it)
                        } else {
                            return@mapNotNull null
                        }
                    }

                Triple(asset, supportedMetrics, metricsGroupedByDataSource)
            }

        data class TimeParameters(
            val startTime: Instant?,
            val endTime: Instant?,
            val pagingFrom: PagingFrom,
        )

        val (effectiveLimitPerAsset, timeParameters) =
            if (sortingFirstByTimeOrHeight) {
                // the logic is only for time-range requests
                val (startTime, endTime) =
                    DataUtils
                        .parseTimeParameters(
                            request.startTime,
                            request.startInclusive,
                            request.endTime,
                            request.endInclusive,
                            request.timezone,
                        ).getOrElse { return Response.errorResponse(it, headers) }
                null to TimeParameters(startTime, endTime, request.pagingFrom)
            } else {
                if (request.frequency == "1b" || request.startHeight != null || request.endHeight != null) {
                    request.limitPerAsset to TimeParameters(null, null, request.pagingFrom)
                } else {
                    // the logic is only for time-range requests
                    val (startTime, endTime) =
                        DataUtils
                            .parseTimeParameters(
                                request.startTime,
                                request.startInclusive,
                                request.endTime,
                                request.endInclusive,
                                request.timezone,
                            ).getOrElse { return Response.errorResponse(it, headers) }
                    val frequencyDuration =
                        Utils.frequencyToDuration(normalizedFrequency).getOrElse {
                            return Response.errorResponse(it, headers)
                        }
                    val (forcedPagingFrom, effectiveLimitPerAsset) =
                        Utils.getEffectiveLimitPerEntity(
                            request.limitPerAsset,
                            request.pageSize,
                            assets.size,
                            frequencyDuration,
                            startTime,
                            request.startInclusive,
                            endTime,
                            request.endInclusive,
                        )
                    effectiveLimitPerAsset to
                        TimeParameters(startTime, endTime, forcedPagingFrom ?: request.pagingFrom)
                }
            }

        val assetToStreamSpecificDataList =
            assets
                .map { (asset, supportedMetrics, metricsGroupedByDataSource) ->
                    when (
                        val result =
                            checkParameters(
                                request,
                                asset,
                                timeParameters.pagingFrom,
                                metricsGroupedByDataSource,
                                frequency,
                            )
                    ) {
                        is FunctionResult.Success ->
                            asset to
                                Triple(
                                    supportedMetrics,
                                    metricsGroupedByDataSource,
                                    result.value,
                                )
                        is FunctionResult.Failure -> return Response.errorResponse(result.value)
                    }
                }

        val stream =
            if (sortingFirstByTimeOrHeight) {
                // sort by (time, asset) or (height, asset)

                // optimization for buffer size, lower limit of buffer size is 2
                // why not 1? because an additional item is needed to check availability of the next page
                // smaller buffer size is better when we don't want to transfer a lot of redundant data via network
                val bufferSizePerStream = max(1, request.pageSize / assets.size) + 1

                val mergeKeyExtractor: (LinkedHashMap<String, String?>) -> Comparable<*> =
                    if (request.sort == "time" || request.sort == "asset") {
                        { map ->
                            ComparablePair(map["time"]!!, map["asset"]!!)
                        }
                    } else {
                        { map ->
                            ComparablePair(map["height"]!!.toInt(), map["asset"]!!)
                        }
                    }

                val assetToStreamSpecificData = assetToStreamSpecificDataList.toMap()

                BatchUtils
                    .mergeSortStreams(
                        streamIds = assetToStreamSpecificData.keys.toTypedArray(),
                        pagingFrom = timeParameters.pagingFrom,
                        nextPageToken = request.nextPageToken,
                        mergeKeyExtractor = mergeKeyExtractor,
                        limitPerStream = request.limitPerAsset,
                        streamSupplier = { id, pageToken ->
                            val (supportedMetrics, metricsGroupedByDataSource, dataSourceSpecificData) =
                                assetToStreamSpecificData.getValue(id)
                            handleInternal(
                                asset = id,
                                patternRequested = patternRequested,
                                metrics = request.metrics.toHashSet(),
                                supportedMetrics = supportedMetrics,
                                metricsGroupedByDataSource = metricsGroupedByDataSource,
                                bufferSize = bufferSizePerStream,
                                pageToken = pageToken,
                                frequency = frequency,
                                frequencyOffset = normalizedFrequencyOffset,
                                startTime = timeParameters.startTime,
                                endTime = timeParameters.endTime,
                                pagingFrom = timeParameters.pagingFrom,
                                request = request,
                                communityApiKey = communityApiKey,
                                dataSourceSpecificData = dataSourceSpecificData,
                            ).toSuccess()
                        },
                    ).getOrElse {
                        return Response.errorResponse(it, headers)
                    }
            } else {
                // sort by (asset, time)
                val (prefetch, bufferSizePerStream) = Utils.getFetchProperties(effectiveLimitPerAsset, assets.size, request.pageSize)

                BatchUtils
                    .sortIdsAndConcatStreams(
                        streams = assetToStreamSpecificDataList.asSequence(),
                        streamIdsAreResolvedDynamically = patternRequested,
                        pagingFrom = timeParameters.pagingFrom,
                        nextPageToken = request.nextPageToken,
                        initialStreamStateParser = { it },
                        numberOfStreamsToPrefetch = prefetch,
                        limitPerStream = effectiveLimitPerAsset,
                        streamSupplier = { id, pageToken, (supportedMetrics, metricsGroupedByDataSource, dataSourceSpecificData) ->
                            handleInternal(
                                asset = id,
                                patternRequested = patternRequested,
                                metrics = request.metrics.toHashSet(),
                                supportedMetrics = supportedMetrics,
                                metricsGroupedByDataSource = metricsGroupedByDataSource,
                                bufferSize = bufferSizePerStream,
                                pageToken = pageToken,
                                frequency = frequency,
                                frequencyOffset = normalizedFrequencyOffset,
                                startTime = timeParameters.startTime,
                                endTime = timeParameters.endTime,
                                pagingFrom = timeParameters.pagingFrom,
                                request = request,
                                communityApiKey = communityApiKey,
                                dataSourceSpecificData = dataSourceSpecificData,
                            )
                        },
                        httpRequestCoroutineContext = request.httpRequest.coroutineContext,
                        logger = log,
                    ).getOrElse {
                        return Response.errorResponse(it)
                    }
            }

        val page = stream.getPageFlow(request.httpRequest, request.pageSize, timeParameters.pagingFrom)

        return Response.chunkedResponse(
            items = page,
            headers = headers,
            format = if (request.format == "csv") ChunkedResponseFormat.Csv() else ChunkedResponseFormat.Json(allowNullValues = true),
        )
    }

    /**
     * Returns true only for 1s ReferenceRate requested for multiple assets for a single point of time.<br/>
     * These type of requests are used in CM mobile App.<br/>
     * This hack is needed to speed up such requests due to "parallel" nature of "sort by time" engine.
     */
    private fun shouldUseSortByTimeWorkaround(request: GetTimeseriesAssetMetricsRequest): Boolean =
        request.sort == "asset" &&
            request.frequency == "1s" &&
            request.startTime != null &&
            request.endTime != null &&
            request.startInclusive &&
            request.endInclusive &&
            TimeUtils.haveTheSameSeconds(request.startTime, request.endTime, request.timezone)

    private suspend fun checkParameters(
        request: GetTimeseriesAssetMetricsRequest,
        asset: String,
        pagingFrom: PagingFrom,
        metricsGroupedByDataSource: List<Map.Entry<DataSourceGroup, List<String>>>,
        frequency: Frequency,
    ): FunctionResult<ApiError, Map<DataSourceGroup, AssetMetricsDataSourceSpecificData>> =
        if (frequency == Frequency.ONE_BLOCK) {
            coroutineScope {
                metricsGroupedByDataSource
                    .mapIndexed { dataSourceIndex, (dataSource, dataSourceMetrics) ->
                        async {
                            dataSource to
                                when (dataSource) {
                                    DataSourceGroup.BBB_NETWORK_METRICS -> {
                                        bbbNetworkMetricsDataSource.checkParameters(
                                            request,
                                            asset,
                                            pagingFrom,
                                        )
                                    }

                                    DataSourceGroup.BBB_NEW_NETWORK_METRICS -> {
                                        bbbNewNetworkMetricsDataSource.checkParameters(
                                            request,
                                            asset,
                                            pagingFrom,
                                        )
                                    }

                                    DataSourceGroup.BBB_NETWORK_METADATA_METRICS -> {
                                        bbbNetworkMetaMetricsDataSource.checkParameters(
                                            request,
                                            asset,
                                            pagingFrom,
                                        )
                                    }

                                    DataSourceGroup.BBB_FLOW_METRICS -> {
                                        bbbFlowMetricsDataSource.checkParameters(
                                            request,
                                            asset,
                                            pagingFrom,
                                        )
                                    }

                                    DataSourceGroup.BBB_CHAIN_MONITOR_METRICS -> {
                                        bbbChainMonitorMetricsDataSource.checkParameters(
                                            request,
                                            asset,
                                            dataSourceMetrics,
                                            pagingFrom,
                                        )
                                    }

                                    DataSourceGroup.BBB_ETH_SC_METRICS -> {
                                        bbbEthScMetricsDataSource.checkParameters(
                                            request,
                                            asset,
                                            dataSourceMetrics,
                                            pagingFrom,
                                        )
                                    }

                                    else -> throw IllegalStateException(
                                        "Unsupported data source '$dataSource' for metrics '$dataSourceMetrics'.",
                                    )
                                }
                        }
                    }.awaitAll()
            }
        } else {
            coroutineScope {
                metricsGroupedByDataSource
                    .mapIndexed { dataSourceIndex, (dataSource, dataSourceMetrics) ->
                        async {
                            // choose applicable data provider based on metrics, data source and frequency
                            val dataProvider =
                                dataProviders.firstOrNull { it.isApplicable(dataSourceMetrics, frequency, dataSource) }
                                    ?: throw IllegalStateException(
                                        "Unsupported data source '$dataSource' for metrics '$dataSourceMetrics'.",
                                    )

                            dataSource to dataProvider.checkParameters(asset, dataSourceMetrics)
                        }
                    }.awaitAll()
            }
        }.associateBy(
            { it.first },
            { it.second.getOrElse { apiError -> return apiError.toFailure() } },
        ).toSuccess()

    private suspend fun handleInternal(
        asset: String,
        patternRequested: Boolean,
        metrics: Set<String>,
        supportedMetrics: Set<String>,
        metricsGroupedByDataSource: List<Map.Entry<DataSourceGroup, List<String>>>,
        bufferSize: Int,
        pageToken: String?,
        frequency: Frequency,
        frequencyOffset: TimeUtils.NormalizedFrequencyOffset,
        startTime: Instant?,
        endTime: Instant?,
        pagingFrom: PagingFrom,
        request: GetTimeseriesAssetMetricsRequest,
        communityApiKey: String,
        dataSourceSpecificData: Map<DataSourceGroup, AssetMetricsDataSourceSpecificData>,
    ): SuspendableStream<LinkedHashMap<String, String?>, SuspendableStream.State> {
        val reviewableMetrics = Resources.retainReviewableMetrics(metrics.toTypedArray())

        val pageTokenPerDataSource =
            MergingStateSerializer
                .deserialize(pageToken, metricsGroupedByDataSource.size)
                .getOrElse { error(it) }
                .states

        // run queries, one per table
        return if (frequency == Frequency.ONE_BLOCK) {
            coroutineScope {
                metricsGroupedByDataSource
                    .mapIndexed { dataSourceIndex, (dataSource, dataSourceMetrics) ->
                        async {
                            val pageTokenForDataSource = pageTokenPerDataSource[dataSourceIndex]
                            when (dataSource) {
                                DataSourceGroup.BBB_NETWORK_METRICS -> {
                                    val (rangeQuery, additionalFilter) =
                                        dataSourceSpecificData[dataSource] as
                                            BbbNetworkMetricsDataSource.BbbNetworkMetricsDataSourceSpecificData
                                    bbbNetworkMetricsDataSource
                                        .query(
                                            asset,
                                            patternRequested,
                                            bufferSize,
                                            pageTokenForDataSource,
                                            dataSourceMetrics,
                                            pagingFrom,
                                            request,
                                            rangeQuery,
                                            additionalFilter,
                                        ).asMergeSource { ComparablePair(it.height, it.hash) }
                                }

                                DataSourceGroup.BBB_NEW_NETWORK_METRICS -> {
                                    val (rangeQuery, additionalFilter) =
                                        dataSourceSpecificData[dataSource] as
                                            BbbNewNetworkMetricsDataSource.BbbNewNetworkMetricsDataSourceSpecificData
                                    bbbNewNetworkMetricsDataSource
                                        .query(
                                            asset,
                                            patternRequested,
                                            bufferSize,
                                            pageTokenForDataSource,
                                            dataSourceMetrics,
                                            pagingFrom,
                                            rangeQuery,
                                            additionalFilter,
                                        ).asMergeSource { ComparablePair(it.height, it.hash) }
                                }

                                DataSourceGroup.BBB_NETWORK_METADATA_METRICS -> {
                                    val (rangeQuery, additionalFilter) =
                                        dataSourceSpecificData[dataSource] as
                                            BbbNetworkMetricsDataSource.BbbNetworkMetricsDataSourceSpecificData
                                    bbbNetworkMetaMetricsDataSource
                                        .query(
                                            asset,
                                            patternRequested,
                                            bufferSize,
                                            pageTokenForDataSource,
                                            dataSourceMetrics,
                                            pagingFrom,
                                            request,
                                            rangeQuery,
                                            additionalFilter,
                                        ).asMergeSource { ComparablePair(it.height, it.hash) }
                                }

                                DataSourceGroup.BBB_FLOW_METRICS -> {
                                    val (rangeQuery, additionalFilter) =
                                        dataSourceSpecificData[dataSource] as
                                            BbbFlowMetricsDataSource.BbbFlowMetricsDataSourceSpecificData
                                    bbbFlowMetricsDataSource
                                        .query(
                                            asset,
                                            patternRequested,
                                            bufferSize,
                                            pageTokenForDataSource,
                                            dataSourceMetrics,
                                            pagingFrom,
                                            request,
                                            rangeQuery,
                                            additionalFilter,
                                        ).asMergeSource { ComparablePair(it.height, it.hash) }
                                }

                                DataSourceGroup.BBB_CHAIN_MONITOR_METRICS -> {
                                    val (rangeQuery, additionalFilter) =
                                        dataSourceSpecificData[dataSource] as
                                            BbbMetricsDataSource.BbbMetricsDataSourceSpecificData
                                    bbbChainMonitorMetricsDataSource
                                        .query(
                                            asset,
                                            patternRequested,
                                            bufferSize,
                                            pageTokenForDataSource,
                                            dataSourceMetrics,
                                            pagingFrom,
                                            rangeQuery,
                                            additionalFilter,
                                        ).asMergeSource { ComparablePair(it.height, it.hash) }
                                }

                                DataSourceGroup.BBB_ETH_SC_METRICS -> {
                                    val (rangeQuery, additionalFilter) =
                                        dataSourceSpecificData[dataSource] as
                                            BbbMetricsDataSource.BbbMetricsDataSourceSpecificData
                                    bbbEthScMetricsDataSource
                                        .query(
                                            asset,
                                            patternRequested,
                                            bufferSize,
                                            pageTokenForDataSource,
                                            dataSourceMetrics,
                                            pagingFrom,
                                            rangeQuery,
                                            additionalFilter,
                                        ).asMergeSource { ComparablePair(it.height, it.hash) }
                                }

                                else -> throw IllegalStateException(
                                    "Unsupported data source '$dataSource' for metrics '$dataSourceMetrics'.",
                                )
                            }
                        }
                    }.awaitAll()
            }.mergeJoin(
                streamId = if (patternRequested) asset else null,
                pagingFrom = pagingFrom,
            ) { values ->
                val map = LinkedHashMap<String, String?>()
                val mapMetrics = HashMap<String, String?>()
                values.forEach { value ->
                    if (value != null) {
                        map["block_hash"] = value.hash
                        map["parent_block_hash"] = value.parentHash
                        map["height"] = value.height.toString()
                        map["asset"] = asset
                        map["time"] = TimeUtils.dateTimeFormatter.format(value.time)
                        mapMetrics.putAll(value.metrics)
                    }
                }
                request.metrics.forEach {
                    if (supportedMetrics.contains(it)) {
                        // add nulls for absent metrics if they are supported
                        if (request.nullAsZero) {
                            mapMetrics.putIfAbsent(it, "0")
                        } else {
                            mapMetrics.putIfAbsent(it, null)
                        }
                    } else if (request.format == "csv") {
                        // add empty strings for unsupported metrics if CSV is requested
                        mapMetrics.putIfAbsent(it, "")
                    }
                    if (reviewableMetrics.contains(it)) {
                        if (supportedMetrics.contains(it)) {
                            // add nulls for absent metrics if they are supported
                            mapMetrics.putIfAbsent("$it-status", null)
                            mapMetrics.putIfAbsent("$it-status-time", null)
                        } else if (request.format == "csv") {
                            // add empty strings for unsupported metrics if CSV is requested
                            mapMetrics.putIfAbsent("$it-status", "")
                            mapMetrics.putIfAbsent("$it-status-time", "")
                        }
                    }
                }
                map.putAll(mapMetrics.map { it.key to it.value }.sortedBy { it.first })
                map
            }
        } else {
            coroutineScope {
                metricsGroupedByDataSource
                    .mapIndexed { dataSourceIndex, (dataSource, dataSourceMetrics) ->
                        val pageTokenForDataSource = pageTokenPerDataSource[dataSourceIndex]
                        async {
                            // choose applicable data provider based on metrics, data source and frequency
                            val dataProvider =
                                dataProviders.firstOrNull { it.isApplicable(dataSourceMetrics, frequency, dataSource) }
                                    ?: throw IllegalStateException(
                                        "Unsupported data source '$dataSource' for metrics '$dataSourceMetrics'.",
                                    )

                            dataProvider
                                .provide(
                                    asset,
                                    patternRequested,
                                    dataSourceMetrics,
                                    startTime ?: error("Start time should not be null."),
                                    endTime ?: error("End time should not be null."),
                                    pagingFrom,
                                    request,
                                    pageTokenForDataSource,
                                    frequencyOffset,
                                    frequency,
                                    bufferSize,
                                    communityApiKey,
                                    dataSourceSpecificData[dataSource],
                                ).asMergeSource { it.time }
                        }
                    }.awaitAll()
            }.let {
                joinMergedSources(
                    it,
                    pagingFrom,
                    request,
                    asset,
                    patternRequested,
                    supportedMetrics,
                    reviewableMetrics,
                )
            }
        }
    }

    private fun <T, S : SuspendableStream.State> joinMergedSources(
        mergeSources: List<SuspendableStreamAsMergeSource<T, S, Instant>>,
        pagingFrom: PagingFrom,
        request: GetTimeseriesAssetMetricsRequest,
        asset: String,
        patternRequested: Boolean,
        supportedMetrics: Set<String>,
        reviewableMetrics: Set<String>,
    ): SuspendableStream<LinkedHashMap<String, String?>, *> =
        mergeSources.mergeJoin(
            streamId = if (patternRequested) asset else null,
            pagingFrom = pagingFrom,
        ) { values ->
            // todo optimize, don't allocate maps in the loop
            val map = LinkedHashMap<String, String?>()
            val mapMetrics = HashMap<String, String?>()
            values.forEach { value ->
                when (value) {
                    is MetricUtils.MetricsWithTimeWrapper -> {
                        map["asset"] = asset
                        map["time"] = TimeUtils.dateTimeFormatter.format(value.time)
                        mapMetrics.putAll(value.metrics)
                    }

                    is MetricDataUtils.DataWithTimeWrapper -> {
                        map["asset"] = asset
                        map["time"] = TimeUtils.dateTimeFormatter.format(value.time)
                        mapMetrics[value.metric] = value.metricValue
                    }

                    is MetricDataUtils.MetricsHolderWithTime -> {
                        map["asset"] = asset
                        map["time"] = TimeUtils.dateTimeFormatter.format(value.time)
                        mapMetrics.putAll(value.metrics.filterKeys { it in supportedMetrics })
                    }
                }
            }
            request.metrics.forEach {
                if (supportedMetrics.contains(it)) {
                    // add nulls for absent metrics if they are supported
                    if (request.nullAsZero) {
                        mapMetrics.putIfAbsent(it, "0")
                    } else {
                        mapMetrics.putIfAbsent(it, null)
                    }
                } else if (request.format == "csv") {
                    // add empty strings for unsupported metrics if CSV is requested
                    mapMetrics.putIfAbsent(it, "")
                }
                if (reviewableMetrics.contains(it)) {
                    if (supportedMetrics.contains(it)) {
                        // add nulls for absent metrics if they are supported
                        mapMetrics.putIfAbsent("$it-status", null)
                        mapMetrics.putIfAbsent("$it-status-time", null)
                    } else if (request.format == "csv") {
                        // add empty strings for unsupported metrics if CSV is requested
                        mapMetrics.putIfAbsent("$it-status", "")
                        mapMetrics.putIfAbsent("$it-status-time", "")
                    }
                }
            }
            map.putAll(mapMetrics.map { it.key to it.value }.sortedBy { it.first })
            map
        }

    interface AssetMetricsDataSourceSpecificData {
        companion object {
            val EMPTY = object : AssetMetricsDataSourceSpecificData {}
        }
    }
}
