package io.coinmetrics.api.endpoints.timeseries.index

import io.coinmetrics.api.CommonConstants
import io.coinmetrics.api.resources.IndexInfo
import io.coinmetrics.api.utils.RangeQuery

object IndexEndpointUtils {
    val unrestrictedCandleFrequencies =
        hashSetOf("1h", "4h", "1d")
            .also {
                if (!CommonConstants.candleFrequenciesMap.keys.containsAll(
                        it,
                    )
                ) {
                    error("Unrestricted candle frequency must be subset of candleFrequenciesMap")
                }
            }
}

data class IndexMetadata(
    val indexInfo: IndexInfo,
    val rangeQuery: RangeQuery.TimeRangeQuery,
)
