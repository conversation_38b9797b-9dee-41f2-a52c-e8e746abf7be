package io.coinmetrics.api.endpoints.timeseries.market

import io.coinmetrics.api.ChunkedResponseFormat
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.Response
import io.coinmetrics.api.endpoints.GetTimeseriesMarketContractPricesEndpoint
import io.coinmetrics.api.endpoints.GetTimeseriesMarketContractPricesRequest
import io.coinmetrics.api.model.DerivativesMarketType.FUTURE
import io.coinmetrics.api.model.NormalizedMarket
import io.coinmetrics.api.model.ParsedMarket
import io.coinmetrics.api.models.MarketContractPrices
import io.coinmetrics.api.models.MarketContractPricesResponse
import io.coinmetrics.api.models.PagingFrom
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.MarketConstraints
import io.coinmetrics.api.service.MarketResolvingService
import io.coinmetrics.api.service.MarketStatisticsService
import io.coinmetrics.api.service.getTimeseriesRateLimitHeaders
import io.coinmetrics.api.utils.BatchUtils
import io.coinmetrics.api.utils.CommonUtils
import io.coinmetrics.api.utils.DataUtils
import io.coinmetrics.api.utils.DataUtils.adjust
import io.coinmetrics.api.utils.QueryTextBuilder
import io.coinmetrics.api.utils.RangeQuery
import io.coinmetrics.api.utils.SqlUtils
import io.coinmetrics.api.utils.TimeUtils
import io.coinmetrics.api.utils.Utils
import io.coinmetrics.api.utils.paging.PageToken
import io.coinmetrics.api.utils.paging.getPageFlow
import io.coinmetrics.api.utils.streams.SuspendableStream
import io.coinmetrics.databases.Database
import kotlinx.coroutines.flow.map
import java.sql.ResultSet
import java.time.Instant
import java.time.ZoneId

/*
 * This endpoint is designed for both futures and option markets,
 * but only options are supported at the moment because there is no futures_ticker table yet.
 */
class ContractPricesEndpointImpl(
    private val futuresDb: Database,
    private val derivTradeDb: Database,
    private val amsService: AmsService,
    private val marketResolvingService: MarketResolvingService,
    private val marketStatisticsService: MarketStatisticsService,
    private val communityApiKey: String,
) : GetTimeseriesMarketContractPricesEndpoint() {
    override suspend fun handle(request: GetTimeseriesMarketContractPricesRequest): Response<MarketContractPricesResponse> {
        val headers =
            amsService
                .getTimeseriesRateLimitHeaders(
                    apiKey = request.apiKey,
                    httpRequest = request.httpRequest,
                ).getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }

        val parsedTimeParams =
            DataUtils
                .parseAndValidateTimeParameters(
                    startTimeStr = request.startTime,
                    startInclusive = request.startInclusive,
                    endTimeStr = request.endTime,
                    endInclusive = request.endInclusive,
                    timezone = request.timezone,
                    pageSize = request.pageSize,
                    pagingFrom = request.pagingFrom,
                ).getOrElse { return Response.errorResponse(it) }

        val (marketsConstraints, marketPatternRequested) =
            marketResolvingService
                .parseAndCheckMarkets(
                    apiKey = request.apiKey,
                    requestedMarkets = request.markets,
                    marketFilter = { market -> market is ParsedMarket.ParsedDerivativesMarket },
                    errorMessageForFilteredOutMarketsIfPatternIsNotRequested = { "Only future and option markets are supported." },
                ).getOrElse { return Response.errorResponse(it) }

        val (prefetch, bufferSizePerStream) =
            Utils.getFetchProperties(
                request.limitPerMarket,
                marketsConstraints.size,
                request.pageSize,
            )

        val downSamplingConfig =
            TimeUtils
                .createStatefulDownSamplerConfig(
                    granularity = request.granularity,
                    pagingFromStart = request.pagingFrom == PagingFrom.START,
                    withAlignment = true,
                    timezone = ZoneId.of(request.timezone),
                ).getOrElse {
                    return Response.errorResponse(it)
                }

        val streams =
            BatchUtils.sortIdsAndConcatStreams(
                streams = marketsConstraints.asSequence().map { it.toPair() },
                numberOfStreamsToPrefetch = prefetch,
                pagingFromStart = request.pagingFrom == PagingFrom.START,
                nextPageToken = request.nextPageToken,
                initialStreamStateParser = { PageToken.TimePageToken.parse(it) },
                limitPerStream = request.limitPerMarket,
                streamSupplier = { id, state, marketConstraints ->
                    handleInternal(
                        market = id,
                        initialState = state,
                        request = request,
                        marketPatternRequested = marketPatternRequested,
                        marketConstraints = marketConstraints,
                        parsedTimeParams = parsedTimeParams,
                        bufferSize = bufferSizePerStream,
                        downSamplingConfig = downSamplingConfig,
                    )
                },
                streamIdsAreResolvedDynamically = marketPatternRequested,
                httpRequestCoroutineContext = request.httpRequest.coroutineContext,
                logger = log,
            )

        return when (streams) {
            is FunctionResult.Success -> {
                val page =
                    streams.value
                        .map { it.marketContractPrices }
                        .getPageFlow(
                            request.httpRequest,
                            request.pageSize,
                            request.pagingFrom == PagingFrom.START,
                        ).let {
                            if (request.format == "csv") {
                                it.map { obj ->
                                    (obj as? MarketContractPrices)?.toMap() ?: obj
                                }
                            } else {
                                it
                            }
                        }

                Response.chunkedResponse(
                    items = page,
                    headers = headers,
                    format = if (request.format == "csv") ChunkedResponseFormat.Csv(nullValue = "") else ChunkedResponseFormat.Json(),
                )
            }

            is FunctionResult.Failure -> Response.errorResponse(streams.value, headers)
        }
    }

    private fun MarketContractPrices.toMap() =
        mapOf(
            "market" to market,
            "time" to time,
            "database_time" to databaseTime,
            "mark_price" to markPrice,
            "index_price" to indexPrice,
            "settlement_price_estimated" to settlementPriceEstimated,
            "exchange_time" to exchangeTime,
        )

    private fun handleInternal(
        market: String,
        initialState: PageToken.TimePageToken?,
        request: GetTimeseriesMarketContractPricesRequest,
        marketPatternRequested: Boolean,
        marketConstraints: MarketConstraints,
        parsedTimeParams: Triple<Instant, Instant, Boolean>,
        bufferSize: Int,
        downSamplingConfig: TimeUtils.StatefulDownSamplerConfig?,
    ): SuspendableStream<MarketContractPricesWrapper, PageToken.TimePageToken> {
        val (marketId, keyMinTime, keyMaxTime) = marketConstraints

        val derivativeMarket = marketId as NormalizedMarket.DerivativesNormalizedMarket

        val (startTime, endTime, pagingFromStart) =
            parsedTimeParams.adjust(
                DataUtils.communityEnforcedStart(request.apiKey, communityApiKey),
                keyMinTime,
                keyMaxTime,
            )
                /**
                 * Returning an empty stream for simplicity.
                 * We could enhance this later, for example, by returning a function failure result if a pattern was not requested —
                 * but that would require more refactoring, which may not be necessary at this point.
                 */
                ?: return SuspendableStream.empty()

        if (MarketEndpointUtil.isDerivativeMarketOperationTimeNotOverlap(
                marketName = market,
                marketType = marketId.type,
                timeRange = startTime to endTime,
                marketStatisticsService = marketStatisticsService,
            )
        ) {
            return SuspendableStream.empty()
        }

        val rangeQuery =
            RangeQuery
                .TimeRangeQuery(
                    startKey = startTime,
                    startInclusive = request.startInclusive,
                    endKey = endTime,
                    endInclusive = request.endInclusive,
                    pagingFromStart = pagingFromStart,
                ).withPageToken(initialState)

        val timeFilter = DataUtils.createTimeFilter(rangeQuery)
        val additionalFilter = { it: MarketContractPricesWrapper ->
            timeFilter.invoke(it.time)
        }

        val sqlQueryDetails =
            if (derivativeMarket.type == FUTURE) {
                // Return empty stream when sharded table does not exist
                if (marketStatisticsService.getFutureTickerStatistics(market) == null) {
                    return SuspendableStream.empty()
                }
                QueryBuilderInfo(
                    db = derivTradeDb,
                    tableName = "${derivTradeDb.config.schema}.futures_ticker_${derivativeMarket.exchange}",
                    timeField = "ticker_deduplication_time",
                    exchangeIdField = "${derivativeMarket.exchange} AS ticker_exchange_id",
                    exchangeTimeField = "NULL AS ticker_exchange_time",
                    exchangeFilter = "",
                )
            } else {
                QueryBuilderInfo(
                    db = futuresDb,
                    tableName = "${futuresDb.config.schema}.option_ticker",
                    timeField = "ticker_time",
                    exchangeIdField = "ticker_exchange_id",
                    exchangeTimeField = "ticker_exchange_time",
                    exchangeFilter = "AND ticker_exchange_id=${derivativeMarket.exchange}",
                )
            }

        val queryTextBuilder: QueryTextBuilder = { filter, limit ->
            val ordering = if (request.pagingFrom == PagingFrom.START) "ASC" else "DESC"
            val symbolSqlParam = SqlUtils.escapeSql(derivativeMarket.symbol)
            val symbolFilter = "ticker_symbol='$symbolSqlParam'"
            """
            SELECT 
                ${sqlQueryDetails.timeField},
                ${sqlQueryDetails.exchangeIdField},
                ticker_symbol,
                ticker_price_mark,
                ticker_price_index,
                ticker_estimated_settlement_price,
                ${sqlQueryDetails.exchangeTimeField},
                ticker_database_time
            FROM ${sqlQueryDetails.tableName}
            WHERE $symbolFilter
                ${sqlQueryDetails.exchangeFilter}
                $filter
            ORDER BY ${sqlQueryDetails.timeField} $ordering
            LIMIT $limit
            """
        }

        val downSamplingFilter = downSamplingConfig?.let { TimeUtils.createStatefulDownSampler(it) }

        return DataUtils
            .createStream(
                db = sqlQueryDetails.db,
                queryTextBuilder = queryTextBuilder,
                bufferSize = bufferSize,
                keyNames = arrayOf(sqlQueryDetails.timeField),
                dataMapper = createMapper(market, sqlQueryDetails.timeField),
                rangeQuery = rangeQuery,
                initialState = initialState,
                stateResolver = { PageToken.TimePageToken(it.time) },
                streamId =
                    if (marketPatternRequested) {
                        market
                    } else {
                        null
                    },
            ).filter(additionalFilter)
            .let { stream ->
                if (downSamplingFilter == null) {
                    stream
                } else {
                    stream.filter { downSamplingFilter(it.time) }
                }
            }
    }

    private fun createMapper(
        market: String,
        timeField: String,
    ): (ResultSet) -> MarketContractPricesWrapper =
        { rs ->
            val time = rs.getTimestamp(timeField).toInstant()
            val timeFormatted = TimeUtils.dateTimeFormatter.format(time)

            val dbTime =
                rs.getTimestamp("ticker_database_time").toInstant().let {
                    TimeUtils.dateTimeFormatter.format(it)
                }

            MarketContractPricesWrapper(
                time,
                MarketContractPrices(
                    time = timeFormatted,
                    market = market,
                    markPrice = rs.getBigDecimal("ticker_price_mark")?.let { CommonUtils.formatBigDecimal(it) },
                    indexPrice = rs.getBigDecimal("ticker_price_index")?.let { CommonUtils.formatBigDecimal(it) },
                    settlementPriceEstimated =
                        rs
                            .getBigDecimal(
                                "ticker_estimated_settlement_price",
                            )?.let { CommonUtils.formatBigDecimal(it) },
                    exchangeTime = rs.getTimestamp("ticker_exchange_time")?.toInstant()?.let { TimeUtils.dateTimeFormatter.format(it) },
                    databaseTime = dbTime,
                ),
            )
        }

    private class QueryBuilderInfo(
        val db: Database,
        val tableName: String,
        val timeField: String,
        val exchangeIdField: String,
        val exchangeTimeField: String,
        val exchangeFilter: String,
    )

    private class MarketContractPricesWrapper(
        val time: Instant,
        val marketContractPrices: MarketContractPrices,
    )
}
