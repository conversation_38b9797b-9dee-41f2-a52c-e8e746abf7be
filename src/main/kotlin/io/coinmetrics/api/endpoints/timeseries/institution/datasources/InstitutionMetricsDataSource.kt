package io.coinmetrics.api.endpoints.timeseries.institution.datasources

import io.coinmetrics.api.utils.DataUtils
import io.coinmetrics.api.utils.MetricUtils
import io.coinmetrics.api.utils.QueryTextBuilder
import io.coinmetrics.api.utils.RangeQuery
import io.coinmetrics.api.utils.SqlUtils
import io.coinmetrics.api.utils.paging.PageToken
import io.coinmetrics.api.utils.streams.SuspendableStream
import io.coinmetrics.api.utils.toSqlOrdering
import io.coinmetrics.databases.Database

class InstitutionMetricsDataSource(
    val db: Database,
) {
    fun query(
        institution: String,
        patternRequested: Boolean,
        institutionId: Int,
        bufferSize: Int,
        initialState: PageToken.TimePageToken?,
        metrics: Set<String>,
        rangeQuery: RangeQuery.TimeRangeQuery,
    ): SuspendableStream<MetricUtils.MetricsWithTimeWrapper, PageToken.TimePageToken> {
        val timeFieldName = "time"
        val tableName = "${db.config.schema}.institution_metrics"
        val metricsInClauseSqlParams = metrics.joinToString { "'${SqlUtils.escapeSql(it)}'" }

        val timeFilter = DataUtils.createTimeFilter(rangeQuery)
        val additionalFilter = { it: MetricUtils.MetricsWithTimeWrapper ->
            timeFilter.invoke(it.time)
        }

        val queryTextBuilder: QueryTextBuilder = { filter, limit ->
            val ordering = rangeQuery.pagingFrom.toSqlOrdering()

            val distinctTimestamps =
                """
               SELECT 
                   DISTINCT($timeFieldName) 
               FROM $tableName
               WHERE 
                   institution_id=$institutionId
                   AND metric IN ($metricsInClauseSqlParams) 
                   $filter
               ORDER BY $timeFieldName $ordering
               LIMIT $limit
            """

            """
                SELECT 
                    $timeFieldName, metric, value 
                FROM $tableName
                WHERE
                    institution_id=$institutionId
                    AND metric IN ($metricsInClauseSqlParams)
                    AND $timeFieldName IN (
                        $distinctTimestamps
                    )
                ORDER BY 1 $ordering
            """
        }

        return DataUtils
            .createStream(
                db = db,
                queryTextBuilder = queryTextBuilder,
                bufferSize = bufferSize,
                keyNames = arrayOf(timeFieldName),
                dataMapper = MetricUtils.createMetricsMapper(populateStatusData = false),
                rangeQuery = rangeQuery,
                initialState = initialState,
                stateResolver = { PageToken.TimePageToken(it.time) },
                streamId = if (patternRequested) institution else null,
            ).collapseByKey { it.time }
            .map { (time, singleTimeData) ->
                // convert rows (timeInstant, apiMetricName, valueString) to DataWithTimeWrapper
                MetricUtils.MetricsWithTimeWrapper(
                    time = time,
                    // Map<metricName, metricValue>
                    metrics = singleTimeData.map { row -> row.metric to row.value },
                )
            }.filter(additionalFilter)
    }
}
