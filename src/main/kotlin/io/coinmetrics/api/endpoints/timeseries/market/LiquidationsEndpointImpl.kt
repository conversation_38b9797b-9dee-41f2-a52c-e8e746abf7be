package io.coinmetrics.api.endpoints.timeseries.market

import io.coinmetrics.api.ApiError.BadParameter
import io.coinmetrics.api.ChunkedResponseFormat
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.Response
import io.coinmetrics.api.endpoints.GetTimeseriesMarketLiquidationsEndpoint
import io.coinmetrics.api.endpoints.GetTimeseriesMarketLiquidationsRequest
import io.coinmetrics.api.model.NormalizedMarket
import io.coinmetrics.api.models.MarketLiquidation
import io.coinmetrics.api.models.MarketLiquidationsResponse
import io.coinmetrics.api.models.PagingFrom
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.MarketConstraints
import io.coinmetrics.api.service.MarketResolvingService
import io.coinmetrics.api.service.getTimeseriesRateLimitHeaders
import io.coinmetrics.api.utils.BatchUtils
import io.coinmetrics.api.utils.CommonUtils
import io.coinmetrics.api.utils.DataUtils
import io.coinmetrics.api.utils.DataUtils.adjust
import io.coinmetrics.api.utils.DataUtils.parseTimeParameters
import io.coinmetrics.api.utils.QueryTextBuilder
import io.coinmetrics.api.utils.RangeQuery
import io.coinmetrics.api.utils.SqlUtils
import io.coinmetrics.api.utils.TimeUtils
import io.coinmetrics.api.utils.Utils
import io.coinmetrics.api.utils.paging.PageToken
import io.coinmetrics.api.utils.paging.getPageFlow
import io.coinmetrics.api.utils.streams.SuspendableStream
import io.coinmetrics.databases.Database
import kotlinx.coroutines.flow.map
import java.math.BigInteger
import java.sql.ResultSet
import java.time.Instant

class LiquidationsEndpointImpl(
    private val db: Database,
    private val amsService: AmsService,
    private val marketResolvingService: MarketResolvingService,
    private val communityApiKey: String,
) : GetTimeseriesMarketLiquidationsEndpoint() {
    override suspend fun handle(request: GetTimeseriesMarketLiquidationsRequest): Response<MarketLiquidationsResponse> {
        val headers =
            amsService
                .getTimeseriesRateLimitHeaders(
                    apiKey = request.apiKey,
                    httpRequest = request.httpRequest,
                ).getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }

        val (marketsConstraints, marketPatternRequested) =
            marketResolvingService
                .parseAndCheckMarkets(request.apiKey, request.markets)
                .getOrElse { return Response.errorResponse(it) }

        val (prefetch, bufferSizePerStream) = Utils.getFetchProperties(request.limitPerMarket, marketsConstraints.size, request.pageSize)

        val (startTime, endTime) =
            when (
                val result =
                    parseTimeParameters(
                        request.startTime,
                        request.startInclusive,
                        request.endTime,
                        request.endInclusive,
                        request.timezone,
                    )
            ) {
                is FunctionResult.Failure -> return Response.errorResponse(result.value)
                is FunctionResult.Success -> result.value
            }

        if (!marketPatternRequested) {
            for (marketConstraints in marketsConstraints.values) {
                marketConstraints.marketId as? NormalizedMarket.DerivativesNormalizedMarket
                    ?: return Response.errorResponse(BadParameter("markets", "Only future and option markets are supported."))
            }
        }

        return when (
            val result =
                BatchUtils.sortIdsAndConcatStreams(
                    streams = marketsConstraints.asSequence().map { it.toPair() },
                    numberOfStreamsToPrefetch = prefetch,
                    pagingFrom = request.pagingFrom,
                    nextPageToken = request.nextPageToken,
                    initialStreamStateParser = { PageToken.TimeAndBigIntegerPageToken.parse(it) },
                    limitPerStream = request.limitPerMarket,
                    streamSupplier = { id, state, marketConstraints ->
                        handleInternal(
                            id,
                            state,
                            request,
                            startTime,
                            endTime,
                            marketPatternRequested,
                            marketConstraints,
                            bufferSizePerStream,
                        )
                    },
                    streamIdsAreResolvedDynamically = marketPatternRequested,
                    httpRequestCoroutineContext = request.httpRequest.coroutineContext,
                    logger = log,
                )
        ) {
            is FunctionResult.Success -> {
                val page =
                    result.value
                        .map { it.marketLiquidation }
                        .getPageFlow(
                            request.httpRequest,
                            request.pageSize,
                            request.pagingFrom,
                        ).let {
                            if (request.format == "csv") {
                                it.map { obj ->
                                    (obj as? MarketLiquidation)?.toMap() ?: obj
                                }
                            } else {
                                it
                            }
                        }

                Response.chunkedResponse(
                    items = page,
                    headers = headers,
                    format = if (request.format == "csv") ChunkedResponseFormat.Csv(nullValue = "") else ChunkedResponseFormat.Json(),
                )
            }

            is FunctionResult.Failure -> Response.errorResponse(result.value, headers)
        }
    }

    private fun MarketLiquidation.toMap() =
        mapOf(
            "market" to market,
            "time" to time,
            "coin_metrics_id" to coinMetricsId,
            "amount" to amount,
            "price" to price,
            "type" to type,
            "database_time" to databaseTime,
            "side" to side,
        )

    private fun handleInternal(
        market: String,
        initialState: PageToken.TimeAndBigIntegerPageToken?,
        request: GetTimeseriesMarketLiquidationsRequest,
        startTime: Instant,
        endTime: Instant,
        marketPatternRequested: Boolean,
        marketConstraints: MarketConstraints,
        bufferSize: Int,
    ): SuspendableStream<MarketLiquidationWrapper, SuspendableStream.State> {
        val derivativeMarket =
            marketConstraints.marketId as? NormalizedMarket.DerivativesNormalizedMarket ?: return SuspendableStream.empty()

        val timeFieldName = "liquidation_time"
        val queryConfig =
            run {
                val symbolSqlParam = SqlUtils.escapeSql(derivativeMarket.symbol)
                QueryConfiguration(
                    db,
                    "liquidations",
                    "liquidation_symbol='$symbolSqlParam'",
                )
            }

        val (adjustedStartTime, adjustedEndTime) =
            Pair(startTime, endTime)
                .adjust(
                    DataUtils.communityEnforcedStart(request.apiKey, communityApiKey),
                    marketConstraints.minTime,
                    marketConstraints.maxTime,
                )
                /**
                 * Returning an empty stream for simplicity.
                 * We could enhance this later, for example, by returning a function failure result if a pattern was not requested —
                 * but that would require more refactoring, which may not be necessary at this point.
                 */
                ?: return SuspendableStream.empty()

        val rangeQuery =
            RangeQuery
                .TimeAndBigIntegerRangeQuery(
                    adjustedStartTime,
                    startKey2 = null,
                    request.startInclusive,
                    adjustedEndTime,
                    endKey2 = null,
                    request.endInclusive,
                    request.pagingFrom,
                ).withPageToken(initialState)

        val timeFilter = DataUtils.createTimeFilter(rangeQuery)
        val additionalFilter = { it: MarketLiquidationWrapper ->
            timeFilter.invoke(it.time)
        }

        val queryTextBuilder: QueryTextBuilder = { filter, limit ->
            val ordering = if (request.pagingFrom == PagingFrom.START) "ASC" else "DESC"
            """
            SELECT 
                $timeFieldName,
                liquidation_id,
                liquidation_exchange_id,
                liquidation_symbol,
                liquidation_amount,
                liquidation_price,
                liquidation_buy,
                liquidation_order,
                liquidation_database_time
            FROM ${queryConfig.db.config.schema}.${queryConfig.tableName}
            WHERE 
                ${queryConfig.instrumentFilter}
                AND liquidation_exchange_id=${derivativeMarket.exchange}
                $filter
            ORDER BY $timeFieldName $ordering, liquidation_id $ordering
            LIMIT $limit
            """
        }

        val stream =
            DataUtils
                .createStream(
                    db = queryConfig.db,
                    queryTextBuilder = queryTextBuilder,
                    bufferSize = bufferSize,
                    keyNames = arrayOf(timeFieldName, "liquidation_id"),
                    dataMapper = createMapper(market),
                    rangeQuery = rangeQuery,
                    initialState = initialState,
                    stateResolver = { PageToken.TimeAndBigIntegerPageToken(it.time, it.liquidationId) },
                    streamId =
                        if (marketPatternRequested) {
                            market
                        } else {
                            null
                        },
                ).filter(additionalFilter)

        return stream
    }

    private fun createMapper(market: String): (ResultSet) -> MarketLiquidationWrapper =
        { rs ->
            val time = rs.getTimestamp("liquidation_time").toInstant()
            val timeFormatted = TimeUtils.dateTimeFormatter.format(time)

            val dbTime =
                rs.getTimestamp("liquidation_database_time").toInstant().let {
                    TimeUtils.dateTimeFormatter.format(it)
                }
            val liquidationId = rs.getBigDecimal("liquidation_id").toBigInteger()
            val liquidationAmount = rs.getBigDecimal("liquidation_amount")
            val liquidationPrice = rs.getBigDecimal("liquidation_price")

            MarketLiquidationWrapper(
                time = time,
                liquidationId = liquidationId,
                MarketLiquidation(
                    time = timeFormatted,
                    market = market,
                    coinMetricsId = liquidationId.toString(),
                    // null values will be filtered later
                    amount = liquidationAmount?.let { CommonUtils.formatBigDecimal(liquidationAmount) } ?: "null",
                    price = liquidationPrice?.let { CommonUtils.formatBigDecimal(liquidationPrice) } ?: "null",
                    side = MarketEndpointUtil.sideToString(rs, "liquidation_buy"),
                    type = typeToString(rs, "liquidation_order"),
                    databaseTime = dbTime,
                ),
            )
        }

    private fun typeToString(
        rs: ResultSet,
        name: String,
    ): String {
        val order = rs.getBoolean(name)
        return if (order) {
            "order"
        } else {
            "trade"
        }
    }

    private class MarketLiquidationWrapper(
        val time: Instant,
        val liquidationId: BigInteger,
        val marketLiquidation: MarketLiquidation,
    )

    private class QueryConfiguration(
        val db: Database,
        val tableName: String,
        val instrumentFilter: String,
    )
}
