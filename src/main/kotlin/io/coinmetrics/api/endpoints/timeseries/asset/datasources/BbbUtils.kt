package io.coinmetrics.api.endpoints.timeseries.asset.datasources

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.endpoints.GetTimeseriesAssetMetricsRequest
import io.coinmetrics.api.models.PagingFrom
import io.coinmetrics.api.utils.CommonUtils
import io.coinmetrics.api.utils.DataUtils
import io.coinmetrics.api.utils.RangeQuery
import io.coinmetrics.api.utils.SqlUtils
import io.coinmetrics.api.utils.TimeUtils
import io.coinmetrics.databases.Database
import java.math.BigInteger
import java.sql.ResultSet
import java.time.Instant

object BbbUtils {
    fun createRangeQueryFromHeightBoundaries(
        pagingFrom: PagingFrom,
        request: GetTimeseriesAssetMetricsRequest,
    ): FunctionResult<ApiError, Pair<RangeQuery.BigIntegerAndStringRangeQuery, (BlockMetricWithTimeAndHeightWrapper) -> <PERSON><PERSON><PERSON>>> {
        val startHeight = request.startHeight?.toBigInteger() ?: BigInteger.ZERO

        val endHeight = request.endHeight?.toBigInteger()

        return when (
            val result =
                DataUtils.createBigIntegerAndStringRangeQuery(
                    startBigInteger = startHeight,
                    startInclusive = request.startInclusive,
                    endBigInteger = endHeight,
                    endInclusive = request.endInclusive,
                    pagingFrom,
                )
        ) {
            is FunctionResult.Success -> {
                val additionalFilter: (BlockMetricWithTimeAndHeightWrapper) -> Boolean = { true }
                FunctionResult.Success(result.value to additionalFilter)
            }

            is FunctionResult.Failure -> FunctionResult.Failure(result.value)
        }
    }

    suspend fun createRangeQueryFromHashBoundaries(
        db: Database,
        asset: String,
        pagingFrom: PagingFrom,
        request: GetTimeseriesAssetMetricsRequest,
        tempMetricSqlParam: String,
    ): FunctionResult<ApiError, Pair<RangeQuery.BigIntegerAndStringRangeQuery, (BlockMetricWithTimeAndHeightWrapper) -> Boolean>> {
        val assetSqlParam = SqlUtils.escapeSql(asset)
        val tableName = "${db.config.schema}.statistics_realtime"

        val inClauseSqlParam =
            listOfNotNull(request.startHash, request.endHash)
                .joinToString { "'${SqlUtils.escapeSql(it)}'" }

        // metric filtering is required to use PK index
        val heightMap: Map<String, BigInteger> =
            db.query(
                "SELECT block_hash, height FROM $tableName WHERE asset='$assetSqlParam' " +
                    "AND metric='$tempMetricSqlParam' AND block_hash IN ($inClauseSqlParam)",
            ) {
                it
                    .map { rs ->
                        rs.getString("block_hash") to rs.getBigDecimal("height").toBigInteger()
                    }.toMap(HashMap())
            }

        val startHeight =
            request.startHash?.let {
                heightMap[it] ?: return FunctionResult.Failure(ApiError.BadParameter("start_hash", "Block with hash '$it' not found."))
            }

        val endHeight =
            request.endHash?.let {
                heightMap[it] ?: return FunctionResult.Failure(ApiError.BadParameter("end_hash", "Block with hash '$it' not found."))
            }

        return when (
            val result =
                DataUtils.createBigIntegerAndStringRangeQuery(
                    startHeight,
                    request.startInclusive,
                    endHeight,
                    request.endInclusive,
                    pagingFrom,
                )
        ) {
            is FunctionResult.Success -> {
                val additionalFilter: (BlockMetricWithTimeAndHeightWrapper) -> Boolean = { true }
                FunctionResult.Success(result.value to additionalFilter)
            }

            is FunctionResult.Failure -> FunctionResult.Failure(result.value)
        }
    }

    suspend fun createRangeQueryFromTimeBoundaries(
        db: Database,
        asset: String,
        pagingFrom: PagingFrom,
        request: GetTimeseriesAssetMetricsRequest,
        tempMetricSqlParam: String,
    ): FunctionResult<ApiError, Pair<RangeQuery.BigIntegerAndStringRangeQuery?, ((BlockMetricWithTimeAndHeightWrapper) -> Boolean)?>> {
        val assetSqlParam = SqlUtils.escapeSql(asset)
        val tableName = "${db.config.schema}.statistics_realtime"

        // Example queries:
        // SELECT time, height FROM production.statistics_realtime WHERE asset='btc' and metric='TxCnt' AND time>='2020-01-01 00:00:00' ORDER BY time LIMIT 1;
        // SELECT time, height FROM production.statistics_realtime WHERE asset='btc' and metric='TxCnt' AND time<='2020-01-01 00:00:00' ORDER BY time DESC LIMIT 1;

        val (startTime, endTime) =
            when (
                val result =
                    DataUtils.parseTimeParameters(
                        request.startTime,
                        request.startInclusive,
                        request.endTime,
                        request.endInclusive,
                        request.timezone,
                    )
            ) {
                is FunctionResult.Success -> result.value
                is FunctionResult.Failure -> return FunctionResult.Failure(result.value)
            }

        val timeFilter =
            DataUtils.createTimeFilter(
                startTime,
                request.startInclusive,
                endTime,
                request.endInclusive,
            )
        val additionalFilter: (BlockMetricWithTimeAndHeightWrapper) -> Boolean = {
            timeFilter.invoke(it.time)
        }

        var startInclusive = request.startInclusive
        var endInclusive = request.endInclusive

        // todo make two queries at once using union
        val startTimeSqlExpression = TimeUtils.toSqlCompareExpression(">=", startTime)
        val startHeight =
            db
                .query(
                    """
                    SELECT time, height
                    FROM $tableName
                    WHERE asset='$assetSqlParam' and metric='$tempMetricSqlParam' AND time $startTimeSqlExpression
                    ORDER BY time
                    LIMIT 1                
                    """,
                ) {
                    it
                        .map { rs ->
                            rs.getTimestamp("time").toInstant() to rs.getBigDecimal("height").toBigInteger()
                        }.firstOrNull()
                }?.let { (time, height) ->
                    if (time > startTime) startInclusive = true
                    // expand range to catch bad miners timestamps
                    height - BigInteger.TWO
                } ?: run {
                // no matched rows
                return FunctionResult.Success(null to null)
            }

        val endTimeSqlExpression = TimeUtils.toSqlCompareExpression("<=", endTime)
        val endHeight =
            db
                .query(
                    """
                    SELECT time, height
                    FROM $tableName
                    WHERE asset='$assetSqlParam' and metric='$tempMetricSqlParam' AND time $endTimeSqlExpression
                    ORDER BY time DESC
                    LIMIT 1                
                    """,
                ) {
                    it
                        .map { rs ->
                            rs.getTimestamp("time").toInstant() to rs.getBigDecimal("height").toBigInteger()
                        }.firstOrNull()
                }?.let { (time, height) ->
                    if (time < endTime) endInclusive = true
                    // expand range to catch bad miners timestamps
                    height + BigInteger.TWO
                } ?: run {
                // no matched rows
                return FunctionResult.Success(null to null)
            }

        return when (
            val result =
                DataUtils.createBigIntegerAndStringRangeQuery(
                    startHeight,
                    startInclusive,
                    endHeight,
                    endInclusive,
                    pagingFrom,
                )
        ) {
            is FunctionResult.Success -> {
                FunctionResult.Success(result.value to additionalFilter)
            }

            is FunctionResult.Failure -> FunctionResult.Failure(result.value)
        }
    }

    fun createRealtimeMetricsBlocksMapper(
        withStatus: Boolean = false,
        metricColumnName: String = "metric",
    ) = { rs: ResultSet ->
        BlockMetricRow(
            hash = rs.getString("block_hash"),
            time = rs.getTimestamp("time").toInstant(),
            parentHash = rs.getString("parent_block_hash"),
            height = rs.getBigDecimal("height").toBigInteger(),
            metric = rs.getString(metricColumnName),
            value =
                if (metricColumnName == "metric") {
                    rs.getBigDecimal("value")?.let { CommonUtils.formatBigDecimal(it) }
                } else {
                    rs.getString("value")
                },
            status = if (withStatus) rs.getString("status").lowercase() else null,
            statusTime = if (withStatus) rs.getTimestamp("status_timestamp").toInstant() else null,
        )
    }

    class BlockMetricRow(
        val height: BigInteger,
        val time: Instant,
        val hash: String,
        val parentHash: String?,
        val metric: String,
        val value: String?,
        val status: String?,
        val statusTime: Instant?,
    )

    class BlockMetricWithTimeAndHeightWrapper(
        val height: BigInteger,
        val hash: String,
        val parentHash: String?,
        val time: Instant,
        val metrics: List<Pair<String, String?>>,
    )
}
