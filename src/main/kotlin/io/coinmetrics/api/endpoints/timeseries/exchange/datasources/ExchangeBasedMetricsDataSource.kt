package io.coinmetrics.api.endpoints.timeseries.exchange.datasources

import io.coinmetrics.api.models.PagingFrom
import io.coinmetrics.api.utils.CommonUtils
import io.coinmetrics.api.utils.DataUtils
import io.coinmetrics.api.utils.DataUtils.adjust
import io.coinmetrics.api.utils.MetricUtils
import io.coinmetrics.api.utils.QueryTextBuilder
import io.coinmetrics.api.utils.RangeQuery
import io.coinmetrics.api.utils.SqlUtils
import io.coinmetrics.api.utils.Utils.getBigDecimalOrNull
import io.coinmetrics.api.utils.paging.PageToken
import io.coinmetrics.api.utils.streams.SuspendableStream
import io.coinmetrics.api.utils.toSqlOrdering
import io.coinmetrics.databases.Database
import java.sql.ResultSet
import java.time.Instant
import java.time.ZoneOffset
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit

class ExchangeBasedMetricsDataSource(
    private val db: Database,
) {
    private val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd").withZone(ZoneOffset.UTC)

    fun query(
        entityId: String,
        patternRequested: Boolean,
        tableName: String,
        valueMapping: ColumnToValueMapping,
        bufferSize: Int,
        pageToken: String?,
        frequency: String,
        metrics: List<String>,
        timeRangeParams: TimeRangeParams,
        downSampleMetrics: Boolean = true,
        minDateFor1d: Instant? = null,
    ): SuspendableStream<MetricUtils.MetricsWithTimeWrapper, PageToken.TimePageToken> {
        val timeFieldName = "time"
        val fullTableName = "${db.config.schema}.$tableName"
        val metricsInClauseSqlParams = metrics.joinToString { "'${SqlUtils.escapeSql(it)}'" }

        val initialState =
            pageToken?.let {
                try {
                    PageToken.TimePageToken.parse(it)
                } catch (_: Exception) {
                    error("Can't parse provided next_page_token '$it'.")
                }
            }

        val timeZone = SqlUtils.escapeSql(timeRangeParams.timezone)

        // timezone env is required only for queries which use INTERVAL keyword
        val (beforeQuery, afterQuery) =
            if (downSampleMetrics && frequency == "1d") {
                "SET timezone='$timeZone'; SET enable_gathermerge='off';" to "SET timezone='UTC'; SET enable_gathermerge='on';"
            } else {
                null to null
            }

        val (adjustedStartTimeParam, adjustedEndTimeParam) =
            Pair(timeRangeParams.startTime, timeRangeParams.endTime)
                .adjust(
                    keyEnforcedStartTime = timeRangeParams.keyMinTime,
                    keyEnforcedEndTime = timeRangeParams.keyMaxTime,
                )
                /**
                 * Returning an empty stream for simplicity.
                 * We could enhance this later, for example, by returning a function failure result if a pattern was not requested —
                 * but that would require more refactoring, which may not be necessary at this point.
                 */
                ?: return SuspendableStream.empty()

        val rangeQuery =
            RangeQuery
                .TimeRangeQuery(
                    adjustedStartTimeParam,
                    timeRangeParams.startInclusive,
                    adjustedEndTimeParam,
                    timeRangeParams.endInclusive,
                    timeRangeParams.pagingFrom,
                ).withPageToken(initialState)

        val timeFilter = DataUtils.createTimeFilter(rangeQuery)
        val additionalFilter = { it: MetricUtils.MetricsWithTimeWrapper ->
            timeFilter.invoke(it.time)
        }

        val queryTextBuilder: QueryTextBuilder =
            { filter, limit ->
                val ordering = timeRangeParams.pagingFrom.toSqlOrdering()
                val predicate = toPredicate(valueMapping.mapping)

                if (downSampleMetrics && frequency == "1d") {
                    val interval = "1 day"
                    val requestedStartDate = rangeQuery.startKey.minus(1, ChronoUnit.DAYS)
                    val startDate: Instant = minDateFor1d?.let { maxOf(it, requestedStartDate) } ?: requestedStartDate
                    val formattedStartDate: String = formatter.format(startDate)
                    val requestedEndDate = rangeQuery.endKey.plus(1, ChronoUnit.DAYS)
                    val formattedEndDate: String = formatter.format(requestedEndDate)

                    val generateSeriesQuery =
                        if (timeRangeParams.pagingFrom == PagingFrom.START) {
                            """SELECT to_timestamp(generate_series(extract(epoch from '$formattedStartDate'::timestamp)::int8, extract(epoch from '$formattedEndDate'::timestamp)::int8, extract(epoch from '$interval'::interval)::int8)) AS $timeFieldName"""
                        } else {
                            """SELECT to_timestamp(generate_series(extract(epoch from '$formattedEndDate'::timestamp)::int8, extract(epoch from '$formattedStartDate'::timestamp)::int8, -extract(epoch from '$interval'::interval)::int8)) AS $timeFieldName"""
                        }

                    val dateSubQuery =
                        """
                        WITH relevant_dates AS (
                          SELECT
                            DISTINCT($timeFieldName) AS relevant_date
                          FROM $fullTableName
                          WHERE $predicate
                          AND metric IN ($metricsInClauseSqlParams) 
                          AND $timeFieldName IN (
                            SELECT $timeFieldName FROM (
                              $generateSeriesQuery
                            ) all_dates
                            WHERE TRUE
                            $filter
                            ORDER BY
                              time $ordering
                          )
                          $filter
                          ORDER BY $timeFieldName $ordering
                          LIMIT $limit
                        )
                        """.trimIndent()

                    """
                    $dateSubQuery
                    SELECT
                      $timeFieldName, metric, value 
                    FROM $fullTableName JOIN relevant_dates ON $timeFieldName = relevant_dates.relevant_date
                    WHERE $predicate
                    AND metric IN ($metricsInClauseSqlParams)
                    ORDER BY $timeFieldName $ordering
                    """.trimIndent()
                } else {
                    val distinctTimestamps = """
               SELECT 
                   DISTINCT($timeFieldName) 
               FROM $fullTableName
               WHERE 
                   $predicate
                   AND metric IN ($metricsInClauseSqlParams) 
                   $filter
               ORDER BY $timeFieldName $ordering
               LIMIT $limit
            """

                    """
                SELECT 
                    $timeFieldName, metric, value 
                FROM $fullTableName
                WHERE
                    $predicate
                    AND metric IN ($metricsInClauseSqlParams)
                    AND $timeFieldName IN (
                        $distinctTimestamps
                    )
                ORDER BY 1 $ordering
            """
                }
            }

        return DataUtils
            .createStream(
                db = db,
                queryTextBuilder = queryTextBuilder,
                bufferSize = bufferSize,
                keyNames = arrayOf(timeFieldName),
                dataMapper = { mapMetricRow(it) },
                rangeQuery = rangeQuery,
                initialState = initialState,
                stateResolver = { PageToken.TimePageToken(it.time) },
                beforeQuery = beforeQuery,
                afterQuery = afterQuery,
                streamId = if (patternRequested) entityId else null,
            ).collapseByKey { it.time }
            .map { (time, singleTimeData) ->
                // convert rows (timeInstant, apiMetricName, valueString) to DataWithTimeWrapper
                MetricUtils.MetricsWithTimeWrapper(
                    time = time,
                    // Map<metricName, metricValue>
                    metrics = singleTimeData.map { row -> row.metric to row.value },
                )
            }.filter(additionalFilter)
    }

    private fun mapMetricRow(resultSet: ResultSet): MetricUtils.MetricRow =
        MetricUtils.MetricRow(
            time = resultSet.getTimestamp("time").toInstant(),
            metric = resultSet.getString("metric"),
            value = resultSet.getBigDecimalOrNull("value")?.let { CommonUtils.formatBigDecimal(it) },
        )

    private fun toPredicate(mapping: Map<String, Any>) =
        mapping.entries.joinToString(" AND ") { (column, value) -> "$column='${SqlUtils.escapeSql(value.toString())}'" }

    data class TimeRangeParams(
        val startTime: Instant,
        val startInclusive: Boolean,
        val endTime: Instant,
        val endInclusive: Boolean,
        val timezone: String,
        val pageSize: Int,
        val pagingFrom: PagingFrom,
        val keyMinTime: Instant? = null,
        val keyMaxTime: Instant? = null,
    )

    interface ColumnToValueMapping {
        val mapping: Map<String, Any>
    }
}
