package io.coinmetrics.api.endpoints.timeseries.asset.datasources

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.endpoints.GetTimeseriesAssetMetricsRequest
import io.coinmetrics.api.endpoints.timeseries.asset.AssetMetricsEndpointHelper
import io.coinmetrics.api.models.PagingFrom
import io.coinmetrics.api.utils.CommonUtils
import io.coinmetrics.api.utils.DataUtils
import io.coinmetrics.api.utils.RangeQuery
import io.coinmetrics.api.utils.TimeUtils
import io.coinmetrics.databases.Database
import java.math.BigInteger
import java.sql.ResultSet
import java.time.Instant

object ChainMonitorUtils {
    // todo report "nodes" field
    val mapper = { rs: ResultSet ->
        MetricRow(
            time = rs.getTimestamp("time").toInstant(),
            metric = rs.getString("name"),
            value = CommonUtils.formatBigDecimal(rs.getBigDecimal("value")),
        )
    }

    class MetricRow(
        val time: Instant,
        val metric: String,
        val value: String,
    )

    val bbbMetricsBlocksMapper = { rs: ResultSet ->
        BbbUtils.BlockMetricRow(
            hash = rs.getString("block_hash"),
            time = rs.getTimestamp("time").toInstant(),
            parentHash = rs.getString("parent_block_hash"),
            height = rs.getBigDecimal("block_height").toBigInteger(),
            metric = rs.getString("name"),
            value = CommonUtils.formatBigDecimal(rs.getBigDecimal("value")),
            status = null,
            statusTime = null,
        )
    }

    suspend fun createRangeQueryFromTimeBoundaries(
        db: Database,
        asset: String,
        pagingFrom: PagingFrom,
        request: GetTimeseriesAssetMetricsRequest,
        dataSource: AssetMetricsEndpointHelper.DataSourceGroup,
    ): FunctionResult<
        ApiError,
        Pair<
            RangeQuery.BigIntegerAndStringRangeQuery?,
            (
                (
                    BbbUtils.BlockMetricWithTimeAndHeightWrapper,
                ) -> Boolean
            )?,
        >,
    > {
        val tableName =
            when (dataSource) {
                AssetMetricsEndpointHelper.DataSourceGroup.BBB_CHAIN_MONITOR_METRICS ->
                    "${db.config.schema}.${asset}_bbb_metrics"

                AssetMetricsEndpointHelper.DataSourceGroup.BBB_ETH_SC_METRICS ->
                    "${db.config.schema}.${asset}_sc_metrics"

                else ->
                    throw IllegalStateException("Unsupported data source '$dataSource' used as BBB metrics data source.")
            }

        val (startTime, endTime) =
            when (
                val result =
                    DataUtils.parseTimeParameters(
                        request.startTime,
                        request.startInclusive,
                        request.endTime,
                        request.endInclusive,
                        request.timezone,
                    )
            ) {
                is FunctionResult.Success -> result.value
                is FunctionResult.Failure -> return FunctionResult.Failure(result.value)
            }

        val timeFilter =
            DataUtils.createTimeFilter(
                startTime,
                request.startInclusive,
                endTime,
                request.endInclusive,
            )
        val additionalFilter: (BbbUtils.BlockMetricWithTimeAndHeightWrapper) -> Boolean = {
            timeFilter.invoke(it.time)
        }

        val startTimeSqlExpression = TimeUtils.toSqlCompareExpression(">=", startTime)
        val endTimeSqlExpression = TimeUtils.toSqlCompareExpression("<=", endTime)
        val rows =
            db.query(
                """
                    (SELECT time, block_height
                    FROM $tableName
                    WHERE time $startTimeSqlExpression
                    ORDER BY time
                    LIMIT 1)
                    UNION ALL
                    (SELECT time, block_height
                    FROM $tableName
                    WHERE time $endTimeSqlExpression
                    ORDER BY time DESC
                    LIMIT 1)
                    """,
            ) {
                it
                    .map { rs ->
                        rs.getTimestamp("time").toInstant() to rs.getBigDecimal("block_height").toBigInteger()
                    }.toList()
            }
        if (rows.size != 2) {
            return FunctionResult.Success(null to null)
        }
        val startTimeEffective = rows[0].first
        val startInclusive = request.startInclusive || startTimeEffective > startTime
        // expand range to catch bad miners timestamps
        val startHeight = rows[0].second - BigInteger.TWO

        val endTimeEffective = rows[1].first
        val endInclusive = request.endInclusive || endTimeEffective < endTime
        // expand range to catch bad miners timestamps
        val endHeight = rows[1].second + BigInteger.TWO

        return when (
            val result =
                DataUtils.createBigIntegerAndStringRangeQuery(
                    startHeight,
                    startInclusive,
                    endHeight,
                    endInclusive,
                    pagingFrom,
                )
        ) {
            is FunctionResult.Success -> {
                FunctionResult.Success(result.value to additionalFilter)
            }

            is FunctionResult.Failure -> FunctionResult.Failure(result.value)
        }
    }
}
