package io.coinmetrics.api.endpoints.timeseries.pair

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.ChunkedResponseFormat
import io.coinmetrics.api.CommonConstants
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.Response
import io.coinmetrics.api.endpoints.GetTimeseriesPairCandlesEndpoint
import io.coinmetrics.api.endpoints.GetTimeseriesPairCandlesRequest
import io.coinmetrics.api.endpoints.timeseries.pair.datasources.PairCandlesDataSource
import io.coinmetrics.api.models.PairCandle
import io.coinmetrics.api.models.PairCandlesResponse
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.getTimeseriesRateLimitHeaders
import io.coinmetrics.api.statistics.pair.PairCandlesStatistics
import io.coinmetrics.api.utils.BatchUtils
import io.coinmetrics.api.utils.CandleUtils
import io.coinmetrics.api.utils.CandleUtils.adjustForFrequency
import io.coinmetrics.api.utils.DataUtils
import io.coinmetrics.api.utils.DataUtils.adjust
import io.coinmetrics.api.utils.RangeQuery
import io.coinmetrics.api.utils.Utils
import io.coinmetrics.api.utils.WildcardUtils
import io.coinmetrics.api.utils.paging.PageToken.TimePageToken
import io.coinmetrics.api.utils.paging.getPageFlow
import kotlinx.coroutines.flow.map
import java.time.Clock

class PairCandlesEndpointImpl(
    private val amsService: AmsService,
    private val pairCandlesStatistics: PairCandlesStatistics,
    private val pairCandlesDataSource: PairCandlesDataSource,
    private val communityApiKey: String,
    private val clock: Clock,
) : GetTimeseriesPairCandlesEndpoint() {
    private val candleFrequenciesMap = CommonConstants.candleFrequenciesMap

    override suspend fun handle(request: GetTimeseriesPairCandlesRequest): Response<PairCandlesResponse> {
        val headers =
            amsService
                .getTimeseriesRateLimitHeaders(request.apiKey, request.httpRequest)
                .getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }

        val (frequency, frequencyOffset) =
            CandleUtils
                .parseFrequency(
                    requestedFrequency = request.frequency,
                    requestedTimezone = request.timezone,
                    isCommunity = communityApiKey == request.apiKey,
                ).getOrElse { return Response.errorResponse(it) }

        if (!candleFrequenciesMap.containsKey(frequency)) {
            return Response.errorResponse(ApiError.UnsupportedParameterValue("frequency", request.frequency))
        }
        amsService
            .check(request.apiKey, resource = "pair_candles", parameters = mapOf("frequency" to frequency))
            .getOrElse { (apiError) -> return Response.errorResponse(apiError, headers) }

        val pairs = request.pairs.map { it.lowercase() }.toSet()
        val (parseInfo, patternRequested) =
            WildcardUtils
                .parseRequestCompositeParameter(
                    paramName = "pairs",
                    paramValues = pairs,
                    universeOfItems = pairCandlesStatistics.getPairCandlesStatistic().keys,
                ).getOrElse { return Response.errorResponse(it) }

        val supportedPairs =
            parseInfo.flatMap { (assetPairs, patternRequested) ->
                if (patternRequested) {
                    assetPairs.filter { pairCandlesStatistics.getPairCandlesStatistic().containsKey(it) }
                } else {
                    assetPairs
                }
            }

        val (prefetch, bufferSizePerStream) = Utils.getFetchProperties(request.limitPerPair, supportedPairs.size, request.pageSize)

        val (startTime, endTime) =
            DataUtils
                .parseTimeParameters(
                    request.startTime,
                    request.startInclusive,
                    request.endTime,
                    request.endInclusive,
                    request.timezone,
                ).getOrElse { return Response.errorResponse(it) }
                .adjust(
                    communityEnforcedStartTime =
                        DataUtils.communityEnforcedStart(
                            apiKey = request.apiKey,
                            communityApiKey = communityApiKey,
                            /**
                             * MD-3542
                             * Expand community permission to timeseries/pair-candles, all assets, all frequencies to have 7 days of history (currently it is 1 day).
                             */
                            delayDays = 7,
                            clock = clock,
                        ),
                )
                /**
                 * Returning an empty response for simplicity.
                 * We could enhance this later, for example, by returning a function failure result if a pattern was not requested —
                 * but that would require more refactoring, which may not be necessary at this point.
                 */
                ?: return Response.successResponse(PairCandlesResponse(emptyList()), headers)
        val rangeQuery =
            RangeQuery
                .TimeRangeQuery(startTime, request.startInclusive, endTime, request.endInclusive, request.pagingFrom)
                .adjustForFrequency(frequencyOffset = frequencyOffset, request.timezone)

        return when (
            val result =
                BatchUtils.sortIdsAndConcatStreams(
                    streams = supportedPairs.asSequence().map { it to Unit },
                    numberOfStreamsToPrefetch = prefetch,
                    pagingFrom = request.pagingFrom,
                    nextPageToken = request.nextPageToken,
                    initialStreamStateParser = { TimePageToken.parse(it) },
                    limitPerStream = request.limitPerPair,
                    streamSupplier = { pair, state, _ ->
                        pairCandlesDataSource.query(
                            pair = pair,
                            patternRequested = patternRequested,
                            frequency = if (frequencyOffset.default) frequency else "1h",
                            frequencyOffset = frequencyOffset,
                            frequencyOffsetTimezone = frequencyOffset.forcedTimeZone ?: request.timezone,
                            rangeQuery = rangeQuery.withPageToken(state),
                            pageToken = state,
                            bufferSize = bufferSizePerStream,
                        )
                    },
                    httpRequestCoroutineContext = request.httpRequest.coroutineContext,
                    streamIdsAreResolvedDynamically = patternRequested,
                    logger = log,
                )
        ) {
            is FunctionResult.Success -> {
                val page =
                    result.value
                        .map { it.pairCandle }
                        .getPageFlow(
                            request.httpRequest,
                            request.pageSize,
                            request.pagingFrom,
                        ).let {
                            if (request.format == "csv") {
                                it.map { obj ->
                                    (obj as? PairCandle)?.toMap() ?: obj
                                }
                            } else {
                                it
                            }
                        }
                Response.chunkedResponse(
                    items = page,
                    headers = headers,
                    format = if (request.format == "csv") ChunkedResponseFormat.Csv(nullValue = "") else ChunkedResponseFormat.Json(),
                )
            }

            is FunctionResult.Failure -> Response.errorResponse(result.value, headers)
        }
    }

    private fun PairCandle.toMap() =
        mapOf(
            "pair" to pair,
            "time" to time,
            "price_open" to priceOpen,
            "price_close" to priceClose,
            "price_high" to priceHigh,
            "price_low" to priceLow,
        )
}
