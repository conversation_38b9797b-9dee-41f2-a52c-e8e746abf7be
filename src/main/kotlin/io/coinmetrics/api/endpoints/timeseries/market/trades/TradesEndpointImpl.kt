package io.coinmetrics.api.endpoints.timeseries.market.trades

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.convertValue
import io.coinmetrics.api.ChunkedResponseFormat
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.Response
import io.coinmetrics.api.endpoints.GetTimeseriesMarketTradesEndpoint
import io.coinmetrics.api.endpoints.GetTimeseriesMarketTradesRequest
import io.coinmetrics.api.endpoints.timeseries.market.trades.provider.BaseTradesDataProvider
import io.coinmetrics.api.endpoints.timeseries.market.trades.provider.DefiTradesDataProvider
import io.coinmetrics.api.endpoints.timeseries.market.trades.provider.TradesStreamSpecificData
import io.coinmetrics.api.model.NormalizedMarket
import io.coinmetrics.api.models.MarketTrade
import io.coinmetrics.api.models.MarketTradesResponse
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.MarketResolvingService
import io.coinmetrics.api.service.MarketStatisticsService
import io.coinmetrics.api.service.getTimeseriesRateLimitHeaders
import io.coinmetrics.api.utils.BatchUtils
import io.coinmetrics.api.utils.DataUtils
import io.coinmetrics.api.utils.DataUtils.adjust
import io.coinmetrics.api.utils.RangeQuery
import io.coinmetrics.api.utils.Utils
import io.coinmetrics.api.utils.paging.PageToken
import io.coinmetrics.api.utils.paging.getPageFlow
import io.coinmetrics.api.utils.streams.SuspendableStream
import io.coinmetrics.databases.Database
import io.coinmetrics.defi.client.DeFiRawDataParser
import kotlinx.coroutines.flow.map
import java.math.BigInteger
import java.time.Instant

class TradesEndpointImpl(
    private val communityApiKey: String,
    private val objectMapper: ObjectMapper,
    private val tradesSpotDb: Database,
    private val tradesDerivDb: Database,
    private val defiDb: Database?,
    private val amsService: AmsService,
    private val marketResolvingService: MarketResolvingService,
    private val marketStatisticsService: MarketStatisticsService,
    deFiRawDataParser: DeFiRawDataParser,
) : GetTimeseriesMarketTradesEndpoint() {
    private val baseTradesDataProvider = BaseTradesDataProvider()
    private val defiTradesDataProvider = DefiTradesDataProvider(deFiRawDataParser, marketStatisticsService)

    override suspend fun handle(request: GetTimeseriesMarketTradesRequest): Response<MarketTradesResponse> {
        val headers =
            amsService
                .getTimeseriesRateLimitHeaders(
                    apiKey = request.apiKey,
                    httpRequest = request.httpRequest,
                ).getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }

        val (marketsConstraints, marketPatternRequested) =
            marketResolvingService
                .parseAndCheckMarkets(request.apiKey, request.markets)
                .getOrElse { return Response.errorResponse(it) }

        val (prefetch, bufferSizePerStream) = Utils.getFetchProperties(request.limitPerMarket, marketsConstraints.size, request.pageSize)

        val (startTime, endTime) =
            DataUtils
                .parseTimeParameters(
                    request.startTime,
                    request.startInclusive,
                    request.endTime,
                    request.endInclusive,
                    request.timezone,
                ).getOrElse { return Response.errorResponse(it) }

        val streamSpecificData =
            marketsConstraints.mapNotNull { (id, marketConstraints) ->
                marketStatisticsService.getTradesStatistics(marketConstraints.parsedMarket) ?: return@mapNotNull null

                id to
                    when (marketConstraints.marketId) {
                        is NormalizedMarket.DerivativesNormalizedMarket -> {
                            TradesStreamSpecificData.Cex(
                                marketConstraints,
                                tradesDerivDb,
                            )
                        }
                        is NormalizedMarket.SpotNormalizedMarket -> {
                            TradesStreamSpecificData.Cex(
                                marketConstraints,
                                tradesSpotDb,
                            )
                        }
                        is NormalizedMarket.DefiNormalizedMarket -> {
                            defiTradesDataProvider
                                .checkParameters(marketConstraints, marketPatternRequested)
                                .getOrElse { return Response.errorResponse(it) }
                                ?: return@mapNotNull null
                        }
                    }
            }

        return when (
            val result =
                BatchUtils.sortIdsAndConcatStreams(
                    streams = streamSpecificData.asSequence(),
                    numberOfStreamsToPrefetch = prefetch,
                    pagingFrom = request.pagingFrom,
                    nextPageToken = request.nextPageToken,
                    initialStreamStateParser = { PageToken.TimeAndBigIntegerPageToken.parse(it) },
                    limitPerStream = request.limitPerMarket,
                    streamSupplier = { id, state, streamSpecificData ->
                        handleInternal(
                            state,
                            request,
                            marketPatternRequested,
                            bufferSizePerStream,
                            startTime,
                            endTime,
                            streamSpecificData,
                        )
                    },
                    streamIdsAreResolvedDynamically = marketPatternRequested,
                    httpRequestCoroutineContext = request.httpRequest.coroutineContext,
                    logger = log,
                )
        ) {
            is FunctionResult.Success -> {
                val page =
                    result.value
                        .map { it.marketTrade }
                        .getPageFlow(
                            request.httpRequest,
                            request.pageSize,
                            request.pagingFrom,
                        ).let {
                            if (request.format == "csv") {
                                it.map { obj ->
                                    (obj as? MarketTrade)?.toMap() ?: obj
                                }
                            } else {
                                it
                            }
                        }

                Response.chunkedResponse(
                    items = page,
                    headers = headers,
                    format = if (request.format == "csv") ChunkedResponseFormat.Csv() else ChunkedResponseFormat.Json(),
                )
            }

            is FunctionResult.Failure -> Response.errorResponse(result.value, headers)
        }
    }

    private suspend fun handleInternal(
        initialState: PageToken.TimeAndBigIntegerPageToken?,
        request: GetTimeseriesMarketTradesRequest,
        marketPatternRequested: Boolean,
        bufferSize: Int,
        startTime: Instant,
        endTime: Instant,
        streamSpecificData: TradesStreamSpecificData,
    ): SuspendableStream<MarketTradeWrapper, SuspendableStream.State> {
        val (adjustedStartTime, adjustedEndTime) =
            Pair(startTime, endTime)
                .adjust(
                    DataUtils.communityEnforcedStart(request.apiKey, communityApiKey),
                    streamSpecificData.marketConstraints.minTime,
                    streamSpecificData.marketConstraints.maxTime,
                )
                /**
                 * Returning an empty stream for simplicity.
                 * We could enhance this later, for example, by returning a function failure result if a pattern was not requested —
                 * but that would require more refactoring, which may not be necessary at this point.
                 */
                ?: return SuspendableStream.empty()

        val rangeQuery =
            RangeQuery
                .TimeAndBigIntegerRangeQuery(
                    adjustedStartTime,
                    startKey2 = null,
                    request.startInclusive,
                    adjustedEndTime,
                    endKey2 = null,
                    request.endInclusive,
                    request.pagingFrom,
                ).withPageToken(initialState)

        return when (streamSpecificData) {
            is TradesStreamSpecificData.Cex ->
                baseTradesDataProvider.provide(
                    request,
                    streamSpecificData,
                    marketPatternRequested,
                    bufferSize,
                    initialState,
                    rangeQuery,
                )
            is TradesStreamSpecificData.Defi ->
                defiDb?.let {
                    defiTradesDataProvider.provide(
                        defiDb,
                        request,
                        marketPatternRequested,
                        bufferSize,
                        initialState,
                        rangeQuery,
                        streamSpecificData,
                    )
                } ?: SuspendableStream.empty()
        }
    }

    private fun MarketTrade.toMap(): Map<String, String?> {
        val marketTradeAsMap = objectMapper.convertValue<LinkedHashMap<String, String?>>(this)
        val mapKeys = ArrayList(marketTradeAsMap.keys)
        mapKeys.forEach { key -> marketTradeAsMap.putIfAbsent(key, "") }
        return marketTradeAsMap
    }
}

class MarketTradeWrapper(
    val time: Instant,
    val coinMetricsId: BigInteger,
    val marketTrade: MarketTrade,
)
