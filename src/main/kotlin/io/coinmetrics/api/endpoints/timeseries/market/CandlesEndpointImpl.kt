package io.coinmetrics.api.endpoints.timeseries.market

import io.coinmetrics.api.ApiError.BadParameter
import io.coinmetrics.api.ChunkedResponseFormat
import io.coinmetrics.api.CommonConstants.candleFrequenciesMap
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.Response
import io.coinmetrics.api.endpoints.GetTimeseriesMarketCandlesEndpoint
import io.coinmetrics.api.endpoints.GetTimeseriesMarketCandlesRequest
import io.coinmetrics.api.endpoints.timeseries.market.datasources.MarketCandlesDelayedDataSource
import io.coinmetrics.api.endpoints.timeseries.market.datasources.MarketCandlesNonDelayedDataSource
import io.coinmetrics.api.endpoints.timeseries.market.datasources.MarketCandlesUtil
import io.coinmetrics.api.endpoints.timeseries.market.model.MarketCandlesWrapper
import io.coinmetrics.api.models.MarketCandle
import io.coinmetrics.api.models.MarketCandlesResponse
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.MarketConstraints
import io.coinmetrics.api.service.MarketResolvingService
import io.coinmetrics.api.service.MarketStatisticsService
import io.coinmetrics.api.service.getTimeseriesRateLimitHeaders
import io.coinmetrics.api.utils.BatchUtils
import io.coinmetrics.api.utils.CandleUtils
import io.coinmetrics.api.utils.CandleUtils.adjustForFrequency
import io.coinmetrics.api.utils.DataUtils
import io.coinmetrics.api.utils.DataUtils.adjust
import io.coinmetrics.api.utils.RangeQuery
import io.coinmetrics.api.utils.TimeUtils
import io.coinmetrics.api.utils.Utils
import io.coinmetrics.api.utils.paging.PageToken
import io.coinmetrics.api.utils.paging.getPageFlow
import io.coinmetrics.api.utils.streams.SuspendableStream
import kotlinx.coroutines.flow.map
import java.time.Clock
import java.time.Duration
import java.time.temporal.ChronoUnit

class CandlesEndpointImpl(
    private val clock: Clock,
    private val dataSourceDelayed: MarketCandlesDelayedDataSource,
    private val dataSourceNonDelayed: MarketCandlesNonDelayedDataSource,
    private val amsService: AmsService,
    private val marketResolvingService: MarketResolvingService,
    private val communityApiKey: String,
    private val marketStatisticsService: MarketStatisticsService,
) : GetTimeseriesMarketCandlesEndpoint() {
    private val frequencySupportedValuesStr = candleFrequenciesMap.keys.joinToString(separator = ", ") { "'$it'" }

    override suspend fun handle(request: GetTimeseriesMarketCandlesRequest): Response<MarketCandlesResponse> {
        val headers =
            amsService
                .getTimeseriesRateLimitHeaders(
                    apiKey = request.apiKey,
                    httpRequest = request.httpRequest,
                ).getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }

        val (frequency, frequencyOffset) =
            CandleUtils
                .parseFrequency(
                    requestedFrequency = request.frequency,
                    requestedTimezone = request.timezone,
                    isCommunity = communityApiKey == request.apiKey,
                ).getOrElse { return Response.errorResponse(it) }

        if (!candleFrequenciesMap.containsKey(frequency)) {
            return Response.errorResponse(
                BadParameter(
                    "frequency",
                    "Unsupported candle frequency. Supported values are $frequencySupportedValuesStr.",
                ),
            )
        }

        val (marketsConstraints, marketPatternRequested) =
            marketResolvingService
                .parseAndCheckMarkets(request.apiKey, request.markets)
                .getOrElse { return Response.errorResponse(it) }

        // TODO: prohibit the usage of custom offsets in combination with market patterns for now
        //       until we verify that there is no performance implications, or we implement chunked responses
        if (marketPatternRequested && !frequencyOffset.default) {
            return Response.errorResponse(
                BadParameter(
                    "markets",
                    "Market patterns are not supported for '1d-HH:00' frequencies",
                ),
            )
        }

        val (prefetch, bufferSizePerStream) =
            Utils.getFetchProperties(request.limitPerMarket, marketsConstraints.size, request.pageSize)

        val unadjustedTiming =
            DataUtils
                .parseTimeParameters(
                    request.startTime,
                    request.startInclusive,
                    request.endTime,
                    request.endInclusive,
                    request.timezone,
                ).getOrElse { return Response.errorResponse(it) }

        return when (
            val result =
                BatchUtils.sortIdsAndConcatStreams(
                    streams = marketsConstraints.asSequence().map { it.toPair() },
                    numberOfStreamsToPrefetch = prefetch,
                    pagingFrom = request.pagingFrom,
                    nextPageToken = request.nextPageToken,
                    initialStreamStateParser = { PageToken.TimePageToken.parse(it) },
                    limitPerStream = request.limitPerMarket,
                    streamSupplier = { id, state, marketConstraints ->
                        val (startTime, endTime) =
                            unadjustedTiming
                                .adjust(
                                    if (frequency != "1d") {
                                        DataUtils.communityEnforcedStart(request.apiKey, communityApiKey)
                                    } else {
                                        null
                                    },
                                    marketConstraints.minTime,
                                    marketConstraints.maxTime,
                                )
                                /**
                                 * Returning an empty stream for simplicity.
                                 * We could enhance this later, for example, by returning a function failure result if a pattern was not requested —
                                 * but that would require more refactoring, which may not be necessary at this point.
                                 */
                                ?: return@sortIdsAndConcatStreams SuspendableStream.empty<MarketCandlesWrapper, PageToken.TimePageToken>()

                        val rangeQuery =
                            RangeQuery
                                .TimeRangeQuery(
                                    startTime,
                                    request.startInclusive,
                                    endTime,
                                    request.endInclusive,
                                    request.pagingFrom,
                                ).adjustForFrequency(frequencyOffset = frequencyOffset, request.timezone)
                                .withPageToken(state)

                        handleInternal(
                            id,
                            frequency,
                            frequencyOffset,
                            state,
                            request,
                            rangeQuery,
                            marketPatternRequested,
                            marketConstraints,
                            bufferSizePerStream,
                        )
                    },
                    streamIdsAreResolvedDynamically = marketPatternRequested,
                    httpRequestCoroutineContext = request.httpRequest.coroutineContext,
                    logger = log,
                )
        ) {
            is FunctionResult.Success -> {
                val page =
                    result.value
                        .map { it.marketCandle }
                        .getPageFlow(
                            request.httpRequest,
                            request.pageSize,
                            request.pagingFrom,
                        ).let {
                            if (request.format == "csv") {
                                it.map { obj ->
                                    (obj as? MarketCandle)?.toMap() ?: obj
                                }
                            } else {
                                it
                            }
                        }

                Response.chunkedResponse(
                    items = page,
                    headers = headers,
                    format = if (request.format == "csv") ChunkedResponseFormat.Csv(nullValue = "") else ChunkedResponseFormat.Json(),
                )
            }

            is FunctionResult.Failure -> Response.errorResponse(result.value, headers)
        }
    }

    private fun MarketCandle.toMap() =
        mapOf(
            "market" to market,
            "time" to time,
            "price_open" to priceOpen,
            "price_close" to priceClose,
            "price_high" to priceHigh,
            "price_low" to priceLow,
            "vwap" to vwap,
            "volume" to volume,
            "candle_usd_volume" to candleUsdVolume,
            "candle_trades_count" to candleTradesCount,
        )

    /**
     * 20 minutes is the delayed candle delay
     */
    private val delayedCandleDelayMin = 20L

    /**
     * Minimal difference between candle_end_time and candle_start_time
     */
    private val minCandleTimeStepMinutes = 1L

    /**
     * This optimization eliminates the need for a nested query to the delayed candle table.
     * It allows us to query data exclusively from the non-delayed candle table.
     */
    private val candleIntervalToDefaultNonDelayedLookBack: Map<String, Duration> =
        MarketCandlesUtil.candleIntervalToMinutesMap
            .map { (interval, minutes) ->
                val duration = Duration.ofMinutes(delayedCandleDelayMin + minutes - minCandleTimeStepMinutes)
                interval to duration
            }.toMap()

    private fun handleInternal(
        market: String,
        requestedFrequency: String,
        frequencyOffset: TimeUtils.NormalizedFrequencyOffset,
        initialState: PageToken.TimePageToken?,
        request: GetTimeseriesMarketCandlesRequest,
        rangeQuery: RangeQuery.TimeRangeQuery,
        marketPatternRequested: Boolean,
        marketConstraints: MarketConstraints,
        bufferSize: Int,
    ): SuspendableStream<MarketCandlesWrapper, PageToken.TimePageToken> {
        val frequencyDatasource = if (frequencyOffset.default) requestedFrequency else "1h"
        val (marketId, _, _, parsedMarket) = marketConstraints

        if (marketStatisticsService.getCandlesStatistics(parsedMarket)[frequencyDatasource] == null) {
            return SuspendableStream.empty()
        }

        if (!frequencyOffset.default) {
            // We don't use tiers for non-default frequency offsets as we utilize aggregated data there.
            val stream =
                dataSourceDelayed
                    .query1dCustomOffset(
                        market = market,
                        marketId = marketId,
                        marketPatternRequested = marketPatternRequested,
                        frequency = frequencyDatasource,
                        frequencyOffset = frequencyOffset,
                        frequencyOffsetTimezone = frequencyOffset.forcedTimeZone ?: request.timezone,
                        rangeQuery = rangeQuery,
                        pageToken = initialState,
                        bufferSize = bufferSize,
                    )

            return stream
        }

        /**
         * We should proceed only with the default offset because the non-default one should be processed earlier
         */
        assert(frequencyOffset.default)

        /**
         * If the user requests candles for a range with a start time within "candle_start_time"s for the last 20 "candle_end_time" minutes, querying delayed candles is not necessary.
         * In short, within the non-delayed candle range.
         */
        val nonDelayedStartTime =
            clock
                .instant()
                .truncatedTo(ChronoUnit.MINUTES)
                .minus(candleIntervalToDefaultNonDelayedLookBack.getValue(frequencyDatasource))
        if (rangeQuery.startKey >= nonDelayedStartTime) {
            val stream =
                dataSourceNonDelayed
                    .query(
                        market = market,
                        marketId = marketId,
                        marketPatternRequested = marketPatternRequested,
                        frequency = frequencyDatasource,
                        rangeQuery = rangeQuery,
                        pageToken = initialState,
                        bufferSize = bufferSize,
                    )
            return stream
        }

        val stream =
            dataSourceDelayed
                .query(
                    market = market,
                    marketId = marketId,
                    marketPatternRequested = marketPatternRequested,
                    frequency = frequencyDatasource,
                    rangeQuery = rangeQuery,
                    pageToken = initialState,
                    bufferSize = bufferSize,
                )

        return stream
    }
}
