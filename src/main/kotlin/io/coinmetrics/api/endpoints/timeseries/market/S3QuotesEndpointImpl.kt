package io.coinmetrics.api.endpoints.timeseries.market

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import io.coinmetrics.api.ChunkedResponseFormat
import io.coinmetrics.api.Response
import io.coinmetrics.api.endpoints.GetTimeseriesMarketOrderbooksRequest
import io.coinmetrics.api.endpoints.GetTimeseriesMarketQuotesEndpoint
import io.coinmetrics.api.endpoints.GetTimeseriesMarketQuotesRequest
import io.coinmetrics.api.models.MarketOrderBook
import io.coinmetrics.api.models.MarketQuote
import io.coinmetrics.api.models.MarketQuotesResponse
import io.coinmetrics.api.models.PagingFrom
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.MarketResolvingService
import io.coinmetrics.api.service.getTimeseriesRateLimitHeaders
import io.coinmetrics.api.utils.paging.getPageFlow
import io.coinmetrics.api.utils.streams.SuspendableStream

class S3QuotesEndpointImpl(
    private val objectMapper: ObjectMapper,
    private val amsService: AmsService,
    private val marketResolvingService: MarketResolvingService,
    private val s3OrderBookEndpointImpl: S3OrderBooksEndpointImpl,
) : GetTimeseriesMarketQuotesEndpoint() {
    override suspend fun handle(request: GetTimeseriesMarketQuotesRequest): Response<MarketQuotesResponse> {
        val headers =
            amsService
                .getTimeseriesRateLimitHeaders(
                    apiKey = request.apiKey,
                    httpRequest = request.httpRequest,
                ).getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }

        val (marketsConstraints, marketPatternRequested) =
            marketResolvingService
                .parseAndCheckMarkets(request.apiKey, request.markets)
                .getOrElse { return Response.errorResponse(it) }

        val patchedRequest =
            if (request.format == "json_stream") {
                request.copy(
                    pagingFrom = PagingFrom.START,
                    nextPageToken = null,
                )
            } else {
                request
            }

        return s3OrderBookEndpointImpl
            .buildResponseStream(
                GetTimeseriesMarketOrderbooksRequest(
                    httpRequest = patchedRequest.httpRequest,
                    apiKey = patchedRequest.apiKey,
                    markets = patchedRequest.markets,
                    granularity = patchedRequest.granularity,
                    startTime = patchedRequest.startTime,
                    endTime = patchedRequest.endTime,
                    startInclusive = patchedRequest.startInclusive,
                    endInclusive = patchedRequest.endInclusive,
                    timezone = patchedRequest.timezone,
                    pageSize = patchedRequest.pageSize,
                    depthLimit = "1",
                    pagingFrom = patchedRequest.pagingFrom,
                    limitPerMarket = patchedRequest.limitPerMarket,
                    pretty = patchedRequest.pretty,
                    nextPageToken = patchedRequest.nextPageToken,
                ),
                marketsConstraints,
                marketPatternRequested,
            ).getOrElse { return Response.errorResponse(it, headers) }
            .parseJson()
            .filter { orderBook ->
                patchedRequest.includeOneSided || (orderBook.bids.isNotEmpty() && orderBook.asks.isNotEmpty())
            }.map { orderBook ->
                val bid = orderBook.bids.firstOrNull()
                val ask = orderBook.asks.firstOrNull()
                MarketQuote(
                    time = orderBook.time,
                    market = orderBook.market,
                    coinMetricsId = orderBook.coinMetricsId,
                    askPrice = ask?.price,
                    askSize = ask?.propertySize,
                    bidPrice = bid?.price,
                    bidSize = bid?.propertySize,
                )
            }.let { result ->
                if (patchedRequest.format == "json_stream") {
                    Response.chunkedResponse(
                        items = result.asFlow(),
                        headers = headers,
                        format = ChunkedResponseFormat.JsonStream,
                    )
                } else {
                    // json
                    val pageFlow =
                        result.getPageFlow(
                            patchedRequest.httpRequest,
                            patchedRequest.pageSize,
                            patchedRequest.pagingFrom,
                        )
                    Response.chunkedResponse(
                        items = pageFlow,
                        headers = headers,
                        format = ChunkedResponseFormat.Json(),
                    )
                }
            }
    }

    private fun <S : SuspendableStream.State> SuspendableStream<ByteArray, S>.parseJson(): SuspendableStream<MarketOrderBook, S> =
        map {
            objectMapper.readValue<MarketOrderBook>(it)
        }
}
