package io.coinmetrics.api.endpoints.timeseries.index

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.ApiError.UnsupportedParameterValue
import io.coinmetrics.api.ChunkedResponseFormat
import io.coinmetrics.api.CommonConstants
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.Response
import io.coinmetrics.api.endpoints.GetTimeseriesIndexCandlesEndpoint
import io.coinmetrics.api.endpoints.GetTimeseriesIndexCandlesRequest
import io.coinmetrics.api.endpoints.timeseries.index.IndexEndpointUtils.unrestrictedCandleFrequencies
import io.coinmetrics.api.endpoints.timeseries.index.datasource.IndexCandlesDataSource
import io.coinmetrics.api.models.IndexCandle
import io.coinmetrics.api.models.IndexCandlesResponse
import io.coinmetrics.api.models.PagingFrom
import io.coinmetrics.api.resources.IndexInfo
import io.coinmetrics.api.resources.Resources
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.IndexDiscoveryService
import io.coinmetrics.api.service.getTimeseriesRateLimitHeaders
import io.coinmetrics.api.statistics.index.IndexCandlesStatistics
import io.coinmetrics.api.toFailure
import io.coinmetrics.api.toSuccess
import io.coinmetrics.api.utils.BatchUtils
import io.coinmetrics.api.utils.CandleUtils
import io.coinmetrics.api.utils.CandleUtils.adjustForFrequency
import io.coinmetrics.api.utils.DataUtils
import io.coinmetrics.api.utils.DataUtils.adjust
import io.coinmetrics.api.utils.RangeQuery
import io.coinmetrics.api.utils.Utils
import io.coinmetrics.api.utils.WildcardUtils
import io.coinmetrics.api.utils.paging.PageToken
import io.coinmetrics.api.utils.paging.getPageFlow
import kotlinx.coroutines.flow.map

class GetTimeseriesIndexCandlesEndpointImpl(
    private val amsService: AmsService,
    private val indexCandlesStatistics: IndexCandlesStatistics,
    private val indexCandlesDataSource: IndexCandlesDataSource,
    private val communityApiKey: String,
    private val indexDiscoveryService: IndexDiscoveryService,
) : GetTimeseriesIndexCandlesEndpoint() {
    override suspend fun handle(request: GetTimeseriesIndexCandlesRequest): Response<IndexCandlesResponse> {
        val headers =
            amsService
                .getTimeseriesRateLimitHeaders(request.apiKey, request.httpRequest)
                .getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }

        val (frequency, normalizedFrequencyOffset) =
            CandleUtils
                .parseFrequency(
                    requestedFrequency = request.frequency,
                    requestedTimezone = request.timezone,
                    isCommunity = communityApiKey == request.apiKey,
                ).getOrElse { return Response.errorResponse(it) }

        if (!CommonConstants.candleFrequenciesMap.containsKey(frequency)) {
            return Response.errorResponse(UnsupportedParameterValue("frequency", request.frequency))
        }

        val (parseInfo, patternRequested) =
            WildcardUtils
                .parseRequestCompositeParameter(
                    paramName = "indexes",
                    paramValues = request.indexes.map { it.uppercase() }.toHashSet(),
                    universeOfItems = indexCandlesStatistics.getIndexCandlesStatistics().keys,
                ).getOrElse { return Response.errorResponse(it) }

        val supportedIndexes =
            parseInfo
                .flatMap { (indexes, unwrappedFromPattern) ->
                    validate(
                        indexes,
                        unwrappedFromPattern,
                        request.apiKey,
                        frequency,
                    ).getOrElse { return Response.errorResponse(it) }.entries
                }.associate { entry -> entry.key to entry.value }

        val (prefetch, bufferSizePerStream) =
            Utils.getFetchProperties(
                request.limitPerIndex,
                supportedIndexes.size,
                request.pageSize,
            )

        val enforcedTime =
            if (unrestrictedCandleFrequencies.contains(frequency)) {
                null
            } else {
                DataUtils.communityEnforcedStart(request.apiKey, communityApiKey)
            }
        val (startTime, endTime, pagingFromStart) =
            DataUtils
                .parseAndValidateTimeParameters(
                    request.startTime,
                    request.startInclusive,
                    request.endTime,
                    request.endInclusive,
                    request.timezone,
                    request.pageSize,
                    request.pagingFrom,
                ).getOrElse { return Response.errorResponse(it) }
                .adjust(enforcedTime)
                /**
                 * Returning an empty response for simplicity.
                 * We could enhance this later, for example, by returning a function failure result if a pattern was not requested —
                 * but that would require more refactoring, which may not be necessary at this point.
                 */
                ?: return Response.successResponse(IndexCandlesResponse(emptyList()), headers)
        val rangeQuery =
            RangeQuery
                .TimeRangeQuery(startTime, request.startInclusive, endTime, request.endInclusive, pagingFromStart)
                .adjustForFrequency(normalizedFrequencyOffset, request.timezone)

        return when (
            val result =
                BatchUtils.sortIdsAndConcatStreams(
                    streams = supportedIndexes.asSequence().map { it.toPair() },
                    numberOfStreamsToPrefetch = prefetch,
                    pagingFromStart = request.pagingFrom == PagingFrom.START,
                    nextPageToken = request.nextPageToken,
                    initialStreamStateParser = { PageToken.TimePageToken.parse(it) },
                    limitPerStream = request.limitPerIndex,
                    streamSupplier = { index, initialState, indexInfo ->
                        indexCandlesDataSource.query(
                            index = indexInfo,
                            patternRequested = patternRequested,
                            frequency = frequency,
                            frequencyOffset = normalizedFrequencyOffset,
                            frequencyOffsetTimezone = normalizedFrequencyOffset.forcedTimeZone ?: request.timezone,
                            rangeQuery = rangeQuery.withPageToken(initialState),
                            pageToken = initialState,
                            bufferSize = bufferSizePerStream,
                        )
                    },
                    httpRequestCoroutineContext = request.httpRequest.coroutineContext,
                    streamIdsAreResolvedDynamically = patternRequested,
                    logger = log,
                )
        ) {
            is FunctionResult.Success -> {
                val page =
                    result.value
                        .map { it.candles }
                        .getPageFlow(request.httpRequest, request.pageSize, request.pagingFrom == PagingFrom.START)
                        .let { flow ->
                            if (request.format == "csv") {
                                flow.map { obj ->
                                    (obj as? IndexCandle)?.toMap() ?: obj
                                }
                            } else {
                                flow
                            }
                        }

                Response.chunkedResponse(
                    items = page,
                    headers = headers,
                    format = if (request.format == "csv") ChunkedResponseFormat.Csv(nullValue = "") else ChunkedResponseFormat.Json(),
                )
            }

            is FunctionResult.Failure -> Response.errorResponse(result.value, headers)
        }
    }

    private fun IndexCandle.toMap() =
        mapOf(
            "index" to index,
            "time" to time,
            "price_open" to priceOpen,
            "price_close" to priceClose,
            "price_high" to priceHigh,
            "price_low" to priceLow,
            "candle_trades_count" to candleTradesCount,
        )

    private suspend fun validate(
        indexes: List<String>,
        patternRequested: Boolean,
        apiKey: String,
        frequency: String,
    ): FunctionResult<ApiError, Map<String, IndexInfo>> {
        val discoveryResultPerIndex =
            indexDiscoveryService
                .discoverIndexes(
                    apiKey = apiKey,
                    requestedIndexes = indexes,
                    ignoreUnsupportedAndForbidden = patternRequested,
                    ignoreDiscoveryScript = true,
                ).getOrElse {
                    return it.toFailure()
                }.associateBy { (index) -> index }

        val forbiddenIndexes: Set<String> =
            if (discoveryResultPerIndex.size < indexes.size) {
                indexes.minus(discoveryResultPerIndex.keys).toSet()
            } else {
                emptySet()
            }

        if (forbiddenIndexes.isNotEmpty() && !patternRequested) {
            val indexesStr = forbiddenIndexes.take(3).joinToString(", ")
            return FunctionResult.Failure(
                ApiError.ForbiddenWithMessage(
                    "Requested indexes are not available with supplied credentials: $indexesStr.",
                ),
            )
        }

        return indexes
            .minus(forbiddenIndexes)
            .map { index ->
                val statisticsList =
                    indexCandlesStatistics.getIndexCandlesStatistics()[index]
                        ?: return UnsupportedParameterValue("indexes", index).toFailure()
                if (statisticsList.none { it.frequency == frequency }) {
                    return ApiError
                        .BadParameters("Candles for index $index and frequency $frequency are not supported")
                        .toFailure()
                }
                val indexInfo =
                    Resources.getIndexInfo(index).getOrElse { errorMessage ->
                        log.warn(errorMessage)
                        return UnsupportedParameterValue("indexes", index).toFailure()
                    }
                index to indexInfo
            }.associate { it }
            .toSuccess()
    }
}
