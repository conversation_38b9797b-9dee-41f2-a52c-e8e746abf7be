package io.coinmetrics.api.endpoints.timeseries.market

import io.coinmetrics.api.ApiError.BadParameter
import io.coinmetrics.api.ChunkedResponseFormat
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.Response
import io.coinmetrics.api.endpoints.GetTimeseriesMarketFundingRatesEndpoint
import io.coinmetrics.api.endpoints.GetTimeseriesMarketFundingRatesRequest
import io.coinmetrics.api.model.NormalizedMarket
import io.coinmetrics.api.models.MarketFundingRate
import io.coinmetrics.api.models.MarketFundingRatesResponse
import io.coinmetrics.api.models.PagingFrom
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.MarketConstraints
import io.coinmetrics.api.service.MarketResolvingService
import io.coinmetrics.api.service.getTimeseriesRateLimitHeaders
import io.coinmetrics.api.utils.BatchUtils
import io.coinmetrics.api.utils.CommonUtils
import io.coinmetrics.api.utils.DataUtils
import io.coinmetrics.api.utils.DataUtils.adjust
import io.coinmetrics.api.utils.QueryTextBuilder
import io.coinmetrics.api.utils.RangeQuery
import io.coinmetrics.api.utils.SqlUtils
import io.coinmetrics.api.utils.TimeUtils
import io.coinmetrics.api.utils.Utils
import io.coinmetrics.api.utils.paging.PageToken
import io.coinmetrics.api.utils.paging.getPageFlow
import io.coinmetrics.api.utils.streams.SuspendableStream
import io.coinmetrics.databases.Database
import kotlinx.coroutines.flow.map
import java.sql.ResultSet
import java.time.Instant

class FundingRatesEndpointImpl(
    private val db: Database,
    private val amsService: AmsService,
    private val marketResolvingService: MarketResolvingService,
    private val communityApiKey: String,
) : GetTimeseriesMarketFundingRatesEndpoint() {
    override suspend fun handle(request: GetTimeseriesMarketFundingRatesRequest): Response<MarketFundingRatesResponse> {
        val headers =
            amsService
                .getTimeseriesRateLimitHeaders(
                    apiKey = request.apiKey,
                    httpRequest = request.httpRequest,
                ).getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }

        val (marketsConstraints, marketPatternRequested) =
            marketResolvingService
                .parseAndCheckMarkets(request.apiKey, request.markets)
                .getOrElse { return Response.errorResponse(it) }

        val (prefetch, bufferSizePerStream) = Utils.getFetchProperties(request.limitPerMarket, marketsConstraints.size, request.pageSize)

        val (startTime, endTime) =
            DataUtils
                .parseTimeParameters(
                    request.startTime,
                    request.startInclusive,
                    request.endTime,
                    request.endInclusive,
                    request.timezone,
                ).getOrElse { return Response.errorResponse(it) }

        if (!marketPatternRequested) {
            for (marketConstraints in marketsConstraints.values) {
                marketConstraints.marketId as? NormalizedMarket.DerivativesNormalizedMarket
                    ?: return Response.errorResponse(BadParameter("markets", "Only future and option markets are supported."))
            }
        }

        return when (
            val result =
                BatchUtils.sortIdsAndConcatStreams(
                    streams = marketsConstraints.asSequence().map { it.toPair() },
                    numberOfStreamsToPrefetch = prefetch,
                    pagingFrom = request.pagingFrom,
                    nextPageToken = request.nextPageToken,
                    initialStreamStateParser = { PageToken.TimePageToken.parse(it) },
                    limitPerStream = request.limitPerMarket,
                    streamSupplier = { id, state, marketConstraints ->
                        handleInternal(
                            id,
                            state,
                            request,
                            startTime,
                            endTime,
                            marketPatternRequested,
                            bufferSizePerStream,
                            marketConstraints,
                        )
                    },
                    streamIdsAreResolvedDynamically = marketPatternRequested,
                    httpRequestCoroutineContext = request.httpRequest.coroutineContext,
                    logger = log,
                )
        ) {
            is FunctionResult.Success -> {
                val page =
                    result.value
                        .map { it.marketFundingRate }
                        .getPageFlow(
                            request.httpRequest,
                            request.pageSize,
                            request.pagingFrom,
                        ).let {
                            if (request.format == "csv") {
                                it.map { obj ->
                                    (obj as? MarketFundingRate)?.toMap() ?: obj
                                }
                            } else {
                                it
                            }
                        }
                Response.chunkedResponse(
                    items = page,
                    headers = headers,
                    format = if (request.format == "csv") ChunkedResponseFormat.Csv(nullValue = "") else ChunkedResponseFormat.Json(),
                )
            }

            is FunctionResult.Failure -> Response.errorResponse(result.value, headers)
        }
    }

    private fun MarketFundingRate.toMap() =
        mapOf(
            "market" to market,
            "time" to time,
            "database_time" to databaseTime,
            "rate" to rate,
            "period" to period,
            "interval" to interval,
        )

    private fun handleInternal(
        market: String,
        initialState: PageToken.TimePageToken?,
        request: GetTimeseriesMarketFundingRatesRequest,
        startTime: Instant,
        endTime: Instant,
        marketPatternRequested: Boolean,
        bufferSizePerStream: Int,
        marketConstraints: MarketConstraints,
    ): SuspendableStream<MarketFundingRateWrapper, SuspendableStream.State> {
        val derivativeMarket =
            marketConstraints.marketId as? NormalizedMarket.DerivativesNormalizedMarket ?: return SuspendableStream.empty()
        val timeFieldName = "funding_time"
        val queryConfig =
            run {
                val symbolSqlParam = SqlUtils.escapeSql(derivativeMarket.symbol)
                QueryConfiguration(
                    db,
                    "funding_rates",
                    "funding_symbol='$symbolSqlParam'",
                )
            }

        val (adjustedStartTime, adjustedEndTime) =
            Pair(startTime, endTime)
                .adjust(
                    DataUtils.communityEnforcedStart(request.apiKey, communityApiKey),
                    marketConstraints.minTime,
                    marketConstraints.maxTime,
                )
                /**
                 * Returning an empty stream for simplicity.
                 * We could enhance this later, for example, by returning a function failure result if a pattern was not requested —
                 * but that would require more refactoring, which may not be necessary at this point.
                 */
                ?: return SuspendableStream.empty()

        val rangeQuery =
            RangeQuery
                .TimeRangeQuery(
                    adjustedStartTime,
                    request.startInclusive,
                    adjustedEndTime,
                    request.endInclusive,
                    request.pagingFrom,
                ).withPageToken(initialState)

        val timeFilter = DataUtils.createTimeFilter(rangeQuery)
        val additionalFilter = { it: MarketFundingRateWrapper ->
            timeFilter.invoke(it.time)
        }

        val queryTextBuilder: QueryTextBuilder = { filter, limit ->
            val ordering = if (request.pagingFrom == PagingFrom.START) "ASC" else "DESC"
            """
            SELECT 
                $timeFieldName,
                funding_exchange_id,
                funding_symbol,
                funding_rate,
                funding_rate_period,
                funding_interval,
                funding_database_time
            FROM ${queryConfig.db.config.schema}.${queryConfig.tableName}
            WHERE 
                ${queryConfig.instrumentFilter}
                AND funding_exchange_id=${derivativeMarket.exchange}
                $filter
            ORDER BY $timeFieldName $ordering
            LIMIT $limit
            """
        }

        val stream =
            DataUtils
                .createStream(
                    db = queryConfig.db,
                    queryTextBuilder = queryTextBuilder,
                    bufferSize = bufferSizePerStream,
                    keyNames = arrayOf(timeFieldName),
                    dataMapper = createMapper(market),
                    rangeQuery = rangeQuery,
                    initialState = initialState,
                    stateResolver = { PageToken.TimePageToken(it.time) },
                    streamId =
                        if (marketPatternRequested) {
                            market
                        } else {
                            null
                        },
                ).filter(additionalFilter)

        return stream
    }

    private fun createMapper(market: String): (ResultSet) -> MarketFundingRateWrapper =
        { rs ->
            val time = rs.getTimestamp("funding_time").toInstant()
            val timeFormatted = TimeUtils.dateTimeFormatter.format(time)

            val dbTime =
                rs.getTimestamp("funding_database_time").toInstant().let {
                    TimeUtils.dateTimeFormatter.format(it)
                }

            MarketFundingRateWrapper(
                time,
                MarketFundingRate(
                    time = timeFormatted,
                    market = market,
                    rate = rs.getBigDecimal("funding_rate")?.let { CommonUtils.formatBigDecimal(it) },
                    period = rs.getString("funding_rate_period"),
                    interval = rs.getString("funding_interval"),
                    // null values will be filtered later
                    databaseTime = dbTime,
                ),
            )
        }

    private class MarketFundingRateWrapper(
        val time: Instant,
        val marketFundingRate: MarketFundingRate,
    )

    private class QueryConfiguration(
        val db: Database,
        val tableName: String,
        val instrumentFilter: String,
    )
}
