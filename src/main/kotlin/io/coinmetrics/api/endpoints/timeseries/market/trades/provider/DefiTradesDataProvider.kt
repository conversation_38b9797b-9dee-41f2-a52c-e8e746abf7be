package io.coinmetrics.api.endpoints.timeseries.market.trades.provider

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.endpoints.GetTimeseriesMarketTradesRequest
import io.coinmetrics.api.endpoints.timeseries.market.MarketEndpointUtil
import io.coinmetrics.api.endpoints.timeseries.market.trades.MarketTradeWrapper
import io.coinmetrics.api.model.NormalizedMarket
import io.coinmetrics.api.model.ParsedMarket
import io.coinmetrics.api.models.MarketTrade
import io.coinmetrics.api.models.PagingFrom
import io.coinmetrics.api.resources.Resources
import io.coinmetrics.api.service.MarketConstraints
import io.coinmetrics.api.service.MarketStatisticsService
import io.coinmetrics.api.utils.CommonUtils
import io.coinmetrics.api.utils.DataUtils
import io.coinmetrics.api.utils.QueryTextBuilder
import io.coinmetrics.api.utils.RangeQuery
import io.coinmetrics.api.utils.ThrottledLogger
import io.coinmetrics.api.utils.TimeUtils
import io.coinmetrics.api.utils.paging.PageToken
import io.coinmetrics.api.utils.streams.SuspendableStream
import io.coinmetrics.api.utils.toHex
import io.coinmetrics.databases.Database
import io.coinmetrics.databases.getLongOrNull
import io.coinmetrics.defi.client.DeFiRawDataParser
import io.coinmetrics.defi.client.model.MDMarketData
import io.coinmetrics.defi.client.model.RawTradeData
import org.slf4j.LoggerFactory
import java.math.RoundingMode
import java.sql.ResultSet
import java.time.Instant
import kotlin.math.max

class DefiTradesDataProvider(
    private val deFiRawDataParser: DeFiRawDataParser,
    private val marketStatisticsService: MarketStatisticsService,
) {
    companion object {
        private val log = LoggerFactory.getLogger(BaseTradesDataProvider::class.java)
    }

    private val throttledLogger: ThrottledLogger = ThrottledLogger(log, throttleMillis = 2000)

    fun checkParameters(
        marketConstraints: MarketConstraints,
        marketPatternRequested: Boolean,
    ): FunctionResult<ApiError, TradesStreamSpecificData.Defi?> {
        val (rawMarketId, _, _, parsedMarket) = marketConstraints
        val marketNormalizedId = rawMarketId as NormalizedMarket.DefiNormalizedMarket
        val poolMarketId = parsedMarket.toString()
        val defiMetadata = marketStatisticsService.getDefiMetadata(parsedMarket as ParsedMarket.ParsedDefiMarket)

        val rawMarket =
            deFiRawDataParser
                .convertMDMarketToRawMarket(
                    mdMarket =
                        MDMarketData(
                            poolMarketId = poolMarketId,
                            baseId = marketNormalizedId.base,
                            quoteId = marketNormalizedId.quote,
                        ),
                ) ?: let {
                throttledLogger.log {
                    warn("Failed to resolve raw market data for DeFi market $poolMarketId")
                }
                null
            }

        val exchange =
            Resources.getExchangeById(marketNormalizedId.exchange).onFailure { log.warn(it) }.getOrNull()
                ?: let {
                    throttledLogger.log {
                        warn("Failed to resolve exchange ${marketNormalizedId.exchange} for DeFi market $poolMarketId")
                    }
                    null
                }
        val poolId =
            defiMetadata?.poolId
                ?: let {
                    throttledLogger.log {
                        warn("Failed to resolve pool address for DeFi market $poolMarketId")
                    }
                    null
                }
        val exchangeNetwork = exchange?.network

        if (rawMarket == null || exchange == null || exchangeNetwork == null || poolId == null) {
            return if (marketPatternRequested) {
                FunctionResult.Success(null)
            } else {
                FunctionResult.Failure(ApiError.UnsupportedParameterValue("markets", poolMarketId))
            }
        }

        return FunctionResult.Success(
            TradesStreamSpecificData.Defi(
                marketConstraints = marketConstraints,
                poolMarketId = poolMarketId,
                rawMarket = rawMarket,
                exchangeName = exchange.name,
                exchangeNetwork = exchangeNetwork,
                poolId = poolId,
            ),
        )
    }

    suspend fun provide(
        db: Database,
        request: GetTimeseriesMarketTradesRequest,
        marketPatternRequested: Boolean,
        bufferSize: Int,
        initialState: PageToken.TimeAndBigIntegerPageToken?,
        rangeQuery: RangeQuery.TimeAndBigIntegerRangeQuery,
        streamSpecificData: TradesStreamSpecificData.Defi,
    ): SuspendableStream<MarketTradeWrapper, PageToken.TimeAndBigIntegerPageToken> {
        val timeFilter = DataUtils.createTimeFilter(rangeQuery)
        val additionalFilter: (MarketTradeWrapper) -> Boolean = { it: MarketTradeWrapper ->
            if (it.marketTrade.amount == "null" || it.marketTrade.price == "null") {
                log.warn(
                    "Found null values in trade, price pair for market '${streamSpecificData.poolMarketId}', tradeId is '${it.coinMetricsId}'.",
                )
                false
            } else {
                timeFilter.invoke(it.time)
            }
        }

        val tableName = "dex_raw_swaps_${streamSpecificData.exchangeName}"

        val maxBlockTimeFilter =
            getMaxBlockTimeAccordingToMinConfirmations(db, request.minConfirmations, streamSpecificData.exchangeNetwork)
                ?.let { TimeUtils.toSqlCompareExpression("<=", it) }
                ?.let { "block_time $it" }
                ?: "TRUE"

        val queryTextBuilder: QueryTextBuilder = { filter, limit ->
            val ordering = if (request.pagingFrom == PagingFrom.START) "ASC" else "DESC"
            """
            SELECT 
                base_handle,
                quote_handle,
                block_time,
                trade_id,
                encode(block_hash, 'hex') AS block_hash_hexed,
                block_height,
                encode(txid, 'hex') AS txid_hexed,
                encode(initiator, 'hex') AS initiator_hexed,
                encode(sender, 'hex') AS sender_hexed,
                encode(beneficiary, 'hex') AS beneficiary_hexed,
                amount_raw,
                price_raw,
                buy,
                database_time,
                cm_trade_id
            FROM ${db.config.schema}.$tableName
                INNER JOIN ${db.config.schema}.${streamSpecificData.exchangeNetwork}_main_blocks USING (block_hash)
            WHERE
                pool_id='\x${streamSpecificData.poolId.toHex()}' 
                AND base_handle='\x${streamSpecificData.rawMarket.baseHandle.toHex()}' 
                AND quote_handle='\x${streamSpecificData.rawMarket.quoteHandle.toHex()}'
                AND $maxBlockTimeFilter
                $filter
            ORDER BY block_time $ordering, trade_id $ordering
            LIMIT $limit
            """
        }

        val stream =
            DataUtils
                .createStream(
                    db = db,
                    queryTextBuilder = queryTextBuilder,
                    bufferSize = bufferSize,
                    keyNames = arrayOf("block_time", "trade_id"),
                    dataMapper = createMapper(streamSpecificData.poolMarketId, streamSpecificData.exchangeName),
                    rangeQuery = rangeQuery,
                    initialState = initialState,
                    stateResolver = { PageToken.TimeAndBigIntegerPageToken(it.time, it.coinMetricsId) },
                    streamId =
                        if (marketPatternRequested) {
                            streamSpecificData.poolMarketId
                        } else {
                            null
                        },
                    lbKey = streamSpecificData.poolMarketId,
                ).filter(additionalFilter)

        return stream
    }

    private suspend fun getMaxBlockTimeAccordingToMinConfirmations(
        defiDb: Database,
        minConfirmations: Int?,
        network: String,
    ): Instant? {
        if (minConfirmations == null || minConfirmations == 0) {
            return null
        }
        return defiDb.query(
            """
            SELECT max(block_timestamp)
            FROM ${defiDb.config.schema}.${network}_main_blocks
            WHERE height = 
                (SELECT max(height) - $minConfirmations 
                FROM ${defiDb.config.schema}.${network}_main_blocks);
            """.trimIndent(),
        ) { it.map { rs -> rs.getTimestamp(1)?.toInstant() }.first() }
    }

    private fun createMapper(
        poolMarketId: String,
        exchangeTicker: String,
    ): (ResultSet) -> MarketTradeWrapper =
        { rs: ResultSet ->
            val time = rs.getTimestamp("block_time").toInstant()
            val timeFormatted = TimeUtils.dateTimeFormatter.format(time)

            val dbTime =
                rs.getTimestamp("database_time")?.toInstant()?.let {
                    TimeUtils.dateTimeFormatter.format(it)
                } ?: timeFormatted
            val tradeId = rs.getBigDecimal("trade_id").toBigInteger()
            val coinMetricsId = rs.getString("cm_trade_id")
            val tradeAmountRaw = rs.getBigDecimal("amount_raw")
            val tradePriceRaw = rs.getBigDecimal("price_raw")

            val baseHandle = rs.getBytes("base_handle")
            val quoteHandle = rs.getBytes("quote_handle")

            val mdTrade =
                deFiRawDataParser.convertRawTradeToMdTrade(
                    rawTrade =
                        RawTradeData(
                            baseHandle = baseHandle,
                            quoteHandle = quoteHandle,
                            priceRaw = tradePriceRaw,
                            amountRaw = tradeAmountRaw,
                        ),
                    exchange = exchangeTicker,
                )

            MarketTradeWrapper(
                time,
                tradeId,
                MarketTrade(
                    time = timeFormatted,
                    market = poolMarketId,
                    coinMetricsId = coinMetricsId,
                    // null values will be filtered later
                    amount = mdTrade?.let { CommonUtils.formatBigDecimal(it.amount) } ?: "null",
                    price =
                        mdTrade?.let {
                            CommonUtils.formatBigDecimal(
                                it.price.let { trade ->
                                    // Round to 20 significant digits.
                                    val newScale = max(20 - trade.precision() + trade.scale(), 0)
                                    trade.setScale(newScale, RoundingMode.HALF_UP)
                                },
                            )
                        } ?: "null",
                    side = MarketEndpointUtil.sideToString(rs, "buy"),
                    blockHash = rs.getString("block_hash_hexed"),
                    blockHeight = rs.getLongOrNull("block_height")?.toString(),
                    txid = rs.getString("txid_hexed"),
                    initiator = rs.getString("initiator_hexed"),
                    sender = rs.getString("sender_hexed"),
                    beneficiary = rs.getString("beneficiary_hexed"),
                    databaseTime = dbTime,
                ),
            )
        }
}
