package io.coinmetrics.api.endpoints.timeseries.market

import io.coinmetrics.api.ChunkedResponseFormat
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.Response
import io.coinmetrics.api.endpoints.GetTimeseriesMarketImpliedVolatilityEndpoint
import io.coinmetrics.api.endpoints.GetTimeseriesMarketImpliedVolatilityRequest
import io.coinmetrics.api.model.DerivativesMarketType
import io.coinmetrics.api.model.NormalizedMarket
import io.coinmetrics.api.model.ParsedMarket
import io.coinmetrics.api.models.MarketImpliedVolatility
import io.coinmetrics.api.models.MarketImpliedVolatilityResponse
import io.coinmetrics.api.models.PagingFrom
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.MarketConstraints
import io.coinmetrics.api.service.MarketResolvingService
import io.coinmetrics.api.service.MarketStatisticsService
import io.coinmetrics.api.service.getTimeseriesRateLimitHeaders
import io.coinmetrics.api.utils.BatchUtils
import io.coinmetrics.api.utils.CommonUtils
import io.coinmetrics.api.utils.DataUtils
import io.coinmetrics.api.utils.DataUtils.adjust
import io.coinmetrics.api.utils.QueryTextBuilder
import io.coinmetrics.api.utils.RangeQuery
import io.coinmetrics.api.utils.SqlUtils
import io.coinmetrics.api.utils.TimeUtils
import io.coinmetrics.api.utils.Utils
import io.coinmetrics.api.utils.paging.PageToken
import io.coinmetrics.api.utils.paging.getPageFlow
import io.coinmetrics.api.utils.streams.SuspendableStream
import io.coinmetrics.databases.Database
import kotlinx.coroutines.flow.map
import java.sql.ResultSet
import java.time.Instant
import java.time.ZoneId

class ImpliedVolatilityEndpointImpl(
    private val db: Database,
    private val amsService: AmsService,
    private val marketResolvingService: MarketResolvingService,
    private val marketStatisticsService: MarketStatisticsService,
    private val communityApiKey: String,
) : GetTimeseriesMarketImpliedVolatilityEndpoint() {
    override suspend fun handle(request: GetTimeseriesMarketImpliedVolatilityRequest): Response<MarketImpliedVolatilityResponse> {
        val headers =
            amsService
                .getTimeseriesRateLimitHeaders(
                    apiKey = request.apiKey,
                    httpRequest = request.httpRequest,
                ).getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }

        val parsedTimeParams =
            DataUtils
                .parseTimeParameters(
                    startTimeStr = request.startTime,
                    startInclusive = request.startInclusive,
                    endTimeStr = request.endTime,
                    endInclusive = request.endInclusive,
                    timezone = request.timezone,
                ).getOrElse { return Response.errorResponse(it) }

        val (marketsConstraints, marketPatternRequested) =
            marketResolvingService
                .parseAndCheckMarkets(
                    apiKey = request.apiKey,
                    requestedMarkets = request.markets,
                    marketFilter = { market ->
                        market is ParsedMarket.ParsedDerivativesMarket && market.type == DerivativesMarketType.OPTION
                    },
                    errorMessageForFilteredOutMarketsIfPatternIsNotRequested = { "Only option markets are supported." },
                ).getOrElse { return Response.errorResponse(it) }

        val (prefetch, bufferSizePerStream) =
            Utils.getFetchProperties(
                request.limitPerMarket,
                marketsConstraints.size,
                request.pageSize,
            )

        val downSamplingConfig =
            TimeUtils
                .createStatefulDownSamplerConfig(
                    granularity = request.granularity,
                    pagingFromStart = request.pagingFrom == PagingFrom.START,
                    withAlignment = true,
                    timezone = ZoneId.of(request.timezone),
                ).getOrElse {
                    return Response.errorResponse(it)
                }

        val streams =
            BatchUtils.sortIdsAndConcatStreams(
                streams = marketsConstraints.asSequence().map { it.toPair() },
                numberOfStreamsToPrefetch = prefetch,
                pagingFrom = request.pagingFrom,
                nextPageToken = request.nextPageToken,
                initialStreamStateParser = { PageToken.TimePageToken.parse(it) },
                limitPerStream = request.limitPerMarket,
                streamSupplier = { id, state, marketConstraints ->
                    handleInternal(
                        market = id,
                        initialState = state,
                        request = request,
                        marketPatternRequested = marketPatternRequested,
                        marketConstraints = marketConstraints,
                        parsedTimeParams = parsedTimeParams,
                        bufferSize = bufferSizePerStream,
                        downSamplingConfig = downSamplingConfig,
                    )
                },
                streamIdsAreResolvedDynamically = marketPatternRequested,
                httpRequestCoroutineContext = request.httpRequest.coroutineContext,
                logger = log,
            )

        return when (streams) {
            is FunctionResult.Success -> {
                val page =
                    streams.value
                        .map { it.marketImpliedVolatility }
                        .getPageFlow(
                            request.httpRequest,
                            request.pageSize,
                            request.pagingFrom,
                        ).let {
                            if (request.format == "csv") {
                                it.map { obj ->
                                    (obj as? MarketImpliedVolatility)?.toMap() ?: obj
                                }
                            } else {
                                it
                            }
                        }
                Response.chunkedResponse(
                    items = page,
                    headers = headers,
                    format = if (request.format == "csv") ChunkedResponseFormat.Csv(nullValue = "") else ChunkedResponseFormat.Json(),
                )
            }

            is FunctionResult.Failure -> Response.errorResponse(streams.value, headers)
        }
    }

    private fun MarketImpliedVolatility.toMap() =
        mapOf(
            "market" to market,
            "time" to time,
            "database_time" to databaseTime,
            "iv_trade" to ivTrade,
            "iv_bid" to ivBid,
            "iv_ask" to ivAsk,
            "iv_mark" to ivMark,
            "exchange_time" to exchangeTime,
        )

    private fun handleInternal(
        market: String,
        initialState: PageToken.TimePageToken?,
        request: GetTimeseriesMarketImpliedVolatilityRequest,
        marketPatternRequested: Boolean,
        marketConstraints: MarketConstraints,
        parsedTimeParams: Pair<Instant, Instant>,
        bufferSize: Int,
        downSamplingConfig: TimeUtils.StatefulDownSamplerConfig?,
    ): SuspendableStream<MarketImpliedVolatilityWrapper, PageToken.TimePageToken> {
        val (marketId, keyMinTime, keyMaxTime) = marketConstraints

        val optionMarket = marketId as NormalizedMarket.DerivativesNormalizedMarket

        val (startTime, endTime) =
            parsedTimeParams.adjust(
                DataUtils.communityEnforcedStart(request.apiKey, communityApiKey),
                keyMinTime,
                keyMaxTime,
            )
                /**
                 * Returning an empty stream for simplicity.
                 * We could enhance this later, for example, by returning a function failure result if a pattern was not requested —
                 * but that would require more refactoring, which may not be necessary at this point.
                 */
                ?: return SuspendableStream.empty()

        if (MarketEndpointUtil.isDerivativeMarketOperationTimeNotOverlap(
                marketName = market,
                marketType = marketId.type,
                timeRange = startTime to endTime,
                marketStatisticsService = marketStatisticsService,
            )
        ) {
            return SuspendableStream.empty()
        }

        val rangeQuery =
            RangeQuery
                .TimeRangeQuery(
                    startKey = startTime,
                    startInclusive = request.startInclusive,
                    endKey = endTime,
                    endInclusive = request.endInclusive,
                    pagingFrom = request.pagingFrom,
                ).withPageToken(initialState)

        val timeFilter = DataUtils.createTimeFilter(rangeQuery)
        val additionalFilter = { it: MarketImpliedVolatilityWrapper ->
            timeFilter.invoke(it.time)
        }

        val timeFieldName = "ticker_time"

        val queryTextBuilder: QueryTextBuilder = { filter, limit ->
            val ordering = if (request.pagingFrom == PagingFrom.START) "ASC" else "DESC"
            val symbolSqlParam = SqlUtils.escapeSql(optionMarket.symbol)
            val symbolFilter = "ticker_symbol='$symbolSqlParam'"
            """
            SELECT 
                $timeFieldName,
                ticker_exchange_id,
                ticker_symbol,
                ticker_implied_vol_trade,
                ticker_implied_vol_bid,
                ticker_implied_vol_ask,
                ticker_implied_vol_mark,
                ticker_exchange_time,
                ticker_database_time
            FROM ${db.config.schema}.option_ticker
            WHERE $symbolFilter
                AND ticker_exchange_id=${optionMarket.exchange}
                $filter
            ORDER BY $timeFieldName $ordering
            LIMIT $limit
            """
        }

        val downSamplingFilter = downSamplingConfig?.let { TimeUtils.createStatefulDownSampler(it) }

        return DataUtils
            .createStream(
                db = db,
                queryTextBuilder = queryTextBuilder,
                bufferSize = bufferSize,
                keyNames = arrayOf(timeFieldName),
                dataMapper = createMapper(market),
                rangeQuery = rangeQuery,
                initialState = initialState,
                stateResolver = { PageToken.TimePageToken(it.time) },
                streamId =
                    if (marketPatternRequested) {
                        market
                    } else {
                        null
                    },
            ).filter(additionalFilter)
            .let { stream ->
                if (downSamplingFilter == null) {
                    stream
                } else {
                    stream.filter { downSamplingFilter(it.time) }
                }
            }
    }

    private fun createMapper(market: String): (ResultSet) -> MarketImpliedVolatilityWrapper =
        { rs ->
            val time = rs.getTimestamp("ticker_time").toInstant()
            val timeFormatted = TimeUtils.dateTimeFormatter.format(time)

            val dbTime =
                rs.getTimestamp("ticker_database_time").toInstant().let {
                    TimeUtils.dateTimeFormatter.format(it)
                }

            MarketImpliedVolatilityWrapper(
                time,
                MarketImpliedVolatility(
                    time = timeFormatted,
                    market = market,
                    ivTrade = rs.getBigDecimal("ticker_implied_vol_trade")?.let { CommonUtils.formatBigDecimal(it) },
                    ivAsk = rs.getBigDecimal("ticker_implied_vol_ask")?.let { CommonUtils.formatBigDecimal(it) },
                    ivBid = rs.getBigDecimal("ticker_implied_vol_bid")?.let { CommonUtils.formatBigDecimal(it) },
                    ivMark = rs.getBigDecimal("ticker_implied_vol_mark")?.let { CommonUtils.formatBigDecimal(it) },
                    exchangeTime = rs.getTimestamp("ticker_exchange_time")?.toInstant()?.let { TimeUtils.dateTimeFormatter.format(it) },
                    databaseTime = dbTime,
                ),
            )
        }

    private class MarketImpliedVolatilityWrapper(
        val time: Instant,
        val marketImpliedVolatility: MarketImpliedVolatility,
    )
}
