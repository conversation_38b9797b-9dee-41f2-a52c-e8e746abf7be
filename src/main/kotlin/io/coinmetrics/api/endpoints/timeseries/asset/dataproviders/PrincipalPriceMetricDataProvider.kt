package io.coinmetrics.api.endpoints.timeseries.asset.dataproviders

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.endpoints.GetTimeseriesAssetMetricsRequest
import io.coinmetrics.api.endpoints.timeseries.asset.AssetMetricsEndpointHelper
import io.coinmetrics.api.endpoints.timeseries.asset.AssetMetricsEndpointImpl
import io.coinmetrics.api.endpoints.timeseries.asset.datasources.MetricDataUtils
import io.coinmetrics.api.endpoints.timeseries.asset.datasources.PrincipalPriceDataSource
import io.coinmetrics.api.models.PagingFrom
import io.coinmetrics.api.utils.TimeUtils
import io.coinmetrics.api.utils.paging.PageToken
import io.coinmetrics.api.utils.streams.SuspendableStream
import java.time.Clock
import java.time.Instant

class PrincipalPriceMetricDataProvider(
    private val dataSource: PrincipalPriceDataSource,
    private val clock: Clock,
) : AssetMetricDataProvider<MetricDataUtils.MetricsHolderWithTime> {
    override fun isApplicable(
        metrics: List<String>,
        frequency: AssetMetricsEndpointHelper.Frequency,
        dataSourceGroup: AssetMetricsEndpointHelper.DataSourceGroup,
    ): Boolean = dataSourceGroup.name.startsWith("PRINCIPAL_MARKET")

    override suspend fun checkParameters(
        asset: String,
        metrics: List<String>,
    ): FunctionResult<ApiError, AssetMetricsEndpointImpl.AssetMetricsDataSourceSpecificData> = dataSource.checkParameters(asset, metrics)

    override fun provide(
        asset: String,
        patternRequested: Boolean,
        metrics: List<String>,
        startTime: Instant,
        endTime: Instant,
        pagingFrom: PagingFrom,
        request: GetTimeseriesAssetMetricsRequest,
        pageTokenForDataSource: String?,
        frequencyOffset: TimeUtils.NormalizedFrequencyOffset,
        frequency: AssetMetricsEndpointHelper.Frequency,
        bufferSize: Int,
        communityApiKey: String,
        dataSourceSpecificData: AssetMetricsEndpointImpl.AssetMetricsDataSourceSpecificData?,
    ): SuspendableStream<MetricDataUtils.MetricsHolderWithTime, PageToken.TimePageToken> =
        dataSource.query(
            asset,
            patternRequested,
            bufferSize,
            pageTokenForDataSource,
            frequency.value,
            frequencyOffset,
            startTime,
            endTime,
            pagingFrom,
            request,
            metrics,
            dataSourceSpecificData as? PrincipalPriceDataSource.PrincipalPriceDataSourceSpecificData
                ?: error("Data source specific data must not be null"),
            MetricDataUtils.enforcedStartTime(
                clock = clock,
                apiKey = request.apiKey,
                communityApiKey = communityApiKey,
                /**
                 * MD-3542
                 * Expand community permission to ReferenceRate* metrics served through /timeseries/asset-metrics, all frequencies, to have 7 days of history (currently it is 1 day).
                 */
                delayDays = 7,
            ),
        )
}
