package io.coinmetrics.api.endpoints.timeseries.market

import io.coinmetrics.api.model.DerivativesMarketType
import io.coinmetrics.api.model.DerivativesMarketType.OPTION
import io.coinmetrics.api.model.ParsedMarket
import io.coinmetrics.api.model.ParsedMarket.ParsedDerivativesMarket
import io.coinmetrics.api.service.MarketStatisticsService
import io.coinmetrics.api.statistics.market.MarketStatistics
import java.time.Instant

class S3OrderBookDepthSelector(
    private val marketStatisticsService: MarketStatisticsService,
) {
    companion object {
        private const val ONE_DAY_SECONDS = 24 * 3600
    }

    /**
     * Returns a triple of:
     * - enforced depth (1-30000) if the API needs to further limit the book depth
     * - statistical depth (100 or 30000) used for data retrieval
     * - bucket name prefix
     * @param parsedDepth 0 means 10pct_mid_price
     * @param requestedStartTime start time from the user's request
     * @param requestedEndTime end time from the user's request
     */
    fun getApplicableDepths(
        parsedMarket: ParsedMarket,
        parsedDepth: Int,
        requestedStartTime: Instant,
        requestedEndTime: Instant,
    ): S3OrderBookDepths {
        // If the user requests a depth limit in the range (100, 30000], always use depth 30000
        if (parsedDepth > 100 && parsedDepth <= 30000) {
            val enforcedDepth: Int? = if (parsedDepth == 30000) null else parsedDepth
            return S3OrderBookDepths(
                enforcedDepthS3 = enforcedDepth,
                enforcedDepthDb = enforcedDepth,
                statDepth = 30000,
                bucketNamePrefix = parsedMarket.getBucketNamePrefix(Depth.DEPTH_FULL),
            )
        }

        // 10pct_mid_price always uses depth 100
        if (parsedDepth == 0) {
            // We don't need to enforce limits for 10pct_mid_price because:
            // - S3 has a separate bucket for it
            // - DB stores 10pct_mid_price with the depth_limit column value equal 100
            return S3OrderBookDepths(
                enforcedDepthS3 = null,
                enforcedDepthDb = null,
                statDepth = 100,
                bucketNamePrefix = parsedMarket.getBucketNamePrefix(Depth.DEPTH_10PCT),
            )
        }

        // For depth ≤100 requests, check if the requested time range intersects with depth 100 statistics
        val statistics100 = getDepthStatistics(parsedMarket.toString(), 100)
        val statistics30000 = getDepthStatistics(parsedMarket.toString(), 30000)

        // If depth 100 statistics are unavailable, fall back to depth 30000
        if (statistics100 == null) {
            return S3OrderBookDepths(
                enforcedDepthS3 = parsedDepth,
                enforcedDepthDb = parsedDepth,
                statDepth = 30000,
                bucketNamePrefix = parsedMarket.getBucketNamePrefix(Depth.DEPTH_FULL),
            )
        }

        // Check if the requested time range falls completely outside the depth 100 statistics range
        if (isTimeRangeOutsideStatistics(requestedStartTime, requestedEndTime, statistics100)) {
            return S3OrderBookDepths(
                enforcedDepthS3 = parsedDepth,
                enforcedDepthDb = parsedDepth,
                statDepth = 30000,
                bucketNamePrefix = parsedMarket.getBucketNamePrefix(Depth.DEPTH_FULL),
            )
        }

        // Check data freshness: if depth 100 data is more than 1 day staler than depth 30000 data, use depth 30000
        if (statistics30000 != null && isDepth100DataStale(statistics100, statistics30000)) {
            return S3OrderBookDepths(
                enforcedDepthS3 = parsedDepth,
                enforcedDepthDb = parsedDepth,
                statDepth = 30000,
                bucketNamePrefix = parsedMarket.getBucketNamePrefix(Depth.DEPTH_FULL),
            )
        }

        // Use depth 100
        val enforcedDepthS3: Int? = if (parsedDepth == 100) null else parsedDepth
        return S3OrderBookDepths(
            enforcedDepthS3 = enforcedDepthS3,
            enforcedDepthDb = parsedDepth,
            statDepth = 100,
            bucketNamePrefix = parsedMarket.getBucketNamePrefix(Depth.DEPTH_100),
        )
    }

    private fun getDepthStatistics(
        market: String,
        depth: Int,
    ): MarketStatistics.Statistics? = marketStatisticsService.getTieredS3BooksStatisticsForCatalogV2()[depth]?.get(market)

    /**
     * Determines whether the requested time range falls completely outside the depth 100 statistics range.
     * Returns true if there is no overlap between the requested range and the available statistics range.
     */
    private fun isTimeRangeOutsideStatistics(
        requestedStartTime: Instant,
        requestedEndTime: Instant,
        statistics: MarketStatistics.Statistics,
    ): Boolean {
        val statMinTime = Instant.parse(statistics.minTime)
        if (requestedEndTime.isBefore(statMinTime)) {
            return true
        }

        val statMaxTime = Instant.parse(statistics.maxTime)
        return requestedStartTime.isAfter(statMaxTime)
    }

    /**
     * Determines whether depth 100 statistics are more than one day staler than depth 30000 statistics.
     */
    private fun isDepth100DataStale(
        statistics100: MarketStatistics.Statistics,
        statistics30000: MarketStatistics.Statistics,
    ): Boolean {
        val depth100MaxTimeSeconds = Instant.parse(statistics100.maxTime).epochSecond
        val depth30000MaxTimeSeconds = Instant.parse(statistics30000.maxTime).epochSecond
        val differenceSeconds = depth30000MaxTimeSeconds - depth100MaxTimeSeconds
        return differenceSeconds >= ONE_DAY_SECONDS
    }

    private enum class Depth { DEPTH_100, DEPTH_10PCT, DEPTH_FULL }

    private fun ParsedMarket.getBucketNamePrefix(depth: Depth): String =
        when (this) {
            is ParsedMarket.ParsedSpotMarket -> {
                when (depth) {
                    Depth.DEPTH_100 -> BookBucketNamePrefixes.SPOT_100
                    Depth.DEPTH_10PCT -> BookBucketNamePrefixes.SPOT_10PCT
                    Depth.DEPTH_FULL -> BookBucketNamePrefixes.SPOT_FULL
                }
            }

            is ParsedDerivativesMarket -> {
                when (type) {
                    DerivativesMarketType.FUTURE -> {
                        when (depth) {
                            Depth.DEPTH_100 -> BookBucketNamePrefixes.FUTURES_100
                            Depth.DEPTH_10PCT -> BookBucketNamePrefixes.FUTURES_10PCT
                            Depth.DEPTH_FULL -> BookBucketNamePrefixes.FUTURES_FULL
                        }
                    }

                    OPTION -> {
                        when (depth) {
                            Depth.DEPTH_100 -> BookBucketNamePrefixes.OPTIONS_100
                            Depth.DEPTH_10PCT -> BookBucketNamePrefixes.OPTIONS_10PCT
                            Depth.DEPTH_FULL -> BookBucketNamePrefixes.OPTIONS_FULL
                        }
                    }
                }
            }

            is ParsedMarket.ParsedDefiMarket -> error("Unsupported market type: $this")
        }
}
