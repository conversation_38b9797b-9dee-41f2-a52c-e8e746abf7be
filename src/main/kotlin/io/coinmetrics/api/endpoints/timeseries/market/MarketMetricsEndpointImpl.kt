package io.coinmetrics.api.endpoints.timeseries.market

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.ApiError.BadParameter
import io.coinmetrics.api.ApiError.ForbiddenWithMessage
import io.coinmetrics.api.ApiError.UnsupportedParameterValue
import io.coinmetrics.api.ChunkedResponseFormat
import io.coinmetrics.api.CommonConstants
import io.coinmetrics.api.MarketMetricFrequency
import io.coinmetrics.api.Response
import io.coinmetrics.api.endpoints.GetTimeseriesMarketMetricsEndpoint
import io.coinmetrics.api.endpoints.GetTimeseriesMarketMetricsRequest
import io.coinmetrics.api.endpoints.timeseries.exchange.datasources.ExchangeBasedMetricsDataSource
import io.coinmetrics.api.endpoints.timeseries.exchange.datasources.ExchangeBasedMetricsDataSource.TimeRangeParams
import io.coinmetrics.api.endpoints.timeseries.market.datasources.LiquidationsMetricDataProvider
import io.coinmetrics.api.endpoints.timeseries.market.datasources.LiquidityMetricDataProvider
import io.coinmetrics.api.models.MarketMetricsResponse
import io.coinmetrics.api.models.PagingFrom
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.MarketConstraints
import io.coinmetrics.api.service.MarketResolvingService
import io.coinmetrics.api.service.MarketStatisticsService
import io.coinmetrics.api.service.forMarket
import io.coinmetrics.api.service.getTimeseriesRateLimitHeaders
import io.coinmetrics.api.toSuccess
import io.coinmetrics.api.utils.BatchUtils
import io.coinmetrics.api.utils.ComparablePair
import io.coinmetrics.api.utils.DataUtils
import io.coinmetrics.api.utils.MetricUtils.MetricsWithTimeWrapper
import io.coinmetrics.api.utils.TimeUtils
import io.coinmetrics.api.utils.Utils
import io.coinmetrics.api.utils.paging.PageToken.TimePageToken
import io.coinmetrics.api.utils.paging.getPageFlow
import io.coinmetrics.api.utils.streams.SuspendableStream
import io.coinmetrics.api.utils.streams.SuspendableStreamAsMergeSource
import io.coinmetrics.api.utils.streams.asMergeSource
import io.coinmetrics.api.utils.streams.operations.MergingState
import io.coinmetrics.api.utils.streams.operations.MergingStateSerializer
import io.coinmetrics.api.utils.streams.operations.mergeJoin
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.coroutineScope
import java.time.Instant
import kotlin.math.max

class MarketMetricsEndpointImpl(
    private val amsService: AmsService,
    private val marketResolvingService: MarketResolvingService,
    private val marketStatisticsService: MarketStatisticsService,
    marketMetricsDataSource: ExchangeBasedMetricsDataSource,
) : GetTimeseriesMarketMetricsEndpoint() {
    private val emptyResponse = MarketMetricsResponse(emptyList())

    private val dataProviders =
        listOf(
            LiquidationsMetricDataProvider(marketMetricsDataSource),
            LiquidityMetricDataProvider(marketMetricsDataSource),
        )

    override suspend fun handle(request: GetTimeseriesMarketMetricsRequest): Response<MarketMetricsResponse> {
        val headers =
            amsService
                .getTimeseriesRateLimitHeaders(
                    apiKey = request.apiKey,
                    httpRequest = request.httpRequest,
                ).getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }

        amsService.check(apiKey = request.apiKey, resource = "market_metrics").getOrElse { (apiError) ->
            return if (apiError is ApiError.Forbidden) {
                Response.errorResponse(ForbiddenWithMessage("Market metrics are not available with supplied credentials."))
            } else {
                Response.errorResponse(apiError)
            }
        }

        val frequency =
            CommonConstants.marketMetricsSupportedFrequencies[request.frequency] ?: run {
                return Response.errorResponse(UnsupportedParameterValue("frequency", request.frequency))
            }

        val (marketsConstraints, marketPatternRequested) =
            marketResolvingService
                .parseAndCheckMarkets(request.apiKey, request.markets)
                .getOrElse { return Response.errorResponse(it) }

        if (marketsConstraints.isEmpty() && marketPatternRequested) {
            return Response.successResponse(emptyResponse, headers)
        }

        val markets = getMarketsWithAvailableMetrics(marketsConstraints.keys, request.metrics)
        if (markets.isEmpty()) {
            return Response.errorResponse(
                BadParameter("markets", "All requested markets do not support any requested metric."),
            )
        }

        val (startTime, endTime, pagingFromStart) =
            DataUtils
                .parseAndValidateTimeParameters(
                    request.startTime,
                    request.startInclusive,
                    request.endTime,
                    request.endInclusive,
                    request.timezone,
                    request.pageSize,
                    request.pagingFrom,
                ).getOrElse { return Response.errorResponse(it) }

        val marketToStreamSpecificDataList =
            markets.map { market ->
                val checkResults =
                    request.metrics.map { metric ->
                        marketStatisticsService
                            .getMarketMetrics(market)
                            ?.get(metric)
                            ?.find { it.frequency == frequency.shortForm } to metric
                    }

                val unsupportedMetrics =
                    checkResults
                        .asSequence()
                        .filter { it.first == null }
                        .map { it.second }
                        .toSet()

                val supportedMetrics = request.metrics.toHashSet() - unsupportedMetrics
                if (supportedMetrics.isEmpty()) {
                    // all metrics are unsupported
                    return Response.errorResponse(
                        BadParameter(
                            "metrics",
                            "All requested metrics aren't supported for market '$market' and frequency '${frequency.shortForm}'.",
                        ),
                    )
                }

                market to
                    Pair(
                        supportedMetrics,
                        marketsConstraints.forMarket(market),
                    )
            }

        val stream =
            if (request.sort == "time") {
                // sort by (time, market)
                // TODO: return a 400 error if sort=time is combined with marketPatternRequested
                // optimization for buffer size, lower limit of buffer size is 2
                // why not 1? because an additional item is needed to check availability of the next page
                // smaller buffer size is better when we don't want to transfer a lot of redundant data via network
                val bufferSizePerStream = max(1, request.pageSize / markets.size) + 1

                val mergeKeyExtractor: (LinkedHashMap<String, String?>) -> Comparable<*> = { map ->
                    ComparablePair(
                        map["time"]!!,
                        map["market"]!!,
                    )
                }

                val marketToStreamSpecificData = marketToStreamSpecificDataList.toMap()

                BatchUtils
                    .mergeSortStreams(
                        streamIds = marketToStreamSpecificDataList.map { (market) -> market }.toTypedArray(),
                        pagingFromStart = request.pagingFrom == PagingFrom.START,
                        nextPageToken = request.nextPageToken,
                        mergeKeyExtractor = mergeKeyExtractor,
                        limitPerStream = request.limitPerMarket,
                        streamSupplier = { id, pageToken ->
                            val (supportedMetrics, marketConstraints) = marketToStreamSpecificData.getValue(id)
                            handleInternal(
                                market = id,
                                patternRequested = marketPatternRequested,
                                supportedMetrics = supportedMetrics,
                                bufferSize = bufferSizePerStream,
                                pageToken = pageToken,
                                frequency = frequency,
                                request = request,
                                timeRangeParams =
                                    request.toTimeRangeParams(
                                        startTime,
                                        endTime,
                                        pagingFromStart,
                                        marketConstraints.minTime,
                                        marketConstraints.maxTime,
                                    ),
                                marketConstraints = marketConstraints,
                            ).toSuccess()
                        },
                    ).getOrElse {
                        return Response.errorResponse(it, headers)
                    }
            } else {
                // sort by (market, time)

                val (prefetch, bufferSizePerStream) = Utils.getFetchProperties(request.limitPerMarket, markets.size, request.pageSize)

                BatchUtils
                    .sortIdsAndConcatStreams(
                        streams = marketToStreamSpecificDataList.asSequence(),
                        streamIdsAreResolvedDynamically = marketPatternRequested,
                        pagingFromStart = request.pagingFrom == PagingFrom.START,
                        nextPageToken = request.nextPageToken,
                        initialStreamStateParser = { it },
                        numberOfStreamsToPrefetch = prefetch,
                        limitPerStream = request.limitPerMarket,
                        streamSupplier = { id, pageToken, (supportedMetrics, marketConstraints) ->
                            handleInternal(
                                market = id,
                                patternRequested = marketPatternRequested,
                                supportedMetrics = supportedMetrics,
                                bufferSize = bufferSizePerStream,
                                pageToken = pageToken,
                                frequency = frequency,
                                request = request,
                                timeRangeParams =
                                    request.toTimeRangeParams(
                                        startTime,
                                        endTime,
                                        pagingFromStart,
                                        marketConstraints.minTime,
                                        marketConstraints.maxTime,
                                    ),
                                marketConstraints = marketConstraints,
                            )
                        },
                        httpRequestCoroutineContext = request.httpRequest.coroutineContext,
                        logger = log,
                    ).getOrElse {
                        return Response.errorResponse(it)
                    }
            }

        val page = stream.getPageFlow(request.httpRequest, request.pageSize, request.pagingFrom == PagingFrom.START)
        return Response.chunkedResponse(
            items = page,
            headers = headers,
            format = if (request.format == "csv") ChunkedResponseFormat.Csv() else ChunkedResponseFormat.Json(allowNullValues = true),
        )
    }

    private suspend fun handleInternal(
        market: String,
        patternRequested: Boolean,
        supportedMetrics: Set<String>,
        bufferSize: Int,
        pageToken: String?,
        frequency: MarketMetricFrequency,
        request: GetTimeseriesMarketMetricsRequest,
        timeRangeParams: TimeRangeParams,
        marketConstraints: MarketConstraints,
    ): SuspendableStream<LinkedHashMap<String, String?>, SuspendableStream.State> {
        val metricsPerProviderIndex =
            supportedMetrics
                .groupBy { metric -> dataProviders.indexOfFirst { it.isApplicable(metric) } }
                .entries
                .sortedBy { it.key }

        val pageTokenForDataSources =
            MergingStateSerializer
                .deserialize(pageToken, metricsPerProviderIndex.size)
                .getOrElse { error(it) }
                .states

        return coroutineScope {
            metricsPerProviderIndex.mapIndexed { tokenIndex, (providerIndex, metrics) ->
                async {
                    dataProviders[providerIndex]
                        .provide(
                            market,
                            patternRequested,
                            marketConstraints.marketId,
                            bufferSize,
                            pageTokenForDataSources[tokenIndex],
                            frequency,
                            metrics,
                            timeRangeParams,
                        ).asMergeSource { it.time }
                }
            }
        }.awaitAll().let { joinMergedSources(it, request, market, patternRequested, supportedMetrics) }
    }

    private fun joinMergedSources(
        mergeSources: List<SuspendableStreamAsMergeSource<MetricsWithTimeWrapper, TimePageToken, Instant>>,
        request: GetTimeseriesMarketMetricsRequest,
        market: String,
        patternRequested: Boolean,
        supportedMetrics: Set<String>,
    ): SuspendableStream<LinkedHashMap<String, String?>, MergingState> =
        mergeSources.mergeJoin(
            streamId = if (patternRequested) market else null,
            pagingFromStart = request.pagingFrom == PagingFrom.START,
        ) { values ->
            val map = LinkedHashMap<String, String?>()
            val mapMetrics = HashMap<String, String?>()
            values.forEach { value ->
                when (value) {
                    is MetricsWithTimeWrapper -> {
                        map["market"] = market
                        map["time"] = TimeUtils.dateTimeFormatter.format(value.time)
                        mapMetrics.putAll(value.metrics)
                    }
                }
            }
            request.metrics.forEach {
                if (supportedMetrics.contains(it)) {
                    // add nulls for absent metrics if they are supported
                    mapMetrics.putIfAbsent(it, null)
                } else if (request.format == "csv") {
                    // add empty strings for unsupported metrics if CSV is requested
                    mapMetrics.putIfAbsent(it, "")
                }
            }
            map.putAll(mapMetrics.toList().sortedBy { it.first })
            map
        }

    private fun getMarketsWithAvailableMetrics(
        markets: Collection<String>,
        metrics: List<String>,
    ): List<String> =
        markets.filter { market ->
            marketStatisticsService.getMarketMetrics(market)?.let { marketMetrics ->
                metrics.any { requestedMetric -> marketMetrics.containsKey(requestedMetric) }
            } ?: false
        }

    private fun GetTimeseriesMarketMetricsRequest.toTimeRangeParams(
        startTime: Instant,
        endTime: Instant,
        pagingFromStart: Boolean,
        keyMinTime: Instant?,
        keyMaxTime: Instant?,
    ) = TimeRangeParams(
        startTime,
        startInclusive,
        endTime,
        endInclusive,
        timezone,
        pageSize,
        pagingFromStart,
        keyMinTime,
        keyMaxTime,
    )
}
