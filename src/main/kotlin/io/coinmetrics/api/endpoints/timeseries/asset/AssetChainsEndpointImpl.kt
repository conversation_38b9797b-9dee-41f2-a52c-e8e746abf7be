package io.coinmetrics.api.endpoints.timeseries.asset

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.Response
import io.coinmetrics.api.endpoints.GetAssetChainsEndpoint
import io.coinmetrics.api.endpoints.GetAssetChainsRequest
import io.coinmetrics.api.models.AssetChainBlock
import io.coinmetrics.api.models.AssetChains
import io.coinmetrics.api.models.AssetChainsResponse
import io.coinmetrics.api.models.PagingFrom
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.getTimeseriesRateLimitHeaders
import io.coinmetrics.api.utils.BatchUtils
import io.coinmetrics.api.utils.CsvUtils
import io.coinmetrics.api.utils.DataUtils
import io.coinmetrics.api.utils.QueryTextBuilder
import io.coinmetrics.api.utils.RangeQuery
import io.coinmetrics.api.utils.TimeUtils
import io.coinmetrics.api.utils.paging.PageToken
import io.coinmetrics.api.utils.paging.PagingUtils
import io.coinmetrics.api.utils.paging.getPage
import io.coinmetrics.api.utils.streams.SuspendableStream
import io.coinmetrics.api.utils.toHex
import io.coinmetrics.api.utils.toSqlOrdering
import io.coinmetrics.databases.Database
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.coroutineScope
import java.sql.ResultSet
import java.sql.Timestamp
import java.time.Instant

class AssetChainsEndpointImpl(
    private val db: Database,
    private val amsService: AmsService,
) : GetAssetChainsEndpoint() {
    private val amsParamsToEndpointParams =
        hashMapOf(
            "asset" to "assets",
        )

    override suspend fun handle(request: GetAssetChainsRequest): Response<AssetChainsResponse> {
        val headers =
            amsService
                .getTimeseriesRateLimitHeaders(
                    apiKey = request.apiKey,
                    httpRequest = request.httpRequest,
                ).getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }

        val checkResults =
            request.assets
                .distinct()
                .map { asset ->
                    amsService
                        .check(
                            apiKey = request.apiKey,
                            resource = "asset_chains",
                            parameters = hashMapOf("asset" to asset),
                        ) { amsParamsToEndpointParams[it] }
                }

        for (checkResult in checkResults) {
            checkResult.getOrElse { (e) -> return Response.errorResponse(e) }
        }

        val (startTime, endTime) =
            DataUtils
                .parseTimeParameters(
                    request.startTime,
                    request.startInclusive,
                    request.endTime,
                    request.endInclusive,
                    request.timezone,
                ).getOrElse { return Response.errorResponse(it) }

        return when (
            val result =
                BatchUtils.sortIdsAndConcatStreams(
                    streams = request.assets.asSequence().map { it to Unit },
                    streamIdsAreResolvedDynamically = false,
                    pagingFrom = request.pagingFrom,
                    nextPageToken = request.nextPageToken,
                    initialStreamStateParser = { PageToken.TimePageToken.parse(it) },
                    streamSupplier = { id, state, streamSpecificData ->
                        handleInternal(id, state, request, startTime, endTime)
                    },
                    httpRequestCoroutineContext = request.httpRequest.coroutineContext,
                    logger = log,
                )
        ) {
            is FunctionResult.Success -> {
                val page =
                    @Suppress("DEPRECATION")
                    result.value.getPage(request.pageSize, request.pagingFrom == PagingFrom.START)
                val nextPageUrl = page.nextPageToken?.let { PagingUtils.createNextPageUrl(request.httpRequest, it) }

                // todo: PLAT-708 this part of code is not expected after "getPage". It prevents the migration of this endpoint to Response.chunkedResponse. All transformations must be done before calling getPage.
                val assetChains =
                    coroutineScope {
                        page
                            .items
                            .map { it.assetChains.asset }
                            .distinct()
                            .map { asset ->
                                async {
                                    getAssetChains(asset, page.items)
                                }
                            }.awaitAll()
                            .flatten()
                            .groupByTo(HashMap()) { it.asset to (it.time to it.lastBlockHeightPosition) }
                    }

                return if (request.format == "csv") {
                    val pageItems: Array<Map<String, String?>> =
                        page.items
                            .flatMap { row: AssetChainsWrapper ->
                                val assetChain =
                                    getAssetChains(row, assetChains) ?: return returnOperationFailedOnAssetChainNotFound(row, headers)
                                assetChain.flatMap { chainRow ->
                                    chainRow.chains.map { chainBlock ->
                                        mapOf(
                                            "asset" to row.assetChains.asset,
                                            "time" to row.assetChains.time,
                                            "chains_count" to row.assetChains.chainsCount,
                                            "blocks_count_at_tip" to row.assetChains.blocksCountAtTip,
                                            "reorg" to row.assetChains.reorg,
                                            "reorg_depth" to row.assetChains.reorgDepth,
                                            "block_hash" to chainBlock.hash,
                                            "block_height" to chainBlock.height,
                                            "block_time" to chainBlock.time,
                                        )
                                    }
                                }
                            }.toTypedArray()

                    Response.rawHttpResponse(
                        @Suppress("DEPRECATION")
                        CsvUtils.toCsvHttpResponse(
                            data = pageItems,
                            nextPageToken = page.nextPageToken,
                            nextPageUrl = nextPageUrl,
                            headers = headers,
                            nullValue = "",
                        ),
                    )
                } else {
                    val data =
                        page.items.map { row ->
                            val assetChain =
                                getAssetChains(row, assetChains) ?: return returnOperationFailedOnAssetChainNotFound(row, headers)
                            val chains =
                                assetChain.map { chainRow ->
                                    chainRow.chains
                                }

                            row.assetChains.copy(chains = chains)
                        }

                    Response.successResponse(
                        AssetChainsResponse(
                            data = data,
                            nextPageToken = page.nextPageToken,
                            nextPageUrl = nextPageUrl,
                        ),
                        headers,
                    )
                }
            }

            is FunctionResult.Failure -> Response.errorResponse(result.value, headers)
        }
    }

    private fun getAssetChains(
        row: AssetChainsWrapper,
        assetChains: HashMap<Pair<String, Pair<Instant, Int>>, MutableList<AssetChainBlockWrapper>>,
    ): MutableList<AssetChainBlockWrapper>? {
        val assetChainKey = row.assetChains.asset to (row.time to row.lastBlockHeightPosition)
        return assetChains[assetChainKey]
    }

    private fun returnOperationFailedOnAssetChainNotFound(
        row: AssetChainsWrapper,
        headers: List<Pair<String, String>>,
    ): Response.ErrorObjectResponse<AssetChainsResponse> {
        log.error("Can't construct the asset chain for time={${row.time}}.")
        return Response.errorResponse(ApiError.OperationFailed, headers)
    }

    private fun handleInternal(
        asset: String,
        initialState: PageToken.TimePageToken?,
        request: GetAssetChainsRequest,
        startTime: Instant,
        endTime: Instant,
    ): SuspendableStream<AssetChainsWrapper, SuspendableStream.State> {
        val activeChainsSnapshotsTableName = "${db.config.schema}.${asset}_active_chains_snapshots"

        val ordering = request.pagingFrom.toSqlOrdering()
        val queryTextBuilder: QueryTextBuilder = { filter, limit ->
            """
                SELECT 
                    time, last_block_height_position, chains_count, blocks_count_at_tip, reorg, reorg_depth
                FROM $activeChainsSnapshotsTableName 
                WHERE 
                    TRUE
                    $filter
                ORDER BY time $ordering, last_block_height_position $ordering 
                LIMIT $limit
            """
        }

        val rangeQuery =
            RangeQuery
                .TimeRangeQuery(startTime, request.startInclusive, endTime, request.endInclusive, request.pagingFrom)
                .withPageToken(initialState)

        val timeFilter = DataUtils.createTimeFilter(rangeQuery)
        val additionalFilter = { it: AssetChainsWrapper ->
            timeFilter.invoke(it.time)
        }

        val stream =
            DataUtils
                .createStream(
                    db = db,
                    queryTextBuilder = queryTextBuilder,
                    bufferSize = request.pageSize + 1,
                    keyNames = arrayOf("time"),
                    dataMapper = createAssetChainsRowMapper(asset),
                    rangeQuery = rangeQuery,
                    initialState = initialState,
                    stateResolver = { PageToken.TimePageToken(it.time) },
                    streamId = null,
                ).filter(additionalFilter)

        return stream
    }

    private suspend fun getAssetChains(
        asset: String,
        assetChainSnapshots: List<AssetChainsWrapper>,
    ): List<AssetChainBlockWrapper> {
        if (assetChainSnapshots.isEmpty()) {
            return emptyList()
        }

        var minTime = assetChainSnapshots.first().time
        var maxTime = assetChainSnapshots.first().time

        assetChainSnapshots.forEach {
            if (it.time > maxTime) {
                maxTime = it.time
            } else if (it.time < minTime) {
                minTime = it.time
            }
        }

        return db.query(
            """
            SELECT
                time, last_block_height_position, block_hashes, block_heights, block_times 
            FROM ${db.config.schema}.${asset}_active_chains 
            WHERE 
                time BETWEEN '$minTime' AND '$maxTime';
            """.trimIndent(),
        ) {
            it.map(createAssetChainsMapper(asset)).toList()
        }
    }

    private fun createAssetChainsMapper(asset: String) =
        { rs: ResultSet ->
            val chainHashes =
                @Suppress(
                    "UNCHECKED_CAST",
                )
                (rs.getArray("block_hashes").array as Array<ByteArray>)
            val chainHeights =
                @Suppress(
                    "UNCHECKED_CAST",
                )
                (rs.getArray("block_heights").array as Array<Int>)
            val chainTimes =
                @Suppress(
                    "UNCHECKED_CAST",
                )
                (rs.getArray("block_times").array as Array<Timestamp>)
            assert(chainHeights.size == chainHashes.size)
            assert(chainHashes.size == chainTimes.size)
            AssetChainBlockWrapper(
                asset = asset,
                time = rs.getTimestamp("time").toInstant(),
                lastBlockHeightPosition = rs.getInt("last_block_height_position"),
                chains =
                    chainHashes.mapIndexed { index, chainHash ->
                        AssetChainBlock(
                            hash = chainHash.toHex(),
                            height = chainHeights[index].toString(),
                            time = TimeUtils.format(chainTimes[index].toInstant()),
                        )
                    },
            )
        }

    private fun createAssetChainsRowMapper(asset: String) =
        { rs: ResultSet ->
            val time = rs.getTimestamp("time").toInstant()
            val isReorg = rs.getBoolean("reorg")
            AssetChainsWrapper(
                time = time,
                lastBlockHeightPosition = rs.getInt("last_block_height_position"),
                assetChains =
                    AssetChains(
                        asset = asset,
                        time = TimeUtils.format(time),
                        chainsCount = rs.getInt("chains_count").toString(),
                        blocksCountAtTip = rs.getInt("blocks_count_at_tip").toString(),
                        reorg = if (isReorg) isReorg.toString() else null,
                        reorgDepth = if (isReorg) rs.getInt("reorg_depth").toString() else null,
                        chains = emptyList(),
                    ),
            )
        }

    private data class AssetChainsWrapper(
        val time: Instant,
        val lastBlockHeightPosition: Int,
        val assetChains: AssetChains,
    )

    private data class AssetChainBlockWrapper(
        val asset: String,
        val time: Instant,
        val lastBlockHeightPosition: Int,
        val chains: List<AssetChainBlock>,
    )
}
