package io.coinmetrics.api.endpoints.timeseries.defi

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.Response
import io.coinmetrics.api.endpoints.GetDefiBalanceSheetsEndpoint
import io.coinmetrics.api.endpoints.GetDefiBalanceSheetsRequest
import io.coinmetrics.api.models.DefiBalanceSheet
import io.coinmetrics.api.models.DefiBalanceSheetAssetItem
import io.coinmetrics.api.models.DefiBalanceSheetLiabilityItem
import io.coinmetrics.api.models.DefiBalanceSheetsResponse
import io.coinmetrics.api.models.PagingFrom
import io.coinmetrics.api.persistence.DefiDatabase
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.getTimeseriesRateLimitHeaders
import io.coinmetrics.api.statistics.defi.DefiProtocol
import io.coinmetrics.api.statistics.defi.DefiStatistics
import io.coinmetrics.api.utils.BatchUtils
import io.coinmetrics.api.utils.CommonUtils
import io.coinmetrics.api.utils.CsvUtils
import io.coinmetrics.api.utils.DataUtils
import io.coinmetrics.api.utils.QueryTextBuilder
import io.coinmetrics.api.utils.RangeQuery
import io.coinmetrics.api.utils.TimeUtils
import io.coinmetrics.api.utils.Utils
import io.coinmetrics.api.utils.paging.PageToken
import io.coinmetrics.api.utils.paging.PagingUtils
import io.coinmetrics.api.utils.paging.getPageGrouped
import io.coinmetrics.api.utils.streams.SuspendableStream
import java.math.BigDecimal
import java.math.BigInteger
import java.math.RoundingMode
import java.sql.ResultSet
import java.time.Instant

class GetDefiBalanceSheetsEndpointImpl(
    private val defiDb: DefiDatabase?,
    private val amsService: AmsService,
    private val defiStatistics: DefiStatistics,
) : GetDefiBalanceSheetsEndpoint() {
    override suspend fun handle(request: GetDefiBalanceSheetsRequest): Response<DefiBalanceSheetsResponse> {
        val headers =
            amsService
                .getTimeseriesRateLimitHeaders(
                    apiKey = request.apiKey,
                    httpRequest = request.httpRequest,
                ).getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }

        // Planning to add this parameter in request in the future
        val limitPerDefiProtocol = null
        val (defiProtocolPatternRequested, defiProtocols) =
            unwrapDefiProtocols(request).getOrElse { apiError ->
                return Response.errorResponse(apiError, headers)
            }

        val (prefetch, bufferSizePerStream) =
            Utils.getFetchProperties(
                limitPerDefiProtocol,
                defiProtocols.size,
                request.pageSize,
            )

        // At this point we know exactly that the defi database is not null
        // (because defi statistics are not null)
        val db = defiDb ?: error("Unexpected null defi database")

        val (startTime, endTime) =
            when (
                val result =
                    DataUtils.parseTimeParameters(
                        request.startTime,
                        request.startInclusive,
                        request.endTime,
                        request.endInclusive,
                        request.timezone,
                    )
            ) {
                is FunctionResult.Success -> result.value
                is FunctionResult.Failure -> return Response.errorResponse(result.value)
            }

        if (request.startTime == null && request.endTime == null) {
            if (request.startHeight != null && request.endHeight != null && request.startHeight > request.endHeight) {
                return Response.errorResponse(ApiError.BadParameter(name = "start_height", "Start height is less than end height."))
            }
        }

        val defiProtocolToStreamSpecificData =
            defiProtocols.map { defiProtocolString ->
                val defiProtocol =
                    DefiProtocol.fromString(defiProtocolString)
                        ?: return Response.errorResponse(ApiError.UnsupportedParameterValue("defi_protocol", defiProtocolString))

                defiProtocolString to defiProtocol
            }

        val streams =
            BatchUtils.sortIdsAndConcatStreams(
                streams = defiProtocolToStreamSpecificData.asSequence(),
                numberOfStreamsToPrefetch = prefetch,
                pagingFrom = request.pagingFrom,
                nextPageToken = request.nextPageToken,
                initialStreamStateParser = { PageToken.BigIntegerPageToken.parse(it) },
                limitPerStream = limitPerDefiProtocol,
                streamSupplier = { id, state, defiProtocol ->
                    handleInternal(
                        request,
                        state,
                        db,
                        defiProtocolPatternRequested,
                        bufferSizePerStream,
                        startTime,
                        endTime,
                        defiProtocol,
                    )
                },
                streamIdsAreResolvedDynamically = defiProtocolPatternRequested,
                httpRequestCoroutineContext = request.httpRequest.coroutineContext,
                logger = log,
            )

        return when (streams) {
            is FunctionResult.Success -> {
                val page =
                    streams.value.getPageGrouped(request.pageSize, request.pagingFrom) {
                        Triple(it.defiProtocol, it.calculationHeight, it.time)
                    }
                val nextPageUrl = page.nextPageToken?.let { PagingUtils.createNextPageUrl(request.httpRequest, it) }
                // todo: PLAT-708 this part of code is not expected after "getPage". It prevents the migration of this endpoint to Response.chunkedResponse. All transformations must be done before calling getPage.
                val defiBalanceSheets: List<DefiBalanceSheet> =
                    page.items.map { (key, rows) ->
                        val (defiProtocol, calculationHeight, time) = key
                        toDefiBalanceSheet(defiProtocol, calculationHeight, time, rows)
                    }

                return if (request.format == "csv") {
                    val pageItems: Array<Map<String, String?>> =
                        defiBalanceSheets
                            .flatMap { sheet ->
                                val baseMap =
                                    mapOf(
                                        "defi_protocol" to sheet.defiProtocol,
                                        "block_height" to sheet.blockHeight,
                                        "time" to sheet.time,
                                        "loans_lent_total_usd" to sheet.loansLentTotalUsd,
                                        "tvl_total_usd" to sheet.tvlTotalUsd,
                                        "net_working_capital_usd" to sheet.netWorkingCapitalUsd,
                                        "protocol_utilization_ratio" to sheet.protocolUtilizationRatio,
                                        "liquid_supply_ratio" to sheet.liquidSupplyRatio,
                                        "current_ratio" to sheet.currentRatio,
                                        "debt_to_assets_ratio" to sheet.debtToAssetsRatio,
                                        "assets_total_usd" to sheet.assetsTotalUsd,
                                        "assets_total_count" to sheet.assetsTotalCount,
                                        "asset_asset" to null,
                                        "asset_total_units" to null,
                                        "asset_loans_lent_units" to null,
                                        "asset_tvl_units" to null,
                                        "asset_total_usd" to null,
                                        "asset_loans_lent_usd" to null,
                                        "asset_tvl_usd" to null,
                                        "asset_total_share" to null,
                                        "asset_loans_lent_share" to null,
                                        "asset_tvl_share" to null,
                                        "liabilities_total_usd" to sheet.liabilitiesTotalUsd,
                                        "liabilities_total_count" to sheet.liabilitiesTotalCount,
                                        "liability_asset" to null,
                                        "liability_total_units" to null,
                                        "liability_total_usd" to null,
                                        "liability_total_share" to null,
                                    )

                                val assetsMap: Map<String, DefiBalanceSheetAssetItem> = sheet.assets.associateBy { it.asset }
                                val liabilitiesMap: Map<String, DefiBalanceSheetLiabilityItem> = sheet.liabilities.associateBy { it.asset }

                                val commonKeys = assetsMap.keys.intersect(liabilitiesMap.keys)
                                val commonMaps =
                                    commonKeys.map { assetName ->
                                        val asset = assetsMap[assetName]!!
                                        val liability = liabilitiesMap[assetName]!!
                                        baseMap + createAssetMap(asset) + createLiabilityMap(liability)
                                    }

                                val onlyAssetMaps =
                                    if (assetsMap.size != commonKeys.size) {
                                        assetsMap
                                            .filterKeys { !commonKeys.contains(it) }
                                            .map { (_, asset) -> baseMap + createAssetMap(asset) }
                                    } else {
                                        emptyList()
                                    }

                                val onlyLiabilityMaps =
                                    if (liabilitiesMap.size != commonKeys.size) {
                                        liabilitiesMap
                                            .filterKeys { !commonKeys.contains(it) }
                                            .map { (_, asset) -> baseMap + createLiabilityMap(asset) }
                                    } else {
                                        emptyList()
                                    }

                                commonMaps + onlyAssetMaps + onlyLiabilityMaps
                            }.toTypedArray()

                    @Suppress("DEPRECATION")
                    Response.rawHttpResponse(
                        CsvUtils.toCsvHttpResponse(
                            data = pageItems,
                            nextPageToken = page.nextPageToken,
                            nextPageUrl = nextPageUrl,
                            headers = headers,
                            nullValue = "",
                        ),
                    )
                } else {
                    Response.successResponse(
                        DefiBalanceSheetsResponse(
                            data = defiBalanceSheets,
                            nextPageToken = page.nextPageToken,
                            nextPageUrl = nextPageUrl,
                        ),
                        headers,
                    )
                }
            }

            is FunctionResult.Failure -> Response.errorResponse(streams.value, headers)
        }
    }

    private fun createAssetMap(asset: DefiBalanceSheetAssetItem): Map<String, String?> =
        mapOf(
            "asset_asset" to asset.asset,
            "asset_total_units" to asset.totalUnits,
            "asset_loans_lent_units" to asset.loansLentUnits,
            "asset_tvl_units" to asset.tvlUnits,
            "asset_total_usd" to asset.totalUsd,
            "asset_loans_lent_usd" to asset.loansLentUsd,
            "asset_tvl_usd" to asset.tvlUsd,
            "asset_total_share" to asset.totalShare,
            "asset_loans_lent_share" to asset.loansLentShare,
            "asset_tvl_share" to asset.tvlShare,
        )

    private fun createLiabilityMap(liability: DefiBalanceSheetLiabilityItem): Map<String, String?> =
        mapOf(
            "liability_asset" to liability.asset,
            "liability_total_units" to liability.totalUnits,
            "liability_total_usd" to liability.totalUsd,
            "liability_total_share" to liability.totalShare,
        )

    private suspend fun handleInternal(
        request: GetDefiBalanceSheetsRequest,
        initialState: PageToken.BigIntegerPageToken?,
        db: DefiDatabase,
        defiProtocolPatternRequested: Boolean,
        bufferSizePerStream: Int,
        startTime: Instant,
        endTime: Instant,
        defiProtocol: DefiProtocol,
    ): SuspendableStream<DefiProtocolBalanceSheetRow, SuspendableStream.State> {
        val (rangeQuery, additionalFilter) = determineRangeQuery(db, defiProtocol, request, startTime, endTime, request.pagingFrom)

        if (rangeQuery == null) {
            return SuspendableStream.empty()
        }

        val stream =
            DataUtils
                .createStream(
                    db = db,
                    queryTextBuilder = getQueryTextBuilder(db, request, defiProtocol),
                    bufferSize = bufferSizePerStream,
                    keyNames = arrayOf("calculation_height"),
                    dataMapper = { rs -> rs.toDefiProtocolBalanceSheetRow() },
                    rangeQuery = rangeQuery,
                    initialState = initialState,
                    stateResolver = { PageToken.BigIntegerPageToken(it.calculationHeight.toBigInteger()) },
                    streamId = if (defiProtocolPatternRequested) defiProtocol.toString() else null,
                ).let {
                    if (additionalFilter != null) {
                        it.filter(additionalFilter)
                    } else {
                        it
                    }
                }
        return stream
    }

    private fun getQueryTextBuilder(
        db: DefiDatabase,
        request: GetDefiBalanceSheetsRequest,
        defiProtocol: DefiProtocol,
    ): QueryTextBuilder =
        { filter, limit ->
            val tableName = db.protocolsBalanceSheetTableName()
            val ordering = if (request.pagingFrom == PagingFrom.START) "ASC" else "DESC"
            """
                WITH heights AS (
                    SELECT DISTINCT(calculation_height) AS height
                        FROM $tableName
                    WHERE protocol = '${defiProtocol.protocol}'
                        AND "version" = '${defiProtocol.version}'
                        AND chain = '${defiProtocol.chain}'
                        $filter
                    ORDER BY calculation_height $ordering
                    LIMIT $limit
                )
                SELECT $tableName.* FROM $tableName
                    INNER JOIN heights ON $tableName.calculation_height = heights.height
                WHERE protocol = '${defiProtocol.protocol}'
                  AND "version" = '${defiProtocol.version}'
                  AND chain = '${defiProtocol.chain}'
                  AND asset NOT LIKE 'TOTAL%'
                ORDER BY $tableName.calculation_height $ordering, $tableName.asset;
            """
        }

    private suspend fun unwrapDefiProtocols(
        request: GetDefiBalanceSheetsRequest,
    ): FunctionResult<ApiError, Pair<Boolean, Collection<String>>> {
        val allSupportedDefiProtocols = defiStatistics.getSupportedDefiProtocols()
        val supportedForApiKeyDefiProtocols =
            getSupportedForApiKeyDefiProtocols(request.apiKey)
                .getOrElse { return FunctionResult.Failure(it) }

        var defiProtocolPatternRequested = false
        val defiProtocols =
            request.defiProtocols
                .flatMap { defiProtocolOrPattern ->
                    if (defiProtocolOrPattern.contains("*")) {
                        val defiProtocolPatternParts = defiProtocolOrPattern.split("*")
                        if (defiProtocolPatternParts.size != 2) {
                            return FunctionResult.Failure(
                                ApiError.BadParameter(
                                    "defi_protocols",
                                    "Only one '*' per pattern is supported in the 'defi_protocols' parameter.",
                                ),
                            )
                        }

                        allSupportedDefiProtocols
                            .filter {
                                it.startsWith(defiProtocolPatternParts[0]) &&
                                    it.endsWith(defiProtocolPatternParts[1]) &&
                                    it in supportedForApiKeyDefiProtocols
                            }.also { if (it.isNotEmpty()) defiProtocolPatternRequested = true }
                    } else {
                        if (defiProtocolOrPattern in supportedForApiKeyDefiProtocols &&
                            defiProtocolOrPattern in allSupportedDefiProtocols
                        ) {
                            listOf(defiProtocolOrPattern)
                        } else {
                            return FunctionResult.Failure(ApiError.UnsupportedParameterValue("defi_protocols", defiProtocolOrPattern))
                        }
                    }
                }.distinct()

        if (defiProtocols.isEmpty()) {
            return FunctionResult.Failure(
                ApiError.BadParameter(
                    "defi_protocols",
                    "No DeFi protocol found for specified protocol pattern(s).",
                ),
            )
        }
        return FunctionResult.Success(defiProtocolPatternRequested to defiProtocols)
    }

    private suspend fun determineRangeQuery(
        db: DefiDatabase,
        defiProtocol: DefiProtocol,
        request: GetDefiBalanceSheetsRequest,
        startTime: Instant,
        endTime: Instant,
        pagingFrom: PagingFrom,
    ): Pair<RangeQuery.BigIntegerRangeQuery?, ((DefiProtocolBalanceSheetRow) -> Boolean)?> =
        if (request.startTime != null || request.endTime != null) {
            createRangeQueryFromTimeBoundaries(
                db,
                defiProtocol,
                startTime = startTime,
                endTime = endTime,
                startInclusive = request.startInclusive,
                endInclusive = request.endInclusive,
                pagingFrom = pagingFrom,
            )
        } else {
            RangeQuery.BigIntegerRangeQuery(
                request.startHeight?.toBigInteger(),
                request.startInclusive,
                request.endHeight?.toBigInteger(),
                request.endInclusive,
                pagingFrom,
            ) to null
        }

    private suspend inline fun createRangeQueryFromTimeBoundaries(
        db: DefiDatabase,
        defiProtocol: DefiProtocol,
        startTime: Instant,
        endTime: Instant,
        startInclusive: Boolean,
        endInclusive: Boolean,
        pagingFrom: PagingFrom,
    ): Pair<RangeQuery.BigIntegerRangeQuery?, ((DefiProtocolBalanceSheetRow) -> Boolean)?> {
        val tableName = db.protocolsBalanceSheetTableName()

        val timeFilter =
            DataUtils.createTimeFilter(
                startTime,
                startInclusive,
                endTime,
                endInclusive,
            )
        val additionalFilter: (DefiProtocolBalanceSheetRow) -> Boolean = { timeFilter(it.time) }

        val timeColumn = "time"
        val numberColumn = "calculation_height"
        val cornerRows: List<Pair<Instant, BigInteger>> =
            db.query(
                """
                (SELECT * FROM $tableName
                WHERE protocol = '${defiProtocol.protocol}'
                    AND "version" = '${defiProtocol.version}'
                    AND chain = '${defiProtocol.chain}' 
                    AND $timeColumn ${TimeUtils.toSqlCompareExpression(">=", startTime)}
                ORDER BY $timeColumn, $numberColumn
                LIMIT 1)
                UNION
                (SELECT * FROM $tableName
                WHERE protocol = '${defiProtocol.protocol}'
                    AND "version" = '${defiProtocol.version}'
                    AND chain = '${defiProtocol.chain}' 
                    AND $timeColumn ${TimeUtils.toSqlCompareExpression("<=", endTime)}
                ORDER BY $timeColumn DESC, $numberColumn DESC
                LIMIT 1);
                """.trimIndent(),
            ) { it.map { rs -> rs.getTimestamp(timeColumn).toInstant() to rs.getLong(numberColumn).toBigInteger() }.toList() }

        val (minTime, minHeight) = cornerRows.minByOrNull { it.second } ?: return null to null
        val (maxTime, maxHeight) = cornerRows.maxByOrNull { it.second } ?: return null to null

        val startInclusiveDerived = if (minTime > startTime) true else startInclusive
        val endInclusiveDerived = if (maxTime < endTime) true else endInclusive

        // expand range to catch bad miners timestamps
        val startHeight = minHeight - BigInteger.TWO
        val endHeight = maxHeight + BigInteger.TWO

        return RangeQuery.BigIntegerRangeQuery(
            startHeight,
            startInclusiveDerived,
            endHeight,
            endInclusiveDerived,
            pagingFrom,
        ) to additionalFilter
    }

    private suspend fun getSupportedForApiKeyDefiProtocols(apiKey: String): FunctionResult<ApiError, Collection<String>> {
        amsService.check(apiKey, resource = "defi_project_metrics").getOrElse { return FunctionResult.Failure(it.first) }
        // Maybe in the future we will have some restrictions in AMS
        // and use discovery method to find supported protocols individually for each API key.
        // Returning all protocols for now.
        return FunctionResult.Success(defiStatistics.getSupportedDefiProtocols())
    }
}

private fun toDefiBalanceSheet(
    defiProtocol: String,
    blockHeight: Long,
    time: Instant,
    defiProtocolBalanceSheetRows: List<DefiProtocolBalanceSheetRow>,
): DefiBalanceSheet {
    val tvl = HashMap<String, Pair<BigDecimal?, BigDecimal>>()
    val borrows = HashMap<String, Pair<BigDecimal?, BigDecimal>>()
    val ious = LinkedHashMap<String, Pair<BigDecimal?, BigDecimal>>()
    val assets = LinkedHashMap<String, Pair<BigDecimal?, BigDecimal>>()

    defiProtocolBalanceSheetRows.forEach { row ->
        val asset = row.asset

        val tvlUsd = row.tvlUsd
        val tvlAmount = row.tvlAmount
        if (tvlAmount != null && tvlAmount.signum() != 0) {
            tvl[asset] = tvlUsd to tvlAmount
        }

        val borrowsUsd = row.borrowsUsd
        val borrowsAmount = row.borrowsAmount
        if (borrowsAmount != null && borrowsAmount.signum() != 0) {
            borrows[asset] = borrowsUsd to borrowsAmount
        }

        val assetsAmount = (tvlAmount ?: BigDecimal.ZERO) + (borrowsAmount ?: BigDecimal.ZERO)
        val assetsUsd = (tvlUsd ?: BigDecimal.ZERO) + (borrowsUsd ?: BigDecimal.ZERO)
        if (assetsAmount.signum() != 0) {
            assets[asset] = assetsUsd to assetsAmount
        }

        val iousUsd = row.iousUsd
        val iousAmount = row.iousAmount
        if (iousAmount != null && iousAmount.signum() != 0) {
            ious[asset] = iousUsd to iousAmount
        }
    }

    val assetsTotalUsd = assets.values.sumOf { it.first ?: BigDecimal.ZERO }
    val assetsTotalCount = assets.size.toBigDecimal()

    val iousTotalUsd = ious.values.sumOf { it.first ?: BigDecimal.ZERO }
    val iousTotalCount = ious.size.toBigDecimal()

    val tvlTotalUsd = tvl.values.sumOf { it.first ?: BigDecimal.ZERO }
    val borrowsTotalUsd = borrows.values.sumOf { it.first ?: BigDecimal.ZERO }

    val protocolUtilizationRatio = if (iousTotalUsd == BigDecimal.ZERO) null else borrowsTotalUsd.divide(iousTotalUsd, 6, RoundingMode.UP)
    val liquidSupplyRatio = if (iousTotalUsd == BigDecimal.ZERO) null else tvlTotalUsd.divide(iousTotalUsd, 6, RoundingMode.UP)
    val deptToAssetRatio = if (assetsTotalUsd == BigDecimal.ZERO) null else iousTotalUsd.divide(assetsTotalUsd, 6, RoundingMode.UP)
    val netWorkingCapitalUsd = tvlTotalUsd - iousTotalUsd

    return DefiBalanceSheet(
        defiProtocol = defiProtocol,
        blockHeight = blockHeight.toString(),
        time = TimeUtils.dateTimeFormatter.format(time),
        assetsTotalUsd = CommonUtils.formatBigDecimal(assetsTotalUsd),
        assetsTotalCount = CommonUtils.formatBigDecimal(assetsTotalCount),
        assets = getDefiBalanceSheetAssetItems(assetsTotalUsd, tvl, borrows, assets),
        liabilitiesTotalUsd = CommonUtils.formatBigDecimal(iousTotalUsd),
        liabilitiesTotalCount = CommonUtils.formatBigDecimal(iousTotalCount),
        liabilities = getDefiBalanceSheetLiabilityItem(iousTotalUsd, ious),
        loansLentTotalUsd = CommonUtils.formatBigDecimal(borrowsTotalUsd),
        tvlTotalUsd = CommonUtils.formatBigDecimal(tvlTotalUsd),
        netWorkingCapitalUsd = CommonUtils.formatBigDecimal(netWorkingCapitalUsd),
        protocolUtilizationRatio = protocolUtilizationRatio?.let { CommonUtils.formatBigDecimal(it) },
        // the next two are the same intentionally
        liquidSupplyRatio = liquidSupplyRatio?.let { CommonUtils.formatBigDecimal(it) },
        currentRatio = liquidSupplyRatio?.let { CommonUtils.formatBigDecimal(it) },
        debtToAssetsRatio = deptToAssetRatio?.let { CommonUtils.formatBigDecimal(it) },
    )
}

private fun getDefiBalanceSheetAssetItems(
    balanceTotalUsd: BigDecimal,
    tvl: Map<String, Pair<BigDecimal?, BigDecimal>>,
    borrows: Map<String, Pair<BigDecimal?, BigDecimal>>,
    assets: Map<String, Pair<BigDecimal?, BigDecimal>>,
): List<DefiBalanceSheetAssetItem> =
    assets.map { entry ->
        val (asset, priceInfo) = entry
        val (totalUsd, totalAmount) = priceInfo
        val (borrowsUsd, borrowsAmount) = (borrows[asset] ?: (null to null))
        val (tvlUsd, tvlAmount) = (tvl[asset] ?: (null to null))

        val tvlTotalUsd = tvl.values.sumOf { it.first ?: BigDecimal.ZERO }
        val borrowsTotalUsd = borrows.values.sumOf { it.first ?: BigDecimal.ZERO }
        val realTotalUsd = totalUsd?.takeIf { it.signum() != 0 }

        val totalShare = if (balanceTotalUsd > BigDecimal.ZERO) realTotalUsd?.divide(balanceTotalUsd, 6, RoundingMode.HALF_UP) else null
        val borrowsShare = if (borrowsTotalUsd > BigDecimal.ZERO) borrowsUsd?.divide(borrowsTotalUsd, 6, RoundingMode.HALF_UP) else null
        val tvlShare = if (tvlTotalUsd > BigDecimal.ZERO) tvlUsd?.divide(tvlTotalUsd, 6, RoundingMode.HALF_UP) else null

        DefiBalanceSheetAssetItem(
            asset = asset,
            loansLentUnits = borrowsAmount?.let { CommonUtils.formatBigDecimal(it) },
            tvlUnits = tvlAmount?.let { CommonUtils.formatBigDecimal(it) },
            totalUnits = totalAmount.let { CommonUtils.formatBigDecimal(it) },
            loansLentUsd = borrowsUsd?.let { CommonUtils.formatBigDecimal(it) },
            tvlUsd = tvlUsd?.let { CommonUtils.formatBigDecimal(it) },
            totalUsd = realTotalUsd?.let { CommonUtils.formatBigDecimal(it) },
            loansLentShare = borrowsShare?.let { CommonUtils.formatBigDecimal(it) },
            tvlShare = tvlShare?.let { CommonUtils.formatBigDecimal(it) },
            totalShare = totalShare?.let { CommonUtils.formatBigDecimal(it) },
        )
    }

private fun getDefiBalanceSheetLiabilityItem(
    balanceTotalUsd: BigDecimal,
    ious: Map<String, Pair<BigDecimal?, BigDecimal>>,
): List<DefiBalanceSheetLiabilityItem> =
    ious.map { entry ->
        val (asset, priceInfo) = entry
        val (totalUsd, totalAmount) = priceInfo
        val realTotalUsd = totalUsd?.takeIf { it.signum() != 0 }
        val totalShare = if (balanceTotalUsd > BigDecimal.ZERO) realTotalUsd?.divide(balanceTotalUsd, 6, RoundingMode.HALF_UP) else null
        DefiBalanceSheetLiabilityItem(
            asset = asset,
            totalUnits = totalAmount.let { CommonUtils.formatBigDecimal(it) },
            totalUsd = realTotalUsd?.let { CommonUtils.formatBigDecimal(it) },
            totalShare = totalShare?.let { CommonUtils.formatBigDecimal(it) },
        )
    }

private fun ResultSet.toDefiProtocolBalanceSheetRow(): DefiProtocolBalanceSheetRow {
    val protocol = getString("protocol")
    val version = getString("version")
    val chain = getString("chain")
    return DefiProtocolBalanceSheetRow(
        defiProtocol = DefiProtocol(protocol, version, chain).toString(),
        asset = getString("asset"),
        tvlAmount = getBigDecimal("tvl_amount"),
        tvlUsd = getBigDecimal("tvl_usd"),
        tvlPercentage = getBigDecimal("tvl_percentage"),
        borrowsAmount = getBigDecimal("borrows_amount"),
        borrowsUsd = getBigDecimal("borrows_usd"),
        borrowsPercentage = getBigDecimal("borrows_percentage"),
        iousAmount = getBigDecimal("ious_amount"),
        iousUsd = getBigDecimal("ious_usd"),
        iousPercentage = getBigDecimal("ious_percentage"),
        time = getTimestamp("time").toInstant(),
        calculationHeight = getLong("calculation_height"),
    )
}

private data class DefiProtocolBalanceSheetRow(
    val defiProtocol: String,
    val asset: String,
    val tvlAmount: BigDecimal?,
    val tvlUsd: BigDecimal?,
    val tvlPercentage: BigDecimal?,
    val borrowsAmount: BigDecimal?,
    val borrowsUsd: BigDecimal?,
    val borrowsPercentage: BigDecimal?,
    val iousAmount: BigDecimal?,
    val iousUsd: BigDecimal?,
    val iousPercentage: BigDecimal?,
    val calculationHeight: Long,
    val time: Instant,
)
