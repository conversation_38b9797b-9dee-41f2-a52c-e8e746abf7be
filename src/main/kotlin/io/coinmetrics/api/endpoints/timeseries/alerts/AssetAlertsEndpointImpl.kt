package io.coinmetrics.api.endpoints.timeseries.alerts

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.Response
import io.coinmetrics.api.endpoints.GetAssetAlertsEndpoint
import io.coinmetrics.api.endpoints.GetAssetAlertsRequest
import io.coinmetrics.api.models.AssetAlert
import io.coinmetrics.api.models.AssetAlertHeartbeat
import io.coinmetrics.api.models.AssetAlertsResponse
import io.coinmetrics.api.models.PagingFrom
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.getTimeseriesRateLimitHeaders
import io.coinmetrics.api.utils.BatchUtils
import io.coinmetrics.api.utils.CommonUtils
import io.coinmetrics.api.utils.CsvUtils
import io.coinmetrics.api.utils.DataUtils
import io.coinmetrics.api.utils.QueryTextBuilder
import io.coinmetrics.api.utils.RangeQuery
import io.coinmetrics.api.utils.SqlUtils.escapeSql
import io.coinmetrics.api.utils.TimeUtils
import io.coinmetrics.api.utils.Utils.getBigDecimalOrNull
import io.coinmetrics.api.utils.paging.PageToken
import io.coinmetrics.api.utils.paging.PagingUtils
import io.coinmetrics.api.utils.paging.getPage
import io.coinmetrics.api.utils.streams.SuspendableStream
import io.coinmetrics.databases.Database
import io.coinmetrics.databases.getIntOrNull
import java.sql.ResultSet
import java.time.Instant

class AssetAlertsEndpointImpl(
    private val db: Database,
    private val amsService: AmsService,
) : GetAssetAlertsEndpoint() {
    private val amsParamsToEndpointParams =
        hashMapOf(
            "asset" to "assets",
            "alert" to "alerts",
        )

    private val assetAlertsMapper = { asset: String ->
        { rs: ResultSet ->
            val time = rs.getTimestamp("time").toInstant()
            AssetAlertWrapper(
                assetAlert =
                    AssetAlert(
                        asset = asset,
                        time = TimeUtils.format(time),
                        blockHeight = rs.getIntOrNull("block_height")?.toString(),
                        alert = rs.getString("alert"),
                        value = rs.getBigDecimalOrNull("value")?.let { CommonUtils.formatBigDecimal(it) },
                        threshold = rs.getBigDecimalOrNull("threshold")?.let { CommonUtils.formatBigDecimal(it) },
                        status = if (rs.getBoolean("active")) "active" else "inactive",
                    ),
                time = time,
            )
        }
    }

    override suspend fun handle(request: GetAssetAlertsRequest): Response<AssetAlertsResponse> {
        val headers =
            amsService
                .getTimeseriesRateLimitHeaders(
                    apiKey = request.apiKey,
                    httpRequest = request.httpRequest,
                ).getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }

        val checkResults =
            request.assets
                .distinct()
                .flatMap { asset ->
                    request.alerts
                        .distinct()
                        .map { alert ->
                            Triple(
                                amsService.check(
                                    apiKey = request.apiKey,
                                    resource = "asset_alerts",
                                    parameters =
                                        hashMapOf(
                                            "asset" to asset,
                                            "alert" to alert,
                                        ),
                                ) { amsParamsToEndpointParams[it] },
                                asset,
                                alert,
                            )
                        }
                }

        val supportedAssetToAlerts = mutableMapOf<String, MutableList<String>>()
        for ((checkResult, asset, alert) in checkResults) {
            val alerts = supportedAssetToAlerts.getOrPut(asset) { mutableListOf() }
            if (checkResult is FunctionResult.Failure) {
                return Response.errorResponse(
                    when (val apiError = checkResult.value.first) {
                        is ApiError.BadParameters -> continue
                        is ApiError.Forbidden ->
                            ApiError.ForbiddenWithMessage(
                                "Requested alert '$alert' for asset '$asset' is not available with supplied credentials.",
                            )

                        else -> apiError
                    },
                )
            } else {
                alerts.add(alert)
            }
        }

        for ((asset, alerts) in supportedAssetToAlerts) {
            if (alerts.isEmpty()) {
                return Response.errorResponse(
                    ApiError.BadParameter("alerts", "All requested alerts aren't supported for asset '$asset'."),
                )
            }
        }

        val (startTime, endTime, pagingFromStart) =
            DataUtils
                .parseAndValidateTimeParameters(
                    request.startTime,
                    request.startInclusive,
                    request.endTime,
                    request.endInclusive,
                    request.timezone,
                    request.pageSize,
                    request.pagingFrom,
                ).getOrElse { return Response.errorResponse(it) }

        return when (
            val result =
                BatchUtils.sortIdsAndConcatStreams(
                    streams = supportedAssetToAlerts.asSequence().map { it.toPair() },
                    streamIdsAreResolvedDynamically = false,
                    pagingFromStart = request.pagingFrom == PagingFrom.START,
                    nextPageToken = request.nextPageToken,
                    initialStreamStateParser = { PageToken.TimeAndStringPageToken.parse(it) },
                    streamSupplier = { asset, state, streamSpecificData ->
                        handleInternal(
                            asset,
                            streamSpecificData,
                            state,
                            request,
                            startTime,
                            endTime,
                            pagingFromStart,
                        )
                    },
                    httpRequestCoroutineContext = request.httpRequest.coroutineContext,
                    logger = log,
                )
        ) {
            is FunctionResult.Success -> {
                val page =
                    @Suppress("DEPRECATION")
                    result.value.getPage(request.pageSize, request.pagingFrom == PagingFrom.START)
                val nextPageUrl = page.nextPageToken?.let { PagingUtils.createNextPageUrl(request.httpRequest, it) }

                return if (request.format == "csv") {
                    val pageItems: Array<Map<String, String?>> =
                        page.items
                            .map { row ->
                                mapOf(
                                    "asset" to row.assetAlert.asset,
                                    "time" to row.assetAlert.time,
                                    "alert" to row.assetAlert.alert,
                                    "status" to row.assetAlert.status,
                                    "block_height" to row.assetAlert.blockHeight,
                                    "value" to row.assetAlert.value,
                                    "threshold" to row.assetAlert.threshold,
                                )
                            }.toTypedArray()

                    Response.rawHttpResponse(
                        @Suppress("DEPRECATION") CsvUtils.toCsvHttpResponse(
                            data = pageItems,
                            nextPageToken = page.nextPageToken,
                            nextPageUrl = nextPageUrl,
                            headers = headers,
                            nullValue = "",
                        ),
                    )
                } else {
                    val heartbeats =
                        if (request.includeHeartbeats) {
                            getHeartbeats(supportedAssetToAlerts)
                        } else {
                            null
                        }

                    Response.successResponse(
                        AssetAlertsResponse(
                            data = page.items.map { it.assetAlert },
                            // todo: heartbeats field contradicts general API design guidelines and prevents the migration of that endpoint to Response.chunkedResponse
                            heartbeats = heartbeats,
                            nextPageToken = page.nextPageToken,
                            nextPageUrl = nextPageUrl,
                        ),
                        headers,
                    )
                }
            }

            is FunctionResult.Failure -> Response.errorResponse(result.value, headers)
        }
    }

    private suspend fun getHeartbeats(assetToAlerts: Map<String, List<String>>): List<AssetAlertHeartbeat> {
        val assetAlertPairsSql =
            assetToAlerts
                .asSequence()
                .flatMap { (asset, alerts) -> alerts.asSequence().map { alert -> asset to alert } }
                .joinToString { (asset, alert) ->
                    "ROW('${escapeSql(asset)}', '${escapeSql(alert)}')"
                }
        return db.query(
            """
            SELECT asset, time, alert, block_height
            FROM asset_alert_heartbeats
            WHERE ROW(asset, alert) IN ($assetAlertPairsSql)
            ORDER BY asset, alert
            """.trimIndent(),
        ) {
            it
                .map { rs ->
                    AssetAlertHeartbeat(
                        asset = rs.getString("asset"),
                        time = TimeUtils.dateTimeFormatter.format(rs.getTimestamp("time").toInstant()),
                        blockHeight = rs.getIntOrNull("block_height")?.toString(),
                        alert = rs.getString("alert"),
                    )
                }.toList()
        }
    }

    private fun handleInternal(
        asset: String,
        alerts: List<String>,
        initialState: PageToken.TimeAndStringPageToken?,
        request: GetAssetAlertsRequest,
        startTime: Instant,
        endTime: Instant,
        pagingFromStart: Boolean,
    ): SuspendableStream<AssetAlertWrapper, SuspendableStream.State> {
        val additionalWhereExpression = "AND alert IN (${
            alerts.joinToString(separator = ",") { "'${escapeSql(it.lowercase())}'" }
        })"

        val ordering = if (request.pagingFrom == PagingFrom.START) "ASC" else "DESC"
        val queryTextBuilder: QueryTextBuilder = { filter, limit ->
            """
                SELECT 
                    time, block_height, alert, value, threshold, active
                FROM asset_alerts
                WHERE 
                    asset = '$asset'
                    $filter $additionalWhereExpression
                ORDER BY asset, time $ordering, alert $ordering
                LIMIT $limit
            """
        }

        val rangeQuery =
            RangeQuery
                .TimeAndStringRangeQuery(
                    startTime,
                    null,
                    request.startInclusive,
                    endTime,
                    null,
                    request.endInclusive,
                    pagingFromStart,
                ).withPageToken(initialState)

        val timeFilter = DataUtils.createTimeFilter(rangeQuery)
        val additionalFilter = { it: AssetAlertWrapper ->
            timeFilter(it.time)
        }

        val stream =
            DataUtils
                .createStream(
                    db = db,
                    queryTextBuilder = queryTextBuilder,
                    bufferSize = request.pageSize + 1,
                    keyNames = arrayOf("time", "alert"),
                    dataMapper = assetAlertsMapper(asset),
                    rangeQuery = rangeQuery,
                    initialState = initialState,
                    stateResolver = { PageToken.TimeAndStringPageToken(it.time, it.assetAlert.alert) },
                    streamId = null,
                ).filter(additionalFilter)

        return stream
    }

    private data class AssetAlertWrapper(
        val assetAlert: AssetAlert,
        val time: Instant,
    )
}
