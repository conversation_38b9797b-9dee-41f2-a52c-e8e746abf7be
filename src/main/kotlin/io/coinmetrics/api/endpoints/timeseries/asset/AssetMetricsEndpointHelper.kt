package io.coinmetrics.api.endpoints.timeseries.asset

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.endpoints.timeseries.asset.dataproviders.AssetMarketMetricsDataProvider
import io.coinmetrics.api.endpoints.timeseries.asset.dataproviders.RealizedVolatilityMarketsMetricsDataProvider
import io.coinmetrics.api.resources.Resources
import io.coinmetrics.api.toSuccess
import java.util.EnumMap

object AssetMetricsEndpointHelper {
    private val bbbNewNetworkMetricsAssets = listOf("sol")

    enum class Frequency(
        val value: String,
    ) {
        ONE_SECOND("1s"),
        ONE_MINUTE("1m"),
        FIVE_MINUTES("5m"),
        TEN_MINUTES("10m"),
        ONE_HOUR("1h"),
        ONE_DAY("1d"),
        ONE_BLOCK("1b"),
        ;

        companion object {
            fun fromString(value: String): Frequency? = values().firstOrNull { it.value == value }
        }
    }

    // ordering is important, add new items to the end
    enum class DataSourceGroup {
        HOURLY_REFERENCE_RATES,
        HOURLY_REFERENCE_RATES_USD,
        HOURLY_REFERENCE_RATES_EUR,
        PER_SECOND_REFERENCE_RATES,
        PER_SECOND_REFERENCE_RATES_USD,
        PER_SECOND_REFERENCE_RATES_EUR,
        DAILY_NETWORK_METRICS,
        BBB_NETWORK_METRICS,
        BBB_NEW_NETWORK_METRICS,
        DAILY_FLOWS_METRICS,
        BBB_FLOW_METRICS,
        MINUTELY_CHAIN_MONITOR_METRICS,
        BBB_CHAIN_MONITOR_METRICS,
        ASSET_MARKET_METRICS,
        REALIZED_VOLATILITY_MARKET_METRICS,
        BBB_NETWORK_METADATA_METRICS,
        HOURLY_REFERENCE_RATES_BTC,
        HOURLY_REFERENCE_RATES_ETH,
        PER_SECOND_REFERENCE_RATES_BTC,
        PER_SECOND_REFERENCE_RATES_ETH,
        BBB_ETH_SC_METRICS,
        PRINCIPAL_MARKET_PRICE_USD,
        PRINCIPAL_MARKET_PRICE_EUR,
        PRINCIPAL_MARKET_PRICE_BTC,
        PRINCIPAL_MARKET_PRICE_ETH,
        HOURLY_NETWORK_METRICS,
        MINUTELY_NETWORK_METRICS,
    }

    private val MARKET_METRICS_PREFIXES =
        listOf(
            "volume_trusted_",
            "volume_reported_",
            "open_interest_reported_",
            "futures_aggregate_funding_rate_",
            "futures_cumulative_funding_rate_",
            "liquidations_reported_future_",
        )

    fun groupByDataSource(
        asset: String,
        metrics: Collection<String>,
        frequency: Frequency,
        skipOnError: Boolean = false,
    ): FunctionResult<ApiError, List<Map.Entry<DataSourceGroup, List<String>>>> {
        return FunctionResult.Success(
            metrics
                .mapNotNull { metric ->
                    val dataSourceGroup =
                        when (val result = getDataSource(asset, metric, frequency)) {
                            is FunctionResult.Success -> result.value
                            is FunctionResult.Failure -> if (skipOnError) null else return FunctionResult.Failure(result.value)
                        }
                    dataSourceGroup?.let { metric to it }
                }.groupByTo(
                    EnumMap(DataSourceGroup::class.java),
                    { (_, dataSourceGroup) -> dataSourceGroup },
                    { (metric, _) -> metric },
                ).entries
                .sortedBy { it.key },
        )
    }

    private fun getDataSource(
        asset: String,
        metric: String,
        frequency: Frequency,
    ): FunctionResult<ApiError, DataSourceGroup> {
        val metricInfo by lazy { Resources.getMetricInfo(metric) }
        return when {
            metric == "ReferenceRate" -> {
                when (frequency) {
                    Frequency.ONE_HOUR, Frequency.ONE_DAY -> FunctionResult.Success(DataSourceGroup.HOURLY_REFERENCE_RATES)
                    Frequency.ONE_SECOND, Frequency.ONE_MINUTE -> FunctionResult.Success(DataSourceGroup.PER_SECOND_REFERENCE_RATES)
                    else -> return createFrequencyFailure(frequency, metric)
                }
            }

            metric == "ReferenceRateUSD" -> {
                when (frequency) {
                    Frequency.ONE_HOUR, Frequency.ONE_DAY -> FunctionResult.Success(DataSourceGroup.HOURLY_REFERENCE_RATES_USD)
                    Frequency.ONE_SECOND, Frequency.ONE_MINUTE -> FunctionResult.Success(DataSourceGroup.PER_SECOND_REFERENCE_RATES_USD)
                    else -> return createFrequencyFailure(frequency, metric)
                }
            }

            metric == "ReferenceRateEUR" -> {
                when (frequency) {
                    Frequency.ONE_HOUR, Frequency.ONE_DAY -> FunctionResult.Success(DataSourceGroup.HOURLY_REFERENCE_RATES_EUR)
                    Frequency.ONE_SECOND, Frequency.ONE_MINUTE -> FunctionResult.Success(DataSourceGroup.PER_SECOND_REFERENCE_RATES_EUR)
                    else -> return createFrequencyFailure(frequency, metric)
                }
            }

            metric == "ReferenceRateBTC" -> {
                when (frequency) {
                    Frequency.ONE_HOUR, Frequency.ONE_DAY -> FunctionResult.Success(DataSourceGroup.HOURLY_REFERENCE_RATES_BTC)
                    Frequency.ONE_SECOND, Frequency.ONE_MINUTE -> FunctionResult.Success(DataSourceGroup.PER_SECOND_REFERENCE_RATES_BTC)
                    else -> return createFrequencyFailure(frequency, metric)
                }
            }

            metric == "ReferenceRateETH" -> {
                when (frequency) {
                    Frequency.ONE_HOUR, Frequency.ONE_DAY -> FunctionResult.Success(DataSourceGroup.HOURLY_REFERENCE_RATES_ETH)
                    Frequency.ONE_SECOND, Frequency.ONE_MINUTE -> FunctionResult.Success(DataSourceGroup.PER_SECOND_REFERENCE_RATES_ETH)
                    else -> return createFrequencyFailure(frequency, metric)
                }
            }

            metric.startsWith("principal_market") && metric.endsWith("usd") -> {
                when (frequency) {
                    Frequency.ONE_DAY, Frequency.ONE_HOUR, Frequency.ONE_MINUTE, Frequency.ONE_SECOND ->
                        FunctionResult.Success(
                            DataSourceGroup.PRINCIPAL_MARKET_PRICE_USD,
                        )

                    else -> return createFrequencyFailure(frequency, metric)
                }
            }

            metric.startsWith("principal_market") && metric.endsWith("eur") -> {
                when (frequency) {
                    Frequency.ONE_DAY, Frequency.ONE_HOUR, Frequency.ONE_MINUTE, Frequency.ONE_SECOND ->
                        FunctionResult.Success(
                            DataSourceGroup.PRINCIPAL_MARKET_PRICE_EUR,
                        )

                    else -> return createFrequencyFailure(frequency, metric)
                }
            }

            metric.startsWith("principal_market") && metric.endsWith("btc") -> {
                when (frequency) {
                    Frequency.ONE_DAY, Frequency.ONE_HOUR, Frequency.ONE_MINUTE, Frequency.ONE_SECOND ->
                        FunctionResult.Success(
                            DataSourceGroup.PRINCIPAL_MARKET_PRICE_BTC,
                        )

                    else -> return createFrequencyFailure(frequency, metric)
                }
            }

            metric.startsWith("principal_market") && metric.endsWith("eth") -> {
                when (frequency) {
                    Frequency.ONE_DAY, Frequency.ONE_HOUR, Frequency.ONE_MINUTE, Frequency.ONE_SECOND ->
                        FunctionResult.Success(
                            DataSourceGroup.PRINCIPAL_MARKET_PRICE_ETH,
                        )

                    else -> return createFrequencyFailure(frequency, metric)
                }
            }

            MARKET_METRICS_PREFIXES.any { metric.startsWith(it) } -> {
                when {
                    frequency in AssetMarketMetricsDataProvider.applicableFrequencies ->
                        DataSourceGroup.ASSET_MARKET_METRICS.toSuccess()
                    else -> return createFrequencyFailure(frequency, metric)
                }
            }

            metric.startsWith("volatility_realized_") -> {
                if (RealizedVolatilityMarketsMetricsDataProvider.applicableFrequencies.contains(frequency)) {
                    FunctionResult.Success(DataSourceGroup.REALIZED_VOLATILITY_MARKET_METRICS)
                } else {
                    return createFrequencyFailure(frequency, metric)
                }
            }

            metric.startsWith("mempool_") ||
                metric.startsWith("mining_") ||
                metric.startsWith("time_") ||
                metric.startsWith("confirmation_") -> {
                when (frequency) {
                    Frequency.ONE_MINUTE -> FunctionResult.Success(DataSourceGroup.MINUTELY_CHAIN_MONITOR_METRICS)
                    else -> return createFrequencyFailure(frequency, metric)
                }
            }

            metric.startsWith("block_") -> {
                when (frequency) {
                    Frequency.ONE_BLOCK -> FunctionResult.Success(DataSourceGroup.BBB_CHAIN_MONITOR_METRICS)
                    else -> return createFrequencyFailure(frequency, metric)
                }
            }

            metric.startsWith("sc_") -> {
                when (frequency) {
                    Frequency.ONE_BLOCK -> FunctionResult.Success(DataSourceGroup.BBB_ETH_SC_METRICS)
                    else -> return createFrequencyFailure(frequency, metric)
                }
            }

            metric == "MinerEntity" -> {
                when (frequency) {
                    Frequency.ONE_BLOCK -> FunctionResult.Success(DataSourceGroup.BBB_NETWORK_METADATA_METRICS)
                    else -> return createFrequencyFailure(frequency, metric)
                }
            }

            metricInfo != null && metricInfo!!.flow -> {
                when (frequency) {
                    Frequency.ONE_DAY -> FunctionResult.Success(DataSourceGroup.DAILY_FLOWS_METRICS)
                    Frequency.ONE_BLOCK -> FunctionResult.Success(DataSourceGroup.BBB_FLOW_METRICS)
                    else -> return createFrequencyFailure(frequency, metric)
                }
            }

            metricInfo == null -> return createMetricFailure(metric)
            else -> {
                when {
                    asset in bbbNewNetworkMetricsAssets && frequency == Frequency.ONE_BLOCK ->
                        FunctionResult.Success(
                            DataSourceGroup.BBB_NEW_NETWORK_METRICS,
                        )

                    frequency == Frequency.ONE_MINUTE -> FunctionResult.Success(DataSourceGroup.MINUTELY_NETWORK_METRICS)
                    frequency == Frequency.ONE_HOUR -> FunctionResult.Success(DataSourceGroup.HOURLY_NETWORK_METRICS)
                    frequency == Frequency.ONE_DAY -> FunctionResult.Success(DataSourceGroup.DAILY_NETWORK_METRICS)
                    frequency == Frequency.ONE_BLOCK -> FunctionResult.Success(DataSourceGroup.BBB_NETWORK_METRICS)
                    else -> return createFrequencyFailure(frequency, metric)
                }
            }
        }
    }

    private fun createMetricFailure(metric: String): FunctionResult.Failure<ApiError, DataSourceGroup> =
        FunctionResult.Failure(ApiError.BadParameter("metrics", "Metric '$metric' is not supported."))

    private fun createFrequencyFailure(
        frequency: Frequency,
        metric: String,
    ): FunctionResult.Failure<ApiError, DataSourceGroup> =
        FunctionResult.Failure(
            ApiError.BadParameter("frequency", "Unsupported frequency '${frequency.value}' for metric '$metric'."),
        )
}
