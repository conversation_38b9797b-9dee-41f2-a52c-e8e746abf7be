package io.coinmetrics.api.endpoints.timeseries.asset.dataproviders

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.endpoints.GetTimeseriesAssetMetricsRequest
import io.coinmetrics.api.endpoints.timeseries.asset.AssetMetricsEndpointHelper
import io.coinmetrics.api.endpoints.timeseries.asset.AssetMetricsEndpointImpl
import io.coinmetrics.api.endpoints.timeseries.asset.datasources.DailyFlowMetricsDataSource
import io.coinmetrics.api.utils.MetricUtils
import io.coinmetrics.api.utils.TimeUtils
import io.coinmetrics.api.utils.paging.PageToken
import io.coinmetrics.api.utils.streams.SuspendableStream
import java.time.Instant

class DailyFlowMetricsDataProvider(
    private val dailyFlowMetricsDataSource: DailyFlowMetricsDataSource,
) : AssetMetricDataProvider<MetricUtils.MetricsWithTimeWrapper> {
    override fun isApplicable(
        metrics: List<String>,
        frequency: AssetMetricsEndpointHelper.Frequency,
        dataSourceGroup: AssetMetricsEndpointHelper.DataSourceGroup,
    ): Boolean =
        frequency == AssetMetricsEndpointHelper.Frequency.ONE_DAY &&
            dataSourceGroup == AssetMetricsEndpointHelper.DataSourceGroup.DAILY_FLOWS_METRICS

    override suspend fun checkParameters(
        asset: String,
        metrics: List<String>,
    ): FunctionResult<ApiError, AssetMetricsEndpointImpl.AssetMetricsDataSourceSpecificData> = dailyFlowMetricsDataSource.checkParameters()

    override fun provide(
        asset: String,
        patternRequested: Boolean,
        metrics: List<String>,
        startTime: Instant,
        endTime: Instant,
        pagingFromStart: Boolean,
        request: GetTimeseriesAssetMetricsRequest,
        pageTokenForDataSource: String?,
        frequencyOffset: TimeUtils.NormalizedFrequencyOffset,
        frequency: AssetMetricsEndpointHelper.Frequency,
        bufferSize: Int,
        communityApiKey: String,
        dataSourceSpecificData: AssetMetricsEndpointImpl.AssetMetricsDataSourceSpecificData?,
    ): SuspendableStream<MetricUtils.MetricsWithTimeWrapper, PageToken.TimePageToken> =
        dailyFlowMetricsDataSource.query(
            asset,
            patternRequested,
            bufferSize,
            pageTokenForDataSource,
            metrics,
            startTime,
            endTime,
            pagingFromStart,
            request,
        )
}
