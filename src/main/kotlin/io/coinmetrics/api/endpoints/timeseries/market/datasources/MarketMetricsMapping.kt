package io.coinmetrics.api.endpoints.timeseries.market.datasources

import io.coinmetrics.api.endpoints.timeseries.exchange.datasources.ExchangeBasedMetricsDataSource.ColumnToValueMapping

class ExchangeSymbol(
    exchangeId: Int,
    symbol: String,
) : ColumnToValueMapping {
    override val mapping: Map<String, Any> = mapOf("exchange_id" to exchangeId, "symbol" to symbol)
}

class Symbol(
    symbol: String,
) : ColumnToValueMapping {
    override val mapping: Map<String, Any> = mapOf("symbol" to symbol)
}

class BaseAndQuote(
    baseId: Int,
    quoteId: Int,
) : ColumnToValueMapping {
    override val mapping: Map<String, Any> = mapOf("base_id" to baseId, "quote_id" to quoteId)
}
