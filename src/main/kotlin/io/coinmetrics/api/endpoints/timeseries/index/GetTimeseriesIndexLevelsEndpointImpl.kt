package io.coinmetrics.api.endpoints.timeseries.index

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import io.coinmetrics.api.ApiError
import io.coinmetrics.api.ChunkedResponseFormat
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.Response
import io.coinmetrics.api.endpoints.GetTimeseriesIndexLevelsEndpoint
import io.coinmetrics.api.endpoints.GetTimeseriesIndexLevelsRequest
import io.coinmetrics.api.models.IndexLevel
import io.coinmetrics.api.models.IndexLevelsResponse
import io.coinmetrics.api.models.PagingFrom
import io.coinmetrics.api.models.Verification
import io.coinmetrics.api.resources.IndexInfo
import io.coinmetrics.api.resources.Resources
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.IndexDiscoveryService
import io.coinmetrics.api.service.getTimeseriesRateLimitHeaders
import io.coinmetrics.api.statistics.index.IndexStatistics
import io.coinmetrics.api.toFailure
import io.coinmetrics.api.toSuccess
import io.coinmetrics.api.utils.BatchUtils
import io.coinmetrics.api.utils.CommonUtils
import io.coinmetrics.api.utils.DataUtils
import io.coinmetrics.api.utils.DataUtils.adjust
import io.coinmetrics.api.utils.QueryTextBuilder
import io.coinmetrics.api.utils.RangeQuery
import io.coinmetrics.api.utils.SqlUtils
import io.coinmetrics.api.utils.ThrottledLogger
import io.coinmetrics.api.utils.TimeUtils
import io.coinmetrics.api.utils.Utils
import io.coinmetrics.api.utils.WildcardUtils
import io.coinmetrics.api.utils.paging.PageToken
import io.coinmetrics.api.utils.paging.getPageFlow
import io.coinmetrics.api.utils.streams.SuspendableStream
import io.coinmetrics.databases.Database
import kotlinx.coroutines.flow.map
import java.sql.ResultSet
import java.time.Instant

/**
 * We support:
 * CMBI indexes using two methodologies:
 * - cm_index table (every 15 seconds)
 * - cm_index_close (every 1 hour)
 * Fidelity index:
 * - fidelity_index (every 15 seconds)
 */
class GetTimeseriesIndexLevelsEndpointImpl(
    private val db: Database,
    private val amsService: AmsService,
    private val objectMapper: ObjectMapper,
    private val indexStatistics: IndexStatistics,
    private val indexDiscoveryService: IndexDiscoveryService,
) : GetTimeseriesIndexLevelsEndpoint() {
    private val throttledLogger = ThrottledLogger(log)

    override suspend fun handle(request: GetTimeseriesIndexLevelsRequest): Response<IndexLevelsResponse> {
        val headers =
            amsService
                .getTimeseriesRateLimitHeaders(
                    apiKey = request.apiKey,
                    httpRequest = request.httpRequest,
                ).getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }

        val (prefetch, bufferSizePerStream) =
            Utils.getFetchProperties(
                request.limitPerIndex,
                request.indexes.size,
                request.pageSize,
            )
        val (frequency, offset) = TimeUtils.parseFrequency(request.frequency)
        val normalizedFrequencyOffset =
            TimeUtils.normalizeFrequencyOffset(offset)
                ?: return Response.errorResponse(
                    ApiError.BadParameter("frequency", "Frequency '${request.frequency}' is not supported."),
                )
        val (parseInfo, patternRequested) =
            WildcardUtils
                .parseRequestCompositeParameter(
                    paramName = "indexes",
                    paramValues = request.indexes.map { it.uppercase() }.toHashSet(),
                    universeOfItems = indexStatistics.getIndexes(),
                ).getOrElse { return Response.errorResponse(it) }
        val supportedIndexesMetadata =
            parseInfo
                .flatMap { (indexes, unwrappedFromPattern) ->
                    validate(indexes, unwrappedFromPattern, frequency, request)
                        .getOrElse { return Response.errorResponse(it) }
                        .entries
                }.associate { entry -> entry.key to entry.value }

        return when (
            val result =
                BatchUtils.sortIdsAndConcatStreams(
                    streams = supportedIndexesMetadata.asSequence().map { it.toPair() },
                    streamIdsAreResolvedDynamically = patternRequested,
                    numberOfStreamsToPrefetch = prefetch,
                    pagingFromStart = request.pagingFrom == PagingFrom.START,
                    nextPageToken = request.nextPageToken,
                    initialStreamStateParser = { PageToken.TimePageToken.parse(it) },
                    limitPerStream = request.limitPerIndex,
                    streamSupplier = { index, state, indexMetadata ->
                        handleInternal(
                            index = index,
                            request = request,
                            patternRequested = patternRequested,
                            indexInfo = indexMetadata.indexInfo,
                            frequency = frequency,
                            normalizedFrequencyOffset = normalizedFrequencyOffset,
                            initialState = state,
                            bufferSize = bufferSizePerStream,
                            rangeQuery = indexMetadata.rangeQuery,
                        )
                    },
                    httpRequestCoroutineContext = request.httpRequest.coroutineContext,
                    logger = log,
                )
        ) {
            is FunctionResult.Success -> {
                val page =
                    result.value
                        .map { it.indexLevel }
                        .getPageFlow(
                            request.httpRequest,
                            request.pageSize,
                            request.pagingFrom == PagingFrom.START,
                        ).let {
                            if (request.format == "csv") {
                                it.map { obj ->
                                    (obj as? IndexLevel)?.toMap(request.includeVerification) ?: obj
                                }
                            } else {
                                it
                            }
                        }

                Response.chunkedResponse(
                    items = page,
                    headers = headers,
                    format = if (request.format == "csv") ChunkedResponseFormat.Csv(nullValue = "") else ChunkedResponseFormat.Json(),
                )
            }

            is FunctionResult.Failure -> Response.errorResponse(result.value, headers)
        }
    }

    private fun handleInternal(
        index: String,
        request: GetTimeseriesIndexLevelsRequest,
        patternRequested: Boolean,
        indexInfo: IndexInfo,
        frequency: String,
        normalizedFrequencyOffset: TimeUtils.NormalizedFrequencyOffset,
        initialState: PageToken.TimePageToken?,
        bufferSize: Int,
        rangeQuery: RangeQuery.TimeRangeQuery,
    ): SuspendableStream<IndexLevelWrapper, PageToken.TimePageToken> {
        val timeFilter = DataUtils.createTimeFilter(rangeQuery)
        val additionalFilter = { it: IndexLevelWrapper ->
            timeFilter.invoke(it.time)
        }
        val (tableName, timeFieldName, valueFieldName, indexIdFilter, joinQuery, timeFilteringQuery, temporaryTimeZone) =
            if (indexInfo.base == "fid") {
                // hourly table is not available for Fidelity indexes
                val timeFieldName = "r.index_time"

                val (joinQuery, timeFilteringQuery, temporaryTimeZone) =
                    if (frequency == "1d") {
                        // optimization for the case when 1d frequency is requested, but hourly table is not available
                        val joinQuery =
                            SqlUtils.createTimeseriesJoinQuery(
                                timeFieldName,
                                normalizedFrequencyOffset,
                                request.pagingFrom == PagingFrom.START,
                            )
                        val temporaryTimezone = normalizedFrequencyOffset.forcedTimeZone ?: request.timezone
                        Triple(joinQuery, "", temporaryTimezone)
                    } else {
                        val timeFilteringQuery =
                            SqlUtils.createTimeFilteringQuery(
                                timeFieldName = timeFieldName,
                                frequency = frequency,
                                hourlyTable = false,
                                dayOffsetHour = normalizedFrequencyOffset.hours,
                                dayOffsetMinute = normalizedFrequencyOffset.minutes,
                                timezone = normalizedFrequencyOffset.forcedTimeZone ?: request.timezone,
                            )
                        Triple("", timeFilteringQuery, null)
                    }

                QueryParameters(
                    tableName = "fidelity_index",
                    timeFieldName = timeFieldName,
                    valueFieldName = "r.index_price",
                    indexIdFilter = "r.index_id=${indexInfo.id}",
                    joinQuery = joinQuery,
                    timeFilteringQuery = timeFilteringQuery,
                    temporaryTimezone = temporaryTimeZone,
                )
            } else {
                val (tableName, hourlyTable) =
                    if ((frequency == "1h" || frequency == "1d") && indexInfo.hasHourlyValuesTable) {
                        "cm_index_close" to true
                    } else if (frequency == "1s") {
                        "cm_index_realtime" to false
                    } else {
                        "cm_index" to false
                    }
                val timeFieldName = "r.cm_index_time"

                val (joinQuery, timeFilteringQuery, temporaryTimeZone) =
                    if (frequency == "1d" && !hourlyTable) {
                        // optimization for the case when 1d frequency is requested, but hourly table is not available
                        val joinQuery =
                            SqlUtils.createTimeseriesJoinQuery(
                                timeFieldName,
                                normalizedFrequencyOffset,
                                request.pagingFrom == PagingFrom.START,
                            )
                        val temporaryTimezone = normalizedFrequencyOffset.forcedTimeZone ?: request.timezone
                        Triple(joinQuery, "", temporaryTimezone)
                    } else {
                        val timeFilteringQuery =
                            SqlUtils.createTimeFilteringQuery(
                                timeFieldName = timeFieldName,
                                frequency = frequency,
                                hourlyTable = hourlyTable,
                                dayOffsetHour = normalizedFrequencyOffset.hours,
                                dayOffsetMinute = normalizedFrequencyOffset.minutes,
                                timezone = normalizedFrequencyOffset.forcedTimeZone ?: request.timezone,
                            )
                        Triple("", timeFilteringQuery, null)
                    }

                QueryParameters(
                    tableName = tableName,
                    timeFieldName = timeFieldName,
                    valueFieldName = "r.cm_index_price",
                    indexIdFilter = "r.cm_index_id=${indexInfo.id}",
                    joinQuery = joinQuery,
                    timeFilteringQuery = timeFilteringQuery,
                    temporaryTimezone = temporaryTimeZone,
                )
            }

        val valueQuery =
            if (indexInfo.returnType == "total_return" && !indexInfo.returnMultipliers.isNullOrEmpty()) {
                val returnMultipliers =
                    indexInfo.returnMultipliers
                        ?: throw Exception("index $index one of total_return_time, total_return_multiplier or both are missing.")
                // Assuming timestamps are sorted from older to recent.
                // Timestamps need to go in reversed order to make sure that correct multiplier is applied
                val whenConditions =
                    returnMultipliers.reversed().joinToString("\n") { returnMultiplier ->
                        "WHEN $timeFieldName >= timestamp '${returnMultiplier.time}' " +
                            "THEN $valueFieldName::NUMERIC * ${returnMultiplier.value}"
                    }
                "CASE $whenConditions ELSE $valueFieldName::NUMERIC END"
            } else {
                "$valueFieldName::NUMERIC"
            }

        val verificationExists =
            request.includeVerification && (tableName == "cm_index_realtime" || tableName == "cm_index_close")
        val queryTextBuilder: QueryTextBuilder = { filter, limit ->
            val ordering = if (request.pagingFrom == PagingFrom.START) "ASC" else "DESC"
            val verificationPart = if (verificationExists) ", verification" else ""
            """
            SELECT
                $timeFieldName AS time,
                $valueQuery AS value$verificationPart
            FROM ${db.config.schema}.$tableName r
            $joinQuery
            WHERE $indexIdFilter $timeFilteringQuery
            $filter
            ORDER BY $timeFieldName $ordering
            LIMIT $limit
            """
        }

        val (beforeQuery, afterQuery) =
            if (temporaryTimeZone == null) {
                null to null
            } else {
                val escapedTimeZone = SqlUtils.escapeSql(temporaryTimeZone)
                "SET timezone='$escapedTimeZone'" to "SET timezone='UTC'"
            }

        return DataUtils
            .createStream(
                db = db,
                queryTextBuilder = queryTextBuilder,
                bufferSize = bufferSize,
                keyNames = arrayOf(timeFieldName),
                dataMapper = createMapper(index, verificationExists),
                rangeQuery = rangeQuery,
                initialState = initialState,
                stateResolver = { PageToken.TimePageToken(it.time) },
                beforeQuery = beforeQuery,
                afterQuery = afterQuery,
                streamId =
                    if (patternRequested) {
                        index
                    } else {
                        null
                    },
            ).filter(additionalFilter)
    }

    private suspend fun validate(
        indexes: List<String>,
        patternRequested: Boolean,
        frequency: String,
        request: GetTimeseriesIndexLevelsRequest,
    ): FunctionResult<ApiError, Map<String, IndexMetadata>> {
        val timeRestrictionsPerIndex =
            indexDiscoveryService
                .discoverIndexes(
                    apiKey = request.apiKey,
                    frequencies = listOf(frequency),
                    requestedIndexes = indexes,
                    ignoreUnsupportedAndForbidden = patternRequested,
                    ignoreDiscoveryScript = true,
                ).getOrElse {
                    return it.toFailure()
                }.associate { (index, _, timeRestrictions) ->
                    // frequency is not taken into account as it's constant here
                    index to timeRestrictions
                }

        val forbiddenIndexes: Set<String> =
            if (timeRestrictionsPerIndex.size < indexes.size) {
                indexes.minus(timeRestrictionsPerIndex.keys).toSet()
            } else {
                emptySet()
            }

        if (forbiddenIndexes.isNotEmpty() && !patternRequested) {
            val indexesStr = forbiddenIndexes.take(3).joinToString(", ")
            return FunctionResult.Failure(
                ApiError.ForbiddenWithMessage(
                    "Requested indexes are not available with supplied credentials: $indexesStr.",
                ),
            )
        }

        return indexes
            .minus(forbiddenIndexes)
            .mapNotNull { index ->
                val timeRestrictions = timeRestrictionsPerIndex.getValue(index) ?: return@mapNotNull null
                val indexMetadata =
                    toIndexMetadata(index, frequency, timeRestrictions, request)
                        .getOrElse { return it.toFailure() }
                        ?: error("Index metadata should not be null.")
                index to indexMetadata
            }.associate { it }
            .toSuccess()
    }

    private fun toIndexMetadata(
        index: String,
        frequency: String,
        timeRestrictions: List<String>,
        request: GetTimeseriesIndexLevelsRequest,
    ): FunctionResult<ApiError, IndexMetadata?> {
        val enforcedTime =
            indexStatistics.getIndexFrequencyStatistics(index, frequency)?.let { indexStatistics ->
                TimeUtils
                    .getEnforcedStartTime(
                        timeRestrictions,
                        request.timezone,
                        indexStatistics.maxTime.toString(),
                        request.httpRequest.receivedTime,
                    ).getOrElse { return it.toFailure() }
            }

        return DataUtils
            .parseAndValidateTimeParameters(
                request.startTime,
                request.startInclusive,
                request.endTime,
                request.endInclusive,
                request.timezone,
                request.pageSize,
                request.pagingFrom,
            ).map {
                it.adjust(enforcedTime)
                    ?: return ApiError
                        .ForbiddenWithMessage("Requested time range is not available with supplied credentials.")
                        .toFailure()
            }.map { (startTime, endTime, pagingFromStart) ->
                RangeQuery.TimeRangeQuery(
                    startTime,
                    request.startInclusive,
                    endTime,
                    request.endInclusive,
                    pagingFromStart,
                )
            }.map { rangeQuery ->
                val indexInfo = Resources.getIndexInfo(index).getOrElse { error("Index '$index' is not supported.") }
                IndexMetadata(indexInfo, rangeQuery)
            }
    }

    private class IndexLevelWrapper(
        val time: Instant,
        val indexLevel: IndexLevel,
    )

    private fun IndexLevel.toMap(includeVerification: Boolean): Map<String, String?> {
        val baseMap =
            mapOf(
                "index" to index,
                "time" to time,
                "level" to level,
            )
        return if (includeVerification) {
            baseMap +
                mapOf(
                    "verification_signature" to verification?.signature,
                    "verification_timestamp" to verification?.timestamp,
                    "verification_level" to verification?.level,
                )
        } else {
            baseMap
        }
    }

    private fun createMapper(
        index: String,
        verificationExists: Boolean,
    ): (ResultSet) -> IndexLevelWrapper =
        { rs: ResultSet ->
            val time = rs.getTimestamp("time").toInstant()
            val timeFormatted = TimeUtils.dateTimeFormatter.format(time)

            val verification =
                if (verificationExists) {
                    val verificationString = rs.getString("verification")
                    if (verificationString != null) {
                        runCatching { objectMapper.readValue<Verification>(verificationString) }
                            .onFailure {
                                throttledLogger.log {
                                    warn(
                                        "Invalid verification '$verificationString' for index $index at $timeFormatted",
                                        it,
                                    )
                                }
                            }.getOrNull()
                    } else {
                        null
                    }
                } else {
                    null
                }

            IndexLevelWrapper(
                time,
                IndexLevel(
                    time = timeFormatted,
                    index = index,
                    level = CommonUtils.formatBigDecimal(rs.getBigDecimal("value")),
                    verification = verification,
                ),
            )
        }

    private data class QueryParameters(
        val tableName: String,
        val timeFieldName: String,
        val valueFieldName: String,
        val indexIdFilter: String,
        val joinQuery: String,
        val timeFilteringQuery: String,
        val temporaryTimezone: String? = null,
    )
}
