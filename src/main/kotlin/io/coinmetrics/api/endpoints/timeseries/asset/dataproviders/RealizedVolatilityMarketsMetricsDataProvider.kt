package io.coinmetrics.api.endpoints.timeseries.asset.dataproviders

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.endpoints.GetTimeseriesAssetMetricsRequest
import io.coinmetrics.api.endpoints.timeseries.asset.AssetMetricsEndpointHelper.DataSourceGroup
import io.coinmetrics.api.endpoints.timeseries.asset.AssetMetricsEndpointHelper.Frequency
import io.coinmetrics.api.endpoints.timeseries.asset.AssetMetricsEndpointImpl
import io.coinmetrics.api.endpoints.timeseries.asset.datasources.MarketMetricsDataSource
import io.coinmetrics.api.models.PagingFrom
import io.coinmetrics.api.utils.MetricUtils
import io.coinmetrics.api.utils.TimeUtils
import io.coinmetrics.api.utils.paging.PageToken
import io.coinmetrics.api.utils.streams.SuspendableStream
import java.time.Instant

class RealizedVolatilityMarketsMetricsDataProvider(
    private val marketMetricsDataSource: MarketMetricsDataSource,
) : AssetMetricDataProvider<MetricUtils.MetricsWithTimeWrapper> {
    companion object {
        val applicableFrequencies =
            hashSetOf(
                Frequency.TEN_MINUTES,
                Frequency.ONE_HOUR,
                Frequency.ONE_DAY,
            )
    }

    override fun isApplicable(
        metrics: List<String>,
        frequency: Frequency,
        dataSourceGroup: DataSourceGroup,
    ): Boolean =
        frequency in applicableFrequencies &&
            dataSourceGroup == DataSourceGroup.REALIZED_VOLATILITY_MARKET_METRICS

    override suspend fun checkParameters(
        asset: String,
        metrics: List<String>,
    ): FunctionResult<ApiError, AssetMetricsEndpointImpl.AssetMetricsDataSourceSpecificData> = marketMetricsDataSource.checkParameters()

    override fun provide(
        asset: String,
        patternRequested: Boolean,
        metrics: List<String>,
        startTime: Instant,
        endTime: Instant,
        pagingFrom: PagingFrom,
        request: GetTimeseriesAssetMetricsRequest,
        pageTokenForDataSource: String?,
        frequencyOffset: TimeUtils.NormalizedFrequencyOffset,
        frequency: Frequency,
        bufferSize: Int,
        communityApiKey: String,
        dataSourceSpecificData: AssetMetricsEndpointImpl.AssetMetricsDataSourceSpecificData?,
    ): SuspendableStream<MetricUtils.MetricsWithTimeWrapper, PageToken.TimePageToken> =
        marketMetricsDataSource.query(
            tableName = "volatility_metrics",
            asset = asset,
            patternRequested = patternRequested,
            bufferSize = bufferSize,
            pageToken = pageTokenForDataSource,
            frequency = frequency.value,
            metrics = metrics,
            startTime = startTime,
            endTime = endTime,
            pagingFrom = pagingFrom,
            request = request,
            // The default frequency is set to 10 minutes, so we need to perform resampling for frequencies other than 10 minutes.
            doResampling = frequency != Frequency.TEN_MINUTES,
        )
}
