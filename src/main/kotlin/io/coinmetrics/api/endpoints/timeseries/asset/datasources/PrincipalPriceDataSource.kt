package io.coinmetrics.api.endpoints.timeseries.asset.datasources

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.endpoints.GetTimeseriesAssetMetricsRequest
import io.coinmetrics.api.endpoints.timeseries.asset.AssetMetricsEndpointImpl
import io.coinmetrics.api.endpoints.timeseries.asset.datasources.MetricDataUtils.MetricsHolderWithTime
import io.coinmetrics.api.resources.CurrencyInfo
import io.coinmetrics.api.resources.Resources
import io.coinmetrics.api.service.MarketStatisticsService
import io.coinmetrics.api.toSuccess
import io.coinmetrics.api.utils.CommonUtils
import io.coinmetrics.api.utils.DataUtils
import io.coinmetrics.api.utils.DataUtils.adjust
import io.coinmetrics.api.utils.QueryTextBuilder
import io.coinmetrics.api.utils.RangeQuery
import io.coinmetrics.api.utils.SqlUtils
import io.coinmetrics.api.utils.TimeUtils
import io.coinmetrics.api.utils.paging.PageToken
import io.coinmetrics.api.utils.streams.SuspendableStream
import io.coinmetrics.databases.Database
import org.slf4j.LoggerFactory
import java.sql.ResultSet
import java.text.DecimalFormat
import java.time.Instant

internal const val PRICE_METRIC = "principal_market_price"
internal const val MARKET_METRIC = "principal_market"

class PrincipalPriceDataSource(
    private val db: Database,
    private val marketStatisticsService: MarketStatisticsService,
) {
    companion object {
        private val decimalFormat = DecimalFormat("00")
        private val log = LoggerFactory.getLogger(PrincipalPriceDataSource::class.java)
    }

    data class PrincipalPriceDataSourceSpecificData(
        val baseAsset: CurrencyInfo,
        val quoteAsset: CurrencyInfo,
    ) : AssetMetricsEndpointImpl.AssetMetricsDataSourceSpecificData

    fun checkParameters(
        asset: String,
        metrics: List<String>,
    ): FunctionResult<ApiError, AssetMetricsEndpointImpl.AssetMetricsDataSourceSpecificData> {
        val baseAsset =
            Resources.getCurrencyInfo(asset) ?: run {
                log.error("Can't resolve provided asset '{}'.", asset)
                return FunctionResult.Failure(ApiError.BadParameter("asset"))
            }
        val ticker = metrics.first().substringAfterLast("_", "")
        val quoteAsset =
            Resources.getCurrencyInfo(ticker) ?: run {
                log.error("Can't resolve quote asset {} from provided metrics '{}'.", ticker, metrics)
                return FunctionResult.Failure(ApiError.BadParameter("metrics"))
            }

        return PrincipalPriceDataSourceSpecificData(baseAsset, quoteAsset).toSuccess()
    }

    fun query(
        asset: String,
        patternRequested: Boolean,
        bufferSize: Int,
        pageToken: String?,
        frequency: String,
        frequencyOffset: TimeUtils.NormalizedFrequencyOffset,
        startTime: Instant,
        endTime: Instant,
        pagingFromStart: Boolean,
        request: GetTimeseriesAssetMetricsRequest,
        metrics: List<String>,
        dataSourceSpecificData: PrincipalPriceDataSourceSpecificData,
        enforcedStartTime: Instant? = null,
    ): SuspendableStream<MetricsHolderWithTime, PageToken.TimePageToken> {
        val timeFieldName = "time"
        val priceFieldName = "price"
        val baseAsset = dataSourceSpecificData.baseAsset
        val quoteAsset = dataSourceSpecificData.quoteAsset
        val priceMetricName = metrics.find { it.startsWith(PRICE_METRIC) } ?: "${PRICE_METRIC}_${quoteAsset.cmTicker}"
        val marketMetricName = metrics.find { !it.startsWith(PRICE_METRIC) } ?: "${MARKET_METRIC}_${quoteAsset.cmTicker}"

        val initialState =
            pageToken?.let {
                try {
                    PageToken.TimePageToken.parse(it)
                } catch (_: Exception) {
                    error("Can't parse provided next_page_token '$it'.")
                }
            }

        val (startTimeParam, endTimeParam, _) =
            Triple(startTime, endTime, pagingFromStart)
                /**
                 * Returning an empty stream for simplicity.
                 * We could enhance this later, for example, by returning a function failure result if a pattern was not requested —
                 * but that would require more refactoring, which may not be necessary at this point.
                 */
                .adjust(enforcedStartTime) ?: return SuspendableStream.empty()

        val rangeQuery =
            RangeQuery
                .TimeRangeQuery(
                    startTimeParam,
                    request.startInclusive,
                    endTimeParam,
                    request.endInclusive,
                    pagingFromStart,
                ).withPageToken(initialState)

        val timeZone = SqlUtils.escapeSql(frequencyOffset.forcedTimeZone ?: request.timezone)

        // timezone env is required only for queries which use INTERVAL keyword
        val (beforeQuery, afterQuery) =
            if (frequency == "1d" && timeZone != "UTC") {
                "SET timezone='$timeZone'" to "SET timezone='UTC'"
            } else {
                null to null
            }

        val queryTextBuilder: QueryTextBuilder = { filter, limit ->
            val (joinQuery, timeFilteringQuery) =
                if (frequency == "1d") {
                    val hours = decimalFormat.format(frequencyOffset.hours)
                    val minutes = decimalFormat.format(frequencyOffset.minutes)
                    val startTime = "$hours:$minutes:00"
                    if (pagingFromStart) {
                        listOf(
                            """
                    INNER JOIN generate_series('1970-01-01 $startTime'::timestamp, NOW()::timestamp, '1 day'::interval) AS t(time)
                    ON r.$timeFieldName = t.time::timestamptz AT TIME ZONE '$timeZone'
                    """,
                            "",
                        )
                    } else {
                        listOf(
                            """
                    INNER JOIN generate_series(CONCAT(NOW()::date, ' $startTime')::timestamp, '1970-01-01'::timestamp, -'1 day'::interval) AS t(time)
                    ON r.$timeFieldName = t.time::timestamptz AT TIME ZONE '$timeZone'
                    """,
                            "",
                        )
                    }
                } else if (frequency == "1m") {
                    val timeFilteringQuery =
                        SqlUtils.createTimeFilteringQuery(
                            "r.$timeFieldName",
                            frequency,
                            hourlyTable = false,
                            dayOffsetHour = frequencyOffset.hours,
                            dayOffsetMinute = frequencyOffset.minutes,
                            timezone = frequencyOffset.forcedTimeZone ?: request.timezone,
                        )
                    listOf("", timeFilteringQuery)
                } else {
                    // 1h, 1s
                    listOf("", "")
                }

            val tableName =
                if ((frequency == "1d" || frequency == "1h") && frequencyOffset.minutes == 0) {
                    "principal_price_1h_${baseAsset.id}_${quoteAsset.id}"
                } else {
                    "principal_price_1s_${baseAsset.id}_${quoteAsset.id}"
                }

            val order = if (pagingFromStart) "ASC" else "DESC"

            """
            SELECT
                r.$timeFieldName,
                r.$priceFieldName::NUMERIC,
                r.trade_exchange_id::INTEGER,
                r.trade_symbol::TEXT
            FROM ${db.config.schema}.$tableName r
                $joinQuery
            WHERE
                TRUE
                $timeFilteringQuery
                $filter
            ORDER BY r.$timeFieldName $order
            LIMIT $limit
            """.trimIndent()
        }

        return DataUtils
            .createStream(
                db = db,
                queryTextBuilder = queryTextBuilder,
                bufferSize = bufferSize,
                keyNames = arrayOf("r.$timeFieldName"),
                dataMapper = principalPriceMapper(),
                rangeQuery = rangeQuery,
                initialState = initialState,
                stateResolver = { PageToken.TimePageToken(it.time) },
                beforeQuery = beforeQuery,
                afterQuery = afterQuery,
                streamId = if (patternRequested) asset else null,
            ).map {
                val market = marketStatisticsService.getSpotMarket(it.exchangeId, it.tradeSymbol)
                MetricsHolderWithTime(
                    time = it.time,
                    metrics =
                        mapOf(
                            priceMetricName to it.price,
                            marketMetricName to market.toString(),
                        ),
                )
            }
    }

    private fun principalPriceMapper(): (ResultSet) -> PrincipalPrice =
        { rs: ResultSet ->
            PrincipalPrice(
                time = rs.getTimestamp("time").toInstant(),
                price = CommonUtils.formatBigDecimal(rs.getBigDecimal("price")),
                exchangeId = rs.getInt("trade_exchange_id"),
                tradeSymbol = rs.getString("trade_symbol"),
            )
        }

    private class PrincipalPrice(
        val time: Instant,
        val price: String,
        val exchangeId: Int,
        val tradeSymbol: String,
    )
}
