package io.coinmetrics.api.endpoints.timeseries.asset.datasources

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.endpoints.GetTimeseriesAssetMetricsRequest
import io.coinmetrics.api.endpoints.timeseries.asset.AssetMetricsEndpointImpl
import io.coinmetrics.api.models.PagingFrom
import io.coinmetrics.api.toSuccess
import io.coinmetrics.api.utils.DataUtils
import io.coinmetrics.api.utils.MetricUtils
import io.coinmetrics.api.utils.QueryTextBuilder
import io.coinmetrics.api.utils.RangeQuery
import io.coinmetrics.api.utils.SqlUtils
import io.coinmetrics.api.utils.paging.PageToken
import io.coinmetrics.api.utils.streams.SuspendableStream
import io.coinmetrics.api.utils.toSqlOrdering
import io.coinmetrics.databases.Database
import java.time.Instant

class MinutelyChainMonitorMetricsDataSource(
    val db: Database,
) {
    fun checkParameters(): FunctionResult<ApiError, AssetMetricsEndpointImpl.AssetMetricsDataSourceSpecificData> =
        AssetMetricsEndpointImpl.AssetMetricsDataSourceSpecificData.EMPTY.toSuccess()

    fun query(
        asset: String,
        patternRequested: Boolean,
        bufferSize: Int,
        pageToken: String?,
        metrics: List<String>,
        startTime: Instant,
        endTime: Instant,
        pagingFrom: PagingFrom,
        request: GetTimeseriesAssetMetricsRequest,
    ): SuspendableStream<MetricUtils.MetricsWithTimeWrapper, PageToken.TimePageToken> {
        val timeFieldName = "time"
        val tableName = "${db.config.schema}.${asset}_metrics"
        val metricsInClauseSqlParams = metrics.joinToString { "'${SqlUtils.escapeSql(it)}'" }

        val initialState =
            pageToken?.let {
                try {
                    PageToken.TimePageToken.parse(it)
                } catch (_: Exception) {
                    error("Can't parse provided next_page_token '$it'.")
                }
            }

        val rangeQuery =
            RangeQuery
                .TimeRangeQuery(startTime, request.startInclusive, endTime, request.endInclusive, pagingFrom)
                .withPageToken(initialState)

        val timeFilter = DataUtils.createTimeFilter(rangeQuery)
        val additionalFilter = { it: MetricUtils.MetricsWithTimeWrapper ->
            timeFilter.invoke(it.time)
        }

        val queryTextBuilder: QueryTextBuilder = { filter, limit ->
            val ordering = pagingFrom.toSqlOrdering()

            val distinctTimestamps =
                """
               SELECT 
                   DISTINCT($timeFieldName) 
               FROM $tableName
               WHERE
                   name IN ($metricsInClauseSqlParams) 
                   $filter
               ORDER BY $timeFieldName $ordering
               LIMIT $limit
            """

            """
                SELECT 
                    $timeFieldName, name, value 
                FROM $tableName
                WHERE
                    name IN ($metricsInClauseSqlParams)
                    AND $timeFieldName IN (
                        $distinctTimestamps
                    )
                ORDER BY 1 $ordering
            """
        }

        return DataUtils
            .createStream(
                db = db,
                queryTextBuilder = queryTextBuilder,
                bufferSize = bufferSize,
                keyNames = arrayOf(timeFieldName),
                dataMapper = ChainMonitorUtils.mapper,
                rangeQuery = rangeQuery,
                initialState = initialState,
                stateResolver = { PageToken.TimePageToken(it.time) },
                streamId = if (patternRequested) asset else null,
            ).collapseByKey { it.time }
            .map { (time, singleTimeData) ->
                // convert rows (timeInstant, apiMetricName, valueString) to DataWithTimeWrapper
                MetricUtils.MetricsWithTimeWrapper(
                    time = time,
                    // Map<metricName, metricValue>
                    metrics = singleTimeData.map { row -> row.metric to row.value },
                )
            }.filter(additionalFilter)
    }
}
