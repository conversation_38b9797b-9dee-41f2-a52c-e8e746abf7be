package io.coinmetrics.api.endpoints.timeseries.market.trades.provider

import io.coinmetrics.api.service.MarketConstraints
import io.coinmetrics.databases.Database
import io.coinmetrics.defi.client.model.RawMarketData

sealed class TradesStreamSpecificData(
    val marketConstraints: MarketConstraints,
) {
    class Cex(
        marketConstraints: MarketConstraints,
        val tradesDb: Database,
    ) : TradesStreamSpecificData(marketConstraints)

    class Defi(
        marketConstraints: MarketConstraints,
        val poolMarketId: String,
        val rawMarket: RawMarketData,
        val exchangeName: String,
        val exchangeNetwork: String,
        val poolId: ByteArray,
    ) : TradesStreamSpecificData(marketConstraints)
}
