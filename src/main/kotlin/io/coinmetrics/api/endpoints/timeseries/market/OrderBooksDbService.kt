package io.coinmetrics.api.endpoints.timeseries.market

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.ChunkedResponseFormat
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.Response
import io.coinmetrics.api.endpoints.GetTimeseriesMarketOrderbooksEndpoint
import io.coinmetrics.api.endpoints.GetTimeseriesMarketOrderbooksRequest
import io.coinmetrics.api.model.DerivativesMarketType.FUTURE
import io.coinmetrics.api.model.DerivativesMarketType.OPTION
import io.coinmetrics.api.model.MarketWithSymbol
import io.coinmetrics.api.model.NormalizedMarket
import io.coinmetrics.api.model.NormalizedMarket.DerivativesNormalizedMarket
import io.coinmetrics.api.model.NormalizedMarket.SpotNormalizedMarket
import io.coinmetrics.api.model.ParsedMarket
import io.coinmetrics.api.models.BookEntry
import io.coinmetrics.api.models.MarketOrderBook
import io.coinmetrics.api.models.MarketOrderbookResponse
import io.coinmetrics.api.models.PagingFrom
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.MarketConstraints
import io.coinmetrics.api.service.MarketResolvingService
import io.coinmetrics.api.service.MarketStatisticsService
import io.coinmetrics.api.service.getTimeseriesRateLimitHeaders
import io.coinmetrics.api.toFailure
import io.coinmetrics.api.utils.BatchUtils
import io.coinmetrics.api.utils.CommonUtils
import io.coinmetrics.api.utils.DataUtils
import io.coinmetrics.api.utils.DataUtils.adjust
import io.coinmetrics.api.utils.QueryTextBuilder
import io.coinmetrics.api.utils.RangeQuery
import io.coinmetrics.api.utils.SqlUtils
import io.coinmetrics.api.utils.TimeUtils
import io.coinmetrics.api.utils.Utils
import io.coinmetrics.api.utils.paging.PageToken
import io.coinmetrics.api.utils.paging.getPageFlow
import io.coinmetrics.api.utils.streams.SuspendableStream
import io.coinmetrics.databases.Database
import java.math.BigDecimal
import java.sql.ResultSet
import java.time.Instant
import java.time.ZoneId
import java.time.temporal.ChronoUnit

class OrderBooksDbService(
    private val booksDb: Database,
    private val amsService: AmsService,
    private val marketResolvingService: MarketResolvingService,
    private val marketStatisticsService: MarketStatisticsService,
    private val communityApiKey: String,
    private val tenPercentMidPriceBookStartTime: String,
    private val useNewBooksTables: Boolean,
) : GetTimeseriesMarketOrderbooksEndpoint() {
    private val bookDepthSelector = S3OrderBookDepthSelector(marketStatisticsService)

    override suspend fun handle(request: GetTimeseriesMarketOrderbooksRequest): Response<MarketOrderbookResponse> {
        val headers =
            amsService
                .getTimeseriesRateLimitHeaders(
                    apiKey = request.apiKey,
                    httpRequest = request.httpRequest,
                ).getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }

        if (request.format != "json") {
            return Response.errorResponse(
                ApiError.BadParameter(
                    "format",
                    "The provided format is not yet supported.",
                ),
                headers,
            )
        }

        val (marketsConstraints, marketPatternRequested) =
            marketResolvingService
                .parseAndCheckMarkets(
                    apiKey = request.apiKey,
                    requestedMarkets = request.markets,
                    marketFilter = { market ->
                        market is ParsedMarket.ParsedSpotMarket ||
                            (
                                market is ParsedMarket.ParsedDerivativesMarket &&
                                    (market.type == FUTURE || market.type == OPTION)
                            )
                    },
                    errorMessageForFilteredOutMarketsIfPatternIsNotRequested = { "Only spot, future and option markets are supported." },
                ).getOrElse { return Response.errorResponse(it) }

        return buildResponseStream(
            request = request,
            marketsConstraints = marketsConstraints,
            marketPatternRequested = marketPatternRequested,
        ).getOrElse { return Response.errorResponse(it, headers) }.let { result ->
            // json
            val pageFlow =
                result.map { it.orderBook }.getPageFlow(
                    httpRequest = request.httpRequest,
                    pageSize = request.pageSize,
                    pagingFromStart = request.pagingFrom == PagingFrom.START,
                )
            Response.chunkedResponse(
                items = pageFlow,
                headers = headers,
                format = ChunkedResponseFormat.Json(),
            )
        }
    }

    internal suspend fun buildResponseStream(
        request: GetTimeseriesMarketOrderbooksRequest,
        marketsConstraints: Map<String, MarketConstraints>,
        marketPatternRequested: Boolean,
    ): FunctionResult<ApiError, SuspendableStream<MarketOrderbookWrapper, SuspendableStream.State>> {
        val (prefetch, bufferSizePerStream) =
            Utils.getFetchProperties(
                request.limitPerMarket,
                marketsConstraints.size,
                request.pageSize,
            )

        val parsedDepth =
            parseAndValidateDepth(request.depthLimit, request.pageSize).getOrElse {
                return FunctionResult.Failure(it)
            }

        val startTimeStr =
            if (request.depthLimit == "10pct_mid_price" &&
                (request.startTime == null || request.startTime < tenPercentMidPriceBookStartTime)
            ) {
                tenPercentMidPriceBookStartTime
            } else {
                request.startTime
            }

        val parsedTimeParams =
            DataUtils
                .parseAndValidateTimeParameters(
                    startTimeStr,
                    request.startInclusive,
                    request.endTime,
                    request.endInclusive,
                    request.timezone,
                    request.pageSize,
                    request.pagingFrom,
                ).getOrElse { return it.toFailure() }

        val downSamplingConfig =
            TimeUtils
                .createStatefulDownSamplerConfig(
                    granularity = request.granularity,
                    pagingFromStart = request.pagingFrom == PagingFrom.START,
                    withAlignment = true,
                    timezone = ZoneId.of(request.timezone),
                ).getOrElse { return it.toFailure() }

        return BatchUtils.sortIdsAndConcatStreams(
            streams = marketsConstraints.asSequence().map { it.toPair() },
            numberOfStreamsToPrefetch = prefetch,
            pagingFromStart = request.pagingFrom == PagingFrom.START,
            nextPageToken = request.nextPageToken,
            initialStreamStateParser = {
                PageToken.TimePageToken.parse(it)
            },
            limitPerStream = request.limitPerMarket,
            streamSupplier = { id, state, marketConstraints ->
                val bufferSize =
                    if (marketConstraints.marketId is DerivativesNormalizedMarket &&
                        marketConstraints.marketId.type == OPTION
                    ) {
                        bufferSizePerStream
                    } else {
                        // As an optimization in the API to minimize heap memory usage and decrease Kafka consumer lags,
                        // I suggest decreasing the internal buffer size (limit) for order book SQL queries from 251 (with their requested page_size of 250) to 51.
                        // This adjustment will enable the API to retrieve fewer book snapshots before transmitting a response chunk to the client.
                        minOf(51, bufferSizePerStream)
                    }

                val (marketId, keyMinTime, keyMaxTime, market) = marketConstraints
                val (startTime, endTime, pagingFromStart) =
                    parsedTimeParams.adjust(
                        DataUtils.communityEnforcedStart(request.apiKey, communityApiKey),
                        keyMinTime,
                        keyMaxTime,
                    )
                        /**
                         * Returning an empty stream for simplicity.
                         * We could enhance this later, for example, by returning a function failure result if a pattern was not requested —
                         * but that would require more refactoring, which may not be necessary at this point.
                         */
                        ?: return@sortIdsAndConcatStreams SuspendableStream.empty<MarketOrderbookWrapper, PageToken.TimePageToken>()
                val rangeQuery =
                    RangeQuery
                        .TimeRangeQuery(
                            startTime,
                            request.startInclusive,
                            endTime,
                            request.endInclusive,
                            pagingFromStart,
                        ).withPageToken(state)
                handleInternalLegacy(
                    market = market,
                    marketId = marketId,
                    rangeQuery = rangeQuery,
                    initialState = state,
                    request = request,
                    marketPatternRequested = marketPatternRequested,
                    parsedDepth = parsedDepth,
                    bufferSize = bufferSize,
                    downSamplingConfig = downSamplingConfig,
                )
            },
            streamIdsAreResolvedDynamically = marketPatternRequested,
            httpRequestCoroutineContext = request.httpRequest.coroutineContext,
            logger = log,
        )
    }

    fun handleInternalLegacy(
        market: ParsedMarket,
        marketId: NormalizedMarket,
        rangeQuery: RangeQuery.TimeRangeQuery,
        initialState: PageToken.TimePageToken?,
        request: GetTimeseriesMarketOrderbooksRequest,
        marketPatternRequested: Boolean,
        parsedDepth: Int,
        bufferSize: Int,
        downSamplingConfig: TimeUtils.StatefulDownSamplerConfig?,
    ): SuspendableStream<MarketOrderbookWrapper, PageToken.TimePageToken> {
        val (enforcedDepth, dbDepth) = bookDepthSelector.getApplicableDepths(market, parsedDepth, rangeQuery.startKey, rangeQuery.endKey)
        return handleInternal(
            market = market,
            marketId = marketId,
            rangeQuery = rangeQuery,
            initialState = initialState,
            request = request,
            marketPatternRequested = marketPatternRequested,
            enforcedDepth = enforcedDepth,
            dbDepth = dbDepth,
            bufferSize = bufferSize,
            downSamplingConfig = downSamplingConfig,
        )
    }

    fun handleInternal(
        market: ParsedMarket,
        marketId: NormalizedMarket,
        rangeQuery: RangeQuery.TimeRangeQuery,
        initialState: PageToken.TimePageToken?,
        request: GetTimeseriesMarketOrderbooksRequest,
        marketPatternRequested: Boolean,
        enforcedDepth: Int?,
        dbDepth: Int,
        bufferSize: Int,
        downSamplingConfig: TimeUtils.StatefulDownSamplerConfig?,
    ): SuspendableStream<MarketOrderbookWrapper, PageToken.TimePageToken> {
        val timeFilter = DataUtils.createTimeFilter(rangeQuery)
        val additionalFilter = { it: MarketOrderbookWrapper ->
            timeFilter.invoke(it.time)
        }

        val (tableName, marketFilter) =
            if (useNewBooksTables) {
                getTableNameAndMarketFilter(marketId)
            } else {
                getTableNameAndMarketFilterLegacy(marketId)
            } ?: return SuspendableStream.empty()

        val timeFieldName = "book_time"

        val queryTextBuilder: QueryTextBuilder =
            { filter, limit ->
                val ordering = if (request.pagingFrom == PagingFrom.START) "ASC" else "DESC"
                // The amount of book asks and book bids in db can be different from what user specified.
                // In this case we take only the amount user would like to see.
                // We take first elements from arrays because that is what corresponds to the top of order book (the highest bid and lowest ask):
                // https://guides.cryptowat.ch/trader-education/order-books-and-market-depth-charts-explained#the-top-of-the-order-book.
                val (bidsQuery, asksQuery) =
                    if (enforcedDepth != null) {
                        "book_bids[1:$enforcedDepth]" to "book_asks[1:$enforcedDepth]"
                    } else {
                        "book_bids" to "book_asks"
                    }

                /**
                 * This is a temporary filter to work around the invalid bids and asks with empty amounts and prices for Deribit options books
                 * https://coinmetrics.atlassian.net/browse/MD-3306
                 */
                val emptyAmountPriceFilter =
                    if (marketId.exchange == 37 && marketId is DerivativesNormalizedMarket && marketId.type == OPTION) {
                        """AND book_asks != '{"(,,1)"}' AND book_bids != '{"(,,1)"}'"""
                    } else {
                        ""
                    }

                """
                    SELECT
                        $timeFieldName,
                        $asksQuery,
                        $bidsQuery,
                        book_database_time,
                        book_exchange_sequence_id
                    FROM ${booksDb.config.schema}.$tableName
                    WHERE
                        $marketFilter AND
                        book_exchange_id=${marketId.exchange} AND
                        book_depth_limit=$dbDepth
                        $filter
                        $emptyAmountPriceFilter
                    ORDER BY $timeFieldName $ordering
                    LIMIT $limit
                """
            }

        val downSamplingFilter = downSamplingConfig?.let { TimeUtils.createStatefulDownSampler(it) }

        return DataUtils
            .createStream(
                db = booksDb,
                queryTextBuilder = queryTextBuilder,
                bufferSize = bufferSize,
                keyNames = arrayOf(timeFieldName),
                dataMapper = createMapper(market.toString(), request.depthLimit == "10pct_mid_price"),
                rangeQuery = rangeQuery,
                initialState = initialState,
                stateResolver = { PageToken.TimePageToken(it.time) },
                streamId =
                    if (marketPatternRequested) {
                        market.toString()
                    } else {
                        null
                    },
            ).filter(additionalFilter)
            .let { stream ->
                if (downSamplingFilter == null) {
                    stream
                } else {
                    stream.filter { downSamplingFilter(it.time) }
                }
            }
    }

    private fun getTableNameAndMarketFilter(marketId: NormalizedMarket): Pair<String, String>? {
        val escapedMarketSymbol = if (marketId is MarketWithSymbol) SqlUtils.escapeSql(marketId.symbol) else ""
        return when (marketId) {
            is SpotNormalizedMarket -> {
                if (marketId.symbol.isEmpty()) {
                    return null
                }
                "books_spot"
            }

            is DerivativesNormalizedMarket ->
                when (marketId.type) {
                    FUTURE -> "books_future"
                    OPTION -> "books_option"
                }

            is NormalizedMarket.DefiNormalizedMarket ->
                error(
                    "Only spot, future, and option markets are supported but $marketId was requested.",
                )
        } to "book_symbol='$escapedMarketSymbol'"
    }

    private fun getTableNameAndMarketFilterLegacy(marketId: NormalizedMarket): Pair<String, String> =
        when {
            marketId is SpotNormalizedMarket -> "spot_books" to "book_base_id=${marketId.base} AND book_quote_id=${marketId.quote}"
            marketId is DerivativesNormalizedMarket && marketId.type == FUTURE -> "futures_books" to "book_symbol='${marketId.symbol}'"
            marketId is DerivativesNormalizedMarket && marketId.type == OPTION -> "option_books" to "book_symbol='${marketId.symbol}'"
            else -> error("Only spot, future, and option markets are supported but $marketId was requested.")
        }

    /**
     * @return 0 means 10pct_mid_price
     */
    fun parseAndValidateDepth(
        depth: String,
        pageSize: Int,
    ): FunctionResult<ApiError, Int> {
        if (depth.equals("full_book", ignoreCase = true)) {
            if (pageSize > 100) {
                return FunctionResult.Failure(
                    ApiError.BadParameter(
                        "page_size",
                        "Maximum value for unlimited order books is 100.",
                    ),
                )
            }
            return FunctionResult.Success(30000)
        }
        if (depth.equals("10pct_mid_price", ignoreCase = true)) {
            if (pageSize > 100) {
                return FunctionResult.Failure(
                    ApiError.BadParameter(
                        "page_size",
                        "Maximum value for 10pct_mid_price order books is 100.",
                    ),
                )
            }
            return FunctionResult.Success(0)
        }

        val depthAsInt =
            depth.toIntOrNull()?.takeIf { it in 1..30000 } ?: return FunctionResult.Failure(
                ApiError.BadParameter(
                    "depth_limit",
                    "Supported values are 1-30000 or '10pct_mid_price' or 'full_book'.",
                ),
            )

        if (pageSize > 100 && depthAsInt > 100) {
            return FunctionResult.Failure(
                ApiError.BadParameter(
                    "page_size",
                    "Maximum value for unlimited order books is 100.",
                ),
            )
        }

        return FunctionResult.Success(depthAsInt)
    }

    private fun parseBookPair(obj: Any?): Pair<BigDecimal, BigDecimal> {
        val comps = obj.toString().trim('(', ')').split(',')
        return Pair(
            // size
            comps[0].toBigDecimal(),
            // price
            comps[1].toBigDecimal(),
        )
    }

    private fun createMapper(
        marketId: String,
        calculate10PctBook: Boolean,
    ): (ResultSet) -> MarketOrderbookWrapper =
        { rs: ResultSet ->
            val time = rs.getTimestamp("book_time").toInstant()
            val timeFormatted = TimeUtils.dateTimeFormatter.format(time)
            val databaseTime = rs.getTimestamp("book_database_time").toInstant()
            val databaseTimeFormatted = TimeUtils.dateTimeFormatter.format(databaseTime)
            val exchangeSequenceId = rs.getBigDecimal("book_exchange_sequence_id")

            var asks = (rs.getArray("book_asks").array as Array<*>).map { parseBookPair(it) }
            var bids = (rs.getArray("book_bids").array as Array<*>).map { parseBookPair(it) }

            if (calculate10PctBook) {
                val result = MarketEndpointUtil.cutBookTo10PctMidPrice(asks, bids)
                asks = result.first
                bids = result.second
            }

            MarketOrderbookWrapper(
                time,
                MarketOrderBook(
                    time = timeFormatted,
                    market = marketId,
                    coinMetricsId =
                        exchangeSequenceId?.let { CommonUtils.formatBigDecimal(it) }
                            ?: "${ChronoUnit.MICROS.between(Instant.EPOCH, time)}-0",
                    asks =
                        asks.map {
                            BookEntry(
                                propertySize = CommonUtils.formatBigDecimal(it.first),
                                price = CommonUtils.formatBigDecimal(it.second),
                            )
                        },
                    bids =
                        bids.map {
                            BookEntry(
                                propertySize = CommonUtils.formatBigDecimal(it.first),
                                price = CommonUtils.formatBigDecimal(it.second),
                            )
                        },
                    databaseTime = databaseTimeFormatted,
                ),
            )
        }

    // It is used to wrap autogenerated MarketOrderbook with additional parameters
    class MarketOrderbookWrapper(
        val time: Instant,
        val orderBook: MarketOrderBook,
    )
}
