package io.coinmetrics.api.endpoints.timeseries.market

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.endpoints.GetTimeseriesMarketOrderbooksRequest
import io.coinmetrics.api.model.DerivativesMarketType.FUTURE
import io.coinmetrics.api.model.DerivativesMarketType.OPTION
import io.coinmetrics.api.model.MarketWithSymbol
import io.coinmetrics.api.model.NormalizedMarket
import io.coinmetrics.api.model.NormalizedMarket.DerivativesNormalizedMarket
import io.coinmetrics.api.model.NormalizedMarket.SpotNormalizedMarket
import io.coinmetrics.api.model.ParsedMarket
import io.coinmetrics.api.models.BookEntry
import io.coinmetrics.api.models.MarketOrderBook
import io.coinmetrics.api.models.PagingFrom
import io.coinmetrics.api.utils.CommonUtils
import io.coinmetrics.api.utils.DataUtils
import io.coinmetrics.api.utils.QueryTextBuilder
import io.coinmetrics.api.utils.RangeQuery
import io.coinmetrics.api.utils.SqlUtils
import io.coinmetrics.api.utils.TimeUtils
import io.coinmetrics.api.utils.WithTime
import io.coinmetrics.api.utils.paging.PageToken
import io.coinmetrics.api.utils.streams.SuspendableStream
import io.coinmetrics.databases.Database
import java.math.BigDecimal
import java.sql.ResultSet
import java.time.Instant
import java.time.temporal.ChronoUnit

class OrderBooksDbService(
    private val booksDb: Database,
    private val useNewBooksTables: Boolean,
) {
    fun handleInternal(
        market: ParsedMarket,
        marketId: NormalizedMarket,
        rangeQuery: RangeQuery.TimeRangeQuery,
        initialState: PageToken.TimePageToken?,
        request: GetTimeseriesMarketOrderbooksRequest,
        marketPatternRequested: Boolean,
        enforcedDepth: Int?,
        dbDepth: Int,
        bufferSize: Int,
        downSamplingConfig: TimeUtils.StatefulDownSamplerConfig?,
    ): SuspendableStream<MarketOrderbookWrapper, PageToken.TimePageToken> {
        val timeFilter = DataUtils.createTimeFilter(rangeQuery)
        val additionalFilter = { it: MarketOrderbookWrapper ->
            timeFilter.invoke(it.time)
        }

        val (tableName, marketFilter) =
            if (useNewBooksTables) {
                getTableNameAndMarketFilter(marketId)
            } else {
                getTableNameAndMarketFilterLegacy(marketId)
            } ?: return SuspendableStream.empty()

        val timeFieldName = "book_time"

        val queryTextBuilder: QueryTextBuilder =
            { filter, limit ->
                val ordering = if (request.pagingFrom == PagingFrom.START) "ASC" else "DESC"
                // The amount of book asks and book bids in db can be different from what user specified.
                // In this case we take only the amount user would like to see.
                // We take first elements from arrays because that is what corresponds to the top of order book (the highest bid and lowest ask):
                // https://guides.cryptowat.ch/trader-education/order-books-and-market-depth-charts-explained#the-top-of-the-order-book.
                val (bidsQuery, asksQuery) =
                    if (enforcedDepth != null) {
                        "book_bids[1:$enforcedDepth]" to "book_asks[1:$enforcedDepth]"
                    } else {
                        "book_bids" to "book_asks"
                    }

                /**
                 * This is a temporary filter to work around the invalid bids and asks with empty amounts and prices for Deribit options books
                 * https://coinmetrics.atlassian.net/browse/MD-3306
                 */
                val emptyAmountPriceFilter =
                    if (marketId.exchange == 37 && marketId is DerivativesNormalizedMarket && marketId.type == OPTION) {
                        """AND book_asks != '{"(,,1)"}' AND book_bids != '{"(,,1)"}'"""
                    } else {
                        ""
                    }

                """
                    SELECT
                        $timeFieldName,
                        $asksQuery,
                        $bidsQuery,
                        book_database_time,
                        book_exchange_sequence_id
                    FROM ${booksDb.config.schema}.$tableName
                    WHERE
                        $marketFilter AND
                        book_exchange_id=${marketId.exchange} AND
                        book_depth_limit=$dbDepth
                        $filter
                        $emptyAmountPriceFilter
                    ORDER BY $timeFieldName $ordering
                    LIMIT $limit
                """
            }

        val downSamplingFilter = downSamplingConfig?.let { TimeUtils.createStatefulDownSampler(it) }

        return DataUtils
            .createStream(
                db = booksDb,
                queryTextBuilder = queryTextBuilder,
                bufferSize = bufferSize,
                keyNames = arrayOf(timeFieldName),
                dataMapper = createMapper(market.toString(), request.depthLimit == "10pct_mid_price"),
                rangeQuery = rangeQuery,
                initialState = initialState,
                stateResolver = { PageToken.TimePageToken(it.time) },
                streamId =
                    if (marketPatternRequested) {
                        market.toString()
                    } else {
                        null
                    },
            ).filter(additionalFilter)
            .let { stream ->
                if (downSamplingFilter == null) {
                    stream
                } else {
                    stream.filter { downSamplingFilter(it.time) }
                }
            }
    }

    private fun getTableNameAndMarketFilter(marketId: NormalizedMarket): Pair<String, String>? {
        val escapedMarketSymbol = if (marketId is MarketWithSymbol) SqlUtils.escapeSql(marketId.symbol) else ""
        return when (marketId) {
            is SpotNormalizedMarket -> {
                if (marketId.symbol.isEmpty()) {
                    return null
                }
                "books_spot"
            }

            is DerivativesNormalizedMarket ->
                when (marketId.type) {
                    FUTURE -> "books_future"
                    OPTION -> "books_option"
                }

            is NormalizedMarket.DefiNormalizedMarket ->
                error(
                    "Only spot, future, and option markets are supported but $marketId was requested.",
                )
        } to "book_symbol='$escapedMarketSymbol'"
    }

    private fun getTableNameAndMarketFilterLegacy(marketId: NormalizedMarket): Pair<String, String> =
        when {
            marketId is SpotNormalizedMarket -> "spot_books" to "book_base_id=${marketId.base} AND book_quote_id=${marketId.quote}"
            marketId is DerivativesNormalizedMarket && marketId.type == FUTURE -> "futures_books" to "book_symbol='${marketId.symbol}'"
            marketId is DerivativesNormalizedMarket && marketId.type == OPTION -> "option_books" to "book_symbol='${marketId.symbol}'"
            else -> error("Only spot, future, and option markets are supported but $marketId was requested.")
        }

    /**
     * @return 0 means 10pct_mid_price
     */
    fun parseAndValidateDepth(
        depth: String,
        pageSize: Int,
    ): FunctionResult<ApiError, Int> {
        if (depth.equals("full_book", ignoreCase = true)) {
            if (pageSize > 100) {
                return FunctionResult.Failure(
                    ApiError.BadParameter(
                        "page_size",
                        "Maximum value for unlimited order books is 100.",
                    ),
                )
            }
            return FunctionResult.Success(30000)
        }
        if (depth.equals("10pct_mid_price", ignoreCase = true)) {
            if (pageSize > 100) {
                return FunctionResult.Failure(
                    ApiError.BadParameter(
                        "page_size",
                        "Maximum value for 10pct_mid_price order books is 100.",
                    ),
                )
            }
            return FunctionResult.Success(0)
        }

        val depthAsInt =
            depth.toIntOrNull()?.takeIf { it in 1..30000 } ?: return FunctionResult.Failure(
                ApiError.BadParameter(
                    "depth_limit",
                    "Supported values are 1-30000 or '10pct_mid_price' or 'full_book'.",
                ),
            )

        if (pageSize > 100 && depthAsInt > 100) {
            return FunctionResult.Failure(
                ApiError.BadParameter(
                    "page_size",
                    "Maximum value for unlimited order books is 100.",
                ),
            )
        }

        return FunctionResult.Success(depthAsInt)
    }

    private fun parseBookPair(obj: Any?): Pair<BigDecimal, BigDecimal> {
        val comps = obj.toString().trim('(', ')').split(',')
        return Pair(
            // size
            comps[0].toBigDecimal(),
            // price
            comps[1].toBigDecimal(),
        )
    }

    private fun createMapper(
        marketId: String,
        calculate10PctBook: Boolean,
    ): (ResultSet) -> MarketOrderbookWrapper =
        { rs: ResultSet ->
            val time = rs.getTimestamp("book_time").toInstant()
            val timeFormatted = TimeUtils.dateTimeFormatter.format(time)
            val databaseTime = rs.getTimestamp("book_database_time").toInstant()
            val databaseTimeFormatted = TimeUtils.dateTimeFormatter.format(databaseTime)
            val exchangeSequenceId = rs.getBigDecimal("book_exchange_sequence_id")

            var asks = (rs.getArray("book_asks").array as Array<*>).map { parseBookPair(it) }
            var bids = (rs.getArray("book_bids").array as Array<*>).map { parseBookPair(it) }

            if (calculate10PctBook) {
                val result = MarketEndpointUtil.cutBookTo10PctMidPrice(asks, bids)
                asks = result.first
                bids = result.second
            }

            MarketOrderbookWrapper(
                time,
                MarketOrderBook(
                    time = timeFormatted,
                    market = marketId,
                    coinMetricsId =
                        exchangeSequenceId?.let { CommonUtils.formatBigDecimal(it) }
                            ?: "${ChronoUnit.MICROS.between(Instant.EPOCH, time)}-0",
                    asks =
                        asks.map {
                            BookEntry(
                                propertySize = CommonUtils.formatBigDecimal(it.first),
                                price = CommonUtils.formatBigDecimal(it.second),
                            )
                        },
                    bids =
                        bids.map {
                            BookEntry(
                                propertySize = CommonUtils.formatBigDecimal(it.first),
                                price = CommonUtils.formatBigDecimal(it.second),
                            )
                        },
                    databaseTime = databaseTimeFormatted,
                ),
            )
        }

    // It is used to wrap autogenerated MarketOrderbook with additional parameters
    class MarketOrderbookWrapper(
        override val time: Instant,
        val orderBook: MarketOrderBook,
    ) : WithTime
}
