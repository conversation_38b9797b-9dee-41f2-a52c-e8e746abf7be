package io.coinmetrics.api.endpoints.timeseries.exchange

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.ApiError.UnsupportedParameterValue
import io.coinmetrics.api.ChunkedResponseFormat
import io.coinmetrics.api.Response
import io.coinmetrics.api.endpoints.GetTimeseriesExchangeMetricsEndpoint
import io.coinmetrics.api.endpoints.GetTimeseriesExchangeMetricsRequest
import io.coinmetrics.api.endpoints.timeseries.exchange.datasources.ExchangeBasedMetricsDataSource
import io.coinmetrics.api.endpoints.timeseries.exchange.datasources.ExchangeBasedMetricsDataSource.TimeRangeParams
import io.coinmetrics.api.endpoints.timeseries.exchange.datasources.ExchangeId
import io.coinmetrics.api.model.TimeRestriction
import io.coinmetrics.api.models.ExchangeMetricsResponse
import io.coinmetrics.api.models.PagingFrom
import io.coinmetrics.api.resources.Resources
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.catalog.metric.impl.ExchangeMetricsService
import io.coinmetrics.api.service.catalog.metric.selectUnsupportedMetrics
import io.coinmetrics.api.service.getTimeseriesRateLimitHeaders
import io.coinmetrics.api.toSuccess
import io.coinmetrics.api.utils.BatchUtils
import io.coinmetrics.api.utils.ComparablePair
import io.coinmetrics.api.utils.DataUtils
import io.coinmetrics.api.utils.MetricUtils
import io.coinmetrics.api.utils.ThrottledLogger
import io.coinmetrics.api.utils.TimeUtils
import io.coinmetrics.api.utils.Utils
import io.coinmetrics.api.utils.paging.getPageFlow
import io.coinmetrics.api.utils.streams.SuspendableStream
import io.coinmetrics.api.utils.streams.SuspendableStreamAsMergeSource
import io.coinmetrics.api.utils.streams.asMergeSource
import io.coinmetrics.api.utils.streams.operations.MergingState
import io.coinmetrics.api.utils.streams.operations.MergingStateSerializer
import io.coinmetrics.api.utils.streams.operations.mergeJoin
import java.time.Instant
import kotlin.math.max

// todo: don't allocate LinkedHashMap for each result's row, use List<Exchange<String, String>> instead

/**
 * We support metrics with the following suffixes:
 * 5m
 * 1h
 * 1d
 */
class ExchangeMetricsEndpointImpl(
    private val amsService: AmsService,
    private val exchangeMetricsDataSource: ExchangeBasedMetricsDataSource,
    private val exchangeMetricsService: ExchangeMetricsService,
) : GetTimeseriesExchangeMetricsEndpoint() {
    private val throttledLogger = ThrottledLogger(log, throttleMillis = 2000)

    override suspend fun handle(request: GetTimeseriesExchangeMetricsRequest): Response<ExchangeMetricsResponse> {
        val headers =
            amsService
                .getTimeseriesRateLimitHeaders(
                    apiKey = request.apiKey,
                    httpRequest = request.httpRequest,
                ).getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }

        val (exchanges, patternRequested) =
            exchangeMetricsService
                .parseExchanges(request.exchanges)
                .getOrElse { return Response.errorResponse(it) }
        val metricsAvailabilityMap =
            exchangeMetricsService
                .findTimeseriesMetrics(
                    apiKey = request.apiKey,
                    frequency = request.frequency,
                    entities = exchanges,
                    metrics = request.metrics.toSet(),
                    ignoreForbiddenAndUnsupportedErrors = patternRequested,
                ).getOrElse { return Response.errorResponse(it) }

        val supportedMetricsAvailabilityMapRespectingPatternRequest =
            exchangeMetricsService
                .filterSupportedMetrics(metricsAvailabilityMap, patternRequested)
                .associate { entry -> entry.key to entry.value }

        val (startTime, endTime, pagingFromStart) =
            DataUtils
                .parseAndValidateTimeParameters(
                    request.startTime,
                    request.startInclusive,
                    request.endTime,
                    request.endInclusive,
                    request.timezone,
                    request.pageSize,
                    request.pagingFrom,
                ).getOrElse { return Response.errorResponse(it) }

        val exchangeToStreamSpecificDataList =
            metricsAvailabilityMap.mapNotNull { (exchange, metricsAvailability) ->
                val unsupportedMetrics = metricsAvailability.metricToAvailability.selectUnsupportedMetrics()
                val supportedMetrics = request.metrics.toHashSet() - unsupportedMetrics
                if (supportedMetrics.isEmpty()) {
                    if (patternRequested) {
                        return@mapNotNull null
                    } else {
                        // all metrics are unsupported
                        return Response.errorResponse(
                            ApiError.BadParameter(
                                "metrics",
                                "All requested metrics aren't supported for exchange '$exchange' and frequency '${request.frequency}'.",
                            ),
                        )
                    }
                }

                val timeRangeParams =
                    request
                        .toTimeRangeParams(
                            startTime,
                            endTime,
                            pagingFromStart,
                            when (request.sort) {
                                "time" ->
                                    supportedMetricsAvailabilityMapRespectingPatternRequest[exchange]?.timeRestriction
                                        ?: error("$exchange not found in supported metrics.")
                                else -> metricsAvailability.timeRestriction
                            },
                            request.httpRequest.receivedTime,
                        )

                val exchangeId: Int =
                    Resources.getExchangeIdByName(exchange).getOrElse { errorMessage ->
                        throttledLogger.log {
                            warn(errorMessage)
                        }
                        return Response.errorResponse(UnsupportedParameterValue("exchanges", exchange))
                    }

                exchange.lowercase() to Triple(supportedMetrics, exchangeId, timeRangeParams)
            }

        val stream =
            if (request.sort == "time") {
                // sort by (time, exchange) or (height, exchange)

                // optimization for buffer size, lower limit of buffer size is 2
                // why not 1? because an additional item is needed to check availability of the next page
                // smaller buffer size is better when we don't want to transfer a lot of redundant data via network
                val bufferSizePerStream = max(1, request.pageSize / request.exchanges.size) + 1

                val mergeKeyExtractor: (LinkedHashMap<String, String?>) -> Comparable<*> =
                    { map -> ComparablePair(map["time"]!!, map["exchange"]!!) }

                val exchangeToStreamSpecificData = exchangeToStreamSpecificDataList.toMap()

                BatchUtils
                    .mergeSortStreams(
                        streamIds = exchangeToStreamSpecificData.keys.toTypedArray(),
                        pagingFromStart = request.pagingFrom == PagingFrom.START,
                        nextPageToken = request.nextPageToken,
                        mergeKeyExtractor = mergeKeyExtractor,
                        limitPerStream = request.limitPerExchange,
                        streamSupplier = { id, pageToken ->
                            val (supportedMetrics, exchangeId, timeRangeParams) = exchangeToStreamSpecificData.getValue(id)
                            handleInternal(
                                exchange = id,
                                exchangeId = exchangeId,
                                patternRequested = patternRequested,
                                supportedMetrics = supportedMetrics,
                                timeRangeParams = timeRangeParams,
                                bufferSize = bufferSizePerStream,
                                pageToken = pageToken,
                                frequency = request.frequency,
                                request = request,
                            ).toSuccess()
                        },
                    ).getOrElse {
                        return Response.errorResponse(it, headers)
                    }
            } else {
                // sort by (exchange, time)
                val (prefetch, bufferSizePerStream) =
                    Utils.getFetchProperties(
                        request.limitPerExchange,
                        metricsAvailabilityMap.keys.size,
                        request.pageSize,
                    )

                BatchUtils
                    .sortIdsAndConcatStreams(
                        streams = exchangeToStreamSpecificDataList.asSequence(),
                        pagingFromStart = request.pagingFrom == PagingFrom.START,
                        nextPageToken = request.nextPageToken,
                        initialStreamStateParser = { it },
                        numberOfStreamsToPrefetch = prefetch,
                        limitPerStream = request.limitPerExchange,
                        streamSupplier = { id, pageToken, (supportedMetrics, exchangeId, timeRangeParams) ->
                            handleInternal(
                                exchange = id,
                                exchangeId = exchangeId,
                                patternRequested = patternRequested,
                                supportedMetrics = supportedMetrics,
                                timeRangeParams = timeRangeParams,
                                bufferSize = bufferSizePerStream,
                                pageToken = pageToken,
                                frequency = request.frequency,
                                request = request,
                            )
                        },
                        httpRequestCoroutineContext = request.httpRequest.coroutineContext,
                        streamIdsAreResolvedDynamically = patternRequested,
                        logger = log,
                    ).getOrElse {
                        return Response.errorResponse(it)
                    }
            }

        val page = stream.getPageFlow(request.httpRequest, request.pageSize, request.pagingFrom == PagingFrom.START)
        return Response.chunkedResponse(
            items = page,
            headers = headers,
            format =
                if (request.format == "csv") {
                    ChunkedResponseFormat.Csv()
                } else {
                    ChunkedResponseFormat.Json(
                        allowNullValues = true,
                    )
                },
        )
    }

    private fun handleInternal(
        exchange: String,
        exchangeId: Int,
        patternRequested: Boolean,
        supportedMetrics: Set<String>,
        timeRangeParams: TimeRangeParams,
        bufferSize: Int,
        pageToken: String?,
        frequency: String,
        request: GetTimeseriesExchangeMetricsRequest,
    ): SuspendableStream<LinkedHashMap<String, String?>, SuspendableStream.State> {
        val pageTokenPerDataSource: Array<String?> =
            MergingStateSerializer
                .deserialize(pageToken, 1)
                .getOrElse { error(it) }
                .states

        val minDateFor1d =
            if (frequency == "1d") {
                exchangeMetricsService.findMinDateFor1dFrequency(exchange, supportedMetrics)
            } else {
                null
            }
        return exchangeMetricsDataSource
            .query(
                entityId = exchange,
                patternRequested = patternRequested,
                tableName = "exchange_metrics",
                valueMapping = ExchangeId(exchangeId),
                bufferSize = bufferSize,
                pageToken = pageTokenPerDataSource.firstOrNull(),
                frequency = frequency,
                metrics = supportedMetrics.toList(),
                timeRangeParams = timeRangeParams,
                // currently, only open_interest_reported_future_* metrics support 2 frequencies:
                // - 1h
                // - 1d
                // So we need to downsample data only for 1d frequency
                downSampleMetrics = frequency == "1d",
                minDateFor1d = minDateFor1d,
            ).let { stream ->
                joinMergedSources(
                    listOf(stream.asMergeSource { it.time }),
                    request,
                    exchange,
                    patternRequested,
                    supportedMetrics,
                )
            }
    }

    private fun <S : SuspendableStream.State> joinMergedSources(
        mergeSources: List<SuspendableStreamAsMergeSource<MetricUtils.MetricsWithTimeWrapper, S, Instant>>,
        request: GetTimeseriesExchangeMetricsRequest,
        exchange: String,
        patternRequested: Boolean,
        supportedMetrics: Set<String>,
    ): SuspendableStream<LinkedHashMap<String, String?>, MergingState> =
        mergeSources.mergeJoin(
            streamId = if (patternRequested) exchange else null,
            pagingFromStart = request.pagingFrom == PagingFrom.START,
        ) { values ->
            // todo optimize, don't allocate maps in the loop
            val map = LinkedHashMap<String, String?>()
            val mapMetrics = HashMap<String, String?>()
            values.forEach { value ->
                if (value != null) {
                    map["exchange"] = exchange
                    map["time"] = TimeUtils.dateTimeFormatter.format(value.time)
                    mapMetrics.putAll(value.metrics)
                }
            }
            request.metrics.forEach {
                if (supportedMetrics.contains(it)) {
                    // add nulls for absent metrics if they are supported
                    mapMetrics.putIfAbsent(it, null)
                } else if (request.format == "csv") {
                    // add empty strings for unsupported metrics if CSV is requested
                    mapMetrics.putIfAbsent(it, "")
                }
            }
            map.putAll(mapMetrics.map { it.key to it.value }.sortedBy { it.first })
            map
        }

    private fun GetTimeseriesExchangeMetricsRequest.toTimeRangeParams(
        startTime: Instant,
        endTime: Instant,
        pagingFromStart: Boolean,
        keyTimeRestriction: TimeRestriction,
        requestReceiveTime: Instant,
    ): TimeRangeParams {
        val context =
            TimeRestriction.EvaluateContext(
                now = requestReceiveTime,
                maxAvailableTime = null,
            )

        return TimeRangeParams(
            startTime,
            startInclusive,
            endTime,
            endInclusive,
            timezone,
            pageSize,
            pagingFromStart,
            keyMinTime = keyTimeRestriction.start?.evaluate(context),
            keyMaxTime = keyTimeRestriction.end?.evaluate(context),
        )
    }
}
