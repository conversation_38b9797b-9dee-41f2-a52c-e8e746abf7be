package io.coinmetrics.api.endpoints.timeseries.index.datasource

import io.coinmetrics.api.CommonConstants
import io.coinmetrics.api.models.IndexCandle
import io.coinmetrics.api.resources.IndexInfo
import io.coinmetrics.api.utils.CommonUtils.formatBigDecimal
import io.coinmetrics.api.utils.DataUtils
import io.coinmetrics.api.utils.QueryTextBuilder
import io.coinmetrics.api.utils.RangeQuery.TimeRangeQuery
import io.coinmetrics.api.utils.TimeUtils
import io.coinmetrics.api.utils.TimeUtils.NormalizedFrequencyOffset
import io.coinmetrics.api.utils.paging.PageToken
import io.coinmetrics.api.utils.streams.SuspendableStream
import io.coinmetrics.api.utils.toSqlOrdering
import io.coinmetrics.databases.Database
import java.sql.ResultSet
import java.time.Instant

class IndexCandlesDataSource(
    private val db: Database,
) {
    companion object {
        const val TIME_COLUMN_NAME = "start_time"
    }

    private val schema = db.config.schema

    fun query(
        index: IndexInfo,
        patternRequested: Boolean,
        frequency: String,
        frequencyOffset: NormalizedFrequencyOffset,
        frequencyOffsetTimezone: String,
        rangeQuery: TimeRangeQuery,
        pageToken: PageToken.TimePageToken?,
        bufferSize: Int,
    ): SuspendableStream<IndexCandleWrapper, PageToken.TimePageToken> {
        val ordering = rangeQuery.pagingFrom.toSqlOrdering()
        val queryTextBuilder: QueryTextBuilder =
            if (frequencyOffset.default) {
                createDefaultFrequencyOffsetQuery(index, frequency, ordering)
            } else {
                createCustomFrequencyOffsetQuery(index, frequencyOffset.hours, frequencyOffsetTimezone, ordering)
            }

        val indexName = index.name
        val timeFilter = DataUtils.createTimeFilter(rangeQuery)
        return DataUtils
            .createStream(
                db = db,
                queryTextBuilder = queryTextBuilder,
                bufferSize = bufferSize,
                keyNames = arrayOf(TIME_COLUMN_NAME),
                dataMapper = createMapper(indexName),
                rangeQuery = rangeQuery,
                initialState = pageToken,
                stateResolver = { PageToken.TimePageToken(it.time) },
                lbKey = indexName,
                streamId = if (patternRequested) indexName else null,
            ).filter { timeFilter.invoke(it.time) }
    }

    private fun createMapper(index: String): (ResultSet) -> IndexCandleWrapper =
        { rs: ResultSet ->
            val time = rs.getTimestamp(TIME_COLUMN_NAME).toInstant()
            val timeFormatted = TimeUtils.dateTimeFormatter.format(time)

            IndexCandleWrapper(
                time,
                IndexCandle(
                    time = timeFormatted,
                    index = index,
                    priceOpen = formatBigDecimal(rs.getBigDecimal("open_price")),
                    priceHigh = formatBigDecimal(rs.getBigDecimal("high_price")),
                    priceLow = formatBigDecimal(rs.getBigDecimal("low_price")),
                    priceClose = formatBigDecimal(rs.getBigDecimal("close_price")),
                    candleTradesCount = formatBigDecimal(rs.getBigDecimal("trades_count")),
                ),
            )
        }

    private fun createDefaultFrequencyOffsetQuery(
        index: IndexInfo,
        frequency: String,
        ordering: String,
    ): QueryTextBuilder {
        val tableName = "${CommonConstants.INDEX_CANDLES_TABLE_PREFIX}${index.id}_$frequency"
        return { filter, limit ->
            """
            SELECT
                $TIME_COLUMN_NAME,
                open_price::NUMERIC,
                close_price::NUMERIC,
                low_price::NUMERIC,
                high_price::NUMERIC,
                average_price::NUMERIC,
                trades_count::NUMERIC
            FROM $schema.$tableName
            WHERE TRUE $filter
            ORDER BY $TIME_COLUMN_NAME $ordering
            LIMIT $limit
            """
        }
    }

    private fun createCustomFrequencyOffsetQuery(
        index: IndexInfo,
        offsetHours: Int,
        offsetTimezone: String,
        ordering: String,
    ): QueryTextBuilder {
        // aggregating 1h candles to construct 1d candles with requested start_time
        val tableName = "${CommonConstants.INDEX_CANDLES_TABLE_PREFIX}${index.id}_1h"
        return { filter, limit ->
            """
            SELECT
                -- aggregating by each day shifting the start of the day by specified offset at specified timezone
                DATE_TRUNC('day', start_time AT TIME ZONE 'utc' AT TIME ZONE '$offsetTimezone' - '$offsetHours hours'::INTERVAL),
                MIN(start_time)                                                           AS $TIME_COLUMN_NAME,
                (MIN(ARRAY [EXTRACT(EPOCH FROM start_time), open_price]))[2]::NUMERIC     AS open_price,
                (MAX(ARRAY [EXTRACT(EPOCH FROM start_time), close_price]))[2]::NUMERIC    AS close_price,
                MIN(low_price)::NUMERIC                                                   AS low_price,
                MAX(high_price)::NUMERIC                                                  AS high_price,
                SUM(trades_count)::NUMERIC                                                AS trades_count
            FROM
                -- subquery is used to improve performance by limiting amount of 1h candles needed to be sorted/grouped when client specifies broad or open interval.
                (
                    SELECT start_time,
                            open_price,
                            close_price,
                            low_price,
                            high_price,
                            trades_count
                     FROM $schema.$tableName
                     WHERE TRUE $filter
                     ORDER BY start_time $ordering
                     -- for each day we need 24 1-hours candles + 24 to cover last candle in case if start of time interval != custom offset
                     LIMIT $limit * 24 + 24
                 ) candles
            GROUP BY 1
            -- make sure we return only candles that were aggregated across the whole day and ignore "partial" candles that may be aggregated on the time interval ends.
            HAVING DATE_PART('hours', MIN(start_time) AT TIME ZONE 'utc' AT TIME ZONE '$offsetTimezone') = $offsetHours
               AND DATE_PART('hours', MAX(start_time) AT TIME ZONE 'utc' AT TIME ZONE '$offsetTimezone') = ${(if (offsetHours == 0) 24 else offsetHours) - 1}
            ORDER BY 1 $ordering
            LIMIT $limit;
            """.trimIndent()
        }
    }

    class IndexCandleWrapper(
        val time: Instant,
        val candles: IndexCandle,
    )
}
