package io.coinmetrics.api.endpoints.timeseries.asset.dataproviders

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.endpoints.GetTimeseriesAssetMetricsRequest
import io.coinmetrics.api.endpoints.timeseries.asset.AssetMetricsEndpointHelper.DataSourceGroup
import io.coinmetrics.api.endpoints.timeseries.asset.AssetMetricsEndpointHelper.Frequency
import io.coinmetrics.api.endpoints.timeseries.asset.AssetMetricsEndpointImpl
import io.coinmetrics.api.endpoints.timeseries.asset.datasources.NetworkMetricsDataSource
import io.coinmetrics.api.models.PagingFrom
import io.coinmetrics.api.utils.MetricUtils
import io.coinmetrics.api.utils.TimeUtils
import io.coinmetrics.api.utils.paging.PageToken
import io.coinmetrics.api.utils.streams.SuspendableStream
import java.time.Instant

class NetworkMetricsDataProvider(
    private val networkMetricsDataSource: NetworkMetricsDataSource,
    private val targetFrequency: Frequency,
    private val targetDataSourceGroup: DataSourceGroup,
) : AssetMetricDataProvider<MetricUtils.MetricsWithTimeWrapper> {
    override fun isApplicable(
        metrics: List<String>,
        frequency: Frequency,
        dataSourceGroup: DataSourceGroup,
    ): Boolean = frequency == targetFrequency && dataSourceGroup == targetDataSourceGroup

    override suspend fun checkParameters(
        asset: String,
        metrics: List<String>,
    ): FunctionResult<ApiError, AssetMetricsEndpointImpl.AssetMetricsDataSourceSpecificData> = networkMetricsDataSource.checkParameters()

    override fun provide(
        asset: String,
        patternRequested: Boolean,
        metrics: List<String>,
        startTime: Instant,
        endTime: Instant,
        pagingFrom: PagingFrom,
        request: GetTimeseriesAssetMetricsRequest,
        pageTokenForDataSource: String?,
        frequencyOffset: TimeUtils.NormalizedFrequencyOffset,
        frequency: Frequency,
        bufferSize: Int,
        communityApiKey: String,
        dataSourceSpecificData: AssetMetricsEndpointImpl.AssetMetricsDataSourceSpecificData?,
    ): SuspendableStream<MetricUtils.MetricsWithTimeWrapper, PageToken.TimePageToken> =
        networkMetricsDataSource.query(
            asset,
            patternRequested,
            bufferSize,
            pageTokenForDataSource,
            metrics,
            startTime,
            endTime,
            pagingFrom,
            request,
        )
}
