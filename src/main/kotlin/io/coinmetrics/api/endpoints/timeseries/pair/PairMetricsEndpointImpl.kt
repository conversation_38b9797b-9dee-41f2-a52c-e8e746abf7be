package io.coinmetrics.api.endpoints.timeseries.pair

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.ChunkedResponseFormat
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.Response
import io.coinmetrics.api.badNextPageToken
import io.coinmetrics.api.endpoints.GetTimeseriesPairMetricsEndpoint
import io.coinmetrics.api.endpoints.GetTimeseriesPairMetricsRequest
import io.coinmetrics.api.endpoints.timeseries.pair.datasources.PairMetricsDataSource
import io.coinmetrics.api.models.PairMetricsResponse
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.catalog.metric.impl.PairMetricsService
import io.coinmetrics.api.service.getTimeseriesRateLimitHeaders
import io.coinmetrics.api.toFailure
import io.coinmetrics.api.toSuccess
import io.coinmetrics.api.utils.BatchUtils
import io.coinmetrics.api.utils.ComparablePair
import io.coinmetrics.api.utils.DataUtils
import io.coinmetrics.api.utils.MetricUtils
import io.coinmetrics.api.utils.RangeQuery
import io.coinmetrics.api.utils.Utils
import io.coinmetrics.api.utils.WildcardUtils
import io.coinmetrics.api.utils.paging.PageToken
import io.coinmetrics.api.utils.paging.getPageFlow
import io.coinmetrics.api.utils.streams.SuspendableStream
import io.coinmetrics.databases.Database
import kotlin.math.max

// todo: don't allocate LinkedHashMap for each result's row, use List<Pair<String, String>> instead

/**
 * We support metrics with the following suffixes:
 * 1h
 * 1d
 */
class PairMetricsEndpointImpl(
    metricsDb: Database,
    private val amsService: AmsService,
    private val pairMetricsService: PairMetricsService,
) : GetTimeseriesPairMetricsEndpoint() {
    companion object {
        private enum class Frequency(
            val literal: String,
        ) {
            ONE_HOUR("1h"),
            ONE_DAY("1d"),
            ;

            companion object {
                fun parse(frequency: String): Frequency? = entries.firstOrNull { it.literal == frequency }
            }
        }
    }

    private val pairMetricsDataSource = PairMetricsDataSource(metricsDb)

    override suspend fun handle(request: GetTimeseriesPairMetricsRequest): Response<PairMetricsResponse> {
        val headers =
            amsService
                .getTimeseriesRateLimitHeaders(
                    apiKey = request.apiKey,
                    httpRequest = request.httpRequest,
                ).getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }

        val (parseInfo, patternRequested) =
            WildcardUtils
                .parseRequestCompositeParameter(
                    paramName = "pairs",
                    paramValues = request.pairs.toHashSet(),
                    universeOfItems = pairMetricsService.supportedPairs(),
                ).getOrElse { return Response.errorResponse(it) }

        val metricsAvailabilityMap =
            parseInfo
                .flatMap { (assetPairs, unwrappedFromPattern) ->
                    val metricsAvailability =
                        pairMetricsService
                            .findTimeseriesMetrics(
                                apiKey = request.apiKey,
                                frequency = request.frequency,
                                entities = assetPairs.toSet(),
                                metrics = request.metrics.toSet(),
                                ignoreForbiddenAndUnsupportedErrors = patternRequested,
                            ).getOrElse { return Response.errorResponse(it) }

                    pairMetricsService.filterSupportedMetrics(metricsAvailability, unwrappedFromPattern)
                }.associate { entry -> entry.key to entry.value }

        val pairs = metricsAvailabilityMap.keys
        val frequency =
            Frequency.parse(request.frequency)
                // should not be thrown because frequency is checked by AMS before
                ?: return Response.errorResponse(ApiError.BadParameter("frequency"))
        val supportedMetrics =
            metricsAvailabilityMap.mapValues { (pair, metricsAvailability) ->
                MetricUtils
                    .validateMetricsAvailability(
                        pair,
                        "pair",
                        request.frequency,
                        request.metrics.toHashSet(),
                        metricsAvailability.metricToAvailability,
                    ).getOrElse {
                        return Response.errorResponse(it)
                    }
            }

        val (startTime, endTime) =
            DataUtils
                .parseTimeParameters(
                    request.startTime,
                    request.startInclusive,
                    request.endTime,
                    request.endInclusive,
                    request.timezone,
                ).getOrElse { return Response.errorResponse(it) }

        val rangeQuery =
            RangeQuery.TimeRangeQuery(
                startTime,
                request.startInclusive,
                endTime,
                request.endInclusive,
                request.pagingFrom,
            )

        val stream =
            if (request.sort == "time") {
                // sort by (time, pair) or (height, pair)

                // optimization for buffer size, lower limit of buffer size is 2
                // why not 1? because an additional item is needed to check availability of the next page
                // smaller buffer size is better when we don't want to transfer a lot of redundant data via network
                val bufferSizePerStream = max(1, request.pageSize / request.pairs.size) + 1

                val mergeKeyExtractor: (LinkedHashMap<String, String?>) -> Comparable<*> =
                    if (request.sort == "time" || request.sort == "pair") {
                        { map -> ComparablePair(map["time"]!!, map["pair"]!!) }
                    } else {
                        { map -> ComparablePair(map["height"]!!.toInt(), map["pair"]!!) }
                    }
                when (
                    val result =
                        BatchUtils.mergeSortStreams(
                            streamIds = pairs.map { it.lowercase() }.toTypedArray(),
                            pagingFrom = request.pagingFrom,
                            nextPageToken = request.nextPageToken,
                            mergeKeyExtractor = mergeKeyExtractor,
                            limitPerStream = request.limitPerPair,
                            streamSupplier = { id, pageToken ->
                                val initialState =
                                    pageToken
                                        ?.let {
                                            try {
                                                return@let FunctionResult.Success(PageToken.TimePageToken.parse(it))
                                            } catch (e: Exception) {
                                                log.error("Can't parse provided next_page_token '{}'.", it, e)
                                                return@let FunctionResult.Failure(badNextPageToken())
                                            }
                                        }?.getOrElse { return@mergeSortStreams it.toFailure() }
                                handleInternal(
                                    pair = id,
                                    patternRequested = patternRequested,
                                    supportedMetrics = supportedMetrics[id] ?: error("$id not found in supported metrics."),
                                    bufferSize = bufferSizePerStream,
                                    initialState = initialState,
                                    frequency = frequency,
                                    request = request,
                                    rangeQuery = rangeQuery,
                                ).toSuccess()
                            },
                        )
                ) {
                    is FunctionResult.Success -> result.value
                    is FunctionResult.Failure -> return Response.errorResponse(result.value, headers)
                }
            } else {
                // sort by (pair, time)
                val (prefetch, bufferSizePerStream) = Utils.getFetchProperties(request.limitPerPair, pairs.size, request.pageSize)

                when (
                    val result =
                        BatchUtils.sortIdsAndConcatStreams(
                            streams = supportedMetrics.asSequence().map { it.toPair() },
                            pagingFrom = request.pagingFrom,
                            nextPageToken = request.nextPageToken,
                            initialStreamStateParser = { PageToken.TimePageToken.parse(it) },
                            numberOfStreamsToPrefetch = prefetch,
                            limitPerStream = request.limitPerPair,
                            streamSupplier = { id, pageToken, supportedMetrics ->
                                handleInternal(
                                    pair = id,
                                    patternRequested = patternRequested,
                                    supportedMetrics = supportedMetrics,
                                    bufferSize = bufferSizePerStream,
                                    initialState = pageToken,
                                    frequency = frequency,
                                    request = request,
                                    rangeQuery = rangeQuery,
                                )
                            },
                            httpRequestCoroutineContext = request.httpRequest.coroutineContext,
                            streamIdsAreResolvedDynamically = patternRequested,
                            logger = log,
                        )
                ) {
                    is FunctionResult.Success -> result.value
                    is FunctionResult.Failure -> return Response.errorResponse(result.value)
                }
            }

        val page = stream.getPageFlow(request.httpRequest, request.pageSize, request.pagingFrom)
        return Response.chunkedResponse(
            items = page,
            headers = headers,
            format = if (request.format == "csv") ChunkedResponseFormat.Csv() else ChunkedResponseFormat.Json(allowNullValues = true),
        )
    }

    private fun handleInternal(
        pair: String,
        patternRequested: Boolean,
        supportedMetrics: Set<String>,
        bufferSize: Int,
        initialState: PageToken.TimePageToken?,
        frequency: Frequency,
        request: GetTimeseriesPairMetricsRequest,
        rangeQuery: RangeQuery.TimeRangeQuery,
    ): SuspendableStream<LinkedHashMap<String, String?>, PageToken.TimePageToken> {
        val stream =
            pairMetricsDataSource.query(
                pair,
                patternRequested,
                bufferSize,
                initialState,
                frequency.literal,
                supportedMetrics,
                request,
                rangeQuery.withPageToken(initialState),
            )

        return MetricUtils.transformMetricsStream(
            metricsStream = stream,
            requestedMetrics = request.metrics,
            requestedFormat = request.format,
            entity = pair,
            entityName = "pair",
            supportedMetrics = supportedMetrics,
        )
    }
}
