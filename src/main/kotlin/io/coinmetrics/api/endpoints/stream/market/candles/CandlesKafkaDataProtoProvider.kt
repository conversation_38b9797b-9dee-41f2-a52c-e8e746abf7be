package io.coinmetrics.api.endpoints.stream.market.candles

import io.coinmetrics.api.endpoints.stream.AbstractSourceSwitchingKafkaDataProvider
import io.coinmetrics.api.model.DerivativesMarketType
import io.coinmetrics.api.model.ParsedMarket
import io.coinmetrics.api.models.MarketCandle
import io.coinmetrics.api.modules.main.MainApiConfig
import io.coinmetrics.api.modules.main.MainApiMonitoring
import io.coinmetrics.api.resources.Resources
import io.coinmetrics.api.utils.KafkaDataSource
import io.coinmetrics.api.utils.TimeUtils
import io.coinmetrics.proto.MarketDataFeed
import io.coinmetrics.proto.MarketDataFeed.MarketCandle.MarketTypes
import io.coinmetrics.queues.MessageAndOffset
import io.coinmetrics.queues.QueueConsumer
import org.slf4j.LoggerFactory
import java.time.Clock
import java.time.Instant

class CandlesKafkaDataProtoProvider(
    clock: Clock,
    config: MainApiConfig.KafkaSourceConfig,
    allowedTimeSpentFromTheLastSwitchMs: Long,
    monitoring: MainApiMonitoring,
    private val clientConnections: CandlesClientConnections,
) : AbstractSourceSwitchingKafkaDataProvider<Pair<MarketDataFeed.MarketCandle, ParsedMarket>>(
        clock = clock,
        config = config,
        allowedTimeSpentFromTheLastSwitchMs = allowedTimeSpentFromTheLastSwitchMs,
        ensureRecentDataInFirstSource = true,
        kafkaMonitoring = monitoring.candlesKafkaMonitoring,
    ) {
    companion object {
        private val log = LoggerFactory.getLogger(CandlesKafkaDataProtoProvider::class.java)
    }

    private val latestMarketPerTimestampMap = HashMap<Pair<String, Int>, Long>()

    private val latestMarketTypePerTimestampList =
        dataSources.map { listOf(MarketTypes.SPOT, MarketTypes.FUTURE).associate { it.name to 0L }.toMutableMap() }

    init {
        // We should check for switches between main and non-main data sources more frequently than timeouts to enable earlier switching to the fresher data source.
        check((config.nonMainDataSourceForcedLagMs - config.mainDataSourceSilentLimitMs) / allowedTimeSpentFromTheLastSwitchMs >= 2)
    }

    override suspend fun handle(entry: Pair<MarketDataFeed.MarketCandle, ParsedMarket>) {
        val (inputCandle, parsedMarket) = entry

        val market = parsedMarket.toString()
        val marketKey = market to inputCandle.intervalMinutes
        val latestMarketTimestamp = latestMarketPerTimestampMap[marketKey]
        if (latestMarketTimestamp == null || inputCandle.startTime > latestMarketTimestamp) {
            latestMarketPerTimestampMap[marketKey] = inputCandle.startTime
            val marketCandle = inputCandle.toMarketCandle(market)
            clientConnections.onCandle(inputCandle.intervalMinutes, marketCandle)
        }
    }

    private fun MarketDataFeed.MarketCandle.toMarketCandle(parsedMarket: String): MarketCandle {
        val timeFormatted = TimeUtils.dateTimeFormatter.format(Instant.ofEpochMilli(this.startTime))
        return MarketCandle(
            market = parsedMarket,
            time = timeFormatted,
            priceOpen = this.openPrice,
            priceClose = this.closePrice,
            priceHigh = this.highPrice,
            priceLow = this.lowPrice,
            vwap = this.vwap,
            volume = this.volume,
            candleUsdVolume = this.usdVolume,
            candleTradesCount = this.tradesCount.toString(),
        )
    }

    override suspend fun getTimestampForSwitch(
        dataSource: KafkaDataSource<Pair<MarketDataFeed.MarketCandle, ParsedMarket>>,
        message: MessageAndOffset<Pair<MarketDataFeed.MarketCandle, ParsedMarket>>,
    ): Long? {
        val (inputCandle, _) = message.message

        val latestMarketTypePerTimestampMap = latestMarketTypePerTimestampList[dataSource.index]
        if (inputCandle.time < latestMarketTypePerTimestampMap[inputCandle.marketType.name]!!) {
            return null
        }

        latestMarketTypePerTimestampMap[inputCandle.marketType.name] = inputCandle.time

        // Future markets catch up faster during real-time candles restart.
        return latestMarketTypePerTimestampMap.values.filter { it > 0L }.min()
    }

    private fun MarketDataFeed.MarketCandle.toParsedMarket(): ParsedMarket? {
        val market = this.market
        val exchange =
            Resources
                .getExchangeNameById(market.exchangeId)
                .onFailure {
                    if (market.exchangeId != -1) {
                        log.warn(it)
                    }
                }.getOrNull() ?: return null
        return if (this.marketType == MarketTypes.SPOT && market.baseId >= 0 && market.quoteId >= 0) {
            val base = Resources.getCurrencyTickerById(market.baseId) ?: return null
            val quote = Resources.getCurrencyTickerById(market.quoteId) ?: return null
            ParsedMarket.ParsedSpotMarket(exchange, base, quote)
        } else if (this.marketType == MarketTypes.FUTURE && market.symbol != null && market.symbol.isNotBlank()) {
            ParsedMarket.ParsedDerivativesMarket(exchange, market.symbol, type = DerivativesMarketType.FUTURE)
        } else {
            null
        }
    }

    override suspend fun consumeNext(
        consumer: QueueConsumer<Pair<MarketDataFeed.MarketCandle, ParsedMarket>>,
    ): MessageAndOffset<Pair<MarketDataFeed.MarketCandle, ParsedMarket>>? {
        val message: MessageAndOffset<ByteArray> = consumer.takeBytes()

        val inputCandle: MarketDataFeed.MarketCandle = MarketDataFeed.MarketCandle.parseFrom(message.message)

        val parsedMarket = inputCandle.toParsedMarket() ?: return null

        return message.map { inputCandle to parsedMarket }
    }
}
