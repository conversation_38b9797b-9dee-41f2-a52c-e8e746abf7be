package io.coinmetrics.api.endpoints.stream.market.candles

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.Response
import io.coinmetrics.api.StreamingConfig
import io.coinmetrics.api.endpoints.GetTimeseriesStreamMarketCandlesEndpoint
import io.coinmetrics.api.endpoints.GetTimeseriesStreamMarketCandlesRequest
import io.coinmetrics.api.models.StreamingMarketCandle
import io.coinmetrics.api.monitoring.StreamingMonitoring
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.MarketResolvingService
import io.coinmetrics.api.service.StreamConnectionCountLimitService
import io.coinmetrics.api.service.getWebsocketsRateLimitHeaders
import io.coinmetrics.httpserver.UpgradeToWebSocketResponse
import java.util.concurrent.TimeUnit

class CandlesWsEndpointImpl(
    private val config: StreamingConfig,
    private val streamConnectionCountLimitService: StreamConnectionCountLimitService,
    private val amsService: AmsService,
    private val monitoring: StreamingMonitoring,
    private val clientConnections: CandlesClientConnections,
    private val marketResolvingService: MarketResolvingService,
) : GetTimeseriesStreamMarketCandlesEndpoint() {
    private val candleFrequencyNamePerSecondMap =
        mapOf(
            "1m" to 1,
            "5m" to 5,
            "10m" to 10,
            "15m" to 15,
            "30m" to 30,
            "1h" to TimeUnit.HOURS.toMinutes(1),
            "4h" to TimeUnit.HOURS.toMinutes(4),
            "1d" to TimeUnit.DAYS.toMinutes(1),
        )

    private val frequencySupportedValuesStr = candleFrequencyNamePerSecondMap.keys.joinToString(separator = ", ") { "'$it'" }

    override suspend fun handle(request: GetTimeseriesStreamMarketCandlesRequest): Response<StreamingMarketCandle> {
        val headers =
            amsService
                .getWebsocketsRateLimitHeaders(
                    apiKey = request.apiKey,
                    httpRequest = request.httpRequest,
                ).getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }

        if (!streamConnectionCountLimitService.isNewConnectionAllowed(request.apiKey)) {
            return Response.errorResponse(ApiError.TooManyStreamConnections, headers)
        }

        val (markets, patternRequested, statisticsHash) =
            marketResolvingService.unwrapMarkets(request.markets).getOrElse {
                return Response.errorResponse(it)
            }

        val (marketsConstraints, _) =
            marketResolvingService
                .parseAndCheckMarketsForStreaming(request.apiKey, markets, patternRequested)
                .getOrElse { return Response.errorResponse(it, headers) }

        val frequencyIntervalMinutes =
            candleFrequencyNamePerSecondMap[request.frequency] ?: return Response.errorResponse(
                ApiError.BadParameter(
                    "frequency",
                    "Unsupported candle frequency. Supported values are $frequencySupportedValuesStr.",
                ),
            )

        return Response.rawHttpResponse(
            UpgradeToWebSocketResponse { webSocket ->
                val connection =
                    CandleClientConnection(
                        config = config,
                        httpRequest = request.httpRequest,
                        webSocket = webSocket,
                        monitoring = monitoring,
                        intervalMinutes = frequencyIntervalMinutes.toInt(),
                        interestedMarkets = marketsConstraints.keys,
                        patternMarkets = request.markets.filter { it.contains("*") }.toHashSet(),
                        statisticsHash = statisticsHash,
                    )
                clientConnections.addClientConnectionAsync(connection, request.backfill == "latest")
            },
        )
    }
}
