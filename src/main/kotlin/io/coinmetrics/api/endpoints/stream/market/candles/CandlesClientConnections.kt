package io.coinmetrics.api.endpoints.stream.market.candles

import com.google.common.util.concurrent.ThreadFactoryBuilder
import io.coinmetrics.api.endpoints.stream.ClientConnections
import io.coinmetrics.api.endpoints.stream.market.MarketClientConnectionService
import io.coinmetrics.api.models.MarketCandle
import io.coinmetrics.api.monitoring.StreamingMonitoring
import io.coinmetrics.api.service.StreamConnectionCountLimitService
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.ExecutorCoroutineDispatcher
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.cancelAndJoin
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.job
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.coroutines.yield
import org.slf4j.LoggerFactory
import java.util.concurrent.Executors

class CandlesClientConnections(
    monitoring: StreamingMonitoring,
    streamConnectionCountLimitService: StreamConnectionCountLimitService,
    private val candlesHistory: CandlesHistory,
    private val marketClientConnectionService: MarketClientConnectionService,
    private val statisticsUpdateIntervalMs: Long,
) {
    private val log = LoggerFactory.getLogger(CandlesClientConnections::class.java)

    private val inboundCandlesProcessingDispatcher: ExecutorCoroutineDispatcher =
        Executors
            .newSingleThreadExecutor(ThreadFactoryBuilder().setNameFormat("in-candles-dispatcher").build())
            .asCoroutineDispatcher()

    private val connections =
        ClientConnections<CandleClientConnection>(
            streamConnectionCountLimitService,
            inboundCandlesProcessingDispatcher,
            monitoring,
            endpointName = "candles",
        )

    private val statisticsDispatcher =
        Executors.newSingleThreadExecutor { Thread(it, "streaming-candles-wildcard-refresh") }.asCoroutineDispatcher()

    private val scope = CoroutineScope(statisticsDispatcher)

    fun start() {
        scope.launch(statisticsDispatcher) {
            while (isActive) {
                runCatching {
                    marketClientConnectionService.handleStatisticsUpdate(
                        dataType = "candles",
                        connections = connections.connections,
                    )
                }.onFailure { e ->
                    if (e is CancellationException) {
                        throw e
                    }
                    log.error("Failed to handle statistics update", e)
                }
                delay(statisticsUpdateIntervalMs)
            }
        }
    }

    fun addClientConnectionAsync(
        clientConnection: CandleClientConnection,
        sendLatest: Boolean,
    ) {
        connections.addAsync(clientConnection) {
            if (sendLatest) {
                candlesHistory
                    .get(clientConnection.intervalMinutes, clientConnection.interestedMarkets)
                    .forEach { candle ->
                        clientConnection.serializeAndSend(candle)
                        yield()
                    }
            }
        }
    }

    suspend fun onCandle(
        intervalMinutes: Int,
        candle: MarketCandle,
    ) {
        withContext(inboundCandlesProcessingDispatcher) {
            candlesHistory.add(intervalMinutes, candle)
            connections.forEach { connection ->
                if (candle.market in connection.interestedMarkets && connection.intervalMinutes == intervalMinutes) {
                    connection.serializeAndSend(candle)
                }
            }
        }
    }

    suspend fun close() {
        scope.coroutineContext.job.cancelAndJoin()
        statisticsDispatcher.close()

        inboundCandlesProcessingDispatcher.close()
        connections.close()
    }
}
