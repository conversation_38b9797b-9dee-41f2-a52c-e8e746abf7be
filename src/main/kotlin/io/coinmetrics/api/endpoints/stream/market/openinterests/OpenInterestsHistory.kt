package io.coinmetrics.api.endpoints.stream.market.openinterests

import io.coinmetrics.api.models.StreamingMarketOpenInterest
import io.coinmetrics.api.modules.main.MainApiMonitoring

/**
 * Accessed only from inboundProcessingDispatcher thread.
 */
class OpenInterestsHistory(
    private val monitoring: MainApiMonitoring,
) {
    private val latest = HashMap<String, StreamingMarketOpenInterest>()

    fun add(openInterest: StreamingMarketOpenInterest) {
        // we expect that scrapers can send "out of order" open interests, so we save only the newest ones
        latest.compute(openInterest.market) { _, oldOpenInterest ->
            // we expect that our timestamps can be compared lexicographically
            if (oldOpenInterest == null || openInterest.time > oldOpenInterest.time) {
                openInterest
            } else {
                oldOpenInterest
            }
        }
        monitoring.commonMonitoring.historyEntries
            .labelValues("market-openinterest")
            .set(latest.size.toDouble())
    }

    fun get(market: String): StreamingMarketOpenInterest? = latest[market]
}
