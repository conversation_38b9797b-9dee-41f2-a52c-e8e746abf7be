package io.coinmetrics.api.endpoints.stream.market.trades.handler

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.endpoints.stream.market.trades.model.DefiExporterMarketTrade
import io.coinmetrics.api.models.StreamingMarketTrade
import io.coinmetrics.api.service.MarketResolvingService
import io.coinmetrics.api.utils.CommonUtils
import io.coinmetrics.api.utils.TimeUtils
import io.coinmetrics.atlas.v2.codec.Codec

class DefiMarketTradeHandler(
    private val objectMapper: ObjectMapper,
    private val marketResolvingService: MarketResolvingService,
) : MarketTradeHandler {
    override fun deserialize(
        exchangeId: Int,
        payload: ByteArray,
    ): RawMarketTrade? {
        val marketTrade = objectMapper.readValue<DefiExporterMarketTrade>(payload)
        val market = marketTrade.getMarket(exchangeId).getOrNull() ?: return null
        val exchangeTime = TimeUtils.toMicros(marketTrade.tx.block.timestamp)
        return DefiRawMarketTrade(market, exchangeTime, marketTrade)
    }

    private fun DefiExporterMarketTrade.getMarket(exchangeId: Int): FunctionResult<String, String> =
        marketResolvingService.resolveDefiMarket(
            exchangeId = exchangeId,
            poolId = this.poolConfigId,
            baseId = this.baseId,
            quoteId = this.quoteId,
        )
}

private class DefiRawMarketTrade(
    override val market: String,
    override val exchangeTime: Long,
    private val marketTrade: DefiExporterMarketTrade,
) : RawMarketTrade {
    override val id: String
        get() = marketTrade.cmTradeId

    override fun toStreamingMarketTrade(): StreamingMarketTrade {
        val timestampStr = TimeUtils.dateTimeFormatter.format(TimeUtils.fromMicros(exchangeTime))
        return StreamingMarketTrade(
            market = market,
            time = timestampStr,
            coinMetricsId = id,
            amount = CommonUtils.formatBigDecimal(marketTrade.amount),
            price = CommonUtils.formatBigDecimal(marketTrade.price),
            collectTime = timestampStr,
            side = if (marketTrade.buy) "buy" else "sell",
            blockHash =
                marketTrade.tx.block.hash
                    .toEthHex(),
            blockHeight =
                marketTrade.tx.block.height
                    .toString(),
            txid = marketTrade.tx.hash.toEthHex(),
            initiator = marketTrade.initiator.toEthHex(),
            sender = marketTrade.sender.toEthHex(),
            beneficiary = marketTrade.beneficiary.toEthHex(),
            // will be overwritten later
            cmSequenceId = "0",
        )
    }

    private fun ByteArray.toEthHex() = Codec.encodeHash("eth", this)
}
