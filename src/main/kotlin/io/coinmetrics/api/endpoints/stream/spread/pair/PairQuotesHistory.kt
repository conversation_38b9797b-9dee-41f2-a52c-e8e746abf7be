package io.coinmetrics.api.endpoints.stream.spread.pair

import io.coinmetrics.api.models.StreamingAggregatedSpreadQuote
import io.coinmetrics.api.modules.main.MainApiMonitoring

/**
 * Accessed only from inboundProcessingDispatcher thread.
 */
class PairQuotesHistory(
    private val monitoring: MainApiMonitoring,
) {
    private val latest = HashMap<String, StreamingAggregatedSpreadQuote>()

    fun add(quote: StreamingAggregatedSpreadQuote) {
        // we expect that scrapers can send "out of order" quotes, so we save only the newest ones
        latest.compute(quote.pair) { _, oldQuote ->
            // we expect that our timestamps can be compared lexicographically
            if (oldQuote == null || quote.time > oldQuote.time) {
                quote
            } else {
                oldQuote
            }
        }
        monitoring.commonMonitoring.historyEntries
            .labelValues("pair-quotes")
            .set(latest.size.toDouble())
    }

    fun get(pair: String): StreamingAggregatedSpreadQuote? = latest[pair]
}
