package io.coinmetrics.api.endpoints.stream.asset

import io.coinmetrics.api.StreamingConfig
import io.coinmetrics.api.endpoints.stream.ClientConnection
import io.coinmetrics.api.monitoring.StreamingMonitoring
import io.coinmetrics.httpserver.HttpRequest
import io.coinmetrics.httpserver.ServerWebSocket

class AssetMetricsClientConnection(
    config: StreamingConfig,
    httpRequest: HttpRequest,
    webSocket: ServerWebSocket,
    monitoring: StreamingMonitoring,
    val interestedAssets: Set<String>,
    val interestedMetrics: Set<String>,
    val interestedFrequency: String,
) : ClientConnection(config, httpRequest, webSocket, monitoring)
