package io.coinmetrics.api.endpoints.stream.spread.pair

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.Response
import io.coinmetrics.api.StreamingConfig
import io.coinmetrics.api.endpoints.GetTimeseriesStreamPairQuotesEndpoint
import io.coinmetrics.api.endpoints.GetTimeseriesStreamPairQuotesRequest
import io.coinmetrics.api.endpoints.stream.spread.AggregationMethod
import io.coinmetrics.api.models.StreamingAggregatedSpreadQuote
import io.coinmetrics.api.monitoring.StreamingMonitoring
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.StreamConnectionCountLimitService
import io.coinmetrics.api.service.catalog.metric.impl.PairMetricsService
import io.coinmetrics.api.service.getWebsocketsRateLimitHeaders
import io.coinmetrics.httpserver.UpgradeToWebSocketResponse

class PairQuotesWsEndpoint(
    private val config: StreamingConfig,
    private val streamConnectionCountLimitService: StreamConnectionCountLimitService,
    private val clientConnections: PairQuotesClientConnections,
    private val pairMetricsService: PairMetricsService,
    private val amsService: AmsService,
    private val monitoring: StreamingMonitoring,
) : GetTimeseriesStreamPairQuotesEndpoint() {
    override suspend fun handle(request: GetTimeseriesStreamPairQuotesRequest): Response<StreamingAggregatedSpreadQuote> {
        val headers =
            amsService
                .getWebsocketsRateLimitHeaders(
                    apiKey = request.apiKey,
                    httpRequest = request.httpRequest,
                ).getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }

        if (!streamConnectionCountLimitService.isNewConnectionAllowed(request.apiKey)) {
            return Response.errorResponse(ApiError.TooManyStreamConnections, headers)
        }

        amsService.check(apiKey = request.apiKey, resource = "aggregated_spread_quotes").getOrElse { (apiError, _) ->
            return Response.errorResponse(apiError, headers)
        }

        val requestPairs =
            request.pairs
                .map { originalPair ->
                    val pair = originalPair.lowercase()
                    if (!pairMetricsService.isEntitySupported(pair)) {
                        return Response.errorResponse(ApiError.UnsupportedParameterValue("pairs", originalPair))
                    }
                    pair
                }.toSet()

        if (AggregationMethod.fromString(request.aggregationMethod) == null) {
            return Response.errorResponse(ApiError.UnsupportedParameterValue("aggregation_method", request.aggregationMethod))
        }

        return Response.rawHttpResponse(
            UpgradeToWebSocketResponse { webSocket ->
                val connection =
                    PairQuotesClientConnection(
                        config = config,
                        httpRequest = request.httpRequest,
                        webSocket = webSocket,
                        monitoring = monitoring,
                        interestedPairs = requestPairs,
                    )
                clientConnections.addClientConnectionAsync(connection, request.backfill == "latest")
            },
        )
    }
}
