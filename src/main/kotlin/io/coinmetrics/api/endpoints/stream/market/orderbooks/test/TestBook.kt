package io.coinmetrics.api.endpoints.stream.market.orderbooks.test

import io.coinmetrics.bookstreams.Book
import io.coinmetrics.bookstreams.MarketId
import io.coinmetrics.bookstreams.PriceLevel
import java.time.Instant

data class TestBook(
    override val marketId: MarketId,
    override val baseId: Int? = null,
    override val quoteId: Int? = null,
    override val receivedFromExchangeTime: Instant,
    override val exchangeProvidedId: String? = null,
    override val exchangeProvidedTime: Instant? = null,
    override val isSnapshot: Boolean,
    override val asks: List<PriceLevel>,
    override val bids: List<PriceLevel>,
) : Book {
    override fun toString(includePriceLevelContent: Boolean): String = toString()
}
