package io.coinmetrics.api.endpoints.stream.spread.asset

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.Response
import io.coinmetrics.api.StreamingConfig
import io.coinmetrics.api.endpoints.GetTimeseriesStreamAssetQuotesEndpoint
import io.coinmetrics.api.endpoints.GetTimeseriesStreamAssetQuotesRequest
import io.coinmetrics.api.endpoints.stream.spread.AggregationMethod
import io.coinmetrics.api.models.StreamingAggregatedSpreadQuote
import io.coinmetrics.api.modules.main.MainApiMonitoring
import io.coinmetrics.api.resources.Resources
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.StreamConnectionCountLimitService
import io.coinmetrics.api.service.getWebsocketsRateLimitHeaders
import io.coinmetrics.httpserver.UpgradeToWebSocketResponse

class AssetQuotesWsEndpoint(
    private val config: StreamingConfig,
    private val streamConnectionCountLimitService: StreamConnectionCountLimitService,
    private val clientConnections: AssetQuotesClientConnections,
    private val amsService: AmsService,
    private val monitoring: MainApiMonitoring,
) : GetTimeseriesStreamAssetQuotesEndpoint() {
    override suspend fun handle(request: GetTimeseriesStreamAssetQuotesRequest): Response<StreamingAggregatedSpreadQuote> {
        val headers =
            amsService
                .getWebsocketsRateLimitHeaders(
                    apiKey = request.apiKey,
                    httpRequest = request.httpRequest,
                ).getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }

        amsService.check(apiKey = request.apiKey, resource = "aggregated_spread_quotes").getOrElse { (apiError, _) ->
            return Response.errorResponse(apiError, headers)
        }

        if (!streamConnectionCountLimitService.isNewConnectionAllowed(request.apiKey)) {
            return Response.errorResponse(ApiError.TooManyStreamConnections, headers)
        }

        val requestAssets =
            request.assets
                .map { originalAsset ->
                    val asset = originalAsset.lowercase()
                    Resources.getCurrencyInfo(asset)
                        ?: return Response.errorResponse(ApiError.UnsupportedParameterValue("assets", originalAsset))
                    asset
                }.toSet()

        if (AggregationMethod.fromString(request.aggregationMethod) == null) {
            return Response.errorResponse(ApiError.UnsupportedParameterValue("aggregation_method", request.aggregationMethod))
        }

        return Response.rawHttpResponse(
            UpgradeToWebSocketResponse { webSocket ->
                val connection =
                    AssetQuotesClientConnection(
                        config = config,
                        httpRequest = request.httpRequest,
                        webSocket = webSocket,
                        monitoring = monitoring.commonMonitoring.streamingMonitoring,
                        interestedAssets = requestAssets,
                    )
                clientConnections.addClientConnectionAsync(connection, request.backfill == "latest")
            },
        )
    }
}
