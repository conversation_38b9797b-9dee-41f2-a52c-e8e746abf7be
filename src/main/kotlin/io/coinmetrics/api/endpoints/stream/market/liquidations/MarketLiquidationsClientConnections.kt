package io.coinmetrics.api.endpoints.stream.market.liquidations

import com.google.common.util.concurrent.ThreadFactoryBuilder
import io.coinmetrics.api.endpoints.stream.ClientConnections
import io.coinmetrics.api.endpoints.stream.market.MarketClientConnectionService
import io.coinmetrics.api.models.StreamingMarketLiquidation
import io.coinmetrics.api.modules.main.MainApiMonitoring
import io.coinmetrics.api.service.StreamConnectionCountLimitService
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.cancelAndJoin
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.job
import kotlinx.coroutines.launch
import kotlinx.coroutines.yield
import org.slf4j.LoggerFactory
import java.util.concurrent.Executors

class MarketLiquidationsClientConnections(
    monitoring: MainApiMonitoring,
    streamConnectionCountLimitService: StreamConnectionCountLimitService,
    private val marketClientConnectionService: MarketClientConnectionService,
    private val statisticsUpdateIntervalMs: Long,
) {
    private val log = LoggerFactory.getLogger(MarketLiquidationsClientConnections::class.java)

    val inboundProcessingDispatcher =
        Executors
            .newSingleThreadExecutor(ThreadFactoryBuilder().setNameFormat("in-liquidations-dispatcher").build())
            .asCoroutineDispatcher()

    // it is only accessed in the inboundDispatcher single thread
    private val connections =
        ClientConnections<MarketLiquidationsClientConnection>(
            streamConnectionCountLimitService,
            inboundProcessingDispatcher,
            monitoring.commonMonitoring.streamingMonitoring,
            "market-liquidations",
        )
    private val liquidationsHistory = LiquidationsHistory(monitoring)

    private val statisticsDispatcher =
        Executors.newSingleThreadExecutor { Thread(it, "streaming-liquidations-wildcard-refresh") }.asCoroutineDispatcher()

    private val scope = CoroutineScope(statisticsDispatcher)

    fun start() {
        scope.launch(statisticsDispatcher) {
            while (isActive) {
                runCatching {
                    marketClientConnectionService.handleStatisticsUpdate(
                        dataType = "liquidations",
                        connections = connections.connections,
                    )
                }.onFailure { e ->
                    if (e is CancellationException) {
                        throw e
                    }
                    log.error("Failed to handle statistics update", e)
                }
                delay(statisticsUpdateIntervalMs)
            }
        }
    }

    fun addClientConnectionAsync(
        clientConnection: MarketLiquidationsClientConnection,
        sendLatest: Boolean,
    ) {
        connections.addAsync(clientConnection) {
            if (sendLatest) {
                clientConnection.interestedMarkets.forEach { market ->
                    liquidationsHistory.get(market)?.also { liquidation ->
                        clientConnection.serializeAndSend(liquidation)
                        yield()
                    }
                }
            }
        }
    }

    /**
     * It should be called from inboundProcessingDispatcher thread.
     */
    suspend fun onLiquidations(
        marketId: String,
        liquidations: Iterable<StreamingMarketLiquidation>,
    ) {
        liquidations.forEach {
            liquidationsHistory.add(it)
        }

        connections.forEach { connection ->
            if (connection.interestedMarkets.contains(marketId)) {
                liquidations.forEach { liquidation ->
                    connection.serializeAndSend(liquidation)
                    yield()
                }
            }
        }
    }

    suspend fun close() {
        scope.coroutineContext.job.cancelAndJoin()
        statisticsDispatcher.close()

        connections.close()
        inboundProcessingDispatcher.close()
    }
}
