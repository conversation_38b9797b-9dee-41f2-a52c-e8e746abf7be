package io.coinmetrics.api.endpoints.stream.market.orderbooks

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.Response
import io.coinmetrics.api.endpoints.GetTimeseriesStreamMarketOrderbooksEndpoint
import io.coinmetrics.api.endpoints.GetTimeseriesStreamMarketOrderbooksRequest
import io.coinmetrics.api.models.StreamingMarketOrderbook
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.MarketResolvingService
import io.coinmetrics.api.service.StreamConnectionCountLimitService
import io.coinmetrics.api.service.getWebsocketsRateLimitHeaders
import io.coinmetrics.bookstreams.Depth
import io.coinmetrics.httpserver.UpgradeToWebSocketResponse

class GetTimeseriesStreamMarketOrderbooksEndpointImpl(
    private val streamConnectionCountLimitService: StreamConnectionCountLimitService,
    private val clientConnections: MarketOrderBooksClientConnections,
    private val amsService: AmsService,
    private val marketResolvingService: MarketResolvingService,
) : GetTimeseriesStreamMarketOrderbooksEndpoint() {
    override suspend fun handle(request: GetTimeseriesStreamMarketOrderbooksRequest): Response<StreamingMarketOrderbook> {
        val headers =
            amsService
                .getWebsocketsRateLimitHeaders(
                    apiKey = request.apiKey,
                    httpRequest = request.httpRequest,
                ).getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }

        if (!streamConnectionCountLimitService.isNewConnectionAllowed(request.apiKey)) {
            return Response.errorResponse(ApiError.TooManyStreamConnections, headers)
        }

        val (parsedMarkets, patternRequested, statisticsHash) =
            when (val result = marketResolvingService.unwrapMarkets(request.markets)) {
                is FunctionResult.Failure -> return Response.errorResponse(result.value)
                is FunctionResult.Success -> result.value
            }
        val (marketsConstraints, _) =
            marketResolvingService
                .parseAndCheckMarketsForStreaming(request.apiKey, parsedMarkets, patternRequested)
                .getOrElse { return Response.errorResponse(it, headers) }

        val depth =
            when (request.depthLimit) {
                "100" -> Depth.Top(100)
                "full_book" -> Depth.Full
                else -> error("INVARIANT")
            }

        return Response.rawHttpResponse(
            UpgradeToWebSocketResponse { webSocket ->
                clientConnections.addClientConnectionAsync(
                    request = request,
                    webSocket = webSocket,
                    depth = depth,
                    interestedMarkets = marketsConstraints.values,
                    backfillRequested = request.backfill == "latest",
                    statisticsHash = statisticsHash,
                )
            },
        )
    }
}
