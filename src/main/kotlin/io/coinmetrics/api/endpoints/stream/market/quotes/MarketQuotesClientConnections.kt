package io.coinmetrics.api.endpoints.stream.market.quotes

import io.coinmetrics.api.endpoints.stream.ClientConnections
import io.coinmetrics.api.endpoints.stream.market.MarketClientConnectionService
import io.coinmetrics.api.models.StreamingMarketQuote
import io.coinmetrics.api.modules.main.MainApiMonitoring
import io.coinmetrics.api.service.StreamConnectionCountLimitService
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.cancelAndJoin
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.job
import kotlinx.coroutines.launch
import kotlinx.coroutines.yield
import org.slf4j.LoggerFactory
import java.util.concurrent.Executors
import kotlin.coroutines.cancellation.CancellationException

class MarketQuotesClientConnections(
    monitoring: MainApiMonitoring,
    streamConnectionCountLimitService: StreamConnectionCountLimitService,
    private val marketClientConnectionService: MarketClientConnectionService,
    private val statisticsUpdateIntervalMs: Long,
) {
    private val log = LoggerFactory.getLogger(MarketQuotesClientConnections::class.java)

    val inboundProcessingDispatcher =
        Executors
            .newSingleThreadExecutor { Thread(it, "in-quote-dispatcher") }
            .asCoroutineDispatcher()

    private val quotesHistory = QuotesHistory(monitoring)

    // it is only accessed in the inboundDispatcher single thread
    private val connections =
        ClientConnections<MarketQuotesClientConnection>(
            streamConnectionCountLimitService,
            inboundProcessingDispatcher,
            monitoring.commonMonitoring.streamingMonitoring,
            "market-quotes",
        )

    private val statisticsDispatcher =
        Executors.newSingleThreadExecutor { Thread(it, "streaming-quotes-wildcard-refresh") }.asCoroutineDispatcher()

    private val scope = CoroutineScope(statisticsDispatcher)

    fun start() {
        scope.launch(statisticsDispatcher) {
            while (isActive) {
                runCatching {
                    marketClientConnectionService.handleStatisticsUpdate(
                        dataType = "quotes",
                        connections = connections.connections,
                    )
                }.onFailure { e ->
                    if (e is CancellationException) {
                        throw e
                    }
                    log.error("Failed to handle statistics update", e)
                }
                delay(statisticsUpdateIntervalMs)
            }
        }
    }

    fun addClientConnection(
        clientConnection: MarketQuotesClientConnection,
        sendLatest: Boolean,
    ) {
        connections.addAsync(clientConnection) {
            if (sendLatest) {
                clientConnection.interestedMarkets.forEach { market ->
                    quotesHistory.get(market)?.also { quote ->
                        clientConnection.serializeAndSend(reformatQuote(quote, clientConnection.includeOneSided))
                        yield()
                    }
                }
            }
        }
    }

    /**
     * It should be called from inboundProcessingDispatcher thread.
     */
    suspend fun onQuotes(quotes: Collection<StreamingMarketQuote>) {
        quotes.forEach { quote ->
            quotesHistory.add(quote)
        }

        connections.forEach { connection ->
            quotes.forEach { quote ->
                if (connection.interestedMarkets.contains(quote.market)) {
                    connection.serializeAndSend(reformatQuote(quote, connection.includeOneSided))
                }
            }
            yield()
        }
    }

    // Provide compatibility with old schema if requested
    private fun reformatQuote(
        quote: StreamingMarketQuote,
        includeOneSided: Boolean,
    ): StreamingMarketQuote {
        if (includeOneSided) {
            return quote
        }
        if (quote.bidPrice != null && quote.askPrice != null && quote.bidSize != null && quote.askSize == null) {
            return quote
        }
        return StreamingMarketQuote(
            market = quote.market,
            coinMetricsId = quote.coinMetricsId,
            askSize = quote.askSize ?: "0",
            askPrice = quote.askPrice ?: "0",
            bidSize = quote.bidSize ?: "0",
            bidPrice = quote.bidPrice ?: "0",
            time = quote.time,
            cmSequenceId = quote.cmSequenceId,
        )
    }

    suspend fun close() {
        scope.coroutineContext.job.cancelAndJoin()
        statisticsDispatcher.close()

        connections.close()
        inboundProcessingDispatcher.close()
    }
}
