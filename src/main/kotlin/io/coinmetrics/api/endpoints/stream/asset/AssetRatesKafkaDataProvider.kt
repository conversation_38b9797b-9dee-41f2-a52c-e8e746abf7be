package io.coinmetrics.api.endpoints.stream.asset

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.databind.ObjectMapper
import io.coinmetrics.api.endpoints.stream.AbstractKafkaBatchDataProvider
import io.coinmetrics.api.modules.main.MainApiConfig
import io.coinmetrics.api.modules.main.MainApiMonitoring
import io.coinmetrics.api.resources.Resources
import io.coinmetrics.api.utils.CommonUtils
import io.coinmetrics.api.utils.KafkaDataSource
import io.coinmetrics.api.utils.TimeUtils
import io.coinmetrics.queues.MessageAndOffset
import io.coinmetrics.queues.QueueConsumer
import kotlinx.coroutines.withContext
import java.math.BigDecimal
import java.time.Instant

class AssetRatesKafkaDataProvider(
    config: MainApiConfig.KafkaSourceConfig,
    private val monitoring: MainApiMonitoring,
    private val clientConnections: AssetMetricsClientConnections,
    private val frequency: String,
    private val objectMapper: ObjectMapper,
) : AbstractKafkaBatchDataProvider<ByteArray>(
        config = config,
        calculateAndLogTheLagThrottled = true,
        kafkaMonitoring = monitoring.ratesKafkaMonitoring,
    ) {
    private val lastSentAssetTimestamps = HashMap<Pair<Int, Int>, Long>()

    override suspend fun handleEntries(
        dataSource: KafkaDataSource<ByteArray>,
        entries: List<ByteArray>,
    ) {
        val rateMetrics: List<RateMetrics> =
            entries.mapNotNull { entryByteArray ->
                val entry: RateInput = objectMapper.readValue(entryByteArray, RateInput::class.java)
                val base: String = Resources.getCurrencyTickerById(entry.base) ?: return@mapNotNull null
                val quote: String = Resources.getCurrencyTickerById(entry.quote) ?: return@mapNotNull null

                // ignore duplicates
                val lastRateKey: Pair<Int, Int> = entry.base to entry.quote
                val lastTime = lastSentAssetTimestamps[lastRateKey]
                if (lastTime == null || entry.millisSinceEpoch > lastTime) {
                    putEntryToCacheAndCreateMetrics(entry = entry, lastRateKey = lastRateKey, base = base, quote = quote)
                } else {
                    null
                }
            }

        if (rateMetrics.isNotEmpty()) {
            calculateAndLogTheLagThrottled(
                gauge = monitoring.wsRatesE2ePipelineLag,
                dataSource = dataSource,
                lastMessageTimeMs = rateMetrics.last().millisSinceEpoch,
            )
        }

        @Suppress("UNCHECKED_CAST")
        withContext(clientConnections.inboundProcessingDispatcher) {
            rateMetrics.forEach { rateMetric ->
                clientConnections.onMetrics(
                    asset = rateMetric.base,
                    headers = rateMetric.headers,
                    metrics = rateMetric.metrics,
                    frequency = frequency,
                    gropingKeyValue = rateMetric.millisSinceEpoch as Comparable<Any>,
                )
            }
        }
    }

    private fun putEntryToCacheAndCreateMetrics(
        entry: RateInput,
        lastRateKey: Pair<Int, Int>,
        base: String,
        quote: String,
    ): RateMetrics {
        lastSentAssetTimestamps[lastRateKey] = entry.millisSinceEpoch

        val time: String = TimeUtils.dateTimeFormatter.format(Instant.ofEpochMilli(entry.millisSinceEpoch))
        val metrics: List<Pair<String, String>> =
            if (quote == "usd") {
                listOf(
                    "ReferenceRate" to CommonUtils.formatBigDecimal(BigDecimal.valueOf(entry.price)),
                    "ReferenceRateUSD" to CommonUtils.formatBigDecimal(BigDecimal.valueOf(entry.price)),
                )
            } else {
                listOf(
                    "ReferenceRate${quote.uppercase()}" to CommonUtils.formatBigDecimal(BigDecimal.valueOf(entry.price)),
                )
            }

        return RateMetrics(
            base = base,
            headers =
                listOf(
                    "time" to time,
                    "asset" to base,
                ),
            metrics = metrics,
            millisSinceEpoch = entry.millisSinceEpoch,
        )
    }

    override suspend fun consumeNextBatch(consumer: QueueConsumer<ByteArray>): List<MessageAndOffset<ByteArray>> = consumer.takeBytesBatch()

    data class RateMetrics(
        val base: String,
        val headers: List<Pair<String, String>>,
        val metrics: List<Pair<String, String>>,
        val millisSinceEpoch: Long,
    )

    @JsonIgnoreProperties(ignoreUnknown = true)
    data class RateInput(
        val base: Int,
        val quote: Int,
        val millisSinceEpoch: Long,
        val price: Double,
        val createdAt: Long? = 0,
        val numTrades: Int? = 0,
    )
}
