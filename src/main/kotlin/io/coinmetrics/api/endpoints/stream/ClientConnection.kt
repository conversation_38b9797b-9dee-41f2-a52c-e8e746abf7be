package io.coinmetrics.api.endpoints.stream

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.ObjectWriter
import com.fasterxml.jackson.databind.node.ObjectNode
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import io.coinmetrics.api.StreamingConfig
import io.coinmetrics.api.conversion.StreamingIndexLevelMixin
import io.coinmetrics.api.conversion.StreamingMarketTradeMixin
import io.coinmetrics.api.conversion.VerificationMixin
import io.coinmetrics.api.effectiveApiKey
import io.coinmetrics.api.models.ErrorObject
import io.coinmetrics.api.models.IndexLevel
import io.coinmetrics.api.models.StreamingMarketTrade
import io.coinmetrics.api.models.Verification
import io.coinmetrics.api.monitoring.StreamingMonitoring
import io.coinmetrics.api.utils.mdc
import io.coinmetrics.api.utils.withMDC
import io.coinmetrics.httpserver.HttpRequest
import io.coinmetrics.httpserver.ServerWebSocket
import org.slf4j.LoggerFactory

open class ClientConnection(
    private val config: StreamingConfig,
    httpRequest: HttpRequest,
    internal val webSocket: ServerWebSocket,
    private val monitoring: StreamingMonitoring,
) {
    companion object {
        private val log = LoggerFactory.getLogger(ClientConnection::class.java)
        private val objectMapper =
            ObjectMapper()
                .registerKotlinModule()
                .addMixIn(StreamingMarketTrade::class.java, StreamingMarketTradeMixin::class.java)
                .addMixIn(IndexLevel::class.java, StreamingIndexLevelMixin::class.java)
                .addMixIn(Verification::class.java, VerificationMixin::class.java)
        private val prettyJsonWriter = objectMapper.writerWithDefaultPrettyPrinter()

        // flow control consts
        private val fallingBehindResponse =
            WarningResponse(
                ErrorObject(
                    "falling_behind",
                    """
                    Your connection is falling behind and messages are being queued for delivery to you. Your queue is now over 60% full. You will be disconnected when the queue is full.
                    """.trimIndent(),
                ),
            )

        private val stallResponse =
            ErrorResponse(
                ErrorObject(
                    "stall",
                    """
                    The client was reading too slowly and was disconnected by the server.
                    """.trimIndent(),
                ),
            )

        val jsonWriter: ObjectWriter = objectMapper.writer()
    }

    private var sequenceId = 0L

    @Volatile
    var lastActivityTimestampMs: Long = System.currentTimeMillis()
        private set

    private var queueFullWarningSent: Boolean = false

    @Volatile
    var closed: Boolean = false
        private set

    fun serializeAndSend(obj: Any) =
        mdc.withMDC {
            send { cmSequenceId, _ ->
                val node = objectMapper.valueToTree<ObjectNode>(obj)
                // it's needed to put field at the end
                node.remove("cm_sequence_id")
                node.put("cm_sequence_id", cmSequenceId)
                serialize(node)
            }
        }

    fun send(messageProvider: (cmSequenceId: String, pretty: Boolean) -> String) =
        mdc.withMDC {
            if (closed) return@withMDC

            // flow control
            val sendQueueSize = webSocket.sendQueueSize
            if (sendQueueSize >= config.maxSendQueueSizeBytes) {
                log.warn("Send queue is full, sending 'stall' error and disconnecting the client")
                monitoring.flowControlTotal.labelValues("error").inc()
                webSocket.sendTextMessageAsync(serialize(stallResponse))
                closeNow(exception = RuntimeException("Send queue is full."))
                return@withMDC
            }
            if (queueFullWarningSent) {
                if (sendQueueSize <= config.resetWarningQueueSizeBytes) {
                    log.warn("Client became faster")
                    queueFullWarningSent = false
                }
            } else if (sendQueueSize >= config.warningSendQueueSizeBytes) {
                log.warn("Send queue is 60% full, sending 'falling_behind' warning to the client")
                monitoring.flowControlTotal.labelValues("warning").inc()
                webSocket.sendTextMessageAsync(serialize(fallingBehindResponse))
                queueFullWarningSent = true
            }

            val message =
                safeCall("invoking message provider") {
                    messageProvider(sequenceId.toString(), prettyJsonRequested)
                } ?: return@withMDC
            sequenceId += 1
            webSocket.sendTextMessageAsync(message)
        }

    private inline fun <T> safeCall(
        description: String,
        block: () -> T,
    ): T? =
        try {
            block()
        } catch (e: Exception) {
            log.error("Error $description: $e", e)
            closeNow(statusCode = 1011, exception = e)
            null
        }

    private fun serialize(obj: Any): String {
        val writer =
            if (prettyJsonRequested) {
                prettyJsonWriter
            } else {
                jsonWriter
            }

        return writer.writeValueAsString(obj)
    }

    fun sendPing() =
        mdc.withMDC {
            if (closed || queueFullWarningSent) return@withMDC
            webSocket.sendPingAsync()
            markAsActive()
        }

    private fun markAsActive() {
        lastActivityTimestampMs = System.currentTimeMillis()
    }

    fun closeNow(
        statusCode: Short = 1000,
        reason: String? = null,
        exception: Throwable? = null,
    ) = mdc.withMDC {
        if (closed) {
            return@withMDC
        }
        closed = true
        webSocket.closeNow(statusCode, reason, exception)
    }

    val apiKey = httpRequest.effectiveApiKey()
    val mdc = httpRequest.mdc

    private val prettyJsonRequested = httpRequest.queryParameters["pretty"].equals("true", true)

    @Suppress("unused")
    private class WarningResponse(
        val warning: ErrorObject,
    )

    @Suppress("unused")
    private class ErrorResponse(
        val error: ErrorObject,
    )
}
