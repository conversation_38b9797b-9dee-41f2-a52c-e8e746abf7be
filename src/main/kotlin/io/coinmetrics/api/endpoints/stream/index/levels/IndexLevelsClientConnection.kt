package io.coinmetrics.api.endpoints.stream.index.levels

import io.coinmetrics.api.StreamingConfig
import io.coinmetrics.api.endpoints.stream.ClientConnection
import io.coinmetrics.api.monitoring.StreamingMonitoring
import io.coinmetrics.api.resources.IndexInfo
import io.coinmetrics.httpserver.HttpRequest
import io.coinmetrics.httpserver.ServerWebSocket

class IndexLevelsClientConnection(
    config: StreamingConfig,
    httpRequest: HttpRequest,
    webSocket: ServerWebSocket,
    monitoring: StreamingMonitoring,
    val interestedIndexes: Map<Int, List<IndexInfo>>,
    val includeVerification: Boolean,
) : ClientConnection(config, httpRequest, webSocket, monitoring)
