package io.coinmetrics.api.endpoints.stream.asset

import io.coinmetrics.api.chainstate.BlockHeader
import io.coinmetrics.api.resources.Resources
import io.coinmetrics.api.utils.CommonUtils
import io.coinmetrics.api.utils.DataUtils
import io.coinmetrics.atlas.v2.codec.Codec
import io.coinmetrics.databases.Database
import java.sql.ResultSet

class BbbNewNetworkMetricsDataProvider(
    private val db: Database,
    asset: String,
    metrics: Collection<String>,
) : AbstractBbbDataProvider(db, asset) {
    private val metricsSqlFilter = metrics.joinToString("','", "'", "'")

    override val assetColumnName = "asset_id"
    override val tableName = "\"1b_metrics\""
    override val baseMetric = "BlkHgt"
    override val schema = "public"

    override val heightColumnName = "block_height"
    override val timeColumnName = "block_time"
    override val hashColumnName = "block_hash"
    override val parentHashColumnName = "block_parent_hash"

    override val getHeaderSubquery = "AND latest_revision IS TRUE"

    override suspend fun fetchAllMetricsForBlock(block: BlockHeader): List<Pair<String, String?>> {
        val decodedHash = Codec.decodeHash(asset, block.hash)
        val blockHashSqlValue = DataUtils.convertByteArrayToSqlParam(decodedHash)
        val queryText =
            """
            SELECT metric, value
            FROM $schema.$tableName
            WHERE $assetColumnName=${convertAssetToSqlValue(asset)} 
                AND $hashColumnName=$blockHashSqlValue 
                AND block_height=${block.height} 
                AND metric in ($metricsSqlFilter) 
                AND latest_revision=TRUE
            """.trimIndent()
        return db.query(queryText) { query ->
            query.map(mapper).toList()
        }
    }

    override fun blockHeaderMapper(rs: ResultSet): BlockHeader =
        BlockHeader(
            height = rs.getInt("height"),
            time = rs.getTimestamp("time").toInstant(),
            hash = Codec.encodeHash(asset, rs.getBytes("hash")),
            parent = Codec.encodeHash(asset, rs.getBytes("parent")),
        )

    override fun convertAssetToSqlValue(asset: String): String {
        val id = Resources.getCurrencyInfo(asset)!!.id
        return id.toString()
    }

    override fun decodeBlockHashTransformation(
        asset: String,
        value: String,
    ): String {
        val decodedHash = Codec.decodeHash(asset, value)
        val convertedHash = DataUtils.convertByteArrayToSqlParamWithoutQuotes(decodedHash)
        return "'$convertedHash'"
    }

    private val mapper = { rs: ResultSet ->
        rs.getString("metric") to rs.getBigDecimal("value")?.let { CommonUtils.formatBigDecimal(it) }
    }
}
