package io.coinmetrics.api.endpoints.stream.market.liquidations

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.Response
import io.coinmetrics.api.StreamingConfig
import io.coinmetrics.api.endpoints.GetTimeseriesStreamMarketLiquidationsEndpoint
import io.coinmetrics.api.endpoints.GetTimeseriesStreamMarketLiquidationsRequest
import io.coinmetrics.api.models.StreamingMarketLiquidation
import io.coinmetrics.api.monitoring.StreamingMonitoring
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.MarketResolvingService
import io.coinmetrics.api.service.StreamConnectionCountLimitService
import io.coinmetrics.api.service.getWebsocketsRateLimitHeaders
import io.coinmetrics.httpserver.UpgradeToWebSocketResponse

class MarketLiquidationsWsEndpointImpl(
    private val config: StreamingConfig,
    private val streamConnectionCountLimitService: StreamConnectionCountLimitService,
    private val clientConnections: MarketLiquidationsClientConnections,
    private val amsService: AmsService,
    private val monitoring: StreamingMonitoring,
    private val marketResolvingService: MarketResolvingService,
) : GetTimeseriesStreamMarketLiquidationsEndpoint() {
    override suspend fun handle(request: GetTimeseriesStreamMarketLiquidationsRequest): Response<StreamingMarketLiquidation> {
        val headers =
            amsService
                .getWebsocketsRateLimitHeaders(
                    apiKey = request.apiKey,
                    httpRequest = request.httpRequest,
                ).getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }

        if (!streamConnectionCountLimitService.isNewConnectionAllowed(request.apiKey)) {
            return Response.errorResponse(ApiError.TooManyStreamConnections, headers)
        }

        val (markets, patternRequested, statisticsHash) =
            when (val result = marketResolvingService.unwrapMarkets(request.markets)) {
                is FunctionResult.Failure -> return Response.errorResponse(result.value)
                is FunctionResult.Success -> result.value
            }

        val (marketsConstraints, _) =
            marketResolvingService
                .parseAndCheckMarketsForStreaming(request.apiKey, markets, patternRequested)
                .getOrElse { return Response.errorResponse(it, headers) }

        return Response.rawHttpResponse(
            UpgradeToWebSocketResponse { webSocket ->
                val connection =
                    MarketLiquidationsClientConnection(
                        config = config,
                        httpRequest = request.httpRequest,
                        webSocket = webSocket,
                        monitoring = monitoring,
                        interestedMarkets = marketsConstraints.keys,
                        patternMarkets = request.markets.filter { it.contains("*") }.toHashSet(),
                        statisticsHash = statisticsHash,
                    )
                clientConnections.addClientConnectionAsync(connection, request.backfill == "latest")
            },
        )
    }
}
