package io.coinmetrics.api.endpoints.stream.market.trades

import io.coinmetrics.api.StreamingConfig
import io.coinmetrics.api.endpoints.stream.ClientConnection
import io.coinmetrics.api.endpoints.stream.market.MarketClientConnection
import io.coinmetrics.api.monitoring.StreamingMonitoring
import io.coinmetrics.httpserver.HttpRequest
import io.coinmetrics.httpserver.ServerWebSocket

class MarketTradesClientConnection(
    config: StreamingConfig,
    httpRequest: HttpRequest,
    webSocket: ServerWebSocket,
    monitoring: StreamingMonitoring,
    @Volatile
    override var interestedMarkets: Set<String>,
    override val patternMarkets: Set<String>,
    override var statisticsHash: Int,
) : ClientConnection(config, httpRequest, webSocket, monitoring),
    MarketClientConnection
