package io.coinmetrics.api.endpoints.stream.asset

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.Response
import io.coinmetrics.api.StreamingConfig
import io.coinmetrics.api.ams.CheckResult
import io.coinmetrics.api.endpoints.GetTimeseriesStreamAssetMetricsEndpoint
import io.coinmetrics.api.endpoints.GetTimeseriesStreamAssetMetricsRequest
import io.coinmetrics.api.models.StreamingAssetMetric
import io.coinmetrics.api.modules.common.CommonMonitoring
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.StreamConnectionCountLimitService
import io.coinmetrics.api.service.getWebsocketsRateLimitHeaders
import io.coinmetrics.api.toFailure
import io.coinmetrics.api.toSuccess
import io.coinmetrics.httpserver.UpgradeToWebSocketResponse
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.coroutineScope

class AssetMetricsWsEndpointImpl(
    private val config: StreamingConfig,
    private val streamConnectionCountLimitService: StreamConnectionCountLimitService,
    private val clientConnections: AssetMetricsClientConnections,
    private val amsService: AmsService,
    private val commonMonitoring: CommonMonitoring,
) : GetTimeseriesStreamAssetMetricsEndpoint() {
    private val amsParamsToEndpointParams =
        hashMapOf(
            "asset" to "assets",
            "metric" to "metrics",
            "frequency" to "frequency",
        )

    override suspend fun handle(request: GetTimeseriesStreamAssetMetricsRequest): Response<StreamingAssetMetric> {
        val headers =
            amsService
                .getWebsocketsRateLimitHeaders(
                    apiKey = request.apiKey,
                    httpRequest = request.httpRequest,
                ).getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }

        if (!streamConnectionCountLimitService.isNewConnectionAllowed(request.apiKey)) {
            return Response.errorResponse(ApiError.TooManyStreamConnections, headers)
        }

        val requestAssets =
            request.assets
                .asSequence()
                .map { it.lowercase() }
                .toSet()

        val (successfulDiscoverResults, discoveryErrors) =
            checkAssetMetricsSupport(requestAssets, request.metrics, request.frequency)
                .partition { (_, discoveryResult) -> discoveryResult is FunctionResult.Success }

        if (!request.ignoreUnsupportedErrors && discoveryErrors.isNotEmpty()) {
            val (_, error) = discoveryErrors.first()
            error.getOrElse { return Response.errorResponse(it, headers) }
        }

        val (availableAssets, availableMetrics) =
            successfulDiscoverResults
                .map { (assetMetricPairs, _) -> assetMetricPairs }
                .groupBy(
                    keySelector = { (asset, _) -> asset },
                    valueTransform = { (_, metric) -> metric },
                ).let {
                    it.keys to it.values.flatten().toSet()
                }

        val allowedMetrics =
            checkPermissions(request.apiKey, availableMetrics, request.ignoreForbiddenErrors)
                .getOrElse { return Response.errorResponse(it, headers) }

        return Response.rawHttpResponse(
            UpgradeToWebSocketResponse { webSocket ->
                val connection =
                    AssetMetricsClientConnection(
                        config = config,
                        httpRequest = request.httpRequest,
                        webSocket = webSocket,
                        monitoring = commonMonitoring.streamingMonitoring,
                        interestedAssets = availableAssets,
                        interestedMetrics = allowedMetrics,
                        interestedFrequency = request.frequency,
                    )
                clientConnections.addClientConnectionAsync(connection, request.backfill == "latest")
            },
        )
    }

    private suspend fun checkAssetMetricsSupport(
        assets: Collection<String>,
        metrics: Collection<String>,
        frequency: String,
    ): List<Pair<AssetMetric, FunctionResult<ApiError, Unit>>> =
        assets.flatMap { asset ->
            coroutineScope {
                metrics
                    .map { metric ->
                        async { AssetMetric(asset, metric) to checkAssetMetricSupport(asset, metric, frequency) }
                    }
            }.awaitAll()
        }

    private suspend fun checkAssetMetricSupport(
        asset: String,
        metric: String,
        frequency: String,
    ): FunctionResult<ApiError, Unit> {
        val discoveryResult =
            amsService
                .discovery(
                    resource = "asset_metrics",
                    target = "asset,metric",
                    filters =
                        hashMapOf(
                            "asset" to asset,
                            "metric" to metric,
                            "frequency" to frequency,
                        ),
                ) { amsParamsToEndpointParams[it] }
                .getOrElse { (apiError) -> return apiError.toFailure() }

        return if (discoveryResult.values.isEmpty()) {
            return ApiError
                .BadParameters("Metric '$metric' with frequency '$frequency' is not supported for '$asset'.")
                .toFailure()
        } else {
            Unit.toSuccess()
        }
    }

    private suspend fun checkPermissions(
        apiKey: String,
        metrics: Collection<String>,
        ignoreForbiddenErrors: Boolean,
    ): FunctionResult<ApiError, Set<String>> {
        val results =
            coroutineScope {
                listOf(
                    async { checkMetricsAccess(metrics, apiKey, ::isReferenceRate, "websocket_reference_rates") },
                    async { checkMetricsAccess(metrics, apiKey, ::isPrincipalPrice, "websocket_principal_price") },
                    async { checkMetricsAccess(metrics, apiKey, ::isNetworkAssetMetric, "realtime_asset_metrics") },
                ).awaitAll()
            }

        val (allowedMetrics, forbiddenMetrics) =
            results.partition { (_, accessCheckResults) ->
                accessCheckResults is FunctionResult.Success
            }

        if (!ignoreForbiddenErrors && forbiddenMetrics.isNotEmpty()) {
            val (metrics, error) = forbiddenMetrics.first()
            val metricsStr = metrics.asSequence().take(3).joinToString(", ")
            error.getOrElse {
                return ApiError
                    .ForbiddenWithMessage("Requested metrics are not available with supplied credentials: $metricsStr.")
                    .toFailure()
            }
        }

        return allowedMetrics
            .map { (metrics, _) -> metrics }
            .flatten()
            .toSet()
            .toSuccess()
    }

    private suspend fun checkMetricsAccess(
        metrics: Collection<String>,
        apiKey: String,
        predicate: (String) -> Boolean,
        resource: String,
    ): Pair<Collection<String>, FunctionResult<ApiError, Unit>> {
        val checkResult =
            if (metrics.any(predicate)) {
                checkAccess(apiKey, resource)
            } else {
                return emptyList<String>() to Unit.toSuccess()
            }
        return metrics.filter(predicate) to checkResult
    }

    private suspend fun checkAccess(
        apiKey: String,
        resource: String,
    ): FunctionResult<ApiError, Unit> =
        when (
            val result = amsService.check(apiKey = apiKey, resource = resource)
        ) {
            is FunctionResult.Failure -> result.value.first.toFailure()
            is FunctionResult.Success -> {
                val checkResult = result.value
                if (checkResult is CheckResult.Success) {
                    Unit.toSuccess()
                } else {
                    throw IllegalStateException()
                }
            }
        }

    private fun isNetworkAssetMetric(metric: String): Boolean = !isReferenceRate(metric) && !isPrincipalPrice(metric)

    private fun isReferenceRate(metric: String): Boolean = metric.startsWith("ReferenceRate")

    private fun isPrincipalPrice(metric: String): Boolean = metric.startsWith("principal_market_price")

    private data class AssetMetric(
        val asset: String,
        val metric: String,
    )
}
