package io.coinmetrics.api.endpoints.stream

import io.coinmetrics.api.monitoring.StreamingMonitoring
import io.coinmetrics.api.service.StreamConnectionCountLimitService
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.slf4j.MDCContext
import org.slf4j.LoggerFactory
import java.util.Collections
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.TimeUnit

class ClientConnections<T : ClientConnection>(
    private val streamConnectionCountLimitService: StreamConnectionCountLimitService,
    dispatcher: CoroutineDispatcher,
    private val monitoring: StreamingMonitoring,
    private val endpointName: String,
    private val onConnectionRemove: (T) -> Unit = {},
) {
    companion object {
        private val log = LoggerFactory.getLogger(ClientConnections::class.java)
        private val PING_INTERVAL_MS = TimeUnit.SECONDS.toMillis(10)
    }

    private val coroutineScope = CoroutineScope(dispatcher)
    val connections: MutableSet<T> = Collections.newSetFromMap(ConcurrentHashMap())

    init {
        coroutineScope.launch {
            while (isActive) {
                try {
                    // send PING frames to inactive connections
                    val idleTimestampMs = System.currentTimeMillis() - PING_INTERVAL_MS
                    connections.forEach {
                        if (it.lastActivityTimestampMs <= idleTimestampMs) {
                            it.sendPing()
                        }
                    }
                    delay(PING_INTERVAL_MS / 2)
                } catch (e: CancellationException) {
                    // normal shutdown
                    break
                } catch (e: Exception) {
                    log.error("Failed to send pings. Will retry in 2 sec.", e)
                    delay(2_000)
                }
            }
        }
    }

    fun close() {
        // the connections are already closed before by closing transport
        coroutineScope.cancel()
    }

    fun addAsync(
        connection: T,
        backFillHandler: suspend (T) -> Unit,
    ) {
        coroutineScope.launch(MDCContext(connection.mdc)) {
            val apiKey = connection.apiKey
            monitoring.wsActiveConnections.labelValues(endpointName, apiKey ?: "").inc()
            monitoring.wsOpenConnectionsTotal.labelValues(endpointName, apiKey ?: "").inc()
            try {
                backFillHandler.invoke(connection)
            } catch (e: CancellationException) {
                throw e
            } catch (e: Exception) {
                log.error("Unexpected exception: $e", e)
                connection.closeNow(statusCode = 1011, exception = e)
            }

            connections.add(connection)
            apiKey?.also { streamConnectionCountLimitService.trackConnection(it) }

            // Wait for connection close and then clean up.
            val closeCause = connection.webSocket.onClose.await()
            log.debug("Connection was closed. Reason: {}", closeCause.toHumanReadable())
            monitoring.wsCloseConnectionsTotal.labelValues(endpointName, apiKey ?: "").inc()
            monitoring.wsActiveConnections.labelValues(endpointName, apiKey ?: "").dec()
            try {
                onConnectionRemove(connection)
            } catch (e: Exception) {
                log.error("onConnectionRemove failed: $e", e)
            }
            apiKey?.also { streamConnectionCountLimitService.untrackConnection(it) }
            connections.remove(connection)
        }
    }

    inline fun forEach(consumer: (T) -> Unit) {
        connections.forEach(consumer)
    }
}
