package io.coinmetrics.api.endpoints.stream.market.trades.handler

import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.models.StreamingMarketTrade
import io.coinmetrics.api.service.MarketResolvingService
import io.coinmetrics.api.utils.CommonUtils
import io.coinmetrics.api.utils.TimeUtils
import io.coinmetrics.proto.MarketDataFeed
import java.math.BigDecimal

class DefaultMarketTradeHandler(
    private val marketResolvingService: MarketResolvingService,
) : MarketTradeHandler {
    override fun deserialize(
        exchangeId: Int,
        payload: ByteArray,
    ): RawMarketTrade? {
        val tradeEntry = MarketDataFeed.TradeEntry.parseFrom(payload)
        val market = tradeEntry.getMarket(exchangeId).getOrNull() ?: return null
        return DefaultRawMarketTrade(market, tradeEntry)
    }

    private fun MarketDataFeed.TradeEntry.getMarket(exchangeId: Int): FunctionResult<String, String> {
        val marketType = this.marketType?.name?.lowercase() ?: return FunctionResult.Failure("Market type was null")
        return marketResolvingService.resolveMarketFromKafkaData(
            exchangeId = exchangeId,
            baseId = this.baseId,
            quoteId = this.quoteId,
            symbol = this.symbol,
            marketType = marketType,
        )
    }
}

private class DefaultRawMarketTrade(
    override val market: String,
    private val tradeEntry: MarketDataFeed.TradeEntry,
) : RawMarketTrade {
    override val id: String
        get() = tradeEntry.id

    override val exchangeTime: Long
        get() = tradeEntry.exchangeTime

    override fun toStreamingMarketTrade(): StreamingMarketTrade {
        // side may have value "unknown" in some cases
        val side =
            tradeEntry.buy
                ?.name
                ?.lowercase()
                ?.takeIf { it == "buy" || it == "sell" }

        return StreamingMarketTrade(
            market = market,
            coinMetricsId = id,
            amount = CommonUtils.formatBigDecimal(BigDecimal(tradeEntry.amount)),
            price = CommonUtils.formatBigDecimal(BigDecimal(tradeEntry.price)),
            side = side,
            collectTime = TimeUtils.dateTimeFormatter.format(TimeUtils.fromMicros(tradeEntry.scraperReceiveTime)),
            time = TimeUtils.dateTimeFormatter.format(TimeUtils.fromMicros(exchangeTime)),
            // will be overwritten later,
            cmSequenceId = "0",
            markPrice = if (tradeEntry.markPrice.isNullOrBlank()) null else tradeEntry.markPrice,
            indexPrice = if (tradeEntry.indexPrice.isNullOrBlank()) null else tradeEntry.indexPrice,
            ivTrade = if (tradeEntry.impliedVolatility.isNullOrBlank()) null else tradeEntry.impliedVolatility,
            liquidation = if (tradeEntry.liquidation.isNullOrBlank()) null else tradeEntry.liquidation,
        )
    }
}
