package io.coinmetrics.api.endpoints.stream.asset

import io.coinmetrics.api.chainstate.BlockHeader
import io.coinmetrics.api.utils.CommonUtils
import io.coinmetrics.databases.Database
import java.sql.ResultSet

class BbbNetworkMetricsDataProvider(
    private val db: Database,
    asset: String,
    metrics: Collection<String>,
) : AbstractBbbDataProvider(db, asset) {
    private val metricsSqlFilter = metrics.joinToString("','", "'", "'")

    override val tableName = "statistics_realtime"
    override val baseMetric = "BlkHgt"

    override suspend fun fetchAllMetricsForBlock(block: BlockHeader): List<Pair<String, String?>> {
        val queryText =
            """
            SELECT metric, value
            FROM $schema.$tableName
            WHERE block_hash=${decodeBlockHashTransformation(asset, block.hash)} 
                AND $assetColumnName='$asset' 
                AND metric in ($metricsSqlFilter)
            """.trimIndent()
        return db.query(queryText) { query ->
            query.map(mapper).toList()
        }
    }

    private val mapper = { rs: ResultSet ->
        rs.getString("metric") to rs.getBigDecimal("value")?.let { CommonUtils.formatBigDecimal(it) }
    }
}
