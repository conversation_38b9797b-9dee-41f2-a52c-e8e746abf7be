package io.coinmetrics.api.endpoints.stream.market.orderbooks

import io.coinmetrics.api.StreamingConfig
import io.coinmetrics.api.endpoints.GetTimeseriesStreamMarketOrderbooksRequest
import io.coinmetrics.api.endpoints.stream.ClientConnections
import io.coinmetrics.api.endpoints.stream.market.MarketClientConnectionService
import io.coinmetrics.api.modules.streamingbooks.StreamingBooksApiMonitoring
import io.coinmetrics.api.service.MarketConstraints
import io.coinmetrics.api.service.MarketResolvingService
import io.coinmetrics.api.service.StreamConnectionCountLimitService
import io.coinmetrics.api.utils.MonitoredFixedThreadPoolDispatcher
import io.coinmetrics.bookstreams.BookConsumer
import io.coinmetrics.bookstreams.BookMessage
import io.coinmetrics.bookstreams.Depth
import io.coinmetrics.httpserver.ServerWebSocket
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.cancelAndJoin
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.shareIn
import kotlinx.coroutines.isActive
import kotlinx.coroutines.job
import kotlinx.coroutines.launch
import org.slf4j.LoggerFactory
import java.time.Duration
import java.util.UUID
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.Executors

class MarketOrderBooksClientConnections(
    private val config: StreamingConfig,
    private val monitoring: StreamingBooksApiMonitoring,
    streamConnectionCountLimitService: StreamConnectionCountLimitService,
    private val bookConsumer: Lazy<BookConsumer>,
    private val marketResolvingService: MarketResolvingService,
    private val marketClientConnectionService: MarketClientConnectionService,
    private val statisticsUpdateIntervalMs: Long,
) {
    private val log = LoggerFactory.getLogger(MarketOrderBooksClientConnections::class.java)

    private val dispatcher =
        MonitoredFixedThreadPoolDispatcher(
            name = "streaming-books",
            threadsCount = Runtime.getRuntime().availableProcessors(),
            monitoring.commonMonitoring,
        )
    private val statisticsDispatcher =
        Executors.newSingleThreadExecutor { Thread(it, "streaming-books-wildcard-refresh") }.asCoroutineDispatcher()

    // it is only accessed in the inboundDispatcher single thread
    private val connections =
        ClientConnections(
            streamConnectionCountLimitService,
            dispatcher.dispatcher,
            monitoring.commonMonitoring.streamingMonitoring,
            endpointName = "market-orderbooks",
            onConnectionRemove = this::removeConnection,
        )

    private val scope = CoroutineScope(dispatcher.dispatcher)
    private val marketAndDepthToLastSnapshot = ConcurrentHashMap<Pair<String, Depth>, StreamingBook>()
    private lateinit var shards: List<Flow<List<StreamingBook>>>

    fun start() {
        shards =
            bookConsumer.value.shards.map { shard ->
                val uuid = UUID.randomUUID()
                var sequenceId = 1L
                shard
                    .map { bookMessages ->
                        bookMessages.messages.mapNotNull { bookMessage ->
                            val (book, initialSnapshot) =
                                when (bookMessage) {
                                    is BookMessage.InitialSnapshot -> bookMessage.book to true
                                    is BookMessage.Update -> bookMessage.book to false
                                    is BookMessage.PositionUpdate -> return@mapNotNull null
                                }

                            val market =
                                marketResolvingService
                                    .resolve(
                                        book.marketId.exchangeId,
                                        book.baseId,
                                        book.quoteId,
                                        book.marketId.symbol,
                                        book.marketId.kind,
                                    ).getOrElse { _ -> return@mapNotNull null }

                            book.exchangeProvidedTime?.also { exchangeProvidedTime ->
                                monitoring.streamingBooksCollectTimeLag
                                    .labelValues(market)
                                    .set(
                                        Duration
                                            .between(exchangeProvidedTime, book.receivedFromExchangeTime)
                                            .toMillis() / 1000.0,
                                    )
                            }

                            val coinMetricsId =
                                book.exchangeProvidedId?.let {
                                    StreamingBook.CoinMetricsId.ExchangeProvided(it)
                                } ?: StreamingBook.CoinMetricsId.Generated(uuid, sequenceId)
                            sequenceId += 1

                            val streamingBook =
                                StreamingBook(
                                    market,
                                    coinMetricsId,
                                    book,
                                    initialSnapshot,
                                )
                            if (streamingBook.book.isSnapshot) {
                                marketAndDepthToLastSnapshot[streamingBook.market to bookMessage.topic.depth] =
                                    streamingBook
                            }
                            streamingBook
                        }
                    }.shareIn(scope, SharingStarted.Eagerly)
            }

        scope.launch(statisticsDispatcher) {
            while (isActive) {
                runCatching {
                    marketClientConnectionService.handleStatisticsUpdate(
                        dataType = "books",
                        connections = connections.connections,
                    )
                }.onFailure { e ->
                    if (e is CancellationException) {
                        throw e
                    }
                    log.error("Failed to handle statistics update", e)
                }
                delay(statisticsUpdateIntervalMs)
            }
        }
    }

    fun addClientConnectionAsync(
        request: GetTimeseriesStreamMarketOrderbooksRequest,
        webSocket: ServerWebSocket,
        depth: Depth,
        interestedMarkets: Collection<MarketConstraints>,
        backfillRequested: Boolean,
        statisticsHash: Int,
    ) {
        val connection =
            MarketOrderBooksClientConnection(
                config = config,
                httpRequest = request.httpRequest,
                webSocket = webSocket,
                monitoring = monitoring.commonMonitoring.streamingMonitoring,
                depth = depth,
                interestedMarkets = interestedMarkets.mapTo(HashSet()) { it.parsedMarket.toString() },
                patternMarkets = request.markets.filter { it.contains("*") }.toHashSet(),
                parentScope = scope,
                statisticsHash = statisticsHash,
            )
        connections.addAsync(connection) {
            scope.launch {
                val subscription = bookConsumer.value.subscribe()
                for (market in interestedMarkets) {
                    if (backfillRequested) {
                        val book =
                            marketAndDepthToLastSnapshot[market.parsedMarket.toString() to connection.depth] ?: continue
                        connection.send(book, backfillSnapshot = true)
                    }
                    subscription.add(market.normalizedMarket.exchange, depth)
                }
                // Subscribe to the relevant shard to start streaming data.
                for (shardId in subscription.shardIds) {
                    connection.subscribe(shards[shardId])
                }
                // Not calling subscription.subscribe() since there's already a catch-all subscription.
            }
        }
    }

    private fun removeConnection(connection: MarketOrderBooksClientConnection) {
        connection.close()
    }

    suspend fun close() {
        scope.coroutineContext.job.cancelAndJoin()

        dispatcher.close()
        statisticsDispatcher.close()

        connections.close()
    }
}
