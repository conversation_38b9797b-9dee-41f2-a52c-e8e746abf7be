package io.coinmetrics.api.endpoints.stream.asset

import com.google.common.util.concurrent.ThreadFactoryBuilder
import io.coinmetrics.api.chainstate.BlockHeader
import io.coinmetrics.api.chainstate.ChainState
import io.coinmetrics.api.chainstate.ChainStateListener
import io.coinmetrics.api.utils.TimeUtils
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.job
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.slf4j.LoggerFactory
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.Executors

class NetworkDataPoller(
    private val name: String,
    private val clientConnections: AssetMetricsClientConnections,
    private val pollIntervalMs: Long,
    private val produceReorgEvents: Boolean,
    private val metricsResolver: suspend () -> Map<String, List<String>>,
    // (Asset, Metrics) -> BaseNetworkDataProvider
    private val dataProviderFactory: (String, Collection<String>) -> AbstractBbbDataProvider,
) {
    companion object {
        private val log = LoggerFactory.getLogger(NetworkDataPoller::class.java)
    }

    private val dispatcher =
        Executors
            .newSingleThreadExecutor(
                ThreadFactoryBuilder().setNameFormat("network-$name-data-poller").build(),
            ).asCoroutineDispatcher()
    private val scope = CoroutineScope(dispatcher)

    // Map<Asset, List<Metric>>
    private lateinit var metrics: Map<String, List<String>>

    // Map<Asset, AssetState>
    private val assetStates = ConcurrentHashMap<String, AssetState>()

    suspend fun start() {
        if (pollIntervalMs != 0L) {
            // todo refresh metrics periodically?
            metrics = metricsResolver.invoke()
            scope.launch { pollLoop() }
        }
    }

    private suspend fun pollLoop() {
        try {
            while (scope.isActive) {
                pollUpdates()
                delay(pollIntervalMs)
            }
        } catch (e: CancellationException) {
            // normal exit
        } catch (e: Exception) {
            log.error("Fail to poll updates.", e)
        }
    }

    private suspend fun pollUpdates() {
        // log.trace("Updating latest network data realtime metrics for '{}'.", dataProviderType)

        metrics.forEach { (asset, metrics) ->
            try {
                pollUpdates(asset, metrics)
            } catch (e: CancellationException) {
                throw e
            } catch (e: Exception) {
                log.error("Fail to poll updates for asset '{}', provider '{}'.", asset, name, e)
            }
        }
    }

    private suspend fun pollUpdates(
        asset: String,
        metrics: Collection<String>,
    ) {
        val assetState = assetStates[asset]
        if (assetState == null) {
            val dataProvider = dataProviderFactory.invoke(asset, metrics)

            val initialChainTip = getTip(dataProvider, asset) ?: return

            log.trace("Initial tip for asset '$asset' is ${initialChainTip.height}/${initialChainTip.hash}.")

            val chainStateListener = createChainStateListener(asset, dataProvider)
            val chainState = ChainState(dataProvider, initialChainTip, chainStateListener)
            val newAssetState = AssetState(dataProvider, chainState, chainStateListener)
            chainStateListener.executeCallbacks()

            assetStates[asset] = newAssetState
        } else {
            val newChainTip = getTip(assetState.dataProvider, asset) ?: return
            if (newChainTip.hash != assetState.chainState.getTip().hash) {
                log.trace(
                    "Changing tip for asset '{}' from {}/{} to {}/{}.",
                    asset,
                    assetState.chainState.getTip().height,
                    assetState.chainState.getTip().hash,
                    newChainTip.height,
                    newChainTip.hash,
                )
                val chainStateListener =
                    try {
                        assetState.chainState.setTip(newChainTip)
                        assetState.chainStateListener
                    } catch (e: Exception) {
                        // catch only specific exceptions inside ChainState class
                        if (e is NoSuchElementException || e is IllegalStateException) {
                            log.error(
                                "Can't change tip for asset '{}' from {}/{} to {}/{}. Resetting chain state.",
                                asset,
                                assetState.chainState.getTip().height,
                                assetState.chainState.getTip().hash,
                                newChainTip.height,
                                newChainTip.hash,
                                e,
                            )

                            val chainStateListener = createChainStateListener(asset, assetState.dataProvider)
                            val chainState = ChainState(assetState.dataProvider, newChainTip, chainStateListener)
                            val newAssetState = AssetState(assetState.dataProvider, chainState, chainStateListener)
                            assetStates[asset] = newAssetState
                            chainStateListener
                        } else {
                            throw e
                        }
                    }
                chainStateListener.executeCallbacks()
            }
        }
    }

    private suspend fun getTip(
        dataProvider: AbstractBbbDataProvider,
        asset: String,
    ): BlockHeader? =
        try {
            dataProvider.getTip().also {
                if (it == null) {
                    log.warn("Cannot get tip of the chain for asset '{}', dataType '{}'. No data in the database.", asset, name)
                }
            }
        } catch (e: CancellationException) {
            // expected on shutdown
            null
        } catch (e: Exception) {
            log.error("Cannot get tip of the chain for asset '{}', dataType '{}'.", asset, name, e)
            null
        }

    private fun createChainStateListener(
        asset: String,
        dataProvider: AbstractBbbDataProvider,
    ): AsyncChainStateListener =
        object : AsyncChainStateListener {
            // following callback methods are called from external library without suspend support
            // so we have to make a workaround
            private val callbacks = ArrayList<suspend () -> Unit>()

            override fun connect(block: BlockHeader) {
                callbacks.add {
                    val header =
                        listOf(
                            "time" to TimeUtils.dateTimeFormatter.format(block.time),
                            "asset" to asset,
                            "height" to block.height.toString(),
                            "hash" to block.hash,
                            "parent_hash" to block.parent,
                            "type" to "new_block",
                        )
                    val metrics = dataProvider.fetchAllMetricsForBlock(block)
                    withContext(clientConnections.inboundProcessingDispatcher) {
                        clientConnections.onMetrics(
                            asset = asset,
                            headers = header,
                            metrics = metrics,
                            frequency = "1b",
                            gropingKeyValue = @Suppress("UNCHECKED_CAST") (block.height as Comparable<Any>),
                        )
                    }
                }
            }

            override fun disconnect(block: BlockHeader) {
                if (produceReorgEvents) {
                    callbacks.add {
                        val header =
                            listOf(
                                "time" to TimeUtils.dateTimeFormatter.format(block.time),
                                "asset" to asset,
                                "height" to block.height.toString(),
                                "hash" to block.hash,
                                "parent_hash" to block.parent,
                                "type" to "reorg",
                            )
                        withContext(clientConnections.inboundProcessingDispatcher) {
                            clientConnections.onReorg(asset, header)
                        }
                    }
                }
            }

            override suspend fun executeCallbacks() {
                callbacks.forEach { it.invoke() }
                callbacks.clear()
            }
        }

    fun getAssetHeight(asset: String): Int? = assetStates[asset]?.chainState?.getTip()?.height

    suspend fun close() {
        scope.cancel()
        scope.coroutineContext.job.join()
        dispatcher.close()
    }

    private class AssetState(
        val dataProvider: AbstractBbbDataProvider,
        val chainState: ChainState,
        val chainStateListener: AsyncChainStateListener,
    )

    private interface AsyncChainStateListener : ChainStateListener {
        suspend fun executeCallbacks()
    }
}
