package io.coinmetrics.api.endpoints.stream

import java.util.concurrent.atomic.AtomicLong

internal class ThrottledAction(
    val throttleTimeMs: Long,
) {
    private val lastExecutionTime = AtomicLong(0)

    inline fun act(action: () -> Unit) {
        val now = System.currentTimeMillis()
        val lastExecutionTimeVal = lastExecutionTime.get()
        if (now - lastExecutionTimeVal > throttleTimeMs && lastExecutionTime.compareAndSet(lastExecutionTimeVal, now)) {
            action()
        }
    }
}
