package io.coinmetrics.api.endpoints.stream

import io.coinmetrics.api.model.KafkaDataProcessorInfo
import io.coinmetrics.api.modules.main.MainApiConfig.KafkaSourceConfig
import io.coinmetrics.api.monitoring.SwitchingSourceKafkaMonitoring
import io.coinmetrics.api.utils.KafkaDataSource
import io.coinmetrics.queues.MessageAndOffset
import java.time.Clock

/**
 * Consumes data from multiple data sources.
 * Forward records to clients only from a mainDataSource.
 * If the current mainDataSource lags behind, it switches to the next ACTIVE (dataSource.lastMessageTime >= now - nonMainDataSourceLagToConsiderActiveMs) data source in the list.
 * Always tries to return to the first data source defined in the list.
 *
 * Strives to keep gap-less sequence of 1s records when switching is in progress.
 *
 * It's done in the following way:
 * We keep open streaming connections to all data sources.
 * One on them is a main data source. It's the first in the list by default.
 * We emulate slow consumers for all non-main data sources. They all lag by nonMainDataSourceForcedLagMs time.
 * So we try to keep the nonMainDataSourceForcedLagMs lag for all non-main data sources.
 * When the main data source starts to lag, we are switching to the next data source and quickly catching it up.
 * The non-main data source lag is required to not have gaps in message consuming during the switching process.
 */
abstract class AbstractSourceSwitchingKafkaDataProvider<T>(
    clock: Clock,
    config: KafkaSourceConfig,
    allowedTimeSpentFromTheLastSwitchMs: Long = config.mainDataSourceSilentLimitMs,
    ensureRecentDataInFirstSource: Boolean = false,
    kafkaMonitoring: SwitchingSourceKafkaMonitoring,
) : AbstractKafkaDataProvider<T>(
        config = config,
        calculateAndLogTheLagThrottled = false,
        kafkaMonitoring = kafkaMonitoring,
    ) {
    private val kafkaSourceSwitcher =
        KafkaSourceSwitcher(
            clock = clock,
            config = config,
            allowedTimeSpentFromTheLastSwitchMs = allowedTimeSpentFromTheLastSwitchMs,
            dataSources = dataSources,
            kafkaMonitoring = kafkaMonitoring,
            ensureRecentDataInFirstSource = ensureRecentDataInFirstSource,
        )

    override suspend fun prepareNext(dataSource: KafkaDataSource<T>): MessageAndOffset<T>? {
        val message = consumeNext(dataSource.consumer) ?: return null
        val timestamp = getTimestampForSwitch(dataSource, message) ?: return null
        if (!kafkaSourceSwitcher.maybeSwitchAndShouldProceed(dataSource, timestamp)) {
            return null
        }
        return message
    }

    open suspend fun getTimestampForSwitch(
        dataSource: KafkaDataSource<T>,
        message: MessageAndOffset<T>,
    ): Long? = message.timestampMs

    override fun toKafkaDataProcessorInfo(): KafkaDataProcessorInfo =
        KafkaDataProcessorInfo.of(config) {
            kafkaSourceSwitcher.mainDataSourceIndex
        }
}
