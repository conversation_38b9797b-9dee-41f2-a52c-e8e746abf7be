package io.coinmetrics.api.endpoints.catalogall.v2.index

import io.coinmetrics.api.Response
import io.coinmetrics.api.endpoints.GetCatalogAllV2IndexLevelsEndpoint
import io.coinmetrics.api.endpoints.GetCatalogAllV2IndexLevelsRequest
import io.coinmetrics.api.model.page.StringPageRequest
import io.coinmetrics.api.models.CatalogV2IndexLevelsResponse
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.catalog.CatalogIndexService
import io.coinmetrics.api.service.getCatalogRateLimitHeaders
import io.coinmetrics.api.utils.toChunkedResponse

class GetCatalogAllV2IndexLevelsEndpointImpl(
    private val amsService: AmsService,
    private val indexService: CatalogIndexService,
) : GetCatalogAllV2IndexLevelsEndpoint() {
    override suspend fun handle(request: GetCatalogAllV2IndexLevelsRequest): Response<CatalogV2IndexLevelsResponse> {
        val headers =
            amsService
                .getCatalogRateLimitHeaders(request.apiKey, request.httpRequest)
                .getOrElse { (apiError, headers) -> return Response.errorResponse(apiError, headers) }

        amsService.check(apiKey = request.apiKey).getOrElse { (apiError) ->
            return Response.errorResponse(apiError, headers)
        }

        return StringPageRequest
            .create(request.nextPageToken, request.pageSize, request.pagingFrom)
            .flatMap { pageRequest ->
                indexService.getIndexes(
                    apiKey = null,
                    request.indexes,
                    request.format,
                    request.httpRequest,
                    pageRequest,
                )
            }.toChunkedResponse(headers)
    }
}
