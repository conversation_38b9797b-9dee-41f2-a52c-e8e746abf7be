package io.coinmetrics.api.endpoints.catalogall.market

import io.coinmetrics.api.Response
import io.coinmetrics.api.endpoints.GetCatalogAllMarketImpliedVolatilityEndpoint
import io.coinmetrics.api.endpoints.GetCatalogAllMarketImpliedVolatilityRequest
import io.coinmetrics.api.models.CatalogMarketImpliedVolatilityResponse
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.catalog.market.CatalogMarketRequest
import io.coinmetrics.api.service.catalog.market.CatalogV1MarketService
import io.coinmetrics.api.service.getCatalogRateLimitHeaders

class GetCatalogAllMarketImpliedVolatilityEndpointImpl(
    private val amsService: AmsService,
    private val catalogMarketService: CatalogV1MarketService,
) : GetCatalogAllMarketImpliedVolatilityEndpoint() {
    override suspend fun handle(request: GetCatalogAllMarketImpliedVolatilityRequest): Response<CatalogMarketImpliedVolatilityResponse> {
        val headers =
            amsService
                .getCatalogRateLimitHeaders(
                    apiKey = request.apiKey,
                    httpRequest = request.httpRequest,
                ).getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }

        val catalogMarketRequest =
            CatalogMarketRequest(
                request.httpRequest,
                apiKey = null,
                request.markets,
                request.exchange,
                request.type,
                request.base,
                request.quote,
                request.asset,
                request.symbol,
                request.format,
                request.limit,
                request.pretty,
            )

        return catalogMarketService
            .getImpliedVolatility(catalogMarketRequest)
            .getOrElse {
                return Response.errorResponse(it, headers)
            }.let { (format, greeks) ->
                Response.chunkedResponse(items = greeks, format = format, headers = headers)
            }
    }
}
