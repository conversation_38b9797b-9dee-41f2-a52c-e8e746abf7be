package io.coinmetrics.api.endpoints.catalogall.v2

import io.coinmetrics.api.Response
import io.coinmetrics.api.endpoints.GetCatalogAllV2BlockchainBalanceUpdatesEndpoint
import io.coinmetrics.api.endpoints.GetCatalogAllV2BlockchainBalanceUpdatesRequest
import io.coinmetrics.api.model.page.StringPageRequest
import io.coinmetrics.api.models.CatalogV2BlockchainBalanceUpdatesResponse
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.catalog.CatalogAtlasService
import io.coinmetrics.api.service.getCatalogRateLimitHeaders
import io.coinmetrics.api.utils.toChunkedResponse

class GetCatalogAllV2BlockchainBalanceUpdatesEndpointImpl(
    private val amsService: AmsService,
    private val catalogAtlasService: CatalogAtlasService,
) : GetCatalogAllV2BlockchainBalanceUpdatesEndpoint() {
    override suspend fun handle(
        request: GetCatalogAllV2BlockchainBalanceUpdatesRequest,
    ): Response<CatalogV2BlockchainBalanceUpdatesResponse> {
        val headers =
            amsService
                .getCatalogRateLimitHeaders(request.apiKey, request.httpRequest)
                .getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }

        return amsService
            .check(request.apiKey)
            .mapFailure { (apiError) -> apiError }
            .flatMap { StringPageRequest.create(request.nextPageToken, request.pageSize, request.pagingFrom) }
            .flatMap { pageRequest ->
                catalogAtlasService.getAtlasAssets(
                    endpoint = "balance-updates",
                    apiKey = null,
                    requestedAssets = request.assets,
                    format = request.format,
                    httpRequest = request.httpRequest,
                    pageRequest = pageRequest,
                )
            }.toChunkedResponse(headers)
    }
}
