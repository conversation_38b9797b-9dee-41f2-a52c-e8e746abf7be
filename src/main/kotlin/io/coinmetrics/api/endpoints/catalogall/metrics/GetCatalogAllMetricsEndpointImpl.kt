package io.coinmetrics.api.endpoints.catalogall.metrics

import io.coinmetrics.api.Response
import io.coinmetrics.api.endpoints.GetCatalogAllMetricsEndpoint
import io.coinmetrics.api.endpoints.GetCatalogAllMetricsRequest
import io.coinmetrics.api.models.CatalogMetricsResponse
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.catalog.metric.impl.AssetMetricsService
import io.coinmetrics.api.service.getCatalogRateLimitHeaders

class GetCatalogAllMetricsEndpointImpl(
    private val amsService: AmsService,
    private val assetMetricsService: AssetMetricsService,
) : GetCatalogAllMetricsEndpoint() {
    override suspend fun handle(request: GetCatalogAllMetricsRequest): Response<CatalogMetricsResponse> {
        val headers =
            amsService
                .getCatalogRateLimitHeaders(
                    apiKey = request.apiKey,
                    httpRequest = request.httpRequest,
                ).getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }

        amsService.check(apiKey = request.apiKey).getOrElse { (apiError) ->
            return Response.errorResponse(apiError, headers)
        }

        return assetMetricsService
            .findSupportedCatalogV1Metrics(apiKey = null, request.metrics, request.reviewable)
            .getOrElse { return Response.errorResponse(it, headers) }
            .let { Response.successResponse(it, headers) }
    }
}
