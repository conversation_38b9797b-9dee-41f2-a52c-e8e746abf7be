package io.coinmetrics.api.endpoints.catalogall.market

import io.coinmetrics.api.Response
import io.coinmetrics.api.endpoints.GetCatalogAllMarketGreeksEndpoint
import io.coinmetrics.api.endpoints.GetCatalogAllMarketGreeksRequest
import io.coinmetrics.api.models.CatalogMarketGreeksResponse
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.catalog.market.CatalogMarketRequest
import io.coinmetrics.api.service.catalog.market.CatalogV1MarketService
import io.coinmetrics.api.service.getCatalogRateLimitHeaders

class GetCatalogAllMarketGreeksEndpointImpl(
    private val amsService: AmsService,
    private val catalogMarketService: CatalogV1MarketService,
) : GetCatalogAllMarketGreeksEndpoint() {
    override suspend fun handle(request: GetCatalogAllMarketGreeksRequest): Response<CatalogMarketGreeksResponse> {
        val headers =
            amsService
                .getCatalogRateLimitHeaders(
                    apiKey = request.apiKey,
                    httpRequest = request.httpRequest,
                ).getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }

        val catalogMarketRequest =
            CatalogMarketRequest(
                request.httpRequest,
                apiKey = null,
                request.markets,
                request.exchange,
                request.type,
                request.base,
                request.quote,
                request.asset,
                request.symbol,
                request.format,
                request.limit,
                request.pretty,
            )

        return catalogMarketService
            .getGreeks(catalogMarketRequest)
            .getOrElse {
                return Response.errorResponse(it, headers)
            }.let { (format, greeks) ->
                Response.chunkedResponse(items = greeks, format = format, headers = headers)
            }
    }
}
