package io.coinmetrics.api.endpoints.catalogall.market

import io.coinmetrics.api.Response
import io.coinmetrics.api.endpoints.GetCatalogAllMarketOrderbooksEndpoint
import io.coinmetrics.api.endpoints.GetCatalogAllMarketOrderbooksRequest
import io.coinmetrics.api.models.CatalogMarketOrderbooksResponse
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.catalog.market.CatalogMarketRequest
import io.coinmetrics.api.service.catalog.market.CatalogV1MarketService
import io.coinmetrics.api.service.getCatalogRateLimitHeaders

class GetCatalogAllS3MarketOrderbooksEndpointImpl(
    private val amsService: AmsService,
    private val catalogMarketService: CatalogV1MarketService,
) : GetCatalogAllMarketOrderbooksEndpoint() {
    override suspend fun handle(request: GetCatalogAllMarketOrderbooksRequest): Response<CatalogMarketOrderbooksResponse> {
        val headers =
            amsService
                .getCatalogRateLimitHeaders(
                    apiKey = request.apiKey,
                    httpRequest = request.httpRequest,
                ).getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }

        val catalogMarketRequest =
            CatalogMarketRequest(
                request.httpRequest,
                apiKey = null,
                request.markets,
                request.exchange,
                request.type,
                request.base,
                request.quote,
                request.asset,
                request.symbol,
                request.format,
                request.limit,
                request.pretty,
            )

        return catalogMarketService
            .getTieredS3Books(catalogMarketRequest)
            .getOrElse {
                return Response.errorResponse(it, headers)
            }.let { (format, books) ->
                Response.chunkedResponse(items = books, format = format, headers = headers)
            }
    }
}
