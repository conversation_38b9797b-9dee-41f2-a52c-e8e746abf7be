package io.coinmetrics.api.endpoints.catalogall.v2.metrics

import io.coinmetrics.api.Response
import io.coinmetrics.api.endpoints.GetCatalogAllV2ExchangeAssetMetricsEndpoint
import io.coinmetrics.api.endpoints.GetCatalogAllV2ExchangeAssetMetricsRequest
import io.coinmetrics.api.model.page.StringPageRequest
import io.coinmetrics.api.models.CatalogV2ExchangeAssetMetricsResponse
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.catalog.metric.impl.ExchangeAssetMetricsService
import io.coinmetrics.api.service.getCatalogRateLimitHeaders

class GetCatalogAllV2ExchangeAssetMetricsEndpointImpl(
    private val amsService: AmsService,
    private val exchangeAssetMetricsService: ExchangeAssetMetricsService,
) : GetCatalogAllV2ExchangeAssetMetricsEndpoint() {
    override suspend fun handle(request: GetCatalogAllV2ExchangeAssetMetricsRequest): Response<CatalogV2ExchangeAssetMetricsResponse> {
        val headers =
            amsService
                .getCatalogRateLimitHeaders(
                    apiKey = request.apiKey,
                    httpRequest = request.httpRequest,
                ).getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }

        return StringPageRequest
            .create(request.nextPageToken, request.pageSize, request.pagingFrom)
            .flatMap { pageRequest ->
                exchangeAssetMetricsService.findSupportedCatalogV2ExchangeAssetMetrics(
                    apiKey = null,
                    request.exchangeAssets,
                    request.metrics,
                    request.format,
                    request.httpRequest,
                    pageRequest,
                )
            }.getOrElse { return Response.errorResponse(it, headers) }
            .let { (format, exchangeAssets) -> Response.chunkedResponse(exchangeAssets, headers, format) }
    }
}
