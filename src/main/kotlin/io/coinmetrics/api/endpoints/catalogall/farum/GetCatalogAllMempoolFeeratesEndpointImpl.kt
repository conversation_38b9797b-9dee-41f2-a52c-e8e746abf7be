package io.coinmetrics.api.endpoints.catalogall.farum

import io.coinmetrics.api.ChunkedResponseFormat
import io.coinmetrics.api.Response
import io.coinmetrics.api.endpoints.GetCatalogAllMempoolFeeratesEndpoint
import io.coinmetrics.api.endpoints.GetCatalogAllMempoolFeeratesRequest
import io.coinmetrics.api.models.CatalogMempoolFeeratesResponse
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.catalog.CatalogFarumService
import io.coinmetrics.api.service.getCatalogRateLimitHeaders
import io.coinmetrics.api.utils.toChunkedResponse

class GetCatalogAllMempoolFeeratesEndpointImpl(
    private val amsService: AmsService,
    private val farumService: CatalogFarumService,
) : GetCatalogAllMempoolFeeratesEndpoint() {
    override suspend fun handle(request: GetCatalogAllMempoolFeeratesRequest): Response<CatalogMempoolFeeratesResponse> {
        val headers =
            amsService
                .getCatalogRateLimitHeaders(request.apiKey, request.httpRequest)
                .getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }

        return amsService
            .check(request.apiKey)
            .mapFailure { (apiError) -> apiError }
            .flatMap {
                farumService.getMempoolFeerates(
                    apiKey = null,
                    requestedAssets = request.assets,
                    format = ChunkedResponseFormat.Json().name,
                    httpRequest = request.httpRequest,
                )
            }.toChunkedResponse(headers)
    }
}
