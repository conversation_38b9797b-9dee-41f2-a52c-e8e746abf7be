package io.coinmetrics.api.endpoints.catalogall.market

import io.coinmetrics.api.Response
import io.coinmetrics.api.endpoints.GetCatalogAllMarketFundingRatesEndpoint
import io.coinmetrics.api.endpoints.GetCatalogAllMarketFundingRatesRequest
import io.coinmetrics.api.models.CatalogMarketFundingRatesResponse
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.catalog.market.CatalogMarketRequest
import io.coinmetrics.api.service.catalog.market.CatalogV1MarketService
import io.coinmetrics.api.service.getCatalogRateLimitHeaders

class GetCatalogAllMarketFundingRatesEndpointImpl(
    private val amsService: AmsService,
    private val catalogMarketService: CatalogV1MarketService,
) : GetCatalogAllMarketFundingRatesEndpoint() {
    override suspend fun handle(request: GetCatalogAllMarketFundingRatesRequest): Response<CatalogMarketFundingRatesResponse> {
        val headers =
            amsService
                .getCatalogRateLimitHeaders(
                    apiKey = request.apiKey,
                    httpRequest = request.httpRequest,
                ).getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }

        val catalogMarketRequest =
            CatalogMarketRequest(
                request.httpRequest,
                apiKey = null,
                request.markets,
                request.exchange,
                request.type,
                request.base,
                request.quote,
                request.asset,
                request.symbol,
                request.format,
                request.limit,
                request.pretty,
            )

        return catalogMarketService
            .getFundingRates(catalogMarketRequest)
            .getOrElse {
                return Response.errorResponse(it, headers)
            }.let { (format, fundingRates) ->
                Response.chunkedResponse(items = fundingRates, format = format, headers = headers)
            }
    }
}
