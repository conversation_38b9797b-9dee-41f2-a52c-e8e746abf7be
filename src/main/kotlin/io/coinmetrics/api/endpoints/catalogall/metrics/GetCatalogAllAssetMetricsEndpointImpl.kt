package io.coinmetrics.api.endpoints.catalogall.metrics

import io.coinmetrics.api.Response
import io.coinmetrics.api.endpoints.GetCatalogAllAssetMetricsEndpoint
import io.coinmetrics.api.endpoints.GetCatalogAllAssetMetricsRequest
import io.coinmetrics.api.models.CatalogAssetMetricsResponse
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.catalog.metric.impl.AssetMetricsService
import io.coinmetrics.api.service.getCatalogRateLimitHeaders

class GetCatalogAllAssetMetricsEndpointImpl(
    private val amsService: AmsService,
    private val assetMetricsService: AssetMetricsService,
) : GetCatalogAllAssetMetricsEndpoint() {
    override suspend fun handle(request: GetCatalogAllAssetMetricsRequest): Response<CatalogAssetMetricsResponse> {
        val headers =
            amsService
                .getCatalogRateLimitHeaders(
                    apiKey = request.apiKey,
                    httpRequest = request.httpRequest,
                ).getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }

        amsService.check(apiKey = request.apiKey).getOrElse { (apiError) ->
            return Response.errorResponse(apiError, headers)
        }

        return assetMetricsService
            .findSupportedCatalogV1AssetMetrics(apiKey = null, request.metrics, request.reviewable)
            .getOrElse { return Response.errorResponse(it, headers) }
            .let { r ->
                Response
                    .successResponse(
                        r.copy(data = r.data.filter { info -> !info.metric.startsWith("liquidations_reported_future_") }),
                        headers,
                    )
            }
    }
}
