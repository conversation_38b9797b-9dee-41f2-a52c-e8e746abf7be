package io.coinmetrics.api.endpoints.catalogall

import io.coinmetrics.api.ChunkedResponseFormat
import io.coinmetrics.api.Response
import io.coinmetrics.api.endpoints.GetCatalogAllIndexesEndpoint
import io.coinmetrics.api.endpoints.GetCatalogAllIndexesRequest
import io.coinmetrics.api.models.IndexesResponse
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.catalog.CatalogIndexService
import io.coinmetrics.api.service.getCatalogRateLimitHeaders
import io.coinmetrics.api.utils.toChunkedResponse

class GetCatalogAllIndexesEndpointImpl(
    private val amsService: AmsService,
    private val indexService: CatalogIndexService,
) : GetCatalogAllIndexesEndpoint() {
    override suspend fun handle(request: GetCatalogAllIndexesRequest): Response<IndexesResponse> {
        val headers =
            amsService
                .getCatalogRateLimitHeaders(
                    apiKey = request.apiKey,
                    httpRequest = request.httpRequest,
                ).getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }

        amsService.check(apiKey = request.apiKey).getOrElse { (apiError) ->
            return Response.errorResponse(apiError, headers)
        }

        return indexService
            .getIndexes(
                apiKey = null,
                request.indexes,
                format = ChunkedResponseFormat.Json().name,
                request.httpRequest,
            ).toChunkedResponse(headers)
    }
}
