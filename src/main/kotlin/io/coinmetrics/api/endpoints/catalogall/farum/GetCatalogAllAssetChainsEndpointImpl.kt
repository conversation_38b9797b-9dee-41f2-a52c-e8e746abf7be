package io.coinmetrics.api.endpoints.catalogall.farum

import io.coinmetrics.api.ChunkedResponseFormat
import io.coinmetrics.api.Response
import io.coinmetrics.api.endpoints.GetCatalogAllAssetChainsEndpoint
import io.coinmetrics.api.endpoints.GetCatalogAllAssetChainsRequest
import io.coinmetrics.api.models.CatalogAssetChainsResponse
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.catalog.CatalogFarumService
import io.coinmetrics.api.service.getCatalogRateLimitHeaders
import io.coinmetrics.api.utils.toChunkedResponse

class GetCatalogAllAssetChainsEndpointImpl(
    private val amsService: AmsService,
    private val farumService: CatalogFarumService,
) : GetCatalogAllAssetChainsEndpoint() {
    override suspend fun handle(request: GetCatalogAllAssetChainsRequest): Response<CatalogAssetChainsResponse> {
        val headers =
            amsService
                .getCatalogRateLimitHeaders(request.apiKey, request.httpRequest)
                .getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }

        return amsService
            .check(request.apiKey)
            .mapFailure { (apiError) -> apiError }
            .flatMap {
                farumService.getAssetChains(
                    apiKey = null,
                    requestedAssets = request.assets,
                    format = ChunkedResponseFormat.Json().name,
                    httpRequest = request.httpRequest,
                )
            }.toChunkedResponse(headers)
    }
}
