package io.coinmetrics.api.endpoints.catalogall.market

import io.coinmetrics.api.Response
import io.coinmetrics.api.endpoints.GetCatalogAllMarketMetricsEndpoint
import io.coinmetrics.api.endpoints.GetCatalogAllMarketMetricsRequest
import io.coinmetrics.api.models.CatalogMarketMetricsResponse
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.catalog.market.CatalogMarketRequest
import io.coinmetrics.api.service.catalog.market.CatalogV1MarketService
import io.coinmetrics.api.service.getCatalogRateLimitHeaders

class GetCatalogAllMarketMetricsEndpointImpl(
    private val amsService: AmsService,
    private val catalogMarketService: CatalogV1MarketService,
) : GetCatalogAllMarketMetricsEndpoint() {
    override suspend fun handle(request: GetCatalogAllMarketMetricsRequest): Response<CatalogMarketMetricsResponse> {
        val headers =
            amsService
                .getCatalogRateLimitHeaders(
                    apiKey = request.apiKey,
                    httpRequest = request.httpRequest,
                ).getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }

        amsService.check(apiKey = request.apiKey).getOrElse { (apiError) ->
            return Response.errorResponse(apiError, headers)
        }

        val catalogMarketRequest =
            CatalogMarketRequest(
                request.httpRequest,
                apiKey = null,
                request.markets,
                request.exchange,
                request.type,
                request.base,
                request.quote,
                request.asset,
                request.symbol,
                request.format,
                request.limit,
                request.pretty,
            )

        return catalogMarketService
            .getMetrics(catalogMarketRequest)
            .getOrElse {
                return Response.errorResponse(it, headers)
            }.let { (format, metrics) ->
                Response.chunkedResponse(items = metrics, format = format, headers = headers)
            }
    }
}
