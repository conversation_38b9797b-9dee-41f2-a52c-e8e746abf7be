package io.coinmetrics.api.endpoints.catalogall.v2

import io.coinmetrics.api.Response
import io.coinmetrics.api.badNextPageToken
import io.coinmetrics.api.endpoints.GetCatalogAllV2MarketOrderbooksEndpoint
import io.coinmetrics.api.endpoints.GetCatalogAllV2MarketOrderbooksRequest
import io.coinmetrics.api.model.page.StringPageRequest
import io.coinmetrics.api.models.CatalogV2MarketOrderbooksResponse
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.catalog.market.CatalogMarketRequest
import io.coinmetrics.api.service.catalog.market.CatalogV2MarketService
import io.coinmetrics.api.service.getCatalogRateLimitHeaders
import io.coinmetrics.api.utils.paging.PageToken

class GetCatalogAllV2S3MarketOrderbooksEndpointImpl(
    private val amsService: AmsService,
    private val catalogMarketService: CatalogV2MarketService,
) : GetCatalogAllV2MarketOrderbooksEndpoint() {
    override suspend fun handle(request: GetCatalogAllV2MarketOrderbooksRequest): Response<CatalogV2MarketOrderbooksResponse> {
        val headers =
            amsService
                .getCatalogRateLimitHeaders(
                    apiKey = request.apiKey,
                    httpRequest = request.httpRequest,
                ).getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }

        val market =
            request.nextPageToken?.let {
                PageToken.StringPageToken
                    .parseCatching(it) {
                        return Response.errorResponse(badNextPageToken())
                    }?.string
            }

        val catalogMarketRequest =
            CatalogMarketRequest(
                request.httpRequest,
                apiKey = null,
                request.markets,
                request.exchange,
                request.type,
                request.base,
                request.quote,
                request.asset,
                request.symbol,
                request.format,
                pretty = request.pretty,
                pageRequest =
                    StringPageRequest(
                        entity = market,
                        pageSize = request.pageSize,
                        pagingFrom = request.pagingFrom,
                    ),
            )

        return catalogMarketService
            .getTieredS3Books(catalogMarketRequest)
            .getOrElse {
                return Response.errorResponse(it, headers)
            }.let { (format, trades) ->
                Response.chunkedResponse(items = trades, format = format, headers = headers)
            }
    }
}
