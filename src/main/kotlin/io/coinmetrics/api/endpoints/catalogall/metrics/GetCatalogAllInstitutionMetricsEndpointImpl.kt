package io.coinmetrics.api.endpoints.catalogall.metrics

import io.coinmetrics.api.Response
import io.coinmetrics.api.endpoints.GetCatalogAllInstitutionMetricsEndpoint
import io.coinmetrics.api.endpoints.GetCatalogAllInstitutionMetricsRequest
import io.coinmetrics.api.models.CatalogInstitutionMetricsResponse
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.catalog.metric.impl.InstitutionMetricsService
import io.coinmetrics.api.service.getCatalogRateLimitHeaders

class GetCatalogAllInstitutionMetricsEndpointImpl(
    private val amsService: AmsService,
    private val institutionMetricsService: InstitutionMetricsService,
) : GetCatalogAllInstitutionMetricsEndpoint() {
    override suspend fun handle(request: GetCatalogAllInstitutionMetricsRequest): Response<CatalogInstitutionMetricsResponse> {
        val headers =
            amsService
                .getCatalogRateLimitHeaders(
                    apiKey = request.apiKey,
                    httpRequest = request.httpRequest,
                ).getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }

        amsService.check(apiKey = request.apiKey).getOrElse { (apiError) ->
            return Response.errorResponse(apiError, headers)
        }

        return institutionMetricsService
            .findSupportedCatalogV1InstitutionMetrics(apiKey = null, request.metrics, request.reviewable)
            .getOrElse { return Response.errorResponse(it, headers) }
            .let { Response.successResponse(it, headers) }
    }
}
