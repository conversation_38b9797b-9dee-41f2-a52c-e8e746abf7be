package io.coinmetrics.api.endpoints.catalogall

import io.coinmetrics.api.Response
import io.coinmetrics.api.endpoints.GetCatalogAllInstitutionsEndpoint
import io.coinmetrics.api.endpoints.GetCatalogAllInstitutionsRequest
import io.coinmetrics.api.endpoints.catalog.GetCatalogInstitutionsEndpointImpl
import io.coinmetrics.api.models.InstitutionsResponse
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.getCatalogRateLimitHeaders

class AllInstitutionsEndpointImpl(
    private val amsService: AmsService,
    private val delegate: GetCatalogInstitutionsEndpointImpl,
) : GetCatalogAllInstitutionsEndpoint() {
    override suspend fun handle(request: GetCatalogAllInstitutionsRequest): Response<InstitutionsResponse> {
        val headers =
            amsService
                .getCatalogRateLimitHeaders(
                    apiKey = request.apiKey,
                    httpRequest = request.httpRequest,
                ).getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }

        amsService.check(apiKey = request.apiKey).getOrElse { (apiError) ->
            return Response.errorResponse(apiError, headers)
        }
        return delegate.handle(
            null,
            request.institutions?.map { it.lowercase() }?.toTypedArray(),
            headers,
            request.httpRequest.receivedTime,
        )
    }
}
