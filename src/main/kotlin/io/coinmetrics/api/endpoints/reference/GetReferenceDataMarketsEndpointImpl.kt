package io.coinmetrics.api.endpoints.reference

import io.coinmetrics.api.Response
import io.coinmetrics.api.endpoints.GetReferenceDataMarketsEndpoint
import io.coinmetrics.api.endpoints.GetReferenceDataMarketsRequest
import io.coinmetrics.api.model.page.StringPageRequest
import io.coinmetrics.api.models.ReferenceDataMarketsResponse
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.catalog.market.CatalogMarketRequest
import io.coinmetrics.api.service.catalog.market.CatalogV2MarketService
import io.coinmetrics.api.service.getReferenceDataRateLimitHeaders
import io.coinmetrics.api.utils.toChunkedResponse

class GetReferenceDataMarketsEndpointImpl(
    private val amsService: AmsService,
    private val catalogMarketService: CatalogV2MarketService,
) : GetReferenceDataMarketsEndpoint() {
    override suspend fun handle(request: GetReferenceDataMarketsRequest): Response<ReferenceDataMarketsResponse> {
        val headers =
            amsService
                .getReferenceDataRateLimitHeaders(
                    apiKey = request.apiKey,
                    httpRequest = request.httpRequest,
                ).getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }

        return StringPageRequest
            .create(request.nextPageToken, request.pageSize, request.pagingFrom)
            .flatMap { pageRequest ->
                val catalogMarketRequest =
                    CatalogMarketRequest(
                        request.httpRequest,
                        apiKey = null,
                        request.markets,
                        request.exchange,
                        request.type,
                        request.base,
                        request.quote,
                        request.asset,
                        request.symbol,
                        request.format,
                        limit = null,
                        request.pretty,
                        pageRequest,
                    )
                catalogMarketService.marketReferenceDataFlow(catalogMarketRequest) { _, referenceData, _ -> referenceData }
            }.toChunkedResponse(headers)
    }
}
