package io.coinmetrics.api.endpoints.reference

import io.coinmetrics.api.Response
import io.coinmetrics.api.badNextPageToken
import io.coinmetrics.api.endpoints.GetReferenceDataInstitutionMetricsEndpoint
import io.coinmetrics.api.endpoints.GetReferenceDataInstitutionMetricsRequest
import io.coinmetrics.api.model.page.StringPageRequest
import io.coinmetrics.api.models.ReferenceDataInstitutionMetricsResponse
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.catalog.metric.impl.InstitutionMetricsService
import io.coinmetrics.api.service.getReferenceDataRateLimitHeaders
import io.coinmetrics.api.utils.paging.PageToken
import io.coinmetrics.api.utils.toChunkedResponse

class GetReferenceDataInstitutionMetricsEndpointImpl(
    private val amsService: AmsService,
    private val institutionMetricsService: InstitutionMetricsService,
) : GetReferenceDataInstitutionMetricsEndpoint() {
    override suspend fun handle(request: GetReferenceDataInstitutionMetricsRequest): Response<ReferenceDataInstitutionMetricsResponse> {
        val headers =
            amsService
                .getReferenceDataRateLimitHeaders(
                    apiKey = request.apiKey,
                    httpRequest = request.httpRequest,
                ).getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }

        val metricName =
            request.nextPageToken?.let {
                PageToken.StringPageToken
                    .parseCatching(it) {
                        return Response.errorResponse(badNextPageToken())
                    }?.string
            }

        return institutionMetricsService
            .findMetricsReferenceData(
                request.metrics,
                reviewable = null,
                request.format,
                request.httpRequest,
                pageRequest =
                    StringPageRequest(
                        entity = metricName,
                        request.pageSize,
                        request.pagingFrom,
                    ),
            ).toChunkedResponse(headers)
    }
}
