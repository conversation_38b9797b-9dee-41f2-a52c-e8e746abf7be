package io.coinmetrics.api.endpoints.reference

import io.coinmetrics.api.Response
import io.coinmetrics.api.badNextPageToken
import io.coinmetrics.api.endpoints.GetReferenceDataExchangeMetricsEndpoint
import io.coinmetrics.api.endpoints.GetReferenceDataExchangeMetricsRequest
import io.coinmetrics.api.model.page.StringPageRequest
import io.coinmetrics.api.models.ReferenceDataExchangeMetricsResponse
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.catalog.metric.impl.ExchangeMetricsService
import io.coinmetrics.api.service.getReferenceDataRateLimitHeaders
import io.coinmetrics.api.utils.paging.PageToken
import io.coinmetrics.api.utils.toChunkedResponse

class GetReferenceDataExchangeMetricsEndpointImpl(
    private val amsService: AmsService,
    private val exchangeMetricsService: ExchangeMetricsService,
) : GetReferenceDataExchangeMetricsEndpoint() {
    override suspend fun handle(request: GetReferenceDataExchangeMetricsRequest): Response<ReferenceDataExchangeMetricsResponse> {
        val headers =
            amsService
                .getReferenceDataRateLimitHeaders(
                    apiKey = request.apiKey,
                    httpRequest = request.httpRequest,
                ).getOrElse { (apiError, rateLimitHeaders) -> return Response.errorResponse(apiError, rateLimitHeaders) }

        val metricName =
            request.nextPageToken?.let {
                PageToken.StringPageToken
                    .parseCatching(it) {
                        return Response.errorResponse(badNextPageToken())
                    }?.string
            }

        return exchangeMetricsService
            .findMetricsReferenceData(
                request.metrics,
                reviewable = null,
                request.format,
                request.httpRequest,
                pageRequest =
                    StringPageRequest(
                        entity = metricName,
                        request.pageSize,
                        request.pagingFrom,
                    ),
            ).toChunkedResponse(headers)
    }
}
