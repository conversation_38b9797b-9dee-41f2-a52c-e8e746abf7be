package io.coinmetrics.api.utils

import com.fasterxml.jackson.databind.DeserializationFeature
import io.coinmetrics.api.modules.main.MainApiConfig.KafkaConfig
import io.coinmetrics.api.modules.main.MainApiConfig.KafkaSourceConfig
import io.coinmetrics.queues.QueueConsumer
import org.slf4j.LoggerFactory

class KafkaDataSource<T>(
    kafkaConfig: KafkaConfig,
    topic: String,
    val index: Int,
    val interestedHeaderNames: Set<String>? = null,
) {
    companion object {
        private val log = LoggerFactory.getLogger(KafkaDataSource::class.java)

        fun <T> create(
            config: KafkaSourceConfig,
            interestedHeaderNames: Set<String>?,
        ): List<KafkaDataSource<T>> =
            config.kafkaServers
                .mapIndexed<KafkaConfig, KafkaDataSource<T>> { index, kafkaConfig ->
                    KafkaDataSource(
                        kafkaConfig = kafkaConfig,
                        topic = config.topicName,
                        index = index,
                        interestedHeaderNames = interestedHeaderNames,
                    )
                }.also {
                    if (isAllSourcesFailed(it)) error("All consumers from ${config.configName} Kafka sources have not been initialized.")
                }

        fun <T> newConsumer(
            kafkaConfig: KafkaConfig,
            topicName: String,
            index: Int,
            interestedHeaderNames: Set<String>? = null,
        ): QueueConsumer<T> {
            val serverUrlParts = kafkaConfig.serverUrl.split(":")
            check(serverUrlParts.size == 2) { "Invalid server url for ${kafkaConfig.configName} kafka: ${kafkaConfig.serverUrl}." }
            return QueueConsumer<T>(
                queueName = topicName,
                host = serverUrlParts[0],
                port = serverUrlParts[1].toInt(),
                groupName = kafkaConfig.groupName,
                clientId = "${kafkaConfig.groupName}_${topicName}_$index",
                autoOffsetReset = QueueConsumer.AutoOffsetReset.LATEST,
                batchSize = kafkaConfig.batchSize,
                maxPartitionFetchBytes = kafkaConfig.maxPartitionFetchBytes,
                interestedHeaderNames = interestedHeaderNames,
                pollDuration = kafkaConfig.pollDuration,
                requestTimeoutMs = kafkaConfig.requestTimeoutMs,
            ).also {
                it.objectMapper.enable(DeserializationFeature.FAIL_ON_NULL_FOR_PRIMITIVES)
            }
        }

        private fun <T> tryInitialize(datasource: KafkaDataSource<T>): Boolean =
            try {
                datasource.consumer
                true
            } catch (e: Exception) {
                log.error("Could not initialize a consumer in Kafka Data Source $datasource.", e)
                false
            }

        private fun <T> isAllSourcesFailed(dataSources: List<KafkaDataSource<T>>) =
            dataSources.isNotEmpty() &&
                dataSources.all {
                    !tryInitialize(it)
                }
    }

    val url: String = kafkaConfig.serverUrl
    var lastMessageTimeMs = 0L
    var initialized = false

    // if one of the consumers cannot be initialized on startup (broker is unavailable) we will try to initialize it letter during consuming
    val consumer: QueueConsumer<T> by lazy { newConsumer<T>(kafkaConfig, topic, index, interestedHeaderNames).also { initialized = true } }

    override fun toString(): String = "$url (index $index)"
}
