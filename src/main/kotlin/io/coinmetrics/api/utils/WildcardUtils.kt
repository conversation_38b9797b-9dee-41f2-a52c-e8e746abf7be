package io.coinmetrics.api.utils

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.toFailure
import io.coinmetrics.api.toSuccess

object WildcardUtils {
    /**
     * @param T must have a correct toString method to allow wildcards to work.
     */
    internal inline fun <T> unwrapWildcards(
        items: Collection<String>,
        paramName: String,
        universeOfItems: Sequence<T>,
        itemParser: (String) -> T,
    ): FunctionResult<ApiError, Pair<List<T>, Boolean>> {
        val patternPrefixAndSuffixes = mutableListOf<Pair<String, String>>()
        val parsedNonPatterns = mutableListOf<T>()
        for (item in items) {
            if (item.contains("*")) {
                val patternParts = item.split("*")
                if (patternParts.size > 2) {
                    return FunctionResult.Failure(
                        ApiError.BadParameter(
                            paramName,
                            "Only one '*' per pattern is supported in the '$paramName' parameter.",
                        ),
                    )
                }
                patternPrefixAndSuffixes.add(patternParts[0] to patternParts[1])
            } else {
                parsedNonPatterns.add(itemParser.invoke(item))
            }
        }
        if (patternPrefixAndSuffixes.isEmpty()) {
            return FunctionResult.Success(Pair(parsedNonPatterns, false))
        }

        val result =
            universeOfItems
                .filter {
                    val str = it.toString()
                    for ((patternPrefix, patternSuffix) in patternPrefixAndSuffixes) {
                        if (str.length >= patternPrefix.length + patternSuffix.length &&
                            str.startsWith(patternPrefix) &&
                            str.endsWith(patternSuffix)
                        ) {
                            return@filter true
                        }
                    }
                    false
                }.toList() + parsedNonPatterns
        return FunctionResult.Success(Pair(result, true))
    }

    fun unwrapWildcards(
        items: Collection<String>,
        paramName: String,
        universeOfItems: Sequence<String>,
    ) = unwrapWildcards(items, paramName, universeOfItems, itemParser = { it })

    fun parseRequestCompositeParameter(
        paramName: String,
        paramValues: Set<String>,
        universeOfItems: Iterable<String>,
    ): FunctionResult<ApiError, Pair<List<Pair<List<String>, Boolean>>, Boolean>> {
        val (specificParamValues, wildcards) = paramValues.partition { '*' !in it }

        specificParamValues.firstOrNull { paramValue -> !universeOfItems.contains(paramValue) }?.let { paramValue ->
            return ApiError.UnsupportedParameterValue(paramName, paramValue).toFailure()
        }

        val unwrappedWildcards =
            unwrapWildcards(
                items = wildcards,
                paramName = paramName,
                universeOfItems = universeOfItems.asSequence(),
            ).getOrElse { error -> return error.toFailure() }

        val result =
            listOf(
                specificParamValues to false,
                unwrappedWildcards,
            ).filter { it.first.isNotEmpty() }

        return (result to unwrappedWildcards.first.isNotEmpty()).toSuccess()
    }

    fun parseSimpleRequestParameter(
        paramName: String,
        paramValues: Set<String>,
        universeOfItems: Set<String>,
    ): FunctionResult<ApiError, Pair<Set<String>, Boolean>> {
        return if (paramValues.contains("*")) {
            // ignore other values if '*' was specified
            universeOfItems to true
        } else {
            paramValues
                .map {
                    if (universeOfItems.contains(it)) {
                        it
                    } else {
                        return ApiError.UnsupportedParameterValue(paramName, it).toFailure()
                    }
                }.toSet() to false
        }.toSuccess()
    }
}
