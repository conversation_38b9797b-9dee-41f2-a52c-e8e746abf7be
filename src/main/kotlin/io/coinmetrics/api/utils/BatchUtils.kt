package io.coinmetrics.api.utils

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.badNextPageToken
import io.coinmetrics.api.models.PagingFrom
import io.coinmetrics.api.toSuccess
import io.coinmetrics.api.utils.paging.PageToken.Companion.logInvalidPageToken
import io.coinmetrics.api.utils.streams.SuspendableStream
import io.coinmetrics.api.utils.streams.asMergeSource
import io.coinmetrics.api.utils.streams.operations.ConcatStateSerializer
import io.coinmetrics.api.utils.streams.operations.MergingSortState
import io.coinmetrics.api.utils.streams.operations.MergingSortStateSerializer
import io.coinmetrics.api.utils.streams.operations.concat
import io.coinmetrics.api.utils.streams.operations.mergeSort
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.cancel
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.currentCoroutineContext
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.buffer
import kotlinx.coroutines.flow.consumeAsFlow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.produceIn
import org.slf4j.Logger
import kotlin.coroutines.ContinuationInterceptor
import kotlin.coroutines.CoroutineContext
import kotlin.math.max

object BatchUtils {
    /**
     * Clients usually request data for a list of entities (for example, a list of assets or markets).<br/>
     * For each entity, we create a dedicated stream (see streamSupplier)<br/>
     * Each stream is identified by a stream ID string.<br/>
     * We usually use an entity ID (for example, btc, eth, coinbase-btc-usd-spot) as a stream ID.<br/>
     * This method sorts entities by their name, creates streams for them, and concatenates the streams producing a new stream.<br/>
     * All source streams have some data source behind like a Postgres database.<br/>
     * The streams are lazy and buffered. It means that if we read (poll or peek) an item from a stream,
     * the stream triggers a database request to fill the internal stream buffer (see bufferSize in BufferedSuspendableStream).<br/>
     * All start positions for streams are encoded in the corresponding pageTokens.<br/>
     *
     * The results are sorted by (stream ID, time). Instead of time, other fields can be used. For example, height.<br/>
     * The sorting of the result stream is stable. It means source streams preserve their initial sorting.<br/>
     * When we read items from the result stream, we read all items from the first stream,
     * then all items from second and so on. The start stream index (0 in our example) is also encoded in the nextPageToken.
     *
     * @param streams sequence of (stream ID, stream-specific data) pairs. Stream-specific data is passed to [streamSupplier] for each stream.
     *        If stream supplier doesn't require an stream-specific data, `Unit` can be used.
     * @param nextPageToken contains pageTokens for all requested streams and the current active stream identifier.
     * @param numberOfStreamsToPrefetch controls whether we need to pre-fill streams' buffers (by executing SQL queries) upon
     * this method call. It helps with performance in some cases (for example, if limitPerStream is set).
     * @param streamSupplier creates a new SuspendableStream for a provided streamId and state. Can not have request parameter checks inside.
     * @param streamIdsAreResolvedDynamically must be set to true if a client uses wildcards.
     * See [ConcatStateSerializer.deserialize] for details.
     */
    suspend fun <T, S, D> sortIdsAndConcatStreams(
        streams: Sequence<Pair<String, D>>,
        pagingFrom: PagingFrom,
        nextPageToken: String?,
        initialStreamStateParser: (String) -> S,
        numberOfStreamsToPrefetch: Int = 0,
        limitPerStream: Int? = null,
        streamSupplier: (suspend (streamId: String, initialState: S?, streamSpecificData: D) -> SuspendableStream<T, *>),
        streamIdsAreResolvedDynamically: Boolean,
        httpRequestCoroutineContext: CoroutineContext,
        logger: Logger,
    ): FunctionResult<ApiError, SuspendableStream<T, SuspendableStream.State>> {
        val streamDeque = streams.toCollection(ArrayDeque())
        if (pagingFrom == PagingFrom.START) {
            streamDeque.sortBy { it.first }
        } else {
            streamDeque.sortByDescending { it.first }
        }

        val state =
            when (
                val result =
                    if (streamIdsAreResolvedDynamically) {
                        ConcatStateSerializer.deserialize(nextPageToken, streamDeque.map { it.first }, pagingFrom)
                    } else {
                        ConcatStateSerializer.deserialize(nextPageToken)
                    }
            ) {
                is FunctionResult.Success -> result.value
                is FunctionResult.Failure -> return FunctionResult.Failure(result.value)
            }

        val idInitialState =
            state.activeStreamState?.let {
                try {
                    initialStreamStateParser.invoke(it)
                } catch (e: Exception) {
                    logger.logInvalidPageToken(it, e)
                    return FunctionResult.Failure(badNextPageToken())
                }
            }

        streamDeque.subList(0, state.activeStreamIndex).clear()

        if (streamDeque.isEmpty()) {
            return SuspendableStream.empty<T, SuspendableStream.State>().toSuccess()
        }

        val streams =
            flow {
                for (index in 0..<streamDeque.size) {
                    val (streamId, streamSpecificData) = streamDeque.removeFirst()
                    val (idState, idEmitted) =
                        if (index == 0) {
                            idInitialState to state.activeStreamEmitted
                        } else {
                            null to 0L
                        }
                    val stream =
                        streamSupplier.invoke(streamId, idState, streamSpecificData).let { stream ->
                            if (limitPerStream == null) {
                                stream
                            } else {
                                stream.limit(maxOf(limitPerStream - (idEmitted ?: 0), 0L).toInt())
                            }
                        }
                    emit(stream)
                }
            }

        val streamsWithPrefetching =
            if (numberOfStreamsToPrefetch > 0) {
                streams.enabledPrefetching(
                    numberOfStreamsToPrefetch = numberOfStreamsToPrefetch,
                    coroutineContext = httpRequestCoroutineContext,
                    coroutineDispatcher = currentCoroutineContext()[ContinuationInterceptor] as CoroutineDispatcher,
                )
            } else {
                streams
            }

        return FunctionResult.Success(
            streamsWithPrefetching.concat(
                // custom coroutineContext & coroutineDispatcher are needed to allow the coroutines created by streams
                // run during the "send response" phase of request processing
                coroutineContext = httpRequestCoroutineContext,
                coroutineDispatcher = currentCoroutineContext()[ContinuationInterceptor] as CoroutineDispatcher,
                firstStreamIndex = state.activeStreamIndex,
                alreadyEmitted = if (limitPerStream != null) state.activeStreamEmitted else null,
            ),
        )
    }

    private fun <T, S : SuspendableStream.State> Flow<SuspendableStream<T, S>>.enabledPrefetching(
        numberOfStreamsToPrefetch: Int,
        coroutineContext: CoroutineContext,
        coroutineDispatcher: CoroutineDispatcher,
    ): Flow<SuspendableStream<T, S>> {
        // this scope is also cancelled automatically when the HTTP request finishes with an exception
        val scope = CoroutineScope(coroutineContext + coroutineDispatcher + SupervisorJob(coroutineContext[Job]))
        return map {
            scope.async {
                it.prefetch()
                it
            }
        }.buffer(numberOfStreamsToPrefetch)
            .produceIn(scope)
            .consumeAsFlow()
            .map { it.await() }
            .onCompletion { scope.cancel() }
    }

    /**
     * Merges all streams for the requested entities using a merge sort algorithm.<br/>
     * The results are sorted by mergeKeyExtractor's return values. Usually, it extracts "time" values from items.<br/>
     * So the resulting stream will be sorted by (time, streamId).<br/>
     * Sorting is stable. It means source streams preserve their original sorting, and they must be pre-sorted by time.
     */
    suspend fun <T, S : SuspendableStream.State> mergeSortStreams(
        streamIds: Array<String>,
        pagingFrom: PagingFrom,
        nextPageToken: String?,
        mergeKeyExtractor: (T) -> Comparable<*>,
        limitPerStream: Int? = null,
        streamSupplier: suspend (streamId: String, pageToken: String?) -> FunctionResult<ApiError, SuspendableStream<T, S>>,
    ): FunctionResult<ApiError, SuspendableStream<T, MergingSortState>> {
        // sorting is needed to make the nextPageToken serialization stable and proper ordering by entityId
        streamIds.sort()
        val state =
            when (val result = MergingSortStateSerializer.deserialize(nextPageToken, streamIds.size)) {
                is FunctionResult.Success -> result.value
                is FunctionResult.Failure -> return FunctionResult.Failure(result.value)
            }

        // creates all streams in parallel
        val mergeSources =
            coroutineScope {
                streamIds
                    .mapIndexed { index, id ->
                        val idState = state.states[index]
                        val emitted = idState?.second
                        async {
                            streamSupplier.invoke(id, idState?.first).let { stream ->
                                if (limitPerStream == null) {
                                    stream
                                } else {
                                    stream.map { it.limit(max(limitPerStream - (emitted ?: 0), 0L).toInt()) }
                                }
                            }
                        }
                    }.awaitAll()
            }.map { result ->
                when (result) {
                    is FunctionResult.Success -> result.value.asMergeSource(mergeKeyExtractor)
                    is FunctionResult.Failure -> return FunctionResult.Failure(result.value)
                }
            }

        val emittedByStream =
            limitPerStream?.let { _ ->
                LongArray(streamIds.size) { index ->
                    state.states[index]?.second ?: 0
                }
            }

        return FunctionResult.Success(
            mergeSources.mergeSort(
                pagingFrom = pagingFrom,
                emittedByStream = emittedByStream,
            ),
        )
    }
}
