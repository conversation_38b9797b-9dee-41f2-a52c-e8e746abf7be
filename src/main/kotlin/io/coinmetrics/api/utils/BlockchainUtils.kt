package io.coinmetrics.api.utils

import java.util.regex.Pattern

object BlockchainUtils {
    private val hexPattern = Pattern.compile("(?:[0-9a-f][0-9a-f])+")

    private val hexEncodedTransactions = hashSetOf("btc", "eth")

    private val btcTransactionIdPattern = Pattern.compile("[a-fA-F0-9]{64}")

    fun normalizeTransactionHash(
        asset: String,
        hash: String,
    ): String = normalize(asset, hexEncodedTransactions, hash)

    private fun normalize(
        asset: String,
        supportedSet: Set<String>,
        hash: String,
    ): String =
        if (supportedSet.contains(asset)) {
            (
                if (hash.startsWith("0x")) {
                    hash.substring(2)
                } else {
                    hash
                }
            ).lowercase()
        } else {
            hash
        }

    /**
     * @param str normalized string
     */
    fun isHexEncodedString(str: String): Boolean = hexPattern.matcher(str).matches()

    fun isValidBtcTransactionId(str: String): Boolean = btcTransactionIdPattern.matcher(str).matches()
}
