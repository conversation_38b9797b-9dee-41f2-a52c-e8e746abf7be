package io.coinmetrics.api.utils

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.toFailure
import io.coinmetrics.api.toSuccess
import java.time.ZoneId

object CandleUtils {
    fun parseFrequency(
        requestedFrequency: String,
        requestedTimezone: String,
        isCommunity: Boolean,
    ): FunctionResult<ApiError, Pair<String, TimeUtils.NormalizedFrequencyOffset>> {
        val (frequency, offset) = TimeUtils.parseFrequency(requestedFrequency)
        if (offset != null && frequency != "1d") {
            return ApiError.UnsupportedParameterValue("frequency", requestedFrequency).toFailure()
        }
        val normalizedOffset =
            TimeUtils.normalizeFrequencyOffset(offset)?.adjustForTimezone(frequency, requestedTimezone)
                ?: return ApiError.UnsupportedParameterValue("frequency", requestedFrequency).toFailure()
        if (normalizedOffset.minutes != 0) {
            return ApiError.UnsupportedParameterValue("frequency", requestedFrequency).toFailure()
        }
        // prohibit using custom offset for community key for now until we verify that there is no performance implications.
        if (offset != null && isCommunity) {
            return ApiError
                .ForbiddenWithMessage("Requested frequency '$requestedFrequency' is not available with supplied credentials.")
                .toFailure()
        }
        return (frequency to normalizedOffset).toSuccess()
    }

    private fun TimeUtils.NormalizedFrequencyOffset.adjustForTimezone(
        frequency: String,
        timezone: String,
    ): TimeUtils.NormalizedFrequencyOffset =
        if (this.default && frequency == "1d" && timezone != "UTC") {
            TimeUtils.NormalizedFrequencyOffset(0, 0, timezone)
        } else {
            this
        }

    fun RangeQuery.TimeRangeQuery.adjustForFrequency(
        frequencyOffset: TimeUtils.NormalizedFrequencyOffset,
        timezone: String,
    ): RangeQuery.TimeRangeQuery {
        if (frequencyOffset.default) return this
        // We are going to aggregate 1h candles to compute 1d, so we need additional 23 hours in the end of the initial interval to aggregate all 1h candles for the last 1d candle
        // 1. 23 hours is sufficient amount to cover all cases.
        //    - E.g. for query frequency=1d-16:00&end_time=2021-11-05T16:00:00 we will need to aggregate candles till 2021-11-06T15:00:00.
        //    - Excessive 1h candles that were not fully aggregated into 1d are ignored by sql query (e.g. if end_time=2021-11-05T18:00:00)
        // 2. local time is used to avoid issues with DST.
        // 3. inclusive/exclusive is also handled since the last needed 1h candle will not be included if endInclusive == false
        val zoneId = ZoneId.of(frequencyOffset.forcedTimeZone ?: timezone)
        val newEndKey =
            endKey
                .atZone(zoneId)
                .toLocalDateTime()
                .plusHours(23)
                .atZone(zoneId)
                .toInstant()
        return RangeQuery.TimeRangeQuery(
            startKey,
            startInclusive,
            newEndKey,
            endInclusive,
            pagingFrom,
        )
    }
}
