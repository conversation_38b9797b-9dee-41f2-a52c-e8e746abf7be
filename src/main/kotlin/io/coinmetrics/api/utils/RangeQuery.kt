package io.coinmetrics.api.utils

import io.coinmetrics.api.models.PagingFrom
import io.coinmetrics.api.utils.paging.PageToken
import java.math.BigInteger
import java.time.Instant

sealed class RangeQuery<ME : RangeQuery<ME, P>, P : PageToken>(
    val startInclusive: <PERSON><PERSON><PERSON>,
    val endInclusive: Boolean,
) {
    abstract fun withPageToken(pageToken: P?): ME

    class TimeRangeQuery(
        val startKey: Instant,
        startInclusive: <PERSON>olean,
        val endKey: Instant,
        endInclusive: <PERSON>olean,
        val pagingFrom: PagingFrom,
    ) : RangeQuery<TimeRangeQuery, PageToken.TimePageToken>(startInclusive, endInclusive) {
        override fun withPageToken(pageToken: PageToken.TimePageToken?): TimeRangeQuery {
            if (pageToken == null) return this
            return if (pagingFrom == PagingFrom.START) {
                TimeRangeQuery(
                    pageToken.time,
                    false,
                    endKey,
                    endInclusive,
                    pagingFrom,
                )
            } else {
                TimeRangeQuery(
                    startKey,
                    startInclusive,
                    pageToken.time,
                    false,
                    pagingFrom,
                )
            }
        }
    }

    class TimeAndStringRangeQuery(
        override val startKey1: Instant,
        override val startKey2: String?,
        startInclusive: Boolean,
        override val endKey1: Instant,
        override val endKey2: String?,
        endInclusive: Boolean,
        override val pagingFrom: PagingFrom,
    ) : RangeQuery<TimeAndStringRangeQuery, PageToken.TimeAndStringPageToken>(
            startInclusive,
            endInclusive,
        ),
        TwoComponentsRangeQuery<Instant, String> {
        override fun withPageToken(pageToken: PageToken.TimeAndStringPageToken?): TimeAndStringRangeQuery {
            if (pageToken == null) return this
            return if (pagingFrom == PagingFrom.START) {
                TimeAndStringRangeQuery(
                    pageToken.time,
                    pageToken.string,
                    false,
                    endKey1,
                    endKey2,
                    endInclusive,
                    pagingFrom,
                )
            } else {
                TimeAndStringRangeQuery(
                    startKey1,
                    startKey2,
                    startInclusive,
                    pageToken.time,
                    pageToken.string,
                    false,
                    pagingFrom,
                )
            }
        }
    }

    class TimeAndBigIntegerRangeQuery(
        override val startKey1: Instant,
        override val startKey2: BigInteger?,
        startInclusive: Boolean,
        override val endKey1: Instant,
        override val endKey2: BigInteger?,
        endInclusive: Boolean,
        override val pagingFrom: PagingFrom,
    ) : RangeQuery<TimeAndBigIntegerRangeQuery, PageToken.TimeAndBigIntegerPageToken>(
            startInclusive,
            endInclusive,
        ),
        TwoComponentsRangeQuery<Instant, BigInteger> {
        override fun withPageToken(pageToken: PageToken.TimeAndBigIntegerPageToken?): TimeAndBigIntegerRangeQuery {
            if (pageToken == null) return this
            return if (pagingFrom == PagingFrom.START) {
                TimeAndBigIntegerRangeQuery(
                    pageToken.time,
                    pageToken.value,
                    false,
                    endKey1,
                    endKey2,
                    endInclusive,
                    pagingFrom,
                )
            } else {
                TimeAndBigIntegerRangeQuery(
                    startKey1,
                    startKey2,
                    startInclusive,
                    pageToken.time,
                    pageToken.value,
                    false,
                    pagingFrom,
                )
            }
        }
    }

    class TimeAndByteArrayRangeQuery(
        override val startKey1: Instant,
        override val startKey2: ByteArray?,
        startInclusive: Boolean,
        override val endKey1: Instant,
        override val endKey2: ByteArray?,
        endInclusive: Boolean,
        override val pagingFrom: PagingFrom,
    ) : RangeQuery<TimeAndByteArrayRangeQuery, PageToken.TimeAndByteArrayPageToken>(
            startInclusive,
            endInclusive,
        ),
        TwoComponentsRangeQuery<Instant, ByteArray> {
        override fun withPageToken(pageToken: PageToken.TimeAndByteArrayPageToken?): TimeAndByteArrayRangeQuery {
            if (pageToken == null) return this
            return if (pagingFrom == PagingFrom.START) {
                TimeAndByteArrayRangeQuery(
                    pageToken.time,
                    pageToken.value,
                    false,
                    endKey1,
                    endKey2,
                    endInclusive,
                    pagingFrom,
                )
            } else {
                TimeAndByteArrayRangeQuery(
                    startKey1,
                    startKey2,
                    startInclusive,
                    pageToken.time,
                    pageToken.value,
                    false,
                    pagingFrom,
                )
            }
        }
    }

    interface TwoComponentsRangeQuery<T1 : Comparable<T1>, T2> {
        val startKey1: T1
        val startKey2: T2?
        val startInclusive: Boolean
        val endKey1: T1
        val endKey2: T2?
        val endInclusive: Boolean
        val pagingFrom: PagingFrom
    }

    class BigIntegerRangeQuery(
        val startKey: BigInteger?,
        startInclusive: Boolean,
        val endKey: BigInteger?,
        endInclusive: Boolean,
        val pagingFrom: PagingFrom,
    ) : RangeQuery<BigIntegerRangeQuery, PageToken.BigIntegerPageToken>(startInclusive, endInclusive) {
        override fun withPageToken(pageToken: PageToken.BigIntegerPageToken?): BigIntegerRangeQuery {
            if (pageToken == null) return this
            return if (pagingFrom == PagingFrom.START) {
                BigIntegerRangeQuery(
                    pageToken.value,
                    false,
                    endKey,
                    endInclusive,
                    pagingFrom,
                )
            } else {
                BigIntegerRangeQuery(
                    startKey,
                    startInclusive,
                    pageToken.value,
                    false,
                    pagingFrom,
                )
            }
        }
    }

    class BigIntegerAndStringRangeQuery(
        val startKey1: BigInteger?,
        val startKey2: String?,
        startInclusive: Boolean,
        val endKey1: BigInteger?,
        val endKey2: String?,
        endInclusive: Boolean,
        val pagingFrom: PagingFrom,
        val strTransformer: (String) -> String = { it },
    ) : RangeQuery<BigIntegerAndStringRangeQuery, PageToken.BigIntegerAndStringPageToken>(
            startInclusive,
            endInclusive,
        ) {
        override fun withPageToken(pageToken: PageToken.BigIntegerAndStringPageToken?): BigIntegerAndStringRangeQuery {
            if (pageToken == null) return this
            return if (pagingFrom == PagingFrom.START) {
                BigIntegerAndStringRangeQuery(
                    pageToken.bigInteger,
                    pageToken.str,
                    false,
                    endKey1,
                    endKey2,
                    endInclusive,
                    pagingFrom,
                    pageToken.strTransformer,
                )
            } else {
                BigIntegerAndStringRangeQuery(
                    startKey1,
                    startKey2,
                    startInclusive,
                    pageToken.bigInteger,
                    pageToken.str,
                    false,
                    pagingFrom,
                    pageToken.strTransformer,
                )
            }
        }
    }
}
