package io.coinmetrics.api.utils

import java.io.Closeable
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicInteger

/**
 * Keeps key contexts while they are not closed.
 * If all callers close their contexts for a key, the corresponding key context will be evicted from memory.
 * It's intended to be used from multiple threads.
 *
 * This class is needed when you develop something that requires you to keep some state in memory ONLY for the duration
 * of parallel requests.
 * For example, implementing active request limits per API key, implementing request queueing per API key.
 * Without this class, it's very easy to get memory leaks while implementing such things from scratch.
 */
class ReferenceCountedKeyContexts<K, V>(
    private val onKeyContextRemoved: (K, V) -> Unit = { _, _ -> },
) {
    private val contexts = ConcurrentHashMap<K, KeyContext>()
    val size: Int
        get() = contexts.size

    /**
     * All callers must release a reference to the returned KeyContext by calling its "close" method.
     *
     * WARNING: Don't forget to call "close" upon your coroutine cancellation to avoid memory leaks.
     * It can be done in three ways:
     * 1) Automatically via use: getOrCreateKeyContextAndIncreaseReferenceCount(...).use { ... }
     * 2) Automatically via finally: try { val keyContext = getOrCreateKeyContextAndIncreaseReferenceCount(..) } finally { keyContext.close() }
     * 3) Manually: currentCoroutineContext().job.invokeOnCompletion { keyContext.close() }
     */
    fun getOrCreateKeyContextAndIncreaseReferenceCount(
        key: K,
        initialKeyContextValueFactory: (K) -> V,
    ): KeyContext =
        contexts.compute(key) { keyLocal, currentContext ->
            if (currentContext == null || !currentContext.increaseRefCntIfNotEvicted()) {
                KeyContext(
                    key = keyLocal,
                    value = initialKeyContextValueFactory.invoke(key),
                )
            } else {
                currentContext
            }
        } ?: error("KeyContext can't be null.")

    operator fun get(key: K) = contexts[key]

    inner class KeyContext(
        val key: K,
        val value: V,
    ) : Closeable {
        private val refCnt = AtomicInteger(1)

        /**
         * Shouldn't be used externally.
         * @return true if reference count was increased. false if the context was already closed.
         */
        internal fun increaseRefCntIfNotEvicted(): Boolean {
            do {
                val refCntValue = refCnt.get()
                if (refCntValue == 0) return false
            } while (!refCnt.compareAndSet(refCntValue, refCntValue + 1))
            return true
        }

        fun numberOfReferences() = refCnt.get()

        /**
         * Releases one reference to this context. If all references are released, the context gets evicted from memory.
         */
        override fun close() {
            while (true) {
                val refCntValue = refCnt.get()
                if (refCntValue == 0) break
                val newRefCntValue = refCntValue - 1
                if (refCnt.compareAndSet(refCntValue, newRefCntValue)) {
                    if (newRefCntValue == 0) {
                        contexts.remove(key, this@KeyContext)
                        onKeyContextRemoved.invoke(key, value)
                    }
                    break
                }
            }
        }
    }
}
