package io.coinmetrics.api.utils.paging

import io.coinmetrics.api.models.PagingFrom
import kotlin.math.max
import kotlin.math.min

object ListPagingUtils {
    val pageTokenValueResolver: (Int?, Int, Int) -> PageToken.IntPageToken = { state, bufferSize, lastIndex ->
        val currentState = state?.let { if (it < 0) 0 else it } ?: 0
        PageToken.IntPageToken(min(currentState + bufferSize, lastIndex))
    }

    fun <T> pageLoader(
        data: List<T>,
        pagingFrom: PagingFrom = PagingFrom.START,
    ): suspend (PageToken.IntPageToken?, Int) -> List<T> =
        { state, bufferSize ->
            val startIndex =
                state?.value?.let {
                    when {
                        it < 0 -> 0
                        it > data.size -> data.size
                        else -> it
                    }
                }
            when ((startIndex == null) to pagingFrom) {
                true to PagingFrom.START -> data.subList(0, min(bufferSize, data.size))
                true to PagingFrom.END -> data.subList(max(0, data.size - bufferSize), data.size)
                false to PagingFrom.START -> data.subList(startIndex!!, min(startIndex + bufferSize, data.size))
                false to PagingFrom.END -> data.subList(max(0, data.size - startIndex!! - bufferSize), max(0, data.size - startIndex))
                else -> emptyList() // never happens. pair of booleans has only four combinations.
            }
        }
}
