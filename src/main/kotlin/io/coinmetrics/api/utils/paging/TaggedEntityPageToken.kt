package io.coinmetrics.api.utils.paging

import io.coinmetrics.api.model.tagging.TaggedEntity
import org.slf4j.Logger
import java.time.Instant

class TaggedEntityPageToken(
    val key: TaggedEntity.Key,
) : PageToken() {
    companion object {
        fun parse(pageToken: String): TaggedEntityPageToken {
            val values = PagingUtils.parsePageTokenInternal(pageToken)
            return TaggedEntityPageToken(
                TaggedEntity.Key(
                    entity = values[0],
                    tag = values[1],
                    location = values[2],
                    startTime = Instant.parse(values[3]),
                    startedBy = values[4],
                ),
            )
        }

        inline fun parseCatching(
            pageToken: String,
            logger: Logger? = null,
            exceptionHandler: (Exception) -> Unit,
        ): TaggedEntityPageToken? =
            try {
                parse(pageToken)
            } catch (e: Exception) {
                logger?.logInvalidPageToken(pageToken, e)
                exceptionHandler(e)
                null
            }
    }

    override fun serialize(): String =
        PagingUtils.createPageToken(key.entity, key.tag, key.location, key.startTime.toString(), key.startedBy)
}
