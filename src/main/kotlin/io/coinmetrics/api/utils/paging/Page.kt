package io.coinmetrics.api.utils.paging

import io.coinmetrics.api.NextPageInfo
import io.coinmetrics.api.models.PagingFrom
import io.coinmetrics.api.utils.streams.SuspendableStream
import io.coinmetrics.httpserver.HttpRequest
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.onCompletion

data class Page<out T>(
    val items: List<T>,
    val nextPageToken: String?,
) {
    inline fun <R> map(func: (T) -> R): Page<R> = Page(items.map(func), nextPageToken)
}

@Deprecated("Use getPageFlow.")
suspend fun <T, S : SuspendableStream.State> SuspendableStream<T, S>.getPage(
    pageSize: Int,
    pagingFromStart: Boolean,
): Page<T> =
    use {
        val items = ArrayList<T>()
        repeat(pageSize) {
            val item =
                poll() ?: run {
                    if (!pagingFromStart) items.reverse()
                    return Page(items, null)
                }
            items.add(item)
        }
        if (!pagingFromStart) items.reverse()
        val nextPageToken =
            if (peek() == null) {
                null
            } else {
                state()?.serialize()
            }
        return Page(items, nextPageToken)
    }

/**
 * @return a Flow<[T] or [NextPageInfo]>
 */
fun <T, S : SuspendableStream.State> SuspendableStream<T, S>.getPageFlow(
    httpRequest: HttpRequest,
    pageSize: Int,
    pagingFrom: PagingFrom,
): Flow<Any> {
    val flow =
        if (pagingFrom == PagingFrom.START) {
            flow {
                repeat(pageSize) {
                    val item = poll() ?: return@flow
                    emit(item)
                }

                if (peek() != null) {
                    val nextPageToken = state()?.serialize()
                    if (nextPageToken != null) {
                        val nextPageUrl = PagingUtils.createNextPageUrl(httpRequest, nextPageToken)
                        emit(NextPageInfo(nextPageUrl, nextPageToken))
                    }
                }
            }
        } else {
            flow {
                val items = ArrayList<T>()
                repeat(pageSize) {
                    val item = poll()
                    if (item == null) {
                        items.asReversed().forEach { emit(it as Any) }
                        return@flow
                    }
                    items.add(item)
                }
                items.asReversed().forEach { emit(it as Any) }
                if (peek() != null) {
                    val nextPageToken = state()?.serialize()
                    if (nextPageToken != null) {
                        val nextPageUrl = PagingUtils.createNextPageUrl(httpRequest, nextPageToken)
                        emit(NextPageInfo(nextPageUrl, nextPageToken))
                    }
                }
            }
        }
    return flow.onCompletion { close() }
}

/**
 * @return the page grouped by the key.
 *
 * Assuming that stream is already sorted by that key.
 * Example:
 *      stream: [[1, 2], [1, 3], [2, 4], [3, 5]]
 *      pageSize: 2
 *      keySelector: { it.first }
 *      result: [[1, [2, 3]], [2, [4]]]], nextPageToken: 3
 */
suspend fun <T, S : SuspendableStream.State, P> SuspendableStream<T, S>.getPageGrouped(
    pageSize: Int,
    pagingFrom: PagingFrom,
    keyExtractor: (T) -> P,
): Page<Map.Entry<P, List<T>>> =
    use {
        val items = LinkedHashMap<P, ArrayList<T>>()
        var hasNext = false
        do {
            val item = poll()
            if (item != null) {
                val key = keyExtractor(item)
                items.getOrPut(key) { ArrayList() }.add(item)

                val nextItem = peek()
                if (nextItem != null) {
                    val nextKey = keyExtractor(nextItem)
                    if (items.size == pageSize && nextKey !in items) {
                        // if we have reached the page size and this key is going to increase the map size, we need to return the page
                        hasNext = true
                        break
                    }
                }
            }
        } while (item != null)
        val nextPageToken = if (hasNext) state()?.serialize() else null
        val result =
            if (pagingFrom == PagingFrom.START) {
                items.entries
            } else {
                items.entries.reversed()
            }
        return Page(result.toList(), nextPageToken)
    }
