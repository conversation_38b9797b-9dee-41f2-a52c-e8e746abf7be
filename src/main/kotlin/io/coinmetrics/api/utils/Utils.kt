package io.coinmetrics.api.utils

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import io.coinmetrics.api.ApiError
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.ams.CheckLimitsResult
import io.coinmetrics.api.ams.CheckResult
import io.coinmetrics.api.ams.DiscoveryResult
import io.coinmetrics.api.models.ErrorObject
import io.coinmetrics.api.models.ErrorResponse
import io.coinmetrics.api.models.PagingFrom
import io.coinmetrics.api.toFailure
import io.coinmetrics.api.toSuccess
import java.net.URLEncoder
import java.sql.ResultSet
import java.text.DecimalFormat
import java.text.DecimalFormatSymbols
import java.time.Duration
import java.time.Instant
import kotlin.math.ceil
import kotlin.math.min

object Utils {
    private val decimalSymbols = DecimalFormatSymbols().also { it.decimalSeparator = '.' }

    private val doubleFormat = DecimalFormat("0.##", decimalSymbols)

    fun formatDouble(value: Double): String = doubleFormat.format(value)

    fun encodeURIComponent(s: String) =
        URLEncoder
            .encode(s, Charsets.UTF_8)
            .replace("+", "%20")
            .replace("%2C", ",")

    /**
     * @param normalizedFrequency a frequency without an offset. For example, 1d.
     */
    fun frequencyToDuration(normalizedFrequency: String): FunctionResult<ApiError, Duration> {
        val value = normalizedFrequency.dropLast(1)
        val unit = normalizedFrequency.last().takeUnless { value.toLongOrNull() == null }
        return when (unit) {
            'd' -> Duration.ofDays(value.toLong()).toSuccess()
            's' -> Duration.ofSeconds(value.toLong()).toSuccess()
            'm' -> Duration.ofMinutes(value.toLong()).toSuccess()
            'h' -> Duration.ofHours(value.toLong()).toSuccess()
            else -> ApiError.BadParameter("frequency", "Value '$normalizedFrequency' is not supported.").toFailure()
        }
    }

    /**
     * Try to derive limitPerEntity from the requested time range and frequency.
     * @param limitPerEntity the limitPerEntity initially requested by a client. The effective one returned by this method may be smaller.
     * @param normalizedFrequency a frequency without an offset. For example, 1d. Also, you can pass a downsampling granularity here.
     * @return Pair<forcedPagingFrom, effectiveLimitPerEntity?>
     */
    fun getEffectiveLimitPerEntity(
        limitPerEntity: Int?,
        pageSize: Int,
        entitiesCount: Int,
        normalizedFrequency: Duration,
        startTime: Instant,
        startTimeInclusive: Boolean,
        endTime: Instant,
        endTimeInclusive: Boolean,
    ): Pair<PagingFrom?, Int?> {
        val howManyTimesFrequencyMayFitInterval =
            run {
                val frequencyDuration = normalizedFrequency.toNanos()
                val effectiveStartTime =
                    if (startTimeInclusive) {
                        startTime
                    } else {
                        startTime + Duration.ofNanos(1)
                    }
                val effectiveEndTimeExclusive =
                    if (endTimeInclusive) {
                        endTime + Duration.ofNanos(1)
                    } else {
                        endTime
                    }
                val intervalDuration =
                    Duration
                        .between(
                            maxOf(effectiveStartTime, Instant.EPOCH),
                            minOf(effectiveEndTimeExclusive, Instant.now()),
                        ).toNanos()
                maxOf(1.0, ceil(intervalDuration.toDouble() / frequencyDuration)).toLong()
            }

        val maxRows = howManyTimesFrequencyMayFitInterval * entitiesCount
        val forcedPagingFromStart = maxRows <= pageSize
        // Override limitPerEntity only if all rows fit a single page.
        // If we always override it, the nextPageToken will be switched to more expensive format (to support SuspendableStream.limit()) for no reason.
        val effectiveLimitPerEntity =
            if (forcedPagingFromStart) {
                if (limitPerEntity == null) {
                    howManyTimesFrequencyMayFitInterval.toInt()
                } else {
                    minOf(howManyTimesFrequencyMayFitInterval.toInt(), limitPerEntity)
                }
            } else {
                limitPerEntity
            }
        return PagingFrom.START.takeIf { forcedPagingFromStart } to effectiveLimitPerEntity
    }

    /**
     * Calculates variables to control the prefetch algorithm while working with multiple streams.
     * @return Pair<numberOfStreamsToPrefetch, streamBufferSize>
     */
    fun getFetchProperties(
        limitPerEntity: Int?,
        entitiesCount: Int,
        pageSize: Int,
    ): Pair<Int, Int> =
        if (limitPerEntity != null) {
            // good case 1: all results are going to fit into a single page
            // we never need a stream buffer size greater than pageSize + 1
            val streamBufferSize = minOf(limitPerEntity, pageSize + 1)
            if ((limitPerEntity.toLong() * entitiesCount) <= pageSize) {
                Pair(pageSize, streamBufferSize)
            } else {
                val streamsPerPage = ceil(pageSize.toDouble() / limitPerEntity).toInt()
                if (streamsPerPage > 1) {
                    // good case 2: we know how many items will be on every page most likely and prefetching makes sense
                    if (pageSize % limitPerEntity == 0) {
                        // limitPerEntity was perfectly aligned with pageSize.
                        // We need to prefetch one more stream to generate nextPageToken faster.
                        Pair(streamsPerPage + 1, streamBufferSize)
                    } else {
                        // steamsPerPage already contains +1 due to the rounding we did before
                        Pair(streamsPerPage, streamBufferSize)
                    }
                } else {
                    // we will have <= one stream per page so prefetching is not needed
                    // but a smaller buffer size will help with perf. as well
                    Pair(0, streamBufferSize)
                }
            }
        } else {
            // we can't estimate the number of items for an entity
            // prefetching is disabled
            Pair(0, pageSize + 1)
        }

    inline fun failedAmsResultToApiError(
        failedDiscoveryResult: DiscoveryResult.Failed,
        displayParameterNameResolver: (String) -> String?,
    ): ApiError =
        when (failedDiscoveryResult.status) {
            400 -> {
                if (failedDiscoveryResult is DiscoveryResult.BadParameter) {
                    val parameterName = failedDiscoveryResult.parameterName
                    if (parameterName == "*") {
                        ApiError.BadParameters(failedDiscoveryResult.message)
                    } else {
                        val displayParameterName = displayParameterNameResolver.invoke(parameterName)
                        if (displayParameterName == null) {
                            ApiError.OperationFailed
                        } else {
                            ApiError.BadParameter(displayParameterName, failedDiscoveryResult.message)
                        }
                    }
                } else {
                    ApiError.OperationFailed
                }
            }

            401 -> ApiError.WrongApiKey
            403 -> ApiError.Forbidden
            else -> ApiError.OperationFailed
        }

    inline fun failedAmsResultToApiError(
        failedCheckResult: CheckResult.Failed,
        displayParameterNameResolver: (String) -> String?,
    ): ApiError =
        when (failedCheckResult.status) {
            400 -> {
                if (failedCheckResult is CheckResult.BadParameter) {
                    val parameterName = failedCheckResult.parameterName
                    if (parameterName == "*") {
                        ApiError.BadParameters(failedCheckResult.message)
                    } else {
                        val displayParameterName = displayParameterNameResolver.invoke(parameterName)
                        if (displayParameterName == null) {
                            ApiError.OperationFailed
                        } else {
                            ApiError.BadParameter(displayParameterName, failedCheckResult.message)
                        }
                    }
                } else {
                    ApiError.OperationFailed
                }
            }

            401 -> ApiError.WrongApiKey
            403 -> ApiError.Forbidden
            else -> ApiError.OperationFailed
        }

    fun failedAmsResultToApiError(failedCheckResult: CheckLimitsResult.Failed): ApiError =
        when (failedCheckResult.status) {
            401 -> ApiError.WrongApiKey
            403 -> ApiError.Forbidden
            429 -> ApiError.TooManyRequests
            else -> ApiError.OperationFailed
        }

    fun getStatusFilter(
        requestStatus: String,
        fieldName: String = "status",
    ): String =
        if (requestStatus == "all") {
            ""
        } else {
            "AND $fieldName='${requestStatus.uppercase()}'"
        }

    inline fun <reified T> extractArray(
        rs: ResultSet,
        columnLabel: String,
    ): Array<T> =
        rs.getArray(columnLabel)?.let {
            @Suppress("UNCHECKED_CAST")
            (it.array as Array<T>)
        } ?: emptyArray()

    fun ResultSet.getBigDecimalOrNull(column: String) = extractNullableValue { getBigDecimal(column) }

    inline fun <T> ResultSet.getNullable(f: ResultSet.() -> T): T? {
        val value = f()
        return if (wasNull()) {
            null
        } else {
            value
        }
    }

    /**
     * Returns a view of a list containing first [n] elements.
     * The returned list is backed by this list, so non-structural changes in the returned list are reflected in this list, and vice-versa
     *
     * @throws IllegalArgumentException if [n] is negative.
     */
    fun <T> List<T>.takeView(n: Int): List<T> {
        require(n >= 0) { "Requested element count $n is less than zero." }
        return subList(0, min(size, n))
    }

    private fun <T> ResultSet.extractNullableValue(extractor: (ResultSet) -> T?): T? = extractor(this)?.takeIf { !wasNull() }
}

inline fun <reified T> ObjectMapper.convert(obj: Any): T {
    val valueAsString = this.writeValueAsString(obj)
    return this.readValue(valueAsString)
}

fun ByteArray.indexOf(
    sequence: ByteArray,
    startFrom: Int = 0,
): Int {
    if (sequence.isEmpty()) return 0
    if (sequence.size > size) return -1
    var matchOffset = 0
    var start = startFrom
    var offset = startFrom
    while (offset < size) {
        if (this[offset] == sequence[matchOffset]) {
            if (matchOffset++ == 0) start = offset
            if (matchOffset == sequence.size) return start
        } else {
            matchOffset = 0
        }
        offset++
    }
    return -1
}

fun ApiError.toErrorObject() = ErrorObject(type, message)

fun ApiError.toResponseObject() = ErrorResponse(toErrorObject())
