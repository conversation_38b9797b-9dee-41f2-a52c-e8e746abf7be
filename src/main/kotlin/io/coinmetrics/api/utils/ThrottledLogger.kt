package io.coinmetrics.api.utils

import io.coinmetrics.api.endpoints.stream.ThrottledAction
import org.slf4j.Logger

internal class ThrottledLogger(
    val logger: Logger,
    throttleMillis: Int = 1000,
) {
    private val throttledAction = ThrottledAction(throttleMillis.toLong())

    inline fun log(loggerConsumer: Logger.() -> Unit) {
        throttledAction.act {
            loggerConsumer(logger)
        }
    }
}
