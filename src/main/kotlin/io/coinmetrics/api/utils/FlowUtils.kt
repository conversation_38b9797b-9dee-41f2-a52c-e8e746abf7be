package io.coinmetrics.api.utils

import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.mapNotNull
import kotlinx.coroutines.flow.onEach

fun <I, T> Flow<Iterable<I>>.mapInParallel(mapper: suspend (I) -> T?): Flow<Iterable<T>> =
    mapNotNull { messages ->
        coroutineScope {
            messages
                .map { message ->
                    async { mapper(message) }
                }.awaitAll()
                .filterNotNull()
                .takeIf { it.isNotEmpty() }
        }
    }

fun <T> Flow<Iterable<T>>.partition(keyExtractor: (T) -> String?): Flow<Iterable<Pair<String, Iterable<T>>>> =
    mapNotNull { items ->
        items
            .groupBy { keyExtractor(it) }
            .mapNotNull { (key, value) -> key?.let { key to value } }
    }

fun <T, K, V> Flow<Iterable<T>>.partition(
    keySelector: (T) -> K?,
    valueTransformer: (T) -> V?,
): Flow<Iterable<Pair<K, Iterable<V>>>> =
    mapNotNull { items ->
        items
            .groupBy(keySelector, valueTransformer)
            .mapNotNull { (key, value) ->
                key?.let {
                    value.mapNotNull { it }.takeIf { it.isNotEmpty() }?.let { key to it }
                }
            }
    }

fun <T> Flow<Iterable<T>>.onEachInParallel(action: suspend (T) -> Unit): Flow<Iterable<T>> =
    onEach { messages ->
        coroutineScope {
            messages.map { message -> async { action(message) } }.awaitAll()
        }
    }
