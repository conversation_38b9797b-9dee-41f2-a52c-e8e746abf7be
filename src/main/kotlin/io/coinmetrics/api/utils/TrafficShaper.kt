package io.coinmetrics.api.utils

import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import java.util.concurrent.atomic.AtomicInteger

/**
 * Splits API server's bandwidth evenly between active API keys.
 * All requests of a single API key share the same API key bandwidth.
 *
 * TODO later: Dmytro L: Though overall this seems wrong to reset the transfer rate stats each time there's a change in the active sessions. In the worst case sessions can come and go at a rate so high that the stats will be always in reset state making throttling ineffective. I think it needs a sliding window to compute transfer rate over some small period of time and throttle based on that.
 */
class TrafficShaper(
    private val instanceBandwidthBytesPerSec: Long,
) {
    // Map<ApiKey, NumberOfActiveFlowsForTheKey>
    private val clientKeyFlows = ReferenceCountedKeyContexts<String, Unit>()

    // The version is used to know when we need to reset counters for all active streams to handle the cases when the number of active flows has changed.
    // The reset of sentBytes & startTimeMs is needed to correctly re-balance targetBytesPerSecond values between active flows.
    private val version = AtomicInteger()

    /**
     * @param clientKey API key or IP address (community)
     */
    fun shape(
        sourceFlow: Flow<ByteArray>,
        clientKey: String,
        onDelay: (Long) -> Unit,
    ): Flow<ByteArray> =
        flow {
            val clientFlowCounter = clientKeyFlows.getOrCreateKeyContextAndIncreaseReferenceCount(clientKey) { _ -> }
            val targetClientSpeedCalculator = {
                val totalClients = maxOf(1, clientKeyFlows.size)
                val bandwidthPerClient = instanceBandwidthBytesPerSec / totalClients
                val clientParallelRequests = maxOf(1, clientFlowCounter.numberOfReferences())
                val bandwidthPerRequest = maxOf(1, bandwidthPerClient / clientParallelRequests)
                bandwidthPerRequest
            }
            val shapingState = FlowCounter()
            try {
                version.incrementAndGet()
                sourceFlow.shape(shapingState, { version.get() }, onDelay, targetClientSpeedCalculator).collect { emit(it) }
            } finally {
                version.incrementAndGet()
                clientFlowCounter.close()
            }
        }
}

/**
 * Can be used directly only in tests.
 * @param onDelay for delay monitoring
 * @param versionFun FlowCounters are reset upon each version change
 */
fun Flow<ByteArray>.shape(
    shapingState: FlowCounter = FlowCounter(),
    versionFun: () -> Int = { 0 },
    onDelay: (Long) -> Unit = {},
    targetBytesPerSecondFun: () -> Long,
): Flow<ByteArray> =
    flow {
        shapingState.reset(System.currentTimeMillis())
        var version = versionFun.invoke()
        collect { bytes ->
            val nowMs = System.currentTimeMillis()
            val newVersion = versionFun.invoke()
            if (newVersion != version) {
                shapingState.reset(nowMs)
                version = newVersion
            }
            val transferDurationMs = maxOf(0, nowMs - shapingState.startTimeMs)
            val targetBytesPerSecond = targetBytesPerSecondFun.invoke()
            val expectedSentBytes = (transferDurationMs * targetBytesPerSecond) / 1000
            if (shapingState.sentBytes > expectedSentBytes) {
                val waitMs = (1000 * (shapingState.sentBytes - expectedSentBytes)) / targetBytesPerSecond
                // println("wait $waitMs because sent $sentBytes but expected $expectedSentBytes")
                onDelay(waitMs)
                delay(waitMs)
            } else {
                // println("no wait")
            }
            emit(bytes)
            shapingState.inc(bytes.size)
        }
    }

class FlowCounter {
    var startTimeMs: Long = 0L
        private set
    var sentBytes = 0L
        private set

    fun inc(bytes: Int) {
        sentBytes += bytes
    }

    fun reset(nowMs: Long) {
        startTimeMs = nowMs
        sentBytes = 0
    }
}
