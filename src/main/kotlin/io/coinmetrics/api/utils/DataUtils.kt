package io.coinmetrics.api.utils

import com.google.common.collect.BoundType
import com.google.common.collect.Range
import io.coinmetrics.api.ApiError
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.badTimeFormatMessage
import io.coinmetrics.api.endpoints.timeseries.market.datasources.S3CompatibleDataTier
import io.coinmetrics.api.model.tagging.TaggedEntity
import io.coinmetrics.api.models.PagingFrom
import io.coinmetrics.api.startTimeLessThanEndMessage
import io.coinmetrics.api.toFailure
import io.coinmetrics.api.utils.TimeUtils.parseTimezoneRequestParam
import io.coinmetrics.api.utils.paging.PageToken
import io.coinmetrics.api.utils.streams.BufferedSuspendableStream
import io.coinmetrics.api.utils.streams.FlowSuspendableStream
import io.coinmetrics.api.utils.streams.SuspendableStream
import io.coinmetrics.databases.Database
import io.coinmetrics.s3databases.read.Reader
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.currentCoroutineContext
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.asFlow
import kotlinx.coroutines.flow.flatMapConcat
import kotlinx.coroutines.flow.flattenConcat
import kotlinx.coroutines.flow.flow
import java.math.BigInteger
import java.sql.ResultSet
import java.time.Clock
import java.time.Instant
import java.time.temporal.ChronoUnit
import kotlin.coroutines.ContinuationInterceptor
import kotlin.coroutines.CoroutineContext

object DataUtils {
    /**
     * @return Pair<startTime, endTime>
     */
    fun parseTimeParameters(
        startTimeStr: String?,
        startInclusive: Boolean,
        endTimeStr: String?,
        endInclusive: Boolean,
        timezone: String,
    ): FunctionResult<ApiError, Pair<Instant, Instant>> {
        val zoneId = parseTimezoneRequestParam(timezone).getOrElse { return FunctionResult.Failure(it) }

        val startTime =
            startTimeStr?.let {
                TimeUtils
                    .parseTimeAndRound(startTimeStr, zoneId, roundToLatest = !startInclusive)
                    .getOrElse {
                        return ApiError.BadParameter("start_time", badTimeFormatMessage(startTimeStr)).toFailure()
                    }
            }

        val effectiveStartTime = startTime ?: Instant.EPOCH

        val effectiveEndTime =
            endTimeStr?.let {
                TimeUtils
                    .parseTimeAndRound(endTimeStr, zoneId, roundToLatest = endInclusive)
                    .getOrElse {
                        return ApiError.BadParameter("end_time", badTimeFormatMessage(endTimeStr)).toFailure()
                    }
            } ?: Instant.now()

        return if (effectiveStartTime > effectiveEndTime) {
            val effectiveStartTimeString = startTimeStr ?: "1970-01-01"
            val effectiveEndTimeString = endTimeStr ?: "now"
            FunctionResult.Failure(
                ApiError.BadParameter(
                    "start_time",
                    startTimeLessThanEndMessage(effectiveStartTimeString, effectiveEndTimeString),
                ),
            )
        } else {
            FunctionResult.Success(effectiveStartTime to effectiveEndTime)
        }
    }

    fun createTimeAndByteArrayRangeQuery(
        startTimeStr: String?,
        startInclusive: Boolean,
        endTimeStr: String?,
        endInclusive: Boolean,
        timezone: String,
        pagingFrom: PagingFrom,
        communityEnforcedStartTime: Instant? = null,
        keyEnforcedStartTime: Instant? = null,
        keyEnforcedEndTime: Instant? = null,
    ): FunctionResult<ApiError, RangeQuery.TimeAndByteArrayRangeQuery> {
        val (startTime, endTime) =
            parseTimeParameters(
                startTimeStr,
                startInclusive,
                endTimeStr,
                endInclusive,
                timezone,
            ).getOrElse { return it.toFailure() }
                .adjust(communityEnforcedStartTime, keyEnforcedStartTime, keyEnforcedEndTime)
                ?: return ApiError
                    .ForbiddenWithMessage("Requested time range is not available with supplied credentials.")
                    .toFailure()

        return FunctionResult.Success(
            RangeQuery.TimeAndByteArrayRangeQuery(
                startTime,
                startKey2 = null,
                startInclusive,
                endTime,
                endKey2 = null,
                endInclusive,
                pagingFrom,
            ),
        )
    }

    /**
     * The current approach is to represent the time range adjustment as the intersection of three ranges:
     * - The requested time range
     * - The enforced time range (e.g., from AMS per market)
     * - The community time range
     */
    fun Pair<Instant, Instant>.adjust(
        communityEnforcedStartTime: Instant? = null,
        keyEnforcedStartTime: Instant? = null,
        keyEnforcedEndTime: Instant? = null,
    ): Pair<Instant, Instant>? {
        val (startTime, endTime) = this

        // Intersect all enforced boundaries
        val adjustedStartTime =
            listOfNotNull(startTime, communityEnforcedStartTime, keyEnforcedStartTime).maxOrNull() ?: startTime
        val adjustedEndTime = listOfNotNull(endTime, keyEnforcedEndTime).minOrNull() ?: endTime

        // If the adjusted time range is invalid, return null
        if (adjustedStartTime > adjustedEndTime) {
            return null
        }

        return Pair(adjustedStartTime, adjustedEndTime)
    }

    fun createBigIntegerAndStringRangeQuery(
        startBigInteger: BigInteger?,
        startInclusive: Boolean,
        endBigInteger: BigInteger?,
        endInclusive: Boolean,
        pagingFrom: PagingFrom,
    ): FunctionResult<ApiError, RangeQuery.BigIntegerAndStringRangeQuery> =
        FunctionResult.Success(
            RangeQuery.BigIntegerAndStringRangeQuery(
                startBigInteger,
                null,
                startInclusive,
                endBigInteger,
                null,
                endInclusive,
                pagingFrom,
            ),
        )

    fun createWhereExpression(
        subRangeQuery: RangeQuery<*, *>,
        keyNames: Array<String>,
    ): String {
        val whereFilters = ArrayList<String>()

        when (subRangeQuery) {
            is RangeQuery.TimeRangeQuery -> {
                assert(keyNames.size == 1)
                val keyName = keyNames[0]

                whereFilters.add(
                    createKeyExpression(
                        keyName,
                        subRangeQuery.startKey,
                        subRangeQuery.startInclusive,
                        isStart = true,
                    ),
                )
                whereFilters.add(
                    createKeyExpression(
                        keyName,
                        subRangeQuery.endKey,
                        subRangeQuery.endInclusive,
                        isStart = false,
                    ),
                )
            }

            is RangeQuery.TimeAndStringRangeQuery ->
                whereFilters.addAll(
                    toPaginationFilters(keyNames, subRangeQuery) { "'${SqlUtils.escapeSql(it)}'" },
                )

            is RangeQuery.TimeAndBigIntegerRangeQuery ->
                whereFilters.addAll(
                    toPaginationFilters(keyNames, subRangeQuery) { it.toString() },
                )

            is RangeQuery.TimeAndByteArrayRangeQuery ->
                whereFilters.addAll(
                    toPaginationFilters(keyNames, subRangeQuery) { "'\\x${it.toHex()}'" },
                )

            is RangeQuery.BigIntegerRangeQuery -> {
                assert(keyNames.size == 1)
                val keyName = keyNames[0]
                if (subRangeQuery.startKey != null) {
                    val value = subRangeQuery.startKey
                    val comparison = if (subRangeQuery.startInclusive) ">=" else ">"
                    whereFilters.add("$keyName $comparison $value")
                }
                if (subRangeQuery.endKey != null) {
                    val value = subRangeQuery.endKey
                    val comparison = if (subRangeQuery.endInclusive) "<=" else "<"
                    whereFilters.add("$keyName $comparison $value")
                }
            }

            is RangeQuery.BigIntegerAndStringRangeQuery -> {
                assert(keyNames.size == 2)
                val key1Name = keyNames[0]
                val key2Name = keyNames[1]
                if (subRangeQuery.startKey1 != null) {
                    val comparison = if (subRangeQuery.startInclusive) ">=" else ">"
                    val startKey2 = subRangeQuery.startKey2?.let { subRangeQuery.strTransformer(it) }
                    if (startKey2 == null) {
                        val value = subRangeQuery.startKey1
                        whereFilters.add("$key1Name $comparison $value")
                    } else {
                        val value = SqlUtils.escapeSql(startKey2)
                        whereFilters.add("$key1Name >= ${subRangeQuery.startKey1}")
                        whereFilters.add(
                            "(($key1Name = ${subRangeQuery.startKey1} AND $key2Name $comparison '$value') " +
                                "OR $key1Name $comparison ${subRangeQuery.startKey1})",
                        )
                    }
                }
                if (subRangeQuery.endKey1 != null) {
                    val comparison = if (subRangeQuery.endInclusive) "<=" else "<"
                    val endKey2 = subRangeQuery.endKey2?.let { subRangeQuery.strTransformer(it) }
                    if (endKey2 == null) {
                        val value = subRangeQuery.endKey1
                        whereFilters.add("$key1Name $comparison $value")
                    } else {
                        val value = SqlUtils.escapeSql(endKey2)
                        whereFilters.add("$key1Name <= ${subRangeQuery.endKey1}")
                        whereFilters.add(
                            "(($key1Name = ${subRangeQuery.endKey1} AND $key2Name $comparison '$value')" +
                                " OR $key1Name $comparison ${subRangeQuery.endKey1})",
                        )
                    }
                }
            }

            is TaggedEntityRangeQuery -> whereFilters.addAll(subRangeQuery.toPaginationFilter(keyNames))
        }

        return whereFilters.joinToString(separator = "") {
            " AND $it"
        }
    }

    internal fun TaggedEntityRangeQuery.toPaginationFilter(keyNames: Array<String>): List<String> {
        val rowConstructor: (List<Pair<String, CharSequence?>>, ((Pair<String, CharSequence?>) -> CharSequence)) -> String =
            { values, transform ->
                values.joinToString(prefix = "(", postfix = ")", transform = transform)
            }

        assert(keyNames.size == 5)
        return this.startKey?.let {
            val mapping =
                with(startKey) {
                    listOf(
                        keyNames[0] to mapTaggedEntityKeys(keyNames[0], this),
                        keyNames[1] to mapTaggedEntityKeys(keyNames[1], this),
                        keyNames[2] to mapTaggedEntityKeys(keyNames[2], this),
                        keyNames[3] to mapTaggedEntityKeys(keyNames[3], this),
                        keyNames[4] to mapTaggedEntityKeys(keyNames[4], this),
                    )
                }
            val currentRow = rowConstructor(mapping) { (columnName, _) -> columnName }
            val startRow = rowConstructor(mapping) { (_, startValue) -> startValue!! }
            listOf("$currentRow > $startRow")
        } ?: emptyList()
    }

    private fun createKeyExpression(
        keyName: String,
        key: Instant,
        isInclusive: Boolean,
        isStart: Boolean,
    ): String {
        val comparison =
            if (isInclusive) {
                if (isStart) {
                    ">="
                } else {
                    "<="
                }
            } else {
                if (isStart) {
                    ">"
                } else {
                    "<"
                }
            }
        val keyExpression = TimeUtils.toSqlCompareExpression(comparison, key)
        return "$keyName $keyExpression"
    }

    private fun mapTaggedEntityKeys(
        keyName: String,
        key: TaggedEntity.Key,
    ): CharSequence? =
        when (keyName) {
            "r.tag" -> SqlUtils.escapeAndQuote().invoke(key.tag)
            "r.location" -> SqlUtils.escapeAndQuote().invoke(key.location)
            "r.entity" -> SqlUtils.escapeAndQuote().invoke(key.entity)
            "r.timestamp_start" -> TimeUtils.toSqlCompareExpressionPair(">", key.startTime).second
            "r.started_by" -> SqlUtils.escapeAndQuote().invoke(key.startedBy)
            else -> {
                null
            }
        }

    private fun <T> toPaginationFilters(
        keyNames: Array<String>,
        rangeQuery: RangeQuery.TwoComponentsRangeQuery<Instant, T>,
        key2ToString: (T) -> String,
    ): ArrayList<String> {
        assert(keyNames.size == 2)
        val key1Name = keyNames[0]
        val key2Name = keyNames[1]

        val filters = ArrayList<String>()
        val startComparison = if (rangeQuery.startInclusive) ">=" else ">"
        if (rangeQuery.startKey2 == null) {
            val expression = TimeUtils.toSqlCompareExpression(startComparison, rangeQuery.startKey1)
            filters.add("$key1Name $expression")
        } else {
            val expression0 = TimeUtils.toSqlCompareExpression(">=", rangeQuery.startKey1)
            val expression1 = TimeUtils.toSqlCompareExpression("=", rangeQuery.startKey1)
            val expression2 = TimeUtils.toSqlCompareExpression(">", rangeQuery.startKey1)
            val value = key2ToString(rangeQuery.startKey2!!)
            filters.add("$key1Name $expression0")
            filters.add("(($key1Name $expression1 AND $key2Name $startComparison $value) OR $key1Name $expression2)")
        }

        val endComparison = if (rangeQuery.endInclusive) "<=" else "<"
        if (rangeQuery.endKey2 == null) {
            val expression = TimeUtils.toSqlCompareExpression(endComparison, rangeQuery.endKey1)
            filters.add("$key1Name $expression")
        } else {
            val expression0 = TimeUtils.toSqlCompareExpression("<=", rangeQuery.endKey1)
            val expression1 = TimeUtils.toSqlCompareExpression("=", rangeQuery.endKey1)
            val expression2 = TimeUtils.toSqlCompareExpression("<", rangeQuery.endKey1)
            val value = key2ToString(rangeQuery.endKey2!!)
            filters.add("$key1Name $expression0")
            filters.add("(($key1Name $expression1 AND $key2Name $endComparison $value) OR $key1Name $expression2)")
        }
        return filters
    }

    fun createTimeFilter(
        startKey: Instant,
        startInclusive: Boolean,
        endKey: Instant,
        endInclusive: Boolean,
    ): (Instant) -> Boolean {
        if (startInclusive && endInclusive) {
            return { it in startKey..endKey }
        } else if (!startInclusive && !endInclusive) {
            return { it > startKey && it < endKey }
        } else if (startInclusive && !endInclusive) {
            return { it >= startKey && it < endKey }
        } else {
            assert(!startInclusive)
            assert(endInclusive)
            return { it > startKey && it <= endKey }
        }
    }

    fun createTimeFilter(rangeQuery: RangeQuery.TimeRangeQuery): (Instant) -> Boolean {
        if (rangeQuery.startInclusive && rangeQuery.endInclusive) {
            return { it in rangeQuery.startKey..rangeQuery.endKey }
        } else if (!rangeQuery.startInclusive && !rangeQuery.endInclusive) {
            return { it > rangeQuery.startKey && it < rangeQuery.endKey }
        } else if (rangeQuery.startInclusive && !rangeQuery.endInclusive) {
            return { it >= rangeQuery.startKey && it < rangeQuery.endKey }
        } else {
            assert(!rangeQuery.startInclusive)
            assert(rangeQuery.endInclusive)
            return { it > rangeQuery.startKey && it <= rangeQuery.endKey }
        }
    }

    fun <Q : RangeQuery.TwoComponentsRangeQuery<Instant, *>> createTimeFilter(rangeQuery: Q): (Instant) -> Boolean {
        val startInclusive = if (rangeQuery.startKey2 == null) rangeQuery.startInclusive else true
        val endInclusive = if (rangeQuery.endKey2 == null) rangeQuery.endInclusive else true

        if (startInclusive && endInclusive) {
            return { it >= rangeQuery.startKey1 && it <= rangeQuery.endKey1 }
        } else if (!startInclusive && !endInclusive) {
            return { it > rangeQuery.startKey1 && it < rangeQuery.endKey1 }
        } else if (startInclusive && !endInclusive) {
            return { it >= rangeQuery.startKey1 && it < rangeQuery.endKey1 }
        } else {
            assert(!startInclusive)
            assert(endInclusive)
            return { it > rangeQuery.startKey1 && it <= rangeQuery.endKey1 }
        }
    }

    /**
     * @param streamId must be non-null if wildcards are used (streamIdsAreResolvedDynamically=true).
     */
    fun <T, Q : RangeQuery<Q, P>, P : PageToken> createStream(
        db: Database,
        queryTextBuilder: QueryTextBuilder,
        bufferSize: Int,
        keyNames: Array<String>,
        dataMapper: (ResultSet) -> T,
        rangeQuery: Q,
        initialState: P?,
        stateResolver: (T) -> P,
        beforeQuery: String? = null,
        afterQuery: String? = null,
        streamId: String?,
        lbKey: String? = null,
    ): SuspendableStream<T, P> {
        val effectiveLbKey = if (db.config.lb) lbKey else null

        val bufferLoader: suspend (P?, Int) -> List<T> = { state, limit ->
            val newRangeQuery = rangeQuery.withPageToken(state)
            val whereExpression = createWhereExpression(newRangeQuery, keyNames)

            val queryText =
                queryTextBuilder.invoke(
                    whereExpression,
                    limit,
                )
            // println(queryText)

            try {
                db.query(
                    queryText,
                    beforeQuery = beforeQuery,
                    afterQuery = afterQuery,
                    lbKey = effectiveLbKey,
                ) { query ->
                    query.map(dataMapper).toList()
                }
            } catch (e: CancellationException) {
                // it's a workaround for Kotlin circular exceptions, see https://github.com/Kotlin/kotlinx.coroutines/issues/1264
                throw CancellationException("Query cancelled: $queryText", e)
            } catch (e: Exception) {
                throw RuntimeException("Query failed: $queryText", e)
            }
        }

        // postgres doesn't work well with LIMIT 10001, so we reduce the maximum limit
        val effectiveBufferSize = minOf(5_001, bufferSize)

        return BufferedSuspendableStream(
            initialState = initialState,
            bufferLoader = bufferLoader,
            bufferSize = effectiveBufferSize,
            stateResolver = stateResolver,
            streamId = streamId,
        )
    }

    suspend fun <Q : RangeQuery<Q, P>, P : PageToken> createTieredStream(
        tiers: List<Pair<S3CompatibleDataTier, TimeUtils.StatefulDownSamplerConfig?>>,
        rangeQuery: Q,
        initialState: P?,
        stateResolver: (Reader.TimedByteArray) -> P,
        streamId: String? = null,
        httpRequestCoroutineContext: CoroutineContext,
        downSamplingConfig: TimeUtils.StatefulDownSamplerConfig? = null,
    ): SuspendableStream<Reader.TimedByteArray, P> {
        require(rangeQuery is RangeQuery.TimeRangeQuery) { "Only TimeRangeQuery is supported." }

        val baseFlow = createTieredExecutionPlan(tiers, rangeQuery)

        val flow =
            if (downSamplingConfig != null) {
                baseFlow
                    .boundaryAwareDownsampleTimedByteArray(downSamplingConfig)
            } else {
                baseFlow
            }

        return FlowSuspendableStream(
            coroutineContext = httpRequestCoroutineContext,
            coroutineDispatcher = currentCoroutineContext()[ContinuationInterceptor] as CoroutineDispatcher,
            initialState = initialState,
            flow = flow,
            stateResolver = stateResolver,
            streamId = streamId,
        )
    }

    /**
     * A single rangeQuery can be split into multiple rangeQueries against non-intersecting gap-less tiers.
     * After that, resulting flows can be merged into a single flow.
     *
     * An example,
     *        startDate=null            startDate=xxxx-xx-xx     now
     * Tiers:  |-------------0--------------|----------1----------|
     * Query:                     |--------------|
     * Plan:                      |----0----|-1--|
     *
     * Basically, we need to calculate the intersections of the provided time range with each tier and concatenate
     * results according to the paging_from value.
     */
    private fun createTieredExecutionPlan(
        tiers: List<Pair<S3CompatibleDataTier, TimeUtils.StatefulDownSamplerConfig?>>,
        rangeQuery: RangeQuery.TimeRangeQuery,
    ): Flow<Reader.TimedByteArray> {
        val queryTimeRange = rangeQuery.toGuavaTimeRange()
        val sortedTiers =
            if (rangeQuery.pagingFrom == PagingFrom.START) {
                tiers.sortedBy { (tier, _) -> tier.supportedTimeRange.startTimeForSorting() }
            } else {
                tiers.sortedByDescending { (tier, _) -> tier.supportedTimeRange.startTimeForSorting() }
            }

        return sortedTiers
            .mapNotNull { (tier, downSamplingConfig) ->
                if (tier.supportedTimeRange.isConnected(queryTimeRange)) {
                    val intersection = tier.supportedTimeRange.intersection(queryTimeRange)
                    if (intersection.isEmpty) {
                        null
                    } else {
                        val flow =
                            tier
                                .newFlow(
                                    RangeQuery.TimeRangeQuery(
                                        startKey = intersection.lowerEndpoint(),
                                        startInclusive = intersection.lowerBoundType() == BoundType.CLOSED,
                                        endKey = intersection.upperEndpoint(),
                                        endInclusive = intersection.upperBoundType() == BoundType.CLOSED,
                                        pagingFrom = rangeQuery.pagingFrom,
                                    ),
                                ).flatMapConcat { it.asFlow() }
                        if (downSamplingConfig != null) flow.boundaryAwareDownsampleTimedByteArray(downSamplingConfig) else flow
                    }
                } else {
                    null
                }
            }.asFlow()
            .flattenConcat()
    }

    private fun Range<Instant>.startTimeForSorting(): Instant? = if (hasLowerBound()) lowerEndpoint() else Instant.MIN

    private fun RangeQuery.TimeRangeQuery.toGuavaTimeRange(): Range<Instant> {
        if (startInclusive) {
            return if (endInclusive) {
                Range.closed(
                    startKey,
                    endKey,
                )
            } else {
                Range.closedOpen(
                    startKey,
                    endKey,
                )
            }
        } else {
            return if (endInclusive) {
                Range.openClosed(
                    startKey,
                    endKey,
                )
            } else {
                Range.open(
                    startKey,
                    endKey,
                )
            }
        }
    }

    fun communityEnforcedStart(
        apiKey: String,
        communityApiKey: String,
        delayDays: Long = 1,
        clock: Clock? = null,
    ): Instant? {
        // enforce 24h limit for community key if requesting sub day frequency reference rate market data
        return if (apiKey == communityApiKey) {
            getCommunityDelayedTime(clock, delayDays)
        } else {
            null
        }
    }

    fun getMinMaxTimeBasedOnApiKey(
        apiKey: String?,
        communityApiKey: String,
        statisticsMinTime: String,
        statisticsMaxTime: String,
        delayDays: Long = 1,
        clock: Clock? = null,
    ): Pair<String?, String?> =
        if (apiKey == communityApiKey) {
            val forcedMinTime = getCommunityDelayedTime(clock, delayDays).truncatedTo(ChronoUnit.SECONDS)
            val newMinTime = maxOf(statisticsMinTime, TimeUtils.dateTimeFormatter.format(forcedMinTime))
            if (newMinTime > statisticsMaxTime) {
                null to null
            } else {
                newMinTime to statisticsMaxTime
            }
        } else {
            statisticsMinTime to statisticsMaxTime
        }

    private fun getCommunityDelayedTime(
        clock: Clock?,
        delayDays: Long,
    ): Instant {
        val now = clock?.instant() ?: Instant.now()
        return now.minus(delayDays, ChronoUnit.DAYS)
    }

    /**
     * Compares the parameters provided by the request (if any) to the configured boundaries provided (if any)
     * and returns a pair (startTime?, endTime?) accordingly
     */
    fun getStartEndTimeLimitedByBoundaries(
        requestStart: String?,
        requestEnd: String?,
        requestTimezone: String,
        requestStartInclusive: Boolean,
        requestEndInclusive: Boolean,
        configuredStartBoundary: Instant?,
        configuredEndBoundary: Instant?,
    ): FunctionResult<ApiError, Pair<Instant, Instant>> {
        // Parse requests strings

        val (parsedStartTime, parsedEndTime) =
            when (
                val result =
                    parseTimeParameters(
                        requestStart,
                        requestStartInclusive,
                        requestEnd,
                        requestEndInclusive,
                        requestTimezone,
                    )
            ) {
                is FunctionResult.Success -> result.value
                is FunctionResult.Failure -> return FunctionResult.Failure(result.value)
            }

        var resultingStartTime = parsedStartTime
        if (configuredStartBoundary?.isAfter(parsedStartTime) == true) {
            resultingStartTime = configuredStartBoundary
        }

        var resultingEndTime = parsedEndTime
        if (configuredEndBoundary?.isBefore(parsedEndTime) == true) {
            resultingEndTime = configuredEndBoundary
        }

        return FunctionResult.Success(resultingStartTime to resultingEndTime)
    }

    fun convertListOfBytesArraysToSqlCollection(
        items: List<ByteArray>,
        truncateToSize: Int? = null,
    ): String =
        items.joinToString(separator = ",") { item ->
            convertByteArrayToSqlParam(item, truncateToSize)
        }

    fun convertByteArrayToSqlParam(
        item: ByteArray,
        truncateToSize: Int? = null,
    ): String = "'${convertByteArrayToSqlParamWithoutQuotes(item, truncateToSize)}'"

    fun convertByteArrayToSqlParamWithoutQuotes(
        item: ByteArray,
        truncateToSize: Int? = null,
    ): String {
        val normalizedValue =
            if (truncateToSize == null || item.size <= truncateToSize) {
                item
            } else {
                item.copyOfRange(0, truncateToSize)
            }
        return "\\x${normalizedValue.toHex()}"
    }

    fun createPageTokenStrTransformer(
        asset: String,
        decoder: (String, String) -> ByteArray,
    ): (String) -> String =
        { value ->
            val decodedHash = decoder(asset, value)
            convertByteArrayToSqlParamWithoutQuotes(decodedHash)
        }

    fun isOverlap(
        startTime1: Instant,
        endTime1: Instant,
        startTime2: Instant,
        endTime2: Instant,
    ): Boolean = startTime1 <= endTime2 && endTime1 >= startTime2

    /**
     * Applies boundary-aware downsampling to a flow of Reader.TimedByteArray.
     * This ensures that the record closest to each time boundary is selected.
     */
    fun Flow<Reader.TimedByteArray>.boundaryAwareDownsampleTimedByteArray(
        config: TimeUtils.StatefulDownSamplerConfig,
    ): Flow<Reader.TimedByteArray> =
        flow {
            val downsampler = TimeUtils.BoundaryAwareDownsampler(config)

            collect { item ->
                val nextItems = downsampler.processNextRecord(item)

                nextItems?.forEach { nextItem ->
                    if (nextItem != null) {
                        emit(nextItem)
                    }
                }
            }

            downsampler.pollLast()?.also {
                emit(it)
            }
        }
}

typealias QueryTextBuilder = (filter: String, limit: Int) -> String
