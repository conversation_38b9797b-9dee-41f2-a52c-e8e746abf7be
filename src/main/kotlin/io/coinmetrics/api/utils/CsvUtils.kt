package io.coinmetrics.api.utils

import io.coinmetrics.httpserver.HttpResponse

object CsvUtils {
    private val ESCAPED_CHARS = charArrayOf(',', '\n', '"')

    @Deprecated("Use Response.chunkedResponse(format=CSV).")
    fun toCsvHttpResponse(
        data: Array<Map<String, String?>>,
        nextPageToken: String?,
        nextPageUrl: String?,
        headers: List<Pair<String, String>>,
        nullValue: String = "null",
    ): HttpResponse {
        val newHeaders = ArrayList<Pair<String, String>>(headers)
        newHeaders.add("content-disposition" to "inline; filename=data.csv")

        if (nextPageToken != null) {
            assert(nextPageUrl != null)
            newHeaders.add("x-next-page-token" to nextPageToken)
            newHeaders.add("x-next-page-url" to nextPageUrl!!)
        }

        return HttpResponse(contentType = "text/csv", body = toCsvData(data, nullValue), headers = newHeaders)
    }

    fun toCsvData(
        data: Array<Map<String, String?>>,
        nullValue: String,
        sb: StringBuilder = StringBuilder(),
    ): String {
        if (data.isNotEmpty()) {
            appendHeader(data.first(), sb)
            for (row in data) {
                appendValues(row, sb, nullValue)
            }
        }

        return sb.toString()
    }

    internal fun headerLine(map: Map<String, String?>): String =
        map.keys.joinToString(separator = ",") {
            escape(it)
        }

    private fun appendHeader(
        map: Map<String, String?>,
        sb: StringBuilder,
    ) {
        sb.append(headerLine(map)).append('\n')
    }

    internal fun valuesLine(
        map: Map<String, String?>,
        nullValue: String,
    ): String =
        map.values.joinToString(separator = ",") {
            if (it == null) {
                nullValue
            } else {
                escape(it)
            }
        }

    private fun appendValues(
        map: Map<String, String?>,
        sb: StringBuilder,
        nullValue: String,
    ) {
        sb.append(valuesLine(map, nullValue)).append('\n')
    }

    private fun escape(value: String): String =
        if (ESCAPED_CHARS.any { value.contains(it) }) {
            "\"" + value.replace("\"", "\"\"") + "\""
        } else {
            value
        }
}
