package io.coinmetrics.api.utils

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.FunctionResult

object CommonEndpointUtils {
    /**
     * Validates mutually exclusive parameters.
     * Result is null or ApiError with message, for instance:
     * - Filters "*_time" and "*_chain_sequence_number" are mutually exclusive and can't be specified in the same request.
     * - Filters "*_time", "*_height" and "*_chain_sequence_number" are mutually exclusive and can't be specified in the same request.
     * - etc.
     */
    fun validateMutuallyExclusiveFilters(parameters: List<Pair<String, Any?>>): FunctionResult<ApiError, Any?> {
        val specifiedParameters =
            parameters.filter { (_, value) ->
                if (value != null && value is Pair<*, *>) {
                    val (from, to) = value
                    from != null || to != null
                } else if (value != null && value is Collection<*>) {
                    value.isNotEmpty()
                } else {
                    value != null
                }
            }
        if (specifiedParameters.size > 1) {
            val specifiedParameterDescriptions = specifiedParameters.map { it.first }
            val firstParameterNames = specifiedParameterDescriptions.take(specifiedParameterDescriptions.size - 1)
            val lastParameterName = specifiedParameterDescriptions[specifiedParameterDescriptions.size - 1]
            val message = "Filters ${
                firstParameterNames.joinToString(
                    separator = ", ",
                ) { "'$it'" }
            } and '$lastParameterName' are mutually exclusive and can't be specified in the same request."
            return FunctionResult.Failure(ApiError.BadParameters(message))
        }
        return FunctionResult.Success(null)
    }

    /**
     * Finds fields to be included based on specified 'include' or 'exclude' parameters.
     * Parameter 'optionalResponseFields' specifies which fields can be possibly included.
     */
    fun determineFieldsToInclude(
        include: List<String>?,
        exclude: List<String>?,
        optionalResponseFields: Set<String>,
    ): FunctionResult<ApiError, Set<String>> {
        val includeFields = include?.toHashSet() ?: emptySet()
        val excludeFields = exclude?.toHashSet() ?: emptySet()

        validateMutuallyExclusiveFilters(
            listOf(
                "include" to includeFields,
                "exclude" to excludeFields,
            ),
        ).getOrElse { apiError -> return FunctionResult.Failure(apiError) }

        if (includeFields.isNotEmpty()) {
            val illegalField = includeFields.find { it !in optionalResponseFields }
            if (illegalField != null) {
                return FunctionResult.Failure(ApiError.BadParameter("include", "Value '$illegalField' is not supported."))
            }
        }

        if (excludeFields.isNotEmpty()) {
            val illegalField = excludeFields.find { it !in optionalResponseFields }
            if (illegalField != null) {
                return FunctionResult.Failure(ApiError.BadParameter("exclude", "Value '$illegalField' is not supported."))
            }
        }

        val fieldsToBeIncluded =
            if (includeFields.isNotEmpty()) {
                includeFields
            } else if (excludeFields.isNotEmpty()) {
                optionalResponseFields - excludeFields
            } else {
                optionalResponseFields
            }
        return FunctionResult.Success(fieldsToBeIncluded)
    }

    /**
     * Validates that each one of the parameters contain null or a single element.
     * Result is null or ApiError (forbidden) with message.
     */
    fun validateSingleParametersForCommunity(vararg parameters: Pair<String, List<String>?>): ApiError? {
        parameters.forEach { (parameterName, parameterValues) ->
            if (parameterValues != null && parameterValues.size > 1) {
                return ApiError.ForbiddenWithMessage("Filter '$parameterName' with multiple elements is not allowed for Community API.")
            }
        }

        return null
    }

    /**
     * Validates that the parameters are not present (contain null or empty arrays)
     * Result is null or ApiError (forbidden) with message.
     */
    fun validateNotPresentParametersForCommunity(vararg parameters: Pair<String, List<String>?>): ApiError? =
        when (val parameterName = getFirstNonEmptyParameter(*parameters)) {
            is String -> ApiError.ForbiddenWithMessage("Filter '$parameterName' is not allowed for Community API.")
            else -> null
        }

    fun getFirstNonEmptyParameter(vararg parameters: Pair<String, List<String>?>): String? {
        parameters.forEach { (parameterName, parameterValues) ->
            if (parameterValues?.isNotEmpty() == true) {
                return parameterName
            }
        }

        return null
    }

    fun validateChainParameter(chain: String): ApiError? {
        return if (chain !in listOf("main", "all")) {
            return ApiError.BadParameter(
                "chain",
                "Must be one of the following: main, all.",
            )
        } else {
            null
        }
    }
}
