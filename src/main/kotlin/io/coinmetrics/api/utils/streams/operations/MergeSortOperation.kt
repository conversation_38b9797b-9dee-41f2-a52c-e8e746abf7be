package io.coinmetrics.api.utils.streams.operations

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.models.PagingFrom
import io.coinmetrics.api.utils.streams.SuspendableStream
import io.coinmetrics.api.utils.streams.SuspendableStreamAsMergeSource
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.coroutineScope
import java.util.PriorityQueue

/**
 * Sorting is not stable.
 */
fun <T, KV : Comparable<*>> List<SuspendableStreamAsMergeSource<T, *, KV>>.mergeSort(
    pagingFrom: PagingFrom,
    emittedByStream: LongArray? = null,
): SuspendableStream<T, MergingSortState> {
    return object : SuspendableStream<T, MergingSortState>() {
        private val sources = this@mergeSort
        private var inited = false
        private val queue =
            if (pagingFrom == PagingFrom.START) {
                PriorityQueue<MergeItemForSorting<T, KV>>(compareBy({ it.mergeKeyValue() }, { it.sourceIndex }))
            } else {
                PriorityQueue<MergeItemForSorting<T, KV>>(
                    compareBy<MergeItemForSorting<T, KV>>({ it.mergeKeyValue() }, { it.sourceIndex }).reversed(),
                )
            }

        // these two fields are set in the same time
        private var forcedNextItem: Holder<Pair<T, Int>?>? = null
        private var forcedMergingState: Holder<MergingSortState?>? = null

        private suspend fun lazyInit() {
            if (inited) return
            coroutineScope {
                sources.map { source ->
                    async { source to source.suspendableStream.peek() }
                }
            }.awaitAll().forEachIndexed { index, (source, item) ->
                if (item != null) {
                    val mergeItem =
                        MergeItemForSorting(source, index, item)
                    queue.add(mergeItem)
                }
            }
            inited = true
        }

        override suspend fun poll(): T? {
            val item = pollInternal()
            emittedByStream?.also {
                item?.also {
                    emittedByStream[item.second]++
                }
            }
            return item?.first
        }

        /**
         * @return Pair<T, sourceIndex>
         */
        private suspend fun pollInternal(): Pair<T, Int>? {
            lazyInit()
            forcedNextItem?.also {
                forcedNextItem = null
                forcedMergingState = null
                return it.value
            }
            val mergeItem = queue.poll() ?: return null

            mergeItem.source.suspendableStream.poll()
            val value = mergeItem.currentItem
            addToQueueIfNeeded(mergeItem)

            return value?.let { it to mergeItem.sourceIndex }
        }

        override suspend fun peek(): T? {
            forcedNextItem?.also {
                return it.value?.first
            }
            forcedMergingState =
                Holder(state())
            val holder = Holder<Pair<T, Int>?>(pollInternal())
            forcedNextItem = holder
            return holder.value?.first
        }

        override suspend fun prefetch() {
            // ignore
        }

        override fun state(): MergingSortState? {
            forcedMergingState?.also {
                return it.value
            }

            val states =
                sources.mapIndexed { index, it ->
                    StreamStateSort(
                        state = it.suspendableStream.state(),
                        emitted = emittedByStream?.let { it[index] },
                    )
                }
            return MergingSortState(states)
        }

        override fun close() {
            sources.forEach { it.suspendableStream.close() }
        }

        private suspend fun addToQueueIfNeeded(mergeItem: MergeItemForSorting<T, KV>) {
            mergeItem.currentItem = mergeItem.source.suspendableStream.peek() ?: return
            queue.add(mergeItem)
        }

        override fun streamId(): String? = null
    }
}

private class MergeItemForSorting<T, KV>(
    val source: SuspendableStreamAsMergeSource<T, *, KV>,
    val sourceIndex: Int,
    var currentItem: T,
) {
    fun mergeKeyValue(): KV = source.mergeKeyValueExtractor.invoke(currentItem)
}

class MergingSortState(
    val states: List<StreamStateSort<SuspendableStream.State>>,
) : SuspendableStream.State {
    override fun serialize(): String = MergingSortStateSerializer.serialize(this)
}

class MergingSortStateDeserialized(
    // Array<Pair<nextPageToken, emitted>>
    val states: Array<Pair<String, Long?>?>,
)

class StreamStateSort<S : SuspendableStream.State>(
    val state: S?,
    val emitted: Long?,
)

object MergingSortStateSerializer {
    private const val SEPARATOR = ";"
    private const val NESTED_SEPARATOR = ":"

    fun serialize(state: MergingSortState): String =
        state.states.joinToString(separator = SEPARATOR) { streamWithSerializer ->
            val part1 = streamWithSerializer.state?.serialize() ?: ""
            if (part1.isEmpty()) {
                part1
            } else {
                part1 + (streamWithSerializer.emitted?.let { "$NESTED_SEPARATOR$it" } ?: "")
            }
        }

    fun deserialize(
        str: String?,
        sourceCount: Int,
    ): FunctionResult<ApiError, MergingSortStateDeserialized> {
        if (str == null) {
            return FunctionResult.Success(MergingSortStateDeserialized(Array(sourceCount) { null }))
        }
        val parts = str.split(SEPARATOR)
        if (parts.size != sourceCount) return FunctionResult.Failure(ApiError.BadParameter("next_page_token"))
        return FunctionResult.Success(
            MergingSortStateDeserialized(
                Array(sourceCount) {
                    val part = parts[it]
                    if (part.isEmpty()) {
                        null
                    } else {
                        val nestedParts = part.split(NESTED_SEPARATOR)
                        if (nestedParts.size == 1) {
                            nestedParts[0] to null
                        } else {
                            nestedParts[0] to (
                                nestedParts[1].toLongOrNull()
                                    ?: return FunctionResult.Failure(ApiError.BadParameter("next_page_token"))
                            )
                        }
                    }
                },
            ),
        )
    }
}
