package io.coinmetrics.api.utils.streams

class SuspendableStreamAsMergeSource<T, S : SuspendableStream.State, V>(
    val suspendableStream: SuspendableStream<T, S>,
    val mergeKeyValueExtractor: (T) -> V,
)

/**
 * @param mergeKeyValueExtractor Warning! It must return unique values for the SuspendableStream.
 */
fun <T, S : SuspendableStream.State, V : Comparable<*>> SuspendableStream<T, S>.asMergeSource(
    mergeKeyValueExtractor: (T) -> V,
): SuspendableStreamAsMergeSource<T, S, V> = SuspendableStreamAsMergeSource(this, mergeKeyValueExtractor)
