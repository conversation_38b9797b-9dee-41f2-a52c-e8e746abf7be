package io.coinmetrics.api.utils.streams.operations

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.models.PagingFrom
import io.coinmetrics.api.utils.streams.SuspendableStream
import io.coinmetrics.api.utils.streams.SuspendableStreamAsMergeSource
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.coroutineScope
import java.util.PriorityQueue

/**
 * @param streamId should be non-null if wildcards are used.
 */
fun <KV : Comparable<*>, R, T> Collection<SuspendableStreamAsMergeSource<T, *, KV>>.mergeJoin(
    streamId: String?,
    pagingFrom: PagingFrom,
    mergeFunction: (List<T?>) -> R,
): SuspendableStream<R, MergingState> {
    return object : SuspendableStream<R, MergingState>() {
        private val sources = this@mergeJoin
        private var inited = false
        private val queue =
            if (pagingFrom == PagingFrom.START) {
                PriorityQueue<MergeItem<T, KV>>(compareBy { it.mergeKeyValue() })
            } else {
                PriorityQueue<MergeItem<T, KV>>(compareByDescending { it.mergeKeyValue() })
            }
        private val mergeCandidates = MutableList<T?>(sources.size) { null }

        // these two fields are set in the same time
        private var forcedNextItem: Holder<R?>? = null
        private var forcedMergingState: Holder<MergingState?>? = null

        private suspend fun lazyInit() {
            if (inited) return
            coroutineScope {
                sources.map { source ->
                    async { source to source.suspendableStream.peek() }
                }
            }.awaitAll().forEachIndexed { index, (source, item) ->
                if (item != null) {
                    val mergeItem =
                        MergeItem(
                            source,
                            index,
                            item,
                        )
                    queue.add(mergeItem)
                }
            }
            inited = true
        }

        override suspend fun poll(): R? {
            lazyInit()
            forcedNextItem?.also {
                forcedNextItem = null
                forcedMergingState = null
                return it.value
            }
            val mergeItem = queue.poll() ?: return null
            val mergeKeyValue = mergeItem.mergeKeyValue()

            mergeItem.source.suspendableStream.poll()
            mergeCandidates.fill(null)
            mergeCandidates[mergeItem.originalPosition] = mergeItem.currentItem
            addToQueueIfNeeded(mergeItem)

            while (true) {
                val anotherMergeItem = queue.peek() ?: break
                if (anotherMergeItem.mergeKeyValue() == mergeKeyValue) {
                    queue.poll()
                    anotherMergeItem.source.suspendableStream.poll()
                    mergeCandidates[anotherMergeItem.originalPosition] = anotherMergeItem.currentItem
                    addToQueueIfNeeded(anotherMergeItem)
                } else {
                    break
                }
            }

            return mergeFunction.invoke(mergeCandidates)
        }

        override suspend fun peek(): R? {
            forcedNextItem?.also {
                return it.value
            }
            forcedMergingState =
                Holder(state())
            val holder = Holder<R?>(poll())
            forcedNextItem = holder
            return holder.value
        }

        override suspend fun prefetch() {
            // ignore
        }

        override fun state(): MergingState? {
            forcedMergingState?.also {
                return it.value
            }

            val states =
                sources.map {
                    it.suspendableStream.state()
                }
            return MergingState(states)
        }

        override fun close() {
            sources.forEach { it.suspendableStream.close() }
        }

        private suspend fun addToQueueIfNeeded(mergeItem: MergeItem<T, KV>) {
            mergeItem.currentItem = mergeItem.source.suspendableStream.peek() ?: return
            queue.add(mergeItem)
        }

        override fun streamId(): String? = streamId
    }
}

private class MergeItem<T, KV>(
    val source: SuspendableStreamAsMergeSource<T, *, KV>,
    val originalPosition: Int,
    var currentItem: T,
) {
    fun mergeKeyValue(): KV = source.mergeKeyValueExtractor.invoke(currentItem)
}

class MergingState(
    val states: List<SuspendableStream.State?>,
) : SuspendableStream.State {
    override fun serialize() = MergingStateSerializer.serialize(this)
}

class MergingStateDeserialized(
    val states: Array<String?>,
)

object MergingStateSerializer {
    private const val SEPARATOR = ","

    fun serialize(state: MergingState): String = state.states.joinToString(separator = SEPARATOR) { it?.serialize() ?: "" }

    fun deserialize(
        str: String?,
        sourceCount: Int,
    ): FunctionResult<ApiError, MergingStateDeserialized> {
        if (str == null) {
            return FunctionResult.Success(MergingStateDeserialized(Array(sourceCount) { null }))
        }
        val parts = str.split(SEPARATOR)
        if (parts.size != sourceCount) return FunctionResult.Failure(ApiError.BadParameter("next_page_token"))
        return FunctionResult.Success(
            MergingStateDeserialized(
                Array(sourceCount) {
                    val part = parts[it]
                    if (part.isEmpty()) {
                        null
                    } else {
                        part
                    }
                },
            ),
        )
    }
}
