package io.coinmetrics.api.utils.streams

import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.isActive
import kotlin.coroutines.coroutineContext

/**
 * The SuspendableStreams are lazy and buffered. It means that if we read (poll or peek) an item from a stream,
 * the stream triggers a database request to fill the internal stream buffer (see bufferSize in BufferedSuspendableStream).<br/>
 *
 * States are used to maintain the information about the current stream position.<br/>
 * It's needed to be able to suspend streams while navigating between response pages by serializing streams' states to pageTokens.
 *
 * Streams use the same semantic as standard Java queues.
 */
abstract class SuspendableStream<out T, out S : SuspendableStream.State> : AutoCloseable {
    companion object {
        private val emptyStream =
            object : SuspendableStream<Any, State>() {
                override suspend fun poll(): Any? = null

                override suspend fun peek(): Any? = null

                override suspend fun prefetch() {
                    // ignore
                }

                override fun state(): State? = null

                override fun close() {
                    // ignore
                }

                override fun streamId(): String? = null
            }

        fun <T, S : State> empty(): SuspendableStream<T, S> {
            @Suppress("UNCHECKED_CAST")
            return emptyStream as SuspendableStream<T, S>
        }
    }

    abstract fun streamId(): String?

    /**
     * Reads and removes an item from the stream.
     */
    abstract suspend fun poll(): T?

    /**
     * Reads an item from the stream but doesn't remove it. So the next peek or poll invocation returns the same value.
     * Peek doesn't modify stream's state.
     */
    abstract suspend fun peek(): T?

    abstract suspend fun prefetch()

    /**
     * Returns the current stream position for further serialization.
     */
    abstract fun state(): S?

    /**
     * Must be called if the stream is not used anymore.
     */
    abstract override fun close()

    /**
     * It is important that state should not be modified in mapper.
     */
    inline fun <R> map(crossinline mapper: (T) -> R): SuspendableStream<R, S> {
        return object : SuspendableStream<R, S>() {
            override suspend fun poll(): R? {
                val item = <EMAIL>() ?: return null
                return mapper.invoke(item)
            }

            override suspend fun peek(): R? {
                val item = <EMAIL>() ?: return null
                return mapper.invoke(item)
            }

            override suspend fun prefetch() {
                <EMAIL>()
            }

            override fun state(): S? = <EMAIL>()

            override fun close() {
                <EMAIL>()
            }

            override fun streamId(): String? = <EMAIL>()
        }
    }

    inline fun filter(crossinline filter: (T) -> Boolean): SuspendableStream<T, S> {
        return object : SuspendableStream<T, S>() {
            private var forcedNextItem: Holder<T?>? = null
            private var forcedState: Holder<S?>? = null

            override suspend fun poll(): T? {
                forcedNextItem?.also {
                    forcedNextItem = null
                    forcedState = null
                    return it.value
                }

                while (true) {
                    val item = <EMAIL>() ?: return null
                    if (filter.invoke(item)) return item
                }
            }

            override suspend fun peek(): T? {
                forcedNextItem?.also {
                    return it.value
                }
                forcedState = Holder(state())
                val item = poll()
                forcedNextItem = Holder(item)
                return item
            }

            override suspend fun prefetch() {
                <EMAIL>()
            }

            override fun state(): S? {
                forcedState?.also {
                    return it.value
                }
                return <EMAIL>()
            }

            override fun close() {
                <EMAIL>()
            }

            override fun streamId(): String? = <EMAIL>()
        }
    }

    /**
     * Collapses adjacent items with the same key value into a Pair<Key, List<Item>>.<br/>
     * It is only useful when the stream produces items which non-unique keys.<br/>
     * Key object(K) should have an "equals" method defined.
     *
     * This method doesn't change the ordering of items.
     */
    inline fun <K> collapseByKey(crossinline keyExtractor: (T) -> K): SuspendableStream<Pair<K, List<T>>, S> {
        return object : SuspendableStream<Pair<K, List<T>>, S>() {
            private var forcedNextItem: Holder<Pair<K, List<T>>?>? = null
            private var forcedState: Holder<S?>? = null

            override suspend fun poll(): Pair<K, List<T>>? {
                forcedNextItem?.also {
                    forcedNextItem = null
                    forcedState = null
                    return it.value
                }

                val item = <EMAIL>() ?: return null
                val state = keyExtractor.invoke(item)

                val list = ArrayList<T>()
                list.add(item)

                while (true) {
                    val candidate = <EMAIL>() ?: break
                    if (keyExtractor.invoke(candidate) == state) {
                        <EMAIL>()
                        list.add(candidate)
                    } else {
                        break
                    }
                }

                return if (list.isEmpty()) {
                    null
                } else {
                    state to list
                }
            }

            override suspend fun peek(): Pair<K, List<T>>? {
                forcedNextItem?.also {
                    return it.value
                }
                forcedState = Holder(state())
                val item = poll()
                forcedNextItem = Holder(item)
                return item
            }

            override suspend fun prefetch() {
                <EMAIL>()
            }

            override fun state(): S? {
                forcedState?.also {
                    return it.value
                }
                return <EMAIL>()
            }

            override fun close() {
                <EMAIL>()
            }

            override fun streamId(): String? = <EMAIL>()
        }
    }

    /**
     * As a result of this call, the stream will not return more than limit items.
     */
    fun limit(limit: Int): SuspendableStream<T, S> {
        return object : SuspendableStream<T, S>() {
            private var forcedNextItem: Holder<T?>? = null
            private var forcedState: Holder<S?>? = null
            private var pollCount = 0

            override suspend fun poll(): T? {
                forcedNextItem?.also {
                    forcedNextItem = null
                    forcedState = null
                    return it.value
                }

                if (pollCount == limit) {
                    // close stream earlier
                    close()
                    return null
                }
                val item = <EMAIL>()
                if (item != null) pollCount++
                return item
            }

            override suspend fun peek(): T? {
                forcedNextItem?.also {
                    return it.value
                }
                forcedState = Holder(state())
                val item = poll()
                forcedNextItem = Holder(item)
                return item
            }

            override suspend fun prefetch() {
                <EMAIL>()
            }

            override fun state(): S? {
                forcedState?.also {
                    return it.value
                }
                return <EMAIL>()
            }

            override fun streamId(): String? = <EMAIL>()

            override fun close() {
                <EMAIL>()
            }
        }
    }

    fun asFlow(): Flow<T> =
        flow {
            while (coroutineContext.isActive) {
                emit(poll() ?: break)
            }
        }

    class Holder<X>(
        var value: X?,
    )

    interface State {
        fun serialize(): String
    }
}
