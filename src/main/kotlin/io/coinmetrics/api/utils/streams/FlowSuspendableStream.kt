package io.coinmetrics.api.utils.streams

import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.CoroutineStart
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.async
import kotlinx.coroutines.cancel
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.channels.ClosedReceiveChannelException
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.buffer
import kotlinx.coroutines.flow.produceIn
import kotlin.coroutines.ContinuationInterceptor
import kotlin.coroutines.CoroutineContext

/**
 * {@inheritDoc}
 *
 * @param coroutineContext Flow collection will be automatically cancelled if that context becomes completed. Usually, we need to use a coroutineContext from an HttpRequest to prevent memory leaks upon unexpected TCP disconnections.
 * @param coroutineDispatcher A dispatcher to collect flow in. Here, you can pass a processing dispatcher instead of a NIO dispatcher from HttpRequest's coroutineContext.
 */
class FlowSuspendableStream<T, S : SuspendableStream.State>(
    coroutineContext: CoroutineContext,
    coroutineDispatcher: CoroutineDispatcher = coroutineContext[ContinuationInterceptor] as CoroutineDispatcher,
    private val initialState: S?,
    flow: Flow<T>,
    private val stateResolver: (T) -> S,
    private val streamId: String? = null,
) : SuspendableStream<T, S>() {
    private var lastNonNullPollResult: T? = null

    private var forcedNextItem: Holder<T?>? = null
    private var forcedState: Holder<S?>? = null

    // this cryptic line creates a new scope as a child for the given coroutineContext
    // if coroutineContext is cancelled, this scope will also be cancelled and the following channel be closed
    // but if any tasks of the scope are cancelled, the parent coroutineContext will remain active due to SupervisorJob
    private val scope = CoroutineScope(coroutineContext + coroutineDispatcher + SupervisorJob(coroutineContext[Job]))

    // it's a standard Kotlin pattern on how to convert flow to channel
    // unfortunately, there is no other easier ways of getting an iterator-like interface from flows
    private val channel by lazy {
        scope.async(start = CoroutineStart.LAZY) {
            flow.buffer(Channel.RENDEZVOUS).produceIn(scope)
        }
    }

    override fun streamId(): String? = streamId

    override suspend fun poll(): T? {
        forcedNextItem?.also {
            forcedNextItem = null
            forcedState = null
            return it.value
        }

        return try {
            val item = channel.await().receive()
            lastNonNullPollResult = item
            item
        } catch (e: ClosedReceiveChannelException) {
            // normal closure
            null
        }
    }

    override suspend fun peek(): T? {
        forcedNextItem?.also {
            return it.value
        }
        forcedState = Holder(state())
        val item = poll()
        forcedNextItem = Holder(item)
        return item
    }

    override suspend fun prefetch() {
        // Prefetching works only for Postgres data sources.
        // At the moment, MINIO data streams ignore them because it can lead to a deadlock caused by MemoryLimitService when we try to prefetch more data than we can fit into memory.
        // TODO: later, come up with safe prefetching logic for MINIO
    }

    override fun state(): S? {
        forcedState?.also {
            return it.value
        }
        return lastNonNullPollResult?.let { stateResolver.invoke(it) } ?: initialState
    }

    override fun close() {
        // stop flow collection
        // it's only useful if we want to do it in the middle of flow collection due to client disconnect or an applied "limit" operator.
        // if you don't call this method after "limit", you will get a memory leak since the flow will not be garbage collected.
        scope.cancel()
    }

    private class Holder<X>(
        var value: X?,
    )
}
