package io.coinmetrics.api.utils.streams.operations

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.models.PagingFrom
import io.coinmetrics.api.utils.paging.PagingUtils
import io.coinmetrics.api.utils.streams.SuspendableStream
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.CoroutineStart
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.async
import kotlinx.coroutines.cancel
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.buffer
import kotlinx.coroutines.flow.produceIn
import java.util.Collections
import java.util.IdentityHashMap
import kotlin.coroutines.ContinuationInterceptor
import kotlin.coroutines.CoroutineContext

/**
 * @param alreadyEmitted if null, the "emitted" functionality is disabled. This feature is needed only if limit_per_<entity> is used.
 */
fun <T> Flow<SuspendableStream<T, *>>.concat(
    coroutineContext: CoroutineContext,
    coroutineDispatcher: CoroutineDispatcher = coroutineContext[ContinuationInterceptor] as CoroutineDispatcher,
    firstStreamIndex: Int = 0,
    alreadyEmitted: Long? = null,
): SuspendableStream<T, ConcatState> {
    return object : SuspendableStream<T, ConcatState>() {
        // this cryptic line creates a new scope as a child for the given coroutineContext
        // if coroutineContext is cancelled, this scope will also be cancelled and the following channel be closed
        // but if any tasks of the scope are cancelled, the parent coroutineContext will remain active due to SupervisorJob
        private val scope = CoroutineScope(coroutineContext + coroutineDispatcher + SupervisorJob(coroutineContext[Job]))

        // convert the flow to a channel
        private val channel by lazy {
            scope.async(start = CoroutineStart.LAZY) {
                <EMAIL>(Channel.RENDEZVOUS).produceIn(scope)
            }
        }

        // fields modified by pollInternal
        private val activeStreams = Collections.newSetFromMap(IdentityHashMap<SuspendableStream<*, *>, Boolean>())
        private var internalStream: SuspendableStream<T, *>? = null
        private var internalStreamIndex: Int = firstStreamIndex - 1
        private var noMoreData = false

        // fields modified by "poll"
        private var lastStreamEmitted = alreadyEmitted
        private var lastStreamIndex: Int? = null
        private var lastStream: SuspendableStream<*, *>? = null

        // these two fields are set in the same time
        private var forcedNextItem: Holder<Triple<T, Int, SuspendableStream<*, *>>?>? = null
        private var forcedState: Holder<ConcatState?>? = null

        override suspend fun poll(): T? {
            val (itemValue, itemStreamIndex, itemStream) = pollInternal() ?: return null
            if (lastStreamIndex == null) {
                lastStreamIndex = itemStreamIndex
                lastStream = itemStream
                lastStreamEmitted?.also {
                    lastStreamEmitted = it + 1
                }
            } else if (itemStreamIndex == lastStreamIndex) {
                lastStreamEmitted?.also {
                    lastStreamEmitted = it + 1
                }
            } else {
                // lastStreamIndex != itemStreamIndex
                lastStreamIndex = itemStreamIndex
                lastStream = itemStream
                if (alreadyEmitted != null) {
                    lastStreamEmitted = 1
                }
            }
            return itemValue
        }

        private suspend fun pollInternal(): Triple<T, Int, SuspendableStream<*, *>>? {
            forcedNextItem?.also {
                forcedNextItem = null
                forcedState = null
                return it.value
            }

            if (noMoreData) return null

            while (true) {
                val stream =
                    internalStream ?: run {
                        val result = channel.await().receiveCatching()
                        if (result.isClosed) {
                            noMoreData = true
                            val exception = result.exceptionOrNull()
                            if (exception != null) throw exception
                            return null
                        } else {
                            internalStream?.also {
                                it.close()
                                activeStreams.remove(it)
                            }
                            val stream = result.getOrNull() ?: error("Must not be null.")
                            internalStreamIndex++
                            activeStreams.add(stream)
                            internalStream = stream
                            stream
                        }
                    }
                val value = stream.poll()
                if (value == null) {
                    internalStream?.also {
                        it.close()
                        activeStreams.remove(it)
                        internalStream = null
                    }
                } else {
                    return Triple(value, internalStreamIndex, stream)
                }
            }
        }

        override suspend fun peek(): T? {
            forcedNextItem?.also {
                return it.value?.first
            }
            forcedState = Holder(state())
            val holder = Holder<Triple<T, Int, SuspendableStream<*, *>>?>(pollInternal())
            forcedNextItem = holder
            return holder.value?.first
        }

        override suspend fun prefetch() {
            // ignore
        }

        override fun state(): ConcatState? {
            forcedState?.also {
                return it.value
            }
            val stream = lastStream ?: error("state() must not be called before the first poll.")
            return ConcatState(
                activeStreamIndex = lastStreamIndex ?: error("lastStreamIndex must not be null."),
                activeStreamEmitted = lastStreamEmitted,
                activeStreamState = stream.state(),
                activeStreamId = stream.streamId(),
            )
        }

        override fun close() {
            scope.cancel()
            activeStreams.forEach { it.close() }
            activeStreams.clear()
        }

        override fun streamId(): String? = null
    }
}

class ConcatState(
    val activeStreamIndex: Int?,
    val activeStreamEmitted: Long?,
    val activeStreamState: SuspendableStream.State?,
    /**
     * mutually exclusive with activeStreamIndex.
     * @see ConcatStateSerializer.deserialize
     */
    val activeStreamId: String? = null,
) : SuspendableStream.State {
    override fun serialize() = ConcatStateSerializer.serialize(this)
}

data class ConcatStateDeserialized(
    val activeStreamIndex: Int,
    val activeStreamEmitted: Long?,
    val activeStreamState: String?,
)

object ConcatStateSerializer {
    private const val SEPARATOR = '.'

    fun serialize(state: ConcatState): String {
        val activeStreamStateSerialized = state.activeStreamState?.serialize() ?: ""
        val streamId =
            state.activeStreamId?.let {
                PagingUtils.base64Encoder.encodeToString(it.toByteArray())
            } ?: state.activeStreamIndex.toString()
        return "${streamId}$SEPARATOR$activeStreamStateSerialized" + (state.activeStreamEmitted?.let { "$SEPARATOR$it" } ?: "")
    }

    /**
     * streamIds should be set to a non-null value only if wildcards are used.
     *
     * In the case of wildcards, API doesn't know the list of requested entities and has to expand wildcards in runtime.
     * It means when API processes requests for different pages the results of wildcard expansion can be potentially
     * different for different pages. So we use a different pageToken serialization algorithm to prevent unexpected
     * results in this case.
     *
     * @param streamIds must be non-null if wildcards are used to resolve the list of entities(streamIds). Also, it
     * must be sorted (asc or desc).
     * @param pagingFrom must be set if streamIds is set.
     */
    fun deserialize(
        pageToken: String?,
        streamIds: List<String>? = null,
        pagingFrom: PagingFrom = PagingFrom.START,
    ): FunctionResult<ApiError, ConcatStateDeserialized> {
        if (pageToken == null) return FunctionResult.Success(ConcatStateDeserialized(0, 0, null))
        val pageTokenComponents = pageToken.split(SEPARATOR)
        if (pageTokenComponents.size != 2 && pageTokenComponents.size != 3) {
            return FunctionResult.Failure(ApiError.BadParameter("next_page_token"))
        }

        val activeStreamIndex =
            if (streamIds != null) {
                /**
                 * We try to deserialize the id of the stream (String) instead of the stream numeric index.<br/>
                 * This string streamId is used to get the right index in the dynamically resolved (due to wildcards) streamIds.
                 */
                val streamIdFromPageToken =
                    try {
                        PagingUtils.base64Decoder.decode(pageTokenComponents[0]).decodeToString()
                    } catch (e: Exception) {
                        return FunctionResult.Failure(ApiError.BadParameter("next_page_token"))
                    }
                // Potentially, streamIds can have many elements (>100k), and it's expensive to search thought it by iteration
                // to find an index. So we use binary search here to speed up the process.
                if (streamIds.isEmpty()) {
                    return FunctionResult.Failure(ApiError.BadParameter("next_page_token"))
                } else if (streamIds.size == 1) {
                    0
                } else {
                    // "if" is used for a reason (there are two different implementations of binarySearch here)
                    val index =
                        if (pagingFrom == PagingFrom.START) {
                            streamIds.binarySearch(streamIdFromPageToken)
                        } else {
                            streamIds.binarySearch(streamIdFromPageToken, comparator = Comparator.reverseOrder())
                        }
                    index.takeIf { it >= 0 } ?: return FunctionResult.Failure(ApiError.BadParameter("next_page_token"))
                }
            } else {
                pageTokenComponents[0].toIntOrNull() ?: return FunctionResult.Failure(ApiError.BadParameter("next_page_token"))
            }

        val activeStreamState = pageTokenComponents[1].let { it.ifEmpty { null } }
        val activeStreamEmitted =
            if (pageTokenComponents.size == 3) {
                pageTokenComponents[2].toLongOrNull() ?: return FunctionResult.Failure(ApiError.BadParameter("next_page_token"))
            } else {
                null
            }

        return FunctionResult.Success(
            ConcatStateDeserialized(
                activeStreamIndex = activeStreamIndex,
                activeStreamEmitted = activeStreamEmitted,
                activeStreamState = activeStreamState,
            ),
        )
    }
}
