package io.coinmetrics.api.utils

import io.coinmetrics.httpserver.HttpRequest
import org.slf4j.MDC

val HttpRequest.mdc: Map<String, String>
    get() {
        val mdc = LinkedHashMap<String, String>()
        mdc["clientIp"] = clientIp
        mdc["path"] = path
        queryParameters["api_key"]?.also { mdc["apiKey"] = it }
        headers["cf-ray"]?.also { mdc["cfRay"] = it }
        return mdc
    }

/**
 * Important: Do not make this function `inline` as it will not work correctly in suspend context.
 */
fun <T> Map<String, String>.withMDC(block: () -> T): T {
    val old = MDC.getCopyOfContextMap()
    MDC.setContextMap(this)
    return try {
        block()
    } finally {
        if (old == null) {
            MDC.clear()
        } else {
            MDC.setContextMap(old)
        }
    }
}
