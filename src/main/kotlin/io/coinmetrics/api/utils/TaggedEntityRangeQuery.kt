package io.coinmetrics.api.utils

import io.coinmetrics.api.model.tagging.TaggedEntity
import io.coinmetrics.api.utils.paging.TaggedEntityPageToken

class TaggedEntityRangeQuery(
    val startKey: TaggedEntity.Key?,
    startInclusive: Boolean = true,
    val pagingFromStart: Boolean = true,
) : RangeQuery<TaggedEntityRangeQuery, TaggedEntityPageToken>(startInclusive, false) {
    companion object {
        fun createRangeQuery(
            startKey: TaggedEntity.Key? = null,
            startInclusive: Boolean = true,
        ): TaggedEntityRangeQuery =
            TaggedEntityRangeQuery(
                startKey = startKey,
                startInclusive = startInclusive,
            )
    }

    override fun withPageToken(pageToken: TaggedEntityPageToken?): TaggedEntityRangeQuery {
        if (pageToken == null) return this
        return TaggedEntityRangeQuery(startKey = pageToken.key)
    }
}
