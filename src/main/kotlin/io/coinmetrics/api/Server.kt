package io.coinmetrics.api

import ch.qos.logback.classic.ClassicConstants
import io.coinmetrics.api.modules.ApiModule
import io.coinmetrics.api.modules.ModuleName
import io.coinmetrics.api.modules.common.CommonConfig
import io.coinmetrics.api.modules.common.CommonModule
import io.coinmetrics.api.modules.main.MainApiModule
import io.coinmetrics.api.modules.streamingbooks.StreamingBooksApiModule
import io.coinmetrics.api.modules.streamingtrades.StreamingTradesApiModule
import io.coinmetrics.httpserver.HttpServer
import io.coinmetrics.httpserver.HttpServerConfig
import io.coinmetrics.httpserver.NetThreadPoolStatisticsConsumer
import io.coinmetrics.httpserver.ProcessingThreadPoolStatisticsConsumer
import io.coinmetrics.httpserver.impl.VertxHttpServerImpl
import kotlinx.coroutines.runBlocking
import org.slf4j.LoggerFactory
import java.lang.management.ManagementFactory
import java.nio.file.Paths
import java.util.TimeZone
import java.util.concurrent.atomic.AtomicBoolean
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine
import kotlin.system.exitProcess

class Server(
    val commonModule: CommonModule,
    otherModules: List<ApiModule>? = null,
) {
    val modules = listOf(commonModule) + (otherModules ?: emptyList())

    private val requestHandler = HttpRequestHandlerImpl(commonModule, modules)

    private val log = LoggerFactory.getLogger(Server::class.java)

    private val transport: HttpServer

    init {
        val netThreadPoolStatisticsConsumer =
            object : NetThreadPoolStatisticsConsumer {
                override fun onQueuePending(
                    threadNum: Int,
                    queuePending: Int,
                ) {
                    commonModule.monitoring.netThreadQueuePending
                        .labelValues(threadNum.toString())
                        .set(queuePending.toDouble())
                }

                override fun onQueueTime(
                    threadNum: Int,
                    queueTimeNs: Long,
                ) {
                    val queueTimeSec = queueTimeNs.toDouble() / 1_000_000_000
                    commonModule.monitoring.netThreadQueueTime
                        .labelValues(threadNum.toString())
                        .observe(queueTimeSec)
                }
            }
        val processingThreadPoolStatisticsConsumer =
            object : ProcessingThreadPoolStatisticsConsumer {
                override fun onQueuePending(queuePending: Int) {
                    commonModule.monitoring.processingThreadQueuePending.set(queuePending.toDouble())
                }

                override fun onQueueTime(queueTimeNs: Long) {
                    val queueTimeSec = queueTimeNs.toDouble() / 1_000_000_000
                    commonModule.monitoring.processingThreadQueueTime.observe(queueTimeSec)
                }
            }

        val commonConfig = commonModule.config
        transport =
            VertxHttpServerImpl(
                config =
                    HttpServerConfig(
                        host = commonConfig.host,
                        port = commonConfig.port,
                        netThreads = commonConfig.netThreads,
                        processingThreads = commonConfig.processingThreads,
                        inTest = commonConfig.inTest,
                        netThreadPoolStatisticsConsumer = netThreadPoolStatisticsConsumer,
                        processingThreadPoolStatisticsConsumer = processingThreadPoolStatisticsConsumer,
                        // Body size is limited by NGINX.
                        maxFormAttributesSize = Int.MAX_VALUE - 1000,
                    ),
                httpRequestHandler = requestHandler,
                nonOkResponseHandler = requestHandler,
            )
    }

    suspend fun start(): Server {
        modules.forEach { module ->
            log.info("Starting module: {}", module.name)
            module.start()
        }
        transport.start()
        return this
    }

    fun actualPort(): Int? = transport.actualPort

    suspend fun close(exitOnClose: Boolean = false) {
        // ordering is matter for graceful shutdown
        try {
            transport.close()
        } catch (e: Exception) {
            log.error("Error closing transport", e)
        }
        try {
            modules.reversed().forEach { module ->
                log.info("Closing module: {}", module.name)
                module.close()
            }
        } catch (e: Exception) {
            log.error("Error closing modules", e)
        }

        if (exitOnClose) {
            exitProcess(1)
        }
    }
}

fun main() =
    runBlocking {
        TimeZone.setDefault(TimeZone.getTimeZone("UTC"))
        if (System.getenv("API_ENV") == "production") {
            System.setProperty(ClassicConstants.CONFIG_FILE_PROPERTY, "production/logback.xml")
        }

        val commonConfig = CommonConfig()
        val commonModule = CommonModule(commonConfig)

        val log = LoggerFactory.getLogger(Server::class.java)

        // Ensure process is killed on any critical error.
        val haltedOnCriticalError = AtomicBoolean()
        Thread.setDefaultUncaughtExceptionHandler { t, e ->
            val shouldHalt = e is VirtualMachineError && haltedOnCriticalError.compareAndSet(false, true)
            try {
                log.error("Uncaught exception in thread \"${t.name}\"", e)
                if (shouldHalt) {
                    log.error("Critical error, JVM will halt", e)
                }
            } finally {
                if (shouldHalt) {
                    Runtime.getRuntime().halt(1)
                }
            }
        }

        log.info("Current directory: {}", Paths.get("").toAbsolutePath())
        log.info("Environment: {}", System.getenv())
        log.info("Command line parameters: {}", ManagementFactory.getRuntimeMXBean().inputArguments.joinToString(separator = " "))
        log.info("Configuration: {}", commonConfig)

        val server =
            Server(
                commonModule,
                otherModules =
                    commonModule.config.modules.mapNotNull { moduleName ->
                        when (moduleName) {
                            ModuleName.MAIN -> MainApiModule(commonModule)
                            ModuleName.STREAMING_BOOKS -> StreamingBooksApiModule(commonModule)
                            ModuleName.STREAMING_TRADES -> StreamingTradesApiModule(commonModule)
                            else -> null
                        }
                    },
            )

        try {
            server.start()
            log.info(
                "Server started on {}:{} ({} threads)",
                commonConfig.host,
                server.actualPort(),
                commonConfig.netThreads,
            )
            suspendCoroutine { cont ->
                Runtime.getRuntime().addShutdownHook(
                    Thread {
                        runBlocking {
                            server.close()
                            log.info("Server stopped")
                            cont.resume(Unit)
                        }
                    },
                )
            }
        } catch (e: Throwable) {
            log.error("Can't start server. Server will close and then exit. Error: ", e)
            server.close(false)
        }
    }
