package io.coinmetrics.api.modules.main

import io.coinmetrics.api.config.StaticTierConfig
import io.coinmetrics.api.config.parseTiers
import io.coinmetrics.api.model.S3BooksMarketType
import io.coinmetrics.api.modules.common.CommonConfig
import io.coinmetrics.api.modules.common.CommonConfig.Companion.defaultEnvVariablesResolver
import io.coinmetrics.api.modules.main.MainApiConfig.Companion.BOOKS_CONFIG_NAME
import io.coinmetrics.api.modules.main.MainApiConfig.Companion.LIQUIDATIONS_CONFIG_NAME
import io.coinmetrics.api.modules.main.MainApiConfig.Companion.OPEN_INTEREST_CONFIG_NAME
import io.coinmetrics.api.modules.main.MainApiConfig.Companion.PRINCIPAL_PRICE_CONFIG_NAME
import io.coinmetrics.api.modules.main.MainApiConfig.Companion.RATES_CONFIG_NAME
import io.coinmetrics.api.persistence.DatabasesConfig
import io.coinmetrics.api.resources.Resources
import org.slf4j.LoggerFactory
import java.nio.file.Files
import java.nio.file.Path
import java.nio.file.Paths
import java.time.Duration
import java.util.EnumMap
import java.util.concurrent.TimeUnit
import kotlin.io.path.Path
import kotlin.time.toJavaDuration

private val log = LoggerFactory.getLogger(MainApiConfig::class.java)

data class MainApiConfig(
    val common: CommonConfig,
    val apiDataBaseDir: Path =
        run {
            val directory = common.envVariablesResolver("API_DATA_PATH") ?: "data"
            Paths.get(directory).toAbsolutePath()
        },
    val envVariablesProvider: () -> Map<String, String> = { System.getenv() },
    val databases: DatabasesConfig = DatabasesConfig(common.envVariablesResolver, envVariablesProvider, common.env),
    val taxonomyDataLoad: Boolean = true,
    val taxonomyDataLoadConfig: DataLoadConfig =
        DataLoadConfig(
            baseDir = apiDataBaseDir,
            dataContentDir = apiDataBaseDir.resolve("taxonomy-data"),
            dataRepoUrl = "**************:coinmetrics/data-delivery/taxonomy/taxonomy-data.git",
            dataCommitHash = common.envVariablesResolver("API_TAXONOMY_DATA_COMMIT_HASH") ?: "origin/master",
        ),
    val assetProfilesDataLoad: Boolean = true,
    val assetProfilesDataLoadConfig: DataLoadConfig =
        DataLoadConfig(
            baseDir = apiDataBaseDir,
            dataContentDir = apiDataBaseDir.resolve("asset-profiles-data"),
            dataRepoUrl = "**************:coinmetrics/data-delivery/asset-profiles-data.git",
            dataCommitHash = common.envVariablesResolver("API_ASSET_PROFILES_DATA_COMMIT_HASH") ?: "origin/master",
        ),
    val networkProfilesDataLoad: Boolean = true,
    val networkProfilesDataLoadConfig: DataLoadConfig = assetProfilesDataLoadConfig,
    val securityMasterDataLoad: Boolean = true,
    val securityMasterDataLoadConfig: DataLoadConfig =
        DataLoadConfig(
            baseDir = apiDataBaseDir,
            dataContentDir = apiDataBaseDir.resolve("security-master-data"),
            dataRepoUrl = "**************:coinmetrics/data-delivery/security-master-data.git",
            dataCommitHash = common.envVariablesResolver("API_SECURITY_MASTER_DATA_COMMIT_HASH") ?: "origin/master",
        ),
    val marketClientConnectionStatisticsUpdateIntervalMs: Long = 60 * 1000L,
    val ratesConfigs: List<Pair<String, KafkaSourceConfig>> = resolveReferenceRatesKafkaConfigs(common.env, common.envVariablesResolver),
    val principalPriceConfigs: List<KafkaSourceConfig> = resolvePrincipalPriceKafkaConfigs(common.env, common.envVariablesResolver),
    val candlesConfig: KafkaSourceConfig =
        KafkaSourceConfig(
            configName = CANDLES_CONFIG_NAME,
            topicName = "realtime_candles_all.proto",
            env = common.env,
            envVariablesResolver = common.envVariablesResolver,
            batchSize = 10_000,
            // Data in Kafka appears in bursts (every minute)
            mainDataSourceSilentLimitMs = TimeUnit.MINUTES.toMillis(1) + TimeUnit.SECONDS.toMillis(10),
            nonMainDataSourceForcedLagMs = TimeUnit.MINUTES.toMillis(1) + TimeUnit.SECONDS.toMillis(40),
            nonMainDataSourceLagToConsiderActiveMs = TimeUnit.MINUTES.toMillis(2),
        ),
    val candlesAllowedTimeSpentFromTheLastSwitchMs: Long = 4000,
    val kafkaLiquidationsConfigsPerExchange: List<Triple<Resources.Exchange, KafkaSourceConfig, Int?>> =
        createKafkaLiquidationsConfigs(common.env, common.envVariablesResolver),
    val kafkaOpenInterestConfigsPerExchange: List<Triple<Resources.Exchange, KafkaSourceConfig, Int?>> =
        createKafkaOpenInterestConfigs(common.env, common.envVariablesResolver),
    val indexLevelsConfigs: List<KafkaSourceConfig> =
        listOf(
            "cmbi_realtime_all",
            "cmbi_index_realtime_all",
            "cmbi_multi_index_1s",
        ).map { topic ->
            KafkaSourceConfig(
                configName = INDEX_LEVELS_CONFIG_NAME,
                topicName = topic,
                env = common.env,
                envVariablesResolver = common.envVariablesResolver,
                batchSize = 10_000,
            )
        },
    // size should be enough to cover 30 min of realtime trades
    val tradeKeysCacheSize: Int = (common.envVariablesResolver("API_TRADE_KEYS_CACHE_SIZE") ?: "12000000").toInt(),
    val kafkaQuotesConfigs: List<KafkaSourceConfig> =
        createQuotesConfigs(
            common.env,
            common.envVariablesResolver,
        ),
    val kafkaPairQuotesConfig: KafkaSourceConfig =
        KafkaSourceConfig(
            configName = PAIR_QUOTES_CONFIG_NAME,
            topicName = "metrics_liquidity_aggregated_spread_pairs",
            env = common.env,
            envVariablesResolver = common.envVariablesResolver,
            batchSize = 10_000,
        ),
    val kafkaAssetQuotesConfig: KafkaSourceConfig =
        KafkaSourceConfig(
            configName = ASSET_QUOTES_CONFIG_NAME,
            topicName = "metrics_liquidity_aggregated_spread_assets",
            env = common.env,
            envVariablesResolver = common.envVariablesResolver,
            batchSize = 10_000,
        ),
    val kafkaBooksConfigsPerExchange: List<BooksKafkaSourceConfig> =
        createKafkaBooksConfigs(common.env, common.envVariablesResolver),
    val realtimeMetricsUpdateFrequencyMs: Long =
        (
            common.envVariablesResolver(
                "API_REALTIME_METRICS_UPDATE_FREQUENCY_MS",
            ) ?: "1000"
        ).toLong(),
    val tenPercentMidPriceBookStartTime: String = (
        common.envVariablesResolver("API_TEN_PERCENT_MID_PRICE_BOOK_START_TIME")
            ?: "2022-10-19T19:00:00.000000000Z"
    ),
    val catalogV1MarketsLimit: Int = (common.envVariablesResolver("API_CATALOG_V1_MARKETS_LIMIT") ?: "170000").toInt(),
    val booksTiers: Map<S3BooksMarketType, List<StaticTierConfig>> =
        listOf(
            S3BooksMarketType.SPOT_BOOKS,
            S3BooksMarketType.FUTURES_BOOKS,
            S3BooksMarketType.OPTIONS_BOOKS,
        ).associateWithTo(EnumMap(S3BooksMarketType::class.java)) { dataType ->
            val tiersString = common.envVariablesResolver("API_${dataType}_TIERS") ?: ""
            tiersString.parseTiers(common.envVariablesResolver, dataType.toString())
        },
    // 1 GB by default
    val totalS3QueryMemoryLimit: Long = (common.envVariablesResolver("API_TOTAL_S3_QUERY_MEMORY_LIMIT_BYTES") ?: "1073741824").toLong(),
    val useNewBooksTables: Boolean = (common.envVariablesResolver("API_USE_NEW_BOOKS_TABLES") ?: "false").toBoolean(),
    val assetMetricConstituentsConfig: AssetMetricConstituentsConfig =
        AssetMetricConstituentsConfig(
            volumeTrustedSpotFileName = "volume_trusted_spot_constituents.json",
        ),
    val dellPowerScaleBugsWorkarounds: Boolean = (common.envVariablesResolver("API_POWERSCALE_WORKAROUNDS") ?: "false").toBoolean(),
    val docsBaseUrl: String = common.envVariablesResolver("API_DOCS_BASE_URL") ?: "https://docs.coinmetrics.io",
    val temporalConfig: TemporalConfig? = resolveTemporalConfig(common.envVariablesResolver),
    // Experimental asset/metrics
    val ndExperimentalAssetMetricsFile: String = "/network_data_experimental_asset_metrics.json",
    val ndExperimentalAtlasFile: String = "/network_data_experimental_atlas.json",
) {
    companion object {
        const val RATES_CONFIG_NAME = "RATES"
        const val PRINCIPAL_PRICE_CONFIG_NAME = "PRINCIPAL_PRICE"
        const val CANDLES_CONFIG_NAME = "CANDLES"
        const val TRADES_CONFIG_NAME = "TRADES"
        const val LIQUIDATIONS_CONFIG_NAME = "LIQUIDATIONS"
        const val OPEN_INTEREST_CONFIG_NAME = "OPEN_INTERESTS"
        const val MARKET_QUOTES_CONFIG_NAME = "MARKET_QUOTES"
        const val PAIR_QUOTES_CONFIG_NAME = "PAIR_QUOTES"
        const val ASSET_QUOTES_CONFIG_NAME = "ASSET_QUOTES"
        const val BOOKS_CONFIG_NAME = "BOOKS"
        const val INDEX_LEVELS_CONFIG_NAME = "INDEX_LEVELS"

        private val defaultPollDuration = Duration.ofMillis(200)
        private const val DEFAULT_REQUEST_TIMEOUT_MS = 30_000
    }

    data class KafkaConfig(
        val configName: String,
        private val env: String,
        val batchSize: Int = 100,
        val envVariablesResolver: (String) -> String? = defaultEnvVariablesResolver,
        // must be unique for all clients. It makes sense only if we commit offsets.
        val groupName: String = "api4-${envVariablesResolver("HOSTNAME") ?: "localhost"}-$env",
        val serverUrl: String,
        val maxPartitionFetchBytes: Int = 1_048_576,
        val pollDuration: Duration = defaultPollDuration,
        val requestTimeoutMs: Int = DEFAULT_REQUEST_TIMEOUT_MS,
    ) {
        fun toListOfAllKafkaServers(): List<KafkaConfig> {
            // if the kafka name contains a comma, it means we defined a list of servers to connect to
            val kafkaServers = serverUrl.split(',').map { it.trim() }.filter { it.isNotEmpty() }

            return kafkaServers.map { serverUrl ->
                this.copy(
                    serverUrl = serverUrl,
                )
            }
        }
    }

    data class KafkaSourceConfig(
        val configName: String,
        private val env: String,
        val envVariablesResolver: (String) -> String? = defaultEnvVariablesResolver,
        val topicName: String,
        val batchSize: Int = (envVariablesResolver("API_${configName}_BATCH_SIZE") ?: "1000").toInt(),
        val maxPartitionFetchBytes: Int = (envVariablesResolver("API_${configName}_MAX_PARTITION_FETCH_BYTES") ?: "1048576").toInt(),
        // if we can't find 'KAFKA_{NAME}' we fall back to the general 'KAFKA' env variable
        val serverUrl: String = envVariablesResolver("KAFKA_$configName") ?: envVariablesResolver("KAFKA") ?: "",
        val pollDuration: Duration = defaultPollDuration,
        val requestTimeoutMs: Int = DEFAULT_REQUEST_TIMEOUT_MS,
        val kafkaServers: List<KafkaConfig> =
            KafkaConfig(
                configName = configName,
                env = env,
                batchSize = batchSize,
                maxPartitionFetchBytes = maxPartitionFetchBytes,
                envVariablesResolver = envVariablesResolver,
                serverUrl = serverUrl,
                pollDuration = pollDuration,
                requestTimeoutMs = requestTimeoutMs,
            ).toListOfAllKafkaServers(),
        val mainDataSourceCheckForSilentIntervalMs: Long =
            (
                envVariablesResolver("API_${configName}_MAIN_DATASOURCE_CHECK_FOR_SILENT_INTERVAL_MS")
                    ?: "1000"
            ).toLong(),
        val mainDataSourceSilentLimitMs: Long =
            (
                envVariablesResolver(
                    "API_${configName}_MAIN_DATASOURCE_SILENT_LIMIT_MS",
                ) ?: "7000"
            ).toLong(),
        val nonMainDataSourceForcedLagMs: Long =
            (
                envVariablesResolver(
                    "API_${configName}_NON_MAIN_DATASOURCE_FORCED_LAG_MS",
                ) ?: "10000"
            ).toLong(),
        // should be nonMainDataSourceForcedLagMs plus some value
        val nonMainDataSourceLagToConsiderActiveMs: Long =
            (
                envVariablesResolver("API_${configName}_NON_MAIN_DATASOURCE_LAG_TO_CONSIDER_ACTIVE_MS")
                    ?: "15000"
            ).toLong(),
    ) {
        init {
            check(nonMainDataSourceLagToConsiderActiveMs > nonMainDataSourceForcedLagMs) {
                log.error(
                    "nonMainDataSourceLagToConsiderActiveMs '$nonMainDataSourceLagToConsiderActiveMs' <= nonMainDataSourceForcedLagMs '$nonMainDataSourceForcedLagMs' for $configName",
                )
            }
            check(mainDataSourceSilentLimitMs < nonMainDataSourceForcedLagMs) {
                log.error(
                    "mainDataSourceSilentLimitMs '$mainDataSourceSilentLimitMs' >= nonMainDataSourceForcedLagMs '$nonMainDataSourceForcedLagMs' for $configName",
                )
            }
            check(mainDataSourceCheckForSilentIntervalMs < mainDataSourceSilentLimitMs) {
                log.error(
                    "mainDataSourceCheckForSilentIntervalMs '$mainDataSourceCheckForSilentIntervalMs' >= mainDataSourceSilentLimitMs '$mainDataSourceSilentLimitMs' for $configName",
                )
            }
        }
    }

    data class DataLoadConfig(
        val baseDir: Path,
        val dataContentDir: Path,
        val dataRepoUrl: String,
        val dataCommitHash: String,
    )

    data class BooksKafkaSourceConfig(
        val config: KafkaSourceConfig,
        val exchange: Resources.Exchange,
    )

    data class AssetMetricConstituentsConfig(
        val volumeTrustedSpotFileName: String,
    )
}

fun String?.createKeyLimitsOverridesMap(): Map<String, Int> =
    this
        ?.split(",")
        ?.associateTo(HashMap()) { keyValueString ->
            val (key, value) =
                keyValueString.split(":").takeIf { it.size == 2 }
                    ?: error("Invalid key-value format specified.")
            val limit = value.trim().toIntOrNull() ?: error("Invalid limit format specified.")
            key.trim() to limit
        } ?: emptyMap()

fun kafkaConfigsPerExchangeToTriples(
    name: String,
    kafkaConfigsPerExchange: List<Triple<Resources.Exchange, MainApiConfig.KafkaSourceConfig, Int?>>,
): List<Triple<Resources.Exchange, MainApiConfig.KafkaSourceConfig, Int>> =
    if (kafkaConfigsPerExchange.isNotEmpty()) {
        kafkaConfigsPerExchange
            .map { (exchange, kafkaSourceConfig, cacheSize) ->
                val nonNullCacheSize = cacheSize ?: 1000
                Triple(exchange, kafkaSourceConfig, nonNullCacheSize)
            }.also { configs ->
                val totalCacheSize = configs.sumOf { (_, _, cacheSize) -> cacheSize }
                log.info("$name deduplication cache total size: $totalCacheSize.")
            }
    } else {
        emptyList()
    }

fun createKafkaBooksConfigs(
    env: String,
    envVariablesResolver: (String) -> String?,
): List<MainApiConfig.BooksKafkaSourceConfig> =
    createMultipleKafkaConfigs(configName = BOOKS_CONFIG_NAME, env = env, envVariablesResolver = envVariablesResolver)
        .map { (exchange, config) ->
            MainApiConfig.BooksKafkaSourceConfig(config, exchange)
        }

private fun createKafkaLiquidationsConfigs(
    env: String,
    envVariablesResolver: (String) -> String?,
): List<Triple<Resources.Exchange, MainApiConfig.KafkaSourceConfig, Int?>> =
    createMultipleKafkaConfigs(
        configName = LIQUIDATIONS_CONFIG_NAME,
        env = env,
        envVariablesResolver = envVariablesResolver,
    )

private fun createKafkaOpenInterestConfigs(
    env: String,
    envVariablesResolver: (String) -> String?,
): List<Triple<Resources.Exchange, MainApiConfig.KafkaSourceConfig, Int?>> =
    createMultipleKafkaConfigs(
        configName = OPEN_INTEREST_CONFIG_NAME,
        env = env,
        envVariablesResolver = envVariablesResolver,
    )

fun createMultipleKafkaConfigs(
    configName: String,
    env: String,
    envVariablesResolver: (String) -> String?,
): List<Triple<Resources.Exchange, MainApiConfig.KafkaSourceConfig, Int?>> =
    generateSequence(0, Int::inc)
        .map { idx ->
            Triple(
                envVariablesResolver("KAFKA_${configName.uppercase()}_$idx"),
                envVariablesResolver("KAFKA_${configName.uppercase()}_${idx}_EXCHANGES"),
                idx,
            )
        }.takeWhile { it.first != null && it.second != null }
        .mapNotNull { (url, exchanges, idx) -> url?.let { exchanges?.let { Triple(url, exchanges, idx) } } }
        .flatMap { (serverUrl, exchangesString, idx) ->
            val exchangeConfigPartsList = exchangesString.split(",").map { it.trim() }
            exchangeConfigPartsList.map { exchangeConfigPart ->
                val exchangeAndOtherProperties = exchangeConfigPart.split(":")
                val exchangeId = exchangeAndOtherProperties[0].toInt()
                val exchange = Resources.getExchangeById(exchangeId).getOrElse { error("Failed to find exchange by id=$exchangeId.") }
                val additionalConfigValue1 = exchangeAndOtherProperties.getOrNull(1)?.toIntOrNull()
                val topicNamePostfix = if (exchange.defi) "" else ".proto"
                val topicName = "${configName.lowercase()}_$exchangeId$topicNamePostfix"
                val batchSize: Int = (envVariablesResolver("KAFKA_${configName.uppercase()}_${idx}_BATCH_SIZE") ?: "10000").toInt()
                val maxPartitionFetchBytes: Int =
                    (
                        envVariablesResolver("KAFKA_${configName.uppercase()}_${idx}_MAX_FETCH_BYTES")
                            ?: (20 * 1_048_576).toString()
                    ).toInt()
                val pollDurationMs: Long = (envVariablesResolver("KAFKA_${configName.uppercase()}_${idx}_POLL_MS") ?: "200").toLong()
                val requestTimeoutMs: Int =
                    (
                        envVariablesResolver(
                            "KAFKA_${configName.uppercase()}_${idx}_REQUEST_TIMEOUT_MS",
                        ) ?: "30000"
                    ).toInt()
                val kafkaSourceConfig =
                    MainApiConfig.KafkaSourceConfig(
                        configName = configName,
                        topicName = topicName,
                        batchSize = batchSize,
                        maxPartitionFetchBytes = maxPartitionFetchBytes,
                        env = env,
                        envVariablesResolver = envVariablesResolver,
                        serverUrl = serverUrl,
                        pollDuration = Duration.ofMillis(pollDurationMs),
                        requestTimeoutMs = requestTimeoutMs,
                    )
                log.info("Kafka config {} for topic {}: {}", idx, topicName, kafkaSourceConfig)
                Triple(exchange, kafkaSourceConfig, additionalConfigValue1)
            }
        }.toList()

fun resolveReferenceRatesKafkaConfigs(
    env: String,
    envVariablesResolver: (String) -> String?,
): List<Pair<String, MainApiConfig.KafkaSourceConfig>> {
    val topicsConfigString = envVariablesResolver("KAFKA_${RATES_CONFIG_NAME.uppercase()}_TOPICS") ?: ""
    val topicConfigs = topicsConfigString.split(",").map { it.trim() }.filter { it.isNotEmpty() }

    fun configError(
        param: String,
        value: List<String>,
    ): Nothing {
        error("Failed to read $param from $value while configuring Reference Rates Kafka source")
    }
    return topicConfigs
        .map { topicConfig ->
            val topicNameToFrequency = topicConfig.split(":").map { it.trim() }
            val topicName = topicNameToFrequency.getOrNull(0) ?: configError("topic name", topicNameToFrequency)
            val frequency = topicNameToFrequency.getOrNull(1) ?: configError("frequency", topicNameToFrequency)
            val kafkaSourceConfig =
                MainApiConfig.KafkaSourceConfig(
                    configName = RATES_CONFIG_NAME,
                    topicName = topicName,
                    batchSize = 8000,
                    maxPartitionFetchBytes = 8 * 1048576,
                    env = env,
                    envVariablesResolver = envVariablesResolver,
                    // Data appears in Kafka every second for real-time rates and every 200 ms for sub-second rates
                    mainDataSourceCheckForSilentIntervalMs = TimeUnit.MILLISECONDS.toMillis(500),
                    mainDataSourceSilentLimitMs = TimeUnit.SECONDS.toMillis(2),
                    nonMainDataSourceForcedLagMs = TimeUnit.SECONDS.toMillis(3),
                    nonMainDataSourceLagToConsiderActiveMs = TimeUnit.SECONDS.toMillis(4),
                )
            Pair(frequency, kafkaSourceConfig)
        }.toList()
}

fun resolvePrincipalPriceKafkaConfigs(
    env: String,
    envVariablesResolver: (String) -> String?,
): List<MainApiConfig.KafkaSourceConfig> {
    val topicsConfigString = envVariablesResolver("KAFKA_${PRINCIPAL_PRICE_CONFIG_NAME.uppercase()}_TOPICS") ?: ""
    val topicNames = topicsConfigString.split(",").map { it.trim() }.filter { it.isNotEmpty() }

    return topicNames.map { topicName ->
        MainApiConfig.KafkaSourceConfig(
            configName = PRINCIPAL_PRICE_CONFIG_NAME,
            topicName = topicName,
            batchSize = 5000,
            env = env,
            envVariablesResolver = envVariablesResolver,
            // Data appears in Kafka every second
            mainDataSourceCheckForSilentIntervalMs = TimeUnit.MILLISECONDS.toMillis(500),
            mainDataSourceSilentLimitMs = TimeUnit.SECONDS.toMillis(2),
            nonMainDataSourceForcedLagMs = TimeUnit.SECONDS.toMillis(3),
            nonMainDataSourceLagToConsiderActiveMs = TimeUnit.SECONDS.toMillis(4),
        )
    }
}

private fun createQuotesConfigs(
    env: String,
    envVariablesResolver: (String) -> String?,
): List<MainApiConfig.KafkaSourceConfig> {
    val quoteDirectServerUrl =
        envVariablesResolver("KAFKA_${MainApiConfig.MARKET_QUOTES_CONFIG_NAME}") ?: envVariablesResolver("KAFKA") ?: ""
    // this is a temporary configuration needed to support quotes from books in legacy Hetzner
    val quotesFromBooksServerUrl = envVariablesResolver("KAFKA_QUOTES_FROM_BOOKS") ?: quoteDirectServerUrl

    return arrayOf(
        quoteDirectServerUrl to
            listOf(
                0, // Bitstamp
                1, // Coinbase
                4, // Binance
                5, // Gemini
                6, // Kraken
                9, // OKEx
                10, // Huobi
                11, // HitBTC
                35, // Binance.US
                37, // Deribit
                42, // Bybit
            ),
        quotesFromBooksServerUrl to
            listOf(
                2, // Bitfinex
                7, // bitFlyer
                20, // Poloniex
                24, // Gate.io
                28, // itBit
                32, // CEX.IO
                34, // BitMEX
                39, // KuCoin
                40, // CME
                41, // LMAX
                48, // Crypto.com
                57, // GFO_X
                59, // dYdX
            ),
    ).map { (serverUrl, exchanges) ->
        exchanges.map {
            MainApiConfig.KafkaSourceConfig(
                configName = MainApiConfig.MARKET_QUOTES_CONFIG_NAME,
                topicName = "quotes_$it",
                env = env,
                envVariablesResolver = envVariablesResolver,
                batchSize = 200_000,
                maxPartitionFetchBytes = 30 * 1_048_576,
                serverUrl = serverUrl,
            )
        }
    }.flatten()
}

private fun resolveTemporalConfig(envVariablesResolver: (String) -> String?): TemporalConfig? {
    val serviceTarget = envVariablesResolver("API_TEMPORAL_SERVICE_TARGET") ?: return null
    val namespace =
        requireNotNull(envVariablesResolver("API_TEMPORAL_NAMESPACE")) {
            "Temporal namespace must be specified."
        }

    val expirationPeriod =
        envVariablesResolver("API_JOB_EXPIRATION_PERIOD")
            ?.let {
                kotlin.time.Duration
                    .parse(it)
                    .toJavaDuration()
            }
            ?: Duration.ofDays(1)

    var crt = envVariablesResolver("API_TEMPORAL_CLIENT_CERTIFICATE_CONTENT")
    var key = envVariablesResolver("API_TEMPORAL_CLIENT_KEY_CONTENT")
    check((crt == null) == (key == null)) {
        "Both API_TEMPORAL_CLIENT_CERTIFICATE_CONTENT and API_TEMPORAL_CLIENT_KEY_CONTENT must be specified or none of them"
    }
    if (crt == null) {
        // Also support loading certificate from files. This is used in Swarm.
        val crtPath = Path("temporal.crt")
        val keyPath = Path("temporal.key")
        crt = if (Files.exists(crtPath)) Files.readString(crtPath) else null
        key = if (Files.exists(keyPath)) Files.readString(keyPath) else null
        check((crt == null) == (key == null)) {
            "Both $crtPath and $keyPath files must be present or none of them"
        }
    }
    val sslConfig =
        if (crt != null && key != null) {
            TemporalSslConfig(crt, key)
        } else {
            null
        }

    val threadCount = envVariablesResolver("API_TEMPORAL_THREAD_COUNT")?.toInt() ?: 16

    return TemporalConfig(serviceTarget, namespace = namespace, expirationPeriod, sslConfig, threadCount)
}

data class TemporalConfig(
    val serviceTarget: String,
    val namespace: String,
    val expirationPeriod: Duration,
    val sslConfig: TemporalSslConfig? = null,
    val threadCount: Int,
)

data class TemporalSslConfig(
    val clientCertFileContent: String,
    val clientKeyFileContent: String,
)
