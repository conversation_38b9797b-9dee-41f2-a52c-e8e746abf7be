package io.coinmetrics.api.modules.main

import com.google.common.collect.Range
import io.coinmetrics.api.ApiError
import io.coinmetrics.api.Paths
import io.coinmetrics.api.Response
import io.coinmetrics.api.Router
import io.coinmetrics.api.ams.DiscoveryResult
import io.coinmetrics.api.endpoints.Endpoint
import io.coinmetrics.api.endpoints.blockchain.full.GetBlockchainV2FullBlockEndpointImpl
import io.coinmetrics.api.endpoints.blockchain.full.GetBlockchainV2FullTransactionEndpointImpl
import io.coinmetrics.api.endpoints.blockchain.full.GetBlockchainV2FullTransactionForBlockEndpointImpl
import io.coinmetrics.api.endpoints.blockchain.job.PutBlockchainJobAccountBalancesEndpointImpl
import io.coinmetrics.api.endpoints.blockchain.job.PutBlockchainJobBalanceUpdatesEndpointImpl
import io.coinmetrics.api.endpoints.blockchain.job.PutBlockchainJobTransactionsEndpointImpl
import io.coinmetrics.api.endpoints.blockchain.list.GetBlockchainV2ListOfAccountsEndpointImpl
import io.coinmetrics.api.endpoints.blockchain.list.GetBlockchainV2ListOfBalanceUpdatesEndpointImpl
import io.coinmetrics.api.endpoints.blockchain.list.GetBlockchainV2ListOfBalanceUpdatesForAccountEndpointImpl
import io.coinmetrics.api.endpoints.blockchain.list.GetBlockchainV2ListOfBlocksEndpointImpl
import io.coinmetrics.api.endpoints.blockchain.list.GetBlockchainV2ListOfSubAccountsEndpointImpl
import io.coinmetrics.api.endpoints.blockchain.list.GetBlockchainV2ListOfTransactionsEndpointImpl
import io.coinmetrics.api.endpoints.blockchain.metadata.GetBlockchainMetadataTaggedEntitiesEndpointImpl
import io.coinmetrics.api.endpoints.blockchain.metadata.GetBlockchainMetadataTagsEndpointImpl
import io.coinmetrics.api.endpoints.catalog.GetCatalogAssetPairsEndpointImpl
import io.coinmetrics.api.endpoints.catalog.GetCatalogAssetsEndpointImpl
import io.coinmetrics.api.endpoints.catalog.GetCatalogExchangeAssetsEndpointImpl
import io.coinmetrics.api.endpoints.catalog.GetCatalogExchangesEndpointImpl
import io.coinmetrics.api.endpoints.catalog.GetCatalogIndexesEndpointImpl
import io.coinmetrics.api.endpoints.catalog.GetCatalogInstitutionsEndpointImpl
import io.coinmetrics.api.endpoints.catalog.GetCatalogMarketsEndpointImpl
import io.coinmetrics.api.endpoints.catalog.farum.GetCatalogAssetAlertRulesEndpointImpl
import io.coinmetrics.api.endpoints.catalog.farum.GetCatalogAssetChainsEndpointImpl
import io.coinmetrics.api.endpoints.catalog.farum.GetCatalogMempoolFeeratesEndpointImpl
import io.coinmetrics.api.endpoints.catalog.farum.GetCatalogMiningPoolTipsSummaryEndpointImpl
import io.coinmetrics.api.endpoints.catalog.farum.GetCatalogTransactionTrackerEndpointImpl
import io.coinmetrics.api.endpoints.catalog.index.GetCatalogIndexCandlesEndpointImpl
import io.coinmetrics.api.endpoints.catalog.market.GetCatalogMarketCandlesEndpointImpl
import io.coinmetrics.api.endpoints.catalog.market.GetCatalogMarketContractPricesEndpointImpl
import io.coinmetrics.api.endpoints.catalog.market.GetCatalogMarketFundingRatesEndpointImpl
import io.coinmetrics.api.endpoints.catalog.market.GetCatalogMarketGreeksEndpointImpl
import io.coinmetrics.api.endpoints.catalog.market.GetCatalogMarketImpliedVolatilityEndpointImpl
import io.coinmetrics.api.endpoints.catalog.market.GetCatalogMarketLiquidationsEndpointImpl
import io.coinmetrics.api.endpoints.catalog.market.GetCatalogMarketMetricsEndpointImpl
import io.coinmetrics.api.endpoints.catalog.market.GetCatalogMarketOpeninterestEndpointImpl
import io.coinmetrics.api.endpoints.catalog.market.GetCatalogMarketTradesEndpointImpl
import io.coinmetrics.api.endpoints.catalog.market.GetCatalogS3MarketOrderbooksEndpointImpl
import io.coinmetrics.api.endpoints.catalog.market.GetCatalogS3MarketQuotesEndpointImpl
import io.coinmetrics.api.endpoints.catalog.metrics.GetCatalogAssetMetricsEndpointImpl
import io.coinmetrics.api.endpoints.catalog.metrics.GetCatalogExchangeAssetMetricsEndpointImpl
import io.coinmetrics.api.endpoints.catalog.metrics.GetCatalogExchangeMetricsEndpointImpl
import io.coinmetrics.api.endpoints.catalog.metrics.GetCatalogInstitutionMetricsEndpointImpl
import io.coinmetrics.api.endpoints.catalog.metrics.GetCatalogMetricsEndpointImpl
import io.coinmetrics.api.endpoints.catalog.metrics.GetCatalogPairMetricsEndpointImpl
import io.coinmetrics.api.endpoints.catalog.pair.GetCatalogPairCandlesEndpointImpl
import io.coinmetrics.api.endpoints.catalog.v2.GetCatalogV2BlockchainAccountsEndpointImpl
import io.coinmetrics.api.endpoints.catalog.v2.GetCatalogV2BlockchainBalanceUpdatesEndpointImpl
import io.coinmetrics.api.endpoints.catalog.v2.GetCatalogV2BlockchainBlocksEndpointImpl
import io.coinmetrics.api.endpoints.catalog.v2.GetCatalogV2BlockchainTransactionsEndpointImpl
import io.coinmetrics.api.endpoints.catalog.v2.GetCatalogV2MarketCandlesEndpointImpl
import io.coinmetrics.api.endpoints.catalog.v2.GetCatalogV2MarketContractPricesEndpointImpl
import io.coinmetrics.api.endpoints.catalog.v2.GetCatalogV2MarketFundingRatesEndpointImpl
import io.coinmetrics.api.endpoints.catalog.v2.GetCatalogV2MarketFundingRatesPredictedEndpointImpl
import io.coinmetrics.api.endpoints.catalog.v2.GetCatalogV2MarketGreeksEndpointImpl
import io.coinmetrics.api.endpoints.catalog.v2.GetCatalogV2MarketImpliedVolatilityEndpointImpl
import io.coinmetrics.api.endpoints.catalog.v2.GetCatalogV2MarketLiquidationsEndpointImpl
import io.coinmetrics.api.endpoints.catalog.v2.GetCatalogV2MarketMetricsEndpointImpl
import io.coinmetrics.api.endpoints.catalog.v2.GetCatalogV2MarketOpeninterestEndpointImpl
import io.coinmetrics.api.endpoints.catalog.v2.GetCatalogV2MarketTradesEndpointImpl
import io.coinmetrics.api.endpoints.catalog.v2.GetCatalogV2S3MarketOrderbooksEndpointImpl
import io.coinmetrics.api.endpoints.catalog.v2.GetCatalogV2S3MarketQuotesEndpointImpl
import io.coinmetrics.api.endpoints.catalog.v2.farum.GetCatalogV2AssetChainsEndpointImpl
import io.coinmetrics.api.endpoints.catalog.v2.farum.GetCatalogV2MempoolFeeratesEndpointImpl
import io.coinmetrics.api.endpoints.catalog.v2.farum.GetCatalogV2MiningPoolTipsSummaryEndpointImpl
import io.coinmetrics.api.endpoints.catalog.v2.farum.GetCatalogV2TransactionTrackerEndpointImpl
import io.coinmetrics.api.endpoints.catalog.v2.index.GetCatalogV2IndexCandlesEndpointImpl
import io.coinmetrics.api.endpoints.catalog.v2.index.GetCatalogV2IndexLevelsEndpointImpl
import io.coinmetrics.api.endpoints.catalog.v2.metrics.GetCatalogV2AssetMetricsEndpointImpl
import io.coinmetrics.api.endpoints.catalog.v2.metrics.GetCatalogV2ExchangeAssetMetricsEndpointImpl
import io.coinmetrics.api.endpoints.catalog.v2.metrics.GetCatalogV2ExchangeMetricsEndpointImpl
import io.coinmetrics.api.endpoints.catalog.v2.metrics.GetCatalogV2InstitutionMetricsEndpointImpl
import io.coinmetrics.api.endpoints.catalog.v2.metrics.GetCatalogV2PairMetricsEndpointImpl
import io.coinmetrics.api.endpoints.catalog.v2.pair.GetCatalogV2PairCandlesEndpointImpl
import io.coinmetrics.api.endpoints.catalogall.AllAssetPairsEndpointImpl
import io.coinmetrics.api.endpoints.catalogall.AllExchangeAssetsEndpointImpl
import io.coinmetrics.api.endpoints.catalogall.AllExchangesEndpointImpl
import io.coinmetrics.api.endpoints.catalogall.AllInstitutionsEndpointImpl
import io.coinmetrics.api.endpoints.catalogall.AllMarketsEndpointImpl
import io.coinmetrics.api.endpoints.catalogall.GetCatalogAllAssetsEndpointImpl
import io.coinmetrics.api.endpoints.catalogall.GetCatalogAllIndexesEndpointImpl
import io.coinmetrics.api.endpoints.catalogall.farum.GetCatalogAllAssetAlertRulesEndpointImpl
import io.coinmetrics.api.endpoints.catalogall.farum.GetCatalogAllAssetChainsEndpointImpl
import io.coinmetrics.api.endpoints.catalogall.farum.GetCatalogAllMempoolFeeratesEndpointImpl
import io.coinmetrics.api.endpoints.catalogall.farum.GetCatalogAllMiningPoolTipsSummaryEndpointImpl
import io.coinmetrics.api.endpoints.catalogall.farum.GetCatalogAllTransactionTrackerEndpointImpl
import io.coinmetrics.api.endpoints.catalogall.index.GetCatalogAllIndexCandlesEndpointImpl
import io.coinmetrics.api.endpoints.catalogall.market.GetCatalogAllMarketCandlesEndpointImpl
import io.coinmetrics.api.endpoints.catalogall.market.GetCatalogAllMarketContractPricesEndpointImpl
import io.coinmetrics.api.endpoints.catalogall.market.GetCatalogAllMarketFundingRatesEndpointImpl
import io.coinmetrics.api.endpoints.catalogall.market.GetCatalogAllMarketGreeksEndpointImpl
import io.coinmetrics.api.endpoints.catalogall.market.GetCatalogAllMarketImpliedVolatilityEndpointImpl
import io.coinmetrics.api.endpoints.catalogall.market.GetCatalogAllMarketLiquidationsEndpointImpl
import io.coinmetrics.api.endpoints.catalogall.market.GetCatalogAllMarketMetricsEndpointImpl
import io.coinmetrics.api.endpoints.catalogall.market.GetCatalogAllMarketOpeninterestEndpointImpl
import io.coinmetrics.api.endpoints.catalogall.market.GetCatalogAllMarketTradesEndpointImpl
import io.coinmetrics.api.endpoints.catalogall.market.GetCatalogAllS3MarketOrderbooksEndpointImpl
import io.coinmetrics.api.endpoints.catalogall.market.GetCatalogAllS3MarketQuotesEndpointImpl
import io.coinmetrics.api.endpoints.catalogall.metrics.GetCatalogAllAssetMetricsEndpointImpl
import io.coinmetrics.api.endpoints.catalogall.metrics.GetCatalogAllExchangeAssetMetricsEndpointImpl
import io.coinmetrics.api.endpoints.catalogall.metrics.GetCatalogAllExchangeMetricsEndpointImpl
import io.coinmetrics.api.endpoints.catalogall.metrics.GetCatalogAllInstitutionMetricsEndpointImpl
import io.coinmetrics.api.endpoints.catalogall.metrics.GetCatalogAllMetricsEndpointImpl
import io.coinmetrics.api.endpoints.catalogall.metrics.GetCatalogAllPairMetricsEndpointImpl
import io.coinmetrics.api.endpoints.catalogall.pair.GetCatalogAllPairCandlesEndpointImpl
import io.coinmetrics.api.endpoints.catalogall.v2.GetCatalogAllV2BlockchainAccountsEndpointImpl
import io.coinmetrics.api.endpoints.catalogall.v2.GetCatalogAllV2BlockchainBalanceUpdatesEndpointImpl
import io.coinmetrics.api.endpoints.catalogall.v2.GetCatalogAllV2BlockchainBlocksEndpointImpl
import io.coinmetrics.api.endpoints.catalogall.v2.GetCatalogAllV2BlockchainTransactionsEndpointImpl
import io.coinmetrics.api.endpoints.catalogall.v2.GetCatalogAllV2MarketCandlesEndpointImpl
import io.coinmetrics.api.endpoints.catalogall.v2.GetCatalogAllV2MarketContractPricesEndpointImpl
import io.coinmetrics.api.endpoints.catalogall.v2.GetCatalogAllV2MarketFundingRatesEndpointImpl
import io.coinmetrics.api.endpoints.catalogall.v2.GetCatalogAllV2MarketFundingRatesPredictedEndpointImpl
import io.coinmetrics.api.endpoints.catalogall.v2.GetCatalogAllV2MarketGreeksEndpointImpl
import io.coinmetrics.api.endpoints.catalogall.v2.GetCatalogAllV2MarketImpliedVolatilityEndpointImpl
import io.coinmetrics.api.endpoints.catalogall.v2.GetCatalogAllV2MarketLiquidationsEndpointImpl
import io.coinmetrics.api.endpoints.catalogall.v2.GetCatalogAllV2MarketMetricsEndpointImpl
import io.coinmetrics.api.endpoints.catalogall.v2.GetCatalogAllV2MarketOpeninterestEndpointImpl
import io.coinmetrics.api.endpoints.catalogall.v2.GetCatalogAllV2MarketTradesEndpointImpl
import io.coinmetrics.api.endpoints.catalogall.v2.GetCatalogAllV2S3MarketOrderbooksEndpointImpl
import io.coinmetrics.api.endpoints.catalogall.v2.GetCatalogAllV2S3MarketQuotesEndpointImpl
import io.coinmetrics.api.endpoints.catalogall.v2.farum.GetCatalogAllV2AssetChainsEndpointImpl
import io.coinmetrics.api.endpoints.catalogall.v2.farum.GetCatalogAllV2MempoolFeeratesEndpointImpl
import io.coinmetrics.api.endpoints.catalogall.v2.farum.GetCatalogAllV2MiningPoolTipsSummaryEndpointImpl
import io.coinmetrics.api.endpoints.catalogall.v2.farum.GetCatalogAllV2TransactionTrackerEndpointImpl
import io.coinmetrics.api.endpoints.catalogall.v2.index.GetCatalogAllV2IndexCandlesEndpointImpl
import io.coinmetrics.api.endpoints.catalogall.v2.index.GetCatalogAllV2IndexLevelsEndpointImpl
import io.coinmetrics.api.endpoints.catalogall.v2.metrics.GetCatalogAllV2AssetMetricsEndpointImpl
import io.coinmetrics.api.endpoints.catalogall.v2.metrics.GetCatalogAllV2ExchangeAssetMetricsEndpointImpl
import io.coinmetrics.api.endpoints.catalogall.v2.metrics.GetCatalogAllV2ExchangeMetricsEndpointImpl
import io.coinmetrics.api.endpoints.catalogall.v2.metrics.GetCatalogAllV2InstitutionMetricsEndpointImpl
import io.coinmetrics.api.endpoints.catalogall.v2.metrics.GetCatalogAllV2PairMetricsEndpointImpl
import io.coinmetrics.api.endpoints.catalogall.v2.pair.GetCatalogAllV2PairCandlesEndpointImpl
import io.coinmetrics.api.endpoints.constituents.GetConstituentSnapshotsAssetMetricsEndpointImpl
import io.coinmetrics.api.endpoints.constituents.GetConstituentTimeframesAssetMetricsEndpointImpl
import io.coinmetrics.api.endpoints.job.GetJobByIdEndpointImpl
import io.coinmetrics.api.endpoints.profile.GetAssetProfilesEndpointImpl
import io.coinmetrics.api.endpoints.profile.GetNetworkProfilesEndpointImpl
import io.coinmetrics.api.endpoints.reference.GetReferenceDataAssetMetricsEndpointImpl
import io.coinmetrics.api.endpoints.reference.GetReferenceDataAssetsEndpointImpl
import io.coinmetrics.api.endpoints.reference.GetReferenceDataExchangeAssetMetricsEndpointImpl
import io.coinmetrics.api.endpoints.reference.GetReferenceDataExchangeMetricsEndpointImpl
import io.coinmetrics.api.endpoints.reference.GetReferenceDataExchangesEndpointImpl
import io.coinmetrics.api.endpoints.reference.GetReferenceDataIndexesEndpointImpl
import io.coinmetrics.api.endpoints.reference.GetReferenceDataInstitutionMetricsEndpointImpl
import io.coinmetrics.api.endpoints.reference.GetReferenceDataMarketMetricsEndpointImpl
import io.coinmetrics.api.endpoints.reference.GetReferenceDataMarketsEndpointImpl
import io.coinmetrics.api.endpoints.reference.GetReferenceDataPairMetricsEndpointImpl
import io.coinmetrics.api.endpoints.reference.GetReferenceDataPairsEndpointImpl
import io.coinmetrics.api.endpoints.securitymaster.GetSecurityMasterAssetsEndpointImpl
import io.coinmetrics.api.endpoints.securitymaster.GetSecurityMasterMarketsEndpointImpl
import io.coinmetrics.api.endpoints.stream.asset.AssetMetricsClientConnections
import io.coinmetrics.api.endpoints.stream.asset.AssetMetricsWsEndpointImpl
import io.coinmetrics.api.endpoints.stream.asset.AssetPrincipalPriceKafkaDataProvider
import io.coinmetrics.api.endpoints.stream.asset.AssetRatesKafkaDataProvider
import io.coinmetrics.api.endpoints.stream.asset.BbbFlowMetricsDataProvider
import io.coinmetrics.api.endpoints.stream.asset.BbbNetworkMetricsDataProvider
import io.coinmetrics.api.endpoints.stream.asset.BbbNewNetworkMetricsDataProvider
import io.coinmetrics.api.endpoints.stream.asset.NetworkDataPoller
import io.coinmetrics.api.endpoints.stream.index.levels.GetTimeseriesStreamIndexLevelsEndpointImpl
import io.coinmetrics.api.endpoints.stream.index.levels.IndexLevelsClientConnections
import io.coinmetrics.api.endpoints.stream.index.levels.IndexLevelsHistory
import io.coinmetrics.api.endpoints.stream.index.levels.IndexLevelsKafkaDataProvider
import io.coinmetrics.api.endpoints.stream.market.candles.CandlesClientConnections
import io.coinmetrics.api.endpoints.stream.market.candles.CandlesHistory
import io.coinmetrics.api.endpoints.stream.market.candles.CandlesKafkaDataProtoProvider
import io.coinmetrics.api.endpoints.stream.market.candles.CandlesWsEndpointImpl
import io.coinmetrics.api.endpoints.stream.market.liquidations.LiquidationKey
import io.coinmetrics.api.endpoints.stream.market.liquidations.MarketLiquidationsClientConnections
import io.coinmetrics.api.endpoints.stream.market.liquidations.MarketLiquidationsKafkaDataProvider
import io.coinmetrics.api.endpoints.stream.market.liquidations.MarketLiquidationsWsEndpointImpl
import io.coinmetrics.api.endpoints.stream.market.openinterests.MarketOpenInterestClientConnections
import io.coinmetrics.api.endpoints.stream.market.openinterests.MarketOpenInterestKafkaDataProvider
import io.coinmetrics.api.endpoints.stream.market.openinterests.MarketOpenInterestWsEndpointImpl
import io.coinmetrics.api.endpoints.stream.market.openinterests.OpenInterestKey
import io.coinmetrics.api.endpoints.stream.market.quotes.MarketQuotesClientConnections
import io.coinmetrics.api.endpoints.stream.market.quotes.MarketQuotesKafkaDataProvider
import io.coinmetrics.api.endpoints.stream.market.quotes.MarketQuotesWsEndpointImpl
import io.coinmetrics.api.endpoints.stream.spread.SpreadQuotesKafkaDataProvider
import io.coinmetrics.api.endpoints.stream.spread.asset.AssetQuotesClientConnections
import io.coinmetrics.api.endpoints.stream.spread.asset.AssetQuotesWsEndpoint
import io.coinmetrics.api.endpoints.stream.spread.pair.PairQuotesClientConnections
import io.coinmetrics.api.endpoints.stream.spread.pair.PairQuotesWsEndpoint
import io.coinmetrics.api.endpoints.taxonomy.GetTaxonomyAssetsEndpointImpl
import io.coinmetrics.api.endpoints.taxonomy.LocalDataRefreshEndpoint
import io.coinmetrics.api.endpoints.taxonomy.metadata.GetTaxonomyMetadataAssetsEndpointImpl
import io.coinmetrics.api.endpoints.timeseries.alerts.AssetAlertsEndpointImpl
import io.coinmetrics.api.endpoints.timeseries.asset.AssetChainsEndpointImpl
import io.coinmetrics.api.endpoints.timeseries.asset.AssetMetricsEndpointImpl
import io.coinmetrics.api.endpoints.timeseries.defi.GetDefiBalanceSheetsEndpointImpl
import io.coinmetrics.api.endpoints.timeseries.exchange.ExchangeMetricsEndpointImpl
import io.coinmetrics.api.endpoints.timeseries.exchange.datasources.ExchangeBasedMetricsDataSource
import io.coinmetrics.api.endpoints.timeseries.exchangeasset.ExchangeAssetMetricsEndpointImpl
import io.coinmetrics.api.endpoints.timeseries.index.GetTimeseriesIndexCandlesEndpointImpl
import io.coinmetrics.api.endpoints.timeseries.index.GetTimeseriesIndexConstituentsEndpointImpl
import io.coinmetrics.api.endpoints.timeseries.index.GetTimeseriesIndexLevelsEndpointImpl
import io.coinmetrics.api.endpoints.timeseries.index.datasource.IndexCandlesDataSource
import io.coinmetrics.api.endpoints.timeseries.institution.InstitutionMetricsEndpointImpl
import io.coinmetrics.api.endpoints.timeseries.market.BookTierName
import io.coinmetrics.api.endpoints.timeseries.market.CandlesEndpointImpl
import io.coinmetrics.api.endpoints.timeseries.market.ContractPricesEndpointImpl
import io.coinmetrics.api.endpoints.timeseries.market.FundingRatesEndpointImpl
import io.coinmetrics.api.endpoints.timeseries.market.FundingRatesPredictedEndpointImpl
import io.coinmetrics.api.endpoints.timeseries.market.GreeksEndpointImpl
import io.coinmetrics.api.endpoints.timeseries.market.ImpliedVolatilityEndpointImpl
import io.coinmetrics.api.endpoints.timeseries.market.LiquidationsEndpointImpl
import io.coinmetrics.api.endpoints.timeseries.market.MarketMetricsEndpointImpl
import io.coinmetrics.api.endpoints.timeseries.market.OpenInterestEndpointImpl
import io.coinmetrics.api.endpoints.timeseries.market.OrderBooksDbService
import io.coinmetrics.api.endpoints.timeseries.market.S3OrderBooksEndpointImpl
import io.coinmetrics.api.endpoints.timeseries.market.S3QuotesEndpointImpl
import io.coinmetrics.api.endpoints.timeseries.market.datasources.MarketCandlesDelayedDataSource
import io.coinmetrics.api.endpoints.timeseries.market.datasources.MarketCandlesNonDelayedDataSource
import io.coinmetrics.api.endpoints.timeseries.market.datasources.S3OrderBooksDataSource
import io.coinmetrics.api.endpoints.timeseries.market.trades.TradesEndpointImpl
import io.coinmetrics.api.endpoints.timeseries.mempool.MempoolFeeratesEndpointImpl
import io.coinmetrics.api.endpoints.timeseries.mining.MiningPoolTipsSummaryEndpointImpl
import io.coinmetrics.api.endpoints.timeseries.pair.PairCandlesEndpointImpl
import io.coinmetrics.api.endpoints.timeseries.pair.PairMetricsEndpointImpl
import io.coinmetrics.api.endpoints.timeseries.pair.datasources.PairCandlesDataSource
import io.coinmetrics.api.endpoints.txtracker.TxTrackerEndpointImpl
import io.coinmetrics.api.endpoints.txtracker.TxTrackerSettlementEndpointImpl
import io.coinmetrics.api.model.KafkaDataProcessorInfo
import io.coinmetrics.api.model.S3BooksMarketType
import io.coinmetrics.api.modules.ApiModule
import io.coinmetrics.api.modules.ModuleName
import io.coinmetrics.api.modules.common.CommonModule
import io.coinmetrics.api.persistence.Databases
import io.coinmetrics.api.resources.Resources
import io.coinmetrics.api.resources.constituents.AssetMetricConstituentProvider
import io.coinmetrics.api.resources.constituents.VolumeTrustedSpotConstituentProvider
import io.coinmetrics.api.service.BlockchainEndpointService
import io.coinmetrics.api.service.IndexDiscoveryService
import io.coinmetrics.api.service.MarketDiscoveryContextFactory
import io.coinmetrics.api.service.MemoryLimitService
import io.coinmetrics.api.service.TaxonomyService
import io.coinmetrics.api.service.catalog.CatalogAtlasService
import io.coinmetrics.api.service.catalog.CatalogFarumService
import io.coinmetrics.api.service.catalog.CatalogIndexService
import io.coinmetrics.api.service.catalog.CatalogPairService
import io.coinmetrics.api.service.catalog.market.CatalogV1MarketService
import io.coinmetrics.api.service.catalog.market.CatalogV2MarketService
import io.coinmetrics.api.service.catalog.metric.impl.AssetMetricsService
import io.coinmetrics.api.service.catalog.metric.impl.ExchangeAssetMetricsService
import io.coinmetrics.api.service.catalog.metric.impl.ExchangeMetricsService
import io.coinmetrics.api.service.catalog.metric.impl.InstitutionMetricsService
import io.coinmetrics.api.service.catalog.metric.impl.PairMetricsService
import io.coinmetrics.api.service.job.JobService
import io.coinmetrics.api.service.job.TemporalClients
import io.coinmetrics.api.statistics.blockchain.UdmV2Statistics
import io.coinmetrics.api.statistics.defi.DefiStatistics
import io.coinmetrics.api.statistics.farum.AssetChainsStatistics
import io.coinmetrics.api.statistics.farum.MempoolFeeratesStatistics
import io.coinmetrics.api.statistics.farum.MiningPoolTipsSummaryStatistics
import io.coinmetrics.api.statistics.farum.TransactionTrackerStatistics
import io.coinmetrics.api.statistics.index.IndexCandlesStatistics
import io.coinmetrics.api.statistics.index.IndexConstituentsStatistics
import io.coinmetrics.api.statistics.index.IndexStatistics
import io.coinmetrics.api.statistics.metrics.AssetMetricStatistics
import io.coinmetrics.api.statistics.metrics.ExchangeAssetMetricStatistics
import io.coinmetrics.api.statistics.metrics.ExchangeMetricStatistics
import io.coinmetrics.api.statistics.metrics.InstitutionMetricStatistics
import io.coinmetrics.api.statistics.metrics.PairMetricStatistics
import io.coinmetrics.api.statistics.pair.PairCandlesStatistics
import io.coinmetrics.api.statistics.securitymaster.EndOfDayMarketCandleStatistics
import io.coinmetrics.api.statistics.tagging.AddressTaggingStatistics
import io.coinmetrics.api.storage.AssetProfilesLocalStorage
import io.coinmetrics.api.storage.LocalDataLoader
import io.coinmetrics.api.storage.NetworkProfilesLocalStorage
import io.coinmetrics.api.storage.SecurityMasterLocalStorage
import io.coinmetrics.api.storage.TaxonomyLocalStorage
import io.coinmetrics.api.utils.CircularHash
import io.coinmetrics.api.utils.ExperimentalAtlas
import io.coinmetrics.api.utils.S3Utils
import io.coinmetrics.api.utils.tiering.DynamicTierConfig
import io.coinmetrics.api.utils.tiering.DynamicTieringConfigGenerator
import io.coinmetrics.databases.Database
import io.coinmetrics.httpserver.HttpRequest
import kotlinx.coroutines.asCoroutineDispatcher
import org.jetbrains.annotations.VisibleForTesting
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import java.time.Instant
import java.util.EnumMap
import java.util.concurrent.Executors

class MainApiModule(
    private val common: CommonModule,
    val config: MainApiConfig = MainApiConfig(common.config),
) : ApiModule {
    override val name = ModuleName.MAIN

    private val log: Logger = LoggerFactory.getLogger(MainApiModule::class.java)

    @VisibleForTesting
    val monitoring = MainApiMonitoring(common.monitoring)

    internal val databases: Databases = Databases(config.databases, common.monitoring.registry)

    private val booksS3StorageClients =
        config.booksTiers.mapValuesTo(EnumMap(S3BooksMarketType::class.java)) { (_, tiers) ->
            S3Utils.createS3StorageClient(
                tiers,
                monitoring.s3Latency,
            )
        }

    private val defiStatistics = DefiStatistics(common.statisticsRepository)

    private val booksS3Databases =
        booksS3StorageClients.mapValuesTo(EnumMap(S3BooksMarketType::class.java)) { (_, client) ->
            client?.let { S3Utils.createS3Databases(it, config.dellPowerScaleBugsWorkarounds) }
        }

    private val pairMetricStatistics = PairMetricStatistics(common.statisticsRepository)

    private val pairCandlesStatistics = PairCandlesStatistics(common.statisticsRepository)

    private val exchangeMetricStatistics = ExchangeMetricStatistics(common.statisticsRepository)

    private val exchangeAssetMetricStatistics = ExchangeAssetMetricStatistics(common.statisticsRepository)

    private val institutionMetricStatistics = InstitutionMetricStatistics(common.statisticsRepository)

    private val indexStatistics = IndexStatistics(common.statisticsRepository)

    private val indexCandleStatistics = IndexCandlesStatistics(common.statisticsRepository)

    private val indexConstituentsStatistics = IndexConstituentsStatistics(common.statisticsRepository)

    private val assetMetricStatistics = AssetMetricStatistics(common.statisticsRepository)

    private val udmV2Statistics = UdmV2Statistics(common.statisticsRepository)

    private val addressTaggingStatistics = AddressTaggingStatistics(common.statisticsRepository)

    private val liquidationsClientConnections =
        MarketLiquidationsClientConnections(
            monitoring = monitoring,
            streamConnectionCountLimitService = common.streamConnectionCountLimitService,
            marketClientConnectionService = common.marketClientConnectionService,
            statisticsUpdateIntervalMs = config.marketClientConnectionStatisticsUpdateIntervalMs,
        )
    private val openInterestClientConnections =
        MarketOpenInterestClientConnections(
            monitoring = monitoring,
            streamConnectionCountLimitService = common.streamConnectionCountLimitService,
            marketClientConnectionService = common.marketClientConnectionService,
            statisticsUpdateIntervalMs = config.marketClientConnectionStatisticsUpdateIntervalMs,
        )
    private val quotesClientConnections =
        MarketQuotesClientConnections(
            monitoring = monitoring,
            streamConnectionCountLimitService = common.streamConnectionCountLimitService,
            marketClientConnectionService = common.marketClientConnectionService,
            statisticsUpdateIntervalMs = config.marketClientConnectionStatisticsUpdateIntervalMs,
        )
    private val pairQuotesClientConnections = PairQuotesClientConnections(monitoring, common.streamConnectionCountLimitService)
    private val assetQuotesClientConnections = AssetQuotesClientConnections(monitoring, common.streamConnectionCountLimitService)
    private val metricsClientConnections = AssetMetricsClientConnections(monitoring, common.streamConnectionCountLimitService)

    private val experimentalAtlas = ExperimentalAtlas(config.ndExperimentalAtlasFile)

    val assetRatesDataProviders =
        if (config.ratesConfigs.isNotEmpty()) {
            config.ratesConfigs.map { (frequency, ratesKafkaConfig) ->
                AssetRatesKafkaDataProvider(
                    config = ratesKafkaConfig,
                    monitoring = monitoring,
                    clientConnections = metricsClientConnections,
                    frequency = frequency,
                    objectMapper = common.objectMapper,
                )
            }
        } else {
            emptyList()
        }

    val assetPrincipalPriceDataProviders =
        if (config.principalPriceConfigs.isNotEmpty()) {
            config.principalPriceConfigs.map { principalPriceConfig ->
                AssetPrincipalPriceKafkaDataProvider(
                    config = principalPriceConfig,
                    monitoring = monitoring,
                    clientConnections = metricsClientConnections,
                    objectMapper = common.objectMapper,
                )
            }
        } else {
            emptyList()
        }

    private val candlesHistory = CandlesHistory(monitoring)

    private val candlesClientConnections =
        CandlesClientConnections(
            monitoring = common.monitoring.streamingMonitoring,
            streamConnectionCountLimitService = common.streamConnectionCountLimitService,
            candlesHistory = candlesHistory,
            marketClientConnectionService = common.marketClientConnectionService,
            statisticsUpdateIntervalMs = config.marketClientConnectionStatisticsUpdateIntervalMs,
        )

    val candlesDataProvider =
        CandlesKafkaDataProtoProvider(
            clock = config.common.clock,
            config = config.candlesConfig,
            allowedTimeSpentFromTheLastSwitchMs = config.candlesAllowedTimeSpentFromTheLastSwitchMs,
            monitoring = monitoring,
            clientConnections = candlesClientConnections,
        )

    private val indexLevelHistory = IndexLevelsHistory(monitoring)
    private val indexLevelsClientConnections =
        IndexLevelsClientConnections(common.monitoring.streamingMonitoring, common.streamConnectionCountLimitService, indexLevelHistory)

    val indexLevelsDataProviders =
        config.indexLevelsConfigs.map { config ->
            IndexLevelsKafkaDataProvider(config, monitoring.indexLevelsKafkaMonitoring, indexLevelsClientConnections)
        }

    private val newNetworkMetricsDataPoller = createNewNetworkMetricsDataPoller()
    private val networkMetricsDataPoller = createNetworkMetricsDataPoller()
    private val flowMetricsDataPoller = createFlowMetricsDataPoller()

    private val assetChainsStatistics = AssetChainsStatistics(common.statisticsRepository)
    private val mempoolFeeratesStatistics = MempoolFeeratesStatistics(common.statisticsRepository)
    private val miningPoolTipsSummaryStatistics = MiningPoolTipsSummaryStatistics(common.statisticsRepository)
    private val transactionTrackerStatistics = TransactionTrackerStatistics(common.statisticsRepository)

    private val endOfDayMarketCandleStatistics = EndOfDayMarketCandleStatistics(common.statisticsRepository)

    private val marketDiscoveryContextFactory =
        MarketDiscoveryContextFactory(
            common.marketStatisticsService,
            common.exchangeDiscoveryService,
            config.catalogV1MarketsLimit,
        )

    private val catalogFarumService =
        CatalogFarumService(
            common.amsService,
            assetChainsStatistics,
            mempoolFeeratesStatistics,
            transactionTrackerStatistics,
            miningPoolTipsSummaryStatistics,
        )

    private val catalogPairService =
        CatalogPairService(common.amsService, pairCandlesStatistics, config.common.communityApiKey, common.config.clock)
    private val indexDiscoveryService = IndexDiscoveryService(common.amsService)
    private val catalogIndexService =
        CatalogIndexService(config.common.communityApiKey, indexDiscoveryService, indexStatistics, indexCandleStatistics)

    val jobService: JobService? =
        config.temporalConfig?.let { temporalConfig ->
            JobService(
                common.objectMapper,
                temporalClients = TemporalClients(temporalConfig, common.monitoring),
                temporalConfig.expirationPeriod,
            )
        }

    private val indexCandlesDataSource = IndexCandlesDataSource(databases.indexes)
    private val pairCandlesDataSource = PairCandlesDataSource(databases.rates)

    private val assetMetricConstituentProviders: Map<String, AssetMetricConstituentProvider> =
        listOf(
            VolumeTrustedSpotConstituentProvider(
                config.assetMetricConstituentsConfig.volumeTrustedSpotFileName,
                common.objectMapper,
            ),
        ).flatMap { provider -> provider.supportedMetrics().map { metric -> metric to provider } }.toMap(HashMap())

    private val assetMetricsService =
        AssetMetricsService(
            config.docsBaseUrl,
            config.common.communityApiKey,
            common.amsService,
            monitoring,
            assetMetricStatistics,
            assetMetricConstituentProviders,
            config.ndExperimentalAssetMetricsFile,
            common.config.clock,
        )
    private val pairMetricsService = PairMetricsService(config.docsBaseUrl, common.amsService, pairMetricStatistics)
    private val exchangeMetricsService = ExchangeMetricsService(config.docsBaseUrl, common.amsService, exchangeMetricStatistics)
    private val exchangeAssetMetricsService =
        ExchangeAssetMetricsService(config.docsBaseUrl, common.amsService, exchangeAssetMetricStatistics)
    private val institutionMetricsService = InstitutionMetricsService(config.docsBaseUrl, common.amsService, institutionMetricStatistics)

    private val catalogMarketService =
        CatalogV1MarketService(
            config.common.communityApiKey,
            marketDiscoveryContextFactory,
            common.marketStatisticsService,
            config.tenPercentMidPriceBookStartTime,
        )

    private val catalogV2MarketService =
        CatalogV2MarketService(
            config.common.communityApiKey,
            marketDiscoveryContextFactory,
            common.marketStatisticsService,
            config.tenPercentMidPriceBookStartTime,
        )

    private val blockchainEndpointService = BlockchainEndpointService(common.amsService, udmV2Statistics)

    private val catalogAtlasService = CatalogAtlasService(blockchainEndpointService, experimentalAtlas)

    val liquidationsKafkaDataProviders =
        kafkaConfigsPerExchangeToTriples(
            name = "Liquidations",
            kafkaConfigsPerExchange = config.kafkaLiquidationsConfigsPerExchange,
        ).map { (exchange, kafkaSourceConfig, cacheSize) ->
            log.info("Using liquidations deduplication cache size $cacheSize for exchange '${exchange.getNormalizedName()}'.")
            val marketLiquidationKeysCache = CircularHash<LiquidationKey, Boolean>(cacheSize)
            if (exchange.defi) {
                throw RuntimeException("Defi exchanges are not supported for stream liquidations")
            } else {
                MarketLiquidationsKafkaDataProvider(
                    exchange = exchange,
                    config = kafkaSourceConfig,
                    monitoring = monitoring,
                    clientConnections = liquidationsClientConnections,
                    marketLiquidationKeysCache = marketLiquidationKeysCache,
                    marketResolvingService = common.marketResolvingService,
                    clock = common.config.clock,
                )
            }
        }

    val openInterestKafkaDataProviders =
        kafkaConfigsPerExchangeToTriples(
            name = "OpenInterest",
            kafkaConfigsPerExchange = config.kafkaOpenInterestConfigsPerExchange,
        ).map { (exchange, kafkaSourceConfig, cacheSize) ->
            log.info("Using open interest deduplication cache size $cacheSize for exchange '${exchange.getNormalizedName()}'.")
            val marketOpenInterestKeysCache = CircularHash<OpenInterestKey, Boolean>(cacheSize)
            if (exchange.defi) {
                throw RuntimeException("Defi exchanges are not supported for stream open interest")
            } else {
                MarketOpenInterestKafkaDataProvider(
                    exchange = exchange,
                    config = kafkaSourceConfig,
                    monitoring = monitoring,
                    clientConnections = openInterestClientConnections,
                    marketOpenInterestKeysCache = marketOpenInterestKeysCache,
                    marketResolvingService = common.marketResolvingService,
                    clock = common.config.clock,
                )
            }
        }

    val marketQuotesDataProviders: List<MarketQuotesKafkaDataProvider> =
        config.kafkaQuotesConfigs.map {
            MarketQuotesKafkaDataProvider(it, common.objectMapper, monitoring, quotesClientConnections, common.marketResolvingService)
        }

    val pairQuotesDataProvider =
        SpreadQuotesKafkaDataProvider(
            config = config.kafkaPairQuotesConfig,
            clientConnections = pairQuotesClientConnections,
            monitoring = monitoring.pairQuotesKafkaMonitoring,
            objectMapper = common.objectMapper,
        )
    val assetQuotesDataProvider =
        SpreadQuotesKafkaDataProvider(
            config = config.kafkaAssetQuotesConfig,
            clientConnections = assetQuotesClientConnections,
            monitoring = monitoring.assetQuotesKafkaMonitoring,
            objectMapper = common.objectMapper,
        )

    private val gitDispatcher = Executors.newSingleThreadExecutor { r -> Thread(r, "git-dispatcher") }.asCoroutineDispatcher()

    val taxonomyLocalStorage = TaxonomyLocalStorage(common.objectMapper)

    private val taxonomyService = TaxonomyService(taxonomyLocalStorage)

    private val taxonomyDataLoader =
        LocalDataLoader(config.taxonomyDataLoad, config.taxonomyDataLoadConfig, taxonomyLocalStorage, gitDispatcher)

    val networkProfilesLocalStorage = NetworkProfilesLocalStorage(common.objectMapper)

    private val networkProfilesDataLoader =
        LocalDataLoader(config.networkProfilesDataLoad, config.networkProfilesDataLoadConfig, networkProfilesLocalStorage, gitDispatcher)

    val assetProfilesLocalStorage = AssetProfilesLocalStorage(common.objectMapper)

    private val assetProfilesDataLoader =
        LocalDataLoader(config.assetProfilesDataLoad, config.assetProfilesDataLoadConfig, assetProfilesLocalStorage, gitDispatcher)

    val securityMasterLocalStorage = SecurityMasterLocalStorage(common.objectMapper)
    private val securityMasterDataLoader =
        LocalDataLoader(config.securityMasterDataLoad, config.securityMasterDataLoadConfig, securityMasterLocalStorage, gitDispatcher)

    // it helps NetworkDataPoller to don't poll chain monitor metrics
    private val assetsSupportedByNetworkDataPoller = "btc,eth"
    private val metricsIgnoredByNetworkDataPoller = hashSetOf("block_count_at_tip")

    private val memoryLimitService =
        MemoryLimitService(
            memoryLimit = config.totalS3QueryMemoryLimit,
            onApiKeyQueueTime = { apiKey, queueTimeNs ->
                monitoring.s3QueryApiKeyQueueTime.labelValues(apiKey).observe(queueTimeNs.toDouble() / 1_000_000_000)
            },
            onMemoryQueueTime = { apiKey, queueTimeNs ->
                monitoring.s3QueryMemoryQueueTime.labelValues(apiKey).observe(queueTimeNs.toDouble() / 1_000_000_000)
            },
            onMemoryQueueLength = { apiKey, increased ->
                monitoring.s3QueryMemoryQueueLength.labelValues(apiKey).inc(if (increased) 1.0 else -1.0)
            },
            onMemoryUsage = { memoryUsageBytes ->
                monitoring.s3QueryMemoryUsage.set(memoryUsageBytes.toDouble())
            },
        )

    private val s3DataSources =
        listOf(S3BooksMarketType.SPOT_BOOKS, S3BooksMarketType.FUTURES_BOOKS, S3BooksMarketType.OPTIONS_BOOKS)
            .associateWithTo(EnumMap(S3BooksMarketType::class.java)) { dataType ->
                S3OrderBooksDataSource(
                    s3Databases = booksS3Databases[dataType],
                    marketStatisticsService = common.marketStatisticsService,
                    memoryLimitService = memoryLimitService,
                    spotMetadataStatistics = common.spotMetadataStatistics,
                )
            }

    private val router =
        Router(
            run {
                val getCatalogAssetsEndpoint =
                    GetCatalogAssetsEndpointImpl(
                        common.amsService,
                        assetMetricsService,
                        marketDiscoveryContextFactory,
                        blockchainEndpointService,
                    )
                val getCatalogAssetPairsEndpointImpl =
                    GetCatalogAssetPairsEndpointImpl(
                        common.amsService,
                        pairMetricsService,
                    )
                val exchangeAssetsEndpointImpl =
                    GetCatalogExchangeAssetsEndpointImpl(
                        common.amsService,
                        exchangeAssetMetricsService,
                    )
                val getCatalogInstitutionsEndpointImpl =
                    GetCatalogInstitutionsEndpointImpl(
                        common.amsService,
                        institutionMetricsService,
                    )
                val getCatalogAssetAlertRulesEndpointImpl =
                    GetCatalogAssetAlertRulesEndpointImpl(
                        databases.chainMonitor,
                        common.amsService,
                    )
                val metricsDataSource = ExchangeBasedMetricsDataSource(databases.metrics)
                val orderBookEndpointImpl =
                    OrderBooksDbService(
                        booksDb = databases.books,
                        useNewBooksTables = config.useNewBooksTables,
                    )
                val s3OrderBookEndpointImpl =
                    S3OrderBooksEndpointImpl(
                        dynamicTierConfigGenerator = this::generateDynamicTiersForBooks,
                        s3DataSources = s3DataSources,
                        orderBooksDbService = orderBookEndpointImpl,
                        amsService = common.amsService,
                        marketResolvingService = common.marketResolvingService,
                        marketStatisticsService = common.marketStatisticsService,
                        communityApiKey = config.common.communityApiKey,
                        tenPercentMidPriceBookStartTime = config.tenPercentMidPriceBookStartTime,
                    )
                val s3QuotesEndpointImpl =
                    S3QuotesEndpointImpl(
                        objectMapper = common.objectMapper,
                        amsService = common.amsService,
                        marketResolvingService = common.marketResolvingService,
                        s3OrderBookEndpointImpl = s3OrderBookEndpointImpl,
                    )
                val txTrackerEndpointImpl =
                    TxTrackerEndpointImpl(
                        chainMonitorDb = databases.chainMonitor,
                        amsService = common.amsService,
                        assetHeightResolver = { asset ->
                            // todo maybe switch to the UDM data source?
                            networkMetricsDataPoller.getAssetHeight(asset)
                        },
                    )

                val getCatalogExchangesEndpoint =
                    GetCatalogExchangesEndpointImpl(
                        common.amsService,
                        marketDiscoveryContextFactory,
                        common.marketStatisticsService,
                        exchangeMetricsService,
                    )

                Paths(
                    // reference data
                    getReferenceDataAssetsEndpoint = GetReferenceDataAssetsEndpointImpl(common.amsService),
                    getReferenceDataExchangesEndpoint = GetReferenceDataExchangesEndpointImpl(common.amsService),
                    getReferenceDataMarketsEndpoint = GetReferenceDataMarketsEndpointImpl(common.amsService, catalogV2MarketService),
                    getReferenceDataIndexesEndpoint =
                        GetReferenceDataIndexesEndpointImpl(
                            common.amsService,
                            indexDiscoveryService,
                            indexStatistics,
                            indexCandleStatistics,
                        ),
                    getReferenceDataPairsEndpoint =
                        GetReferenceDataPairsEndpointImpl(
                            common.amsService,
                            pairMetricStatistics,
                            pairCandlesStatistics,
                        ),
                    getReferenceDataAssetMetricsEndpoint =
                        GetReferenceDataAssetMetricsEndpointImpl(
                            common.amsService,
                            assetMetricsService,
                        ),
                    getReferenceDataExchangeMetricsEndpoint =
                        GetReferenceDataExchangeMetricsEndpointImpl(
                            common.amsService,
                            exchangeMetricsService,
                        ),
                    getReferenceDataExchangeAssetMetricsEndpoint =
                        GetReferenceDataExchangeAssetMetricsEndpointImpl(
                            common.amsService,
                            exchangeAssetMetricsService,
                        ),
                    getReferenceDataPairMetricsEndpoint =
                        GetReferenceDataPairMetricsEndpointImpl(
                            common.amsService,
                            pairMetricsService,
                        ),
                    getReferenceDataInstitutionMetricsEndpoint =
                        GetReferenceDataInstitutionMetricsEndpointImpl(
                            common.amsService,
                            institutionMetricsService,
                        ),
                    getReferenceDataMarketMetricsEndpoint =
                        GetReferenceDataMarketMetricsEndpointImpl(
                            config.docsBaseUrl,
                            common.amsService,
                            common.marketStatisticsService,
                        ),
                    // catalog-v2
                    getCatalogV2AssetMetricsEndpoint = GetCatalogV2AssetMetricsEndpointImpl(common.amsService, assetMetricsService),
                    getCatalogV2ExchangeMetricsEndpoint =
                        GetCatalogV2ExchangeMetricsEndpointImpl(
                            common.amsService,
                            exchangeMetricsService,
                        ),
                    getCatalogV2ExchangeAssetMetricsEndpoint =
                        GetCatalogV2ExchangeAssetMetricsEndpointImpl(
                            common.amsService,
                            exchangeAssetMetricsService,
                        ),
                    getCatalogV2InstitutionMetricsEndpoint =
                        GetCatalogV2InstitutionMetricsEndpointImpl(
                            common.amsService,
                            institutionMetricsService,
                        ),
                    getCatalogV2PairMetricsEndpoint = GetCatalogV2PairMetricsEndpointImpl(common.amsService, pairMetricsService),
                    getCatalogV2MarketTradesEndpoint = GetCatalogV2MarketTradesEndpointImpl(common.amsService, catalogV2MarketService),
                    getCatalogV2MarketQuotesEndpoint = GetCatalogV2S3MarketQuotesEndpointImpl(common.amsService, catalogV2MarketService),
                    getCatalogV2MarketOrderbooksEndpoint =
                        GetCatalogV2S3MarketOrderbooksEndpointImpl(
                            common.amsService,
                            catalogV2MarketService,
                        ),
                    getCatalogV2MarketCandlesEndpoint =
                        GetCatalogV2MarketCandlesEndpointImpl(
                            common.amsService,
                            catalogV2MarketService,
                        ),
                    getCatalogV2MarketOpeninterestEndpoint =
                        GetCatalogV2MarketOpeninterestEndpointImpl(
                            common.amsService,
                            catalogV2MarketService,
                        ),
                    getCatalogV2MarketFundingRatesEndpoint =
                        GetCatalogV2MarketFundingRatesEndpointImpl(
                            common.amsService,
                            catalogV2MarketService,
                        ),
                    getCatalogV2MarketFundingRatesPredictedEndpoint =
                        GetCatalogV2MarketFundingRatesPredictedEndpointImpl(
                            common.amsService,
                            catalogV2MarketService,
                        ),
                    getCatalogV2MarketLiquidationsEndpoint =
                        GetCatalogV2MarketLiquidationsEndpointImpl(
                            common.amsService,
                            catalogV2MarketService,
                        ),
                    getCatalogV2MarketGreeksEndpoint =
                        GetCatalogV2MarketGreeksEndpointImpl(
                            common.amsService,
                            catalogV2MarketService,
                        ),
                    getCatalogV2MarketContractPricesEndpoint =
                        GetCatalogV2MarketContractPricesEndpointImpl(
                            common.amsService,
                            catalogV2MarketService,
                        ),
                    getCatalogV2MarketImpliedVolatilityEndpoint =
                        GetCatalogV2MarketImpliedVolatilityEndpointImpl(
                            common.amsService,
                            catalogV2MarketService,
                        ),
                    getCatalogV2MarketMetricsEndpoint =
                        GetCatalogV2MarketMetricsEndpointImpl(
                            common.amsService,
                            catalogV2MarketService,
                        ),
                    getCatalogV2PairCandlesEndpoint = GetCatalogV2PairCandlesEndpointImpl(common.amsService, catalogPairService),
                    getCatalogV2IndexLevelsEndpoint = GetCatalogV2IndexLevelsEndpointImpl(common.amsService, catalogIndexService),
                    getCatalogV2IndexCandlesEndpoint = GetCatalogV2IndexCandlesEndpointImpl(common.amsService, catalogIndexService),
                    getCatalogV2AssetChainsEndpoint = GetCatalogV2AssetChainsEndpointImpl(common.amsService, catalogFarumService),
                    getCatalogV2MempoolFeeratesEndpoint = GetCatalogV2MempoolFeeratesEndpointImpl(common.amsService, catalogFarumService),
                    getCatalogV2MiningPoolTipsSummaryEndpoint =
                        GetCatalogV2MiningPoolTipsSummaryEndpointImpl(
                            common.amsService,
                            catalogFarumService,
                        ),
                    getCatalogV2TransactionTrackerEndpoint =
                        GetCatalogV2TransactionTrackerEndpointImpl(
                            common.amsService,
                            catalogFarumService,
                        ),
                    getCatalogV2BlockchainAccountsEndpoint =
                        GetCatalogV2BlockchainAccountsEndpointImpl(
                            common.amsService,
                            catalogAtlasService,
                        ),
                    getCatalogV2BlockchainBalanceUpdatesEndpoint =
                        GetCatalogV2BlockchainBalanceUpdatesEndpointImpl(
                            common.amsService,
                            catalogAtlasService,
                        ),
                    getCatalogV2BlockchainBlocksEndpoint =
                        GetCatalogV2BlockchainBlocksEndpointImpl(
                            common.amsService,
                            catalogAtlasService,
                        ),
                    getCatalogV2BlockchainTransactionsEndpoint =
                        GetCatalogV2BlockchainTransactionsEndpointImpl(
                            common.amsService,
                            catalogAtlasService,
                        ),
                    // catalog-all-v2
                    getCatalogAllV2AssetMetricsEndpoint = GetCatalogAllV2AssetMetricsEndpointImpl(common.amsService, assetMetricsService),
                    getCatalogAllV2ExchangeMetricsEndpoint =
                        GetCatalogAllV2ExchangeMetricsEndpointImpl(
                            common.amsService,
                            exchangeMetricsService,
                        ),
                    getCatalogAllV2ExchangeAssetMetricsEndpoint =
                        GetCatalogAllV2ExchangeAssetMetricsEndpointImpl(
                            common.amsService,
                            exchangeAssetMetricsService,
                        ),
                    getCatalogAllV2InstitutionMetricsEndpoint =
                        GetCatalogAllV2InstitutionMetricsEndpointImpl(
                            common.amsService,
                            institutionMetricsService,
                        ),
                    getCatalogAllV2PairMetricsEndpoint =
                        GetCatalogAllV2PairMetricsEndpointImpl(
                            common.amsService,
                            pairMetricsService,
                        ),
                    getCatalogAllV2MarketTradesEndpoint =
                        GetCatalogAllV2MarketTradesEndpointImpl(
                            common.amsService,
                            catalogV2MarketService,
                        ),
                    getCatalogAllV2MarketQuotesEndpoint =
                        GetCatalogAllV2S3MarketQuotesEndpointImpl(
                            common.amsService,
                            catalogV2MarketService,
                        ),
                    getCatalogAllV2MarketOrderbooksEndpoint =
                        GetCatalogAllV2S3MarketOrderbooksEndpointImpl(
                            common.amsService,
                            catalogV2MarketService,
                        ),
                    getCatalogAllV2MarketCandlesEndpoint =
                        GetCatalogAllV2MarketCandlesEndpointImpl(
                            common.amsService,
                            catalogV2MarketService,
                        ),
                    getCatalogAllV2MarketOpeninterestEndpoint =
                        GetCatalogAllV2MarketOpeninterestEndpointImpl(
                            common.amsService,
                            catalogV2MarketService,
                        ),
                    getCatalogAllV2MarketFundingRatesEndpoint =
                        GetCatalogAllV2MarketFundingRatesEndpointImpl(
                            common.amsService,
                            catalogV2MarketService,
                        ),
                    getCatalogAllV2MarketFundingRatesPredictedEndpoint =
                        GetCatalogAllV2MarketFundingRatesPredictedEndpointImpl(
                            common.amsService,
                            catalogV2MarketService,
                        ),
                    getCatalogAllV2MarketLiquidationsEndpoint =
                        GetCatalogAllV2MarketLiquidationsEndpointImpl(
                            common.amsService,
                            catalogV2MarketService,
                        ),
                    getCatalogAllV2MarketGreeksEndpoint =
                        GetCatalogAllV2MarketGreeksEndpointImpl(
                            common.amsService,
                            catalogV2MarketService,
                        ),
                    getCatalogAllV2MarketContractPricesEndpoint =
                        GetCatalogAllV2MarketContractPricesEndpointImpl(
                            common.amsService,
                            catalogV2MarketService,
                        ),
                    getCatalogAllV2MarketImpliedVolatilityEndpoint =
                        GetCatalogAllV2MarketImpliedVolatilityEndpointImpl(
                            common.amsService,
                            catalogV2MarketService,
                        ),
                    getCatalogAllV2MarketMetricsEndpoint =
                        GetCatalogAllV2MarketMetricsEndpointImpl(
                            common.amsService,
                            catalogV2MarketService,
                        ),
                    getCatalogAllV2PairCandlesEndpoint = GetCatalogAllV2PairCandlesEndpointImpl(common.amsService, catalogPairService),
                    getCatalogAllV2IndexLevelsEndpoint = GetCatalogAllV2IndexLevelsEndpointImpl(common.amsService, catalogIndexService),
                    getCatalogAllV2IndexCandlesEndpoint = GetCatalogAllV2IndexCandlesEndpointImpl(common.amsService, catalogIndexService),
                    getCatalogAllV2AssetChainsEndpoint = GetCatalogAllV2AssetChainsEndpointImpl(common.amsService, catalogFarumService),
                    getCatalogAllV2MempoolFeeratesEndpoint =
                        GetCatalogAllV2MempoolFeeratesEndpointImpl(
                            common.amsService,
                            catalogFarumService,
                        ),
                    getCatalogAllV2MiningPoolTipsSummaryEndpoint =
                        GetCatalogAllV2MiningPoolTipsSummaryEndpointImpl(
                            common.amsService,
                            catalogFarumService,
                        ),
                    getCatalogAllV2TransactionTrackerEndpoint =
                        GetCatalogAllV2TransactionTrackerEndpointImpl(
                            common.amsService,
                            catalogFarumService,
                        ),
                    getCatalogAllV2BlockchainAccountsEndpoint =
                        GetCatalogAllV2BlockchainAccountsEndpointImpl(
                            common.amsService,
                            catalogAtlasService,
                        ),
                    getCatalogAllV2BlockchainBalanceUpdatesEndpoint =
                        GetCatalogAllV2BlockchainBalanceUpdatesEndpointImpl(
                            common.amsService,
                            catalogAtlasService,
                        ),
                    getCatalogAllV2BlockchainBlocksEndpoint =
                        GetCatalogAllV2BlockchainBlocksEndpointImpl(
                            common.amsService,
                            catalogAtlasService,
                        ),
                    getCatalogAllV2BlockchainTransactionsEndpoint =
                        GetCatalogAllV2BlockchainTransactionsEndpointImpl(
                            common.amsService,
                            catalogAtlasService,
                        ),
                    getCatalogAssetsEndpoint = getCatalogAssetsEndpoint,
                    getCatalogAssetPairsEndpoint = getCatalogAssetPairsEndpointImpl,
                    getCatalogPairCandlesEndpoint = GetCatalogPairCandlesEndpointImpl(common.amsService, catalogPairService),
                    getCatalogExchangeAssetsEndpoint = exchangeAssetsEndpointImpl,
                    getCatalogInstitutionsEndpoint = getCatalogInstitutionsEndpointImpl,
                    getCatalogAssetAlertRulesEndpoint = getCatalogAssetAlertRulesEndpointImpl,
                    getCatalogExchangesEndpoint = getCatalogExchangesEndpoint,
                    getCatalogIndexesEndpoint = GetCatalogIndexesEndpointImpl(common.amsService, catalogIndexService),
                    getCatalogIndexCandlesEndpoint = GetCatalogIndexCandlesEndpointImpl(common.amsService, catalogIndexService),
                    getCatalogMarketsEndpoint =
                        GetCatalogMarketsEndpointImpl(
                            common.config.clock,
                            common.amsService,
                            catalogMarketService,
                            common.marketStatisticsService,
                        ),
                    getCatalogMarketTradesEndpoint =
                        GetCatalogMarketTradesEndpointImpl(
                            common.amsService,
                            catalogMarketService,
                        ),
                    getCatalogMarketCandlesEndpoint =
                        GetCatalogMarketCandlesEndpointImpl(
                            common.amsService,
                            catalogMarketService,
                        ),
                    getCatalogMarketOrderbooksEndpoint =
                        GetCatalogS3MarketOrderbooksEndpointImpl(
                            common.amsService,
                            catalogMarketService,
                        ),
                    getCatalogMarketQuotesEndpoint =
                        GetCatalogS3MarketQuotesEndpointImpl(
                            common.amsService,
                            catalogMarketService,
                        ),
                    getCatalogMarketFundingRatesEndpoint =
                        GetCatalogMarketFundingRatesEndpointImpl(
                            common.amsService,
                            catalogMarketService,
                        ),
                    getCatalogMarketContractPricesEndpoint =
                        GetCatalogMarketContractPricesEndpointImpl(
                            common.amsService,
                            catalogMarketService,
                        ),
                    getCatalogMarketImpliedVolatilityEndpoint =
                        GetCatalogMarketImpliedVolatilityEndpointImpl(
                            common.amsService,
                            catalogMarketService,
                        ),
                    getCatalogMarketGreeksEndpoint =
                        GetCatalogMarketGreeksEndpointImpl(
                            common.amsService,
                            catalogMarketService,
                        ),
                    getCatalogMarketOpeninterestEndpoint =
                        GetCatalogMarketOpeninterestEndpointImpl(
                            common.amsService,
                            catalogMarketService,
                        ),
                    getCatalogMarketLiquidationsEndpoint =
                        GetCatalogMarketLiquidationsEndpointImpl(
                            common.amsService,
                            catalogMarketService,
                        ),
                    getCatalogMarketMetricsEndpoint =
                        GetCatalogMarketMetricsEndpointImpl(
                            common.amsService,
                            catalogMarketService,
                        ),
                    getCatalogMetricsEndpoint =
                        GetCatalogMetricsEndpointImpl(
                            common.amsService,
                            assetMetricsService,
                        ),
                    getCatalogAssetMetricsEndpoint =
                        GetCatalogAssetMetricsEndpointImpl(
                            common.amsService,
                            assetMetricsService,
                        ),
                    getCatalogExchangeMetricsEndpoint =
                        GetCatalogExchangeMetricsEndpointImpl(
                            common.amsService,
                            exchangeMetricsService,
                        ),
                    getCatalogExchangeAssetMetricsEndpoint =
                        GetCatalogExchangeAssetMetricsEndpointImpl(
                            common.amsService,
                            exchangeAssetMetricsService,
                        ),
                    getCatalogPairMetricsEndpoint =
                        GetCatalogPairMetricsEndpointImpl(
                            common.amsService,
                            pairMetricsService,
                        ),
                    getCatalogInstitutionMetricsEndpoint =
                        GetCatalogInstitutionMetricsEndpointImpl(
                            common.amsService,
                            institutionMetricsService,
                        ),
                    getCatalogAssetChainsEndpoint =
                        GetCatalogAssetChainsEndpointImpl(
                            common.amsService,
                            catalogFarumService,
                        ),
                    getCatalogMempoolFeeratesEndpoint = GetCatalogMempoolFeeratesEndpointImpl(common.amsService, catalogFarumService),
                    getCatalogMiningPoolTipsSummaryEndpoint =
                        GetCatalogMiningPoolTipsSummaryEndpointImpl(
                            common.amsService,
                            catalogFarumService,
                        ),
                    getCatalogTransactionTrackerEndpoint =
                        GetCatalogTransactionTrackerEndpointImpl(
                            common.amsService,
                            catalogFarumService,
                        ),
                    getCatalogAllAssetsEndpoint =
                        GetCatalogAllAssetsEndpointImpl(
                            common.amsService,
                            getCatalogAssetsEndpoint,
                        ),
                    getCatalogAllAssetAlertRulesEndpoint =
                        GetCatalogAllAssetAlertRulesEndpointImpl(
                            common.amsService,
                            getCatalogAssetAlertRulesEndpointImpl,
                        ),
                    getCatalogAllAssetPairsEndpoint =
                        AllAssetPairsEndpointImpl(
                            common.amsService,
                            getCatalogAssetPairsEndpointImpl,
                        ),
                    getCatalogAllPairCandlesEndpoint = GetCatalogAllPairCandlesEndpointImpl(common.amsService, catalogPairService),
                    getCatalogAllExchangeAssetsEndpoint =
                        AllExchangeAssetsEndpointImpl(
                            common.amsService,
                            exchangeAssetsEndpointImpl,
                        ),
                    getCatalogAllInstitutionsEndpoint =
                        AllInstitutionsEndpointImpl(
                            common.amsService,
                            getCatalogInstitutionsEndpointImpl,
                        ),
                    getCatalogAllExchangesEndpoint =
                        AllExchangesEndpointImpl(
                            common.amsService,
                            getCatalogExchangesEndpoint,
                        ),
                    getCatalogAllIndexesEndpoint = GetCatalogAllIndexesEndpointImpl(common.amsService, catalogIndexService),
                    getCatalogAllIndexCandlesEndpoint = GetCatalogAllIndexCandlesEndpointImpl(common.amsService, catalogIndexService),
                    getCatalogAllMarketsEndpoint =
                        AllMarketsEndpointImpl(
                            common.config.clock,
                            common.amsService,
                            catalogMarketService,
                            common.marketStatisticsService,
                        ),
                    getCatalogAllMarketTradesEndpoint =
                        GetCatalogAllMarketTradesEndpointImpl(
                            common.amsService,
                            catalogMarketService,
                        ),
                    getCatalogAllMarketCandlesEndpoint =
                        GetCatalogAllMarketCandlesEndpointImpl(
                            common.amsService,
                            catalogMarketService,
                        ),
                    getCatalogAllMarketOrderbooksEndpoint =
                        GetCatalogAllS3MarketOrderbooksEndpointImpl(
                            common.amsService,
                            catalogMarketService,
                        ),
                    getCatalogAllMarketQuotesEndpoint =
                        GetCatalogAllS3MarketQuotesEndpointImpl(
                            common.amsService,
                            catalogMarketService,
                        ),
                    getCatalogAllMarketFundingRatesEndpoint =
                        GetCatalogAllMarketFundingRatesEndpointImpl(
                            common.amsService,
                            catalogMarketService,
                        ),
                    getCatalogAllMarketContractPricesEndpoint =
                        GetCatalogAllMarketContractPricesEndpointImpl(
                            common.amsService,
                            catalogMarketService,
                        ),
                    getCatalogAllMarketImpliedVolatilityEndpoint =
                        GetCatalogAllMarketImpliedVolatilityEndpointImpl(
                            common.amsService,
                            catalogMarketService,
                        ),
                    getCatalogAllMarketGreeksEndpoint =
                        GetCatalogAllMarketGreeksEndpointImpl(
                            common.amsService,
                            catalogMarketService,
                        ),
                    getCatalogAllMarketOpeninterestEndpoint =
                        GetCatalogAllMarketOpeninterestEndpointImpl(
                            common.amsService,
                            catalogMarketService,
                        ),
                    getCatalogAllMarketLiquidationsEndpoint =
                        GetCatalogAllMarketLiquidationsEndpointImpl(
                            common.amsService,
                            catalogMarketService,
                        ),
                    getCatalogAllMarketMetricsEndpoint =
                        GetCatalogAllMarketMetricsEndpointImpl(
                            common.amsService,
                            catalogMarketService,
                        ),
                    getCatalogAllAssetMetricsEndpoint =
                        GetCatalogAllAssetMetricsEndpointImpl(
                            common.amsService,
                            assetMetricsService,
                        ),
                    getCatalogAllExchangeMetricsEndpoint =
                        GetCatalogAllExchangeMetricsEndpointImpl(
                            common.amsService,
                            exchangeMetricsService,
                        ),
                    getCatalogAllExchangeAssetMetricsEndpoint =
                        GetCatalogAllExchangeAssetMetricsEndpointImpl(
                            common.amsService,
                            exchangeAssetMetricsService,
                        ),
                    getCatalogAllPairMetricsEndpoint =
                        GetCatalogAllPairMetricsEndpointImpl(
                            common.amsService,
                            pairMetricsService,
                        ),
                    getCatalogAllInstitutionMetricsEndpoint =
                        GetCatalogAllInstitutionMetricsEndpointImpl(
                            common.amsService,
                            institutionMetricsService,
                        ),
                    getCatalogAllMetricsEndpoint =
                        GetCatalogAllMetricsEndpointImpl(
                            common.amsService,
                            assetMetricsService,
                        ),
                    getCatalogAllAssetChainsEndpoint =
                        GetCatalogAllAssetChainsEndpointImpl(
                            common.amsService,
                            catalogFarumService,
                        ),
                    getCatalogAllMempoolFeeratesEndpoint =
                        GetCatalogAllMempoolFeeratesEndpointImpl(
                            common.amsService,
                            catalogFarumService,
                        ),
                    getCatalogAllMiningPoolTipsSummaryEndpoint =
                        GetCatalogAllMiningPoolTipsSummaryEndpointImpl(
                            common.amsService,
                            catalogFarumService,
                        ),
                    getCatalogAllTransactionTrackerEndpoint =
                        GetCatalogAllTransactionTrackerEndpointImpl(
                            common.amsService,
                            catalogFarumService,
                        ),
                    getTimeseriesAssetMetricsEndpoint =
                        AssetMetricsEndpointImpl(
                            networkDb = databases.network,
                            hourlyNetworkDb = databases.hourlyNetwork,
                            minutelyNetworkDb = databases.minutelyNetwork,
                            ratesDb = databases.rates,
                            chainMonitorDb = databases.chainMonitor,
                            marketMetricsDb = databases.metrics,
                            factoryDb = databases.factory,
                            principalPriceDb = databases.principalPrice,
                            marketStatisticsService = common.marketStatisticsService,
                            amsService = common.amsService,
                            assetMetricsService = assetMetricsService,
                            communityApiKey = config.common.communityApiKey,
                            clock = common.config.clock,
                        ),
                    getTimeseriesMarketTradesEndpoint =
                        TradesEndpointImpl(
                            objectMapper = common.objectMapper,
                            tradesSpotDb = databases.tradesSpot,
                            tradesDerivDb = databases.tradesDeriv,
                            defiDb = databases.defi,
                            amsService = common.amsService,
                            marketResolvingService = common.marketResolvingService,
                            communityApiKey = config.common.communityApiKey,
                            marketStatisticsService = common.marketStatisticsService,
                            deFiRawDataParser = config.common.deFiRawDataParser,
                        ),
                    getTimeseriesMarketLiquidationsEndpoint =
                        LiquidationsEndpointImpl(
                            databases.futures,
                            common.amsService,
                            marketResolvingService = common.marketResolvingService,
                            communityApiKey = config.common.communityApiKey,
                        ),
                    getTimeseriesMarketFundingRatesEndpoint =
                        FundingRatesEndpointImpl(
                            databases.futures,
                            amsService = common.amsService,
                            marketResolvingService = common.marketResolvingService,
                            communityApiKey = config.common.communityApiKey,
                        ),
                    getTimeseriesMarketFundingRatesPredictedEndpoint =
                        FundingRatesPredictedEndpointImpl(
                            db = databases.tradesDeriv,
                            amsService = common.amsService,
                            marketResolvingService = common.marketResolvingService,
                            marketStatisticsService = common.marketStatisticsService,
                            communityApiKey = config.common.communityApiKey,
                        ),
                    getTimeseriesMarketOpenInteresetEndpoint =
                        OpenInterestEndpointImpl(
                            databases.futures,
                            amsService = common.amsService,
                            marketResolvingService = common.marketResolvingService,
                            communityApiKey = config.common.communityApiKey,
                        ),
                    getTimeseriesMarketQuotesEndpoint =
                    s3QuotesEndpointImpl,
                    getTimeseriesMarketCandlesEndpoint =
                        CandlesEndpointImpl(
                            clock = config.common.clock,
                            dataSourceDelayed = MarketCandlesDelayedDataSource(databases.candles),
                            dataSourceNonDelayed = MarketCandlesNonDelayedDataSource(databases.candles),
                            amsService = common.amsService,
                            marketResolvingService = common.marketResolvingService,
                            communityApiKey = config.common.communityApiKey,
                            marketStatisticsService = common.marketStatisticsService,
                        ),
                    getTimeseriesPairMetricsEndpoint =
                        PairMetricsEndpointImpl(
                            databases.metrics,
                            common.amsService,
                            pairMetricsService,
                        ),
                    getTimeseriesPairCandlesEndpoint =
                        PairCandlesEndpointImpl(
                            amsService = common.amsService,
                            pairCandlesStatistics = pairCandlesStatistics,
                            pairCandlesDataSource = pairCandlesDataSource,
                            communityApiKey = config.common.communityApiKey,
                            clock = common.config.clock,
                        ),
                    getTimeseriesInstitutionMetricsEndpoint =
                        InstitutionMetricsEndpointImpl(
                            databases.futures,
                            common.amsService,
                            institutionMetricsService,
                        ),
                    getTimeseriesExchangeAssetMetricsEndpoint =
                        ExchangeAssetMetricsEndpointImpl(
                            common.amsService,
                            metricsDataSource,
                            exchangeAssetMetricsService,
                        ),
                    getTimeseriesMarketMetricsEndpoint =
                        MarketMetricsEndpointImpl(
                            common.amsService,
                            common.marketResolvingService,
                            common.marketStatisticsService,
                            metricsDataSource,
                        ),
                    getTimeseriesExchangeMetricsEndpoint =
                        ExchangeMetricsEndpointImpl(
                            common.amsService,
                            metricsDataSource,
                            exchangeMetricsService,
                        ),
                    getTimeseriesMarketOrderbooksEndpoint = s3OrderBookEndpointImpl,
                    getTimeseriesIndexCandlesEndpoint =
                        GetTimeseriesIndexCandlesEndpointImpl(
                            common.amsService,
                            indexCandleStatistics,
                            indexCandlesDataSource,
                            config.common.communityApiKey,
                            indexDiscoveryService,
                        ),
                    getTimeseriesIndexLevelsEndpoint =
                        GetTimeseriesIndexLevelsEndpointImpl(
                            databases.indexes,
                            common.amsService,
                            common.objectMapper,
                            indexStatistics,
                            indexDiscoveryService,
                        ),
                    getTimeseriesIndexConstituentsEndpoint =
                        GetTimeseriesIndexConstituentsEndpointImpl(
                            databases.indexes,
                            common.amsService,
                            indexConstituentsStatistics,
                            indexDiscoveryService,
                        ),
                    getTimeseriesStreamAssetMetricsEndpoint =
                        AssetMetricsWsEndpointImpl(
                            config.common.streamingConfig,
                            common.streamConnectionCountLimitService,
                            metricsClientConnections,
                            common.amsService,
                            common.monitoring,
                        ),
                    getTimeseriesStreamMarketLiquidationsEndpoint =
                        MarketLiquidationsWsEndpointImpl(
                            config.common.streamingConfig,
                            common.streamConnectionCountLimitService,
                            liquidationsClientConnections,
                            common.amsService,
                            common.monitoring.streamingMonitoring,
                            common.marketResolvingService,
                        ),
                    getTimeseriesStreamMarketOpenInterestEndpoint =
                        MarketOpenInterestWsEndpointImpl(
                            config.common.streamingConfig,
                            common.streamConnectionCountLimitService,
                            openInterestClientConnections,
                            common.amsService,
                            monitoring,
                            common.marketResolvingService,
                        ),
                    getTimeseriesStreamMarketQuotesEndpoint =
                        MarketQuotesWsEndpointImpl(
                            config.common.streamingConfig,
                            common.streamConnectionCountLimitService,
                            quotesClientConnections,
                            common.amsService,
                            common.marketResolvingService,
                            common.monitoring.streamingMonitoring,
                        ),
                    getTimeseriesStreamPairQuotesEndpoint =
                        PairQuotesWsEndpoint(
                            config.common.streamingConfig,
                            common.streamConnectionCountLimitService,
                            pairQuotesClientConnections,
                            pairMetricsService,
                            common.amsService,
                            common.monitoring.streamingMonitoring,
                        ),
                    getTimeseriesStreamAssetQuotesEndpoint =
                        AssetQuotesWsEndpoint(
                            config.common.streamingConfig,
                            common.streamConnectionCountLimitService,
                            assetQuotesClientConnections,
                            common.amsService,
                            monitoring,
                        ),
                    getTimeseriesStreamMarketCandlesEndpoint =
                        CandlesWsEndpointImpl(
                            config.common.streamingConfig,
                            common.streamConnectionCountLimitService,
                            common.amsService,
                            common.monitoring.streamingMonitoring,
                            candlesClientConnections,
                            common.marketResolvingService,
                        ),
                    getTimeseriesStreamIndexLevelsEndpoint =
                        GetTimeseriesStreamIndexLevelsEndpointImpl(
                            config.common.streamingConfig,
                            common.streamConnectionCountLimitService,
                            common.amsService,
                            common.monitoring.streamingMonitoring,
                            indexLevelsClientConnections,
                            indexDiscoveryService,
                        ),
                    putBlockchainJobAccountBalancesEndpoint =
                        jobService?.let {
                            PutBlockchainJobAccountBalancesEndpointImpl(
                                common.amsService,
                                jobService = it,
                            )
                        },
                    putBlockchainJobBalanceUpdatesEndpoint =
                        jobService?.let {
                            PutBlockchainJobBalanceUpdatesEndpointImpl(
                                common.amsService,
                                jobService = it,
                            )
                        },
                    putBlockchainJobTransactionsEndpoint =
                        jobService?.let {
                            PutBlockchainJobTransactionsEndpointImpl(
                                common.amsService,
                                jobService = it,
                            )
                        },
                    getJobByIdEndpoint =
                        jobService?.let {
                            GetJobByIdEndpointImpl(
                                common.amsService,
                                jobService = it,
                            )
                        },
                    getBlockchainV2ListOfAccountsEndpoint =
                        GetBlockchainV2ListOfAccountsEndpointImpl(
                            databases,
                            blockchainEndpointService,
                            config.common.communityApiKey,
                        ),
                    getBlockchainV2ListOfSubAccountsEndpoint =
                        GetBlockchainV2ListOfSubAccountsEndpointImpl(
                            databases,
                            blockchainEndpointService,
                            config.common.communityApiKey,
                        ),
                    getBlockchainV2ListOfBlocksEndpoint =
                        GetBlockchainV2ListOfBlocksEndpointImpl(
                            databases,
                            blockchainEndpointService,
                            config.common.communityApiKey,
                        ),
                    getBlockchainV2ListOfTransactionsEndpoint =
                        GetBlockchainV2ListOfTransactionsEndpointImpl(
                            databases,
                            blockchainEndpointService,
                            config.common.communityApiKey,
                        ),
                    getBlockchainV2ListOfBalanceUpdatesEndpoint =
                        GetBlockchainV2ListOfBalanceUpdatesEndpointImpl(
                            databases,
                            blockchainEndpointService,
                            config.common.communityApiKey,
                        ),
                    getBlockchainV2ListOfBalanceUpdatesForAccountEndpoint =
                        GetBlockchainV2ListOfBalanceUpdatesForAccountEndpointImpl(
                            databases,
                            blockchainEndpointService,
                        ),
                    getBlockchainV2FullBlockEndpoint =
                        GetBlockchainV2FullBlockEndpointImpl(
                            databases,
                            blockchainEndpointService,
                            config.common.communityApiKey,
                        ),
                    getBlockchainV2FullTransactionEndpoint =
                        GetBlockchainV2FullTransactionEndpointImpl(
                            databases,
                            blockchainEndpointService,
                            config.common.communityApiKey,
                        ),
                    getBlockchainV2FullTransactionForBlockEndpoint =
                        GetBlockchainV2FullTransactionForBlockEndpointImpl(
                            databases,
                            blockchainEndpointService,
                            config.common.communityApiKey,
                        ),
                    getTimeseriesMiningPoolTipsSummaryEndpoint =
                        MiningPoolTipsSummaryEndpointImpl(
                            databases.chainMonitor,
                            common.amsService,
                        ),
                    getTxTrackerEndpoint = txTrackerEndpointImpl,
                    getMempoolFeeratesEndpoint = MempoolFeeratesEndpointImpl(databases.chainMonitor, common.amsService),
                    getAssetAlertsEndpoint = AssetAlertsEndpointImpl(databases.chainMonitor, common.amsService),
                    getDefiBalanceSheetsEndpoint = GetDefiBalanceSheetsEndpointImpl(databases.defi, common.amsService, defiStatistics),
                    getAssetChainsEndpoint = AssetChainsEndpointImpl(databases.chainMonitor, common.amsService),
                    getTimeseriesMarketImpliedVolatilityEndpoint =
                        ImpliedVolatilityEndpointImpl(
                            databases.futures,
                            common.amsService,
                            common.marketResolvingService,
                            common.marketStatisticsService,
                            config.common.communityApiKey,
                        ),
                    getTimeseriesMarketContractPricesEndpoint =
                        ContractPricesEndpointImpl(
                            databases.futures,
                            databases.tradesDeriv,
                            common.amsService,
                            common.marketResolvingService,
                            common.marketStatisticsService,
                            config.common.communityApiKey,
                        ),
                    getTimeseriesMarketGreeksEndpoint =
                        GreeksEndpointImpl(
                            databases.futures,
                            common.amsService,
                            common.marketResolvingService,
                            common.marketStatisticsService,
                            config.common.communityApiKey,
                        ),
                    getTaxonomyAssetsEndpoint =
                        GetTaxonomyAssetsEndpointImpl(
                            common.objectMapper,
                            common.amsService,
                            taxonomyService,
                        ),
                    getTaxonomyMetadataAssetsEndpoint =
                        GetTaxonomyMetadataAssetsEndpointImpl(
                            common.objectMapper,
                            common.amsService,
                            taxonomyService,
                        ),
                    getTxTrackerSettlementEndpoint =
                        TxTrackerSettlementEndpointImpl(
                            databases,
                            common.amsService,
                            txTrackerEndpointImpl,
                        ),
                    getAssetProfilesEndpoint = GetAssetProfilesEndpointImpl(common.amsService, assetProfilesLocalStorage),
                    getNetworkProfilesEndpoint = GetNetworkProfilesEndpointImpl(common.amsService, networkProfilesLocalStorage),
                    getBlockchainMetadataTagsEndpoint = GetBlockchainMetadataTagsEndpointImpl(common.amsService, addressTaggingStatistics),
                    getBlockchainMetadataEntitiesEndpoint =
                        GetBlockchainMetadataTaggedEntitiesEndpointImpl(
                            common.amsService,
                            addressTaggingStatistics,
                            databases.addressTagging,
                        ),
                    getSecurityMasterAssetsEndpoint =
                        GetSecurityMasterAssetsEndpointImpl(
                            common.amsService,
                            securityMasterLocalStorage,
                            assetProfilesLocalStorage,
                        ),
                    getSecurityMasterMarketsEndpoint =
                        GetSecurityMasterMarketsEndpointImpl(
                            common.amsService,
                            catalogV2MarketService,
                            common.marketStatisticsService,
                            endOfDayMarketCandleStatistics,
                            securityMasterLocalStorage,
                        ),
                    getConstituentSnapshotsAssetMetricsEndpoint =
                        GetConstituentSnapshotsAssetMetricsEndpointImpl(
                            common.amsService,
                            assetMetricConstituentProviders,
                        ),
                    getConstituentTimeframesAssetMetricsEndpoint =
                        GetConstituentTimeframesAssetMetricsEndpointImpl(
                            common.amsService,
                            assetMetricConstituentProviders,
                        ),
                ).generatedPaths.also { paths ->
                    paths["/v4/internal/taxonomy-refresh"] = LocalDataRefreshEndpoint(taxonomyDataLoader)
                    paths["/v4/internal/network-profiles-refresh"] = LocalDataRefreshEndpoint(networkProfilesDataLoader)
                    paths["/v4/internal/asset-profiles-refresh"] = LocalDataRefreshEndpoint(assetProfilesDataLoader)
                    paths["/v4/internal/security-master-refresh"] = LocalDataRefreshEndpoint(securityMasterDataLoader)

                    // Stubs for removed Atlas v1 endpoints.
                    listOf(
                        "/v4/blockchain/{asset}/accounts",
                        "/v4/blockchain/{asset}/balance-updates",
                        "/v4/blockchain/{asset}/blocks",
                        "/v4/blockchain/{asset}/blocks/{block_hash}",
                        "/v4/blockchain/{asset}/blocks/{block_hash}/transactions/{txid}",
                        "/v4/blockchain/{asset}/transactions",
                        "/v4/blockchain/{asset}/transactions/{txid}",
                    ).forEach {
                        paths[it] =
                            object : Endpoint<Nothing> {
                                override suspend fun handle(httpRequest: HttpRequest) =
                                    Response.errorResponse<Nothing>(
                                        ApiError.NotFoundWithMessage(
                                            "The ATLAS v1 API is deprecated and removed. Please, migrate to the ATLAS v2 API " +
                                                "(https://docs.coinmetrics.io/api/v4/#tag/List-of-blockchain-entities-v2 and " +
                                                "https://docs.coinmetrics.io/api/v4/#tag/Full-blockchain-entities-v2). " +
                                                "For additional details on ATLAS v2 please refer to: https://coinmetrics.io/atlas-upgrade/",
                                        ),
                                    )
                            }
                    }
                }
            },
        )

    private fun createNewNetworkMetricsDataPoller() =
        NetworkDataPoller(
            name = "new-bbb-metrics",
            clientConnections = metricsClientConnections,
            pollIntervalMs = config.realtimeMetricsUpdateFrequencyMs,
            produceReorgEvents = true,
            metricsResolver = {
                when (
                    val result =
                        common.amsService.discovery(
                            resource = "asset_metrics",
                            target = "asset,metric",
                            filters = mapOf("asset" to "sol", "frequency" to "1b"),
                        )
                ) {
                    is DiscoveryResult.Success -> {
                        result.values
                            .asSequence()
                            .filter { !metricsIgnoredByNetworkDataPoller.contains(it[1]) }
                            .filter { !Resources.isFlowMetric(it[1]) } // remove flow metrics
                            .groupByTo(HashMap(), { it[0] }, { it[1] }) // group by asset
                    }

                    is DiscoveryResult.Failed -> {
                        throw IllegalStateException("Fail to discover new bbb network metrics: $result.")
                    }
                }
            },
            dataProviderFactory = { asset, metrics ->
                BbbNewNetworkMetricsDataProvider(databases.factory, asset, metrics)
            },
        )

    private fun createNetworkMetricsDataPoller() =
        NetworkDataPoller(
            name = "bbb-metrics",
            clientConnections = metricsClientConnections,
            pollIntervalMs = config.realtimeMetricsUpdateFrequencyMs,
            produceReorgEvents = true,
            metricsResolver = {
                when (
                    val result =
                        common.amsService.discovery(
                            resource = "asset_metrics",
                            target = "asset,metric",
                            filters = mapOf("asset" to assetsSupportedByNetworkDataPoller, "frequency" to "1b"),
                        )
                ) {
                    is DiscoveryResult.Success -> {
                        result.values
                            .asSequence()
                            .filter { !metricsIgnoredByNetworkDataPoller.contains(it[1]) }
                            .filter { !Resources.isFlowMetric(it[1]) } // remove flow metrics
                            .groupByTo(HashMap(), { it[0] }, { it[1] }) // group by asset
                    }

                    is DiscoveryResult.Failed -> {
                        throw IllegalStateException("Fail to discovery bbb network metrics: $result.")
                    }
                }
            },
            dataProviderFactory = { asset, metrics ->
                BbbNetworkMetricsDataProvider(databases.network, asset, metrics)
            },
        )

    private fun createFlowMetricsDataPoller() =
        NetworkDataPoller(
            name = "flow-bbb-metrics",
            clientConnections = metricsClientConnections,
            pollIntervalMs = config.realtimeMetricsUpdateFrequencyMs,
            produceReorgEvents = false,
            metricsResolver = {
                when (
                    val result =
                        common.amsService.discovery(
                            resource = "asset_metrics",
                            target = "asset,metric",
                            filters = mapOf("asset" to assetsSupportedByNetworkDataPoller, "frequency" to "1b"),
                        )
                ) {
                    is DiscoveryResult.Success -> {
                        result.values
                            .asSequence()
                            .filter { !metricsIgnoredByNetworkDataPoller.contains(it[1]) }
                            .filter { Resources.isFlowMetric(it[1]) } // retain only flow metrics
                            .groupByTo(HashMap(), { it[0] }, { it[1] }) // group by asset
                    }

                    is DiscoveryResult.Failed -> {
                        throw IllegalStateException("Fail to discovery flow bbb network metrics: $result.")
                    }
                }
            },
            dataProviderFactory = { asset, metrics ->
                BbbFlowMetricsDataProvider(databases.network, asset, metrics)
            },
        )

    private fun generateDynamicTiersForBooks(booksDataType: S3BooksMarketType): List<DynamicTierConfig> =
        DynamicTieringConfigGenerator.generate(
            staticTierConfigs = config.booksTiers[booksDataType] ?: emptyList(),
            availableTimeRangeResolver = { tierName ->
                when (BookTierName.valueOf(tierName)) {
                    BookTierName.HOT -> {
                        // postgres-only statistics
                        common.marketStatisticsService
                            .getPostgresBooksStatistics(booksDataType)
                            ?.let { stats ->
                                Range.closed(Instant.parse(stats.minTime), Instant.parse(stats.maxTime))
                            }
                    }
                    BookTierName.COLD -> {
                        // s3-only statistics
                        common.marketStatisticsService
                            .getS3BooksStatistics(booksDataType)
                            ?.let { stats ->
                                Range.closed(Instant.parse(stats.minTime), Instant.parse(stats.maxTime))
                            }
                    }
                }
            },
        )

    override fun tiers(): Map<String, List<DynamicTierConfig>> =
        config.booksTiers.entries.associate { (bookDataType, _) ->
            bookDataType.toString() to generateDynamicTiersForBooks(bookDataType)
        }

    override fun databases(): List<Database> = databases.list

    override fun kafkas(): List<KafkaDataProcessorInfo> {
        val kafkaDataProcessors =
            listOf(
                candlesDataProvider,
                pairQuotesDataProvider,
                assetQuotesDataProvider,
            ) + marketQuotesDataProviders +
                assetRatesDataProviders +
                assetPrincipalPriceDataProviders +
                indexLevelsDataProviders +
                liquidationsKafkaDataProviders +
                openInterestKafkaDataProviders
        return kafkaDataProcessors.map { it.toKafkaDataProcessorInfo() }
    }

    override suspend fun start() {
        assetMetricsService.init()

        networkMetricsDataPoller.start()
        newNetworkMetricsDataPoller.start()
        flowMetricsDataPoller.start()
        assetRatesDataProviders.forEach { it.start() }
        assetPrincipalPriceDataProviders.forEach { it.start() }
        candlesDataProvider.start()
        candlesClientConnections.start()
        quotesClientConnections.start()
        liquidationsClientConnections.start()
        openInterestClientConnections.start()
        marketQuotesDataProviders.forEach {
            it.start()
        }
        pairQuotesDataProvider.start()
        assetQuotesDataProvider.start()
        indexLevelsDataProviders.forEach { it.start() }
        liquidationsKafkaDataProviders.forEach { it.start() }
        openInterestKafkaDataProviders.forEach { it.start() }
        taxonomyDataLoader.load()
        networkProfilesDataLoader.load()
        assetProfilesDataLoader.load()
        securityMasterDataLoader.load()

        router.init()
    }

    override fun resolveEndpoint(httpRequest: HttpRequest): Endpoint<*>? = router.endpoint(httpRequest)

    override suspend fun close() {
        networkMetricsDataPoller.close()
        newNetworkMetricsDataPoller.close()
        flowMetricsDataPoller.close()
        assetRatesDataProviders.forEach { it.close() }
        assetPrincipalPriceDataProviders.forEach { it.close() }
        candlesDataProvider.close()
        marketQuotesDataProviders.forEach {
            it.close()
        }
        pairQuotesDataProvider.close()
        assetQuotesDataProvider.close()
        indexLevelsDataProviders.forEach { it.close() }
        booksS3StorageClients.values.forEach { it?.close() }

        metricsClientConnections.close()
        liquidationsClientConnections.close()
        openInterestClientConnections.close()
        quotesClientConnections.close()
        pairQuotesClientConnections.close()
        assetQuotesClientConnections.close()
        liquidationsKafkaDataProviders.forEach { it.close() }
        openInterestKafkaDataProviders.forEach { it.close() }
        candlesClientConnections.close()
        indexLevelsClientConnections.close()

        gitDispatcher.close()
        jobService?.close()

        try {
            databases.close()
        } catch (e: Exception) {
            log.error("Error closing databases", e)
        }
    }
}
