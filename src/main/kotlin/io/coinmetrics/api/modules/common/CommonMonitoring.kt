package io.coinmetrics.api.modules.common

import io.coinmetrics.api.monitoring.AmsMonitoring
import io.coinmetrics.api.monitoring.MarketResolvingMonitoring
import io.coinmetrics.api.monitoring.StreamingMonitoring
import io.coinmetrics.api.monitoring.VisKeyMonitoring
import io.coinmetrics.api.statistics.StatisticsMonitoring
import io.coinmetrics.api.utils.KafkaClientMetrics
import io.coinmetrics.bookstreams.kafka.KafkaClientId
import io.prometheus.metrics.core.metrics.Counter
import io.prometheus.metrics.core.metrics.Gauge
import io.prometheus.metrics.core.metrics.Histogram
import io.prometheus.metrics.instrumentation.jvm.JvmMetrics
import io.prometheus.metrics.model.registry.PrometheusRegistry
import java.lang.management.ManagementFactory

class CommonMonitoring {
    internal val registry =
        PrometheusRegistry().also {
            JvmMetrics.builder().register(it)
            Gauge
                .builder()
                .name("jvm_start_time_seconds")
                .help(
                    "Start time of the process since unix epoch in seconds. This is identical to process_start_seconds " +
                        "but also includes k8s_node and pod_ip (obtained from NODENAME, POD_IP env vars).",
                ).labelNames("k8s_node", "pod_ip")
                .register(it)
                .also {
                    it
                        .labelValues(System.getenv("NODENAME") ?: "", System.getenv("POD_IP") ?: "")
                        .set(ManagementFactory.getRuntimeMXBean().startTime / 1000.0)
                }
        }

    val threadQueuePending: Gauge =
        Gauge
            .builder()
            .name("api_thread_queue_pending")
            .help("The number of pending tasks in the execution queue for a thread executor.")
            .labelNames("dispatcher")
            .register(registry)

    val threadQueueTime: Histogram =
        Histogram
            .builder()
            .name("api_thread_queue_time_seconds")
            .help("Time a task spends in the execution queue for a thread executor.")
            .labelNames("dispatcher")
            .classicUpperBounds(
                .0002,
                .0005,
                .0007,
                .001,
                .002,
                .003,
                .004,
                .005,
                .01,
                .015,
                0.05,
                0.1,
                0.2,
                0.3,
                0.4,
                0.6,
                0.8,
                1.0,
                2.0,
                4.0,
                6.0,
                8.0,
                10.0,
                15.0,
            ).register(registry)

    val netThreadQueuePending: Gauge =
        Gauge
            .builder()
            .name("net_thread_queue_pending")
            .help("The number of pending tasks in the execution queue for a net thread.")
            .labelNames("thread")
            .register(registry)

    val netThreadQueueTime: Histogram =
        Histogram
            .builder()
            .name("net_thread_queue_time_seconds")
            .help("Time a task spends in the execution queue for a net thread.")
            .classicUpperBounds(
                .0002,
                .0005,
                .0007,
                .001,
                .002,
                .003,
                .004,
                .005,
                .01,
                .015,
                0.05,
                0.1,
                0.2,
                0.3,
                0.4,
                0.6,
                0.8,
                1.0,
                2.0,
                4.0,
                6.0,
                8.0,
                10.0,
                15.0,
            ).labelNames("thread")
            .register(registry)

    val processingThreadQueuePending: Gauge =
        Gauge
            .builder()
            .name("processing_thread_queue_pending")
            .help("The number of pending tasks in the execution queue for a net thread.")
            .register(registry)

    val processingThreadQueueTime: Histogram =
        Histogram
            .builder()
            .name("processing_thread_queue_time_seconds")
            .help("Time a task spends in the execution queue for a net thread.")
            .classicUpperBounds(
                .0002,
                .0005,
                .0007,
                .001,
                .002,
                .003,
                .004,
                .005,
                .01,
                .015,
                0.05,
                0.1,
                0.2,
                0.3,
                0.4,
                0.6,
                0.8,
                1.0,
                2.0,
                4.0,
                6.0,
                8.0,
                10.0,
                15.0,
            ).register(registry)

    val requestsTotal: Counter =
        Counter
            .builder()
            .name("requests_total")
            .labelNames("path", "key")
            .help("Total requests made to the server.")
            .register(registry)

    val nonOkResponses: Counter =
        Counter
            .builder()
            .name("non_ok_responses_total")
            .help("Number of non-ok HTTP responses.")
            .labelNames("code", "api_key")
            .register(registry)

    val requestDuration: Histogram =
        Histogram
            .builder()
            .name("requests_latency_seconds")
            .help("First-byte response latency in seconds.")
            .classicUpperBounds(
                .0002,
                .0005,
                .0007,
                .001,
                .002,
                .003,
                .004,
                .005,
                .01,
                .015,
                0.05,
                0.1,
                0.2,
                0.3,
                0.4,
                0.6,
                0.8,
                1.0,
                2.0,
                4.0,
                6.0,
                8.0,
                10.0,
                15.0,
            ).labelNames("path", "api_key")
            .register(registry)

    val requestsActive: Gauge =
        Gauge
            .builder()
            .name("requests_active")
            .help("Active http requests")
            .labelNames("endpoint", "api_key")
            .register(registry)

    val requestsQueuePending: Gauge =
        Gauge
            .builder()
            .name("requests_queue_pending")
            .help("The number pending HTTP requests")
            .labelNames("endpoint", "api_key")
            .register(registry)

    val requestsQueueTime: Histogram =
        Histogram
            .builder()
            .name("requests_queue_time_seconds")
            .help("Time a task spends in the execution queue for a net thread.")
            .classicUpperBounds(
                .0002,
                .0005,
                .0007,
                .001,
                .002,
                .003,
                .004,
                .005,
                .01,
                .015,
                0.05,
                0.1,
                0.2,
                0.3,
                0.4,
                0.6,
                0.8,
                1.0,
                2.0,
                4.0,
                6.0,
                8.0,
                10.0,
                15.0,
                30.0,
                60.0,
                80.0,
                100.0,
            ).labelNames("endpoint", "api_key")
            .register(registry)

    val requestsQueueOverflow: Counter =
        Counter
            .builder()
            .name("requests_queue_overflow")
            .help("The of rejected HTTP requests due to request queue overflow.")
            .labelNames("api_key")
            .register(registry)

    val trafficSentBytes: Counter =
        Counter
            .builder()
            .name("api_traffic_sent_bytes")
            .help("How much uncompressed data each client consumed.")
            .labelNames("path", "api_key")
            .register(registry)

    val trafficDelays: Counter =
        Counter
            .builder()
            .name("api_traffic_delays_seconds")
            .help("How long traffic is delayed.")
            .labelNames("api_key")
            .register(registry)

    val historyEntries: Gauge =
        Gauge
            .builder()
            .name("history_entries")
            .help("How many entries are stored in the history.")
            .labelNames("type")
            .register(registry)

    val streamingMonitoring = StreamingMonitoring(registry)
    val amsMonitoring = AmsMonitoring(registry)
    val visKeyMonitoring = VisKeyMonitoring(registry)

    val statisticsMonitoring = StatisticsMonitoring(registry)
    val marketResolvingMonitoring = MarketResolvingMonitoring(registry)

    init {
        KafkaClientMetrics().run {
            clientIdParser { clientIdStr ->
                KafkaClientId.fromString(clientIdStr)?.keyValues ?: mapOf()
            }
            register(registry)
        }
    }
}
