package io.coinmetrics.api.modules.streamingbooks

import io.coinmetrics.api.Paths
import io.coinmetrics.api.Router
import io.coinmetrics.api.endpoints.Endpoint
import io.coinmetrics.api.endpoints.stream.market.orderbooks.GetTimeseriesStreamMarketOrderbooksEndpointImpl
import io.coinmetrics.api.endpoints.stream.market.orderbooks.MarketOrderBooksClientConnections
import io.coinmetrics.api.endpoints.stream.market.orderbooks.test.TestBookStreams
import io.coinmetrics.api.model.KafkaDataProcessorInfo
import io.coinmetrics.api.modules.ApiModule
import io.coinmetrics.api.modules.ModuleName
import io.coinmetrics.api.modules.common.CommonModule
import io.coinmetrics.bookstreams.BookConsumer
import io.coinmetrics.bookstreams.BookStreams
import io.coinmetrics.bookstreams.Depth
import io.coinmetrics.bookstreams.kafka.Monitoring
import io.coinmetrics.bookstreams.kafka.config.BookStreamsConfig
import io.coinmetrics.bookstreams.kafka.newKafkaBookStreams
import io.coinmetrics.httpserver.HttpRequest
import kotlinx.coroutines.channels.Channel

class StreamingBooksApiModule(
    common: CommonModule,
    val config: StreamingBooksApiConfig = StreamingBooksApiConfig(common.config),
) : ApiModule {
    override val name = ModuleName.STREAMING_BOOKS

    private val monitoring = StreamingBooksApiMonitoring(common.monitoring)
    private lateinit var bookStreams: BookStreams
    private lateinit var bookConsumer: BookConsumer
    private val orderbooksClientConnections =
        MarketOrderBooksClientConnections(
            config.commonConfig.streamingConfig,
            monitoring,
            common.streamConnectionCountLimitService,
            lazy { bookConsumer },
            common.marketResolvingService,
            common.marketClientConnectionService,
            config.statisticsUpdateIntervalMs,
        )

    private val router =
        Router(
            Paths(
                getTimeseriesStreamMarketOrderbooksEndpoint =
                    GetTimeseriesStreamMarketOrderbooksEndpointImpl(
                        common.streamConnectionCountLimitService,
                        orderbooksClientConnections,
                        common.amsService,
                        common.marketResolvingService,
                    ),
            ).generatedPaths,
        )

    override suspend fun start() {
        createBookStreamsClient()
        orderbooksClientConnections.start()
        router.init()
    }

    override fun resolveEndpoint(httpRequest: HttpRequest): Endpoint<*>? = router.endpoint(httpRequest)

    override fun kafkas(): List<KafkaDataProcessorInfo> = config.kafkaBooksConfigsPerExchange.map { KafkaDataProcessorInfo.of(it.config) }

    override suspend fun close() {
        orderbooksClientConnections.close()
        if (this::bookStreams.isInitialized) {
            this.bookStreams.close()
        }
    }

    private suspend fun createBookStreamsClient() {
        val serverUrls =
            config.kafkaBooksConfigsPerExchange
                .map {
                    it.config.kafkaServers
                        .map { s -> s.serverUrl }
                        .toSet()
                }.distinct()
        require(serverUrls.size <= 1)
        require(serverUrls.isEmpty() || serverUrls.first().isNotEmpty())

        bookStreams =
            if (serverUrls.isNotEmpty() && config.testBookMessageChannel == null) {
                newKafkaBookStreams(
                    BookStreamsConfig(
                        appId = "streaming-books-api",
                        instanceId = config.commonConfig.instanceId,
                        pipelines =
                            serverUrls.first().map { broker ->
                                BookStreamsConfig.Pipeline(broker)
                            },
                        monitoring = Monitoring(monitoring.commonMonitoring.registry),
                        consumerConfig =
                            mapOf(
                                "max.partition.fetch.bytes" to 3_000_000,
                                "max.poll.records" to 10_000,
                            ),
                    ),
                )
            } else {
                TestBookStreams(
                    config.testBookMessageChannel ?: Channel(),
                    exchangeIds = config.kafkaBooksConfigsPerExchange.map { it.exchange.id }.toSet(),
                )
            }
        bookConsumer = bookStreams.newConsumer()
        bookConsumer.subscribe {
            for (exchangeId in config.kafkaBooksConfigsPerExchange.map { it.exchange.id }) {
                add(exchangeId, Depth.Full)
                add(exchangeId, Depth.Top(100))
            }
        }
    }
}
