package io.coinmetrics.api.modules

enum class ModuleName {
    COMMON,
    MAIN,
    STREAMING_BOOKS,
    STREAMING_TRADES,
    ;

    companion object {
        fun resolve(name: String): ModuleName? =
            try {
                valueOf(name.uppercase())
            } catch (e: IllegalArgumentException) {
                null
            }
    }

    override fun toString(): String = name.lowercase()
}
