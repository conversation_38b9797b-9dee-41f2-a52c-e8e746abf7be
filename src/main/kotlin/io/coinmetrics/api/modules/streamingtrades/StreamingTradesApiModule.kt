package io.coinmetrics.api.modules.streamingtrades

import io.coinmetrics.api.Paths
import io.coinmetrics.api.Router
import io.coinmetrics.api.endpoints.Endpoint
import io.coinmetrics.api.endpoints.stream.market.trades.MarketTradesClientConnections
import io.coinmetrics.api.endpoints.stream.market.trades.MarketTradesProcessor
import io.coinmetrics.api.endpoints.stream.market.trades.MarketTradesWsEndpointImpl
import io.coinmetrics.api.endpoints.stream.market.trades.handler.DefaultMarketTradeHandler
import io.coinmetrics.api.endpoints.stream.market.trades.handler.DefiMarketTradeHandler
import io.coinmetrics.api.endpoints.stream.market.trades.model.TradeKey
import io.coinmetrics.api.model.KafkaDataProcessorInfo
import io.coinmetrics.api.modules.ApiModule
import io.coinmetrics.api.modules.ModuleName
import io.coinmetrics.api.modules.common.CommonModule
import io.coinmetrics.api.modules.main.kafkaConfigsPerExchangeToTriples
import io.coinmetrics.api.utils.CircularHash
import io.coinmetrics.api.utils.MonitoredFixedThreadPoolDispatcher
import io.coinmetrics.httpserver.HttpRequest
import org.slf4j.Logger
import org.slf4j.LoggerFactory

class StreamingTradesApiModule(
    common: CommonModule,
    private val config: StreamingTradesApiConfig = StreamingTradesApiConfig(common.config),
) : ApiModule {
    private val log: Logger = LoggerFactory.getLogger(StreamingTradesApiModule::class.java)

    override val name = ModuleName.STREAMING_TRADES

    private val monitoring = StreamingTradesApiMonitoring(common.monitoring)

    private val defiTradeHandler = DefiMarketTradeHandler(common.objectMapper, common.marketResolvingService)
    private val defaultTradeHandler = DefaultMarketTradeHandler(common.marketResolvingService)

    private val tradesClientConnections =
        MarketTradesClientConnections(
            monitoring,
            common.streamConnectionCountLimitService,
            common.marketClientConnectionService,
            config.statisticsUpdateIntervalMs,
        )

    private val sharedTradesDispatcher =
        MonitoredFixedThreadPoolDispatcher(
            name = "trades-processor",
            threadsCount = config.streamingTradesThreads,
            monitoring.commonMonitoring,
        )

    private val router =
        Router(
            Paths(
                getTimeseriesStreamMarketTradesEndpoint =
                    MarketTradesWsEndpointImpl(
                        config.common.streamingConfig,
                        common.streamConnectionCountLimitService,
                        tradesClientConnections,
                        common.amsService,
                        common.monitoring.streamingMonitoring,
                        common.marketResolvingService,
                    ),
            ).generatedPaths,
        )

    val streamingMarketTradesProcessors =
        kafkaConfigsPerExchangeToTriples(
            name = "Trades",
            kafkaConfigsPerExchange = config.kafkaTradesConfigsPerExchange,
        ).map { (exchange, tradesConfig, cacheSize) ->
            log.info("Using trades deduplication cache size $cacheSize for exchange '${exchange.getNormalizedName()}'.")
            val marketTradeKeysCache = CircularHash<TradeKey, Boolean>(cacheSize)
            val marketTradeHandler = if (exchange.defi) defiTradeHandler else defaultTradeHandler
            MarketTradesProcessor(
                exchange,
                tradesConfig,
                monitoring,
                tradesClientConnections,
                marketTradeKeysCache,
                sharedDispatcher = sharedTradesDispatcher.dispatcher,
                marketTradeHandler,
                common.config.clock,
            )
        }

    override suspend fun start() {
        tradesClientConnections.start()
        streamingMarketTradesProcessors.forEach { it.start() }
        router.init()
    }

    override fun resolveEndpoint(httpRequest: HttpRequest): Endpoint<*>? = router.endpoint(httpRequest)

    override fun kafkas(): List<KafkaDataProcessorInfo> =
        config.kafkaTradesConfigsPerExchange.map { (_, c, _) -> KafkaDataProcessorInfo.of(c) }

    override suspend fun close() {
        tradesClientConnections.close()
        streamingMarketTradesProcessors.forEach { it.close() }
        sharedTradesDispatcher.close()
    }
}
