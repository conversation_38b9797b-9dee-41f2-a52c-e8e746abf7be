package io.coinmetrics.api.service.catalog.metric.impl

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.ChunkedResponseFormat
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.NextPageInfo
import io.coinmetrics.api.ams.DiscoveryResult
import io.coinmetrics.api.endpoints.timeseries.asset.datasources.MetricDataUtils
import io.coinmetrics.api.model.TimeRestriction
import io.coinmetrics.api.model.page.StringPageRequest
import io.coinmetrics.api.models.AssetMetricFrequency
import io.coinmetrics.api.models.AssetMetricInfo
import io.coinmetrics.api.models.CatalogAssetMetricFrequency
import io.coinmetrics.api.models.CatalogAssetMetricInfo
import io.coinmetrics.api.models.CatalogAssetMetricsResponse
import io.coinmetrics.api.models.CatalogMetricInfo
import io.coinmetrics.api.models.CatalogMetricsResponse
import io.coinmetrics.api.models.CatalogV2AssetMetricsInfo
import io.coinmetrics.api.models.PagingFrom
import io.coinmetrics.api.modules.main.MainApiMonitoring
import io.coinmetrics.api.resources.Resources
import io.coinmetrics.api.resources.constituents.AssetMetricConstituentProvider
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.catalog.metric.MetricAvailability
import io.coinmetrics.api.service.catalog.metric.MetricsService
import io.coinmetrics.api.service.catalog.metric.toFullMetricsInfo
import io.coinmetrics.api.statistics.metrics.AssetMetricStatistics
import io.coinmetrics.api.statistics.metrics.AssetMetricStatisticsModel
import io.coinmetrics.api.toFailure
import io.coinmetrics.api.toSuccess
import io.coinmetrics.api.utils.ExperimentalAssetMetrics
import io.coinmetrics.api.utils.FrequencyComparator
import io.coinmetrics.api.utils.TimeUtils
import io.coinmetrics.api.utils.paging.PagingUtils
import io.coinmetrics.httpserver.HttpRequest
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import org.slf4j.LoggerFactory
import java.time.Clock
import java.time.temporal.ChronoUnit
import java.util.TreeMap

class AssetMetricsService(
    docsBaseUrl: String,
    private val communityApiKey: String,
    private val amsService: AmsService,
    private val monitoring: MainApiMonitoring,
    private val assetMetricStatistics: AssetMetricStatistics,
    private val assetMetricConstituentProviders: Map<String, AssetMetricConstituentProvider>,
    ndExperimentalAssetMetrics: String,
    private val clock: Clock,
) : MetricsService(docsBaseUrl) {
    companion object {
        private val log = LoggerFactory.getLogger(AssetMetricsService::class.java)
        private val amsParamsToEndpointParams =
            hashMapOf(
                "asset" to "assets",
                "metric" to "metrics",
                "frequency" to "frequency",
            )
        private val urlTemplate: (String, String, String, String?) -> String = { origin, suffix, metric, apiKey ->
            val apiKeyParam = if (apiKey == null) "" else "&api_key=$apiKey"
            "$origin/v4/constituent-$suffix/asset-metrics?metric=$metric$apiKeyParam"
        }
    }

    /**
     * Set of Triple<Asset, Metric, Frequency>
     */
    private lateinit var communityAssetMetrics: Set<Triple<String, String, String>>

    private val experimentalAssetMetrics by lazy { ExperimentalAssetMetrics(ndExperimentalAssetMetrics) }

    suspend fun init() {
        communityAssetMetrics = getCommunityAssetMetrics(communityApiKey)
        log.info("Community asset metrics initialisation completed.")
        // todo schedule a regular update of communityAssetMetrics?
    }

    suspend fun findSupportedCatalogV1AssetMetrics(
        apiKey: String?,
        metrics: List<String>?,
        reviewable: Boolean?,
    ): FunctionResult<ApiError, CatalogAssetMetricsResponse> =
        findCatalogV1Metrics(apiKey, metrics?.toSet(), reviewable)
            .toFullMetricsInfo { metricName, metricInfo, assetsPerFrequency ->
                CatalogAssetMetricInfo(
                    metric = metricName,
                    fullName = metricInfo.name,
                    description = metricInfo.description,
                    product = metricInfo.product,
                    category = metricInfo.category,
                    subcategory = metricInfo.subcategory,
                    unit = metricInfo.unit,
                    dataType = metricInfo.dataType.lowercase(),
                    type = metricInfo.metricType,
                    reviewable = if (metricInfo.reviewable) true else null,
                    frequencies =
                        assetsPerFrequency.map { (frequency, assets) ->
                            CatalogAssetMetricFrequency(
                                frequency = frequency,
                                assets = assets.toList(),
                            )
                        },
                    displayName = metricInfo.displayName,
                    experimental = metricInfo.experimental,
                )
            }.map { CatalogAssetMetricsResponse(it) }

    suspend fun findSupportedCatalogV1Metrics(
        apiKey: String?,
        metrics: List<String>?,
        reviewable: Boolean?,
    ): FunctionResult<ApiError, CatalogMetricsResponse> =
        findCatalogV1Metrics(apiKey, metrics?.toSet(), reviewable)
            .toFullMetricsInfo { metricName, metricInfo, assetsPerFrequency ->
                CatalogMetricInfo(
                    metric = metricName,
                    fullName = metricInfo.name,
                    description = metricInfo.description,
                    category = metricInfo.category,
                    subcategory = metricInfo.subcategory,
                    unit = metricInfo.unit,
                    dataType = metricInfo.dataType.lowercase(),
                    type = metricInfo.metricType,
                    reviewable = if (metricInfo.reviewable) true else null,
                    frequencies =
                        assetsPerFrequency.map { (frequency, assets) ->
                            CatalogAssetMetricFrequency(
                                frequency = frequency,
                                assets = assets.toList(),
                            )
                        },
                    displayName = metricInfo.displayName,
                    experimental = metricInfo.experimental,
                )
            }.map { CatalogMetricsResponse(it) }

    suspend fun findSupportedCatalogV1AssetMetrics(
        apiKey: String?,
        assets: Set<String>,
    ): List<Deferred<Pair<String, FunctionResult<Pair<ApiError, DiscoveryResult.Failed>, Sequence<Array<String>>>>>> =
        coroutineScope {
            if (assets.isEmpty()) {
                listOf(async { "" to discoverAssetMetrics(apiKey) })
            } else {
                assets.map { asset -> async { asset to discoverAssetMetrics(apiKey, asset) } }
            }
        }

    override suspend fun findCatalogV1Metrics(
        apiKey: String?,
        metrics: Set<String>?,
        reviewable: Boolean?,
    ): FunctionResult<ApiError, Map<String, Map<String, Set<String>>>> {
        val metricAssetsDiscoveryResult =
            coroutineScope {
                metrics
                    ?.map { metric ->
                        async { discoverAssetMetrics(apiKey, asset = null, metric, frequency = null) }
                    }?.awaitAll() ?: listOf(discoverAssetMetrics(apiKey, asset = null, metric = null, frequency = null))
            }

        val supportedMetrics = TreeMap<String, TreeMap<String, LinkedHashSet<String>>>()
        metricAssetsDiscoveryResult
            .map {
                when (it) {
                    is FunctionResult.Success -> it.value
                    is FunctionResult.Failure -> return FunctionResult.Failure(it.value.first)
                }
            }.asSequence()
            .flatten()
            .let {
                if (reviewable == null) {
                    it
                } else {
                    it.filter { (_, metric) ->
                        val reviewableMetric = Resources.isMetricReviewable(metric)
                        reviewableMetric == reviewable
                    }
                }
            }.forEach { (asset, metric, frequency) ->
                if (getMetricAvailability(asset, metric, frequency) == MetricAvailability.Supported) {
                    val frequencies = supportedMetrics.computeIfAbsent(metric) { TreeMap(FrequencyComparator) }
                    val assets = frequencies.computeIfAbsent(frequency) { LinkedHashSet() }
                    assets.add(asset)
                }
            }
        return FunctionResult.Success(supportedMetrics)
    }

    suspend fun findSupportedCatalogV2AssetMetrics(
        apiKey: String?,
        assets: List<String>?,
        metrics: List<String>?,
        reviewable: Boolean?,
        format: String,
        httpRequest: HttpRequest,
        pageRequest: StringPageRequest,
    ): FunctionResult<ApiError, Pair<ChunkedResponseFormat, Flow<Any>>> {
        val responseFormat =
            ChunkedResponseFormat
                .fromValueOrError(
                    format,
                    setOf(ChunkedResponseFormat.Json(), ChunkedResponseFormat.JsonStream),
                ).getOrElse {
                    return it.toFailure()
                }

        val assetsSet = assets?.toSet() ?: emptySet()
        val metricsSet = metrics?.toSet() ?: emptySet()

        var isAssetApplicable = pageRequest.entity == null
        val isAssetApplicableFunction: (String, String) -> Boolean =
            if (pageRequest.pagingFrom == PagingFrom.START) {
                { asset, paginationAsset ->
                    asset >= paginationAsset
                }
            } else {
                { asset, paginationAsset ->
                    asset <= paginationAsset
                }
            }

        // pair each asset with each metric
        val assetMetricPairs = pairAssetsAndMetrics(assetsSet, metricsSet)
        val assetMetricsDiscoveryResults =
            coroutineScope {
                if (assetMetricPairs.isEmpty()) {
                    listOf(async { discoverAssetMetrics(apiKey) })
                } else {
                    assetMetricPairs.map { (asset, metric) ->
                        async { discoverAssetMetrics(apiKey, asset, metric) }
                    }
                }
            }.mapNotNull {
                it.awaitAndValidate()?.getOrElse { apiError -> return FunctionResult.Failure(apiError) }
            }

        var emittedAssetsCounter = 0
        val isJsonStreamFormat = responseFormat is ChunkedResponseFormat.JsonStream
        val pageSize = if (!isJsonStreamFormat) pageRequest.pageSize else null
        val flow =
            flow {
                val itemSequence =
                    assetMetricsDiscoveryResults
                        .asSequence()
                        .flatten()
                        .groupBy({ (asset) -> asset }, { (_, metric, frequency) -> metric to frequency })
                        .entries
                        .let { entries ->
                            if (isJsonStreamFormat || pageRequest.pagingFrom == PagingFrom.START) {
                                entries.sortedBy { it.key }
                            } else {
                                entries.sortedByDescending { it.key }
                            }
                        }.let {
                            if (pageRequest.entity == null) {
                                it
                            } else {
                                it.filter { (entity) ->
                                    if (!isAssetApplicable) {
                                        isAssetApplicable = isAssetApplicableFunction(entity, pageRequest.entity)
                                        isAssetApplicable
                                    } else {
                                        true
                                    }
                                }
                            }
                        }.mapNotNull { (asset, metricFrequencyPairs) ->
                            metricFrequencyPairs
                                .groupByTo(TreeMap(), { (metric) -> metric }, { (_, frequency) -> frequency })
                                .let {
                                    if (reviewable == null) {
                                        it
                                    } else {
                                        it.filter { (metric) ->
                                            val reviewableMetric = Resources.isMetricReviewable(metric)
                                            reviewableMetric == reviewable
                                        }
                                    }
                                }.mapNotNull { (metric, frequencies) ->
                                    val frequenciesSet = frequencies.toSortedSet(FrequencyComparator)
                                    createAssetMetricInfo(apiKey, asset, metric, frequenciesSet)
                                }.takeIf { it.isNotEmpty() }
                                ?.let { asset to it }
                        }.map { (asset, metricInfo) -> CatalogV2AssetMetricsInfo(asset, metricInfo) }

                if (isJsonStreamFormat || pageRequest.pagingFrom == PagingFrom.START) {
                    itemSequence.forEach { assetMetricInfo ->
                        if (emittedAssetsCounter == pageSize) {
                            // Emit nextPageInfo only if 'assetMetricInfo' is not null
                            // If we don't make sure it is not null, we may have a case where next page returns empty response
                            val nextPageToken = PagingUtils.createPageToken(assetMetricInfo.asset)
                            val nextPageUrl = PagingUtils.createNextPageUrl(httpRequest, nextPageToken)
                            val nextPageInfo = NextPageInfo(nextPageUrl, nextPageToken)
                            emit(nextPageInfo)
                            return@flow
                        } else {
                            emit(assetMetricInfo)
                            emittedAssetsCounter++
                        }
                    }
                } else {
                    // buffering is required
                    val buffer = ArrayList<Any>(pageSize ?: 100)
                    itemSequence.forEach { assetMetricInfo ->
                        if (emittedAssetsCounter == pageSize) {
                            // Emit nextPageInfo only if 'assetMetricInfo' is not null
                            // If we don't make sure it is not null, we may have a case where next page returns empty response
                            val nextPageToken = PagingUtils.createPageToken(assetMetricInfo.asset)
                            val nextPageUrl = PagingUtils.createNextPageUrl(httpRequest, nextPageToken)
                            val nextPageInfo = NextPageInfo(nextPageUrl, nextPageToken)
                            buffer.asReversed().forEach { emit(it) }
                            emit(nextPageInfo)
                            return@flow
                        } else {
                            buffer.add(assetMetricInfo)
                            emittedAssetsCounter++
                        }
                    }
                    buffer.asReversed().forEach { emit(it) }
                }
            }
        return (responseFormat to flow).toSuccess()
    }

    override suspend fun findTimeseriesMetrics(
        apiKey: String,
        frequency: String,
        entities: Set<String>,
        metrics: Set<String>,
        ignoreForbiddenAndUnsupportedErrors: Boolean,
    ): FunctionResult<ApiError, Map<String, EntityMetricsAvailability>> {
        val (normalizedFrequency, statisticsFrequency) = normalizeRequestFrequency(frequency)
        return if ("*" in entities) {
            val supportedAssetMetrics =
                coroutineScope {
                    metrics.map { metric -> async { discoverAssetMetrics(apiKey, asset = null, metric, normalizedFrequency) } }
                }.mapNotNull {
                    it.awaitAndValidate()?.getOrElse { apiError ->
                        if (!ignoreForbiddenAndUnsupportedErrors) return FunctionResult.Failure(apiError) else emptySequence()
                    }
                }.asSequence()
                    .flatten()

            val metricsAvailabilityPerAssetMetric =
                supportedAssetMetrics.associate { (asset, metric) ->
                    (asset to metric) to
                        MetricAvailability.Supported
                }
            val distinctAssets = supportedAssetMetrics.map { (asset) -> asset }.distinct()

            // associate each asset with metric-availability pairs
            distinctAssets
                .mapNotNull { asset ->
                    metrics
                        .asSequence()
                        .map { metric ->
                            val metricAvailability =
                                metricsAvailabilityPerAssetMetric[asset to metric]
                                    // if ReferenceRate* metric is supported by AMS we double-check it in statistics
                                    ?.let { getMetricAvailability(asset, metric, statisticsFrequency) }
                                    ?: MetricAvailability.Unsupported
                            metric to metricAvailability
                            // pick only the assets which have at least one supported metric
                        }.takeIf { it.any { (_, availability) -> availability == MetricAvailability.Supported } }
                        ?.toList()
                        ?.let { asset to EntityMetricsAvailability(it, timeRestriction = TimeRestriction.unbounded) }
                }.associate { it }
        } else {
            coroutineScope {
                val availableAssets = HashSet<String>()
                val result =
                    entities
                        .map { asset ->
                            asset to
                                metrics.map { metric ->
                                    metric to
                                        async {
                                            checkAssetMetric(
                                                apiKey,
                                                asset,
                                                statisticsFrequency,
                                                normalizedFrequency,
                                                metric,
                                                availableAssets,
                                            )
                                        }
                                }
                        }.map { (asset, metricAndAvailabilityDeferredList) ->
                            asset to
                                EntityMetricsAvailability(
                                    metricAndAvailabilityDeferredList.map { (metric, availabilityDeferred) ->
                                        metric to availabilityDeferred.await()
                                    },
                                    timeRestriction = TimeRestriction.unbounded,
                                )
                        }.associate { it }
                checkAssetsInResources(availableAssets)
                result
            }
        }.let { FunctionResult.Success(it) }
    }

    suspend fun findMetricsReferenceData(
        metrics: List<String>?,
        reviewable: Boolean?,
        format: String,
        httpRequest: HttpRequest,
        pageRequest: StringPageRequest,
    ): FunctionResult<ApiError, Pair<ChunkedResponseFormat, Flow<Any>>> {
        val origin = httpRequest.origin
        val apiKey = httpRequest.queryParameters["api_key"]
        return getMetricsReferenceData(metrics, reviewable, format, httpRequest, pageRequest) { metric ->
            assetMetricConstituentProviders[metric]?.let {
                val constituentSnapshotsUrl = urlTemplate(origin, "snapshots", metric, apiKey)
                val constituentTimeframesUrl = urlTemplate(origin, "timeframes", metric, apiKey)
                constituentSnapshotsUrl to constituentTimeframesUrl
            }
        }
    }

    private suspend fun Deferred<
        FunctionResult<
            Pair<ApiError, DiscoveryResult.Failed>,
            Sequence<Array<String>>,
        >,
    >.awaitAndValidate(): FunctionResult<ApiError, Sequence<Array<String>>>? {
        return when (val result = this.await()) {
            is FunctionResult.Success -> result.value.toSuccess()
            is FunctionResult.Failure -> {
                val (apiError, discoveryError) = result.value
                if (discoveryError is DiscoveryResult.BadParameter) {
                    // Bad parameter can be returned when metric is invalid/unknown
                    return FunctionResult.Failure(apiError)
                } else {
                    null
                }
            }
        }
    }

    /**
     * This is necessary for monitoring purposes to stay informed about the need to update resources in the API.
     */
    private fun checkAssetsInResources(availableAssets: Set<String>) {
        availableAssets.forEach { asset ->
            if (Resources.getCurrencyInfo(asset) == null) {
                monitoring.notFoundAssetsInResources.labelValues(asset).inc()
            }
        }
    }

    private fun getMetricAvailability(
        asset: String,
        metric: String,
        frequency: String,
    ): MetricAvailability =
        if (assetMetricStatistics.getAssetMetricStatistics(asset, metric, frequency) == null) {
            MetricAvailability.Unsupported
        } else {
            MetricAvailability.Supported
        }

    private suspend fun getCommunityAssetMetrics(communityApiKey: String): Set<Triple<String, String, String>> =
        when (val result = discoverAssetMetrics(communityApiKey)) {
            is FunctionResult.Success -> result.value
            is FunctionResult.Failure -> {
                val (_, discoveryResult) = result.value
                throw IllegalStateException("Can't get community metrics. Reason: ${discoveryResult.message}")
            }
        }.map { Triple(it[0], it[1], it[2]) }.toSet()

    fun createAssetMetricInfo(
        apiKey: String?,
        asset: String,
        metric: String,
        frequencies: Collection<String>?,
    ): AssetMetricInfo? {
        // don't show metric without data
        if (frequencies == null) return null
        val frequenciesArray = createAssetMetricFrequencyArray(apiKey, asset, metric, frequencies)
        if (frequenciesArray.isEmpty()) return null

        return AssetMetricInfo(metric = metric, frequencies = frequenciesArray)
    }

    private fun createAssetMetricFrequencyArray(
        apiKey: String?,
        asset: String,
        metric: String,
        frequencies: Collection<String>,
    ): List<AssetMetricFrequency> =
        frequencies.mapNotNull { frequency ->
            assetMetricStatistics
                .getAssetMetricStatistics(asset, metric, frequency)
                ?.let { statistics ->
                    val (minTime, maxTime) = getAdjustedMetricMinMaxTime(apiKey, metric, frequency, statistics)
                    if (minTime != null && maxTime != null) {
                        AssetMetricFrequency(
                            frequency = frequency,
                            minTime = minTime,
                            maxTime = maxTime,
                            minHeight = statistics.minHeight,
                            maxHeight = statistics.maxHeight,
                            minHash = statistics.minHash,
                            maxHash = statistics.maxHash,
                            community = isCommunityMetric(asset, metric, frequency),
                            experimental = isExperimental(asset, metric, frequency),
                        )
                    } else {
                        null
                    }
                }
        }

    private fun getAdjustedMetricMinMaxTime(
        apiKey: String?,
        metric: String,
        frequency: String,
        statistics: AssetMetricStatisticsModel.Statistics,
    ): Pair<String?, String?> =
        if (apiKey == communityApiKey && (metric.startsWith("ReferenceRate") || metric.startsWith("principal_market"))) {
            val referenceRatesFrequencyUnit =
                when (frequency) {
                    "1s" -> ChronoUnit.SECONDS
                    "1m" -> ChronoUnit.MINUTES
                    else -> ChronoUnit.HOURS
                }

            /**
             * MD-3542
             * Expand community permission to ReferenceRate* metrics served through /timeseries/asset-metrics, all frequencies, to have 7 days of history (currently it is 1 day).
             */
            val forcedMinTime =
                MetricDataUtils
                    .enforcedStartTime(
                        clock = clock,
                        apiKey = apiKey,
                        communityApiKey = communityApiKey,
                        delayDays = 7,
                    )?.truncatedTo(referenceRatesFrequencyUnit)
                    ?.plus(1, referenceRatesFrequencyUnit)
                    ?: throw IllegalStateException("Forced minimum time shouldn't be null")

            val newMinTime = maxOf(statistics.minTime, TimeUtils.dateTimeFormatter.format(forcedMinTime))

            if (newMinTime > statistics.maxTime) {
                null to null
            } else {
                newMinTime to statistics.maxTime
            }
        } else {
            statistics.minTime to statistics.maxTime
        }

    private fun isCommunityMetric(
        asset: String,
        metric: String,
        frequency: String,
    ): Boolean? = communityAssetMetrics.contains(Triple(asset, metric, frequency)).takeIf { it }

    private fun isExperimental(
        asset: String,
        metric: String,
        frequency: String,
    ): Boolean? = experimentalAssetMetrics.matches(asset, metric, frequency).takeIf { it }

    /**
     * Pairs each asset with each metric.
     * If only one of the sets is empty, its values are nulls, for instance:
     * - (asset1, null), (asset2, null);
     * - (null, metric1), (null, metric2).
     *
     * Returns empty list if both sets are empty.
     */
    private fun pairAssetsAndMetrics(
        assetsSet: Set<String>,
        metricsSet: Set<String>,
    ): List<Pair<String?, String?>> {
        return if (assetsSet.isEmpty() && metricsSet.isEmpty()) {
            return emptyList()
        } else if (assetsSet.isNotEmpty()) {
            if (metricsSet.isNotEmpty()) {
                assetsSet.flatMap { asset -> metricsSet.map { metric -> asset to metric } }
            } else {
                assetsSet.map { asset -> asset to null }
            }
        } else {
            metricsSet.map { metric -> null to metric }
        }
    }

    private suspend fun discoverAssetMetrics(
        apiKey: String?,
        asset: String? = null,
        metric: String? = null,
        frequency: String? = null,
    ): FunctionResult<Pair<ApiError, DiscoveryResult.Failed>, Sequence<Array<String>>> {
        val filters =
            hashMapOf<String, String>().also { map ->
                asset?.also { map["asset"] = it }
                metric?.also { map["metric"] = it }
                frequency?.also { map["frequency"] = it }
            }

        return amsService
            .discovery(
                resource = "asset_metrics",
                target = "asset,metric,frequency",
                apiKey = apiKey,
                filters = filters,
            ) { amsParamsToEndpointParams[it] }
            .map {
                it.values.asSequence().let { valuesSequence ->
                    if (frequency != null) {
                        valuesSequence
                    } else {
                        valuesSequence.flatMap { values ->
                            val (vAsset, vMetric, vFrequency) = values
                            if (vFrequency == "1d" && (vMetric.startsWith("ReferenceRate") || vMetric.startsWith("principal_market"))) {
                                listOf(values, arrayOf(vAsset, vMetric, "1d-ny-close"))
                            } else {
                                listOf(values)
                            }
                        }
                    }
                }
            }
    }

    private suspend fun checkAssetMetric(
        apiKey: String,
        asset: String,
        statisticsFrequency: String,
        normalizedFrequency: String,
        metric: String,
        availableAssets: HashSet<String>,
    ): MetricAvailability {
        val checkResult =
            amsService.check(
                apiKey = apiKey,
                resource = "asset_metrics",
                parameters =
                    hashMapOf(
                        "asset" to asset,
                        "metric" to metric,
                        "frequency" to normalizedFrequency,
                    ),
            ) { amsParamsToEndpointParams[it] }

        return when (checkResult) {
            // if metric is supported by AMS we double-check in statistics
            is FunctionResult.Success -> {
                availableAssets.add(asset)
                getMetricAvailability(asset, metric, statisticsFrequency)
            }

            is FunctionResult.Failure -> {
                checkResult.value.first.let { apiError ->
                    if (apiError is ApiError.BadParameters) {
                        MetricAvailability.Unsupported
                    } else if (apiError is ApiError.Forbidden) {
                        availableAssets.add(asset)
                        // it may be a false positive error due to inconsistencies in the legacy product.json
                        // see: https://coinmetrics.slack.com/archives/CSBMENHMY/p1596198954071700
                        // so we check for "support" on the API side
                        if (assetMetricStatistics.getAssetMetricStatistics(asset, metric, statisticsFrequency) != null) {
                            MetricAvailability.Error(
                                ApiError.ForbiddenWithMessage(
                                    "Requested metric '$metric' with frequency '$statisticsFrequency' " +
                                        "for asset '$asset' is not available with supplied credentials.",
                                ),
                            )
                        } else {
                            MetricAvailability.Unsupported
                        }
                    } else {
                        MetricAvailability.Error(apiError)
                    }
                }
            }
        }
    }

    private fun normalizeRequestFrequency(frequency: String): Pair<String, String> {
        val (normalizedFrequency, frequencyOffset) = TimeUtils.parseFrequency(frequency)
        val normalizedFrequencyOffset = TimeUtils.normalizeFrequencyOffset(frequencyOffset)
        val statisticsFrequency =
            when {
                // 1d-HH:MM frequencies
                isDailyWithCustomTimeOffset(normalizedFrequency, normalizedFrequencyOffset, frequencyOffset) -> "1h"

                // 1d-ny-close, 1d-ny-midday, 1d-sg-close frequencies
                normalizedFrequencyOffset?.forcedTimeZone != null -> frequency

                else -> normalizedFrequency
            }
        return normalizedFrequency to statisticsFrequency
    }

    private fun isDailyWithCustomTimeOffset(
        normalizedFrequency: String,
        normalizedFrequencyOffset: TimeUtils.NormalizedFrequencyOffset?,
        frequencyOffset: String?,
    ) = frequencyOffset != null &&
        normalizedFrequencyOffset != null &&
        normalizedFrequencyOffset.forcedTimeZone == null &&
        normalizedFrequency == "1d"
}
