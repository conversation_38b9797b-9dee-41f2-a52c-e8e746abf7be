package io.coinmetrics.api.service.catalog

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.ChunkedResponseFormat
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.ams.DiscoveryResult
import io.coinmetrics.api.model.CandleStatistics
import io.coinmetrics.api.model.page.StringPageRequest
import io.coinmetrics.api.models.CatalogCandleFrequency
import io.coinmetrics.api.models.CatalogPairCandlesInfo
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.statistics.pair.PairCandlesStatistics
import io.coinmetrics.api.toFailure
import io.coinmetrics.api.utils.DataUtils
import io.coinmetrics.api.utils.toEntitiesFlow
import io.coinmetrics.httpserver.HttpRequest
import kotlinx.coroutines.flow.Flow
import java.time.Clock

class CatalogPairService(
    private val amsService: AmsService,
    private val pairCandlesStatistics: PairCandlesStatistics,
    private val communityApiKey: String,
    private val clock: Clock,
) {
    suspend fun getPairCandles(
        apiKey: String?,
        pairs: List<String>?,
        format: String,
        httpRequest: HttpRequest,
        pageRequest: StringPageRequest? = null,
    ): FunctionResult<ApiError, Pair<ChunkedResponseFormat, Flow<Any>>> {
        val statisticsPerPair = pairCandlesStatistics.getPairCandlesStatistic()
        val pairIds = pairs?.sorted()?.map { it.lowercase() } ?: statisticsPerPair.keys.sorted()
        val allowedFrequencies =
            amsService
                .discovery(apiKey = apiKey, resource = "pair_candles", target = "frequency") { it }
                .getOrElse { (apiError, _) ->
                    if (apiError is ApiError.Forbidden) {
                        return ApiError
                            .ForbiddenWithMessage("Requested resource is not available with supplied credentials.")
                            .toFailure()
                    } else {
                        DiscoveryResult.Success(emptyList())
                    }
                }.values
                .map { it[0] }
                .toSet()

        return pairIds
            .map { pair ->
                // statistics can be null only when user-specified pair is unsupported
                val statistics =
                    statisticsPerPair[pair] ?: return ApiError.UnsupportedParameterValue("pairs", pair).toFailure()
                pair to statistics
            }.asSequence()
            .toEntitiesFlow(format, httpRequest, pageRequest) { pair, statistics ->
                val frequencies =
                    statistics
                        .filter { it.frequency in allowedFrequencies }
                        .mapNotNull {
                            toAllowedTimeRange(apiKey, it)?.let { (minTime, maxTime) ->
                                CatalogCandleFrequency(it.frequency, minTime, maxTime)
                            }
                        }
                if (frequencies.isNotEmpty()) {
                    CatalogPairCandlesInfo(pair, frequencies)
                } else {
                    null
                }
            }
    }

    private fun toAllowedTimeRange(
        apiKey: String?,
        stats: CandleStatistics,
    ): Pair<String, String>? {
        val (minTime, maxTime) =
            DataUtils.getMinMaxTimeBasedOnApiKey(
                apiKey = apiKey,
                communityApiKey = communityApiKey,
                statisticsMinTime = stats.minTime,
                statisticsMaxTime = stats.maxTime,
                /**
                 * MD-3542
                 * Expand community permission to timeseries/pair-candles, all assets, all frequencies to have 7 days of history (currently it is 1 day).
                 */
                delayDays = 7,
                clock = clock,
            )
        if (minTime == null || maxTime == null) return null
        return minTime to maxTime
    }
}
