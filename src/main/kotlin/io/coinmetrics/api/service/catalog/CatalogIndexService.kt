package io.coinmetrics.api.service.catalog

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.ChunkedResponseFormat
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.endpoints.timeseries.index.IndexEndpointUtils
import io.coinmetrics.api.model.CandleStatistics
import io.coinmetrics.api.model.page.StringPageRequest
import io.coinmetrics.api.models.CatalogCandleFrequency
import io.coinmetrics.api.models.CatalogIndexCandlesInfo
import io.coinmetrics.api.models.CatalogIndexFrequency
import io.coinmetrics.api.models.CatalogV2IndexInfo
import io.coinmetrics.api.resources.IndexInfo
import io.coinmetrics.api.resources.Resources
import io.coinmetrics.api.service.IndexDiscoveryService
import io.coinmetrics.api.statistics.index.IndexCandlesStatistics
import io.coinmetrics.api.statistics.index.IndexStatistics
import io.coinmetrics.api.utils.DataUtils
import io.coinmetrics.api.utils.FrequencyComparator
import io.coinmetrics.api.utils.TimeUtils
import io.coinmetrics.api.utils.toEntitiesFlow
import io.coinmetrics.httpserver.HttpRequest
import kotlinx.coroutines.flow.Flow
import java.time.Instant
import java.time.ZoneOffset
import java.util.TreeMap

class CatalogIndexService(
    private val communityKey: String,
    private val indexDiscoveryService: IndexDiscoveryService,
    private val indexStatistics: IndexStatistics,
    private val indexCandlesStatistics: IndexCandlesStatistics,
) {
    suspend fun getIndexes(
        apiKey: String?,
        indexes: List<String>?,
        format: String,
        httpRequest: HttpRequest,
        pageRequest: StringPageRequest? = null,
    ): FunctionResult<ApiError, Pair<ChunkedResponseFormat, Flow<Any>>> =
        indexDiscoveryService.discoverIndexes(apiKey, requestedIndexes = indexes).flatMap { allowedIndexes ->
            val timeRestrictionsPerFrequencyPerIndex = allowedIndexes.toTimeRestrictionsPerFrequencyPerIndex()
            if (indexes != null && timeRestrictionsPerFrequencyPerIndex.size < indexes.size) {
                val forbiddenIndexes = indexes.minus(timeRestrictionsPerFrequencyPerIndex.keys)
                val indexesStr = forbiddenIndexes.take(3).joinToString(", ")
                return FunctionResult.Failure(
                    ApiError.ForbiddenWithMessage(
                        "Requested indexes are not available with supplied credentials: $indexesStr.",
                    ),
                )
            }
            timeRestrictionsPerFrequencyPerIndex
                .asSequence()
                .map { it.key to it.value }
                .toEntitiesFlow(format, httpRequest, pageRequest) { index, timeRestrictionPerFrequency ->
                    toCatalogIndexFrequencies(
                        apiKey,
                        index,
                        timeRestrictionPerFrequency,
                        httpRequest.receivedTime,
                    )?.let { (indexInfo, frequencies) ->
                        if (pageRequest == null) {
                            toCatalogV1IndexInfo(indexInfo, frequencies)
                        } else {
                            toCatalogV2IndexInfo(indexInfo, frequencies)
                        }
                    }
                }
        }

    @Suppress("ktlint:standard:max-line-length")
    private fun List<Triple<String, String, List<String>?>>.toTimeRestrictionsPerFrequencyPerIndex(): Map<String, Map<String, List<String>?>> =
        HashMap<String, HashMap<String, List<String>?>>().also {
            this.forEach { (index, frequency, timeRestrictions) ->
                it.computeIfAbsent(index) { HashMap() }[frequency] = timeRestrictions
            }
        }

    private fun toCatalogV1IndexInfo(
        indexInfo: IndexInfo,
        frequencies: Collection<CatalogIndexFrequency>,
    ) = io.coinmetrics.api.models.IndexInfo(
        index = indexInfo.name,
        fullName = indexInfo.fullName,
        description = indexInfo.description,
        frequencies = frequencies.toList(),
        type = indexInfo.type,
    )

    private fun toCatalogV2IndexInfo(
        indexInfo: IndexInfo,
        frequencies: Collection<CatalogIndexFrequency>,
    ) = CatalogV2IndexInfo(
        index = indexInfo.name,
        frequencies = frequencies.toList(),
    )

    private fun toCatalogIndexFrequencies(
        apiKey: String?,
        index: String,
        timeRestrictionPerFrequency: Map<String, List<String>?>,
        requestReceivedAt: Instant,
    ): Pair<IndexInfo, Collection<CatalogIndexFrequency>>? {
        val indexInfo = getApplicableIndexInfo(apiKey, index) ?: return null

        val frequencies =
            timeRestrictionPerFrequency
                .flatMap { (frequency, timeRestriction) ->
                    if (frequency == "1d") {
                        (indexInfo.extraDailyFrequencies + frequency).map { it to timeRestriction }
                    } else {
                        listOf(frequency to timeRestriction)
                    }
                }.mapNotNull { (frequency, timeRestrictions) ->
                    val statistics = indexStatistics.getIndexFrequencyStatistics(index, frequency) ?: return@mapNotNull null
                    val (minTime, maxTime) =
                        if (timeRestrictions != null) {
                            val restrictedMinTime =
                                TimeUtils.getEnforcedStartTime(
                                    timeRestrictions,
                                    ZoneOffset.UTC,
                                    statistics.maxTime.toString(),
                                    requestReceivedAt,
                                )
                            val formattedMinTime = restrictedMinTime?.let { TimeUtils.dateTimeFormatter.format(it) } ?: statistics.minTime
                            formattedMinTime to statistics.maxTime
                        } else {
                            statistics.minTime to statistics.maxTime
                        }
                    frequency to
                        CatalogIndexFrequency(
                            frequency = frequency,
                            minTime = minTime.toString(),
                            maxTime = maxTime.toString(),
                        )
                }.takeIf { it.isNotEmpty() }
                ?.toMap(TreeMap(FrequencyComparator))
                ?.values

        return frequencies?.let { indexInfo to frequencies }
    }

    suspend fun getIndexCandles(
        apiKey: String?,
        indexes: List<String>?,
        format: String,
        httpRequest: HttpRequest,
        pageRequest: StringPageRequest? = null,
    ): FunctionResult<ApiError, Pair<ChunkedResponseFormat, Flow<Any>>> =
        indexDiscoveryService.discoverIndexes(apiKey, requestedIndexes = indexes).flatMap { allowedIndexes ->
            val timeRestrictionsPerFrequencyPerIndex = allowedIndexes.toTimeRestrictionsPerFrequencyPerIndex()
            if (indexes != null && timeRestrictionsPerFrequencyPerIndex.size < indexes.size) {
                val forbiddenIndexes = indexes.minus(timeRestrictionsPerFrequencyPerIndex.keys)
                val indexesStr = forbiddenIndexes.take(3).joinToString(", ")
                return FunctionResult.Failure(
                    ApiError.ForbiddenWithMessage(
                        "Requested indexes are not available with supplied credentials: $indexesStr.",
                    ),
                )
            }
            val statisticsPerIndex = indexCandlesStatistics.getIndexCandlesStatistics()
            timeRestrictionsPerFrequencyPerIndex
                .asSequence()
                .map { (index) -> index }
                .distinct()
                .mapNotNull { index -> statisticsPerIndex[index]?.let { index to it } }
                .toEntitiesFlow(format, httpRequest, pageRequest) { index, statistics ->
                    if (getApplicableIndexInfo(apiKey, index) != null) {
                        val frequencies = statistics.mapNotNull { toCatalogCandleFrequency(apiKey, it) }
                        if (frequencies.isNotEmpty()) {
                            CatalogIndexCandlesInfo(index, frequencies)
                        } else {
                            null
                        }
                    } else {
                        null
                    }
                }
        }

    private fun toCatalogCandleFrequency(
        apiKey: String?,
        stat: CandleStatistics,
    ): CatalogCandleFrequency? {
        if (IndexEndpointUtils.unrestrictedCandleFrequencies.contains(stat.frequency)) {
            return CatalogCandleFrequency(stat.frequency, stat.minTime, stat.maxTime)
        }
        val (minTime, maxTime) = DataUtils.getMinMaxTimeBasedOnApiKey(apiKey, communityKey, stat.minTime, stat.maxTime)
        if (minTime == null || maxTime == null) {
            return null
        }
        return CatalogCandleFrequency(stat.frequency, minTime, maxTime)
    }

    /**
     * We should return only CMBI indexes in /catalog-all and /catalog-all-v2.
     */
    private fun getApplicableIndexInfo(
        apiKey: String?,
        index: String,
    ): IndexInfo? =
        if (apiKey == null) {
            Resources.cmbiIndexesMap[index]
        } else {
            Resources.indexNameToInfoMap[index]
        }
}
