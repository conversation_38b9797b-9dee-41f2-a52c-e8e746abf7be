package io.coinmetrics.api.service.catalog.metric.impl

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.ChunkedResponseFormat
import io.coinmetrics.api.CommonConstants
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.model.DataAvailabilityTimeRange
import io.coinmetrics.api.model.page.StringPageRequest
import io.coinmetrics.api.models.CatalogInstitutionMetricFrequency
import io.coinmetrics.api.models.CatalogInstitutionMetricInfo
import io.coinmetrics.api.models.CatalogInstitutionMetricsResponse
import io.coinmetrics.api.models.CatalogV2InstitutionMetricsInfo
import io.coinmetrics.api.models.InstitutionInfo
import io.coinmetrics.api.models.InstitutionMetricFrequency
import io.coinmetrics.api.models.InstitutionMetricInfo
import io.coinmetrics.api.models.InstitutionsResponse
import io.coinmetrics.api.resources.Resources
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.catalog.metric.BaseMetricsService
import io.coinmetrics.api.service.catalog.metric.discovery.SimpleTimeRestrictionDiscoverer
import io.coinmetrics.api.service.catalog.metric.toFullMetricsInfo
import io.coinmetrics.api.statistics.metrics.InstitutionMetricStatistics
import io.coinmetrics.api.utils.toMetricInfoFlow
import io.coinmetrics.httpserver.HttpRequest
import kotlinx.coroutines.flow.Flow
import org.slf4j.LoggerFactory
import java.time.Instant

class InstitutionMetricsService(
    docsBaseUrl: String,
    amsService: AmsService,
    private val metricStatistics: InstitutionMetricStatistics,
) : BaseMetricsService(
        entityParamName = "institutions",
        metricStatistics = metricStatistics,
        timeRestrictionDiscoverer =
            SimpleTimeRestrictionDiscoverer(
                amsService,
                resource = "institution_metrics",
                entityDisplayName = "institution",
            ),
        docsBaseUrl = docsBaseUrl,
    ) {
    companion object {
        private val log = LoggerFactory.getLogger(InstitutionMetricsService::class.java)
    }

    override val supportedFrequencies = CommonConstants.institutionMetricsSupportedFrequencies.keys

    fun supportedInstitutions() = metricStatistics.groupedByEntity().keys

    suspend fun findSupportedCatalogV1InstitutionMetrics(
        apiKey: String?,
        metrics: List<String>?,
        reviewable: Boolean?,
    ): FunctionResult<ApiError, CatalogInstitutionMetricsResponse> =
        findCatalogV1Metrics(apiKey, metrics?.toSet(), reviewable)
            .toFullMetricsInfo { metricName, metricInfo, institutionsPerFrequency ->
                CatalogInstitutionMetricInfo(
                    metric = metricName,
                    fullName = metricInfo.name,
                    description = metricInfo.description,
                    product = metricInfo.product,
                    category = metricInfo.category,
                    subcategory = metricInfo.subcategory,
                    unit = metricInfo.unit,
                    dataType = metricInfo.dataType.lowercase(),
                    type = metricInfo.metricType,
                    reviewable = if (metricInfo.reviewable) true else null,
                    frequencies =
                        institutionsPerFrequency.map { (frequency, institutions) ->
                            CatalogInstitutionMetricFrequency(
                                frequency = frequency,
                                institutions = institutions.toList(),
                            )
                        },
                    displayName = metricInfo.displayName,
                    experimental = metricInfo.experimental,
                )
            }.map { CatalogInstitutionMetricsResponse(it) }

    suspend fun findSupportedCatalogV1InstitutionMetrics(
        apiKey: String?,
        entities: Set<String>?,
        requestReceivedTime: Instant,
    ): FunctionResult<ApiError, InstitutionsResponse> =
        FindCatalogV1MetricsForEntities(apiKey, entities, requestReceivedTime)
            .execute()
            .toMetricInfoArray { institution, metricsMap ->
                val metrics = toInstitutionMetricInfoArray(metricsMap)
                InstitutionInfo(institution = institution, metrics = metrics)
            }.map { InstitutionsResponse(it) }

    private fun toInstitutionMetricInfoArray(
        metricsMap: Map<String, Map<String, DataAvailabilityTimeRange>>,
    ): List<InstitutionMetricInfo>? =
        metricsMap
            .mapNotNull { (metric, frequencies) -> toInstitutionMetricInfo(metric, frequencies) }
            .takeIf { it.isNotEmpty() }

    private fun toInstitutionMetricInfo(
        metric: String,
        frequencies: Map<String, DataAvailabilityTimeRange>,
    ): InstitutionMetricInfo? {
        if (frequencies.isEmpty()) return null
        val frequenciesArray =
            frequencies.mapNotNull { (frequency, timeRange) ->
                InstitutionMetricFrequency(
                    frequency = frequency,
                    minTime = timeRange.minTime.toString(),
                    maxTime = timeRange.maxTime.toString(),
                    // community = isCommunityMetric(institution, metric, frequency).let { if (it) true else null }
                )
            }
        return InstitutionMetricInfo(metric = metric, frequencies = frequenciesArray)
    }

    override fun validateEntities(entities: Set<String>): ApiError? =
        entities.firstNotNullOfOrNull { institution ->
            when (val result = Resources.getInstitutionIdByName(institution)) {
                is FunctionResult.Success -> null
                is FunctionResult.Failure -> {
                    log.warn(result.value)
                    ApiError.BadParameter("institutions", "Value '$institution' is not supported.")
                }
            }
        }

    suspend fun findSupportedCatalogV2InstitutionMetrics(
        apiKey: String?,
        institutions: List<String>?,
        metrics: List<String>?,
        format: String,
        httpRequest: HttpRequest,
        pageRequest: StringPageRequest,
    ): FunctionResult<ApiError, Pair<ChunkedResponseFormat, Flow<Any>>> =
        findCatalogV2Metrics(apiKey, institutions, metrics, reviewable = null, requestReceivedTime = httpRequest.receivedTime)
            .toMetricInfoFlow(format, httpRequest, pageRequest) { institution, metricsMap ->
                toInstitutionMetricInfoArray(metricsMap)?.let { metricsArray ->
                    CatalogV2InstitutionMetricsInfo(institution, metricsArray)
                }
            }
}
