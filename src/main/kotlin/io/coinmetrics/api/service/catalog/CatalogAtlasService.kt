package io.coinmetrics.api.service.catalog

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.ChunkedResponseFormat
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.model.page.StringPageRequest
import io.coinmetrics.api.models.CatalogV2BlockchainAssetsInfo
import io.coinmetrics.api.service.BlockchainEndpointService
import io.coinmetrics.api.utils.ExperimentalAtlas
import io.coinmetrics.api.utils.toEntitiesFlow
import io.coinmetrics.httpserver.HttpRequest
import kotlinx.coroutines.flow.Flow

class CatalogAtlasService(
    private val blockchainEndpointService: BlockchainEndpointService,
    private val experimentalAtlas: ExperimentalAtlas,
) {
    suspend fun getAtlasAssets(
        endpoint: String,
        apiKey: String?,
        requestedAssets: List<String>?,
        format: String,
        httpRequest: HttpRequest,
        pageRequest: StringPageRequest? = null,
    ): FunctionResult<ApiError, Pair<ChunkedResponseFormat, Flow<Any>>> {
        val atlasAssets = blockchainEndpointService.findSupportedAssets(apiKey).filterValues { it }.keys
        val assets = atlasAssets.filter { requestedAssets == null || requestedAssets.contains(it) }

        return assets
            .sorted()
            .asSequence()
            .mapNotNull { asset ->
                blockchainEndpointService
                    .getStatistics(asset, endpoint)
                    ?.takeIf {
                        it.minTime != null && it.maxTime != null
                    }?.let { stats ->
                        asset to stats
                    }
            }.toEntitiesFlow(format, httpRequest, pageRequest) { asset, stats ->
                CatalogV2BlockchainAssetsInfo(
                    asset = asset,
                    minTime = stats.minTime.toString(),
                    maxTime = stats.maxTime.toString(),
                    experimental = experimentalAtlas.matches(endpoint, asset).takeIf { it },
                )
            }
    }
}
