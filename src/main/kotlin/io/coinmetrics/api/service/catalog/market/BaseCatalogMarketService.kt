package io.coinmetrics.api.service.catalog.market

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.ChunkedResponseFormat
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.model.DerivativesMarketType
import io.coinmetrics.api.model.ParsedMarket
import io.coinmetrics.api.model.TimeRestriction
import io.coinmetrics.api.model.page.StringPageRequest
import io.coinmetrics.api.models.CatalogCandleFrequency
import io.coinmetrics.api.models.CatalogMarketCandlesInfo
import io.coinmetrics.api.models.CatalogMarketContractPricesInfo
import io.coinmetrics.api.models.CatalogMarketFundingRatesInfo
import io.coinmetrics.api.models.CatalogMarketFundingRatesPredictedInfo
import io.coinmetrics.api.models.CatalogMarketGreeksInfo
import io.coinmetrics.api.models.CatalogMarketImpliedVolatilityInfo
import io.coinmetrics.api.models.CatalogMarketLiquidationsInfo
import io.coinmetrics.api.models.CatalogMarketMetricsInfo
import io.coinmetrics.api.models.CatalogMarketOpeninterestInfo
import io.coinmetrics.api.models.CatalogMarketOrderbookDepth
import io.coinmetrics.api.models.CatalogMarketQuotesInfo
import io.coinmetrics.api.models.CatalogMarketTradesInfo
import io.coinmetrics.api.models.MarketMetricFrequency
import io.coinmetrics.api.models.MarketMetricInfo
import io.coinmetrics.api.models.MarketTimeRange
import io.coinmetrics.api.models.ReferenceDataMarketInfo
import io.coinmetrics.api.service.MarketDiscoveryContextFactory
import io.coinmetrics.api.service.MarketStatisticsService
import io.coinmetrics.api.statistics.market.FutureMetadata
import io.coinmetrics.api.statistics.market.MarketStatistics
import io.coinmetrics.api.statistics.market.OptionMetadata
import io.coinmetrics.api.toFailure
import io.coinmetrics.api.utils.CommonUtils
import io.coinmetrics.api.utils.DataUtils
import io.coinmetrics.api.utils.TimeUtils
import io.coinmetrics.httpserver.HttpRequest
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlin.system.measureTimeMillis

abstract class BaseCatalogMarketService(
    private val catalogVersion: Int,
    private val communityApiKey: String,
    private val marketDiscoveryContextFactory: MarketDiscoveryContextFactory,
    protected val marketStatisticsService: MarketStatisticsService,
    private val tenPercentMidPriceBookStartTime: String,
) {
    companion object {
        private val amsParamsToEndpointParams =
            hashMapOf(
                "type" to "type",
                "exchange" to "exchange",
                "base" to "base",
                "quote" to "quote",
                "symbol" to "symbol",
                "asset" to "asset",
            )
        private val amsParamsToEndpointParamsForMarkets =
            hashMapOf(
                "type" to "markets",
                "exchange" to "markets",
                "base" to "markets",
                "quote" to "markets",
                "symbol" to "markets",
            )
    }

    suspend fun getTrades(request: CatalogMarketRequest): FunctionResult<ApiError, Pair<ChunkedResponseFormat, Flow<Any>>> =
        marketCatalogFlow(request) { marketInfo, timeRestriction ->
            val tradesStatistics = marketStatisticsService.getTradesStatistics(marketInfo)
            toMarketTimeRange(request.apiKey, tradesStatistics)
                ?.let { (minTime, maxTime) ->
                    CatalogMarketTradesInfo(
                        market = marketInfo.toString(),
                        minTime = minTime,
                        maxTime = maxTime,
                    )
                }
        }

    suspend fun getCandles(request: CatalogMarketRequest): FunctionResult<ApiError, Pair<ChunkedResponseFormat, Flow<Any>>> =
        marketCatalogFlow(request) { marketInfo, timeRestriction ->
            val candlesStatistics = marketStatisticsService.getCandlesStatistics(marketInfo)
            val frequencies =
                candlesStatistics
                    .asSequence()
                    .mapNotNull { (duration, stats) ->
                        if (duration == "1d" && request.apiKey == communityApiKey) {
                            CatalogCandleFrequency(duration, stats.minTime, stats.maxTime)
                        } else {
                            toMarketTimeRange(request.apiKey, stats)?.let { timeRange ->
                                CatalogCandleFrequency(duration, timeRange.minTime, timeRange.maxTime)
                            }
                        }
                    }.toList()
            frequencies.takeIf { it.isNotEmpty() }?.let {
                CatalogMarketCandlesInfo(market = marketInfo.toString(), frequencies = it)
            }
        }

    abstract fun getTieredS3BooksStatistics(market: ParsedMarket): MarketStatistics.Statistics?

    suspend fun getTieredS3Books(request: CatalogMarketRequest): FunctionResult<ApiError, Pair<ChunkedResponseFormat, Flow<Any>>> =
        marketCatalogFlow(request) { marketInfo, timeRestriction ->
            toTieredS3MarketOrderBooksInfo(request, marketInfo)
        }

    protected abstract fun toTieredS3MarketOrderBooksInfo(
        request: CatalogMarketRequest,
        marketInfo: ParsedMarket,
    ): Any?

    abstract fun getTieredS3QuotesStatistics(market: ParsedMarket): MarketStatistics.Statistics?

    suspend fun getTieredS3Quotes(request: CatalogMarketRequest): FunctionResult<ApiError, Pair<ChunkedResponseFormat, Flow<Any>>> =
        marketCatalogFlow(request) { marketInfo, timeRestriction ->
            val quotesStatistics = getTieredS3QuotesStatistics(marketInfo)
            toMarketTimeRange(request.apiKey, quotesStatistics)
                ?.let { (minTime, maxTime) ->
                    CatalogMarketQuotesInfo(
                        market = marketInfo.toString(),
                        minTime = minTime,
                        maxTime = maxTime,
                    )
                }
        }

    suspend fun getFundingRates(request: CatalogMarketRequest): FunctionResult<ApiError, Pair<ChunkedResponseFormat, Flow<Any>>> =
        marketCatalogFlow(request) { marketInfo, timeRestriction ->
            val fundingRatesStatistics = marketStatisticsService.getFundingRatesStatistics(marketInfo)
            toMarketTimeRange(request.apiKey, fundingRatesStatistics)
                ?.let { (minTime, maxTime) ->
                    CatalogMarketFundingRatesInfo(
                        market = marketInfo.toString(),
                        minTime = minTime,
                        maxTime = maxTime,
                    )
                }
        }

    suspend fun getFundingRatesPredicted(request: CatalogMarketRequest): FunctionResult<ApiError, Pair<ChunkedResponseFormat, Flow<Any>>> =
        marketCatalogFlow(request) { marketInfo, timeRestriction ->
            val statistics =
                if (marketInfo is ParsedMarket.ParsedDerivativesMarket && marketInfo.type == DerivativesMarketType.FUTURE) {
                    marketStatisticsService.getFundingRatesPredictedStatistics(marketInfo)
                } else {
                    null
                }
            toMarketTimeRange(request.apiKey, statistics)
                ?.let { (minTime, maxTime) ->
                    CatalogMarketFundingRatesPredictedInfo(
                        market = marketInfo.toString(),
                        minTime = minTime,
                        maxTime = maxTime,
                    )
                }
        }

    suspend fun getGreeks(request: CatalogMarketRequest): FunctionResult<ApiError, Pair<ChunkedResponseFormat, Flow<Any>>> =
        getOptionTickerData(request) { market, minTime, maxTime ->
            CatalogMarketGreeksInfo(
                market = market,
                minTime = minTime,
                maxTime = maxTime,
            )
        }

    suspend fun getContractPrices(request: CatalogMarketRequest): FunctionResult<ApiError, Pair<ChunkedResponseFormat, Flow<Any>>> =
        marketCatalogFlow(request) { marketInfo, timeRestriction ->
            val tickerStatistics =
                if (marketInfo is ParsedMarket.ParsedDerivativesMarket) {
                    when (marketInfo.type) {
                        DerivativesMarketType.OPTION -> marketStatisticsService.getOptionTickerStatistics(marketInfo)
                        DerivativesMarketType.FUTURE -> marketStatisticsService.getFutureTickerStatistics(marketInfo)
                    }
                } else {
                    null
                }
            toMarketTimeRange(request.apiKey, tickerStatistics)
                ?.let { (minTime, maxTime) ->
                    CatalogMarketContractPricesInfo(
                        market = marketInfo.toString(),
                        minTime = minTime,
                        maxTime = maxTime,
                    )
                }
        }

    suspend fun getImpliedVolatility(request: CatalogMarketRequest): FunctionResult<ApiError, Pair<ChunkedResponseFormat, Flow<Any>>> =
        getOptionTickerData(request) { market, minTime, maxTime ->
            CatalogMarketImpliedVolatilityInfo(
                market = market,
                minTime = minTime,
                maxTime = maxTime,
            )
        }

    private suspend fun <T> getOptionTickerData(
        request: CatalogMarketRequest,
        resultPayloadConverter: (market: String, minTime: String, maxTime: String) -> T,
    ): FunctionResult<ApiError, Pair<ChunkedResponseFormat, Flow<Any>>> =
        marketCatalogFlow(request) { marketInfo, timeRestriction ->
            val optionTickerStatistics = marketStatisticsService.getOptionTickerStatistics(marketInfo)
            toMarketTimeRange(request.apiKey, optionTickerStatistics)
                ?.let { (minTime, maxTime) -> resultPayloadConverter(marketInfo.toString(), minTime, maxTime) }
        }

    suspend fun getOpeninterest(request: CatalogMarketRequest): FunctionResult<ApiError, Pair<ChunkedResponseFormat, Flow<Any>>> =
        marketCatalogFlow(request) { marketInfo, timeRestriction ->
            val openinterestStatistics = marketStatisticsService.getOpenInterestStatistics(marketInfo)
            toMarketTimeRange(request.apiKey, openinterestStatistics)
                ?.let { (minTime, maxTime) ->
                    CatalogMarketOpeninterestInfo(
                        market = marketInfo.toString(),
                        minTime = minTime,
                        maxTime = maxTime,
                    )
                }
        }

    suspend fun getLiquidations(request: CatalogMarketRequest): FunctionResult<ApiError, Pair<ChunkedResponseFormat, Flow<Any>>> =
        marketCatalogFlow(request) { marketInfo, timeRestriction ->
            val liquidationsStatistics = marketStatisticsService.getLiquidationsStatistics(marketInfo)
            toMarketTimeRange(request.apiKey, liquidationsStatistics)
                ?.let { (minTime, maxTime) ->
                    CatalogMarketLiquidationsInfo(
                        market = marketInfo.toString(),
                        minTime = minTime,
                        maxTime = maxTime,
                    )
                }
        }

    suspend fun getMetrics(
        request: CatalogMarketRequest,
        metrics: List<String>? = null,
    ): FunctionResult<ApiError, Pair<ChunkedResponseFormat, Flow<Any>>> {
        val metricsSet = metrics?.toHashSet()
        return marketCatalogFlow(request) { parsedMarket, timeRestriction ->
            val market = parsedMarket.toString()
            val metricsMap =
                if (!metricsSet.isNullOrEmpty()) {
                    marketStatisticsService.getMarketMetrics(market)?.filterKeys { it in metricsSet }
                } else {
                    marketStatisticsService.getMarketMetrics(market)
                }?.takeIf { it.isNotEmpty() }

            metricsMap
                ?.toSortedMap()
                ?.map { (metric, statistics) ->
                    val frequencies = statistics.map { MarketMetricFrequency(it.frequency, it.minTime, it.maxTime) }
                    MarketMetricInfo(metric, frequencies)
                }?.let { CatalogMarketMetricsInfo(market, it) }
        }
    }

    abstract suspend fun <R> marketCatalogFlow(
        request: CatalogMarketRequest,
        transform: (ParsedMarket, TimeRestriction?) -> R?,
    ): FunctionResult<ApiError, Pair<ChunkedResponseFormat, Flow<Any>>>

    suspend fun <R> marketReferenceDataFlow(
        request: CatalogMarketRequest,
        transform: (ParsedMarket, ReferenceDataMarketInfo, TimeRestriction?) -> R?,
    ): FunctionResult<ApiError, Pair<ChunkedResponseFormat, Flow<Any>>> =
        marketCatalogFlow(request) { market, timeRestriction ->
            val marketInfo =
                when (market) {
                    is ParsedMarket.ParsedSpotMarket -> market.toMarketInfo()
                    is ParsedMarket.ParsedDerivativesMarket -> market.toMarketInfo()
                    is ParsedMarket.ParsedDefiMarket -> market.toMarketInfo()
                }
            marketInfo?.let { transform(market, marketInfo, timeRestriction) }
        }

    private fun ParsedMarket.ParsedSpotMarket.toMarketInfo(): ReferenceDataMarketInfo {
        val market = this.toString()
        val metadata = marketStatisticsService.getSpotMetadata(market)

        return ReferenceDataMarketInfo(
            market = market,
            exchange = this.exchange,
            base = this.base,
            quote = this.quote,
            pair = "${this.base}-${this.quote}",
            symbol = marketStatisticsService.getSpotSymbol(market),
            type = "spot",
            status = metadata?.status,
            orderAmountIncrement = metadata?.orderAmountIncrement,
            orderAmountMin = metadata?.orderAmountMin,
            orderAmountMax = metadata?.orderAmountMax,
            orderPriceIncrement = metadata?.orderPriceIncrement,
            orderPriceMin = metadata?.orderPriceMin,
            orderPriceMax = metadata?.orderPriceMax,
            orderSizeMin = metadata?.orderSizeMin,
            orderTakerFee = metadata?.orderTakerFee,
            orderMakerFee = metadata?.orderMakerFee,
            marginTradingEnabled = metadata?.marginTradingEnabled,
            baseNative = metadata?.baseNative,
            quoteNative = metadata?.quoteNative,
        )
    }

    private fun ParsedMarket.ParsedDefiMarket.toMarketInfo(): ReferenceDataMarketInfo? {
        val defiMetadata = marketStatisticsService.getDefiMetadata(this) ?: return null

        return ReferenceDataMarketInfo(
            market = this.toString(),
            exchange = this.exchange,
            base = this.base,
            quote = this.quote,
            pair = "${this.base}-${this.quote}",
            type = "spot",
            // omit the pool config id for DeFi markets which:
            // 1. don't support multiple pools
            // 2. are aggregate markets
            poolConfigId = this.poolId.takeIf { it != "0" && it != "-1" },
            contractAddress = defiMetadata.contractAddress,
            baseAddress = defiMetadata.baseAddress,
            quoteAddress = defiMetadata.quoteAddress,
            fee = defiMetadata.fee,
            // omit for aggregate DeFi markets
            priceIncludesFee = if (this.poolId != "-1") defiMetadata.priceIncludesFee else null,
            // omit for aggregate DeFi markets
            variableFee = if (this.poolId != "-1") defiMetadata.variableFee else null,
            experimental = true,
        )
    }

    private fun ParsedMarket.ParsedDerivativesMarket.toMarketInfo(): ReferenceDataMarketInfo {
        val market = this.toString()
        val metadata =
            when (this.type) {
                DerivativesMarketType.OPTION -> marketStatisticsService.getOptionsMetadata(market)
                DerivativesMarketType.FUTURE -> marketStatisticsService.getFuturesMetadata(market)
            }
        val optionMetadata = metadata as? OptionMetadata
        val futureMetadata = metadata as? FutureMetadata
        return ReferenceDataMarketInfo(
            market = market,
            pair = metadata?.getPair(),
            exchange = this.exchange,
            symbol = this.symbol,
            type = this.getType(),
            base = metadata?.base,
            quote = metadata?.quote,
            sizeAsset = metadata?.sizeAsset,
            contractSize = metadata?.size?.let { CommonUtils.formatBigDecimal(it) },
            tickSize = futureMetadata?.tickSize?.let { CommonUtils.formatBigDecimal(it) },
            multiplierSize = futureMetadata?.multiplierSize?.let { CommonUtils.formatBigDecimal(it) },
            listing = metadata?.listing?.let { TimeUtils.dateTimeFormatter.format(it) },
            expiration = metadata?.expiration?.let { TimeUtils.dateTimeFormatter.format(it) },
            status = metadata?.status,
            orderAmountIncrement = metadata?.orderAmountIncrement,
            orderAmountMin = metadata?.orderAmountMin,
            orderAmountMax = metadata?.orderAmountMax,
            orderPriceIncrement = metadata?.orderPriceIncrement,
            orderPriceMin = metadata?.orderPriceMin,
            orderPriceMax = metadata?.orderPriceMax,
            orderSizeMin = metadata?.orderSizeMin,
            orderTakerFee = metadata?.orderTakerFee,
            orderMakerFee = metadata?.orderMakerFee,
            marginTradingEnabled = metadata?.marginTradingEnabled,
            isEuropean = optionMetadata?.european,
            strike = optionMetadata?.strike?.let { CommonUtils.formatBigDecimal(it) },
            optionContractType = optionMetadata?.optionContractType,
            settlementPrice = optionMetadata?.settlementPrice?.let { CommonUtils.formatBigDecimal(it) },
            marginAsset = metadata?.marginAsset,
            baseNative = metadata?.baseNative,
            quoteNative = metadata?.quoteNative,
        )
    }

    private fun MarketStatistics.MarketMetadata?.getPair(): String? =
        if (this?.base != null && this.quote != null) "${this.base}-${this.quote}" else null

    protected suspend fun discoverMarkets(
        request: CatalogMarketRequest,
        marketIds: List<ParsedMarket>,
        reverseOrder: Boolean,
    ): FunctionResult<ApiError, Sequence<Pair<ParsedMarket, TimeRestriction?>>> {
        val amsParamsToEndpointParamsLocal =
            if (marketIds.isNotEmpty()) {
                amsParamsToEndpointParamsForMarkets
            } else {
                amsParamsToEndpointParams
            }

        val requestedMarketIds = marketIds.toSet()
        val context =
            marketDiscoveryContextFactory.createContext(
                request.apiKey,
                requestedMarketIds,
                amsParamsToEndpointParamsLocal,
            )
        return context
            .discover(
                request.type,
                request.exchange,
                requestedMarketIds,
                request.base,
                request.quote,
                request.symbol,
                request.asset?.let { setOf(it) },
                catalogVersion,
                reverseOrder,
                true,
            ).map { results -> results.map { it.marketAndMetadata.market to it.timeRestriction } }
            .mapFailure { context.discoveryToApiError(it) }
    }

    fun validateInputParamsAndParseMarketIds(
        request: CatalogMarketRequest,
    ): FunctionResult<ApiError, Triple<ChunkedResponseFormat, Int, List<ParsedMarket>>> {
        // validate parameters
        if (request.type != null && request.type !in listOf("spot", "future", "option")) {
            return FunctionResult.Failure(ApiError.BadParameter("type", "Type '${request.type}' is not supported."))
        }

        val marketIds: List<ParsedMarket> =
            request.markets?.let { market ->
                market.map {
                    CommonUtils.parseMarket(it)
                        ?: return FunctionResult.Failure(ApiError.BadParameter("markets", "Invalid format for market: '$it'."))
                }
            } ?: emptyList()

        if (marketIds.isNotEmpty()) {
            if (request.asset != null ||
                request.exchange != null ||
                request.base != null ||
                request.quote != null ||
                request.symbol != null ||
                request.type != null
            ) {
                return FunctionResult.Failure(
                    ApiError.BadParameter("markets", "Cannot combine filtering by 'markets' with other filtering options."),
                )
            }
        } else if (request.asset != null) {
            if (request.base != null || request.quote != null || request.symbol != null) {
                return FunctionResult.Failure(
                    ApiError.BadParameter("asset", "Cannot combine filtering by 'asset' with other filtering options."),
                )
            }
            if (request.type != null && !request.type.equals("spot", true)) {
                return FunctionResult.Failure(ApiError.BadParameter("type", "'type' should be 'spot' when filtering by 'asset'."))
            }
        } else if (request.symbol != null) {
            if (request.base != null || request.quote != null) {
                return FunctionResult.Failure(
                    ApiError.BadParameter("symbol", "Cannot combine filtering by 'symbol' with other filtering options."),
                )
            }
            if (request.type != null && request.type.equals("spot", true)) {
                return FunctionResult.Failure(ApiError.BadParameter("type", "'type' should not be 'spot' when filtering by 'symbol'."))
            }
        }
        val responseFormat =
            ChunkedResponseFormat
                .fromValueOrError(
                    request.format,
                    setOf(ChunkedResponseFormat.Json(), ChunkedResponseFormat.JsonStream),
                ).getOrElse {
                    return it.toFailure()
                }
        val responseLimit =
            if (request.limit == null || request.limit.equals("none", ignoreCase = true)) {
                -1
            } else {
                val limit = request.limit.toIntOrNull() ?: -1
                if (limit <= 0) {
                    return FunctionResult.Failure(ApiError.BadParameter("limit", "Must be positive number or `none`."))
                }
                limit
            }
        return FunctionResult.Success(Triple(responseFormat, responseLimit, marketIds))
    }

    fun toMarketTimeRange(
        apiKey: String?,
        stats: MarketStatistics.Statistics?,
    ): MarketTimeRange? {
        val statistics = stats ?: return null
        val (minTime, maxTime) =
            DataUtils.getMinMaxTimeBasedOnApiKey(
                apiKey,
                communityApiKey,
                statistics.minTime,
                statistics.maxTime,
            )
        if (minTime == null || maxTime == null) {
            return null
        }
        return MarketTimeRange(minTime, maxTime)
    }

    abstract fun getTieredS3BooksStatisticsMap(): Map<Int, Map<String, MarketStatistics.Statistics>>

    protected fun getTieredS3OrderbooksDepths(
        market: String,
        request: CatalogMarketRequest,
    ): List<CatalogMarketOrderbookDepth> =
        getTieredS3BooksStatisticsMap()
            .entries
            .sortedBy { it.key }
            .mapNotNull { (depth, byMarket) ->
                // 0 is a union of all depths
                if (depth == 0) {
                    null
                } else {
                    toMarketTimeRange(request.apiKey, byMarket[market])?.let { (minTime, maxTime) ->
                        if (depth == 100 && maxTime > tenPercentMidPriceBookStartTime) {
                            val tenPctMidPriceMinTime =
                                if (tenPercentMidPriceBookStartTime > minTime) {
                                    tenPercentMidPriceBookStartTime
                                } else {
                                    minTime
                                }
                            listOf(
                                CatalogMarketOrderbookDepth(
                                    depth = depth.toString(),
                                    minTime = minTime,
                                    maxTime = maxTime,
                                ),
                                CatalogMarketOrderbookDepth(
                                    depth = "10pct_mid_price",
                                    minTime = tenPctMidPriceMinTime,
                                    maxTime = maxTime,
                                ),
                            )
                        } else {
                            listOf(
                                CatalogMarketOrderbookDepth(
                                    depth = depth.toString(),
                                    minTime = minTime,
                                    maxTime = maxTime,
                                ),
                            )
                        }
                    }
                }
            }.flatten()
}

class CatalogMarketRequest(
    val httpRequest: HttpRequest,
    val apiKey: String?,
    val markets: List<String>?,
    val exchange: String?,
    val type: String?,
    val base: String?,
    val quote: String?,
    val asset: String?,
    val symbol: String?,
    val format: String?,
    val limit: String? = null,
    val pretty: Boolean = false,
    val pageRequest: StringPageRequest? = null,
)

/**
 * This class is aimed to prevent keeping worker thread busy during the execution of heavy CPU operations
 * by using delay after some period of time the thread was occupied.
 * We are using delay instead of yield because yield didn't give us the desired behaviour.
 */
class ExecutionTimeThrottler(
    val allowedBusyTimeMs: Long,
    val delayMs: Long = 1,
) {
    var collectedTimeMs: Long = 0

    inline fun <T> track(action: () -> T?): T? {
        val result: T?
        val timeSpentMs =
            measureTimeMillis {
                result = action()
            }
        collectedTimeMs += timeSpentMs
        return result
    }

    suspend inline fun <T> execute(action: () -> T?): T? {
        if (collectedTimeMs > allowedBusyTimeMs) {
            delay()
            collectedTimeMs = 0
        }
        return track(action)
    }

    suspend fun delay() {
        delay(delayMs)
    }
}
