package io.coinmetrics.api.service.catalog.metric.discovery

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.model.TimeRestriction
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.toFailure
import io.coinmetrics.api.toSuccess

/**
 * When time restrictions are not supported by resource yet, we do AMS check for this resource,
 * and return unbound time restrictions for each entity.
 */
class SimpleTimeRestrictionDiscoverer(
    private val amsService: AmsService,
    private val resource: String,
    private val entityDisplayName: String,
) : TimeRestrictionDiscoverer {
    override suspend fun discover(
        apiKey: String?,
        entities: Set<String>?,
        filters: Map<String, String>,
        ignoreUnsupportedOrForbiddenErrors: Boolean,
    ): FunctionResult<ApiError, Map<String, TimeRestriction>?> {
        if (apiKey != null) {
            check(apiKey).getOrElse {
                if (ignoreUnsupportedOrForbiddenErrors && it.status == 403) {
                    return mapOf<String, TimeRestriction>().toSuccess()
                }
                return it.toFailure()
            }
        }
        return entities?.associateWith { TimeRestriction.unbounded }.toSuccess()
    }

    private suspend fun check(apiKey: String): FunctionResult<ApiError, Unit> {
        amsService
            .check(apiKey, resource) { null }
            .getOrElse { (apiError) ->
                return apiError.normalise().toFailure()
            }
        return Unit.toSuccess()
    }

    private fun ApiError.normalise(): ApiError =
        if (this.status == 403) {
            ApiError.ForbiddenWithMessage(
                "${entityDisplayName.replaceFirstChar { it.titlecaseChar() }} " +
                    "metrics are not available with supplied credentials.",
            )
        } else {
            this
        }
}
