package io.coinmetrics.api.service.catalog.metric.impl

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.ChunkedResponseFormat
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.model.DataAvailabilityTimeRange
import io.coinmetrics.api.model.page.StringPageRequest
import io.coinmetrics.api.models.CatalogExchangeMetricFrequency
import io.coinmetrics.api.models.CatalogExchangeMetricInfo
import io.coinmetrics.api.models.CatalogExchangeMetricsResponse
import io.coinmetrics.api.models.CatalogV2ExchangeMetricsInfo
import io.coinmetrics.api.models.ExchangeMetricFrequency
import io.coinmetrics.api.models.ExchangeMetricInfo
import io.coinmetrics.api.resources.Resources
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.service.catalog.metric.BaseMetricsService
import io.coinmetrics.api.service.catalog.metric.discovery.TimeRestrictionDiscovererImpl
import io.coinmetrics.api.service.catalog.metric.toFullMetricsInfo
import io.coinmetrics.api.statistics.metrics.ExchangeMetricStatistics
import io.coinmetrics.api.toFailure
import io.coinmetrics.api.toSuccess
import io.coinmetrics.api.utils.toMetricInfoFlow
import io.coinmetrics.httpserver.HttpRequest
import kotlinx.coroutines.flow.Flow
import org.slf4j.LoggerFactory
import java.time.Instant

class ExchangeMetricsService(
    docsBaseUrl: String,
    amsService: AmsService,
    private val metricStatistics: ExchangeMetricStatistics,
) : BaseMetricsService(
        entityParamName = "exchanges",
        metricStatistics = metricStatistics,
        timeRestrictionDiscoverer =
            TimeRestrictionDiscovererImpl(
                amsService,
                resource = "exchange_metrics",
                resourceEntityParam = "exchange",
                entityDisplayName = "exchange",
            ),
        docsBaseUrl = docsBaseUrl,
    ) {
    companion object {
        private val log = LoggerFactory.getLogger(ExchangeMetricsService::class.java)
    }

    override val supportedFrequencies: Collection<String>
        get() = metricStatistics.supportedFrequencies()

    suspend fun findSupportedCatalogV1ExchangeMetrics(
        apiKey: String?,
        metrics: List<String>?,
        reviewable: Boolean?,
    ): FunctionResult<ApiError, CatalogExchangeMetricsResponse> =
        findCatalogV1Metrics(apiKey, metrics?.toSet(), reviewable)
            .toFullMetricsInfo { metricName, metricInfo, exchangeAssetsPerFrequency ->
                CatalogExchangeMetricInfo(
                    metric = metricName,
                    fullName = metricInfo.name,
                    description = metricInfo.description,
                    product = metricInfo.product,
                    category = metricInfo.category,
                    subcategory = metricInfo.subcategory,
                    unit = metricInfo.unit,
                    dataType = metricInfo.dataType.lowercase(),
                    type = metricInfo.metricType,
                    reviewable = if (metricInfo.reviewable) true else null,
                    frequencies =
                        exchangeAssetsPerFrequency.map { (frequency, exchanges) ->
                            CatalogExchangeMetricFrequency(
                                frequency = frequency,
                                exchanges = exchanges.toList(),
                            )
                        },
                    displayName = metricInfo.displayName,
                    experimental = metricInfo.experimental,
                )
            }.map { CatalogExchangeMetricsResponse(it) }

    suspend fun findSupportedCatalogV1ExchangeMetrics(
        apiKey: String?,
        entities: Set<String>,
        failOnForbiddenEntities: Boolean,
        requestReceivedTime: Instant,
    ): HashMap<String, List<ExchangeMetricInfo>?> =
        FindCatalogV1MetricsForEntities(apiKey, entities, requestReceivedTime)
            .apply {
                this.failOnUnsupportedEntities = false
                this.failOnForbiddenEntities = failOnForbiddenEntities
            }.execute()
            .getOrElse { emptyMap() }
            .entries
            .associateTo(HashMap()) { (exchange, metricsMap) -> exchange to toExchangeMetricInfoArray(metricsMap) }

    suspend fun findSupportedCatalogV2ExchangeMetrics(
        apiKey: String?,
        exchanges: List<String>?,
        metrics: List<String>?,
        format: String,
        httpRequest: HttpRequest,
        pageRequest: StringPageRequest,
    ): FunctionResult<ApiError, Pair<ChunkedResponseFormat, Flow<Any>>> =
        findCatalogV2Metrics(apiKey, exchanges, metrics, reviewable = null, httpRequest.receivedTime)
            .toMetricInfoFlow(format, httpRequest, pageRequest) { exchange, metricsMap ->
                toExchangeMetricInfoArray(metricsMap)?.let { metricsArray ->
                    CatalogV2ExchangeMetricsInfo(exchange, metricsArray)
                }
            }

    override fun validateEntities(entities: Set<String>): ApiError? =
        entities.firstNotNullOfOrNull { exchange ->
            when (val result = Resources.getExchangeIdByName(exchange)) {
                is FunctionResult.Success -> null
                is FunctionResult.Failure -> {
                    log.warn(result.value)
                    ApiError.BadParameter("exchanges", "Value '$exchange' is not supported.")
                }
            }
        }

    private fun toExchangeMetricInfoArray(metricsMap: Map<String, Map<String, DataAvailabilityTimeRange>>): List<ExchangeMetricInfo>? =
        metricsMap
            .mapNotNull { (metric, frequencies) -> toExchangeMetricInfo(metric, frequencies) }
            .takeIf { it.isNotEmpty() }

    private fun toExchangeMetricInfo(
        metric: String,
        frequencies: Map<String, DataAvailabilityTimeRange>,
    ): ExchangeMetricInfo? {
        if (frequencies.isEmpty()) return null
        val frequenciesArray =
            frequencies.mapNotNull { (frequency, timeRange) ->
                ExchangeMetricFrequency(
                    frequency = frequency,
                    minTime = timeRange.minTime.toString(),
                    maxTime = timeRange.maxTime.toString(),
                    // community = isCommunityMetric(exchange, metric, frequency).let { if (it) true else null }
                )
            }
        return ExchangeMetricInfo(metric = metric, frequencies = frequenciesArray)
    }

    fun parseExchanges(requestedExchanges: List<String>): FunctionResult<ApiError, Pair<Set<String>, Boolean>> {
        return if (requestedExchanges.contains("*")) {
            // ignore other values if '*' was specified
            metricStatistics.supportedExchanges() to true
        } else {
            requestedExchanges
                .map {
                    if (this.isEntitySupported(it)) {
                        it
                    } else {
                        return ApiError.UnsupportedParameterValue("exchanges", it).toFailure()
                    }
                }.toSet() to false
        }.toSuccess()
    }
}
