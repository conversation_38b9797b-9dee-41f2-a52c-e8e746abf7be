package io.coinmetrics.api.service.catalog.metric.discovery

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.model.TimeRestriction

interface TimeRestrictionDiscoverer {
    suspend fun discover(
        apiKey: String?,
        entities: Set<String>?,
        filters: Map<String, String> = emptyMap(),
        ignoreUnsupportedOrForbiddenErrors: Boolean,
    ): FunctionResult<ApiError, Map<String, TimeRestriction>?>
}
