package io.coinmetrics.api.service.catalog.metric

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.ChunkedResponseFormat
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.model.DataAvailabilityTimeRange
import io.coinmetrics.api.model.TimeRestriction
import io.coinmetrics.api.model.isUnbounded
import io.coinmetrics.api.model.page.StringPageRequest
import io.coinmetrics.api.resources.Resources
import io.coinmetrics.api.service.catalog.metric.discovery.TimeRestrictionDiscoverer
import io.coinmetrics.api.statistics.metrics.MetricsStatistics
import io.coinmetrics.api.toFailure
import io.coinmetrics.api.toSuccess
import io.coinmetrics.api.utils.LazyInstant.Companion.toLazyInstant
import io.coinmetrics.httpserver.HttpRequest
import kotlinx.coroutines.flow.Flow
import java.time.Instant

abstract class BaseMetricsService(
    docsBaseUrl: String,
    private val entityParamName: String,
    private val metricStatistics: MetricsStatistics,
    private val timeRestrictionDiscoverer: TimeRestrictionDiscoverer,
) : MetricsService(docsBaseUrl) {
    protected abstract val supportedFrequencies: Collection<String>

    fun isEntitySupported(entity: String) = metricStatistics.isSupported(entity)

    open suspend fun findMetricsReferenceData(
        metrics: List<String>?,
        reviewable: Boolean?,
        format: String,
        httpRequest: HttpRequest,
        pageRequest: StringPageRequest,
    ): FunctionResult<ApiError, Pair<ChunkedResponseFormat, Flow<Any>>> =
        getMetricsReferenceData(metrics, reviewable, format, httpRequest, pageRequest)

    protected inner class FindCatalogV1MetricsForEntities(
        private val apiKey: String?,
        private val entities: Set<String>?,
        private val requestReceivedTime: Instant,
    ) {
        var failOnForbiddenEntities = entities != null
        var failOnUnsupportedEntities = entities != null

        suspend fun execute(): FunctionResult<ApiError, Map<String, Map<String, Map<String, DataAvailabilityTimeRange>>>> {
            require(entities != null || !failOnForbiddenEntities && !failOnUnsupportedEntities)
            if (entities != null) {
                require(entities.isNotEmpty())
                validateEntities(entities)?.also { return it.toFailure() }
            }

            // entity -> metric -> frequency -> statistics
            val metricAvailabilityMap = metricStatistics.groupedByEntity()

            val entityToTimeRestrictionMap =
                timeRestrictionDiscoverer
                    .discover(
                        apiKey,
                        entities = entities ?: metricAvailabilityMap.keys,
                        ignoreUnsupportedOrForbiddenErrors = !failOnForbiddenEntities,
                    ).getOrElse {
                        return it.toFailure()
                    } ?: emptyMap()

            val context =
                TimeRestriction.EvaluateContext(
                    now = requestReceivedTime,
                    maxAvailableTime = null,
                )
            val result =
                entityToTimeRestrictionMap
                    .mapNotNull { (entity, timeRestriction) ->
                        val metricToFrequencyToAvailability = metricAvailabilityMap[entity]
                        if (metricToFrequencyToAvailability == null) {
                            if (failOnUnsupportedEntities) {
                                return ApiError.BadParameter(entityParamName, "Value '$entity' is not supported.").toFailure()
                            } else {
                                null
                            }
                        } else {
                            entity to
                                intersectDataAvailabilityTimeRanges(
                                    timeRanges = metricToFrequencyToAvailability,
                                    timeRestriction,
                                    context,
                                )
                        }
                    }.associateTo(hashMapOf()) { it }

            return FunctionResult.Success(result)
        }
    }

    override suspend fun findCatalogV1Metrics(
        apiKey: String?,
        metrics: Set<String>?,
        reviewable: Boolean?,
    ): FunctionResult<ApiError, Map<String, Map<String, Set<String>>>> {
        metrics?.also { validateMetrics(it)?.also { apiError -> return apiError.toFailure() } }

        // Return empty results even when there are no supported metrics for this key
        val entityToTimeRestrictionsMap =
            timeRestrictionDiscoverer
                .discover(
                    apiKey,
                    entities = null,
                    ignoreUnsupportedOrForbiddenErrors = true,
                ).getOrElse { return FunctionResult.Success(emptyMap()) }

        return metricStatistics
            .groupedByMetric()
            .asSequence()
            .mapNotNull { (key, value) ->
                value
                    .asSequence()
                    .filter { (freq, _) ->
                        freq in supportedFrequencies
                    }.mapNotNull { (key, entities) ->
                        if (entityToTimeRestrictionsMap != null) {
                            entities.intersect(entityToTimeRestrictionsMap.keys)
                        } else {
                            entities
                        }.takeIf { it.isNotEmpty() }
                            ?.let { key to it }
                    }.associate { it }
                    .takeIf { it.isNotEmpty() }
                    ?.let { key to it }
            }.associate { it }
            .let {
                if (metrics != null) {
                    it.filterKeys { metric -> metric in metrics }
                } else {
                    it
                }
            }.let {
                if (reviewable == null) {
                    it
                } else {
                    it.filter { (metric) ->
                        val reviewableMetric = Resources.isMetricReviewable(metric)
                        reviewableMetric == reviewable
                    }
                }
            }.toSuccess()
    }

    override suspend fun findTimeseriesMetrics(
        apiKey: String,
        frequency: String,
        entities: Set<String>,
        metrics: Set<String>,
        ignoreForbiddenAndUnsupportedErrors: Boolean,
    ): FunctionResult<ApiError, Map<String, EntityMetricsAvailability>> {
        if (supportedFrequencies.isNotEmpty() && frequency !in supportedFrequencies) {
            return FunctionResult.Failure(ApiError.UnsupportedParameterValue("frequency", frequency))
        }

        return timeRestrictionDiscoverer
            .discover(
                apiKey = apiKey,
                entities = entities,
                ignoreUnsupportedOrForbiddenErrors = ignoreForbiddenAndUnsupportedErrors,
            ).map { it ?: emptyMap() }
            .map {
                it.mapValues { (entity, timeRestriction) ->
                    val metricToAvailability =
                        metrics.map { metric ->
                            metric to
                                if (metricStatistics.isSupported(entity, metric, frequency)) {
                                    MetricAvailability.Supported
                                } else {
                                    MetricAvailability.Unsupported
                                }
                        }
                    EntityMetricsAvailability(metricToAvailability, timeRestriction)
                }
            }
    }

    protected suspend fun findCatalogV2Metrics(
        apiKey: String?,
        entities: List<String>?,
        metrics: List<String>?,
        reviewable: Boolean?,
        requestReceivedTime: Instant,
    ): FunctionResult<ApiError, Map<String, Map<String, Map<String, DataAvailabilityTimeRange>>>> =
        findCatalogV2Metrics(apiKey, entities?.toSet(), metrics?.toSet(), reviewable, requestReceivedTime)

    fun filterSupportedMetrics(
        metricAvailability: Map<String, EntityMetricsAvailability>,
        patternRequested: Boolean,
    ): Set<Map.Entry<String, EntityMetricsAvailability>> =
        if (patternRequested) {
            metricAvailability
                .mapValues { (_, entityMetricsAvailability) ->
                    entityMetricsAvailability.copy(
                        metricToAvailability =
                            entityMetricsAvailability.metricToAvailability
                                .filter { (_, availability) -> availability is MetricAvailability.Supported },
                    )
                }.filter { it.value.metricToAvailability.isNotEmpty() }
        } else {
            metricAvailability
        }.entries

    /**
     * Accessible metrics for Catalog V2.
     * @return Map<Entity, Map<Metric, Map<Frequency, DataAvailabilityTimeRange>>>.
     */
    private suspend fun findCatalogV2Metrics(
        apiKey: String?,
        entities: Set<String>?,
        metrics: Set<String>?,
        reviewable: Boolean?,
        requestReceivedTime: Instant,
    ): FunctionResult<ApiError, Map<String, Map<String, Map<String, DataAvailabilityTimeRange>>>> {
        entities?.let { validateEntities(entities) }?.also { return it.toFailure() }
        metrics?.let { validateMetrics(metrics) }?.also { return it.toFailure() }

        val entityToTimeRestrictionMap =
            timeRestrictionDiscoverer
                .discover(
                    apiKey,
                    entities,
                    ignoreUnsupportedOrForbiddenErrors = entities == null,
                ).getOrElse { return it.toFailure() }

        val context =
            TimeRestriction.EvaluateContext(
                now = requestReceivedTime,
                maxAvailableTime = null,
            )
        return metricStatistics
            .groupedByEntity()
            .asSequence()
            .mapNotNull { (entity, metricsMap) ->
                if (entityToTimeRestrictionMap?.containsKey(entity) == false) {
                    return@mapNotNull null
                }

                val timeRestriction =
                    entityToTimeRestrictionMap?.get(entity).also {
                        if (entityToTimeRestrictionMap != null && it == null) {
                            // Entity is not authorized.
                            return@mapNotNull null
                        }
                    } ?: TimeRestriction.unbounded
                val timeRestrictionEvaluated = timeRestriction.evaluate(context)

                metricsMap
                    .asSequence()
                    .mapNotNull { (metric, frequencyToDataTimeRange) ->
                        if (metrics?.contains(metric) == false) {
                            null
                        } else {
                            val effectiveFrequencyToDataTimeRange =
                                if (reviewable == null && timeRestrictionEvaluated.isUnbounded()) {
                                    frequencyToDataTimeRange
                                } else {
                                    frequencyToDataTimeRange
                                        .asSequence()
                                        .mapNotNull { (frequency, dataRange) ->
                                            val effectiveDataRange =
                                                getEffectiveTimeRange(metric, reviewable, dataRange, timeRestrictionEvaluated)
                                            effectiveDataRange?.let { frequency to it }
                                        }.associate { it }
                                }
                            effectiveFrequencyToDataTimeRange
                                .takeIf { it.isNotEmpty() }
                                ?.let { metric to it }
                        }
                    }.associate { it }
                    .takeIf { it.isNotEmpty() }
                    ?.let { entity to it }
            }.associate { it }
            .toSuccess()
    }

    private fun getEffectiveTimeRange(
        metric: String,
        reviewable: Boolean?,
        dataAvailabilityTimeRange: DataAvailabilityTimeRange,
        timeRestrictionEvaluated: Pair<Instant?, Instant?>,
    ): DataAvailabilityTimeRange? {
        if (reviewable != null) {
            val reviewableMetric = Resources.isMetricReviewable(metric)
            if (reviewableMetric != reviewable) {
                return null
            }
        }
        return if (timeRestrictionEvaluated.isUnbounded()) {
            dataAvailabilityTimeRange
        } else {
            intersectDataAvailabilityTimeRange(dataAvailabilityTimeRange, timeRestrictionEvaluated)
        }
    }

    protected open fun validateEntities(entities: Set<String>): ApiError? = null

    private fun validateMetrics(metrics: Set<String>): ApiError? {
        metrics.forEach { metric ->
            Resources.getMetricInfo(metric) ?: return ApiError.BadParameter("metrics", "Value '$metric' is not supported.")
        }
        return null
    }

    protected inline fun <reified T> FunctionResult<
        ApiError,
        Map<String, Map<String, Map<String, DataAvailabilityTimeRange>>>,
    >.toMetricInfoArray(
        converter: (String, Map<String, Map<String, DataAvailabilityTimeRange>>) -> T?,
    ): FunctionResult<ApiError, List<T>> =
        this.map { entitiesMap ->
            entitiesMap.entries
                .sortedBy { (entity) -> entity }
                .mapNotNull { (entity, metricsMap) -> converter(entity, metricsMap) }
        }

    fun findMinDateFor1dFrequency(
        entity: String,
        requestedMetrics: Collection<String>,
    ): Instant? {
        val metrics: Map<String, Map<String, DataAvailabilityTimeRange>> =
            metricStatistics
                .groupedByEntity()[entity] ?: return null
        return metrics
            .filter { (key, _) -> key in requestedMetrics }
            .map { (_, value) -> value }
            .flatMap { entry ->
                entry
                    .filter { it.key == "1d" }
                    .values
            }.minOfOrNull { it.minTime }
            ?.value
    }
}

private fun intersectDataAvailabilityTimeRanges(
    // metric -> frequency -> statistics
    timeRanges: Map<String, Map<String, DataAvailabilityTimeRange>>,
    timeRestriction: TimeRestriction,
    context: TimeRestriction.EvaluateContext,
): Map<String, Map<String, DataAvailabilityTimeRange>> {
    if (timeRestriction.isUnbounded()) {
        return timeRanges
    }

    val timeRangeVal = timeRestriction.evaluate(context)

    return timeRanges
        .asSequence()
        .mapNotNull { (key1, map) ->
            map
                .asSequence()
                .mapNotNull { (key2, datr) ->
                    intersectDataAvailabilityTimeRange(datr, timeRangeVal)?.let { key2 to it }
                }.associateTo(hashMapOf()) { it }
                .takeIf { it.isNotEmpty() }
                ?.let { key1 to it }
        }.associateTo(hashMapOf()) { it }
}

private fun intersectDataAvailabilityTimeRange(
    timeRange1: DataAvailabilityTimeRange,
    timeRange2: Pair<Instant?, Instant?>,
): DataAvailabilityTimeRange? {
    val minTime = timeRange2.first?.let { maxOf(it, timeRange1.minTime.value).toLazyInstant() } ?: timeRange1.minTime
    val maxTime = timeRange2.second?.let { minOf(it, timeRange1.maxTime.value).toLazyInstant() } ?: timeRange1.maxTime
    return if (minTime > maxTime) {
        null
    } else {
        DataAvailabilityTimeRange(minTime, maxTime)
    }
}
