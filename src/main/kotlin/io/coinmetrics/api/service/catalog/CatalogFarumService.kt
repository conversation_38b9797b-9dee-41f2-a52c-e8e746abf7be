package io.coinmetrics.api.service.catalog

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.ChunkedResponseFormat
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.model.page.StringPageRequest
import io.coinmetrics.api.models.CatalogAssetChainsInfo
import io.coinmetrics.api.models.CatalogMempoolFeeratesInfo
import io.coinmetrics.api.models.CatalogMiningPoolTipsSummaryInfo
import io.coinmetrics.api.models.CatalogTransactionTrackerInfo
import io.coinmetrics.api.service.AmsService
import io.coinmetrics.api.statistics.farum.AssetChainsStatistics
import io.coinmetrics.api.statistics.farum.MempoolFeeratesStatistics
import io.coinmetrics.api.statistics.farum.MiningPoolTipsSummaryStatistics
import io.coinmetrics.api.statistics.farum.TransactionTrackerStatistics
import io.coinmetrics.api.utils.toEntitiesFlow
import io.coinmetrics.httpserver.HttpRequest
import kotlinx.coroutines.flow.Flow

class CatalogFarumService(
    private val amsService: AmsService,
    private val assetChainsStatistics: AssetChainsStatistics,
    private val mempoolFeeratesStatistics: MempoolFeeratesStatistics,
    private val transactionTrackerStatistics: TransactionTrackerStatistics,
    private val miningPoolTipsSummaryStatistics: MiningPoolTipsSummaryStatistics,
) {
    companion object {
        const val ASSET_CHAINS_RESOURCE = "asset_chains"
        const val MEMPOOL_FEERATES_RESOURCE = "mempool_feerates"
        const val MINING_POOL_TIPS_SUMMARY_RESOURCE = "mining_pool_tips"
        const val TRANSACTION_TRACKER_RESOURCE = "tx_tracker"
    }

    suspend fun getAssetChains(
        apiKey: String?,
        requestedAssets: List<String>?,
        format: String,
        httpRequest: HttpRequest,
        pageRequest: StringPageRequest? = null,
    ): FunctionResult<ApiError, Pair<ChunkedResponseFormat, Flow<Any>>> {
        val assetChainsStatisticsMap = assetChainsStatistics.getAssetChainsStatistics()
        val assets =
            filterAmsAssets(
                apiKey,
                resource = ASSET_CHAINS_RESOURCE,
                statisticsAssets = assetChainsStatisticsMap.keys,
                requestedAssets,
            ).intersect(setOf("btc", "eth")) // TODO: FIX TEMPORARY WORKAROUND FOR AMS discovery_script problems

        return assets
            .asSequence()
            .mapNotNull { asset ->
                val dataAvailabilityTimeRange = assetChainsStatisticsMap[asset]
                dataAvailabilityTimeRange?.let { asset to it }
            }.toEntitiesFlow(format, httpRequest, pageRequest) { asset, timeRange ->
                CatalogAssetChainsInfo(
                    asset,
                    minTime = timeRange.minTime.toString(),
                    maxTime = timeRange.maxTime.toString(),
                )
            }
    }

    suspend fun getMempoolFeerates(
        apiKey: String?,
        requestedAssets: List<String>?,
        format: String,
        httpRequest: HttpRequest,
        pageRequest: StringPageRequest? = null,
    ): FunctionResult<ApiError, Pair<ChunkedResponseFormat, Flow<Any>>> {
        val mempoolFeeratesStatisticsMap = mempoolFeeratesStatistics.getMempoolFeeratesStatistics()
        val assets =
            filterAmsAssets(
                apiKey,
                resource = MEMPOOL_FEERATES_RESOURCE,
                statisticsAssets = mempoolFeeratesStatisticsMap.keys,
                requestedAssets,
            )

        return assets
            .asSequence()
            .mapNotNull { asset ->
                val dataAvailabilityTimeRange = mempoolFeeratesStatisticsMap[asset]
                dataAvailabilityTimeRange?.let { asset to it }
            }.toEntitiesFlow(format, httpRequest, pageRequest) { asset, timeRange ->
                CatalogMempoolFeeratesInfo(
                    asset,
                    minTime = timeRange.minTime.toString(),
                    maxTime = timeRange.maxTime.toString(),
                )
            }
    }

    suspend fun getMiningPoolTipsSummary(
        apiKey: String?,
        requestedAssets: List<String>?,
        format: String,
        httpRequest: HttpRequest,
        pageRequest: StringPageRequest? = null,
    ): FunctionResult<ApiError, Pair<ChunkedResponseFormat, Flow<Any>>> {
        val miningPoolTipsSummaryStatisticsMap = miningPoolTipsSummaryStatistics.getMiningPoolTipsSummaryStatistics()
        val assets =
            filterAmsAssets(
                apiKey,
                resource = MINING_POOL_TIPS_SUMMARY_RESOURCE,
                statisticsAssets = miningPoolTipsSummaryStatisticsMap.keys,
                requestedAssets,
            ).intersect(setOf("btc")) // TODO: FIX TEMPORARY WORKAROUND FOR AMS discovery_script problems

        return assets
            .asSequence()
            .mapNotNull { asset ->
                val dataAvailabilityTimeRange = miningPoolTipsSummaryStatisticsMap[asset]
                dataAvailabilityTimeRange?.let { asset to it }
            }.toEntitiesFlow(format, httpRequest, pageRequest) { asset, timeRange ->
                CatalogMiningPoolTipsSummaryInfo(
                    asset,
                    minTime = timeRange.minTime.toString(),
                    maxTime = timeRange.maxTime.toString(),
                )
            }
    }

    suspend fun getTransactionTracker(
        apiKey: String?,
        requestedAssets: List<String>?,
        format: String,
        httpRequest: HttpRequest,
        pageRequest: StringPageRequest? = null,
    ): FunctionResult<ApiError, Pair<ChunkedResponseFormat, Flow<Any>>> {
        val transactionTrackerStatisticsMap = transactionTrackerStatistics.getTransactionTrackerStatistics()
        val assets =
            filterAmsAssets(
                apiKey,
                resource = TRANSACTION_TRACKER_RESOURCE,
                statisticsAssets = transactionTrackerStatisticsMap.keys,
                requestedAssets,
            )

        return assets
            .asSequence()
            .mapNotNull { asset ->
                val dataAvailabilityTimeRange = transactionTrackerStatisticsMap[asset]
                dataAvailabilityTimeRange?.let { asset to it }
            }.toEntitiesFlow(format, httpRequest, pageRequest) { asset, timeRange ->
                CatalogTransactionTrackerInfo(
                    asset,
                    minTime = timeRange.minTime.toString(),
                    maxTime = timeRange.maxTime.toString(),
                )
            }
    }

    private suspend fun filterAmsAssets(
        apiKey: String?,
        resource: String,
        statisticsAssets: Set<String>,
        requestedAssets: List<String>?,
    ): Set<String> {
        val amsAssets =
            amsService
                .discovery(resource = resource, target = "asset", apiKey = apiKey) { it }
                .getOrElse { return emptySet() }
                .values
                .map { it[0] }
                .toSet()

        return statisticsAssets
            .let { if (amsAssets.isEmpty()) it else it.intersect(amsAssets) }
            .let { requestedAssets?.intersect(it) ?: it }
    }
}
