package io.coinmetrics.api.service

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.ams.CheckResult
import io.coinmetrics.api.badMarketIdMessage
import io.coinmetrics.api.model.NormalizedMarket
import io.coinmetrics.api.model.ParsedMarket
import io.coinmetrics.api.model.TimeRestriction
import io.coinmetrics.api.monitoring.MarketResolvingMonitoring
import io.coinmetrics.api.resources.Resources
import io.coinmetrics.api.toFailure
import io.coinmetrics.api.toSuccess
import io.coinmetrics.api.utils.CommonUtils
import io.coinmetrics.api.utils.MarketUtils
import io.coinmetrics.api.utils.WildcardUtils
import io.coinmetrics.bookstreams.MarketKind
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import org.slf4j.LoggerFactory
import java.time.Clock
import java.time.Instant

class MarketResolvingService(
    private val clock: Clock,
    private val amsService: AmsService,
    private val marketStatisticsService: MarketStatisticsService,
    private val exchangeDiscoveryService: ExchangeDiscoveryService,
    private val monitoring: MarketResolvingMonitoring,
) {
    companion object {
        private val log = LoggerFactory.getLogger(MarketResolvingService::class.java)
    }

    suspend fun parseAndCheckMarkets(
        apiKey: String,
        requestedMarkets: List<String>,
        marketFilter: ((ParsedMarket) -> Boolean)? = null,
        errorMessageForFilteredOutMarketsIfPatternIsNotRequested: ((ParsedMarket) -> String)? = null,
    ): FunctionResult<ApiError, MarketsParsingResult> {
        val (markets, patternRequested) = unwrapMarkets(requestedMarkets).getOrElse { return FunctionResult.Failure(it) }

        val filteredMarkets =
            if (marketFilter != null) {
                require(errorMessageForFilteredOutMarketsIfPatternIsNotRequested != null) {
                    "errorMessageForFilteredOutMarkets must be set if marketFilter is set."
                }
                if (patternRequested) {
                    markets.filter(marketFilter)
                } else {
                    markets.forEach {
                        if (!marketFilter.invoke(it)) {
                            return ApiError
                                .BadParameter(
                                    "markets",
                                    errorMessageForFilteredOutMarketsIfPatternIsNotRequested.invoke(it),
                                ).toFailure()
                        }
                    }
                    markets
                }
            } else {
                markets
            }

        return parseAndCheckMarkets(apiKey, filteredMarkets, patternRequested)
    }

    private suspend fun parseAndCheckMarkets(
        apiKey: String,
        markets: List<ParsedMarket>,
        patternRequested: Boolean,
    ): FunctionResult<ApiError, MarketsParsingResult> {
        val timeRestrictionContext: TimeRestriction.EvaluateContext =
            TimeRestriction.EvaluateContext(
                now = clock.instant(),
                maxAvailableTime = null,
            )

        val marketsByAmsMarketType =
            if (patternRequested) {
                markets.mapNotNull { market ->
                    validateMarket(market).getOrNull()
                }
            } else {
                markets.map { market ->
                    validateMarket(market).getOrElse { return FunctionResult.Failure(it) }
                }
            }.groupByTo(HashMap()) {
                exchangeDiscoveryService.getAmsMarketType(it.parsedMarket)
            }

        if (marketsByAmsMarketType.isEmpty()) {
            return if (patternRequested) {
                FunctionResult.Success(MarketsParsingResult(emptyMap(), patternRequested))
            } else {
                FunctionResult.Failure(ApiError.BadParameter("markets", "All requested markets are not supported."))
            }
        }

        val marketsConstraints =
            marketsByAmsMarketType
                .flatMap { (amsMarketType, markets) ->
                    if (patternRequested) {
                        val discoveredExchangeToTimeRestrictionMap =
                            exchangeDiscoveryService.discoverExchanges(apiKey, amsMarketType, emptySet())
                        markets.mapNotNull { market ->
                            validatedMarketToConstraints(
                                market = market,
                                exchangeToTimeRestrictionMap = discoveredExchangeToTimeRestrictionMap,
                                timeRestrictionContext = timeRestrictionContext,
                            )
                        }
                    } else {
                        val requestedExchanges = markets.map { it.parsedMarket.exchange }.toHashSet()
                        val discoveredExchangeToTimeRestrictionMap =
                            exchangeDiscoveryService.discoverExchanges(apiKey, amsMarketType, requestedExchanges)
                        if (requestedExchanges.size > discoveredExchangeToTimeRestrictionMap.size) {
                            val forbiddenExchanges = requestedExchanges.minus(discoveredExchangeToTimeRestrictionMap.keys)
                            val forbiddenMarkets =
                                markets
                                    .asSequence()
                                    .filter { forbiddenExchanges.contains(it.parsedMarket.exchange) }
                                    .take(3)
                                    .map { it.parsedMarket.toString() }
                            val marketsStr = forbiddenMarkets.joinToString(", ")
                            return FunctionResult.Failure(
                                ApiError.ForbiddenWithMessage(
                                    "Requested markets are not available with supplied credentials: $marketsStr.",
                                ),
                            )
                        }
                        markets.mapNotNull { market ->
                            validatedMarketToConstraints(
                                market = market,
                                exchangeToTimeRestrictionMap = discoveredExchangeToTimeRestrictionMap,
                                timeRestrictionContext = timeRestrictionContext,
                            )
                        }
                    }
                }.toMap(HashMap())

        return FunctionResult.Success(MarketsParsingResult(marketsConstraints, patternRequested))
    }

    private fun validatedMarketToConstraints(
        market: ValidatedMarket,
        exchangeToTimeRestrictionMap: Map<String, TimeRestriction>,
        timeRestrictionContext: TimeRestriction.EvaluateContext,
    ): Pair<String, MarketConstraints>? {
        val timeRestriction = exchangeToTimeRestrictionMap[market.parsedMarket.exchange] ?: return null

        return market.parsedMarket.toString() to
            MarketConstraints(
                market.normalizedMarket,
                timeRestriction.start?.evaluate(timeRestrictionContext),
                timeRestriction.end?.evaluate(timeRestrictionContext),
                market.parsedMarket,
                market.normalizedMarket,
            )
    }

    data class UnwrappedMarkets(
        val markets: List<ParsedMarket>,
        val patternRequested: Boolean,
        val hash: Int,
    )

    /**
     * Unwraps wildcard market identifiers and returns the resulting set of unwrapped markets.
     *
     * This function records the initial market statistics hash at the start, allowing the caller to verify if the hash
     * has changed after unwrapping. A hash change might indicate a statistics update, potentially requiring another
     * call to `unwrapMarkets` to ensure consistency.
     *
     * @return A `FunctionResult` containing the unwrapped market identifiers and the statistics hash prior to
     *         unwrapping.
     */
    fun unwrapMarkets(markets: Collection<String>): FunctionResult<ApiError, UnwrappedMarkets> {
        val statisticsHash = marketStatisticsService.supportedMarketsHash

        val unwrappedWildcards =
            WildcardUtils
                .unwrapWildcards(
                    items = markets,
                    paramName = "markets",
                    universeOfItems = marketStatisticsService.getSupportedMarkets(reverseOrder = false).asSequence().map { it.value },
                    itemParser = { market ->
                        CommonUtils.parseMarket(market) ?: return FunctionResult.Failure(
                            ApiError.BadParameter(
                                "markets",
                                badMarketIdMessage(market),
                            ),
                        )
                    },
                ).getOrElse { return FunctionResult.Failure(it) }

        return FunctionResult.Success(
            UnwrappedMarkets(
                unwrappedWildcards.first,
                unwrappedWildcards.second,
                statisticsHash,
            ),
        )
    }

    suspend fun parseAndCheckMarketsForStreaming(
        apiKey: String,
        markets: List<ParsedMarket>,
        patternRequested: Boolean,
    ): FunctionResult<ApiError, MarketsParsingResult> =
        coroutineScope {
            val parseDeferred = async { parseMarketsForStreaming(apiKey, markets, patternRequested) }
            val accessDeferred = async { checkAccessToMDF(apiKey, amsService) }
            parseDeferred.await().flatMap { parseResult ->
                accessDeferred.await().map { parseResult }
            }
        }

    fun resolveDefiMarket(
        exchangeId: Int,
        poolId: Int,
        baseId: Int?,
        quoteId: Int?,
    ): FunctionResult<String, String> {
        val exchange =
            Resources.getExchangeById(exchangeId).getOrElse { errorMessage ->
                log.warn(errorMessage)
                return FunctionResult.Failure("Exchange by ID=$exchangeId not found.")
            }
        val base = baseId?.let { Resources.getCurrencyTickerById(it) }
        val quote = quoteId?.let { Resources.getCurrencyTickerById(it) }
        return if (base != null && quote != null) {
            ParsedMarket.ParsedDefiMarket
                .parse(exchange.name, poolId.toString(), base, quote)
                .toString()
                .toSuccess()
        } else {
            val exchangeName = exchange.getNormalizedName()
            monitorNonResolvedMarket(
                exchangeName = exchangeName,
                baseId = baseId,
                quoteId = quoteId,
                symbol = "",
                "defi",
            )
            FunctionResult.Failure(
                "Failed to resolve Defi market by base/quote (exchange='$exchangeName', baseId='$baseId', quoteId='$quoteId').",
            )
        }
    }

    fun resolve(
        exchangeId: Int,
        baseId: Int?,
        quoteId: Int?,
        symbol: String?,
        marketKind: MarketKind,
    ): FunctionResult<String, String> {
        val exchange =
            Resources.getExchangeById(exchangeId).getOrElse { errorMessage ->
                log.warn(errorMessage)
                return FunctionResult.Failure("Exchange by ID=$exchangeId not found.")
            }
        val exchangeName = exchange.getNormalizedName()
        val result =
            when (marketKind) {
                MarketKind.Spot -> resolveSpotMarketFromKafka(exchange, baseId, quoteId, symbol)
                MarketKind.Future, MarketKind.Option -> {
                    val type = marketKind.name.lowercase()
                    symbol?.let { "$exchangeName-$symbol-$type".toSuccess() }
                        ?: FunctionResult.Failure("Symbol is empty for $type market (exchange='$exchangeName').")
                }
            }
        if (result is FunctionResult.Failure) {
            monitorNonResolvedMarket(
                exchangeName = exchangeName,
                baseId = baseId,
                quoteId = quoteId,
                symbol = symbol,
                type = marketKind.name.lowercase(),
            )
        }
        return result
    }

    private fun monitorNonResolvedMarket(
        exchangeName: String,
        baseId: Int?,
        quoteId: Int?,
        symbol: String?,
        type: String,
    ) {
        val base = baseId?.let { Resources.getCurrencyTickerById(it) }
        val quote = quoteId?.let { Resources.getCurrencyTickerById(it) }
        monitoring.nonResolvedMarkets.labelValues(exchangeName, base ?: "", quote ?: "", symbol ?: "", type).inc()
    }

    fun resolveMarketFromKafkaData(
        exchangeId: Int,
        baseId: Int?,
        quoteId: Int?,
        symbol: String?,
        marketType: String?,
    ): FunctionResult<String, String> {
        val marketKind =
            when (marketType) {
                "spot" -> MarketKind.Spot
                "futures", "future" -> MarketKind.Future
                "option" -> MarketKind.Option
                else -> return FunctionResult.Failure("Unknown market type: $marketType (exchange=$exchangeId).")
            }
        return resolve(exchangeId, baseId, quoteId, symbol, marketKind)
    }

    private fun resolveSpotMarketFromKafka(
        exchange: Resources.Exchange,
        baseId: Int?,
        quoteId: Int?,
        symbol: String?,
    ): FunctionResult<String, String> {
        if (!symbol.isNullOrBlank()) {
            val spotMarket = marketStatisticsService.getSpotMarket(exchange.id, symbol)
            if (spotMarket != null) {
                return FunctionResult.Success(spotMarket.toString())
            }
        }

        val base = baseId?.let { Resources.getCurrencyTickerById(it) }
        val quote = quoteId?.let { Resources.getCurrencyTickerById(it) }
        return if (base != null && quote != null) {
            CommonUtils.createSpotMarket(exchange.getNormalizedName(), base, quote).toSuccess()
        } else {
            val exchangeName = exchange.getNormalizedName()
            FunctionResult.Failure(
                "Failed to resolve market by either base/quote or symbol " +
                    "(exchange='$exchangeName', baseId='$baseId', quoteId='$quoteId', symbol='$symbol').",
            )
        }
    }

    private suspend fun parseMarketsForStreaming(
        apiKey: String,
        markets: List<ParsedMarket>,
        patternRequested: Boolean,
    ): FunctionResult<ApiError, MarketsParsingResult> =
        when (val checkResult = parseAndCheckMarkets(apiKey, markets, patternRequested)) {
            is FunctionResult.Success -> {
                // If max time is limited in any way we cannot serve data via streaming endpoint (at least at the moment)
                val limitedMarket =
                    checkResult.value.marketConstraints
                        .filter { it.value.maxTime != null }
                        .keys
                        .firstOrNull()
                if (limitedMarket != null) {
                    FunctionResult.Failure(
                        ApiError.BadParameter(
                            "markets",
                            "Not enough permissions to access streaming data for market '$limitedMarket'.",
                        ),
                    )
                } else {
                    FunctionResult.Success(checkResult.value)
                }
            }

            is FunctionResult.Failure -> {
                FunctionResult.Failure(checkResult.value)
            }
        }

    private fun validateMarket(parsedMarket: ParsedMarket): FunctionResult<ApiError, ValidatedMarket> {
        val market = parsedMarket.toString()
        if (!marketStatisticsService.isMarketExists(market)) {
            return FunctionResult.Failure(
                ApiError.BadParameter(
                    "markets",
                    "Market '$market' is not supported.",
                ),
            )
        }

        val normalizedMarket =
            MarketUtils.normalizeParsedMarket(parsedMarket, marketStatisticsService) ?: return FunctionResult.Failure(
                ApiError.BadParameter(
                    "markets",
                    "Market '$market' is not supported.",
                ),
            )
        return FunctionResult.Success(ValidatedMarket(parsedMarket, normalizedMarket))
    }

    private suspend fun checkAccessToMDF(
        apiKey: String,
        amsService: AmsService,
    ): FunctionResult<ApiError, Boolean> =
        when (
            val result =
                amsService.check(
                    apiKey = apiKey,
                    resource = "market_data_feed",
                )
        ) {
            is FunctionResult.Failure -> FunctionResult.Failure(result.value.first)
            is FunctionResult.Success -> {
                if (result.value is CheckResult.Success) {
                    FunctionResult.Success(true)
                } else {
                    throw IllegalStateException()
                }
            }
        }

    private class ValidatedMarket(
        val parsedMarket: ParsedMarket,
        val normalizedMarket: NormalizedMarket,
    )
}

data class MarketConstraints(
    val marketId: NormalizedMarket,
    val minTime: Instant?,
    val maxTime: Instant?,
    val parsedMarket: ParsedMarket,
    val normalizedMarket: NormalizedMarket,
)

data class MarketsParsingResult(
    val marketConstraints: Map<String, MarketConstraints>,
    val patternRequested: Boolean,
)

fun Map<String, MarketConstraints>.forMarket(marketId: String): MarketConstraints =
    this[marketId] ?: error("Cannot get constraints for market $marketId. It is not specified in the client request.")
