package io.coinmetrics.api.service

import io.coinmetrics.api.CommonConstants
import io.coinmetrics.api.model.NormalizedMarket
import io.coinmetrics.api.model.ParsedMarket
import io.coinmetrics.api.model.S3BooksMarketType
import io.coinmetrics.api.statistics.market.FutureMetadataStatistics
import io.coinmetrics.api.statistics.market.MarketCandlesStatistics
import io.coinmetrics.api.statistics.market.MarketColdBooksStatistics
import io.coinmetrics.api.statistics.market.MarketDefiStatistics
import io.coinmetrics.api.statistics.market.MarketFundingRatesPredictedStatistics
import io.coinmetrics.api.statistics.market.MarketFundingRatesStatistics
import io.coinmetrics.api.statistics.market.MarketFutureTickerStatistics
import io.coinmetrics.api.statistics.market.MarketHotBooksStatistics
import io.coinmetrics.api.statistics.market.MarketLiquidationsStatistics
import io.coinmetrics.api.statistics.market.MarketMetricsStatistics
import io.coinmetrics.api.statistics.market.MarketOpenInterestStatistics
import io.coinmetrics.api.statistics.market.MarketOptionTickerStatistics
import io.coinmetrics.api.statistics.market.MarketStatistics
import io.coinmetrics.api.statistics.market.MarketTradesStatistics
import io.coinmetrics.api.statistics.market.MetricStatistics
import io.coinmetrics.api.statistics.market.OptionMetadataStatistics
import io.coinmetrics.api.statistics.market.SpotMetadataStatistics
import io.coinmetrics.api.statistics.market.SupportedMarketStatistics.Companion.getSupportedMarkets
import io.coinmetrics.api.utils.MarketUtils
import io.coinmetrics.api.utils.fromHex
import io.coinmetrics.api.utils.toHex
import io.coinmetrics.defi.client.DeFiRawDataParser
import io.coinmetrics.defi.client.model.MDMarketData
import java.math.BigInteger

class MarketStatisticsService(
    private val marketHotBooksStatistics: MarketHotBooksStatistics,
    private val marketColdBooksStatistics: MarketColdBooksStatistics,
    private val marketCandlesStatistics: MarketCandlesStatistics,
    private val marketDefiStatistics: MarketDefiStatistics,
    private val marketFutureTickerStatistics: MarketFutureTickerStatistics,
    private val marketFundingRatesStatistics: MarketFundingRatesStatistics,
    private val marketFundingRatesPredictedStatistics: MarketFundingRatesPredictedStatistics,
    private val marketLiquidationsStatistics: MarketLiquidationsStatistics,
    private val marketMetricsStatistics: MarketMetricsStatistics,
    private val marketTradesStatistics: MarketTradesStatistics,
    private val marketOpenInterestStatistics: MarketOpenInterestStatistics,
    private val marketOptionTickerStatistics: MarketOptionTickerStatistics,
    private val spotMetadataStatistics: SpotMetadataStatistics,
    private val futureMetadataStatistics: FutureMetadataStatistics,
    private val optionMetadataStatistics: OptionMetadataStatistics,
    private val deFiRawDataParser: DeFiRawDataParser,
) {
    private val partialStats =
        listOf(
            marketHotBooksStatistics,
            marketColdBooksStatistics,
            marketCandlesStatistics,
            marketDefiStatistics,
            marketFutureTickerStatistics,
            marketFundingRatesStatistics,
            marketFundingRatesPredictedStatistics,
            marketLiquidationsStatistics,
            marketMetricsStatistics,
            marketTradesStatistics,
            marketOpenInterestStatistics,
            marketOptionTickerStatistics,
        )

    val supportedMarketsHash: Int
        get() = partialStats.map { it.supportedMarketsHash }.hashCode()

    fun getTradesStatistics(market: ParsedMarket): MarketStatistics.Statistics? {
        if (market is ParsedMarket.ParsedDefiMarket) {
            return marketDefiStatistics.marketToStatistics[market.toString()]
        }
        return marketTradesStatistics.marketToStatistics[market.toString()]
    }

    fun getTieredS3BooksStatisticsForCatalogV1(market: ParsedMarket): MarketStatistics.Statistics? =
        marketColdBooksStatistics.tieredS3BooksStatisticsPerMarketMapForCatalogV1[0]?.get(market.toString())

    fun getTieredS3BooksStatisticsForCatalogV2(market: ParsedMarket): MarketStatistics.Statistics? =
        marketColdBooksStatistics.tieredS3BooksStatisticsPerMarketMap[0]?.get(market.toString())

    fun getTieredS3BooksStatisticsForCatalogV1(): Map<Int, Map<String, MarketStatistics.Statistics>> =
        marketColdBooksStatistics.tieredS3BooksStatisticsPerMarketMapForCatalogV1

    fun getTieredS3BooksStatisticsForCatalogV2(): Map<Int, Map<String, MarketStatistics.Statistics>> =
        marketColdBooksStatistics.tieredS3BooksStatisticsPerMarketMap

    fun getPostgresBooksStatistics(dataType: S3BooksMarketType): MarketStatistics.Statistics? =
        marketHotBooksStatistics.booksStatisticsPerDataType[dataType]

    fun getS3BooksStatistics(dataType: S3BooksMarketType): MarketStatistics.Statistics? =
        marketColdBooksStatistics.s3BooksStatisticsPerDataType[dataType]

    fun getCandlesStatistics(market: ParsedMarket): Map<String, MarketStatistics.Statistics> =
        CommonConstants.candleFrequenciesMap.keys
            .asSequence()
            .mapNotNull { key -> marketCandlesStatistics.frequencyToMarketToStatistics[key]?.get(market.toString())?.let { key to it } }
            .associate { it }

    fun getTieredS3QuotesStatisticsForCatalogV1(market: ParsedMarket): MarketStatistics.Statistics? =
        marketColdBooksStatistics.tieredS3BooksStatisticsPerMarketMapForCatalogV1[0]?.get(market.toString())

    fun getTieredS3QuotesStatisticsForCatalogV2(market: ParsedMarket): MarketStatistics.Statistics? =
        marketColdBooksStatistics.tieredS3BooksStatisticsPerMarketMap[0]?.get(market.toString())

    fun getFundingRatesStatistics(market: ParsedMarket): MarketStatistics.Statistics? =
        marketFundingRatesStatistics.marketToStatistics[market.toString()]

    fun getOptionTickerStatistics(market: ParsedMarket): MarketStatistics.Statistics? =
        marketOptionTickerStatistics.marketToStatistics[market.toString()]

    fun getFutureTickerStatistics(market: ParsedMarket): MarketStatistics.Statistics? = getFutureTickerStatistics(market.toString())

    fun getFutureTickerStatistics(market: String): MarketStatistics.Statistics? = marketFutureTickerStatistics.marketToStatistics[market]

    fun getFundingRatesPredictedStatistics(market: ParsedMarket): MarketStatistics.Statistics? =
        getFundingRatesPredictedStatistics(market.toString())

    fun getFundingRatesPredictedStatistics(market: String): MarketStatistics.Statistics? =
        marketFundingRatesPredictedStatistics.marketToStatistics[market]

    fun getOpenInterestStatistics(market: ParsedMarket): MarketStatistics.Statistics? =
        marketOpenInterestStatistics.marketToStatistics[market.toString()]

    fun getLiquidationsStatistics(market: ParsedMarket): MarketStatistics.Statistics? =
        marketLiquidationsStatistics.marketToStatistics[market.toString()]

    fun getMarketMetrics(market: String): Map<String, List<MetricStatistics>>? =
        marketMetricsStatistics.marketToMetricToMetricStatistics[market]

    fun getAllMarketMetrics(): Set<String> = marketMetricsStatistics.marketMetrics

    fun isMarketExists(market: String): Boolean = partialStats.any { it.isMarketSupported(market) }

    fun getExchangeStatistics(exchange: String): MarketStatistics.ExchangeStatistics? =
        marketTradesStatistics.exchangeToStatistics[exchange] ?: marketDefiStatistics.exchangeToStatistics[exchange]

    fun isExchangeSupported(exchange: String) = partialStats.any { it.isExchangeSupported(exchange) }

    fun isSymbolSupported(symbol: String) = partialStats.any { it.isSymbolSupported(symbol) }

    fun isBaseSupported(base: String) = partialStats.any { it.isBaseSupported(base) }

    fun isQuoteSupported(quote: String) = partialStats.any { it.isQuoteSupported(quote) }

    fun isAssetSupported(asset: String): Boolean = isBaseSupported(asset) || isQuoteSupported(asset)

    /**
     * Ordered by key in ascending order (`reverseOrder=false) or descending order (`reverseOrder=true`).
     */
    fun getSupportedMarkets(reverseOrder: Boolean) = partialStats.getSupportedMarkets(reverseOrder)

    fun getSpotSymbol(market: String): String? = spotMetadataStatistics.get(market)?.symbol

    fun getSpotMarket(
        exchangeId: Int,
        symbol: String,
    ): ParsedMarket.ParsedSpotMarket? =
        spotMetadataStatistics.getMarket(
            exchangeId,
            symbol,
        )

    fun getSpotMetadata(market: String) = spotMetadataStatistics.get(market)

    fun getFuturesMetadata(market: String) = futureMetadataStatistics.get(market)

    fun getOptionsMetadata(market: String) = optionMetadataStatistics.get(market)

    fun getDefiMetadata(market: ParsedMarket.ParsedDefiMarket): MarketStatistics.DefiMetadata? {
        val normalizedParsedMarketId = MarketUtils.normalizeParsedMarket(market) as NormalizedMarket.DefiNormalizedMarket? ?: return null
        val rawMarket =
            deFiRawDataParser.convertMDMarketToRawMarket(
                mdMarket =
                    MDMarketData(
                        poolMarketId = market.toString(),
                        baseId = normalizedParsedMarketId.base,
                        quoteId = normalizedParsedMarketId.quote,
                    ),
            ) ?: return null

        val defiFee = marketDefiStatistics.marketToDefiFee[market.toString()]

        return MarketStatistics.DefiMetadata(
            type = "spot",
            exchange = market.exchange,
            base = market.base,
            quote = market.quote,
            symbol = getSpotSymbol(market.toString()),
            fee = defiFee?.fee,
            priceIncludesFee = defiFee?.priceIncludesFee,
            variableFee = defiFee?.variableFee,
            contractAddress = marketDefiStatistics.marketToDefiContractAddress[market.toString()],
            baseAddress = resolveAddress(rawMarket.baseHandle),
            quoteAddress = resolveAddress(rawMarket.quoteHandle),
            poolId = normalizedParsedMarketId.poolId?.let { marketDefiStatistics.marketToDefiPoolAddress[market.toString()]?.fromHex() },
        )
    }

    private fun resolveAddress(handle: ByteArray): String? =
        BigInteger(1, handle).let {
            if (it <= BigInteger("FFFF", 16)) {
                null
            } else {
                handle.toHex()
            }
        }
}
