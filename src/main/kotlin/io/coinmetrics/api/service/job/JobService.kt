package io.coinmetrics.api.service.job

import com.fasterxml.jackson.databind.ObjectMapper
import io.coinmetrics.api.ApiError
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.endpoints.PutBlockchainJobAccountBalancesRequest
import io.coinmetrics.api.endpoints.PutBlockchainJobBalanceUpdatesRequest
import io.coinmetrics.api.endpoints.PutBlockchainJobTransactionsRequest
import io.coinmetrics.api.models.CreatedJobDetailsResponse
import io.coinmetrics.api.models.JobDetails
import io.coinmetrics.api.models.JobResult
import io.coinmetrics.api.models.JobStatus
import io.coinmetrics.api.toFailure
import io.coinmetrics.api.toSuccess
import io.coinmetrics.api.utils.TimeUtils
import io.coinmetrics.api.utils.toErrorObject
import io.coinmetrics.jobs.networkdata.ApiNdJobs
import io.coinmetrics.jobs.networkdata.BlockchainAccountBalancesRequest
import io.coinmetrics.jobs.networkdata.BlockchainAccountBalancesWorkflow
import io.coinmetrics.jobs.networkdata.BlockchainBalanceUpdatesRequest
import io.coinmetrics.jobs.networkdata.BlockchainBalanceUpdatesWorkflow
import io.coinmetrics.jobs.networkdata.BlockchainTransactionsRequest
import io.coinmetrics.jobs.networkdata.BlockchainTransactionsWorkflow
import io.temporal.api.enums.v1.WorkflowExecutionStatus
import org.jetbrains.annotations.VisibleForTesting
import java.time.Duration
import java.time.Instant
import java.util.Base64
import java.util.UUID

class JobService(
    private val objectMapper: ObjectMapper,
    private val temporalClients: TemporalClients,
    private val expirationPeriod: Duration,
) {
    suspend fun findJob(
        jobId: String,
        now: Instant,
    ): FunctionResult<ApiError, JobDetails?> {
        val workflowExecutionInfo =
            decodeJobId(jobId).getOrElse {
                return it.toFailure()
            }
        val executionDetails =
            temporalClients.getWorkflowExecutionDetails(workflowExecutionInfo)
                ?: return null.toSuccess()
        val completionTime = executionDetails.closeTime
        val expirationTime = completionTime?.plus(expirationPeriod)
        val maybeStatus = executionDetails.status.toJobStatus()
        val status =
            if (maybeStatus == JobStatus.COMPLETED && expirationTime != null && expirationTime <= now) {
                JobStatus.EXPIRED
            } else {
                maybeStatus
            }
        return JobDetails(
            id = jobId,
            status = status,
            creationTime = TimeUtils.format(executionDetails.startTime),
            completionTime =
                when (status) {
                    JobStatus.COMPLETED,
                    JobStatus.FAILED,
                    JobStatus.EXPIRED,
                    -> TimeUtils.format(completionTime ?: error("INVARIANT"))

                    JobStatus.RUNNING,
                    -> null
                },
            expirationTime =
                when (status) {
                    JobStatus.COMPLETED,
                    JobStatus.EXPIRED,
                    -> TimeUtils.format(expirationTime ?: error("INVARIANT"))

                    JobStatus.RUNNING,
                    JobStatus.FAILED,
                    -> null
                },
            results =
                if (status == JobStatus.COMPLETED) {
                    val links = executionDetails.result?.links ?: error("INVARIANT")
                    links.map { JobResult(it) }
                } else {
                    null
                },
            error =
                if (status == JobStatus.FAILED) {
                    ApiError.OperationFailed.toErrorObject()
                } else {
                    null
                },
        ).toSuccess()
    }

    suspend fun submitBlockchainAccountBalancesJob(request: PutBlockchainJobAccountBalancesRequest) =
        executeNewOrReturnRunning(
            request.httpRequest.origin,
            request.apiKey,
            BlockchainAccountBalancesWorkflow::class.java,
            workflowParam =
                BlockchainAccountBalancesRequest(
                    assets = request.assets.sorted(),
                    accounts = request.accounts.sorted(),
                    atTime = request.atTime,
                    atSlot = request.atSlot,
                    atHeight = request.atHeight,
                ),
            workflowMethod = BlockchainAccountBalancesWorkflow::getBlockchainAccountBalances,
        )

    suspend fun submitBlockchainBalanceUpdatesJob(request: PutBlockchainJobBalanceUpdatesRequest) =
        executeNewOrReturnRunning(
            request.httpRequest.origin,
            request.apiKey,
            BlockchainBalanceUpdatesWorkflow::class.java,
            workflowParam =
                BlockchainBalanceUpdatesRequest(
                    assets = request.assets.sorted(),
                    accounts = request.accounts.sorted(),
                    startTime = request.startTime,
                    endTime = request.endTime,
                    startHeight = request.startHeight,
                    endHeight = request.endHeight,
                    startSlot = request.startSlot,
                    endSlot = request.endSlot,
                ),
            workflowMethod = BlockchainBalanceUpdatesWorkflow::getBlockchainBalanceUpdates,
        )

    suspend fun submitBlockchainTransactionsJob(request: PutBlockchainJobTransactionsRequest) =
        executeNewOrReturnRunning(
            request.httpRequest.origin,
            request.apiKey,
            BlockchainTransactionsWorkflow::class.java,
            workflowParam =
                BlockchainTransactionsRequest(
                    assets = request.assets.sorted(),
                    txids = request.txids.sorted(),
                ),
            workflowMethod = BlockchainTransactionsWorkflow::getBlockchainTransactions,
        )

    suspend fun terminateJob(jobId: String) {
        val (workflowId, runId) = decodeJobId(jobId).getOrElse { error(it) }
        temporalClients.terminateWorkflowExecution(WorkflowExecutionInfo(workflowId, runId))
    }

    @VisibleForTesting
    internal suspend fun <W, P : Any> executeNewOrReturnRunning(
        origin: String,
        apiKey: String,
        workflowType: Class<W>,
        workflowParam: P,
        workflowMethod: (W, P) -> Unit,
        workflowId: String? = null,
    ) = temporalClients
        .executeNewOrReturnRunning(
            ApiNdJobs.getTaskQueue(apiKey),
            workflowId ?: generateWorkflowId(apiKey, workflowParam),
            workflowType,
            workflowParam,
            workflowMethod,
        ).toCreatedJobDetailsResponse(origin, apiKey)

    private fun generateWorkflowId(
        apiKey: String,
        request: Any,
    ): String {
        val operation = request.javaClass.simpleName
        val workflowIdPayload = WorkflowIdPayload(apiKey, operation, request)
        return UUID.nameUUIDFromBytes(objectMapper.writeValueAsBytes(workflowIdPayload)).toString()
    }

    private fun FunctionResult<ApiError, WorkflowExecutionInfo>.toCreatedJobDetailsResponse(
        origin: String,
        apiKey: String,
    ): FunctionResult<ApiError, CreatedJobDetailsResponse> =
        this.map {
            val jobId = encodeJobId(it.workflowId, it.runId)
            CreatedJobDetailsResponse(
                jobId = jobId,
                jobUrl = getJobUrl(origin = origin, jobId = jobId, apiKey = apiKey),
            )
        }

    private fun getJobUrl(
        origin: String,
        jobId: String,
        apiKey: String,
    ) = "$origin/v4/jobs?ids=$jobId&api_key=$apiKey"

    suspend fun close() {
        temporalClients.shutdown()
    }
}

private data class WorkflowIdPayload(
    val apiKey: String,
    val operation: String,
    val request: Any,
)

private fun WorkflowExecutionStatus.toJobStatus(): JobStatus =
    when (this) {
        WorkflowExecutionStatus.WORKFLOW_EXECUTION_STATUS_RUNNING -> JobStatus.RUNNING
        WorkflowExecutionStatus.WORKFLOW_EXECUTION_STATUS_COMPLETED -> JobStatus.COMPLETED
        WorkflowExecutionStatus.WORKFLOW_EXECUTION_STATUS_FAILED,
        WorkflowExecutionStatus.WORKFLOW_EXECUTION_STATUS_UNSPECIFIED,
        WorkflowExecutionStatus.WORKFLOW_EXECUTION_STATUS_CANCELED,
        WorkflowExecutionStatus.WORKFLOW_EXECUTION_STATUS_TERMINATED,
        WorkflowExecutionStatus.WORKFLOW_EXECUTION_STATUS_CONTINUED_AS_NEW,
        WorkflowExecutionStatus.WORKFLOW_EXECUTION_STATUS_TIMED_OUT,
        WorkflowExecutionStatus.UNRECOGNIZED,
        -> JobStatus.FAILED
    }

fun encodeJobId(
    workflowId: String,
    runId: String,
): String =
    Base64
        .getEncoder()
        .encodeToString("$workflowId:$runId".toByteArray())
        .replace("=", "")

private val INVALID_JOB_ID_ERROR = ApiError.BadParameter(name = "ids", "Invalid job ID.")

private fun decodeJobId(jobId: String): FunctionResult<ApiError, WorkflowExecutionInfo> {
    val workflowIdAndRunId =
        runCatching {
            Base64.getDecoder().decode(jobId)
        }.getOrElse {
            return INVALID_JOB_ID_ERROR.toFailure()
        }.let { String(it) }
    val workflowIdAndRunIdArray =
        workflowIdAndRunId.split(":").takeIf { it.size == 2 }
            ?: return INVALID_JOB_ID_ERROR.toFailure()
    val (workflowId, runId) = workflowIdAndRunIdArray
    return WorkflowExecutionInfo(workflowId, runId).toSuccess()
}
