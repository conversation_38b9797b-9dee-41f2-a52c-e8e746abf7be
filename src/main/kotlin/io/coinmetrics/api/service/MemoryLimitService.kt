package io.coinmetrics.api.service

import io.coinmetrics.api.utils.ReferenceCountedKeyContexts
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import java.util.concurrent.atomic.AtomicLong

/**
 * We keep only a limited number of data in memory and use file uncompressed sizes to control/limit memory usage.
 * The "read" requests exceeding memory limits are queued up per key (API key or IP address).
 *
 * For an API instance, we have a fixed amount of memory (memoryLimit).
 * Each file name includes an uncompressed file size to be used here.
 * Each key (API key/IP) has a separate execution queue.
 * Tasks are executed in a round-robin fashion between keys. So one active key can't block requests coming from other keys.
 *
 * To monitor our limits, the following metrics are used:
 * - api_s3_query_api_key_queue_time_seconds {api_key} - the time spent in an api key execution queue. It grows if a client executes parallel requests or queries many entities (markets).
 * - api_s3_query_memory_queue_time_seconds {api_key} - the time spent in a memory execution queue. It grows if the API instance doesn't have enough memory to execute queries.
 * - api_s3_query_memory_queue_length {api_key}
 * - api_s3_query_memory_usage - how much memory is used by the query engine. It is the most important for scaling metric.
 *
 * @param onApiKeyQueueTime the time (ns) spent in an api key execution queue for an API key.
 * @param onMemoryQueueTime the time (ns) spent in a memory execution queue.
 * @param onMemoryQueueLength if boolean is true, queue length for the API key has increased, otherwise decreased
 * @param onMemoryUsage memory usage (bytes).
 */
class MemoryLimitService(
    private val memoryLimit: Long,
    private val onApiKeyQueueTime: (String, Long) -> Unit = { _, _ -> },
    private val onMemoryQueueTime: (String, Long) -> Unit = { _, _ -> },
    private val onMemoryQueueLength: (String, Boolean) -> Unit = { _, _ -> },
    private val onMemoryUsage: (Long) -> Unit = { _ -> },
) {
    private val remainingBytes = AtomicLong(memoryLimit)
    private val remainingBytesUpdatesSharedFlow =
        MutableSharedFlow<Unit>(extraBufferCapacity = 1, onBufferOverflow = BufferOverflow.DROP_LATEST)

    // Map<Key, KeyContext>
    private val keyContexts = ReferenceCountedKeyContexts<String, Mutex>()

    /**
     * Executes the provided tasks honoring all memory limits and queueing.
     *
     * @param key key for limiting purposes (API key or IP address)
     * @param apiKey API key for monitoring (PRO API key or default Community API key)
     * @param taskMemSizeBytes how much memory a provided task wants to use
     */
    suspend fun <T> execute(
        key: String,
        apiKey: String,
        taskMemSizeBytes: Long,
        task: suspend () -> T,
    ): T {
        keyContexts
            .getOrCreateKeyContextAndIncreaseReferenceCount(key) { _ -> Mutex() }
            .use { context ->
                var startNs = System.nanoTime()
                context.value.withLock {
                    onApiKeyQueueTime.invoke(apiKey, System.nanoTime() - startNs)
                    startNs = System.nanoTime()
                    onMemoryQueueLength.invoke(apiKey, true)
                    try {
                        reserveServerMem(taskMemSizeBytes)
                    } finally {
                        onMemoryQueueTime.invoke(apiKey, System.nanoTime() - startNs)
                        onMemoryQueueLength.invoke(apiKey, false)
                        onMemoryUsage.invoke(memoryUsage())
                    }
                }
            }

        // It is our turn. Memory is allocated and execution is allowed.
        // We don't need to keep the key context anymore because it's needed only for queueing behavior.
        // Once we have allocated task's memory and there is no other pending tasks for the key, we don't need to maintain the Mutex.

        // Now we can execute the task and wait for its completion.
        try {
            return task.invoke()
        } finally {
            releaseServerMem(taskMemSizeBytes)
            onMemoryUsage.invoke(memoryUsage())
        }
    }

    private suspend fun reserveServerMem(memSizeBytes: Long) {
        require(memSizeBytes > 0) { "taskMemSize should be > 0" }
        do {
            val currentValue = remainingBytes.get()
            val newValue = currentValue - memSizeBytes
            if (newValue < 0) {
                // wait for a first emit
                remainingBytesUpdatesSharedFlow.first()
            }
        } while (newValue < 0 || !remainingBytes.compareAndSet(currentValue, newValue))
    }

    private suspend fun releaseServerMem(memSizeBytes: Long) {
        remainingBytes.accumulateAndGet(memSizeBytes) { oldValue, sizeLocal -> oldValue + sizeLocal }
        // notify waiters
        remainingBytesUpdatesSharedFlow.emit(Unit)
    }

    fun memoryUsage(): Long = memoryLimit - remainingBytes.get()
}
