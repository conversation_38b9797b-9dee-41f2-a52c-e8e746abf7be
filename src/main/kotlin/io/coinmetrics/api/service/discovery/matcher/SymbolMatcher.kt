package io.coinmetrics.api.service.discovery.matcher

import io.coinmetrics.api.service.discovery.MarketMatchedBy
import io.coinmetrics.api.service.discovery.metadata.MarketAndMetadata

/**
 * Matcher that matches markets by symbol.
 */
class SymbolMatcher(
    private val symbol: String,
) : MarketMatcher {
    override fun match(marketAndMetaData: MarketAndMetadata<*>): Collection<MarketMatchedBy> =
        if (marketAndMetaData.getSymbol() == symbol) {
            listOf(MarketMatchedBy.BaseAndQuote(marketAndMetaData.getBase(), marketAndMetaData.getQuote()))
        } else {
            emptyList()
        }
}
