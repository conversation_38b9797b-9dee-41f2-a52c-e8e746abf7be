package io.coinmetrics.api.service.discovery

import io.coinmetrics.api.model.TimeRestriction

/**
 * Result of AMS (Access Management Service) check for market types.
 * Contains information about requested and available exchanges for a specific market type.
 */
data class AmsResult(
    val amsMarketType: String,
    val requestedExchanges: Set<String>,
    val amsExchangeToTimeRestrictionMap: Map<String, TimeRestriction>,
)
