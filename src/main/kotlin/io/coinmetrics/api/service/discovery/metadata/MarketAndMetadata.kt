package io.coinmetrics.api.service.discovery.metadata

import io.coinmetrics.api.model.ParsedMarket

/**
 * Base class for market metadata.
 * Provides access to market data and metadata.
 */
abstract class MarketAndMetadata<M : ParsedMarket>(
    open val market: M,
) {
    /**
     * Gets the base asset of the market.
     */
    abstract fun getBase(): String?

    /**
     * Gets the quote asset of the market.
     */
    abstract fun getQuote(): String?

    /**
     * Gets the symbol of the market.
     */
    abstract fun getSymbol(): String?
}
