package io.coinmetrics.api.service

import io.coinmetrics.api.ApiError
import io.coinmetrics.api.FunctionResult
import io.coinmetrics.api.ams.CheckLimitsResult
import io.coinmetrics.api.ams.CheckResult
import io.coinmetrics.api.ams.DiscoveryResult
import io.coinmetrics.api.ams.LoadBalancedAmsClient
import io.coinmetrics.api.ams.StatusResult
import io.coinmetrics.api.monitoring.VisKeyMonitoring
import io.coinmetrics.api.toFailure
import io.coinmetrics.api.toSuccess
import io.coinmetrics.api.utils.Utils
import io.coinmetrics.httpserver.HttpRequest
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.coroutineScope
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import java.time.Clock
import java.time.Duration
import java.time.Instant

class AmsService(
    val amsClient: LoadBalancedAmsClient,
    val monitoring: VisKeyMonitoring,
    private val clock: Clock,
) {
    companion object {
        val log: Logger = LoggerFactory.getLogger(AmsService::class.java)
    }

    suspend inline fun getRateLimitHeaders(
        apiKey: String,
        endpoint: String,
        httpRequest: HttpRequest,
    ): FunctionResult<Pair<ApiError, List<Pair<String, String>>>, List<Pair<String, String>>> =
        when (
            val result =
                amsClient.checkRateLimits(
                    apiKey = apiKey,
                    endpoint = endpoint,
                    ip = httpRequest.clientIp,
                    timeSec = httpRequest.timeSec,
                )
        ) {
            is CheckLimitsResult.Success -> {
                if (result.apiKeyType == "vis" && !httpRequest.headers.containsKey("x-request-time")) {
                    monitoring.visKeyMisusedRequestsTotal.labelValues(apiKey).inc()
                } else if (result.apiKeyType == null && httpRequest.headers.containsKey("x-request-time")) {
                    monitoring.apiKeyMisusedRequestsTotal.labelValues(apiKey).inc()
                }
                FunctionResult.Success(result.headers)
            }

            is CheckLimitsResult.Failed -> {
                FunctionResult.Failure(Utils.failedAmsResultToApiError(result) to result.headers)
            }
        }

    suspend fun status(): FunctionResult<StatusResult.Failed, StatusResult> {
        val result = amsClient.status()
        if (result is StatusResult.Failed) {
            return FunctionResult.Failure(StatusResult.Failed(result.status, result.message))
        }
        return FunctionResult.Success(result)
    }

    suspend inline fun check(
        apiKey: String,
        resource: String? = null,
        parameters: Map<String, String> = emptyMap(),
        time: Long? = null,
        displayParameterNameResolver: (String) -> String? = { null },
    ): FunctionResult<Pair<ApiError, CheckResult.Failed>, CheckResult> {
        val result = amsClient.check(time, apiKey, resource, parameters)
        if (result is CheckResult.Failed) {
            val apiError = Utils.failedAmsResultToApiError(result, displayParameterNameResolver)
            if (apiError is ApiError.OperationFailed) {
                log.error("Failed to check api_key. Cause: {}", result)
            }
            return FunctionResult.Failure(apiError to result)
        }
        return FunctionResult.Success(result)
    }

    suspend fun discovery(
        time: Long? = null,
        apiKey: String? = null,
        resource: String,
        target: String,
        filters: Map<String, String> = emptyMap(),
        ignoreUnsupportedValues: Boolean = false,
        ignoreDiscoveryScript: Boolean = false,
        displayParameterNameResolver: (String) -> String?,
    ): FunctionResult<Pair<ApiError, DiscoveryResult.Failed>, DiscoveryResult.Success> =
        when (
            val result =
                amsClient.discovery(
                    time,
                    apiKey,
                    resource,
                    target,
                    filters,
                    ignoreUnsupportedValues,
                    ignoreDiscoveryScript,
                )
        ) {
            is DiscoveryResult.Success -> FunctionResult.Success(result)
            is DiscoveryResult.Failed -> {
                val apiError = Utils.failedAmsResultToApiError(result, displayParameterNameResolver)
                if (apiError is ApiError.OperationFailed) {
                    log.error("Discovery request failed. Cause: {}", result)
                }
                FunctionResult.Failure(apiError to result)
            }
        }

    suspend fun discovery(
        time: Long? = null,
        apiKey: String? = null,
        resource: String,
        target: String,
        filters: Map<String, String> = emptyMap(),
    ): DiscoveryResult = amsClient.discovery(time, apiKey, resource, target, filters)

    suspend fun getResourcesTimeBoundaries(apiKey: String?): Map<String, Pair<Instant?, Instant?>> =
        when (
            val result =
                discovery(
                    apiKey = apiKey,
                    resource = "time_bound",
                    target = "resource_name,start_boundary,end_boundary",
                    displayParameterNameResolver = { null },
                )
        ) {
            is FunctionResult.Success -> {
                result.value.values
                    .groupBy({ it[0] }, { it[1] to it[2] })
                    .asSequence()
                    .map { (resource, timeBounds) ->
                        resource to
                            getMinMaxTimesForResource(
                                (
                                    timeBounds.map { Duration.parse(it.first) }.minOrNull() to
                                        timeBounds.map { Duration.parse(it.second) }.maxOrNull()
                                ),
                            )
                    }.toMap(HashMap())
            }

            is FunctionResult.Failure -> {
                emptyMap()
            }
        }

    private fun getMinMaxTimesForResource(boundaries: Pair<Duration?, Duration?>): Pair<Instant?, Instant?> =
        boundaries.first?.let {
            if (it.isZero) {
                null
            } else {
                var duration = it
                if (it.isNegative) duration = it.negated()
                clock.instant().minus(duration)
            }
        } to
            boundaries.second?.let {
                if (it.isZero) {
                    null
                } else {
                    var duration = it
                    if (it.isNegative) duration = it.negated()
                    clock.instant().minus(duration)
                }
            }
}

suspend inline fun AmsService.getReferenceDataRateLimitHeaders(
    apiKey: String,
    httpRequest: HttpRequest,
): FunctionResult<Pair<ApiError, List<Pair<String, String>>>, List<Pair<String, String>>> =
    getRateLimitHeaders(apiKey, endpoint = "reference-data", httpRequest)

suspend inline fun AmsService.getCatalogRateLimitHeaders(
    apiKey: String,
    httpRequest: HttpRequest,
): FunctionResult<Pair<ApiError, List<Pair<String, String>>>, List<Pair<String, String>>> =
    getRateLimitHeaders(apiKey, endpoint = "catalog", httpRequest)

suspend inline fun AmsService.getTimeseriesRateLimitHeaders(
    apiKey: String,
    httpRequest: HttpRequest,
): FunctionResult<Pair<ApiError, List<Pair<String, String>>>, List<Pair<String, String>>> =
    getRateLimitHeaders(apiKey, endpoint = "timeseries", httpRequest)

suspend inline fun AmsService.getTaxonomyRateLimitHeaders(
    apiKey: String,
    httpRequest: HttpRequest,
): FunctionResult<Pair<ApiError, List<Pair<String, String>>>, List<Pair<String, String>>> =
    getRateLimitHeaders(apiKey, endpoint = "taxonomy", httpRequest)

suspend inline fun AmsService.getProfileRateLimitHeaders(
    apiKey: String,
    httpRequest: HttpRequest,
): FunctionResult<Pair<ApiError, List<Pair<String, String>>>, List<Pair<String, String>>> =
    getRateLimitHeaders(apiKey, endpoint = "profile", httpRequest)

suspend inline fun AmsService.getBlockchainJobRateLimitHeaders(
    apiKey: String,
    httpRequest: HttpRequest,
): FunctionResult<Pair<ApiError, List<Pair<String, String>>>, List<Pair<String, String>>> =
    getRateLimitHeaders(apiKey, endpoint = "blockchain-job", httpRequest)

suspend inline fun AmsService.getBlockchainMetadataRateLimitHeaders(
    apiKey: String,
    httpRequest: HttpRequest,
): FunctionResult<Pair<ApiError, List<Pair<String, String>>>, List<Pair<String, String>>> =
    getRateLimitHeaders(apiKey, endpoint = "blockchain-metadata", httpRequest)

suspend inline fun AmsService.getWebsocketsRateLimitHeaders(
    apiKey: String,
    httpRequest: HttpRequest,
): FunctionResult<Pair<ApiError, List<Pair<String, String>>>, List<Pair<String, String>>> =
    getRateLimitHeaders(apiKey, endpoint = "websockets", httpRequest)

suspend inline fun AmsService.getSecurityMasterRateLimitHeaders(
    apiKey: String,
    httpRequest: HttpRequest,
): FunctionResult<Pair<ApiError, List<Pair<String, String>>>, List<Pair<String, String>>> =
    getRateLimitHeaders(apiKey, endpoint = "security-master", httpRequest)

suspend inline fun AmsService.getConstituentsRateLimitHeaders(
    apiKey: String,
    httpRequest: HttpRequest,
): FunctionResult<Pair<ApiError, List<Pair<String, String>>>, List<Pair<String, String>>> =
    getRateLimitHeaders(apiKey, endpoint = "constituents", httpRequest)

suspend inline fun AmsService.getJobRateLimitHeaders(
    apiKey: String,
    httpRequest: HttpRequest,
): FunctionResult<Pair<ApiError, List<Pair<String, String>>>, List<Pair<String, String>>> =
    getRateLimitHeaders(apiKey, endpoint = "job", httpRequest)

suspend fun AmsService.checkBlockchainJob(
    apiKey: String,
    assets: List<String>,
): FunctionResult<ApiError, Unit> {
    val results =
        coroutineScope {
            assets
                .map { asset ->
                    async {
                        check(
                            apiKey,
                            resource = "blockchain_job",
                            parameters = mapOf("asset" to asset),
                        ) { "assets" }.mapFailure { (err) ->
                            err
                        }
                    }
                }.awaitAll()
        }
    for (result in results) {
        result.onFailure {
            return it.toFailure()
        }
    }
    return Unit.toSuccess()
}
