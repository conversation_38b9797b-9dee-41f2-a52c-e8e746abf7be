<configuration>
    <variable name="API_PLAIN_LOG_LEVEL" value="${API_PLAIN_LOG_LEVEL:-TRACE}" />
    <variable name="API_JSON_LOG_LEVEL" value="${API_JSON_LOG_LEVEL:-OFF}" />

    <appender name="plain" class="ch.qos.logback.core.ConsoleAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>${API_PLAIN_LOG_LEVEL}</level>
        </filter>
        <encoder>
            <pattern>%d [%thread][%mdc] %-5level %logger{36} - [%kvp] %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="json" class="ch.qos.logback.core.ConsoleAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>${API_JSON_LOG_LEVEL}</level>
        </filter>
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="io.coinmetrics.api.utils.JsonWithPayloadLayout">
                <jsonFormatter class="ch.qos.logback.contrib.jackson.JacksonJsonFormatter"/>
                <appendLineSeparator>true</appendLineSeparator>
                <timestampFormat>yyyy-MM-dd'T'HH:mm:ss.SSS</timestampFormat>
                <includeContextName>false</includeContextName>
            </layout>
        </encoder>
    </appender>

    <logger name="io.coinmetrics.api" level="trace"/>
    <logger name="org.apache.kafka" level="info"/>
    <logger name="org.apache.kafka.clients.NetworkClient" level="info"/>

    <root level="info">
        <appender-ref ref="json"/>
        <appender-ref ref="plain"/>
    </root>
</configuration>
