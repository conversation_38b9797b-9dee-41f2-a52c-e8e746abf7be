<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <title>Missing Page</title>
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <link href="https://fonts.googleapis.com/css?family=Lato:400,700&display=swap" rel="stylesheet">
        <style>
          * {
            padding: 0;
            margin: 0;
            font-weight: normal;
            font-style: normal;
            box-sizing: border-box;
          }

          body {
            font-family: Lato, sans-serif;
            font-size: 18px;
          }

          .content {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100vw;
            height: 100vh;
          }

          main {
            display: flex;
            flex-direction: column-reverse;
            padding: 1rem;
          }

          @media screen and (min-width: 800px) {
            main {
              display: flex;
              flex-direction: row;
              justify-content: center;
              padding-bottom: 4rem;
            }
          }

          .warning {

          }

          .warning-text {
            margin-bottom: 32px;
          }

          @media screen and (min-width: 800px) {
            .warning {
              width: 500px;
            }
          }

          img {
            width: 100%;
          }

          @media screen and (min-width: 800px){
            img {
              width: 256px;
              height: 256px;
              flex-shrink: 0;
              display: block;
            }
          }

          h1 {
            font-size: 2.66rem;
            font-weight: 700;
            margin-top: 1rem;
            text-align: center;
          }

          @media screen and (min-width: 800px) {
            h1 {
              text-align: left;
            }
          }

          .hr {
            width: 320px;
            border-top: 1px solid #292d3e;
            margin: 32px auto;
          }

          @media screen and (min-width: 800px) {
            .hr {
              margin: 32px 0;
            }
          }

          a {
            text-decoration: none;
            border: 1px solid #292d3e;
            border-radius: 2px;
            text-transform: uppercase;
            color: #292d3e;
            font-weight: 700;
            font-size: 16px;
            height: 19px;

            display: inline-flex;
            justify-content: center;
            align-items: center;
            padding: 1.5rem;
            transition: color 0.3s, background 0.3s;
          }

          a:hover {
            background: #292d3e;
            color: white;
          }

          p {
            text-align: center;
          }

          @media screen and (min-width: 800px) {
            p {
              text-align: left;
            }
          }
        </style>
    </head>
    <body>
        <div class="content">
            <main>
                <div class="warning">
                    <div class="warning-content">

                    </div>
                    <h1>404</h1>

                    <div class="hr"></div>

                    <p class="warning-text">
                        Sorry, but the page that you requested doesn't exist.
                    </p>

                    <p>
                        <a href="/">Main page</a>
                    </p>
                </div>

                <img src="../../api/static/404.svg" alt="Blank page">
            </main>
        </div>

        <script>
          (function () {
            'use strict'

            let sameSite = document.referrer.includes(window.location.hostname)
            let a = document.querySelector('a')

            if (sameSite) {
              a.href = document.referrer
              a.innerText = 'Back'
            } else {
              let pathSegments = window.location.pathname.split('/')
              a.href = pathSegments.slice(0, pathSegments.length - 1).join('/')
            }
          })()
        </script>
    </body>
</html>
