FROM node:22.5.1 AS builder

WORKDIR /opt/api-docs

RUN npm i -g @redocly/cli@1.19.0

COPY ../specs/generated/openapi-public-redocly.yaml /opt/api-docs/openapi-public-redocly.yaml

RUN redocly build-docs openapi-public-redocly.yaml \
        --disableGoogleFont --theme.openapi.expandResponses='200,201' \
        --theme.openapi.jsonSampleExpandLevel=all \
        --theme.openapi.showSecuritySchemeType=true
RUN sed '7 i <link href="https://fonts.googleapis.com/css2?family=Lato:wght@700&display=swap" rel="stylesheet">' redoc-static.html > a.html
RUN sed '7 i <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>' a.html > b.html
RUN sed '7 i <link rel="preconnect" href="https://fonts.googleapis.com">' b.html > c.html
RUN sed '7 i <link rel="stylesheet" type="text/css" href="/api/static/cm.css" />' c.html > d.html
RUN sed '7 i <link rel="shortcut icon" type="image/png" href="data:image/png;base64,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" />' d.html > index.html

FROM nginx:1.27.0

COPY --from=builder /opt/api-docs/index.html /etc/nginx/data/docs/api/v4/index.html
COPY docs/static /etc/nginx/data/docs/api/static
COPY docs/conf.d/default.conf /etc/nginx/conf.d/default.conf
COPY specs/generated/openapi-public-redocly.yaml /etc/nginx/data/docs/api/static/openapi.yaml
RUN chmod 755 /etc/nginx/data/docs/api/static/openapi.yaml

EXPOSE 80
