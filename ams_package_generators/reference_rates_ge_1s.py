import json

def package_type():
    return "composite"

def package_script():
    return ["reference_rates_ge_1h"]

def compute_and_append_asset_metrics(dest):
    am = dest
    with open('currency.json') as json_file:
        data = json.load(json_file)
    for currency in data:
        asset = currency['cm_ticker']
        metadata = currency['metadata']
        if metadata["hasRTRR"]:
            am[(asset, "1s")].add("ReferenceRate")
            am[(asset, "1m")].add("ReferenceRate")
            am[(asset, "1s")].add("ReferenceRateUSD")
            am[(asset, "1m")].add("ReferenceRateUSD")
            am[(asset, "1s")].add("ReferenceRateEUR")
            am[(asset, "1m")].add("ReferenceRateEUR")
            am[(asset, "1s")].add("ReferenceRateBTC")
            am[(asset, "1m")].add("ReferenceRateBTC")
            am[(asset, "1s")].add("ReferenceRateETH")
            am[(asset, "1m")].add("ReferenceRateETH")
