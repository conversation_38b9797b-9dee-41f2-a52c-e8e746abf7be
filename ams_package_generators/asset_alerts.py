chain_monitor_data_source_alerts = {
    "btc": ["mempool_vsize_hi", "mempool_count_5m_lo",
            "time_inter_block_hi", "block_count_empty_6b_hi",
            "block_count_by_unknown_miners_6b_hi", "consecutive_empty_blocks_2b_hi",
            "mining_pool_conflict_2b_hi", "persistent_mining_pool_conflict_3b_hi",
            "hashrate_drop_1d_lo", "difficulty_decrease_1b_lo",
            "satoshi_coins_spent_hi", "vintage_coins_spent_hi",
            "1b_deep_reorg_hi", "2b_deep_reorg_hi", "3b_deep_reorg_hi",
            "mempool_size_270mb_hi", "mempool_size_285mb_hi", "mempool_size_297mb_hi", "mempool_size_300mb_hi"],
    "eth": ["1b_deep_reorg_hi", "2b_deep_reorg_hi", "3b_deep_reorg_hi",
            "1b_consecutive_empty_blocks_hi", "2b_consecutive_empty_blocks_hi", "3b_consecutive_empty_blocks_hi",
            "block_tx_count_rsd_100b_lo", "block_tx_count_rsd_100b_hi",
            "block_base_fee_rsd_100b_lo", "block_base_fee_rsd_100b_hi",
            "block_priority_fee_rsd_100b_lo", "block_priority_fee_rsd_100b_hi",
            "1b_slot_missed_hi",
            "block_active_addresses_rsd_100b_lo", "block_active_addresses_rsd_100b_hi",
            "block_fees_rsd_100b_lo", "block_fees_rsd_100b_hi"],
    "ltc": ["mining_pool_conflict_2b_hi", "persistent_mining_pool_conflict_3b_hi",
            "hashrate_drop_1d_lo", "difficulty_decrease_1b_lo",
            "1b_deep_reorg_hi", "2b_deep_reorg_hi", "3b_deep_reorg_hi"],
    "bch": ["mining_pool_conflict_2b_hi", "persistent_mining_pool_conflict_3b_hi",
            "hashrate_drop_1d_lo", "difficulty_decrease_1b_lo",
            "1b_deep_reorg_hi", "2b_deep_reorg_hi", "3b_deep_reorg_hi"],
    "bsv": ["mining_pool_conflict_2b_hi", "persistent_mining_pool_conflict_3b_hi"],
    "doge": ["mining_pool_conflict_2b_hi", "persistent_mining_pool_conflict_3b_hi",
             "1b_deep_reorg_hi", "2b_deep_reorg_hi", "3b_deep_reorg_hi"],
    "zec": ["hashrate_drop_1d_lo", "difficulty_decrease_10b_lo"],
    "dash": ["hashrate_drop_1d_lo", "difficulty_decrease_10b_lo"]
}

eth_smart_contract_alerts_assets = ["usdc", "usdt_eth", "pax", "busd", "aave", "sushi", "usdk", "husd", "wbtc", "renbtc", "xaut", "paxg"]

eth_smart_contract_data_source_alerts = ["admin_key_change_1b_hi", "admin_key_change_inflation_event_120b_hi",
                                         "admin_key_change_inflation_highvol_120b_hi"]

def package_script():
    # chain monitor data source alerts
    asset_alerts_map = dict(chain_monitor_data_source_alerts.items())

    # eth smart contract data source alerts
    for asset in eth_smart_contract_alerts_assets:
        asset_alerts_map[asset] = eth_smart_contract_data_source_alerts

    for asset, alerts in asset_alerts_map.items():
        asset_alerts_map[asset] = sorted(alerts)
    # sort by keys
    asset_alerts_map = dict(sorted(asset_alerts_map.items()))

    asset_alerts_script_lines = []
    for asset, alerts in asset_alerts_map.items():
        asset_alerts_script_lines.append(
            f"{{'asset': '{asset}', 'alert': '{', '.join(alerts)}'}}"
        )
    return asset_alerts_script_lines
