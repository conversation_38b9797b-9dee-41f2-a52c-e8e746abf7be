from dataclasses import dataclass
from typing import List, Dict
from collections import defaultdict
from ams_data_generator import TimeRestrictions
import json

datonomy_indexes = ['CMBIAUE', 'CMBIBSE', 'CMBIBUE', 'CMB<PERSON>FI<PERSON>',
                    'CMBIDEXE', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', 'C<PERSON><PERSON><PERSON>',
                    'C<PERSON><PERSON><PERSON>', 'CMBIM<PERSON><PERSON>', 'CMBIMSE', 'CMBINFTE',
                    'CMBINSE', 'CMBISCE', 'CMBISCPE', 'CMBIVTCE']
ftx_indexes = ["CMBIBTCFTX", "CMBIETHFTX", "CMBISOLFTX", "CMBIDOGEFTX"]
gfox_indexes = ["CMBIBTCR", "C<PERSON>IETHR", "CMB<PERSON>TCRX", "CMBIETHRX"]
restricted_indexes = ["CMBIBTCV", "CMBIETHV"]
excluded_indexes = datonomy_indexes + ftx_indexes + gfox_indexes + restricted_indexes

def get_released_index_data():
    index_frequencies = {}

    with open('indexes.json') as indexes_file:
        for index in json.load(indexes_file):
            if index.get("released", False) and index.get('level_frequencies'):
                index_frequencies[index['name']] = 'null, ' + ', '.join(index.get('level_frequencies'))

    return index_frequencies

def package_script():
    released_indexes = get_released_index_data()
    from ams_data_generator import TimeRestrictions
    time_restrictions = ','.join(TimeRestrictions.get_list_of_time_restrictions())
    indexes = []
    for ticker, frequencies in released_indexes.items():
        indexes.append(
            f"{{'index': '{ticker}', 'frequency': '{frequencies}', 'layer': 'levels,constituents', 'time_restriction': '{time_restrictions}'}}"
        )
    return indexes

def package_discovery_script():
    index_discovery_script = ["script"]
    for restricted_index in restricted_indexes:
        index_discovery_script.append(f"- script {{ 'index': '{restricted_index}', 'frequency', 'layer', 'time_restriction' }}")
    return index_discovery_script

@dataclass
class IndexInfo:
    """
    This data class matches the data schema for the data in "indexes.json"
    """
    name: str = "Undefined"
    base: str = "Undefined"
    full_name: str = "Undefined"
    description: str = "Undefined"
    id: int = 0
    return_type: str = "Undefined"
    type: str = "Undefined"
    return_multipliers: List[Dict] = None
    has_hourly_values_table: bool = False
    has_hourly_constituents: bool = False
    released: bool = False
    level_frequencies: List[str] = None
    extra_daily_frequencies: List[str] = None
    packages: List[str] = None

    @staticmethod
    def create_datonomy_index_package() -> List[str]:
        datonomy_indexes = IndexInfo.get_datonomy_indexes()
        datonomy_frequencys_dict = IndexInfo.create_frequency_dict(datonomy_indexes)
        datonomy_policy = IndexInfo.create_index_policy_from_frequency_dict(datonomy_frequencys_dict)
        return datonomy_policy

    @staticmethod
    def create_fidelity_index_package() -> List[str]:
        all_indexes = IndexInfo.get_full_indexes()
        fidelity_indexes = [index for index in all_indexes if index.name.startswith("FID")]
        fidelity_frequencies_dict = IndexInfo.create_frequency_dict(fidelity_indexes)
        fidelity_policy = IndexInfo.create_index_policy_from_frequency_dict(fidelity_frequencies_dict)
        return fidelity_policy

    @staticmethod
    def create_cmbi_index_package(make_community_package: bool = False) -> List[str]:
        """
        This function creates the cmbi_indexes and cmbi_indexes_community package. The reason there is extra logic in this function
        is so that we can add the "community enforced frequency limits" to AMS. I.e. there will be a longer window for 1d
        frequency compared to 1s. This pattern might prove helpful for other data types.
        :param make_community_package: if true will add community time restrictions
        :return: List of str in AMS policy format
        """
        all_indexes = IndexInfo.get_full_indexes()
        cmbi_indexes = [index for index in all_indexes if index.name.startswith("CMBI")]
        cmbi_frequencies_dict = IndexInfo.create_frequency_dict(cmbi_indexes)

        if make_community_package:
            seconds_dict, hours_dict, days_dict = defaultdict(set), defaultdict(set), defaultdict(set)

            [seconds_dict[",".join([k for k in key.split(",") if 's' in k])].update(values) for key, values in cmbi_frequencies_dict.items() if 's' in key]
            [hours_dict[",".join([k for k in key.split(",") if 'h' in k])].update(values) for key, values in cmbi_frequencies_dict.items() if 'h' in key]
            [days_dict[",".join([k for k in key.split(",") if 'd' in k])].update(values) for key, values in cmbi_frequencies_dict.items() if 'd' in key]

            realtime_levels_policy = IndexInfo.create_index_policy_from_frequency_dict(seconds_dict, time_restriction=TimeRestrictions.BACK_1H_FROM_LATEST, layers=['levels'])
            hourly_levels_policy = IndexInfo.create_index_policy_from_frequency_dict(hours_dict, time_restriction=TimeRestrictions.BACK_1D_FROM_LATEST, layers=['levels'])
            daily_levels_policy = IndexInfo.create_index_policy_from_frequency_dict(days_dict, time_restriction=TimeRestrictions.BACK_30D_FROM_LATEST, layers=['levels'])
            constituents_policy = IndexInfo.create_index_policy_from_frequency_dict(days_dict, time_restriction=TimeRestrictions.BACK_30D_FROM_NOW, layers=['constituents'])
            combined_policy = []
            for i, item in enumerate(realtime_levels_policy + hourly_levels_policy + daily_levels_policy + constituents_policy):
                if i == 0:
                    combined_policy.append(item)
                elif item.startswith("+ "):
                    combined_policy.append(item)
                else:
                    combined_policy.append("+ " + item)
            return combined_policy
        else:
            cmbi_policy = IndexInfo.create_index_policy_from_frequency_dict(cmbi_frequencies_dict, time_restriction=TimeRestrictions.NONE)
            return cmbi_policy

    @staticmethod
    def create_aark_index_package() -> List[str]:
        all_indexes = IndexInfo.get_full_indexes()
        filtered_indexes = [index for index in all_indexes if index.base == "aark"]
        frequency_dict = IndexInfo.create_frequency_dict(filtered_indexes)
        policy = IndexInfo.create_index_policy_from_frequency_dict(frequency_dict)
        return policy

    @staticmethod
    def create_gmci_index_package() -> List[str]:
        all_indexes = IndexInfo.get_full_indexes()
        filtered_indexes = [index for index in all_indexes if index.base == "gmci"]
        frequency_dict = IndexInfo.create_frequency_dict(filtered_indexes)
        policy = IndexInfo.create_index_policy_from_frequency_dict(frequency_dict)
        return policy

    @staticmethod
    def create_fix_index_package() -> List[str]:
        all_indexes = IndexInfo.get_full_indexes()
        filtered_indexes = [index for index in all_indexes if index.name in ["BTCFIX", "ETHFIX"]]
        frequency_dict = IndexInfo.create_frequency_dict(filtered_indexes)
        policy = IndexInfo.create_index_policy_from_frequency_dict(frequency_dict)
        return policy

    @staticmethod
    def create_frequency_dict(indexes: List['IndexInfo']) -> Dict[str, List[str]]:
        frequency_dict = defaultdict(list)
        for index in indexes:
            frequency_string = ",".join(index.level_frequencies)
            frequency_dict[frequency_string].append(index.name)
        return frequency_dict

    @staticmethod
    def create_index_policy_from_frequency_dict(frequency_dict: Dict[str, List[str]], time_restriction: TimeRestrictions = TimeRestrictions.NONE, layers:  List[str] = None) -> List[str]:
        result = []
        for frequencies, indexes in frequency_dict.items():
            layer_str = f"'layer': '{','.join(sorted(layers))}'" if layers else "'layer'"
            new_policy_line = f"indexes {{'index': '{','.join(sorted(indexes))}', 'frequency': 'null, {', '.join(frequencies.split(','))}', {layer_str}, 'time_restriction': '{time_restriction.value}'}}"
            result.append(new_policy_line)
        return result

    @staticmethod
    def get_full_indexes() -> List['IndexInfo']:
        all_public_indexes = []
        with open('indexes.json') as indexes_file:
            for index in json.load(indexes_file):
                if index.get("released", False) and index['name'] not in excluded_indexes:
                    all_public_indexes.append(IndexInfo(**index))
        return all_public_indexes

    @staticmethod
    def get_datonomy_indexes() -> List['IndexInfo']:
        datonomy_index_info_array = []
        with open("indexes.json") as indexes_file:
            for index in json.load(indexes_file):
                if index['name'] in datonomy_indexes:
                    datonomy_index_info_array.append(IndexInfo(**index))
        return datonomy_index_info_array