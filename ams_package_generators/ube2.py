# Atlas packages (UBE2 = Universal Blockchain Explorer version 2)

# Atlas permissions are controlled by 3 packages:
# atlas_v2_released: if enabled, key has access to released, non-labs, non-internal assets
# atlas_v2_labs: if enabled, key has access to labs assets
# atlas_v2_internal: if enabled, key has access to internal assets
# (god mode keys have all 3 enabled)

# These assets are Atlas V2 assets available to customers without reserve
atlas_v2_released_assets = sorted({
    '1inch',
    'aave',
    'ada',
    'ae_eth',
    'aion_eth',
    'alcx',
    'alpha',
    'alusd',
    'ant',
    'ape',
    'api3',
    'audio',
    'audio_eth',
    'avaxc',
    'avaxp',
    'avaxx',
    'axs_eth',
    'badger',
    'bal',
    'band_eth',
    'bat',
    'bch',
    'bnb_eth',
    'bnt',
    'btc',
    'btg',
    'btm_eth',
    'buidl_eth',
    'busd',
    'cbat',
    'cbbtc_base.eth',
    'cbbtc_eth',
    'cbeth',
    'ccomp',
    'cdai',
    'cel',
    'cennz',
    'ceth',
    'chz_eth',
    'comp',
    'cro',
    'crv',
    'crvusd_eth',
    'ctxc',
    'cuni',
    'cusdc',
    'cusdt',
    'cvc',
    'cwbtc',
    'czrx',
    'dai',
    'dai.e_base.eth',
    'dash',
    'dgb',
    'dola.e_base.eth',
    'doge',
    'dpi',
    'drgn',
    'elf',
    'enj',
    'ens',
    'eos_eth',
    'esd',
    'etc',
    'eth',
    'ethos',
    'eurc_eth',
    'eurcv_eth',
    'eurs_eth',
    'fei_eth',
    'frax_eth',
    'ftm_eth',
    'ftt',
    'fun',
    'fxc_eth',
    'gas',
    'gbpt_eth',
    'gho_eth',
    'glm',
    'gno',
    'gnt',
    'grt',
    'grt_eth',
    'gusd',
    'gyen_eth',
    'hbtc',
    'hedg',
    'ht',
    'husd',
    'icn',
    'icx_eth',
    'idrt_eth',
    'inst',
    'kcs',
    'knc',
    'ldo',
    'lend',
    'leo_eth',
    'link',
    'loom',
    'lpt',
    'lrc_eth',
    'ltc',
    'lusd_eth',
    'maid',
    'mana',
    'matic_eth',
    'mco',
    'mkr',
    'mtl_metal',
    'nas_eth',
    'neo',
    'nftx',
    'nxm',
    'ogn',
    'okb',
    'omg',
    'op_op.eth',
    'paid',
    'pax',
    'paxg',
    'pay',
    'perp',
    'pol_eth',
    'poly',
    'powr',
    'ppt',
    'pyusd_eth',
    'qash',
    'qnt',
    'qtum_eth',
    'rad',
    'rad_eth',
    'radar',
    'rai_finance_old_eth',
    'ren',
    'renbtc',
    'rep',
    'rev_eth',
    'rhoc',
    'rook',
    'rsr',
    'sai',
    'salt',
    'sand',
    'shib',
    'shib_eth',
    'slp_eth',
    'snt',
    'snx',
    'spell',
    'srm',
    'srn',
    'stmx',
    'storj',
    'sushi',
    'swrv',
    'toke',
    'toke_eth',
    'trx_eth',
    'tusd_trx',
    'ubt',
    'uma',
    'uni',
    'usdc',
    'usdc_avaxc',
    'usdc_base.eth',
    'usdc.e_op.eth',
    'usdc_eth',
    'usdc_op.eth',
    'usdc_trx',
    'usde_eth',
    'usdk',
    'usdt_avaxc',
    'usdt.e_op.eth',
    'usdt_eth',
    'usdt_omni',
    'usdt_trx',
    'veri',
    'vet_eth',
    'vtc',
    'wbtc',
    'weth',
    'wnxm',
    'wsteth',
    'wsteth.e_base.eth',
    'wsteth.e_op.eth',
    'wtc',
    'xaut',
    'xidr_eth',
    'xrp',
    'xsgd_eth',
    'xsushi',
    'xvg',
    'yfi',
    'zec',
    'zil_eth',
    'zrx',
})

# These assets are Atlas V2 assets available to customers under our labs initiative
atlas_v2_labs_assets = sorted({
    'algo',
    'icp',
})

# These assets are Atlas V2 assets *not* available to customers but only to internal keys
atlas_v2_internal_assets = sorted({
    'bit',
    'cc',
    'crep',
    'cusdcv3',
    'cvx',
    'dar',
    'dgx',
    'eng',
    'eth_sepolia',
    'fdusd_eth',
    'fwb',
    'fxs',
    'gala',
    'gmt_eth',
    'hbot',
    'imx',
    'looks',
    'myc',
    'myth',
    'swise',
    'usdd_eth',
})

atlas_v2_labs_and_internal = set(atlas_v2_labs_assets).intersection(set(atlas_v2_internal_assets))
if len(atlas_v2_labs_and_internal) > 0:
    print(f'{atlas_v2_labs_and_internal} are in both atlas_v2_labs_assets and atlas_v2_internal_assets')
    exit(1)

atlas_v2_labs_and_released = set(atlas_v2_labs_assets).intersection(set(atlas_v2_released_assets))
if len(atlas_v2_labs_and_released) > 0:
    print(f'{atlas_v2_labs_and_released} are in both atlas_v2_labs_assets and atlas_v2_released_assets')
    exit(1)

atlas_v2_internal_and_released = set(atlas_v2_internal_assets).intersection(set(atlas_v2_released_assets))
if len(atlas_v2_internal_and_released) > 0:
    print(f'{atlas_v2_internal_and_released} are in both atlas_v2_internal_assets and atlas_v2_released_assets')
    exit(1)

def package_script():
    return [
        "{'asset': '%s'}" % ','.join(
            sorted(set(atlas_v2_released_assets + atlas_v2_labs_assets + atlas_v2_internal_assets))
        )
    ]
