def package_script():
    chain_monitor_mining_pool_tips_assets = ["btc", "bch", "ltc", "bsv", "doge"]
    chain_monitor_mining_pool_tips_assets.sort()
    return [
        "{'asset': '%s'}" % ','.join(chain_monitor_mining_pool_tips_assets)
    ]

def package_discovery_script():
    # Obtuse "subtract not(desired_set)" logic used because defining 'discovery_script' directly like 'script' returns nothing.
    chain_monitor_mining_pool_tips_catalog_assets = ["btc"]
    return [
        "script - script {'asset': 'not(%s)'}" % ','.join(chain_monitor_mining_pool_tips_catalog_assets)
    ]
