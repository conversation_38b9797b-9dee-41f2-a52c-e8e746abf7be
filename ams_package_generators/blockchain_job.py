from ams_package_generators.ube2 import atlas_v2_internal_assets, atlas_v2_released_assets, atlas_v2_labs_assets

blockchain_job_async_only_internal_assets = ["eth_cl"]
blockchain_job_async_only_released_assets = ["sol"]
blockchain_job_internal_assets = sorted(blockchain_job_async_only_internal_assets + atlas_v2_internal_assets)
blockchain_job_released_assets = sorted(blockchain_job_async_only_released_assets + atlas_v2_released_assets + atlas_v2_labs_assets)
assert len(set(blockchain_job_internal_assets) & set(blockchain_job_released_assets)) == 0
blockchain_job_all_assets = sorted(blockchain_job_internal_assets + blockchain_job_released_assets)

def package_script():
    return [
        "{'asset': '%s'}" % ','.join(blockchain_job_all_assets)
    ]
