import json

def package_script():
    from ams_data_generator import TimeRestrictions
    return [
        f"{{'exchange': '{','.join(get_all_exchanges())}', 'time_restriction': '{TimeRestrictions.NONE.value},:now-10m,:now-8h'}}"
    ]

def get_all_exchanges():
    from ams_package_generators.market_data_base import normalize_exchange_name

    exchanges = []
    with open('exchange.json') as json_file:
        data = json.load(json_file)
    for exchange in data:
        exchange_name = normalize_exchange_name(exchange['name'])
        exchanges.append(exchange_name)
    return sorted(exchanges)
