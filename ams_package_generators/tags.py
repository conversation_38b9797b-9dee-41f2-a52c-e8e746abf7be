tagging_released_tags = ["ADDRESS_IS_CUSTODIAN",
                         "ADDRESS_IS_DEPOSIT_WALLET",
                         "ADDRESS_IS_DEX",
                         "ADDRESS_IS_EXCHANGE",
                         "ADDRESS_IS_GAMBLING",
                         "ADDRESS_IS_HISTORIC",
                         "ADDRESS_IS_MINING",
                         "ADDRESS_IS_MINING_POOL",
                         "ADDRESS_IS_FOUNDATION",
                         "ADDRESS_IS_LOST",
                         "ADDRESS_IS_TEAM",
                         "ADDRESS_IS_CONTRACT",
                         "CONTRACT_IS_ERC1155",
                         "CONTRACT_IS_ERC20",
                         "CONTRACT_IS_ERC721",
                         "ADDRESS_IS_FUND",
                         "ADDRESS_IS_SERVICE_OTHER",
                         "ADDRESS_IS_ETF"
                         ]
tagging_internal_tags = ["ADDRESS_IS_BINANCE",
                         "ADDRESS_IS_BITFINEX",
                         "ADDRESS_IS_TREASURY",
                         "ADDRESS_IS_ICO",
                         "ADDRESS_IS_COLD_WALLET",
                         "ADDRESS_IS_RESTRICTED",
                         "ADDRESS_IS_LIQUIDITY_PROVIDER",
                         "ADDRESS_IS_SWAPPER",
                         "CONTRACT_IS_LIQUIDITY_POOL",
                         "ENTITY_CREATION",
                         "LIQUIDITY_POOL_CONTAINS_TOKEN",
                         "ADDRESS_IS_BORROWER",
                         "ADDRESS_IS_DEPOSITOR",
                         "ADDRESS_IS_LIQUIDATOR",
                         "ADDRESS_IS_LIQUIDATED_USER",
                         "CONTRACT_IS_LENDING_POOL",
                         "DEPOSITOR_OF_LIQUIDITY_PROVIDER",
                         "CONTRACT_IS_RESERVE",
                         "LIQUIDATOR_LIQUIDATED_USER",
                         "LENDING_POOL_CONTAINS_RESERVE",
                         "LOAN_REPAID_BY",
                         "LOAN_TAKEN_BY",
                         "LOCATION_IDENTITY",
                         "LOCATION_PARENT",
                         "ENTITY_IDENTITY",
                         "ENTITY_OWNERSHIP",
                         "ADDRESS_IS_UNCLAIMED"]

def package_script():
    return [
        "{'tag': '%s'}" % ','.join(sorted(set(tagging_released_tags + tagging_internal_tags)))
    ]
