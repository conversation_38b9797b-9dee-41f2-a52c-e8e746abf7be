## AMS packages

### Type

AMS packages can be of the following types:
1. regular
2. composite

Regular packages just specify a script in the AMS format documented [here](https://docs.google.com/document/d/11zWMaZwZ_GdZqpFJh9CaP0HXwjqqJNhEYTmwr7kD09U/edit?tab=t.0#heading=h.f2e43tlwe38o).

Composite packages can also specify a script

## Adding a new package

1. Create a new Python file for it in this folder: `mypackage.py`
2. Modify `__init__.py` to import your new file by adding

```python
from . import mypackage
```

3. Implement `package_type()` if you are creating a composite package. See section above to know which type suits your needs.

```python
def package_type():
    return "composite"
```

4. Depending on the package type, implement:
* for regular packages: `package_script()` and/or `package_discovery_script()`
* for composite packages: `compute_and_append_asset_metrics(dest)` and/or `package_script()`

5. You probably want to add the new package to the `employee` package in `employee.py` *if it's a regular package*.