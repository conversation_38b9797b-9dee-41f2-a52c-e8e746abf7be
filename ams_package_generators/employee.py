def package_type():
    return "composite"

def package_script():
    return [
        "asset_metrics",
        "asset_profiles",
        "atlas_v2_released",
        "atlas_v2_labs",
        "atlas_v2_internal",
        "blockchain_job_all_assets",
        "cme_market_data",
        "defi_market_data",
        "defi_project_metrics",
        "erisx_market_data",
        "dydx_market_data",
        "chain_monitor",
        "chain_monitor_internal",
        "flat_files_market_data",
        "indexes",
        "market_data_feed_package",
        "realtime_asset_metrics",
        "ndp_rt",
        "websocket_principal_prices_bundle",
        "websocket_reference_rates_bundle",
        "security_master",
        "tags",
        "taxonomy_data"
    ]
