syntax = "proto3";

package io.coinmetrics.proto;

message TradeEntry {
  int32 base_id = 1;
  int32 quote_id = 2;
  string id = 3;
  string amount = 4;
  string price = 5;

  enum Direction {
    BUY = 0;
    SELL = 1;
    UNKNOWN = 2;
  }
  Direction buy = 6;

  uint64 exchange_time = 7;         // microsecond UNIX time
  uint64 scraper_receive_time = 8;  // microsecond UNIX time

  string symbol = 9;

  enum MarketTypes {
    SPOT = 0;
    FUTURE = 1;
    OPTION = 2;
  }
  MarketTypes market_type = 10;
  string implied_volatility = 11;
  string mark_price = 12;
  string index_price = 13;
  string last_id = 14;
  string first_id = 15;
  string liquidation = 16;
}

message TradeMessage {
  uint32 exchange_id = 1;
  repeated TradeEntry entries = 2;
}

message BookEntry {
  string amount = 1;
  string price = 2;
  uint32 order_count = 3;  // is zero, if we don't know how many orders constitute given price level
}

message BookMessage {
  uint32 exchange_id = 1;
  uint32 base_id = 2;
  uint32 quote_id = 3;
  uint64 scraper_session_id = 4;
  uint64 scraper_session_prev_sequence_id = 5;
  uint64 scraper_session_sequence_id = 6;
  uint64 scraper_receive_time = 7;        // microsecond UNIX time
  string exchange_sequence_id = 8;		  // change id provided by exchange
  uint64 exchange_time = 9;               // microsecond UNIX time; is zero, if exchange doesn't provide it
  repeated BookEntry book_bids = 10;
  repeated BookEntry book_asks = 11;
  string new_book_hash = 12;              // new hash after applying these changes
  string prev_exchange_sequence_id = 13;  // previous change id for which these new changes apply
  string symbol = 14;

  enum MarketTypes {
    SPOT = 0;
    FUTURE = 1;
    OPTION = 2;
  }
  MarketTypes market_type = 15;
}

message BookMessageV2 {
  uint32 exchange_id = 1;
  int32 base_id = 2;
  int32 quote_id = 3;
  uint64 scraper_session_id = 4;
  uint64 scraper_session_prev_sequence_id = 5;
  uint64 scraper_session_sequence_id = 6;
  uint64 scraper_receive_time = 7;        // microsecond UNIX time
  string exchange_sequence_id = 8;		  // change id provided by exchange
  uint64 exchange_time = 9;               // microsecond UNIX time; is zero, if exchange doesn't provide it
  repeated BookEntry book_bids = 10;
  repeated BookEntry book_asks = 11;
  string new_book_hash = 12;              // new hash after applying these changes
  string prev_exchange_sequence_id = 13;  // previous change id for which these new changes apply
  string symbol = 14;

  enum MarketTypes {
    SPOT = 0;
    FUTURE = 1;
    OPTION = 2;
  }
  MarketTypes market_type = 15;

  // If this is a snapshot produced artificially (in contrast to received from exchange), provides the time this
  // snapshot is produced for.
  // Defined as microsecond UNIX time.
  // Will be zero if either is true:
  // - This is not a snapshot.
  // - This snapshot was received from exchange.
  uint64 snapshot_time = 16;
}

message BookMessagesBatch {
    repeated BookMessage book_messages= 1;
}

message RateEntry {
    string asset = 1;
    uint64 timeMillis = 2;
    double price = 3;
    string quote = 4;
}

message RateMessage {
    repeated RateEntry rates = 1;
}

message FundingRateEntry {
    int32 base_id = 1;
    int32 quote_id = 2;
    string symbol = 3;

    string funding_rate = 4;
    uint64 rate_period = 5;      // microsecond timedelta
    uint64 interval = 6;         // microsecond timedelta
    uint64 exchange_time = 7;    // microsecond UNIX time
}

message OpenInterestEntry {
    int32 base_id = 1;
    int32 quote_id = 2;
    enum MarketTypes {
        SPOT = 0;
        FUTURE = 1;
        OPTION = 2;
    }
    MarketTypes market_type = 3;
    string symbol = 4;

    uint64 exchange_time = 5;    // microsecond UNIX time
    string contract_count = 6;
    string contract_value_usd = 7;
}

message LiquidationEntry {
    int32 base_id = 1;
    int32 quote_id = 2;
    string symbol = 3;

    string liquidation_id = 4;
    string amount = 5;
    string price = 6;

    enum Direction {
        BUY = 0;
        SELL = 1;
        UNKNOWN = 2;
    }
    Direction is_buy = 7;
    bool is_order = 8;
    uint64 exchange_time = 9;    // microsecond UNIX time
}

message Market {
  uint32 exchange_id = 1;
  int32 base_id = 2;
  int32 quote_id = 3;
  string symbol = 4;
  int32 defi_pool_id = 5;
}

message MarketCandle {
  Market market = 1;
  uint64 start_time = 2;  // milliseconds UNIX time
  uint32 interval_minutes = 3; // candle interval in minutes. 1, 5, 10, 15, 30, 60, 240, 1440
  string open_price = 4;
  string close_price = 5;
  string low_price = 6;
  string high_price = 7;
  string volume = 8;
  string vwap = 9;
  uint32 trades_count = 10;
  string usd_volume = 11;
  string status = 12;
  uint32 gap_length = 13;
  uint64 time = 14;  // milliseconds UNIX time. Candle generation time

  enum MarketTypes {
    SPOT = 0;
    FUTURE = 1;
    OPTION = 2;
    DEFI = 3;
  }
  MarketTypes market_type = 15;
}
