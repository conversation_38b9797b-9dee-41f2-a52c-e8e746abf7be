import io.coinmetrics.gradle.BuildCachePlugin
import kotlin.io.path.Path

pluginManagement {
    repositories {
        mavenLocal()
        gradlePluginPortal()
        maven {
            url = uri("https://gitlab.com/api/v4/projects/31574851/packages/maven")
            credentials(HttpHeaderCredentials::class) {
                System.getenv("CI_JOB_TOKEN")?.also { token ->
                    name = "Job-Token"
                    value = token
                } ?: run {
                    val gitlabPersonalAccessToken: String by settings
                    name = "Private-Token"
                    value = gitlabPersonalAccessToken
                }
            }
            authentication {
                create<HttpHeaderAuthentication>("header")
            }
        }
    }
}

plugins {
    id("org.gradle.toolchains.foojay-resolver-convention") version "0.5.0"
    id("io.coinmetrics.gradle.build-cache") version "1.+"
}

rootProject.name = "api4"

include("common")
include("statistics-generator")

val projectsDir = "project"
for (project in rootProject.children) {
    project.projectDir = Path(projectsDir, project.name).toFile()
}

apply<BuildCachePlugin>()
