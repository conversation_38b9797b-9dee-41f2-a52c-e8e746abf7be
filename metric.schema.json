{"type": "object", "additionalProperties": false, "required": ["short_form", "description", "name", "product", "category", "subcategory", "metric_type", "unit", "interval", "interval_rt", "display_name", "depends_on", "data_type", "internal"], "properties": {"short_form": {"type": "string"}, "description": {"type": "string"}, "name": {"type": "string"}, "product": {"type": ["string", "null"]}, "category": {"type": "string"}, "subcategory": {"type": "string"}, "metric_type": {"type": "string"}, "unit": {"type": "string"}, "interval": {"type": "string"}, "interval_rt": {"type": ["string", "null"]}, "frequencies": {"type": "array", "items": {"type": "string"}}, "display_name": {"type": ["string", "null"]}, "depends_on": {"type": "string"}, "data_type": {"type": "string"}, "internal": {"type": "boolean"}, "experimental": {"type": ["boolean", "null"]}, "url_slug_doc": {"type": "string"}}}