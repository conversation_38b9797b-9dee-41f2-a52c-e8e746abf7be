[{"id": 0, "name": "Bitstamp", "rest_api_rate_limit": 1.0, "trusted_spot_start_date": "1970-01-01", "tickers": {"one": "one_harmony", "quick": "quick_new", "smt": "smt_swarmmarkets", "win": "win_wink"}}, {"id": 1, "name": "Coinbase", "rest_api_rate_limit": 10, "trusted_spot_start_date": "1970-01-01", "tickers": {"cgld": "celo", "corechain": "core", "jup": "jup_jupiterproject", "mantle": "mnt", "mona": "mona_monavale", "mtl": "mtl_metal", "neiro": "neirocto", "one": "one_harmony", "ronin": "ron", "velo": "velo_velodromefinance", "win": "win_wink", "zeta": "zeta_eth", "zetachain": "zeta"}}, {"id": 2, "name": "Bitfinex", "rest_api_rate_limit": 0.33, "streaming_api_connection_rate_limit": 0.33, "trusted_spot_start_date": "1970-01-01", "tickers": {"abs": "abyss", "aio": "aion", "aix": "ai", "alg": "algo", "amp": "ampl", "ato": "atom", "b21x": "b21", "bab": "bch", "bchabc": "bcha", "btt": "bttc", "dad": "dadi", "dapp": "dapp_dapptoken", "drn": "drgn", "dsh": "dash", "eus": "eurs", "eut": "eurt", "gsd": "gusd", "gtx": "gt", "hot": "hot_hydroprotocol", "idx": "id_everest", "ios": "iost", "iot": "miota", "loo": "loom", "mit": "mith", "mna": "mana", "nca": "ncash", "nio": "nio_autonio", "omn": "omni_omni", "one": "one_harmony", "pas": "pass_blockpass", "poy": "poly", "qsh": "qash", "qtm": "qtum", "quick": "quick_new", "rbt": "rbt_rimbit", "rcn": "rcn_ripiocreditnetwork", "scr": "scr_scorum", "scroll": "scr", "see": "seer", "sng": "sngls", "sonic": "s", "stj": "storj", "terraust": "ust", "tsd": "tusd", "tsdusd": "tusd", "udc": "usdc", "ust": "usdt", "utn": "utnp", "vsy": "vsys", "wax": "waxp", "wbt": "wbtc", "win": "win_wink", "xch": "xchf", "ygg": "yeed", "yyw": "yoyow", "zbt": "zb", "zkx": "zk"}, "exclude_markets": {"by_base": ["csx"], "by_quote": ["csx"]}}, {"id": 4, "name": "Binance", "rest_api_rate_limit": 4.0, "trusted_spot_start_date": "1970-01-01", "tickers": {"ace": "ace_fusionist", "ai": "ai_sleeplessai", "bchsv": "bsv", "bifi": "bifi_beef", "bqx": "ethos", "combo": "combo_combo", "dodox": "dodo", "hc": "hc_hypercash", "hot": "hot_holo", "iota": "miota", "jam": "jam_geojam", "luna": "luna2", "lunc": "luna", "mtl": "mtl_metal", "neiro": "neirocto", "one": "one_harmony", "ong": "ong_ontologygas", "quick": "quick_new", "raysol": "ray", "rcn": "rcn_ripiocreditnetwork", "ronin": "ron", "tao": "tao_bittensor", "tst": "tst_test", "usds": "usds_stableusd", "ustc": "ust", "vai": "vai_vai", "velodrome": "velo_velodromefinance", "win": "win_wink", "yoyo": "yoyow"}, "exclude_markets": {"by_base": ["bcc", "ven"]}}, {"id": 5, "name": "Gemini", "rest_api_rate_limit": 1.0, "streaming_api_connection_rate_limit": 0.016, "trusted_spot_start_date": "1970-01-01", "tickers": {"jam": "jam_geojam", "one": "one_harmony", "quick": "quick_new", "win": "win_wink"}}, {"id": 6, "name": "<PERSON><PERSON><PERSON>", "rest_api_rate_limit": 0.33, "trusted_spot_start_date": "1970-01-01", "tickers": {"air": "air_altair", "beam": "beamx", "btt": "bttc", "fly": "fly_flytrade", "neiro": "neirocto", "one": "one_harmony", "ong": "ong_ontologygas", "quick": "quick_new", "repv2": "rep", "sky": "sky_sky", "tao": "tao_bittensor", "win": "win_wink", "xetc": "etc", "xeth": "eth", "xicn": "icn", "xltc": "ltc", "xmln": "mln", "xxbt": "btc", "xxdg": "doge", "xxlm": "xlm", "xxmr": "xmr", "xxrp": "xrp", "xzec": "zec", "zaud": "aud", "zcad": "cad", "zeur": "eur", "zgbp": "gbp", "zjpy": "jpy", "zusd": "usd"}}, {"id": 7, "name": "bitF<PERSON><PERSON>", "rest_api_rate_limit": 0.5, "trusted_spot_start_date": "1970-01-01", "tickers": {"elf": "elf_thelandelfcrossing", "one": "one_harmony", "quick": "quick_new", "win": "win_wink"}}, {"id": 8, "name": "ZB.COM", "metrics_disabled": true, "rest_api_rate_limit": 16.0, "tickers": {"bchabc": "bch", "bchsv": "bsv", "ent": "ent_entcash", "gst": "gst_gstcoin", "hc": "hc_hypercash", "lbtc": "lbtc_lightningbitcoin", "luna": "luna2", "lunc": "luna", "mtl": "mtl_metal", "one": "one_harmony", "pdx": "pdx_pdxblockchain", "rcn": "rcn_ripiocreditnetwork", "slt": "slt_sociallendingtoken", "win": "win_wink"}, "exclude_markets": {"by_base": ["xwc", "nxwc"]}}, {"id": 9, "name": "OKEx", "rest_api_rate_limit": 6.0, "tickers": {"ace": "ace_fusionist", "btt": "bttc", "bttold": "btt", "fame": "fame_famemma", "gpt": "gpt_qna3ai", "ice": "ice_icenetwork", "iota": "miota", "luna": "luna2", "lunc": "luna", "neiro": "neirocto", "one": "one_harmony", "quick": "quick_new", "stc": "stc_satoshiisland", "toncoin": "ton", "trac": "trac_tracordinals", "trade": "trade_unitrade", "ustc": "ust", "velodrome": "velo_velodromefinance", "win": "win_wink", "wsb": "wsb_wallstreetbetsdapp"}}, {"id": 10, "name": "<PERSON><PERSON><PERSON>", "rest_api_rate_limit": 10.0, "tickers": {"ace": "ace_fusionist", "ai": "ai_sleeplessai", "baby": "baby_metadbaby", "btt": "bttc", "bttold": "btt", "bull": "bull_bullieverse", "cate": "cate_cate", "caw": "caw_ahuntersdream", "ctxc2x": "ctx_c2x", "cube": "cube_cubenetwork", "cvcoin": "cvn", "dis": "dis_dischain", "drgn": "drgn_<PERSON>un", "dyp": "dyp_new", "eliza": "eliza_eliza<PERSON><PERSON><PERSON><PERSON>", "elizacto": "eliza_eliza<PERSON><PERSON><PERSON>", "fair": "fair_fairgame", "fud": "fud_ftxusersdebt", "game": "game2", "gear": "gear_metagear", "get": "get_themis", "gtc": "gtc_gamecom", "gxc": "gxs", "hc": "hc_hypercash", "hot": "hot_hydroprotocol", "ice": "ice_icenetwork", "iota": "miota", "juice": "juice_juicefinance", "luna": "luna2", "lunc": "luna", "mtl": "mtl_metal", "neiro": "neiro_neirolol", "nim": "nim_nimnetwork", "one": "one_harmony", "open": "open_opencustodyprotocol", "propy": "pro", "quick": "quick_new", "rcn": "rcn_ripiocreditnetwork", "rif": "rifsol", "ronin": "ron", "sns": "sns_synesisone", "stc": "stc_satoshiisland", "tao": "tao_fusotao", "titan": "titan_titanswap", "tronbull": "bull_tronbull", "tst": "tst_test", "ustc": "ust", "win": "win_wink", "xno": "xno_xeno", "zero": "zero_analysoor", "zerolend": "zero"}, "exclude_markets": {"by_base": ["bt1", "bt2", "hb10", "ven", "cdc"]}}, {"id": 11, "name": "HitBTC", "rest_api_rate_limit": 10.0, "tickers": {"ai": "ai_sleeplessai", "ban": "ban_banano", "berry": "berry_rentberry", "bet": "bet_da<PERSON>asino", "bit": "bit_bitrewards", "bits": "bits_bitcoinus", "bqx": "ethos", "btt": "bttc", "bttold": "btt", "cat": "cat_bitclave", "cbc": "cbc_cashbetcoin", "cvcoin": "cvn", "dapp": "dapp_dapptoken", "emgo": "mgo", "ert": "ert_esportsrewardtoken", "get": "get_themis", "gmt": "gmt_gmttoken", "gst": "gst_grearn", "hero": "hero_hero", "hit": "hit_hitbtctoken", "hot": "hot_holo", "iota": "miota", "lnc": "lnc_linkercoin", "luna": "luna2", "lunc": "luna", "mtl": "mtl_metal", "net": "net_nimiqexchangetoken", "ntk": "ntk_neurotoken", "one": "one_harmony", "ong": "ong_ontologygas", "ormeus": "orme", "pass": "pass_blockpass", "pla": "pla_playchip", "quick": "quick_new", "rcn": "rcn_ripiocreditnetwork", "rpm": "rpm_renderpayment", "sbd": "sbd_steemdollars", "scc": "scc_stockchain", "spd": "spd_spindle", "stx": "stx_stox", "tao": "tao_bittensor", "ton": "ton_freeton", "toncoin": "ton", "usdt20": "usdt_eth", "ustc": "ust", "win": "win_wink", "zrc": "zrc_zrcoin"}, "exclude_markets": {"by_base": ["wmgo", "hbt", "pxg", "poa20"]}}, {"id": 12, "name": "LBank", "metrics_disabled": true, "rest_api_rate_limit": 10.0, "tickers": {"ace": "ace_fusionist", "ai": "ai_sleeplessai", "apx": "apx_apextoken", "apx1": "apx", "bitcoin": "hpos10i", "bloc": "bloc_blockcloud", "blt": "blt_bitultra", "blue": "blue_profitblue", "bmx": "bmx_bitminerx", "btt": "bttc", "bttold": "btt", "bull": "bull_memebull", "caw": "caw_ahuntersdream", "chat": "chat_openchat", "crp": "crp_crypton", "ctx": "ctx_c2x", "cult": "cult_miladycultcoin", "dar": "d", "dino": "dino_<PERSON><PERSON>o", "dlc": "dlc_deeplinkprotocol", "dope": "dope_departmentofpropagandaeverywhere", "dreams": "dreams_daydreams", "eliza": "eliza_eliza<PERSON><PERSON><PERSON><PERSON>", "fine": "fine_thisisfine", "fit": "fit_300fitnetwork", "four": "four_4", "free": "free_freerossdao", "ftt": "ftt_facter", "gaia": "gaia_gaia", "game": "game_gamebyvirtuals", "gas": "gas_gasdao", "gft": "gft_gamefantasy", "gm": "gm_gomble", "gmt": "gmt_gmttoken", "gmt1": "gmt", "goatseus": "goat", "grams": "gram", "gst": "gst_gstcoin", "hbd": "hbd_happyballoondog", "hit": "hit_hiver", "hot": "hot_holo", "ice": "ice_icenetwork", "iq": "iq_iq6900", "knight": "knight_darkness", "lf": "lf_logisticfundamental", "lft": "lft_lendflare", "loom": "loom_loomsync", "lucky": "lucky_lb<PERSON><PERSON><PERSON><PERSON>", "luna": "luna2", "lunc": "luna", "lwolf": "wolf_landwolf0x67", "mad": "mad_mad", "mantle": "mnt", "meme": "meme_memetoon", "mpc": "mpc_mypaqmancoin", "mtc": "mtc_docademic", "mts": "mts_metaplustoken", "mx": "mx_marsx", "myth": "myth_mysticlandcoin", "nap": "nap_snap", "neiro": "neirocto", "neon": "neon_neoncoin", "one": "one_harmony", "p00ls": "00", "paw": "paw_pawzone", "pawswap": "paw_paw", "pdex": "pdex_privatedecentralizedexchange", "pmgpomerium": "pmg", "por": "por_portuma", "put": "put_profileutilitytoken", "rai": "rai_reploy", "rbtc1": "rbtc_rabbitcoin", "rifampicin": "rifsol", "safe": "safe_safeanwang", "sbr": "sbr_strategicbitcoinreserve", "sc": "sc_sharkcat", "seal": "seal_seal", "sen": "sen_sentioprotocol", "send": "send_sendcoin", "shr": "shir<PERSON>u", "stars": "stars_cryptoallstars", "step": "step_step", "talk": "talk_cryptalk", "tao": "tao_bittensor", "tgt": "tgt_tinggle", "tokamak": "ton_tokamaknetwork", "troll": "trollsol", "tronbull": "bull_tronbull", "tst": "tst_test", "tt": "tt_treasuretoken", "upc": "upc_unipaycoin", "upcx": "upc", "usdm": "usdm_usdmappedtoken", "ustc": "ust", "velo": "velo_velodromefinance", "win": "win_wink", "wxt": "wxt_weextoken"}, "exclude_markets": {"by_base": ["bch", "artcn", "cap", "dali", "fil6", "fil12", "fil36", "<PERSON><PERSON><PERSON>", "lbcn", "tena", "sead", "ida", "kisc", "mat", "ddmx"], "by_quote": ["bch", "artcn", "cap", "dali", "fil6", "fil12", "fil36", "<PERSON><PERSON><PERSON>", "lbcn", "tena", "sead", "ida", "kisc", "mat", "ddmx"]}}, {"id": 16, "name": "Bibox", "rest_api_rate_limit": 1.0, "tickers": {"apenft(nft)": "nft", "btt": "bttc", "bttold": "btt", "cpc": "cpc_cpchain", "dog": "dog_dogeswap", "egg": "egg_nestree", "gft": "gft_galaxyfinance", "gmt": "gmt_gmttoken", "gpt": "gpt_qna3ai", "hot": "hot_holo", "iota": "miota", "layer": "layer_unilayer", "luna": "luna2", "lunc": "luna", "nft": "nft_nftprotocol", "one": "one_harmony", "ong": "ong_ontologygas", "quick": "quick_new", "stc": "stc_satoshiisland", "ustc": "ust", "vnt": "vnt_vntchain", "win": "win_wink"}, "exclude_markets": {"by_base": ["4aave", "4ape", "4apt", "4atom", "4axs", "4bch", "4bsv", "4btc", "4dash", "4dot", "4eos", "4etc", "4eth", "4fil", "4ksm", "4link", "4ltc", "4<PERSON>na", "4lunc", "4neo", "4ont", "4sol", "4stepn", "4<PERSON><PERSON>", "4trx", "4uni", "4xrp", "5bch", "5btc", "5dot", "5eth", "5fil", "5link", "5ltc", "5uni", "5xrp"]}}, {"id": 19, "name": "<PERSON><PERSON><PERSON>", "rest_api_rate_limit": 10.0, "tickers": {"ace": "ace_fusionist", "agi": "agi_delysium", "beam": "beamx", "bhp": "bhpc", "btt": "bttc", "egg": "egg_nestree", "gxc": "gxs", "hc": "hc_hypercash", "mtl": "mtl_metal", "neiro": "neirocto", "one": "one_harmony", "ong": "ong_ontologygas", "quick": "quick_new", "sky": "sky_sky", "tokamak": "ton_tokamaknetwork", "win": "win_wink", "xno": "xno_xeno", "ztx": "ztx_ztx"}, "exclude_markets": {"by_base": ["add", "black", "chl", "horus"]}}, {"id": 20, "name": "Poloniex", "rest_api_rate_limit": 10.0, "trusted_spot_start_date": "1970-01-01", "tickers": {"ace": "ace_fusionist", "agi": "agi_agility", "ai1": "ai_sleeplessai", "apu": "apu_aputoken", "bchsv": "bsv", "bifi": "bifi_beef", "btt": "bttc", "cate": "cate_cate", "chat": "chat_vectorchatai", "cre": "cre_cresco", "dog": "dog_thedogenft", "drgn": "drgn_<PERSON>un", "egg": "egg_eggdog", "eliza": "eliza_eliza<PERSON><PERSON><PERSON>", "fine": "fine_thisisfine", "fire": "fire_matr1xfire", "fred": "fred_fred", "free": "free_freerossdao", "grok": "grok_grok", "happy": "happy_chappy", "harry": "hpos10i", "icenetwork": "ice_icenetwork", "kai": "kai_komputai", "king": "king_king", "knight": "knight_darkness", "ladys": "ladys_miladycoin", "lava": "lava_vulcanforged", "ll": "ll_liangle", "loot": "loot_lootbot", "luna": "luna2", "lunc": "luna", "lwolf": "wolf_landwolf0x67", "mad": "mad_mad", "moz": "moz_mozaic", "neiro": "neiro_neirolol", "ngl": "ngl_entangle", "nim": "nim_nimnetwork", "one": "one_harmony", "oneinch": "1inch", "ox": "ox_openexchangetoken", "panda": "panda_pandaswap2", "paw": "paw_pawzone", "port": "port_port", "prompt": "prompt_promptbidder", "quick": "quick_new", "rai": "rai_reploy", "repv2": "rep", "sbd": "sbd_steemdollars", "sen": "sen_sentioprotocol", "send": "send_sendcoin", "shib2": "shib2_shib20", "solchat": "chat_solchat", "str": "xlm", "talk": "talk_cryptalk", "titan": "titan_titanswap", "tokamak": "ton_tokamaknetwork", "trac": "trac_tracordinals", "trade": "trade_unitrade", "tronbull": "bull_tronbull", "trump": "trump_maga", "tst": "tst_test", "urosol": "uro", "win": "win_wink", "wolfsol": "wolf", "wsb": "wsb_wsbcoin", "wzrd": "wzrd_bitcoinwizards", "x": "x_aix", "zero": "zero_analysoor"}}, {"id": 21, "name": "Upbit", "rest_api_rate_limit": 1.0, "trusted_spot_start_date": "1970-01-01", "tickers": {"beam": "beamx", "bsd": "bsd_bitsend", "btt": "bttc", "bttold": "btt", "edr": "edr_endorprotocol", "iota": "miota", "mtl": "mtl_metal", "one": "one_harmony", "ong": "ong_ontologygas", "quick": "quick_new", "rcn": "rcn_ripiocreditnetwork", "sbd": "sbd_steemdollars", "sky": "sky_sky", "ton": "ton_tokamaknetwork", "ttc": "ttc_ttcprotocol", "win": "win_wink"}}, {"id": 22, "name": "LocalBitcoins", "metrics_disabled": true, "rest_api_rate_limit": 0.75, "tickers": {"bsd": "bsd_bahamiandollar", "mtl": "mtl_malteselira", "nio": "nio_nicaraguancordoba", "sbd": "sbd_solomonislandsdollar"}, "exclude_markets": {"by_quote": ["btn", "bmd", "bbd", "awg", "ang", "clf", "cnh", "eek", "bzd", "cve", "cup", "ern", "fkp", "fjd", "gyd", "gnf", "imp", "kmf", "nhl", "kpw", "xau", "xag", "xar", "xdr", "xpd", "xpt", "zmk", "zwl", "gip", "lrd", "syp", "vuv", "srd", "std", "ttd", "pen", "sdg", "mro", "tmt", "ssp", "mwk", "kgs", "djf", "svc", "jep", "mop", "szl", "lyd", "mnt", "pgk", "xcd", "ggp", "khr", "npr", "mzn", "sll", "tjs", "mtl", "sos", "xpf", "shp", "bsd", "sbd", "yer", "wst", "mru", "gmd", "mga", "top", "stn", "xof", "mkd", "nio"]}}, {"id": 23, "name": "Mt.Gox"}, {"id": 24, "name": "Gate.io", "rest_api_rate_limit": 10.0, "trusted_spot_start_date": "1970-01-01", "tickers": {"ace": "ace_fusionist", "acent": "ace", "agi": "agi_delysium", "agility": "agi_agility", "ai": "ai_sleeplessai", "air": "air_airian", "ait": "ait_aitprotocol", "andyeth": "andy_eth", "ars": "ars_arris", "art": "art_genifyart", "aura": "aura_auranetwork", "axl": "axl_axlinu", "bchsv": "bsv", "beefi": "bifi_beef", "benqi": "qi", "bft1": "bft_brazilfantoken", "bifif": "bifi_bifr", "block": "block_blockprotocol", "blt": "blt_blocto", "btt": "bttc", "bttold": "btt", "cateeth": "cate_cate", "chat": "chat_solchat", "combo": "combo_combo", "crt": "crt_carrot", "ctx": "ctx_c2x", "deeplink": "dlc_deeplinkprotocol", "defiland": "dfl", "dfl": "dfl_defil", "dino": "dino_dinolfg", "dognft": "dog_thedogenft", "dope": "dope_dopamine", "dyp": "dyp_new", "eliza": "eliza_eliza<PERSON><PERSON><PERSON>", "elizasol": "eliza_eliza<PERSON><PERSON><PERSON><PERSON>", "elt": "elt_edenloop", "emc": "emc_edgematrixchain", "ethf": "dis_dischain", "fame": "fame_fantommaker", "fly": "fly_flytrade", "form1": "form_form", "four": "four_4", "fuel": "fuel_fuelnetwork", "furucombo": "combo", "gamevirtual": "game_gamebyvirtuals", "gls": "gls_glaciernetwork", "gm": "gm_gomble", "gngl": "ngl_entangle", "gold": "gold_cyberdragongold", "gpt": "gpt_qna3ai", "gst": "gst_bsc", "gtc": "gtc_gamecom", "hc": "hc_hypercash", "hot": "hot_holo", "ice": "ice_icenetwork", "iota": "miota", "jam": "jam_geojam", "juice": "juice_juicefinance", "king": "king_kingdomverse", "knight": "knight_darkness", "landwolf": "wolf", "lava": "lava_vulcanforged", "luna": "luna2", "lunc": "luna", "mad": "mad_mad", "mai": "mai_mai", "miladycult": "cult_miladycultcoin", "mtl": "mtl_metal", "mts": "mts_metastrike", "neiro": "neiro_neirolol", "one": "one_harmony", "ong": "ong_ontologygas", "p00ls": "00", "palm": "palm_palmai", "paw": "paw_paw", "pilotiq": "iq_iq6900", "pix": "pix_pixelswap", "ptc": "ptc_particle", "qbt": "qbt_qbao", "qi": "qi_qiswap", "quick": "quick_new", "rbtc": "rbtc_rabbitcoin", "rcn": "rcn_ripiocreditnetwork", "real": "real_realrealm", "sai": "sai_sharpeai", "seal": "seal_seal", "shib2": "shib2_shib20", "smtx": "smt_swarmmarkets", "snap": "nap_snap", "snft": "snft_suprenft", "snft1": "snft", "soul": "soul_phantasma", "star": "star_filestar", "starheroes": "star_starheroes", "stc": "stc_starcoin", "stox": "stx_stox", "tao": "tao_bittensor", "tap": "tap_tapfantasy", "time": "time_wonderlandtime", "timechrono": "time", "titan": "titan_runesatoshi", "ton": "ton_tontoken", "toncoin": "ton", "trac": "trac_tracordinals", "tracai": "trac", "ttt": "ttt_tabtrader", "unq": "unq_uniqueventureclubs", "uro": "uro_unirouter", "urolithina": "uro", "ustc": "ust", "vee": "vee_veefinance", "velodrome": "velo_velodromefinance", "well3": "well_well3", "win": "win_wink", "wolf": "wolf_landwolf0x67", "wsb": "wsb_wallstreetbaby", "wzrd": "wzrd_wizardia", "zero": "zero_analysoor", "zerolend": "zero", "ztx": "ztx_ztx"}, "exclude_markets": {"by_base": ["cnyx", "hav"], "by_quote": ["cnyx", "hav"]}}, {"id": 26, "name": "Bitbank", "rest_api_rate_limit": 10.0, "trusted_spot_start_date": "1970-01-01", "tickers": {"bcc": "bch", "one": "one_harmony", "quick": "quick_new", "win": "win_wink"}}, {"id": 27, "name": "TheRockTrading", "rest_api_rate_limit": 10.0, "trusted_spot_start_date": "1970-01-01", "tickers": {"luna": "luna2", "lunc": "luna", "one": "one_harmony", "win": "win_wink"}}, {"id": 28, "name": "itBit", "rest_api_rate_limit": 1.0, "trusted_spot_start_date": "1970-01-01", "tickers": {"one": "one_harmony", "quick": "quick_new", "win": "win_wink", "xbt": "btc"}}, {"id": 30, "name": "Gatecoin", "rest_api_rate_limit": 0.016, "tickers": {"arc": "arc_arcona", "bcp": "bcpt", "can": "can_canyacoin", "fli": "flixx", "gat": "gat_gatcoin", "man": "mana", "pas": "pass_blockpass", "slt": "salt", "tra": "trac", "trk": "trak", "usc": "usdc", "wgs": "wings"}, "exclude_markets": {"by_market": ["btc-usc", "btc-sgd", "eth-sgd", "gen-btc", "gen-eth", "arc-btc", "hvn-btc", "mkr-eth", "mkr-btc", "pas-btc", "pas-eth", "sat-btc", "sat-eth", "trk-btc", "trk-eth", "tmt-btc", "zla-eth", "usc-usd"]}}, {"id": 31, "name": "Liquid", "rest_api_rate_limit": 0.5, "trusted_spot_start_date": "1970-01-01", "tickers": {"bifi": "bifi_bifr", "can": "can_canyacoin", "ftt": "ftt_farmatrust", "gat": "gat_gatcoin", "get": "get_getprotocol", "hot": "hot_hottoken", "mtl": "mtl_metal", "one": "one_harmony", "ong": "ong_ontologygas", "win": "win_wink", "xno": "xno_xeno"}, "exclude_markets": {"by_base": ["chi", "hav", "hero", "ignx", "pal", "rkt", "ppl", "pwv", "ser"]}}, {"id": 32, "name": "CEX.IO", "rest_api_rate_limit": 1.0, "trusted_spot_start_date": "1970-01-01", "tickers": {"ai": "ai_sleeplessai", "hot": "hot_holo", "iota": "miota", "lunc": "luna", "one": "one_harmony", "ong": "ong_ontologygas", "quick": "quick_new", "repv2": "rep", "win": "win_wink"}}, {"id": 33, "name": "Bittrex", "rest_api_rate_limit": 1.0, "trusted_spot_start_date": "1970-01-01", "tickers": {"acb": "acb_tokenizedstock", "amc": "amc_tokenizedstock", "amd": "amd_tokenizedstock", "bits": "bits_bitswift", "bitw": "bitw_tokenizedstock", "bsd": "bsd_bitsend", "cbc": "cbc_cashbetcoin", "edr": "edr_endorprotocol", "fb": "fb_tokenizedstock", "gdxj": "gdxj_tokenizedstock", "gme": "gme_tokenizedstock", "gmt": "gmt_gmttoken", "gst": "gst_gstcoin", "iota": "miota", "me": "me_allme", "meme": "meme_memetic", "mim": "mim_mintmarble", "more": "more_morecoin", "mtl": "mtl_metal", "nbt": "usnbt", "nok": "nok_tokenizedstock", "one": "one_harmony", "ong": "ong_ontologygas", "penn": "penn_tokenizedstock", "pla": "pla_playchip", "quick": "quick_new", "rcn": "rcn_ripiocreditnetwork", "repv2": "rep", "sbd": "sbd_steemdollars", "slt": "slt_smartlands", "spy": "spy_tokenizedstock", "ton": "ton_tokamaknetwork", "ttc": "ttc_ttcprotocol", "usds": "usds_stableusd", "win": "win_wink", "xai": "xai_sideshifttoken"}}, {"id": 34, "name": "BitMEX", "rest_api_rate_limit": 0.5, "streaming_api_connection_rate_limit": 0.0055, "tickers": {"luna": "luna2", "one": "one_harmony", "quick": "quick_new", "trump": "trump_maga", "trumpofficial": "trump", "win": "win_wink", "xbt": "btc"}}, {"id": 35, "name": "Binance.US", "rest_api_rate_limit": 4.0, "trusted_spot_start_date": "1970-01-01", "tickers": {"ace": "ace_fusionist", "ai": "ai_sleeplessai", "bchsv": "bsv", "bifi": "bifi_beef", "bqx": "ethos", "combo": "combo_combo", "dodox": "dodo", "hc": "hc_hypercash", "hot": "hot_holo", "iota": "miota", "jam": "jam_geojam", "luna": "luna2", "lunc": "luna", "mtl": "mtl_metal", "neiro": "neirocto", "one": "one_harmony", "ong": "ong_ontologygas", "quick": "quick_new", "raysol": "ray", "rcn": "rcn_ripiocreditnetwork", "ronin": "ron", "tao": "tao_bittensor", "tst": "tst_test", "usds": "usds_stableusd", "ustc": "ust", "vai": "vai_vai", "velodrome": "velo_velodromefinance", "win": "win_wink", "yoyo": "yoyow"}}, {"id": 37, "name": "<PERSON><PERSON><PERSON>", "rest_api_rate_limit": 20.0, "tickers": {"one": "one_harmony", "quick": "quick_new", "win": "win_wink"}}, {"id": 38, "name": "FTX", "rest_api_rate_limit": 30.0, "trusted_spot_start_date": "2022-11-10", "tickers": {"acb": "acb_tokenizedstock", "amc": "amc_tokenizedstock", "amd": "amd_tokenizedstock", "apeamc": "apeamc_tokenizedstock", "apha": "apha_tokenizedstock", "bb": "bb_tokenizedstock", "bitw": "bitw_tokenizedstock", "btt": "bttc", "cron": "cron_tokenizedstock", "ctx": "ctx_c2x", "dkng": "dkng_tokenizedstock", "ethe": "ethe_tokenizedstock", "fb": "fb_tokenizedstock", "gdx": "gdx_tokenizedstock", "gdxj": "gdxj_tokenizedstock", "gme": "gme_tokenizedstock", "hood": "hood_tokenizedstock", "hot": "hot_holo", "jet": "jet_jetprotocol", "lunc": "luna", "mstr": "mstr_tokenizedstock", "mtl": "mtl_metal", "nok": "nok_tokenizedstock", "one": "one_harmony", "penn": "penn_tokenizedstock", "spy": "spy_tokenizedstock", "tlry": "tlry_tokenizedstock", "toncoin": "ton", "uso": "uso_tokenizedstock", "win": "win_wink"}}, {"id": 39, "name": "<PERSON><PERSON><PERSON><PERSON>", "rest_api_rate_limit": 30.0, "streaming_api_connection_rate_limit": 0.5, "tickers": {"agi": "agi_delysium", "alt": "alt_aptoslaunchtoken", "aura": "aura_aurafinance", "bifi": "bifi_bifr", "block": "block_blockprotocol", "btt": "bttc", "bttold": "btt", "bull": "bull_bullieverse", "combo": "combo_combo", "dyp": "dyp_new", "fire": "fire_matr1xfire", "form": "form_form", "fuel": "fuel_fuelnetwork", "gls": "gls_glaciernetwork", "hc": "hc_hypercash", "ice": "ice_icenetwork", "iota": "miota", "jam": "jam_geojam", "kngl": "ngl_entangle", "lava": "lava_lavanetwork", "layer": "layer_unilayer", "loki": "oxen", "luna": "luna2", "lunc": "luna", "miladycult": "cult_miladycultcoin", "mint": "mint_mintify", "mtl": "mtl_metal", "mts": "mts_metastrike", "neiro": "ne<PERSON><PERSON>", "one": "one_harmony", "open": "open_opencustodyprotocol", "p00ls": "00", "pix": "pix_pixie", "rbtc1": "rbtc_rabbitcoin", "redstone": "red", "seed": "seed_seed", "sky": "sky_sky", "sns": "sns_sonorus", "solayer": "layer", "soul": "soul_phantasma", "tao": "tao_bittensor", "tap": "tap_tapprotocol", "troll": "trollsol", "tstbsc": "tst_test", "ustc": "ust", "win": "win_wink", "xbt": "btc"}}, {"id": 40, "name": "CME", "rest_api_rate_limit": 30.0, "streaming_api_connection_rate_limit": 0.5}, {"id": 41, "name": "LMAX", "rest_api_rate_limit": 30.0, "streaming_api_connection_rate_limit": 0.5, "tickers": {"eurm": "eurm_lmax", "one": "one_harmony", "quick": "quick_new", "usdm": "usdm_lmax", "win": "win_wink"}}, {"id": 42, "name": "Bybit", "rest_api_rate_limit": 25.0, "streaming_api_connection_rate_limit": 0.5, "tickers": {"ace": "ace_fusionist", "agi": "agi_delysium", "beam": "beamx", "btt": "bttc", "bttold": "btt", "cat": "cat_cyberarena", "combo": "combo_combo", "ctt": "ctt_cashtreetoken", "dop1": "dop", "fame": "fame_famemma", "fb": "fb_fenerbahcetoken", "fire": "fire_matr1xfire", "fuel": "fuel_fuelnetwork", "game": "game_gamebyvirtuals", "hot": "hot_holo", "lava": "lava_lavanetwork", "luna": "luna2", "lunc": "luna", "neiro": "ne<PERSON><PERSON>", "ngl": "ngl_entangle", "one": "one_harmony", "plt": "plt_palette", "quick": "quick_new", "star": "star_starheroes", "tap": "tap_tapfantasy", "ustc": "ust", "well": "well_well3", "win": "win_wink", "ztx": "ztx_ztx"}, "exclude_markets": {"by_base": ["metis", "lido"]}}, {"id": 43, "name": "uniswap_v1_eth", "rest_api_rate_limit": 30.0, "streaming_api_connection_rate_limit": 0.5, "defi": true, "network": "eth"}, {"id": 44, "name": "uniswap_v2_eth", "rest_api_rate_limit": 30.0, "streaming_api_connection_rate_limit": 0.5, "defi": true, "network": "eth", "tickers": {"free": "free_freerossdao", "jam": "jam_geojam", "mona": "mona_monavale", "trade": "trade_unitrade"}}, {"id": 45, "name": "uniswap_v3_eth", "rest_api_rate_limit": 30.0, "streaming_api_connection_rate_limit": 0.5, "defi": true, "network": "eth", "defi_pools_supported": true, "tickers": {"jam": "jam_geojam"}}, {"id": 46, "name": "FTX.US", "rest_api_rate_limit": 30.0, "trusted_spot_start_date": "2022-11-10", "tickers": {"one": "one_harmony", "win": "win_wink"}}, {"id": 47, "name": "sushiswap_v1_eth", "rest_api_rate_limit": 30.0, "streaming_api_connection_rate_limit": 0.5, "defi": true, "network": "eth"}, {"id": 48, "name": "Crypto.com", "rest_api_rate_limit": 100.0, "tickers": {"bifi": "bifi_beef", "hot": "hot_holo", "lion": "lion_loadedlions", "lunc": "luna", "one": "one_harmony", "ong": "ong_ontologygas", "quick": "quick_new", "rif": "rifsol", "ronin": "ron", "tao": "tao_bittensor", "usd_stable_coin": "usd", "win": "win_wink"}}, {"id": 49, "name": "MEXC", "rest_api_rate_limit": 20.0, "streaming_api_connection_rate_limit": 0.5, "tickers": {"ace": "ace_fusionist", "ad": "ad_artdollar", "agi": "agi_delysium", "ai": "ai_sleeplessai", "ai16zeliza": "eliza_eliza<PERSON><PERSON><PERSON>", "ait": "ait_aimalls", "aitprotocol": "ait_aitprotocol", "alt": "alt_aptoslaunchtoken", "altlayer": "alt", "amo": "amino", "andyeth": "andy_eth", "ars": "ars_aqua<PERSON><PERSON><PERSON>", "art": "art_artelanetwork", "asm": "asm_asmonacofantoken", "ast": "ast_astroon", "aura": "aura_auranetwork", "bac": "bac_cdari", "banano": "ban_banano", "bcx": "bcx_bigcoin", "benji": "benji_benjamin", "benqi": "qi", "bfc": "bfc_bitcoinfreecash", "bft": "bft_brazilfantoken", "bifi": "bifi_bifr", "blue": "blue_blue", "btt": "bttc", "bull": "bull_tronbull", "bxc": "bcx", "cateeth": "cate_cate", "caw": "caw_ahuntersdream", "chat": "chat_solchat", "combo": "combo_combo", "cstars": "stars_cryptoallstars", "cult": "cult_miladycultcoin", "cultdao": "cult", "degen": "degen_degenreborn", "dfi": "dfi_dfistarter", "dino": "dino_<PERSON><PERSON>o", "dis": "dis_dischain", "dlc": "dlc_deeplinkprotocol", "dope": "dope_departmentofpropagandaeverywhere", "dyp": "dyp_new", "edg": "edg_edgegrid", "egg": "egg_eggdog", "elft": "elf_thelandelfcrossing", "eliza": "eliza_eliza<PERSON><PERSON><PERSON><PERSON>", "epik": "epik_tehepikduck", "fan": "fan_filmio", "fight": "fight_fight", "fighteth": "fight", "fine": "fine_fine", "fit": "fit_fit", "fly": "fly_flytrade", "form1": "form_form", "four": "four_4", "free": "free_freerossdao", "frog": "frog_frodothev<PERSON><PERSON><PERSON><PERSON><PERSON>", "fuel": "fuel_fuelnetwork", "furucombo": "combo", "gamevirtual": "game_gamebyvirtuals", "gas": "gas_gasdao", "gm": "gm_gomble", "gpt": "gpt_gptprotocol", "gpt1": "gpt_qna3ai", "harry": "hpos10i", "hbd": "hbd_happyballoondog", "hero": "hero_stephero", "hot": "hot_holo", "ice": "ice_decentralgamesice", "icenetwork": "ice_icenetwork", "iota": "miota", "juice": "juice_juice", "king": "king_king", "kt": "kt_ktalk", "lava": "lava_lavanetwork", "lbtc": "lbtc_lightningbitcoin", "leo": "leo_leo", "lft": "lft_lendflare", "lion": "lion_king<PERSON><PERSON>me", "luna": "luna2", "lunc": "luna", "lwolf": "wolf_landwolf0x67", "mad": "mad_mad", "maga": "maga_trump", "magaeth": "maga", "mai": "mai_muxyai", "mars": "mars_metamars", "mars1": "mars", "mav": "mav_massiveprotocol", "mdt": "mdt_metadance", "memetoon": "meme_memetoon", "mim": "mim_mintmarble", "mist": "mist_mist", "mngl": "ngl_entangle", "move": "move_bluemove", "music": "music_musicbyvirtuals", "neiro": "neiro_neirolol", "nim1": "nim_nimnetwork", "one": "one_harmony", "ong": "ong_ontologygas", "open": "open_opencustodyprotocol", "p00ls": "00", "palmai": "palm_palmai", "panda": "panda_pandaswap", "paw": "paw_paw", "pawswap": "paw_paw", "pawzone": "paw_pawzone", "pbx": "pbx_probinex", "plt": "plt_poollottofinance", "pmg": "pmg_pmgcoin", "portuma": "por_portuma", "qi": "qi_qiswap", "quick": "quick_new", "rai": "rai_reploy", "rbtc1": "rbtc_rabbitcoin", "reap": "reap_releapprotocol", "rem": "rem_realestatemetaverse", "rev": "rev_rchain", "sai": "sai_sharpeai", "sbr1": "sbr_strategicbitcoinreserve", "seal": "seal_sealsolana", "seed": "seed_seed", "sen": "sen_sentioprotocol", "sendsol": "send_sendcoin", "sharkcat": "sc_sharkcat", "shib2": "shib2_shib20", "smt": "smt_swarmmarkets", "snap": "nap_snap", "sns": "sns_solananameservice", "sols": "sols_sols", "sols1": "sols", "soul": "soul_soulsaver", "spot": "spot_spotsquad", "squad": "squad_squadswap", "star": "star_starheroes", "stars": "stars_stargaze", "strx": "strx_strikex", "talk": "talk_cryptalk", "tao": "tao_bittensor", "tap": "tap_tapprotocol", "tct": "tct_tupancommunitytoken", "titan": "titan_irontitaniumtoken", "tokamak": "ton_tokamaknetwork", "ton": "ton_tontoken", "trac": "trac_tracordinals", "trace": "trace_metatrace", "trumpofficial": "trump", "tst": "tst_test", "ttt": "ttt_tabtrader", "txt": "txt_txswap", "unq": "unq_uniqueventureclubs", "usd1": "usd1_currencyoneusd", "usdm": "usdm_usdmappedtoken", "ustc": "ust", "vee": "vee_zeeverse", "velodrome": "velo_velodromefinance", "well": "well_well3", "whale": "whale_whitewhale", "win": "win_wink", "wolf": "wolf_landwolf<PERSON>x", "wolfs": "wolf", "wom": "wom_wombatexchange", "wsb": "wsb_wallstreetbaby", "wxt": "wxt_weextoken", "zetrix": "ztx_zetrix", "ztx": "ztx_ztx"}}, {"id": 50, "name": "Bullish", "rest_api_rate_limit": 50.0, "streaming_api_connection_rate_limit": 0.5, "tickers": {"one": "one_harmony", "quick": "quick_new"}}, {"id": 51, "name": "curve_eth", "rest_api_rate_limit": 30.0, "streaming_api_connection_rate_limit": 0.5, "defi": true, "network": "eth", "defi_pools_supported": true}, {"id": 52, "name": "traderjoe_v1_avaxc", "rest_api_rate_limit": 30.0, "streaming_api_connection_rate_limit": 0.5, "defi": true, "network": "avaxc"}, {"id": 53, "name": "ErisX", "metrics_disabled": false, "rest_api_rate_limit": 20.0, "streaming_api_connection_rate_limit": 0.5}, {"id": 54, "name": "Coinbase_International", "short_name": "CoinbaseInt", "metrics_disabled": false, "rest_api_rate_limit": 20.0, "streaming_api_connection_rate_limit": 0.5, "tickers": {"cgld": "celo", "corechain": "core", "jup": "jup_jupiterproject", "mona": "mona_monavale", "mtl": "mtl_metal", "neiro": "neirocto", "one": "one_harmony", "ronin": "ron", "velo": "velo_velodromefinance", "win": "win_wink", "zeta": "zeta_eth", "zetachain": "zeta"}}, {"id": 55, "name": "Binance_Aggregate", "short_name": "BinanceAgg", "metrics_disabled": true, "rest_api_rate_limit": 4.0, "tickers": {"ace": "ace_fusionist", "ai": "ai_sleeplessai", "bchsv": "bsv", "bifi": "bifi_beef", "bqx": "ethos", "combo": "combo_combo", "dodox": "dodo", "hc": "hc_hypercash", "hot": "hot_holo", "iota": "miota", "jam": "jam_geojam", "luna": "luna2", "lunc": "luna", "mtl": "mtl_metal", "neiro": "neirocto", "one": "one_harmony", "ong": "ong_ontologygas", "quick": "quick_new", "raysol": "ray", "rcn": "rcn_ripiocreditnetwork", "ronin": "ron", "tao": "tao_bittensor", "tst": "tst_test", "usds": "usds_stableusd", "ustc": "ust", "vai": "vai_vai", "velodrome": "velo_velodromefinance", "win": "win_wink", "yoyo": "yoyow"}, "exclude_markets": {"by_base": ["bcc", "ven"]}}, {"id": 56, "name": "Coinbase_Derivatives", "short_name": "CoinbaseDer", "metrics_disabled": false, "rest_api_rate_limit": 20.0, "streaming_api_connection_rate_limit": 0.5, "tickers": {"ava": "avax", "cgld": "celo", "corechain": "core", "dog": "doge", "hed": "hbar", "jup": "jup_jupiterproject", "lc": "ltc", "lnk": "link", "mona": "mona_monavale", "mtl": "mtl_metal", "neiro": "neirocto", "one": "one_harmony", "ronin": "ron", "slc": "sol", "velo": "velo_velodromefinance", "win": "win_wink", "zeta": "zeta_eth", "zetachain": "zeta"}}, {"id": 57, "name": "GFO_X", "short_name": "GFOX", "metrics_disabled": true, "rest_api_rate_limit": 20.0, "streaming_api_connection_rate_limit": 0.5}, {"id": 58, "name": "balancer_v2_eth", "metrics_disabled": true, "rest_api_rate_limit": 30.0, "streaming_api_connection_rate_limit": 0.5, "defi": true, "network": "eth", "defi_pools_supported": true}, {"id": 59, "name": "dYdX", "rest_api_rate_limit": 20.0, "streaming_api_connection_rate_limit": 0.5}, {"id": 60, "name": "aerodrome_slipstream_base", "metrics_disabled": true, "rest_api_rate_limit": 30.0, "streaming_api_connection_rate_limit": 0.5, "defi": true, "network": "base", "defi_pools_supported": true}, {"id": 61, "name": "uniswap_v2_base", "metrics_disabled": true, "rest_api_rate_limit": 30.0, "streaming_api_connection_rate_limit": 0.5, "defi": true, "network": "base", "defi_pools_supported": true}, {"id": 62, "name": "uniswap_v3_base", "metrics_disabled": true, "rest_api_rate_limit": 30.0, "streaming_api_connection_rate_limit": 0.5, "defi": true, "network": "base", "defi_pools_supported": true}, {"id": 63, "name": "uniswap_v4_eth", "metrics_disabled": true, "rest_api_rate_limit": 30.0, "streaming_api_connection_rate_limit": 0.5, "defi": true, "network": "eth", "defi_pools_supported": true}, {"id": 64, "name": "uniswap_v4_base.eth", "metrics_disabled": true, "rest_api_rate_limit": 30.0, "streaming_api_connection_rate_limit": 0.5, "defi": true, "network": "base.eth", "defi_pools_supported": true}, {"id": 65, "name": "Bitget", "metrics_disabled": true, "rest_api_rate_limit": 20.0, "streaming_api_connection_rate_limit": 0.5, "tickers": {"ace": "ace_fusionist", "bitcoin": "hpos10i", "btt": "bttc", "combo": "combo_combo", "eliza": "eliza_eliza<PERSON><PERSON><PERSON>", "fire": "fire_matr1xfire", "fuel": "fuel_fuelnetwork", "galfan": "galft", "game": "game_gamebyvirtuals", "gm": "gm_gomble", "gpt": "gpt_qna3ai", "hot": "hot_holo", "ice": "ice_icenetwork", "landwolf": "wolf", "lifeform": "lft", "luna": "luna2", "lunc": "luna", "mtl": "mtl_metal", "one": "one_harmony", "ong": "ong_ontologygas", "open": "open_opencustodyprotocol", "paw": "paw_paw", "portuma": "por_portuma", "quick": "quick_new", "rbtc": "rbtc_rabbitcoin", "sky": "sky_sky", "tao": "tao_bittensor", "tstbsc": "tst_test", "ustc": "ust", "velodrome": "velo_velodromefinance", "well": "well_well3", "win": "win_wink", "wolf": "wolf_landwolf0x67", "zerolend": "zero", "ztx": "ztx_ztx"}}, {"id": 66, "name": "velodrome_v3_op", "metrics_disabled": true, "rest_api_rate_limit": 30.0, "streaming_api_connection_rate_limit": 0.5, "defi": true, "network": "op", "defi_pools_supported": true}, {"id": 67, "name": "uniswap_v4_arb.eth", "metrics_disabled": true, "rest_api_rate_limit": 30.0, "streaming_api_connection_rate_limit": 0.5, "defi": true, "network": "arb.eth", "defi_pools_supported": true}, {"id": 68, "name": "uniswap_v4_op.eth", "metrics_disabled": true, "rest_api_rate_limit": 30.0, "streaming_api_connection_rate_limit": 0.5, "defi": true, "network": "op.eth", "defi_pools_supported": true}]