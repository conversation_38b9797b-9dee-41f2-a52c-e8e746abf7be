FROM registry.gitlab.com/coinmetrics/libs/standard-images/jvm:21-focal

ARG user=coinmetrics
ARG group=users
ARG app_dir=/home/<USER>
ARG debug_dir=$app_dir/debug
ARG logs_dir=$debug_dir/logs
ARG dumps_dir=$debug_dir/dumps

RUN useradd -m -u 1000 -g $group -s /bin/bash $user

RUN mkdir -p $app_dir && chown $user:$group $app_dir
RUN mkdir -p $debug_dir && chown $user:$group $debug_dir
RUN mkdir -p $logs_dir && chown $user:$group $logs_dir
RUN mkdir -p $dumps_dir && chown $user:$group $dumps_dir

USER $user
WORKDIR $app_dir

ADD --chown=$user:users project/statistics-generator/build/libs/app.jar $app_dir/app.jar
ADD --chown=$user:users docker/statistics/start .

ENV JVM_ARGS -XX:-OmitStackTraceInFastThrow \
-XX:+HeapDumpOnOutOfMemoryError \
-XX:HeapDumpPath=$dumps_dir \
-Xlog:gc*=debug,safepoint,age*=trace:file=$logs_dir/gc.log:time,uptime,pid,tid,level:filecount=10,filesize=10485760 \
-Dio.coinmetrics.jmx.port=8001 \
-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:8000 \
-XX:ErrorFile=$logs_dir/hs_err_pid_%p.log \
-XX:+PrintFlagsFinal \
-XX:ParallelGCThreads=16 \
-XX:ActiveProcessorCount=32
ENV API_STATS_HEAP_SIZE 4G

ENTRYPOINT ["./start"]
