docker-statistics:
  image: docker:latest
  services:
    - docker:dind
  stage: docker
  tags:
    - docker
    - coinmetrics-build-runner
  script:
    - mkdir -p ~/.docker && echo ${DOCKER_AUTH_CONFIG} > ~/.docker/config.json
    - docker build -t ${STATISTICS_IMAGE}:${CI_COMMIT_SHA} -t ${STATISTICS_IMAGE}:latest -t ${STATISTICS_IMAGE}:master -f docker/statistics/Dockerfile .
    - docker login ${CI_REGISTRY} -u ${CI_REGISTRY_USER} -p ${CI_REGISTRY_PASSWORD}
    - docker push ${STATISTICS_IMAGE}:${CI_COMMIT_SHA}
    - |
      if [ "$CI_COMMIT_REF_NAME" = master ]; then
        echo "Tagging: ${STATISTICS_IMAGE} as master"
        docker push ${STATISTICS_IMAGE}:master
      fi


statistics staging:
  stage: deploy hetzner
  image: alpine:latest
  script:
    - apk add --no-cache curl
    - >-
      curl -X POST --fail
      -F "token=$DEPLOY_TOKEN"
      -F "ref=master"
      -F "variables[OPS_DEPLOY_PLAYBOOK]=api_statistics_staging"
      -F "variables[OPS_DEPLOY_EXTRA_VARS]={\"image\":\"${STATISTICS_IMAGE}:${CI_COMMIT_SHA}\"}"
      https://gitlab.com/api/v4/projects/$OPS_PROJECT_ID/trigger/pipeline
      -o job.txt
    - cat job.txt
  environment:
    name: statistics-hetzner-staging
    deployment_tier: staging
  tags:
    - linux
    - docker
  when: manual

statistics staging cdev1:
  extends: .kubernetes
  stage: deploy cdev1
  variables:
    LOCATION: "cdev1"
    DIR: "k8s/statistics"
    HELM_RELEASE_NAME: "statistics"
    HELM_VALUE_FILES: "../main-api/config.yaml,../main-api/stg/cdev1/secret-values.yaml,../main-api/stg/cdev1/pro-config.yaml,config.yaml,values.yaml,stg/values.yaml"
    HELM_FAILURE_HANDLING: "uninstall"
  script:
    - HELM_RELEASE_SUFFIX=1 install-helm-chart
  environment:
    name: statistics cdev1
    deployment_tier: staging
  tags:
    - env-cdev1
    - rt-containerd
  when: manual

statistics production:
  stage: deploy hetzner
  image: alpine:latest
  script:
    - apk add --no-cache curl
    - >-
      curl -X POST --fail
      -F "token=$DEPLOY_TOKEN"
      -F "ref=master"
      -F "variables[OPS_DEPLOY_PLAYBOOK]=api_statistics"
      -F "variables[OPS_DEPLOY_EXTRA_VARS]={\"image\":\"${STATISTICS_IMAGE}:${CI_COMMIT_SHA}\"}"
      https://gitlab.com/api/v4/projects/$OPS_PROJECT_ID/trigger/pipeline
      -o job.txt
    - cat job.txt
  environment:
    name: statistics-hetzner-production
    deployment_tier: production
  tags:
    - linux
    - docker
  only:
    - master
  when: manual

statistics production cp1:
  extends: .kubernetes
  stage: deploy cp1
  variables:
    LOCATION: "cp1"
    DIR: "k8s/statistics"
    HELM_RELEASE_NAME: "statistics"
    HELM_VALUE_FILES: "../main-api/config.yaml,../main-api/prd/config.yaml,../main-api/prd/pro-config.yaml,../main-api/prd/cp1/config.yaml,../main-api/prd/cp1/secret-values.yaml,config.yaml,values.yaml,prd/values.yaml,prd/cp1/secret-values.yaml"
  script:
    - HELM_RELEASE_SUFFIX=1 install-helm-chart
  environment:
    name: statistics cp1
    deployment_tier: production
  tags:
    - kube-any-small
    - linux
    - cp1
  when: manual
  only:
    - master
