docker-docs:
  stage: docker
  image: docker:latest
  services:
    - docker:dind
  tags:
    - docker
    - coinmetrics-build-runner
  script:
    - test -n "${DOCKERHUB_USERNAME}" -a -n "${DOCKERHUB_PASSWORD}"  && echo "${DOCKERHUB_PASSWORD}" | docker login --username "${DOCKERHUB_USERNAME}" --password-stdin
    - docker login $CI_REGISTRY -u ${CI_REGISTRY_USER} -p ${CI_REGISTRY_PASSWORD}
    - docker build -f docs/Dockerfile -t $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA-docs -t $CI_REGISTRY_IMAGE:master-docs .
    - docker push $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA-docs
    - |
      if [ "$CI_COMMIT_REF_NAME" = master ]; then
        echo "Tagging: $CI_REGISTRY_IMAGE as master-docs"
        docker push $CI_REGISTRY_IMAGE:master-docs
      fi


docs staging:
  stage: deploy hetzner
  image: alpine:latest
  script:
    - apk add --no-cache curl
    - >-
      curl -X POST --fail
      -F "token=$DEPLOY_TOKEN"
      -F "ref=master"
      -F "variables[OPS_DEPLOY_PLAYBOOK]=docs_website_staging"
      -F "variables[OPS_DEPLOY_EXTRA_VARS]={\"docs_website_image_version\":\"${CI_COMMIT_SHA}-docs\"}"
      -F "variables[OPS_DEPLOY_EXTRA_OPTIONS]=--tags api4"
      https://gitlab.com/api/v4/projects/$OPS_PROJECT_ID/trigger/pipeline
      -o job.txt
    - cat job.txt
  environment:
    name: hetzner-staging-docs
    deployment_tier: staging
  tags:
    - linux
    - docker
  when: manual

docs staging cdev1:
  extends: .kubernetes
  stage: deploy cdev1
  variables:
    LOCATION: "cdev1"
    DIR: "k8s/docs"
    HELM_RELEASE_NAME: "api4-docs"
    HELM_VALUE_FILES: "values.yaml,stg/values.yaml"
  script:
    - install-helm-chart
    - ROLLOUT_RESOURCE=deploy/api4-docs wait-rollout
  environment:
    name: cdev1-docs
    deployment_tier: staging
  tags:
    - env-cdev1
    - rt-containerd
  when: manual

docs production:
  stage: deploy hetzner
  image: alpine:latest
  script:
    - apk add --no-cache curl
    - >-
      curl -X POST --fail
      -F "token=$DEPLOY_TOKEN"
      -F "ref=master"
      -F "variables[OPS_DEPLOY_PLAYBOOK]=docs_website"
      -F "variables[OPS_DEPLOY_EXTRA_VARS]={\"docs_website_image_version\":\"${CI_COMMIT_SHA}-docs\"}"
      -F "variables[OPS_DEPLOY_EXTRA_OPTIONS]=--tags api4"
      https://gitlab.com/api/v4/projects/$OPS_PROJECT_ID/trigger/pipeline
      -o job.txt
    - cat job.txt
  environment:
    name: hetzner-production-docs
    deployment_tier: production
  tags:
    - linux
    - docker
  only:
    - master
  when: manual

docs production cp1:
  extends: .kubernetes
  stage: deploy cp1
  variables:
    LOCATION: "cp1"
    DIR: "k8s/docs"
    HELM_RELEASE_NAME: "api4-docs"
    HELM_VALUE_FILES: "values.yaml,prd/values.yaml"
  script:
    - install-helm-chart
    - ROLLOUT_RESOURCE=deploy/api4-docs wait-rollout
  environment:
    name: cp1-docs
    deployment_tier: production
  tags:
    - kube-any-small
    - linux
    - cp1
  when: manual
  only:
    - master
